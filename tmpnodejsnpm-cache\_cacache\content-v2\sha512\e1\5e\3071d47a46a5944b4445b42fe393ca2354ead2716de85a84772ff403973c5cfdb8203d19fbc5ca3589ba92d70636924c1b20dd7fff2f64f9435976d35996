{"_id": "@ampproject/remapping", "_rev": "38-18909af46f4ebf19c2bbc5f15b709591", "name": "@ampproject/remapping", "dist-tags": {"latest": "2.3.0"}, "versions": {"0.1.0": {"name": "@ampproject/remapping", "version": "0.1.0", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"lint:ts": "npm run test:lint:ts -- --fix", "lint:prettier": "npm run test:lint:prettier -- --write", "lint": "run-s -n lint:*", "prebuild": "rm -rf dist", "build:ts": "tsc --module commonjs", "build:rollup": "rollup -c rollup.config.js", "build": "run-s -n build:*", "test": "jest --coverage", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:watch": "jest --coverage --watch", "test:lint:ts": "tslint  --project tsconfig.json -t codeFrame '{src,test}/**/*.ts'", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint": "run-s -n test:lint:*", "test:prod": "run-s -n test:lint 'test --no-cache'", "preversion": "run-s test:prod build", "prepublishOnly": "npm run preversion"}, "devDependencies": {"@types/jest": "24.0.17", "@types/node": "12.7.1", "jest": "24.8.0", "jest-config": "24.8.0", "npm-run-all": "4.1.5", "prettier": "1.18.2", "rollup": "1.19.4", "rollup-plugin-commonjs": "10.0.2", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-typescript": "1.0.1", "source-map": "0.6.1", "ts-jest": "24.0.2", "ts-node": "8.3.0", "tslint": "5.18.0", "tslint-config-prettier": "1.18.0", "tslint-config-standard": "8.0.1", "typescript": "3.5.3"}, "dependencies": {"@jridgewell/resolve-uri": "1.0.0", "sourcemap-codec": "1.4.6"}, "gitHead": "0d2f5b3386568f586263068d1f0187b3623f5d55", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@0.1.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.2", "dist": {"integrity": "sha512-d6RZNb6v22Jo3AKir9YoIejHa5/Cuuh3Kx6XmSKJ4gYf/qB167oWqkLt2zdC3rNHnw4kdNCr4895s7o19JMcZg==", "shasum": "4b295c6cbc5b93d45013195e3f46e464033ae58e", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-0.1.0.tgz", "fileCount": 43, "unpackedSize": 224434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTGFjCRA9TVsSAnZWagAApo4P/R1cak8ivGUYcPDObyd8\neWBavFPvBd1d9qVhNzE0x6v6ST/0g8vjhYb1+dWKYIgTEa1yJPjqP55U2ICp\nbDwsfO8HUuo4jUQ9vC4s2+OfVuILOPtBIqnweqg+XutFLC3NTEDRi/WrPZbO\nj4J52iT64u8ESTl41QUe0od2FwXlmwHgcf0IzCqZ1MpgU3UKRYwdzfPrWc4/\nc75jAkETO5h+XBrgs7GujMdN5lQjhxcLwC0nQMZTOxVVlNBIA2Oqr9iL5dux\nypZ7j6zmZ9uVivHcXgE8nosqvAQd/EVHl1IJ33Xdwu5y6bTk2EP09pH7OacW\naoqy/4dSuKNwXUJFQPh8bgyXv2IAxOggKRSav38ph4S7DmLPXqJMCPZ4gNDP\n0Q3fyAue8Puu46xf01KaJmQtRucNf9v5f57sbff/0kabaC+ZKyn20VhPiDuQ\nG7Yg5r/fA4aErLt8nwMTsGeKn/kCk3DWZ0SAQuXW5MSeiE29J1BcUTiP65OM\n6Ilfl67u1zvgtZBR8E1wlu3Gs62//UybKLmW7B6QSu4+d65mqKfCsfkEQRRb\nmw5OX4wz/rvTxSmCgEZ2xjnnwsr2q7ILK2NnU1e9SpzM3KEb+Sas0rlqj8pl\nXUMLjUjb5FsfWyfgJuTWTIlhLkS+5smkeaPDGNoXn0ENyYfRr6f76rMBwG3+\nfS2k\r\n=non+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCaYwG1WUNQoPcDrWKvLth0NNh7S7NquvfEtt9HZSnRNAIgByTzc8m03wupKUXz7SWThsTlBSvpNDZQCAm4Gw3C6EU="}]}, "maintainers": [{"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "prateekbh", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_0.1.0_1565286755075_0.7248427512900835"}, "_hasShrinkwrap": false}, "0.2.0": {"name": "@ampproject/remapping", "version": "0.2.0", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"lint:ts": "npm run test:lint:ts -- --fix", "lint:prettier": "npm run test:lint:prettier -- --write", "lint": "run-s -n lint:*", "prebuild": "rm -rf dist", "build:ts": "tsc --module commonjs", "build:rollup": "rollup -c rollup.config.js", "build": "run-s -n build:*", "test": "jest --coverage", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:watch": "jest --coverage --watch", "test:lint:ts": "tslint  --project tsconfig.json -t codeFrame '{src,test}/**/*.ts'", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint": "run-s -n test:lint:*", "test:prod": "run-s -n test:lint 'test --no-cache'", "preversion": "run-s test:prod build", "prepublishOnly": "npm run preversion"}, "devDependencies": {"@types/jest": "24.9.1", "@types/node": "12.12.25", "jest": "25.1.0", "jest-config": "25.1.0", "npm-run-all": "4.1.5", "prettier": "1.19.1", "rollup": "1.29.1", "rollup-plugin-commonjs": "10.1.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-sourcemaps": "0.5.0", "rollup-plugin-typescript": "1.0.1", "source-map": "0.6.1", "ts-jest": "25.0.0", "ts-node": "8.6.2", "tslint": "5.20.1", "tslint-config-prettier": "1.18.0", "tslint-config-standard": "9.0.0", "typescript": "3.7.4"}, "dependencies": {"@jridgewell/resolve-uri": "1.0.0", "sourcemap-codec": "1.4.8"}, "gitHead": "97105f73a25e6e0e18777011f6cd6b6843c0d41c", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@0.2.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-a4EztS9/GOVQjX5Ol+Iz33TFhaXvYBF7aB6D8+Qz0/SCIxOm3UNRhGZiwcCuJ8/Ifc6NCogp3S48kc5hFxRpUw==", "shasum": "07290a5c0f5eac8a4c33d38aa0d15a3416db432e", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-0.2.0.tgz", "fileCount": 43, "unpackedSize": 227321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMOFbCRA9TVsSAnZWagAA28kP/2ShznG7Y2ulNgDkz4Dx\n5CyRkPEQLFgvwWEHEC0lbnoxwy97UTH3egoXOdrtliYjJw1j1RYtppuXIhbU\ntGewMgf8fOv1wd2pg8pSgVhFxQjpEHh/l/b8e7MNRnYI/E5L1u5SCFUNIkTe\nFYsRvfH5D4Mkax90Mdo8Ok+EmVqrvjfsw8kUD+qt/hjKfSs/NJT38zhHPrFR\nIgluJenHipeCV9GvHiT3KwI7hOZM3I4qKI/BKMGh5LR/b7N7vUyONEtqP9+k\ncMluKOruNAvaaGEY+/orVty75wNTLzxKpn0MxaOMkoDqhtp9Jm4x41WJ4Um2\n8cThaes9rvlMBH00bQlckfKe/IVZoLaCgZPdROaTU0hUaZvUiTjrHHNEkaSV\nsYe0OelhLtMf2PyNXQusFmHlf05TBOeXIT95smrkpkHf8hm/BEeNix8JjK9Z\naSyrsT34x8IXUacJWIOWsGolIdzgOGeihQoahw7EUqUf4Z8w72IVPirKVwpm\nmbn6JBpmoPIlZodsBniue+E3+vBZyHCAvFwfNiTjjNbKdrU2Z3dfqRLp6YxX\nr5EvmOoQE0A0/doIhgosirKqnK0S0OKWdTy09pyXzR7TLpb/Zljs//tcyWIH\nh9xX9j0dmA2fbKm5sw1TXv8z3kIuk+mnqhwCZ7iOT9ojWc35J8NkB8GRoigG\nzDBC\r\n=Ixdj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfhGDHMrQo62RBamo1tnIfZ81diwl5J3QH1mzjVmb7jgIhAJsqvc2Vz/gHCRu5NyzpIKO56tXS61pbIFArIgSlk4Lb"}]}, "maintainers": [{"email": "<EMAIL>", "name": "amp-toolbox"}, {"email": "<EMAIL>", "name": "ampproject-admin"}, {"email": "<EMAIL>", "name": "choumx"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "f<PERSON>is"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_0.2.0_1580261722750_0.05085414549653233"}, "_hasShrinkwrap": false}, "0.3.0": {"name": "@ampproject/remapping", "version": "0.3.0", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"lint:ts": "npm run test:lint:ts -- --fix", "lint:prettier": "npm run test:lint:prettier -- --write", "lint": "run-s -n lint:*", "prebuild": "rm -rf dist", "build:ts": "tsc --module commonjs", "build:rollup": "rollup -c rollup.config.js", "build": "run-s -n build:*", "test": "jest --coverage", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:watch": "jest --coverage --watch", "test:lint:ts": "tslint  --project tsconfig.json -t codeFrame '{src,test}/**/*.ts'", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint": "run-s -n test:lint:*", "test:prod": "run-s -n test:lint 'test --no-cache'", "preversion": "run-s test:prod build", "prepublishOnly": "npm run preversion"}, "devDependencies": {"@rollup/plugin-node-resolve": "8.0.1", "@rollup/plugin-typescript": "4.1.2", "@types/jest": "26.0.0", "jest": "26.0.1", "jest-config": "26.0.1", "npm-run-all": "4.1.5", "prettier": "2.0.5", "rollup": "2.16.0", "rollup-plugin-sourcemaps": "0.6.2", "source-map": "0.6.1", "ts-jest": "26.1.0", "tslint": "6.1.2", "tslint-config-prettier": "1.18.0", "tslint-config-standard": "9.0.0", "typescript": "3.9.5"}, "dependencies": {"@jridgewell/resolve-uri": "1.0.0", "sourcemap-codec": "1.4.8"}, "gitHead": "a1fe7c3a84243b1a152d047a3aa2066d3fbba04d", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@0.3.0", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-dqmASpaTCavldZqwdEpokgG4yOXmEiEGPP3ATTsBbdXXSKf6kx8jt2fPcKhodABdZlYe82OehR2oFK1y9gwZxw==", "shasum": "5936f02c62ef991f0877a6ee1c3e6afb1cea14b2", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-0.3.0.tgz", "fileCount": 43, "unpackedSize": 182619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5JmcCRA9TVsSAnZWagAAhJMP/3NXSM9Y9NXNe4qANcl5\nO3c7thqb9u2xlYOzZkKGv/HeyOIVz2sSALPhii6OkREG06/LkPihwndI6Ukt\nL/4/97Yxb2Lso1I9AvxXP4zL4DdsZqXWM3n5shwpt3bsZEiIeZTDrl1kjK5w\nbJAJZzVZ+tl3qH0MeybDw9BSAbWpTHBZocJOPmLl5mnOwCFsNk5OpOT6ANOK\nA+KUtKp0C0OS0n913ZvhTgfgR1wEdiK3r+xZloOHXM3U5Ebej+3foByjQrak\nrrzKU2wQKePmTcOzBS5f/zhvsqVDHVGx2Fuh1YTiyrd7ojRgQJUdqQQR9A/e\nARP/kGhUbBcDCMl/KOYbwAkSu+lPqAX5Z4m+XPL7L8gs3wdeiFF/gLYoJI6Z\nnS4eT0MLioEGa5fAEt5DjkGWYC+wayM6jF/DAiW/VcvuQUwI8WeyMVxo4hz8\nU5oclzcbx8g5sLdoxshl0iQYNONfBurITWFyQO6cHzxIWN4b1yx3T3fObX79\nLGCMQVb95FtPncWDxi0bMXoyiNqaUaTkSNDmM3DC1uuyYYrHGOaeYV1tRxY6\nqrmT+iVAWS07QQITUUvz5UH6EVGW9pgofqrVzwXuOL4cyVTwOAUGmnd7M/a5\nBEM1HXwYi7eNHMIGzWgLwwPAjBzblkQdiIWqk37RskJKuthTOg0qDbxdI39V\ndH6Z\r\n=BVkt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA2J8uuLFAHzIEkP7o1Ywk9qVGbqCM1wkDvN7BIyvVEtAiEApRe9pnPg9eGIHBib5mVlITrwKglU9uLNQI4iJ4DDLNE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "amp-toolbox"}, {"email": "<EMAIL>", "name": "ampproject-admin"}, {"email": "<EMAIL>", "name": "choumx"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "f<PERSON>is"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_0.3.0_1592039835609_0.14764054482044098"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "@ampproject/remapping", "version": "1.0.0", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint 'test:only -- --no-cache'", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-node-resolve": "11.0.1", "@rollup/plugin-typescript": "8.1.0", "@types/jest": "26.0.19", "@typescript-eslint/eslint-plugin": "4.10.0", "@typescript-eslint/parser": "4.10.0", "eslint": "7.15.0", "eslint-config-prettier": "7.0.0", "jest": "26.6.3", "jest-config": "26.6.3", "npm-run-all": "4.1.5", "prettier": "2.2.1", "rollup": "2.35.1", "ts-jest": "26.4.4", "tslib": "2.0.3", "typescript": "4.1.3"}, "dependencies": {"@jridgewell/resolve-uri": "1.0.0", "sourcemap-codec": "1.4.8"}, "gitHead": "dc0a1053785fcaf483e2be4c3f258c3b1ed56170", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@1.0.0", "_nodeVersion": "14.15.2", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-/B8wfBlUyh9X4Drkt41n6oSL5PYVkZWWLEVZ8RoBZYA4XbVNBTQp2kMhdwEMnQFGB343wROxx3ma7MLuVNrbpw==", "shasum": "f93f76ce5263320c7b48d9631b1f8c992a61c64e", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-1.0.0.tgz", "fileCount": 19, "unpackedSize": 156492, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3YCiCRA9TVsSAnZWagAAAgIP+QC5kqKYUb4JLQhpRLKh\n+cPYzXV0nMZ0GXSmnBO2Ij8x8oJ4mIUey06vuL+8t5X8BDP58NGFPl6TKCtU\n8sQDZE7qgTsOijdR0wtiEcnv4IG1KNgxdPfLzp0ll53yiWRAzrXzqE/dA48p\nIF8Ykr5j/V0I3cBzhQQ6QTHN++uPg7/K5iLJgCfhXpipUE6XFvBv37wXFp+j\nycrOD+UiOg/mYQ/3YaOsY2GQzRTAr0Gz2fUod/n6TFVBckMVL/JmQCMuHXCu\nMmqf7y6y3wOcjg5ziEppdLD5CHujUTESqszqeDbZsDi96qh+R5EzSneKjA74\nNuPm0fzf/ugvxygSxvyA3FeB7Xn0yrW52hEFb03a9VMey8xjM+imjkEOM8QR\nj3Xo2Wx1nP40CS8qIGBm26OB2wewTayXlp6VOtx8LDoWNZGBZqh4GQ/kf9YA\nYqWl2HI0aRoNDmurNY2oxr9Vdr+6yhC6+HQXJOBkDYXTLxj4hivu9FDhdv6g\nIJXG9wbBEm5khgAlSSZ+vqmcJ/ZZO9CALBqlOAx+cKNApoofSridULFOFHGO\n8/86mj5EKWIoTE+PAQKXlsOIbVMnyOVsfXsdaz5B8RFHQVn/x9nQ5uf80xHt\nKpwrlD3wOgfjDVZgCk6od6a2kZQKP8Mg3OSfm7lTYyf6UxfRB47WPelz8Hvc\nNDzH\r\n=95Xt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyjL1vWOKpbZODWZyqPJ8dSODSoXKhVEMF89euOCRkLgIhAPuwaM0ReFUjxXGrCSaIxsU2Pqh3F7YN0egMOQepvsY3"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_1.0.0_1608351905560_0.15353400126776884"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "@ampproject/remapping", "version": "1.0.1", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint 'test:only -- --no-cache'", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-node-resolve": "11.0.1", "@rollup/plugin-typescript": "8.1.0", "@types/jest": "26.0.19", "@typescript-eslint/eslint-plugin": "4.10.0", "@typescript-eslint/parser": "4.10.0", "eslint": "7.15.0", "eslint-config-prettier": "7.0.0", "jest": "26.6.3", "jest-config": "26.6.3", "npm-run-all": "4.1.5", "prettier": "2.2.1", "rollup": "2.35.1", "ts-jest": "26.4.4", "tslib": "2.0.3", "typescript": "4.1.3"}, "dependencies": {"@jridgewell/resolve-uri": "1.0.0", "sourcemap-codec": "1.4.8"}, "gitHead": "9f70ebaa7e6e4f39fa77d1567bb93e93131bd35f", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@1.0.1", "_nodeVersion": "12.13.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-Ta9bMA3EtUHDaZJXqUoT5cn/EecwOp+SXpKJqxDbDuMbLvEMu6YTyDDuvTWeStODfdmXyfMo7LymQyPkN3BicA==", "shasum": "1398e73e567c2a7992df6554c15bb94a89b68ba2", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-1.0.1.tgz", "fileCount": 19, "unpackedSize": 157941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAgxpCRA9TVsSAnZWagAArzEP/29+kE5fk2gNrMV1hg9I\n/h+p8CBe9xo5uB+N+8GUXnaBroCgUKWtd4pnjOL3JxSu0aQfFPHJEQGQqtUr\nrSWrjxBdiZPiBT0srvhQZzos+KrdroJ7CBfDRl1JzVUSkKRUmj+aXFpbM0A7\neEu41fyFCELGBH0r4V7i7Vh8Ky6ZDSMvkptu/W70T5ca52+48TlKutUIFPaf\nS6t+2kb7ZUJ5B+9TGUhZNA8xBPBWxpdYgIv4S+plrgXW2EyFi8aewseIsEGa\ndkcKnPGoc3x6rFxDZy+aVCnDfyDHDs7a3VCkFepy3pCws0U7vHzoIT1mYXrM\n4DmIx5m2pDU6AfwQ/Kw5prbdgTf+kK91ski1i2suHNylFEwt2sHduAlR0qVX\nfIixn985ozA5iIDjz/fMD+QdM4eQ/4knl/HTtojPuXWF8z1Iv29HkDDTvjSU\n5om8yTPjwfqBIqxJT2LHCKEVkjNbyXRBC77N05uZ4nKXh7ZBkorHD8dLwWBV\nxqoS1f6Pjbo0+BrSboKaQVgKmiaHX0eWpWWB5PVC+3A+IJtw6ENPNF5qnNwY\nJfawsZnViqK3u0FMJJUdbDWkz7WGpPjrx3KKVFpCDSQWgn1hw4/ZG6IPJs+Q\neGpxnkqUylxmL/ltTmqqR0wivhlec+GXoM3a/YzTRD+C46qbSPG2T27J2/JY\nvVxf\r\n=V8VE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmAMEyVGgbD7frZfmy2vR0aaWAbL/hbIzByRQzp4es4QIgIwfwzI1ud0ruH2BDomavjvWGNaLMwmr0IfextuAw1fs="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_1.0.1_1610746984619_0.2793398305735728"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "@ampproject/remapping", "version": "1.0.2", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint 'test:only -- --no-cache'", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-node-resolve": "11.0.1", "@rollup/plugin-typescript": "8.1.0", "@types/jest": "26.0.19", "@typescript-eslint/eslint-plugin": "4.10.0", "@typescript-eslint/parser": "4.10.0", "eslint": "7.15.0", "eslint-config-prettier": "7.0.0", "jest": "26.6.3", "jest-config": "26.6.3", "npm-run-all": "4.1.5", "prettier": "2.2.1", "rollup": "2.35.1", "ts-jest": "26.4.4", "tslib": "2.0.3", "typescript": "4.1.3"}, "dependencies": {"@jridgewell/resolve-uri": "1.0.0", "sourcemap-codec": "1.4.8"}, "gitHead": "1789eb07b001f2ef9a9bfa514aa328d3373631d4", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@1.0.2", "_nodeVersion": "14.18.2", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-SncaVxs+E3EdoA9xJgHfWPxZfowAgeIsd71VpqCKP6KNKm6s7zSqqvUc70UpKUFsrV3dAmy6qxHoIj5NG+3DiA==", "shasum": "a7ebbadb71517dd63298420868f27d98fe230a0a", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-1.0.2.tgz", "fileCount": 19, "unpackedSize": 157937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpuTYCRA9TVsSAnZWagAAwIUP/0amzFWES3wPiSrzlDV4\nBVvD8efp8SJJtkByctTM+HXVOM7qOIkw2rSpFvyQDoCk+vncrv8xX9+PGPP/\nrJbRQGZGB8fH4LnvQi9lcnLPdLkBPem4SIRaeYlOQGgiOcXwrrPUofYXRDPU\nDQf33HEY8anE8G02EzQwrLNEO80aQMw7EzUL6JgIQR3OkZyFKTse5+WBHY6J\n3/gSvfC6aze4dn0GKkHTkY4LEARScp5lvaqH21hGc/MtvfbUZ/c3b83kjMNq\nFcACSs1WT0IEGWBS49f9l0Q8KBISNowoQ+BdEccHts8WbNDffHbZ0lW96ktE\n6B4KCUh/3NVqS1jEUw4detoi43CV0UTU92erloMYFq/iSCriTBs89XmYvN+1\nd4bDlKDINkDqLwGYiXeweK0h1Pxeg5uVreoacbwMrH7wngu/x6kMEBQkltTJ\nEdh3ZgO+gktZio8+M0gNFQ0FpB+ZCmyV7C3bJIub1Einqw0jpybQvmCVngvs\nh9EnvafpPFAFdKCFVJXJJRFgalCFmbkqQY6FTQ0h6odHi58z5ZpcczTfGn+s\nzyW6XfvCjmXxwFLMq8YmDbTds16gXUzzNuIFISrUW+UDVkMRSq9sXT3pjgUD\nitQdYKSY45r6HqEPvKt+xvXdh3rP3vjhlO08aCPJFrhcO4pJ+piBpowzY4JW\n1xsk\r\n=i1Re\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICV1Jynqtzyk5EXeCwdqzEvVDrWZS0EmRBto4CLSCQozAiEAknoaHKsO7fNEbGnJT//5Noig9k0GjnEWja3CPSyPkiA="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_1.0.2_1638327512315_0.9137581162436494"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "@ampproject/remapping", "version": "1.1.0", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint 'test:only -- --no-cache'", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.1.0", "@types/jest": "26.0.19", "@typescript-eslint/eslint-plugin": "4.10.0", "@typescript-eslint/parser": "4.10.0", "eslint": "7.15.0", "eslint-config-prettier": "7.0.0", "jest": "26.6.3", "jest-config": "26.6.3", "npm-run-all": "4.1.5", "prettier": "2.2.1", "rollup": "2.35.1", "ts-jest": "26.4.4", "tslib": "2.0.3", "typescript": "4.1.3"}, "dependencies": {"@jridgewell/resolve-uri": "3.0.0", "sourcemap-codec": "1.4.8"}, "gitHead": "e7792c22d7c13fa143ada195cc85291ee071a807", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@1.1.0", "_nodeVersion": "14.18.3", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-r14mg7ZG3xldgV8AcXJVMVcwB18BA4QGsmyYN2jXX7UYc7VMJYBjq4y9sRQnEQeBRSH9zsmqXS9GxVbFM3kMsw==", "shasum": "64ff45788e799bddfef684853d4f79b662eface9", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-1.1.0.tgz", "fileCount": 19, "unpackedSize": 98858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6/AqCRA9TVsSAnZWagAAl58P/jliGLK1WL9Br4lGxOgL\nJoOq3g0QtBybF1bimx7ZO45Anen2KktPXr09WPt/lWQv0lHs7vzdCKjdoM2I\n4HwNfYdr7Z5KeA98mmD+cIulb8NNuL6ukY1EZMTyhp+UU2NEtoN3AblJj2DJ\nxC7WyzyHPw6ovEvdaP/jVINOh3zE2TFR0i6mRk4bXTKRS7zzp+HHJpcXWqcR\nrlwzntFAHHQq+Nq8vy6yAHO1/Rrz2bAPR3avWDEFjRWc6jY+5ZFdjRJ/nJph\nLZQSDnD+kOb7vp7XdauqkmdhZB3D59Rb9TLZbM4jmWn2jFDAULR0qzsEezmC\n7poW3bnyFSaIhhT96tELY5tTIejpGW+/hCqL7cGuUbk8/gK2RvS/gXJttc1u\not/vHV4+7He2UNjOCGQWRT11exVjJ2p2YP4xjSDKpBTgckEzAaA0zz3jItWf\nBtvWV3SLwkZjWHlQrItN3cv9JKDHvdD7EzgDWooGscbVte6K1okAsPqE/23q\nfYFL33g4enqgFfj7hF9wlgDlhi26rsmyczh1sNU9Azcq0fiLPbdxMfwSGagK\nzWLJaH2KCBl7gE8d+Gbel87+IaY36WpHPLkrGKaRDrjMna48xZDSFPelHt58\nrYyu/yDzUdmXj3+G+bgcUcNr/5f6DIU5hgOUdsQoWSM4ic990LZBe9oYEzhD\neWKv\r\n=MilV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA/C44pCmjAIbq3LwlJArPkD0s/2nTxMv3HPu1XdRZ1sAiEAm38LnyqlS/XJaSh35tawVBoc75xmcV/B96kONQr6VYA="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_1.1.0_1642852394608_0.20165798740340413"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "@ampproject/remapping", "version": "1.1.1", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint 'test:only -- --no-cache'", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.1.0", "@types/jest": "26.0.19", "@typescript-eslint/eslint-plugin": "4.10.0", "@typescript-eslint/parser": "4.10.0", "eslint": "7.15.0", "eslint-config-prettier": "7.0.0", "jest": "26.6.3", "jest-config": "26.6.3", "npm-run-all": "4.1.5", "prettier": "2.2.1", "rollup": "2.35.1", "ts-jest": "26.4.4", "tslib": "2.0.3", "typescript": "4.1.3"}, "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "sourcemap-codec": "1.4.8"}, "gitHead": "3dbf8a4169566f3183906eac3b16687ec90118a2", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@1.1.1", "_nodeVersion": "14.18.3", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-YVAcA4DKLOj296CF5SrQ8cYiMRiUGc2sqFpLxsDGWE34suHqhGP/5yMsDHKsrh8hs8I5TiRVXNwKPWQpX3iGjw==", "shasum": "e220d0a5288b07afd6392a584d15921839e9da32", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-1.1.1.tgz", "fileCount": 19, "unpackedSize": 98859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7Q+2CRA9TVsSAnZWagAAOhIP/0xvbFQR7Z2zVuaVq4PK\npAk6H69xxtfuwfpB7xewzHPBsJLyEdJStjJ69dTW/4VDDj3zEvswlYgjcSIu\nVF5ATnweI+y8uo5W+GVPg2IfQG0/Ruv6QDAkP4xd2u90D3F7GDgbwMrunBuW\nva+VaIwoSrCIbK00qGJY642nsq6NMjQqot5ePX4aICbfD8c5TZHxUBtXr7A3\nABuJPKk14cGgMqUQZApaMcRP+zBp16uqJRtAdtLeEZIuEpbKJuJVS7h7PG1y\np4cxhUIPL6NmqTG95SBCL+fSxS/BJ5D5w0PWMc8t2yALvt8zmlzGV7VZRDSo\nB4yCsm7zrQLUiiKzJpriN5r+Y2/qrh2BCP5DGTCT789oIC+hb3pRtxp5vmUp\nTvEid9H2a+xe9Y8cf6ZkLz7DiXc8ea6LBByMY3pvJPOT05l28cjxEorKeB5A\n3LvP+sLpz7SIGXFPJqfVTurd4ZaJPW3whNby7k6AAsOj2HG2VSgj48BF1n/D\nID3ET1GWsUBEfrBd6+R8r1C16NkIEN9lYZG1HEhFYbXMkDAK7vG8nBTbqQIx\nGPrllZ8xy2TRd3WaMMIdyofdc4NNT8zGgGM9LZqv8wZT1GqdFcb6gSlCPfWT\nLd0PhLCfaaTU+37/ypTVMarjIp2mGBcSrx1hZhBkMv3JZd0yA4jOxrF/KpUP\n8UUC\r\n=+kJB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAgZAHfZf9Ccq2W38DTWX4NQW1jEr3E5i4jFOJNFAN2gIgFzGwlPYGHEBH0DY1wXudDBQRwBhHCSSw2j2V8DYgb6U="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_1.1.1_1642926006409_0.40858759519682164"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "@ampproject/remapping", "version": "2.0.0", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint 'test:only -- --no-cache'", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/jest": "27.0.2", "@typescript-eslint/eslint-plugin": "5.3.1", "@typescript-eslint/parser": "5.3.1", "eslint": "8.2.0", "eslint-config-prettier": "8.3.0", "jest": "27.3.1", "jest-config": "27.3.1", "npm-run-all": "4.1.5", "prettier": "2.4.1", "rollup": "2.59.0", "ts-jest": "27.0.7", "tslib": "2.3.1", "typescript": "4.4.4"}, "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/trace-mapping": "^0.2.0", "sourcemap-codec": "1.4.8"}, "gitHead": "fcc9f442f43249389dc946cbe8bb3736a8033d6a", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@2.0.0", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-Ag6L7Pjt3QxYKA5hgc1bvq6pQ3mD7b+BpPqciz38Het1uPsZn5C3/9NSlQyNPlKIKwZxupcU/9TMjLbcEssvsQ==", "shasum": "a1ffcd0e63cae5a6a441669fac1dbc5513cf6881", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.0.0.tgz", "fileCount": 15, "unpackedSize": 59785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8h1vCRA9TVsSAnZWagAAckwQAIdV7/3wCwl4z+hN6RJV\n4cAbmJd3x7QdiYMUyPqF+XrTFteInQt2oSF3r/7FyHRhwdWm9jn4la0OdK2m\n0YVvKHiLaSxDp1mO/9Q88O21Uu0JvIBvEqORMx8VJFw9rw+dznbhnloga71j\nii29UZ8/vf7nOpEuzflJ6zVjII6iWZ1mCICqBFZNyOgmOi+NylfDlh/KUA7v\nzzwWQ5dWOQJJwCLASbXIHRD3OszqNrifq0fS6duEB6WPrt6HVDt7jtRZi1ed\nJoSNHHNe1tGeYC5VhFqiQO2kyzPT7Su3thvlwaFZHtBXebfsuH9FrUt1KRnR\nEQdsErvM8DbfZv23O51rnzV94e6Vb573NtzsP9J42smRcuctxTuEv6I9jLdD\nC1VEuDSW1ypYuhZ8vqMR1wAEzfxI/7oP4dSH7Coo6GwgNlRQlhuqenKlBoSx\nOvxq3ELkvOd1tAZcKJ0GyB813KnuzeAgpJrsx+4bI4Rh4OABy+/WOK5MVFhI\nSBFocuHylhTqEYUxt++AweE7IfSkKgEDzmzxFkuj0Ruoml6soUNB1TtPsilk\nBq4an1w1oaoQjkwPXIwJylA7O3CCRwlB4cY7KaoPp8W+HXB3QUuzSS3jzM7N\nH/mB2ltKMJzLUmaCptjMb1sLsXwlc29MZM8bRum9sTZ7eGduFRlZWF6vl/Nv\nLoe8\r\n=11qM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPBx5+ZZ5mJk8kiA0bu7X/bDjomrgkAjnEudQ/Ay3kKgIhAKIRmcQynisfSEb3bIjL6vZFauWkydgqOH4T8ARYbVZO"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.0.0_1643257199349_0.39243483765565146"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "@ampproject/remapping", "version": "2.0.1", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/jest": "27.0.2", "@typescript-eslint/eslint-plugin": "5.3.1", "@typescript-eslint/parser": "5.3.1", "eslint": "8.2.0", "eslint-config-prettier": "8.3.0", "jest": "27.3.1", "jest-config": "27.3.1", "npm-run-all": "4.1.5", "prettier": "2.4.1", "rollup": "2.59.0", "ts-jest": "27.0.7", "tslib": "2.3.1", "typescript": "4.4.4"}, "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/trace-mapping": "^0.2.2", "sourcemap-codec": "1.4.8"}, "gitHead": "6df5fd13a4b4b0f95902149938f616c8b31aece9", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@2.0.1", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-EldHF4Ufj3NL9yCAmYrPzY+3/Yqrzxu24F4Mu4nRjK3w70AKYRmhuLwGZdA9JeoDsbIwkgGkbqUK2INuF582Og==", "shasum": "9a04a4aba7b8323b65498d9554a1bdd15d960296", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.0.1.tgz", "fileCount": 15, "unpackedSize": 58008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9ONmCRA9TVsSAnZWagAAAdAP/AkkL+UtorAST3xOLCbq\nrUC/xCiiz23GLbYx4BVKkBOzvaOMXCEhQfBtH3tNdDcaNepjcQP0iivzboID\nIPY9jpft5kjrbYe5ZA2+uwj9Vi6mbU8Olx09g3icneOOJgMVCABvZSFCXoF4\nq6vOCK97NWm1qKkHt1YfWu64ka4y+IRMg8yA9fdkv9AomS6WTha8n3UyG9Br\nGJewxGPboQ7u3g1OIhcTaulK/WgHwqeCzwX8j0TQba+mmwt8/MhwWYNjQnSI\nekRYstBslWJcvzM2uOCGhtUmEk+ESH1WIJXq50nZIM6lT0Nr4KClVtsmpDY4\n8PGpNH59JK/BeQ00/VWjoDw5TKTWcOcgSMop2hTuAFHyULqFi+hP4fVAcZHA\nToxGX2JkbdA7kKTo0NTFmDzBRc8qMA9QSGj6FHh9X1BMlwjl8uWKTvC8cENl\nsljSxd/h5CFfthYBGHX0I0gPLmUpcZHmoIAbmO5+59ETXi2OIlBf8xp/cji+\nZkSmWA93qNJEw5+dOahWQ0f9RSCPNQSzSsaeJgetqQrfP6I17cQ+TYtoZBNz\ncwyO4Hx/o1wTVH7XG6LHW3T3Q9lSMNQAQQdNqRiTRtvbNzZq98LDefK85MPO\nBJqtaUi7iJuJvLUSm51Ya1PAQJHqtwpcexNKWMOUBsZBfGwNKl7cdPUs/aa5\n1hPi\r\n=IJDA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVmnPn4PJsi/RpyYi9T9wEapgMpQ3r7UDLHRdK7VjA8QIgboc17JLHIcymvipSUyBlOaxwjW1cE16gQBhG5J40ts0="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.0.1_1643438950143_0.9439718322264126"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "@ampproject/remapping", "version": "2.0.2", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/jest": "27.0.2", "@typescript-eslint/eslint-plugin": "5.3.1", "@typescript-eslint/parser": "5.3.1", "eslint": "8.2.0", "eslint-config-prettier": "8.3.0", "jest": "27.3.1", "jest-config": "27.3.1", "npm-run-all": "4.1.5", "prettier": "2.4.1", "rollup": "2.59.0", "ts-jest": "27.0.7", "tslib": "2.3.1", "typescript": "4.4.4"}, "dependencies": {"@jridgewell/trace-mapping": "^0.2.2", "sourcemap-codec": "1.4.8"}, "gitHead": "e43d71e76dc54eaab3b07e6e73a217b46175e774", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@2.0.2", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-sE8Gx+qSDMLoJvb3QarJJlDQK7SSY4rK3hxp4XsiANeFOmjU46ZI7Y9adAQRJrmbz8zbtZkp3mJTT+rGxtF0XA==", "shasum": "f3d9760bf30588c51408dbe7c05ff2bb13069307", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.0.2.tgz", "fileCount": 14, "unpackedSize": 57885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+2wrCRA9TVsSAnZWagAAlGkP/0bxqCwqlRGxBEf1n9VU\nItzjC0BAuBoGFHFz8wqX2CY+aL01BvND5iv9DBCzdv1CQeO4BlcJqqz5PaIM\nprVQ2yhdqqavwZxszbtNMFuquYLXHCvPJFlN7TIb+wjdtY11StaH4sV/7/8g\nkIu2+3UBa/77wV7Bclx6e3jvgOtGHEdyBC5zvxKP7k7hIC0lJnxDbJn+oBMv\nfghntEchxAkoRJfsiWT+4LWzu907OWVB6v3FK5IHYGxL+aoiI9VLCsbtkqEL\n443fYcCiMpq0K7UwYDj4VOe3+5GP+A60mkAclvNoUiRT5/lXAK3fbC9GBiOC\nLAA+cbe/sKJQU57aSZuXGG2PYHkogiTmNuKV7Lel3uBJqRHfnJdeSQzP3n5t\nAXaj0jcX/PMLxkdPPNJ0EQY9tmnOzWJqGeqUn5q5L4gs9pxqTAwUa2XlEJq1\njKEYhzxx2wrBZZiN+wQHEmFRSECWp/b1SDPczBPdXwNRPY4jaXJ1MnqYGwdQ\nPHE7g6hUdcOV4uPrdlhRSUmgZBFzuVIKGlkIF/YB4imG/jN6SWpcEhW6aLYN\nQrPRU6W5mgL2I8IlErgOrPVi32uFieDGGxiqPucUCVqInK4q8LJ3aCoKD2tu\n8bZTVGOVm+67geZyqtsMy2LgG0dpNKFJg2TG2Q9/0b2rTTWqF75pt79Ld5u9\nugLH\r\n=syJP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAPox35naZTtVLfxWxQmp7NkvfQzkARmu09ZwUw949gZAiAT111ZFezjshYtIzN6cjr5H0PISsavAkDQcAF9tc02gw=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.0.2_1643867179462_0.7923134641618099"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "@ampproject/remapping", "version": "2.0.3", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/jest": "27.0.2", "@typescript-eslint/eslint-plugin": "5.3.1", "@typescript-eslint/parser": "5.3.1", "eslint": "8.2.0", "eslint-config-prettier": "8.3.0", "jest": "27.3.1", "jest-config": "27.3.1", "npm-run-all": "4.1.5", "prettier": "2.4.1", "rollup": "2.59.0", "ts-jest": "27.0.7", "tslib": "2.3.1", "typescript": "4.4.4"}, "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.9", "@jridgewell/trace-mapping": "^0.2.7"}, "gitHead": "a67210717f5dbc77d0932c06a15293b24619b7b5", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@2.0.3", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-DmIAguV77yFP0MGVFWknCMgSLAtsLR3VlRTteR6xgMpIfYtwaZuMvjGv5YlpiqN7S/5q87DHyuIx8oa15kiyag==", "shasum": "899999b5b7a5ce570d6d9bafdcc1e62cea466cf3", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.0.3.tgz", "fileCount": 14, "unpackedSize": 57913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/g3UCRA9TVsSAnZWagAAUwwP/0I2//R/YEucdjy7sI6y\n3kYJp6p5/Gea4xzgAPcNRZinU+NEKll338iS5GTrcoBAVaJSYEHx51oopLYW\nZBvNd5OPXUwoklcF0geiq1/38xSJKeBy+vEdEFN0gnJxswsbjodjJRohoNcb\nI37Vulie3V4ghVSvrdQPwewhVC/oQ7gDVKc9wEWeJSk7dRsU7gWzBRY2MTWj\noTBExrLsolhZTvO8PhP/Nox8Doaaq5vgPtM7MBVFGh6ZuwTzUEMA78dMMkLP\nD9NAJlJUycWNclJogycYETGjSy6pDbEqM9tnQy3jclXcZ2N+LwcahiaEJTQx\noSiwT3jdBHW8UcIfSZBt/xriNX+Od3HR4tgHMSEz0Oxp9x1aoQOZUhxEiDm2\ntRSbjbmgKjx7NbaGJg4VWw/hcpIQOH1gMbRPHY4JgCqmqZivDZXanfaB+bn3\nNkeKVMDH89gysPHY8xudtMQYD05Xo/oT0SOqSMvffVUuQDvoWNoJUJCuS5gQ\nHRAadt9KFT6HH6dVfMUXeLCyaJ3qyGOSXHppYZri/hC0mp7eNBiMD8eRzeEi\na1kGzo1A8zQzcJg/wj4zRYvHNRW/UIVkQ+XbFc0Xdd78znQKttXKH7T/pn00\nMYUMk4YGH1qDZaoRicEjTpglAwiw1ylAiO57zT1LtLV3prmfat+Abe05bUAn\nvi39\r\n=FkV9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHReiTZU6ulOcFUTo4HOXo51a1338Fzsq1+aN2MRtGLYAiBnSKncLM1Rycx5MffWPTUAXE9FbgNJJTUPLordzen6gw=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.0.3_1644039636324_0.12446334720971297"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "@ampproject/remapping", "version": "2.0.4", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/jest": "27.4.0", "@typescript-eslint/eslint-plugin": "5.10.2", "@typescript-eslint/parser": "5.10.2", "eslint": "8.8.0", "eslint-config-prettier": "8.3.0", "jest": "27.4.7", "jest-config": "27.4.7", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.67.0", "ts-jest": "27.1.3", "tslib": "2.3.1", "typescript": "4.5.5"}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.0"}, "gitHead": "309df63fa9fc686876ef7c307b01edd7f3e33bc7", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@2.0.4", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-zU3pj3pf//YhaoozRTYKaL20KopXrzuZFc/8Ylc49AuV8grYKH23TTq9JJoR70F8zQbil58KjSchZTWeX+jrIQ==", "shasum": "ab4b6d858526ebee0d6c3aa5c3fb56a941c0d7be", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.0.4.tgz", "fileCount": 14, "unpackedSize": 59823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/3HUCRA9TVsSAnZWagAAw7YQAIAC2BWp7+1zgTzRPYzW\n7d0IqIHzsAXKWJSXgS9tWdvQG69Y5nU6f9VRaZrU+qdt1dCo21WXnHfSlAAd\nq/TGp44dPDXpPPG0PLC3Rr6h9Z7CGvLVwUcRpbsR6lyhjWUWo5Yy1kYMVWkR\nqFAFkwF1qGOJbJzMtSnXuT08p9KweUvlgY7DaCK4mNgavatr9fMhUERAf4Mz\nJ4rZeNhV1EAFMubKU1yS2dmvEwec4coZ2oKXsf8X37XGaxsLKdWSIX6ALL4x\nizPdxjFR93gkF1KbTSxZJ4gXdRZA9IaPSy5TRVNkVgUv956rHY3Z/Bq5hton\nF32v6w8qMbXB8KFDd2+gXNDImLAmmdpRztjOF/IyRHQH4e+ckeh2xIft5tjt\nNZp8aKOVRjcXOdRuoKo8/1ugzqa4IscX7tpU7adcHLHho+AxelXAeW935YyD\nE++UcobAK51Hs9AcrsMmURVc+oEaQdD+CvuLtsVy9Dznzr1cJGVsmEecdrlb\n6yA2Yq2LEXntW89T/fASFxePZDi4clYACJFj6mA6K+cHJ2jUDy8eHsv3m/Ys\n2i84bvScrPQ6u+feyaHK4iafm0d+/zraNvR2oTbsOEjjbvCMnjzZLq8fGnj1\n78pX9UajQkYYl9uDhd/couw9KvTZkzygT4wS2h9zFc/CJcISKXpyDfAt2wHP\nXB5y\r\n=alqP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEX/bFyUt0O2wmAYEqf+uvvydvz77mPa3seT3nh7lE4IAiEAjHadBUojR2baPduqgDzqvcts1djAC0GuXXA6mkgBJA0="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.0.4_1644130772076_0.46101192893554144"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "@ampproject/remapping", "version": "2.1.0", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/jest": "27.4.0", "@typescript-eslint/eslint-plugin": "5.10.2", "@typescript-eslint/parser": "5.10.2", "eslint": "8.8.0", "eslint-config-prettier": "8.3.0", "jest": "27.4.7", "jest-config": "27.4.7", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.67.0", "ts-jest": "27.1.3", "tslib": "2.3.1", "typescript": "4.5.5"}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.0"}, "gitHead": "7323bcf984463ef7abe85215cc3f8de123f57693", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@2.1.0", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-d5RysTlJ7hmw5Tw4UxgxcY3lkMe92n8sXCcuLPAyIAHK6j8DefDwtGnVVDgOnv+RnEosulDJ9NPKQL27bDId0g==", "shasum": "72becdf17ee44b2d1ac5651fb12f1952c336fe23", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.1.0.tgz", "fileCount": 14, "unpackedSize": 64326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAMZiCRA9TVsSAnZWagAAMzsP/08Htx9yjqzNYstz2VDY\nqvV+Hmfsu1LT8/8rVsHj6+Lmf0cR3OU3vgbEduQqyWeodfuBwNeRBU8OyBcV\nU4Hv5PVqG0Is+MMte598bDJfTTKfDsal8wsbmk2wCbtN8bi6QOd3GhnBhrui\nUMY56FbLMlHzSC7idTJnHAP9g9d6FdrfdLGmQ0bK8jkPK2BsVr+5ZYVQvHda\nDLN9ilfwxRyh1+RsTm/1iNpqAr+88RBwZ8Kgwlu+YoAmpz3+Et09MpBtx2Ki\npQuyfUgBKRyVK0t7wCFQCskVdSgHm9fS5blN9twHDDdTVHrvnlwcOKIRmUY/\n/XFVKlDvwgiICYDkm+UforbTbgzEyZbFUqKSLJTcZuBBmZNXIWs2gy1JKgl2\n2YzhXXGjvSHpqD7TakV7MsBLjokjumj5JsH+5kMgjVnza5ivfrRJPUv1la+W\nZPseVJ8+pmOunDQffxjhIHi315zyLtvHARBX4yyVJI43U6RAjW1Dd8Z98GHI\np5qn/X7t//SOEVV1+M583bztIU+HAVt/woBSKscYkoIZfkJ0kdJ6piTVm7GZ\nG8igCYXDkZL1UP6n5WjZkMaloAXAlcdXOOvMB+UoEfQFQIl1xLs1jDsakf/2\nCeG9Rp1hVJYpQbwzchk3bgyOhwzY0aSFsF2eZbGZS9YwuUwZM3iZoyfebUZ1\nJdBo\r\n=vAuM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID8lVoonCb+itlI6m8HgxCyZIA1AFOhhKt0kJ+k686uvAiEAkWgnUbgCXft8poRyKu1t20kEum+5nnxGO9jvH+5GJvA="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.1.0_1644217954101_0.6571377857516434"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "@ampproject/remapping", "version": "2.1.1", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/jest": "27.4.0", "@typescript-eslint/eslint-plugin": "5.10.2", "@typescript-eslint/parser": "5.10.2", "eslint": "8.8.0", "eslint-config-prettier": "8.3.0", "jest": "27.4.7", "jest-config": "27.4.7", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.67.0", "ts-jest": "27.1.3", "tslib": "2.3.1", "typescript": "4.5.5"}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.0"}, "gitHead": "13c136f3164c306edd465d5b58b2326abb263d13", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@2.1.1", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-Aolwjd7HSC2PyY0fDj/wA/EimQT4HfEnFYNp5s9CQlrdhyvWTtvZ5YzrUPu6R6/1jKiUlxu8bUhkdSnKHNAHMA==", "shasum": "7922fb0817bf3166d8d9e258c57477e3fd1c3610", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.1.1.tgz", "fileCount": 14, "unpackedSize": 64341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBhqrCRA9TVsSAnZWagAAv7YP/2ntUYl77Xucs90HMyHv\nYL8bAJl3pGlOpzi1f7wmxJYtohYTkyaAXTPzkDQ2MeZ/hu/Dni/2PhQg2l7y\nleedYB32ujxKdxVGyuZ1GTD7bQZOrjoL+wR7og3wKztFaZRiDTCGjpuO9scp\nm+2VFZeOMtWXt8pJ1V5EtVEfNMrxWHxr3KQnqsHyKfqTLdFwxoglLzK4m19l\ngH6dcMcQE5zB4P0XWBWsxiyZV7hS5vJNv2qXs8hkWQSyNQusOqhwml2nD7Yl\nkenlOxGjq+TtMwqppAEd9z+MgMK6rFEr7Yauy3VsQAkgkr95qlIA/i7E/uoi\njO3B4WzMOXHsTJzNLQ+e5vejGSVsN3pfzoP1uFWHkgqsDWSiavU1P0yDC70L\nAvfjvNtPNdES9Zw2kjf0rOMnXQ3z3cExi+M56XzjMSqSo+b4W0RKHv0zS7eZ\nBNXqcxzsgvmf2WlBvyCPXM7dSq0WxrNpUYFq4TCTc18QWvlgS65jNDJPy3Rn\nc0XP4PqV7iNXQ/Unw4gwI2QYcm96O29NYWJkfMBxg3g8HxULndXYzS9Alj7Q\nIB3bU5QEBnRy0ou0T5tBrgzSpXCPyajm0b+YjN5r7smxaeXry+3OzfpthaTB\nOOBjVGA+V6asKFJE8bSh8Ncvs+iSY1zOZ5zD5xu8NrP9q1ussuiGo5BF0ggr\nsjIq\r\n=4Gjw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHoTJ45brFtPeOczFdyiCO/WIrpVVHusZDx0q6YDxONeAiBmAtwdC/Es6tI+7WMJSxV9K01aGaiIUKtvlovA+wLOtQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.1.1_1644567211092_0.29203851662118074"}, "_hasShrinkwrap": false}, "2.1.2": {"name": "@ampproject/remapping", "version": "2.1.2", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/jest": "27.4.0", "@typescript-eslint/eslint-plugin": "5.10.2", "@typescript-eslint/parser": "5.10.2", "eslint": "8.8.0", "eslint-config-prettier": "8.3.0", "jest": "27.4.7", "jest-config": "27.4.7", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.67.0", "ts-jest": "27.1.3", "tslib": "2.3.1", "typescript": "4.5.5"}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.0"}, "gitHead": "cd8b4479e129874855e7090a2fd99b9acf1fe581", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@2.1.2", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-hoyByceqwKirw7w3Z7gnIIZC3Wx3J484Y3L/cMpXFbr7d9ZQj2mODrirNzcJa+SM3UlpWXYvKV4RlRpFXlWgXg==", "shasum": "4edca94973ded9630d20101cd8559cedb8d8bd34", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.1.2.tgz", "fileCount": 14, "unpackedSize": 65167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDML0CRA9TVsSAnZWagAAmn8P/iXwDTfogZhZRYiZidBW\ngTJGojZEIeUJYxjh9uY6idY+NRyl6+utusg947/Syt9f7oFBEGz3RGM1D4B/\nmoaXp6DsW1nXCKXp4CblD84Gup7aAh+tirlQkQFnmu8nKsRzX6zcZZ5d3yi/\noa1RLOyUBugLHBqddziDym2KBzdfJNaxSdGAcfZHeK9uHKWWk56vLwI3XvD0\nFGFPk8pM2CbJUzOxSY9w7+fwUJC26ZVIvGCr0NJBggdnWsTldqfeVwJn74wz\nwwXP7QBSqKbWd+yp8TmkaAuDjuWGhlCXTB4Y8Dz3Eff8n7H222a7eqAPrptH\narWScRjirUHg3a/vktRHpDhR7WXTJ1tiCdDrpF7jfhEgPqwqprHN9OSgNym1\nZyzjLaXcfMm6oMpo4RMqE4ncZ1U8T2RRVrGW3DADqnyztQc+xepA3BM+EeLj\nMT1v1NFB6Fgd2ZCFhQdMP5ydaQ4pT7fkJcwyqi3smFJZOTAY8vlDWzGbr2Uk\niVdxCJkiqCGyrTUPwdO2xQtJpq9IaGttQoCyn6xQERla+Km9uXGGy1pNrvlV\nLGRnphvurYroB0B16pW/SJyBQ3aPQyHT7eACtBPdP4jJH8dgXMrAq1rouqkV\n1TE9lQQk+hlRBna++Zt2MK8ejUn8l5d+k9cRCowXA4C2WVh1K/LVVa6ljQ6b\n9XOk\r\n=tMYN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDdOuI/drqArKCwTqJoECFYZsxZzt4iMjZ4m9DwLnc2AIhAL3p/K3qfq/22MF5H/Dv8GMWnX7gu6S1aKuWQP5WMNCc"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.1.2_1645003508366_0.639303950663463"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "@ampproject/remapping", "version": "2.2.0", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "typings": "dist/types/remapping.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.2", "@types/jest": "27.4.1", "@typescript-eslint/eslint-plugin": "5.20.0", "@typescript-eslint/parser": "5.20.0", "eslint": "8.14.0", "eslint-config-prettier": "8.5.0", "jest": "27.5.1", "jest-config": "27.5.1", "npm-run-all": "4.1.5", "prettier": "2.6.2", "rollup": "2.70.2", "ts-jest": "27.1.4", "tslib": "2.4.0", "typescript": "4.6.3"}, "dependencies": {"@jridgewell/gen-mapping": "^0.1.0", "@jridgewell/trace-mapping": "^0.3.9"}, "gitHead": "ee9a0b022cb8f739ae36bd39a2ca4bfdf1d859c1", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@2.2.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.7.0", "dist": {"integrity": "sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==", "shasum": "56c133824780de3174aed5ab6834f3026790154d", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz", "fileCount": 12, "unpackedSize": 55316, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUcSzEGqbqUBYd8Cucxu4adcKWXxnI5PPCg7O89NEMBwIgJDj78jDKef0MTLAWh8NyfJ3dBxUJuwmVEHZH+esBP/4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaL2xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqs7A//To9PUf1sfzrCWTh5/wkqt+Scl531CcZI8+3Lw2pXkNt78K0M\r\n+gKS6hpwyqggN66BAitEU6z/+mO2DgTzb1vsUUDPAUGtEQ1MsCqHokmOBLZA\r\noFShOVENJ3mzNX0zlhgFgGGt1Z0kmN9H0tX7ykmOwFKzXaqCyq8AOnv1xXRq\r\nEsKXkmig0WypKDBWF+9/KXCryj7Dvql499nMWXP8LZs3RLKuPY7o3joM3ZXr\r\nWLYP9DxV32HjMiZGBuybhzZfKbep47sHM3s8qBfN7HZ8pnHsXxAWM1YA80YN\r\nhI8Yv82+jqJ8UOH6OIVZqZoCYX7TO0PBI5qA/F+E5LcGeQkIY5kl0F66hw1d\r\nJcTnxTVHut3H8NAjPWFhEjeBRK93btxgAoC6swkWr/8F43FqRa1anJnjA1Pg\r\nxWxnSiPZyQ+jn9yEWAq5W81gM7t7+3WlkfWrDQ0o0q0Bv4koyJ87VB0zWAkq\r\nFXMY194UMNS7t41r5k+8UlPcRErcL1E59U3f7cj92sCuwxhTEun4ToFwaM0M\r\nYMUZRyePvnLsX1VPJCAEpfW2IZswGWKJQpJ3dmf78luBQ+NWTQFBnFedCjSc\r\nwbaqbge/4vZt0zhgPB2iveHvxbnPPpjOlBdbLuelbADD/pG4CAWf6dkkXTed\r\noRxe3LinlIDuS9kT/cdllQ7MlyePKe+iW/8=\r\n=kYyZ\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "choumx", "email": "<EMAIL>"}, {"name": "amp-toolbox", "email": "<EMAIL>"}, {"name": "f<PERSON>is", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "caro<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rsimha", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "patrick<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "alanorozco", "email": "<EMAIL>"}, {"name": "kdwan", "email": "<EMAIL>"}, {"name": "ampprojectbot", "email": "<EMAIL>"}, {"name": "esth", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.2.0_1651031472899_0.12018851986441148"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "@ampproject/remapping", "version": "2.2.1", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "types": "dist/types/remapping.d.ts", "exports": {".": [{"types": "./dist/types/remapping.d.ts", "browser": "./dist/remapping.umd.js", "require": "./dist/remapping.umd.js", "import": "./dist/remapping.mjs"}, "./dist/remapping.umd.js"], "./package.json": "./package.json"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.2", "@types/jest": "27.4.1", "@typescript-eslint/eslint-plugin": "5.20.0", "@typescript-eslint/parser": "5.20.0", "eslint": "8.14.0", "eslint-config-prettier": "8.5.0", "jest": "27.5.1", "jest-config": "27.5.1", "npm-run-all": "4.1.5", "prettier": "2.6.2", "rollup": "2.70.2", "ts-jest": "27.1.4", "tslib": "2.4.0", "typescript": "4.6.3"}, "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "gitHead": "d791f83a250da5e1741bdc86b906ca01f6bfea1a", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_id": "@ampproject/remapping@2.2.1", "_nodeVersion": "18.15.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg==", "shasum": "99e8e11851128b8702cd57c33684f1d0f260b630", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.1.tgz", "fileCount": 12, "unpackedSize": 76011, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHv2R1N/ln02bucvphRpbbXxGABecsaZdYYmjhacGLz8AiBkN9xbljGRM9PiSU1ek22fZAczy02qxfns5CbkGgPJWw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkL4NBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxGg//bYiWjDB3/WcTuaxWuObywGm/ZwnX8daCodkA6S1gb86Yyjr8\r\n8Bk0eEONehJbHaAnxzcaG47JYwUD7hh+vNyniAINZ/JgJehQRMyINlzUm4/q\r\neWlo2erqwtRHUqSYsgK59eSEzNNpmguDbx9vaU9KFX1VxVba+yjbSEL3zoCj\r\nYNANhDaOcsgNbZWzGzD6heWDzf6+kkuQPntymd/i8VnfXF1b2YQ+i3HGfFZl\r\n+3JIG5oXQOR6ceiqDmBocHefSGQ96bJx1jAYlVJhC8FplZq019d+0JxnYR5V\r\nismaxlRxXX8Vnok2MVpbKlT/8RHliuPIiLQPjZQM3ZOJiKsxL9kxDndOUPa8\r\nXReQUsTpwBjOX2ef867Hrk2ldy8NPhgsmIX0zG+8qvGzkXQFWjbiVAMgXuQm\r\n2DfdE7aZ0B6WRtV74RpaJuXmUh910jdyEut4PvycdGqwmuBW2aLk0xXkXg9T\r\nxYA1LTUsIP6dfuqS/1Pz2oew3z/RTccIxaMV6YQoRkS88kL2sLtXPibEq/Qy\r\nBrU4R5yMb/PMX6Rc/GEHnLq8WU2POw59xmT4R0UoIrqMdtMNMPVIOoSJuk+L\r\n1ZW+rVvtKlPUgr18rxXndVDendgm7cx6tsBDkgaC8xGxPbyHIske5hXwrF+x\r\n/snV5hjJl48z7RwNXyX48Gwq8cNa/pFEO0w=\r\n=W08l\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.2.1_1680835392916_0.4769442103737964"}, "_hasShrinkwrap": false}, "2.3.0": {"name": "@ampproject/remapping", "version": "2.3.0", "description": "Remap sequential sourcemaps through transformations to point at the original source code", "keywords": ["source", "map", "remap"], "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "types": "dist/types/remapping.d.ts", "exports": {".": [{"types": "./dist/types/remapping.d.ts", "browser": "./dist/remapping.umd.js", "require": "./dist/remapping.umd.js", "import": "./dist/remapping.mjs"}, "./dist/remapping.umd.js"], "./package.json": "./package.json"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "license": "Apache-2.0", "engines": {"node": ">=6.0.0"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "jest --coverage", "test:watch": "jest --coverage --watch"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.2", "@types/jest": "27.4.1", "@typescript-eslint/eslint-plugin": "5.20.0", "@typescript-eslint/parser": "5.20.0", "eslint": "8.14.0", "eslint-config-prettier": "8.5.0", "jest": "27.5.1", "jest-config": "27.5.1", "npm-run-all": "4.1.5", "prettier": "2.6.2", "rollup": "2.70.2", "ts-jest": "27.1.4", "tslib": "2.4.0", "typescript": "4.6.3"}, "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "_id": "@ampproject/remapping@2.3.0", "gitHead": "395a8e7ef55138d9c889214983e351cf48b0b530", "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "homepage": "https://github.com/ampproject/remapping#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "shasum": "ed441b6fa600072520ce18b43d2c8cc8caecc7f4", "tarball": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "fileCount": 12, "unpackedSize": 78946, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJRKbt+BW9tvtkpm6zRKhUaEeVUW+x63Y5OfgkCxlhuwIhAJzWZu4aCZ2wXgKTxbTUnWJDWg0HCnwzN27KmLq5eVZi"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/remapping_2.3.0_1709284238895_0.6736711643480133"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-08-08T17:52:34.963Z", "0.1.0": "2019-08-08T17:52:35.249Z", "modified": "2024-03-01T09:10:39.951Z", "0.2.0": "2020-01-29T01:35:22.956Z", "0.3.0": "2020-06-13T09:17:15.752Z", "1.0.0": "2020-12-19T04:25:05.723Z", "1.0.1": "2021-01-15T21:43:04.773Z", "1.0.2": "2021-12-01T02:58:32.506Z", "1.1.0": "2022-01-22T11:53:14.764Z", "1.1.1": "2022-01-23T08:20:06.577Z", "2.0.0": "2022-01-27T04:19:59.480Z", "2.0.1": "2022-01-29T06:49:10.337Z", "2.0.2": "2022-02-03T05:46:19.601Z", "2.0.3": "2022-02-05T05:40:36.464Z", "2.0.4": "2022-02-06T06:59:32.302Z", "2.1.0": "2022-02-07T07:12:34.233Z", "2.1.1": "2022-02-11T08:13:31.269Z", "2.1.2": "2022-02-16T09:25:08.517Z", "2.2.0": "2022-04-27T03:51:13.056Z", "2.2.1": "2023-04-07T02:43:13.129Z", "2.3.0": "2024-03-01T09:10:39.024Z"}, "maintainers": [{"name": "ampproject-admin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Remap sequential sourcemaps through transformations to point at the original source code", "homepage": "https://github.com/ampproject/remapping#readme", "keywords": ["source", "map", "remap"], "repository": {"type": "git", "url": "git+https://github.com/ampproject/remapping.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ampproject/remapping/issues"}, "license": "Apache-2.0", "readme": "# @ampproject/remapping\n\n> Remap sequential sourcemaps through transformations to point at the original source code\n\nRemapping allows you to take the sourcemaps generated through transforming your code and \"remap\"\nthem to the original source locations. Think \"my minified code, transformed with babel and bundled\nwith webpack\", all pointing to the correct location in your original source code.\n\nWith remapping, none of your source code transformations need to be aware of the input's sourcemap,\nthey only need to generate an output sourcemap. This greatly simplifies building custom\ntransformations (think a find-and-replace).\n\n## Installation\n\n```sh\nnpm install @ampproject/remapping\n```\n\n## Usage\n\n```typescript\nfunction remapping(\n  map: SourceMap | SourceMap[],\n  loader: (file: string, ctx: LoaderContext) => (SourceMap | null | undefined),\n  options?: { excludeContent: boolean, decodedMappings: boolean }\n): SourceMap;\n\n// LoaderContext gives the loader the importing sourcemap, tree depth, the ability to override the\n// \"source\" location (where child sources are resolved relative to, or the location of original\n// source), and the ability to override the \"content\" of an original source for inclusion in the\n// output sourcemap.\ntype LoaderContext = {\n readonly importer: string;\n readonly depth: number;\n source: string;\n content: string | null | undefined;\n}\n```\n\n`remapping` takes the final output sourcemap, and a `loader` function. For every source file pointer\nin the sourcemap, the `loader` will be called with the resolved path. If the path itself represents\na transformed file (it has a sourcmap associated with it), then the `loader` should return that\nsourcemap. If not, the path will be treated as an original, untransformed source code.\n\n```js\n// Babel transformed \"helloworld.js\" into \"transformed.js\"\nconst transformedMap = JSON.stringify({\n  file: 'transformed.js',\n  // 1st column of 2nd line of output file translates into the 1st source\n  // file, line 3, column 2\n  mappings: ';CAEE',\n  sources: ['helloworld.js'],\n  version: 3,\n});\n\n// Uglify minified \"transformed.js\" into \"transformed.min.js\"\nconst minifiedTransformedMap = JSON.stringify({\n  file: 'transformed.min.js',\n  // 0th column of 1st line of output file translates into the 1st source\n  // file, line 2, column 1.\n  mappings: 'AACC',\n  names: [],\n  sources: ['transformed.js'],\n  version: 3,\n});\n\nconst remapped = remapping(\n  minifiedTransformedMap,\n  (file, ctx) => {\n\n    // The \"transformed.js\" file is an transformed file.\n    if (file === 'transformed.js') {\n      // The root importer is empty.\n      console.assert(ctx.importer === '');\n      // The depth in the sourcemap tree we're currently loading.\n      // The root `minifiedTransformedMap` is depth 0, and its source children are depth 1, etc.\n      console.assert(ctx.depth === 1);\n\n      return transformedMap;\n    }\n\n    // Loader will be called to load transformedMap's source file pointers as well.\n    console.assert(file === 'helloworld.js');\n    // `transformed.js`'s sourcemap points into `helloworld.js`.\n    console.assert(ctx.importer === 'transformed.js');\n    // This is a source child of `transformed`, which is a source child of `minifiedTransformedMap`.\n    console.assert(ctx.depth === 2);\n    return null;\n  }\n);\n\nconsole.log(remapped);\n// {\n//   file: 'transpiled.min.js',\n//   mappings: 'AAEE',\n//   sources: ['helloworld.js'],\n//   version: 3,\n// };\n```\n\nIn this example, `loader` will be called twice:\n\n1. `\"transformed.js\"`, the first source file pointer in the `minifiedTransformedMap`. We return the\n   associated sourcemap for it (its a transformed file, after all) so that sourcemap locations can\n   be traced through it into the source files it represents.\n2. `\"helloworld.js\"`, our original, unmodified source code. This file does not have a sourcemap, so\n   we return `null`.\n\nThe `remapped` sourcemap now points from `transformed.min.js` into locations in `helloworld.js`. If\nyou were to read the `mappings`, it says \"0th column of the first line output line points to the 1st\ncolumn of the 2nd line of the file `helloworld.js`\".\n\n### Multiple transformations of a file\n\nAs a convenience, if you have multiple single-source transformations of a file, you may pass an\narray of sourcemap files in the order of most-recent transformation sourcemap first. Note that this\nchanges the `importer` and `depth` of each call to our loader. So our above example could have been\nwritten as:\n\n```js\nconst remapped = remapping(\n  [minifiedTransformedMap, transformedMap],\n  () => null\n);\n\nconsole.log(remapped);\n// {\n//   file: 'transpiled.min.js',\n//   mappings: 'AAEE',\n//   sources: ['helloworld.js'],\n//   version: 3,\n// };\n```\n\n### Advanced control of the loading graph\n\n#### `source`\n\nThe `source` property can overridden to any value to change the location of the current load. Eg,\nfor an original source file, it allows us to change the location to the original source regardless\nof what the sourcemap source entry says. And for transformed files, it allows us to change the\nrelative resolving location for child sources of the loaded sourcemap.\n\n```js\nconst remapped = remapping(\n  minifiedTransformedMap,\n  (file, ctx) => {\n\n    if (file === 'transformed.js') {\n      // We pretend the transformed.js file actually exists in the 'src/' directory. When the nested\n      // source files are loaded, they will now be relative to `src/`.\n      ctx.source = 'src/transformed.js';\n      return transformedMap;\n    }\n\n    console.assert(file === 'src/helloworld.js');\n    // We could futher change the source of this original file, eg, to be inside a nested directory\n    // itself. This will be reflected in the remapped sourcemap.\n    ctx.source = 'src/nested/transformed.js';\n    return null;\n  }\n);\n\nconsole.log(remapped);\n// {\n//   …,\n//   sources: ['src/nested/helloworld.js'],\n// };\n```\n\n\n#### `content`\n\nThe `content` property can be overridden when we encounter an original source file. Eg, this allows\nyou to manually provide the source content of the original file regardless of whether the\n`sourcesContent` field is present in the parent sourcemap. It can also be set to `null` to remove\nthe source content.\n\n```js\nconst remapped = remapping(\n  minifiedTransformedMap,\n  (file, ctx) => {\n\n    if (file === 'transformed.js') {\n      // transformedMap does not include a `sourcesContent` field, so usually the remapped sourcemap\n      // would not include any `sourcesContent` values.\n      return transformedMap;\n    }\n\n    console.assert(file === 'helloworld.js');\n    // We can read the file to provide the source content.\n    ctx.content = fs.readFileSync(file, 'utf8');\n    return null;\n  }\n);\n\nconsole.log(remapped);\n// {\n//   …,\n//   sourcesContent: [\n//     'console.log(\"Hello world!\")',\n//   ],\n// };\n```\n\n### Options\n\n#### excludeContent\n\nBy default, `excludeContent` is `false`. Passing `{ excludeContent: true }` will exclude the\n`sourcesContent` field from the returned sourcemap. This is mainly useful when you want to reduce\nthe size out the sourcemap.\n\n#### decodedMappings\n\nBy default, `decodedMappings` is `false`. Passing `{ decodedMappings: true }` will leave the\n`mappings` field in a [decoded state](https://github.com/rich-harris/sourcemap-codec) instead of\nencoding into a VLQ string.\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}