{"_id": "shell-quote", "_rev": "61-2bb6512c860ff75ecce87c87958fc45c", "name": "shell-quote", "dist-tags": {"latest": "1.8.3"}, "versions": {"0.0.0": {"name": "shell-quote", "version": "0.0.0", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@0.0.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "bebda987f1123d9a357a99be33f6f0acae84e2e1", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-0.0.0.tgz", "integrity": "sha512-+Fjv36A1RYiPAqT9liJfqZq4TVhM/tzb+E6kv+eVGejsfUHNbNybiAMzUFpA2L1MCN0ZHMxW1xJ7r2o3uGKUgA==", "signatures": [{"sig": "MEUCID0w/LZ8sXG7SHzrsRF7hhn8gSU19C5mdsK9leFKdLqGAiEA0J7uO5W5jxLYwF++jXxub7LNKreX1HV0dULILmUvEqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engine": {"node": ">=0.4"}, "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.1.19", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "_nodeVersion": "v0.6.11", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.2.5"}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.1": {"name": "shell-quote", "version": "0.0.1", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@0.0.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "1a41196f3c0333c482323593d6886ecf153dd986", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-0.0.1.tgz", "integrity": "sha512-uEWz7wa9vnCi9w4mvKZMgbHFk3DCKjLQlZcy0tJxUH4NwZjRrPPHXAYIEt2TmJs600Dcgj0Z3fZLZKVPVdGNbQ==", "signatures": [{"sig": "MEQCICw0Kb5pOCGWRRaOllRziE2AWUcd1j6xgkuY66lIEAMfAiBauG/s1h0sWXbB3uOLpWaSn7t/ffHflf4epd7yAKr46g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engine": {"node": ">=0.4"}, "engines": {"node": "*"}, "scripts": {"test": "tap test"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.1.19", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "_nodeVersion": "v0.6.11", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.2.5"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.0": {"name": "shell-quote", "version": "0.1.0", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@0.1.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "f751154dd7f2b2c60b66ab7cf8fee1d34d45b9e2", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-0.1.0.tgz", "integrity": "sha512-5JA8ewX9IUTmfEqQguSsKoal4RHOjRD/BNCDNuvbgZFVKDXa4F8mGf306BFVDamMILty/v0hY+srdZ6MR+wz2g==", "signatures": [{"sig": "MEUCIQDdj+1N4yyJPw+GA/2/mtvY5+1BKDI0vnpU/eLN4R5SAgIgPsznPa2yhhu7JTubaRI7XGuNnxYbMbzKp+FnYttYewA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engine": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "devDependencies": {"tap": "~0.4.0", "tape": "~0.3.3"}}, "0.1.1": {"name": "shell-quote", "version": "0.1.1", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@0.1.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "b392bfa55b2ad8b99efc6565e2edc8a3be96d33a", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-0.1.1.tgz", "integrity": "sha512-E38OCpj0Bnz6Lx9509KaqOjMe93YrNDIz+GtTS98k8qmJObJv+UTDZ/8RKrSqgiwWMEXTY7D0596GheFxa2QZg==", "signatures": [{"sig": "MEUCIQCE/ikf+EEHrKtnC+C2vQsQdiY9npf+0j9wHziaPwlFxwIgJVRJbAp0/zdWuK6+pcxGtt3B/97QyRyks8eURCKC4G8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engine": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "devDependencies": {"tap": "~0.4.0", "tape": "~0.3.3"}}, "1.0.0": {"name": "shell-quote", "version": "1.0.0", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.0.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "01d1fb07e34de47dab2998da567fbb0e9c1fa427", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.0.0.tgz", "integrity": "sha512-kcwt1j9aByHpx4KnfRts0/BhsXyjOqYG6xlgNWyPmp/vBQi/Wpwtyr5fv+tWFliOR1WWSmJCBeH9sHDZYMPSTw==", "signatures": [{"sig": "MEUCIQD2sxftLK1QVvn+DllOCCewub8YqHHAqjpPhjC1b/Y9kAIgdgVVrK1QJ9GD0KCJMBBhlDp+oqwsOdvRo7ss9ybPLEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engine": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "devDependencies": {"tap": "~0.4.0", "tape": "~0.3.3"}}, "1.1.0": {"name": "shell-quote", "version": "1.1.0", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.1.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "ec4e2e2f722b1f0bae1daa8d1bbcfe0ebd0083d3", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.1.0.tgz", "integrity": "sha512-i/vNIbQfsThb2Z9OwJEQnQmnnhJagVkzzVMLtELN4vnrOsoVtCUBYlgyR4DPu66BNr9k17JFTYXEIaWJu82+kw==", "signatures": [{"sig": "MEUCIBZnI4SGbye7SQFcJkBj8RkFz2hVpdhHa6sNjZMDCZWjAiEA5OYjWVVo4W9aDsqmIBSem+GmkOuCqVSfmSreNv3fmrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engine": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "devDependencies": {"tap": "~0.4.0", "tape": "~0.3.3"}}, "1.2.0": {"name": "shell-quote", "version": "1.2.0", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.2.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "7d74dd41377b68fe8345dc89420423537dec9240", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.2.0.tgz", "integrity": "sha512-nm+3MEQifUGQuIIwcnP5isTK6825r+J+KidJZUjR2ojCanD/RVN9aZFtDJu68RgEJs2LL4CFeeWk0G7H7JTKIg==", "signatures": [{"sig": "MEUCIQCYHmABuQ7uiXBKn5rQnuU0QeeJdZhg8QislpjrMnEGqwIgcFay0SICTcaU8kTDKSWDFdkK3syrF4xMe4XwHEYV16E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engine": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "devDependencies": {"tap": "~0.4.0", "tape": "~0.3.3"}}, "1.3.0": {"name": "shell-quote", "version": "1.3.0", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.3.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "12f9cd3255ec2d95c82a69a81d851766a8baa32b", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.3.0.tgz", "integrity": "sha512-/uSjkn+BDymeSY6hkxy3gbdUg08C+7wNB7vZN3j6q9yeeFQ+Vjov2aAPwGmFIDwcAs3yrgDJJAF9U8m66v1+Yw==", "signatures": [{"sig": "MEUCIEL9IcD3dykIp7FrPpATp+DDk8zZ6xbI04ssa6B4BEpLAiEAqp2w384yokC+BvFeHJqf2LLbNgBzX0+YC95OCx78fz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engine": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "devDependencies": {"tap": "~0.4.2", "tape": "~1.0.2"}}, "1.3.1": {"name": "shell-quote", "version": "1.3.1", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.3.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "4adb4f6d9e09c18203b3dbf0cf37ef693388eb28", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.3.1.tgz", "integrity": "sha512-M0gimbOt/YjYH1Rsh2bARcIPw3tg759C4FeUzPMRolRJA5iTItVDxuaO8/bzahXUd/Qt7OFgm7TRsonFFFyA+A==", "signatures": [{"sig": "MEUCIQC+MHF221+r5IME27vHB1zq9ZdFVjmWZBEkhQjN6EoYaAIgIDwLlqjHXJMln9HDmD64z3aJkTluKjEwCzztvf6hIQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engine": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "devDependencies": {"tap": "~0.4.2", "tape": "~1.0.2"}}, "1.3.2": {"name": "shell-quote", "version": "1.3.2", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.3.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "1bd13a2b46cb7982ad0d009405a62674d68d663b", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.3.2.tgz", "integrity": "sha512-+SJuTnNVknl56hrEG/Ab04COk02x9rlLv4SDO6djy2bh2/H/cJX4odE/9SzNem7r1LRTnS+O5Vbt3HQb/78xjQ==", "signatures": [{"sig": "MEQCIDA/UZHh0kj7UwKfDOh5fwls/yy99vFV2uRGb1/8/o1TAiBUlkm6+jQAJKNGXVFxwy38kW1Vrr/Cixbn85S+ltShfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engine": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "devDependencies": {"tap": "~0.4.2", "tape": "~1.0.2"}}, "1.3.3": {"name": "shell-quote", "version": "1.3.3", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.3.3", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "07b8826f427c052511e8b5627639e172596e8e4b", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.3.3.tgz", "integrity": "sha512-lP30EJUi3I8yZhDIJR45fE7dbsU3f7vOSHQHKwa2pdfO8scO3f+fE/kLKoeBYq4NPYb4C0E/lh8a+eQf1JlTBg==", "signatures": [{"sig": "MEUCIGo6vX2U700OqVuMroVQ3MAS6TVcbM1PWRCcxI/lVBBCAiEAv4r7DMJcxUA9QOc7UORP+DclzwLTCnuKFha4ppb6Kz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engine": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "devDependencies": {"tap": "~0.4.2", "tape": "~1.0.2"}}, "1.4.0": {"name": "shell-quote", "version": "1.4.0", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.4.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "0715683372d6a87dad8b098812ef72d24620c1b5", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.4.0.tgz", "integrity": "sha512-WFoXpTdinXkxCUSGe72/RyIQOM7k/8QDbsJEp/SwEQXS4VoVRcK5PFzJTCv0QNe9z8qbQ+yBbmxAF1c7T/AKQA==", "signatures": [{"sig": "MEUCIHQyLcJxmTig0SjWP6c4Cioe8diidVyK5XWc+RWJY4pnAiEAgcE5DQnLWJcemcF8IRJRezlTkBABpP4r9q2TQxu71h0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engine": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.3.7", "description": "quote and parse shell commands", "directories": {"test": "test", "example": "example"}, "devDependencies": {"tap": "~0.4.2", "tape": "~1.0.2"}}, "1.4.1": {"name": "shell-quote", "version": "1.4.1", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.4.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "ae18442b536a08c720239b079d2f228acbedee40", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.4.1.tgz", "integrity": "sha512-FS5LkOFh8q9qeA9HdHlZrBOQT372w6AQpKjgGmBW8U4vrDOj0UfapcbzdDpVs3lO2ZM0+zVLDsxak9iysfQnkQ==", "signatures": [{"sig": "MEUCIQDKWD8k1ONmzK5DutTQgb2gLYUBFSGT9262zv1k0tHV/gIgWMQmhgQTo+C8GLmtowQnYSLI79PFigQS15DA27sART8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/3.5", "firefox/15..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/10..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "quote and parse shell commands", "directories": {}, "dependencies": {"jsonify": "~0.0.0", "array-map": "~0.0.0", "array-filter": "~0.0.0", "array-reduce": "~0.0.0"}, "devDependencies": {"tape": "~2.3.0"}}, "1.4.2": {"name": "shell-quote", "version": "1.4.2", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.4.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "f132a54f2030d69280d370d4974155f85f62f67b", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.4.2.tgz", "integrity": "sha512-Y+LuH3/7bbYiE/XcCHNHF6MLDQ4tcLjSprtGKzJpkD4IOSkMkwRLYwpmVMjtvgURlN0kJuTqWedWg2AaznyRUg==", "signatures": [{"sig": "MEYCIQCjxEOFZ3+rABe7NaQCHvPm+6UNOLkROjwTGCDCjONzNwIhAMJq0omXDscNmJPcaGtJHLBosY6XdZEbJTkdIHiovq6t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f132a54f2030d69280d370d4974155f85f62f67b", "gitHead": "cf747ca3969f4a4a27a7270f1f1cc7665751e8ec", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/3.5", "firefox/15..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/10..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "1.4.15", "description": "quote and parse shell commands", "directories": {}, "dependencies": {"jsonify": "~0.0.0", "array-map": "~0.0.0", "array-filter": "~0.0.0", "array-reduce": "~0.0.0"}, "devDependencies": {"tape": "~2.3.0"}}, "1.4.3": {"name": "shell-quote", "version": "1.4.3", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.4.3", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "952c44e0b1ed9013ef53958179cc643e8777466b", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.4.3.tgz", "integrity": "sha512-0Y4SH6mnoNZNW29pNEC0E1F//X7AmbpOj/j5oTssXFUvg2J9MbKIVH3S5ca8Je1Hr36utXJqlzCbYxEYsPAR4A==", "signatures": [{"sig": "MEYCIQD+Xg1BJVfnIKiH9yxYxKu71ON6/gWATmu052713EvhFAIhAPIkioolsqPTwDjObs+XWp//TxiygctAGquifFq++e7S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "952c44e0b1ed9013ef53958179cc643e8777466b", "gitHead": "88c9b82e446c32bf782461059d2cb8b147e037d8", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/3.5", "firefox/15..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/10..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "http://github.com/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"jsonify": "~0.0.0", "array-map": "~0.0.0", "array-filter": "~0.0.0", "array-reduce": "~0.0.0"}, "devDependencies": {"tape": "~2.3.0"}}, "1.5.0": {"name": "shell-quote", "version": "1.5.0", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.5.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote#readme", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "8c0a7e9f4713716c21e962a6ed5faf830dc77a88", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.5.0.tgz", "integrity": "sha512-pxs4LfhAVvVCcJ8QS1K7TAkEDMF09vaFlEjuj8k0DLROgyteNGuYntxSTGJkrL2xiJ2IaGie2LPtIaHIJG62FA==", "signatures": [{"sig": "MEQCID9yexfSO5+YvlC6QV2Tx2X4OdfK6BDWkn2vxNZQvSAyAiB0tqqigscEAetzLLEge97zoq4ICCl0YgPwK473WuKhcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8c0a7e9f4713716c21e962a6ed5faf830dc77a88", "gitHead": "609735869389bd3fbc4bac9830f34fcea5cb6e46", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/3.5", "firefox/15..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/10..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git+ssh://**************/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"jsonify": "~0.0.0", "array-map": "~0.0.0", "array-filter": "~0.0.0", "array-reduce": "~0.0.0"}, "devDependencies": {"tape": "~2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote-1.5.0.tgz_1458151108159_0.1286316504701972", "host": "packages-13-west.internal.npmjs.com"}}, "1.6.0": {"name": "shell-quote", "version": "1.6.0", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.6.0", "maintainers": [{"name": "karissa", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote#readme", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "c8906761e1730d1ef771c82df5423d595c1bb31d", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.6.0.tgz", "integrity": "sha512-zGua5xdLiU/x9oT+JvrAHlxSi01kkLn1zhIqKlIt7w7BoJ8U0TabBryQeIoY9A/xbKUJ8SR5o73HGUgtKksDzQ==", "signatures": [{"sig": "MEYCIQCYaj4cy7U0GOdVVFzSQD/+I6aEzPa5lgL7WLiS7zPgWQIhAJ5C2l6dChwfvdyWi5Yy2kNBOrOzjCaWz1JWQwy7oSw2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c8906761e1730d1ef771c82df5423d595c1bb31d", "gitHead": "88a5280d9c3f5d43fffa538ae9919d1ae579b56e", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/3.5", "firefox/15..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/10..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git+ssh://**************/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"jsonify": "~0.0.0", "array-map": "~0.0.0", "array-filter": "~0.0.0", "array-reduce": "~0.0.0"}, "devDependencies": {"tape": "~2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote-1.6.0.tgz_1461477204944_0.5755356843583286", "host": "packages-16-east.internal.npmjs.com"}}, "1.6.1": {"name": "shell-quote", "version": "1.6.1", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.6.1", "maintainers": [{"name": "karissa", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote#readme", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "f4781949cce402697127430ea3b3c5476f481767", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.6.1.tgz", "integrity": "sha512-V0iQEZ/uoem3NmD91rD8XiuozJnq9/ZJnbHVXHnWqP1ucAhS3yJ7sLIIzEi57wFFcK3oi3kFUC46uSyWr35mxg==", "signatures": [{"sig": "MEUCIQD2T5bXUTDVjVRD7CfLHM8QiJ4eIAXmTDvW1HGG52e7TgIgUMmPTBLvJQqdZMSa7XgMSLnzlbiZRGJwAGQFQbej61w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f4781949cce402697127430ea3b3c5476f481767", "gitHead": "09935581cd2b300d74a65bda3b1cbeb52779dd16", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/3.5", "firefox/15..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/10..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git+ssh://**************/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"jsonify": "~0.0.0", "array-map": "~0.0.0", "array-filter": "~0.0.0", "array-reduce": "~0.0.0"}, "devDependencies": {"tape": "~2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote-1.6.1.tgz_1466196190331_0.3792361367959529", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.2": {"name": "shell-quote", "version": "1.6.2", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.6.2", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote#readme", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "9a9d1df129ab82a16cb57c6ee4d191c7c87793c9", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.6.2.tgz", "fileCount": 17, "integrity": "sha512-k3v2XatA74VlaEKnu/OCXOzn7omw3BahGd/zEj7rhmdOAtkYW5jDE/5xvXsDd2wgkhcqr6eytrTuzVEa61GYzQ==", "signatures": [{"sig": "MEQCIDX3Ijx11f0xfHvRBtmDOfUpiMcdYFx0PoXC0g/uwCr1AiA/BTnzl1htz81atgkN4CDo1q+eL3EcvzP/Sapa0H0W5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUmOoCRA9TVsSAnZWagAA7OcP/3JKEaf+S7NgKE00W14e\nA/UpLwahCWXWz9C/TzYM1ImSBQUMoQwCcr80PeU2Ozkq8zvciDwR/1A55xA2\nOVbDiervZ4AzeJVQuTNfCcE+slFovX324sosPk+EAQ7za4j1gHF8r/slyGoF\nYrIhXY0FViNK0Zav2kBAiMeEG92uFPVWoJptpXP3ErTendXeI/W5F55uWYkN\n02Ej3uJ2JWDo5QI4hZNwHg4x7/LmrKjS3q1DR7sstcM7HrS4GLDNsClTWGnr\nVeLE9kQdA8AJpM3byTDWkCnNwPKsr3U2T3QORcx+dB3hS9TInWDmLWaEfw6e\nZz3tPC10mcmmUrQfEx2iJyNy4czQDAPRuqB/ctVBxKXth9PG7WpwkdfycJaZ\nCLIFXaVgMganMtn9Oqs6ctS8uxA2TvTxPU5m/YRU/dBr041myFuqdF6YW91K\nEb5AGrEiQKsMksUmjW5qaMreHbNq8moSttSit7zViPUGVdBaZUgB6KLn503x\nHJx+REXah0QSto7Vo752mwdS4y6/lZQ2iFpPWr5SyW88DFMh1WdOfhmRWlT9\npH1F3FUU/DL4/YQXb8m/n6dbZw515/PQRWvyj4r9rwKfd3MrQhKgJvXWDDIV\n0I78b3xSwZnRSUlxFE7PkwxRVD2XcZieHol1u9o6kYI9K4ONCFjMXwpmZIcq\n4AlL\r\n=cMDg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "fd9e3da9fd9b275dca0eefdb8062306e76528a97", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "goto-bus-stop", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/3.5", "firefox/15..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/10..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git+ssh://**************/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "12.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote_1.6.2_1565680552220_0.6823407565229571", "host": "s3://npm-registry-packages"}}, "1.6.3": {"name": "shell-quote", "version": "1.6.3", "keywords": ["shell", "command", "quote", "parse"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.6.3", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote#readme", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "54bd93b5964f6a451fe8dbb8668416d40e3501e4", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.6.3.tgz", "fileCount": 17, "integrity": "sha512-KvITSOPOP542Mv4lS5Cx6/qgya20Hyk+JJUdfRfikzyV6iKPszdz5TrssURXRghmi6Z9y9gATRvxJ69zD7wydQ==", "signatures": [{"sig": "MEQCIG2UnR9NA8zohfVnB2IjesWs/Sut+LWYycZ1t4G4dLHbAiBUwiOCHiB/j9/nllbl2RhwQxb1XXkkOQOlHheNYAZY6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUmm0CRA9TVsSAnZWagAA3qQP/iCTt/DptBA10Glxy9my\n3IqT3vLHDTE+mpE8SNG2OO1vUcGNHQ1Trr8509rUSZBHF70esu0bbZz+cFOF\n+Ftwsq3lW3/B16N+mHwqKbCw5Q4Si0+ULtmsTvnrih0F4xmP39XOAt7SMNf9\nvRgoIdB8NwfZ6sUO/6ZYEMMU2jZkVqAQQ7HiSaDli6/eIgviHgYcfQLbZ5vF\nTM2CuLEgHbrc/VjN9SA2BL2p/p1dRDidrrW7xVnua+fJfe/O8mnCVaaP5s3X\ntrFI5YMLfoRJHYJYEaRxEAK8wV+G55KxuSj4C762Xr6vLiprp0xrJ2uMUvRy\n2Hidp2hRRgy50r+S6rYr4EnREdE+ZVt2MiexONVEIFSsbb5T2tKY79wc3bSB\nCjoM1JwAPJHyPKozibb7svvZ/T6xr8sVlOyjkSjs8X60yxt1H+1HFVND+Wkv\nko3eQB9Cinicoh/Fzg8t2pZFP0TTZUay6vyD5EVAQdz9X6ui8yyFpx6c23aG\nN5mQEAAMJIO8rb+jc3uj/xWx3+h+sSSbIbYcluhjMZG8fGRN8WP1rJZEkhtO\nGTvhvrDDM0fj6BeHetcBu03o0uMATqvRtJ4XPBX+9ecDRZTFZh0mA6Pi8tLU\nAaH4pp1hyWXPtWWtuUZwVCVIfrXLTjV/T4ws4EnY6PRs78TYBww+1sbUWNKc\nPVWx\r\n=jo4q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c90d2c69b74865f7018a18e8c847d1fac4efcd28", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "goto-bus-stop", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/3.5", "firefox/15..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/10..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "repository": {"url": "git+ssh://**************/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "12.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "4"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote_1.6.3_1565682100274_0.23050883986074888", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "shell-quote", "version": "1.7.0", "keywords": ["command", "parse", "quote", "shell"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.7.0", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "42d2e1d6d2438ae15b4812a626668f5708213394", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.7.0.tgz", "fileCount": 17, "integrity": "sha512-+zdNWhJbPD3h5jR1LtZwVH+SpSBEzUhHUCHgAZFDKLGvIMR+PnJdsIx6ox7l1WYDT7s2mtjBiahyFAIT/+1ybw==", "signatures": [{"sig": "MEUCIHR95SqvUTTmWuyHBTWwUsFdsa8n7H72tY122Qi7cQY8AiEAua63Wm+rseqhKm+AehSqjhKNjJWHk0Hh3fCbtNjSlY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUmwvCRA9TVsSAnZWagAAR4kP/AuOIIs9xzwRabxjiprr\noZtqepYEwxYNaGETn9Fi5mV6nKVwVOIIuFNlQJdZLIR7gM0s/d5gAZsc/41H\nmsLNRyjdnrGOfXA/2CyaWyilZb0JrjTQbtx7PlPxqCRhBqRVQAGNrpeO51Cj\nV/uUxBklfDKpVCKhM6ULFCPTNDDX/L4Se8uWqgGDXGCNKltcuEFc5JtvVMpC\ngq4Ect43szPf0QSuh5Eo2imDMZ6PiiVwThPHxJ/17mirU0d7PbCYomf3J4P3\nENC2kGJyzy4/JRkU80ikQYMydnWId33kmZG32vtm0XSmqyEUoZB+3npWYYy3\nVailw0ZSFH8LkWrO1bXIpEypYBgSLdjKU6c+wIQQHHWOpZ6w2s9NsRCEobnd\nnVWXLpz1lnkVUTcinYN/saNkCQ6/iauypuFwI4W2o+mVt3lq2XcKQ9QFgNUe\nWZKSOfZuvyrK/dd14nC37hecRcLzTerSkWSJyRTDNeyxPZ0EUjCWwS6LMSv7\n0ZJMKX/eEDH1NzR+mM5wCB+WXBKonr5SUyQ2bkLNvabptyopDyrU/XZpzB9x\nJ3oQzLJTqpKwhQu6OFnNCBYGfp0NGmb3ZXK//KPWLcXGIFDLfQgMrDE5/tOL\ns6/XRlMcRvNFR1e+v3VSUGjK3aeCEU6kKYs3YWu4G323z+hG9I3a3sLtyhso\nz1vT\r\n=rPzW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "09ed4dfb9fe315b4a416e36e0c39bda87975494e", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "goto-bus-stop", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "12.7.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "4"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote_1.7.0_1565682734654_0.9171863951542019", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "shell-quote", "version": "1.7.1", "keywords": ["command", "parse", "quote", "shell"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.7.1", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "3161d969886fb14f9140c65245a5dd19b6f0b06b", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.7.1.tgz", "fileCount": 17, "integrity": "sha512-2kUqeAGnMAu6YrTPX4E3LfxacH9gKljzVjlkUeSqY0soGwK4KLl7TURXCem712tkhBCeeaFP9QK4dKn88s3Icg==", "signatures": [{"sig": "MEYCIQD9i/XFidmQfnfUC8DMTjTxG5nd6k2ro4G9A2R9ioXM1AIhAJbHLzye22Wl26ROurLdeQF3uwTgxhgqxas5eqkCJf56", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUryKCRA9TVsSAnZWagAAMxwP/iETNctxXkNLnj2hWnJF\nGhqLxFTXcha7gKDSMyuKGTY2Sf9du4ku2SB4Gm5Ubxw9ojb98Fgaab0PWW2t\nS21a05Zr8MWjar18LWcFSUKli/cCYzGNCwj0OJ259W1xDV3Ai2hJnuy+RFj7\nzSPFMeVtEZx1m+nKb8FR1T/VZ8pV/1eQeDMjCSvC2WU4Dv07R8xpi7RSvyVS\nVnoCZxaTcWBemcWqg3iZIWidfDzaxLtCO+6QpS5sErjTvido/EZqpjRPIy8y\nLRNL1536Q4IUdGNKCXggmyZ1JZHr1/R2c/oHOD3NzlT16dwwN5AzcUqoXhVk\nPKZuREChjP2v7OIqEY4wEMXrMPobg6pWU49x9Kzf+Tw7ggn0W7PB4zq+EzqU\ni6edBdZE/I0vpzFbjDhXcyqUM6ixUBcPXN5buS3yeTc6TeUOYdWlXhurNcwz\nLcte1HK4IdT1GpNEkU+8RIibfRdGLOUcZM/fCTJ6G3h7ZSE+RUUz0vyCZox+\nm7sDEfS5N5/xxsGpfF8eKUhbRQ5eFa1iOM6ZL1RU945yqlCsVCWDI7suKt+Z\nyAINWzAEwX1qSkl+ITObsz2a0P8Xu5DHwm114NYu4EjZFNaWNWLX0xz4CMw5\nhmifBXQ64zYcbdt13dfrx9F8FP5ZcJrWncHZ9sqrY0zL5KSY4mj/11jrSkSu\n0YWW\r\n=s6sP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c2950fba942da2d2e17a8fb5a4eaf8f8c72d1ab3", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "goto-bus-stop", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "12.7.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "4"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote_1.7.1_1565703305203_0.4317010914374828", "host": "s3://npm-registry-packages"}}, "1.7.2": {"name": "shell-quote", "version": "1.7.2", "keywords": ["command", "parse", "quote", "shell"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.7.2", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "67a7d02c76c9da24f99d20808fcaded0e0e04be2", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.7.2.tgz", "fileCount": 17, "integrity": "sha512-mRz/m/JVscCrkMyPqHc/bczi3OQHkLTqXHEFu0zDhK/qfv3UcOA4SVmRCLmos4bhjr9ekVQubj/R7waKapmiQg==", "signatures": [{"sig": "MEUCIQDhSVpfzynFXIF5w5H3qgHSk4a7aFhnL3ZtdSmHCDidNQIgJupYY/j/m722wgjpsCnjAFxt4K7gQZRlfJxd8X7Nmgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJda3dhCRA9TVsSAnZWagAAd1oP/1KDx7zAYmIbX+77VOUk\nMfZE9PB6hkXNYCvmevMM+lcZeIvujvOIqdwrWZ/PgIEfw98yLPYNnHwf+gLH\nOFbMIiYvkC+hHNfHBxfqZMYc3W1733jPkHFclm0313C8+RyoWN6ZTkD24j34\nMRWU+oAHhjjVVxSNUfoOhIOy/7avNpAYyegBx21++kGeGj2GovUG2g9k6TcQ\nAaSWC48m08bbJFcrn1gk91ayrY5Cr+P0Gj1DidtpQXZmDc6yuxNRwMkUsx6Q\np/4VSo5HhPIkRXIPaD6cmjgacKSqwRwYudMIC3gVBNVj+UnHjsEkYEhnxKhO\nXb6qZZSJkd/gQooRDzv3tWBd4grC2LS731aJB91Qw1MW/v9/IamvYUZvxicT\nmj8yuJkk46Q5n0dlHk9iJfBJC3cqdD6Mwv6hnwpmpRT6gHukno0CBoBeFYCw\nUvtLz7D+xXNqrg4iLM18u01CM4jF77bqAi2zOAvdqtxXjObvKGJxo2aE0u5m\nscCweLnnKRNCfiD/SYLku7EW8Ya6kMuJSWjiIBeElbDFcSW11NfIUw0wb2XU\nQpgKw0n8AfM1JyF6Qrol3run7ObPf2javh+iLAOIoZltVOCH1qBbCesF4Aiq\nbHofviHIRm9KVyn2PDoBDC1W/agvlnzT3NH5qFf3TfR7qil6e8+gUzNEvKXg\niShx\r\n=cFGz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "89a1993809eb7620ec985c3b6869c9079287c35a", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "goto-bus-stop", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "6.11.2", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "12.9.1", "_hasShrinkwrap": false, "devDependencies": {"tape": "4"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote_1.7.2_1567324000519_0.9972134593231239", "host": "s3://npm-registry-packages"}}, "1.7.3": {"name": "shell-quote", "version": "1.7.3", "keywords": ["command", "parse", "quote", "shell"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.7.3", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-shell-quote", "bugs": {"url": "https://github.com/substack/node-shell-quote/issues"}, "dist": {"shasum": "aa40edac170445b9a431e17bb62c0b881b9c4123", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.7.3.tgz", "fileCount": 18, "integrity": "sha512-Vpfqwm4EnqGdlsBFNmHhxhElJYrdfcxPThu+ryKS5J8L/fhAwLazFZtq+S+TWZ9ANj2piSQLGj6NQg+lKPmxrw==", "signatures": [{"sig": "MEUCIDM2juN5SagPU9HRtE+/jJdGHwKwBeVSi4mK01hBpT5pAiEA0D49UQnq2WhkqPmxt0gQ+Klmu0Ulx7nfXy9W0XNeLSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh267MCRA9TVsSAnZWagAAkOQP/2vMykbHsLlZgt4kE3zc\noXFQT6iqpXZBFAK2JHF0fKfshviasuUHGvKmZMUglkmEdK7HZjOkcEBy8m7r\ngT69LqgnAcPU10cZwiotNDCEKzOcARyDDC8d8m+TZPWh4LvQPyT/eErpEBoM\nsS3txAncMBlQWemKH32lro6CcaI59iwCyUqiouw+OnlgxhWqjxKUsmX8l6vr\ninoxPiu/nIbva0xLAjGtdzwm22i/ZobdeArglKzg6QclhJXWPM0hp4OVHTjK\n6r6GS6cCmSW2tH8+0XRU85ndTGBrB/uJDTZaDnWN8U8eSYO27HlPXd8sw76J\nR/r2ZFZ7iOx4pyig2LwlW97lZk5fSpcQxF031HId7X7Q5oWonBmhExytpDJa\n02XD+Kswmvix9dfT3p0ooGBKumYh1uML8yi5zRpiEBz81QKGql1woK6bDfdP\nHjEy+VTWykZ4vZ9KxNzmLczAJlKFIcjmOusq5w5PeGX3Z3wCi34gE866jvx3\nvZPXDlrB1jAPhc/87pXnDWtFKuE3WqCMsDPkUukxg+m1M0nFcqbR0BqzBycY\nBiyP94d1SwCP8AVgpnSzzeSFIF6DofqHlo8OLh8OZJQHCfhLK82KkYA96RRJ\nnErvzU48M2RYfB9/NwuBhNpNpdO/299df+ytmJj/bv6+LbbHVNegS8FrHzAy\n/cFZ\r\n=ssdx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "6a8a899c62a58a30fb128a7079f02826ed4faee0", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/substack/node-shell-quote.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "14.17.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "4"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote_1.7.3_1634798091139_0.6265974561156846", "host": "s3://npm-registry-packages"}}, "1.7.4": {"name": "shell-quote", "version": "1.7.4", "keywords": ["command", "parse", "quote", "shell"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.7.4", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/shell-quote", "bugs": {"url": "https://github.com/ljharb/shell-quote/issues"}, "dist": {"shasum": "33fe15dee71ab2a81fcbd3a52106c5cfb9fb75d8", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.7.4.tgz", "fileCount": 19, "integrity": "sha512-8o/QEhSSRb1a5i7TFR0iM4G16Z0vYB2OQVs4G3aAFXjn3T6yEx8AZxy1PgDF7I00LZHYA3WxaSYIf5e5sAX8Rw==", "signatures": [{"sig": "MEYCIQCQxZOOHPR+VkJR4vUFS/SaWG4rSjKaA30iTDmCSdAwGQIhAKvSBqeTzRbSN++mfYg3uQpm7j2l5W0/HHKajRXwo5R0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSEJaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqu5BAAgvF3/VYIT14KkLKm8BRGf9vkDKjgKDcazwZhFcK7QWY+kyX+\r\niH9RdmJuNaR+/FLomZBcjsIFfpkfKfpA8wsO6lNha8b8pFmEj6BHRaZ+EKiC\r\neZJR2hRTM0DxcThYZ62n4jN3q4JMtUun0X9ncbmsKMmm3VQGPVikE9Vg0cq6\r\nKTkvvveihUjfl//eZfPoiTV6WUHV6foSuIK13HAuked/sfHthD0E8dzOLTdz\r\n6cQDTj+EL9RgsAWumo945XE4zPpAC8hyr0uFA6gn0wjzz6B759IK4dOvgmr4\r\n1hcCILQ+ttOsxgr/WDX57xod6xKo6h+Bhfka1ZUDg4REMLWC1P2WWXmzyPb0\r\nQAPfJeSQE7U9eF+Q5XbPNr/zqV7BSgmmyQptHxIhVwZA6Kasc0izguiAP/M4\r\nhkTYD6CZyMwHR4pNJa3GMk+4a0e576IK7Z/XgvvgT5cjA2IELCJ3Iy9bcKEO\r\nK+Du4NvrfgIjgLIb/l+EA0shZ1jBix/ff1Df8+nuAjhc4dT1LHuJNvQQmIFV\r\nePuWYGH9rLD4CER8ACu6xvkyvKXOibZYXYisxIqhk5z4ZksSMxbdp9rvtM3Q\r\ne6gKGNQS5VRni1t7c2HSszTkj56jxu4v6ZwEdg26I7VfrfjLxkQt/5R6f5PS\r\ny7dYhrTLjRQ8amLMnXD5K+CH7r2F6BV8xYM=\r\n=Y3Qh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "5409e72ef6c905c4d1637883cd394f6f07abd934", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/ljharb/shell-quote.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "18.10.0", "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false, "startingVersion": "1.7.4"}, "devDependencies": {"aud": "^2.0.1", "tape": "^5.6.1", "eslint": "=8.8.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote_1.7.4_1665679962695_0.9983633338915667", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "shell-quote", "version": "1.8.0", "keywords": ["command", "parse", "quote", "shell"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.8.0", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/shell-quote", "bugs": {"url": "https://github.com/ljharb/shell-quote/issues"}, "dist": {"shasum": "20d078d0eaf71d54f43bd2ba14a1b5b9bfa5c8ba", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.0.tgz", "fileCount": 22, "integrity": "sha512-QHsz8GgQIGKlRi24yFc6a6lN69Idnx634w49ay6+jA5yFh7a1UY+4Rp6HPx/L/1zcEDPEij8cIsiqR6bQsE5VQ==", "signatures": [{"sig": "MEUCIEggjd0EYKBKMxT6A5bHSbkm0e/REH61jysFkNQGUczFAiEAvkd7qFS9ay3Enrz2q5fzPqOzAJmKAIas05hRltS13kc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2IqJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofMA/7BgkNB65l6P53oy5kbrbMQwEfGiGv+Y+Tqmje9Zi3iZwWVBj0\r\nad/nL3XNwY5H7qjfUe9+cQPm9dSMIUdh25DdZx26YqNl4c/ja6MQ8izE5axQ\r\n6iL+yOx80rXy6I36LFCzDjUlOAeMpR2e/u76rgtppWPurswWuIoVF+slwoKK\r\nSQd8nDHP3g5Y5pqn3I6Eoy3mbHzXaWGQ7/j3T1FEd/pfOVaJPiguwTUdjCEd\r\nNZF6EpmfZaZFGRkOPcLnd3THYaS9cYGzUIBxE2MzPExsNq9fr9X6dplihfHD\r\n5Yu5t0pP/NCr3ciQskO3WNhmqOVGT7BG0zRZXqECWrYkxvFUtNwZkMWwCLy6\r\nlrZoe9rnHrk1YdBjTVgKr+Td5WdY9fMeiFlGnWrLomKumJb6kExq66JG3O/g\r\nGuJSVB+qKojFVu5qihTTZDN80gM6pu2zW300DWoWZSM0rkBmT4aJQjkWhlrp\r\nzQ+HyfuE+IsrHGyBDPV+jRbFmBnkvChv7g2Wxmi272WukCUmOOCun0bWEtCv\r\n+UJ0gse3Qteub23PIQBIbM1akCBOV4wuIm6N1MGrc1nQNsdafzRhXxU9W4aB\r\n+YS5gt5Z8Vm5+csh3TgoWxAL5+ca5OuXr8AQmffnq7eZVJMZ/tgRD/mc+SIO\r\nzG1LsbZ86OB9j0bF40Nj6ygtbmCQyChWfDk=\r\n=FxQt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "508e2f9c6439706cc696407d52fec36f78248a19", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/ljharb/shell-quote.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "19.5.0", "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false, "startingVersion": "1.7.4"}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.3", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote_1.8.0_1675135625370_0.****************", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "shell-quote", "version": "1.8.1", "keywords": ["command", "parse", "quote", "shell"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.8.1", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/shell-quote", "bugs": {"url": "https://github.com/ljharb/shell-quote/issues"}, "dist": {"shasum": "6dbf4db75515ad5bac63b4f1894c3a154c766680", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.1.tgz", "fileCount": 22, "integrity": "sha512-6j1W9l1iAs/4xYBI1SYOVZyFcCis9b4KCLQ8fgAGG07QvzaRLVVRQvAy85yNmmZSjYjg4MWh4gNvlPujU/5LpA==", "signatures": [{"sig": "MEQCIDZ+iaR8yC0HoLoIi4gZX5FVQoliMpM6b7GuATD0lUfGAiAYZBLnJukgsQ6KpH9RPhyZ1tXu4f66q5BrfS76PBmJxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMINyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopRQ//UM9tAbMjrRpBZ3WKeI7NfoOPEK7tCLDi12SuG8mX6Mb+ShYD\r\nxqwNnCAZoCzuHTaiBIlTBnLKAeI9JZl8RcLq2fUSAPLcLWw8eCYwRtUYwov4\r\nk/AoiDFl5TAEKkikNhcEBWxweJkl8trDAmrZQvLVqzHT+BcwMBYnivIOLPEO\r\nTvmJFzmLvS5RL+r1a+TWSj8F1VP7VB6Zolh950uwZgYtemuNIMs4QMpS3O48\r\ngiFfsJblMxRQrxYT3Z9ao535fSmIWS59FEdsesMGnZWN6Rc/xaLUUw9zUibh\r\nNOcwsYkHsGy1pUiruD7+HIVdUc7581RYP1Qz8csonORaXCOOgJuw999DyGgk\r\nTuscxKqSAnpMbKDGpXleiWM2DFPbkKCbCZwAkEOIzuCUnWWFQIk2lxA7fm4q\r\n5SEI8f7EjVlVfiTjY4wOWVsqq9RVYSkJ7qCSn1GHfyJidfqQeSk9bP0SRCWe\r\nCJnWco96oFdQjqcv4jmhlQIHKlb4Vc153LjwPh5Znm1W0U9MaFAJrT8bAPs6\r\n/HyN2OyiC7qpDabzAmRbCISDf5T/sX0pL+hvFv9rc+0OfBTCx/d+T88LxTvT\r\nWtx81q0AffQn0kuO6HeYIJBkAEJ972+McIbhqoj/x63+sZpeUoWA5oSX1BTE\r\nOMcD45juImeUyNH4n4/X/wTqP2BdIjdmNpM=\r\n=mJdw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "da8a3abb4b2754dd9c1d3203142133e253c515a4", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/ljharb/shell-quote.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "19.8.1", "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false, "startingVersion": "1.7.4"}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.3", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote_1.8.1_1680900978645_0.6957885008213984", "host": "s3://npm-registry-packages"}}, "1.8.2": {"name": "shell-quote", "version": "1.8.2", "keywords": ["command", "parse", "quote", "shell"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "shell-quote@1.8.2", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/shell-quote", "bugs": {"url": "https://github.com/ljharb/shell-quote/issues"}, "dist": {"shasum": "d2d83e057959d53ec261311e9e9b8f51dcb2934a", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.2.tgz", "fileCount": 17, "integrity": "sha512-AzqKpGKjrj7EM6rKVQEPpB288oCfnrEIuyoT9cyF4nmGa7V8Zk6f7RRqYisX8X9m+Q7bd632aZW4ky7EhbQztA==", "signatures": [{"sig": "MEUCIDIsqVpKTipvRLTIsLts+/3fj4ea0gdx4I+vW97QnYdRAiEA/Q6bf9Be3o+tvPXaDWrMA/AtIg12KcSra/jpb1YlgQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23539}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "b19fc77e66871eee10a9978d54c27d802a1da99b", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/ljharb/shell-quote.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "quote and parse shell commands", "directories": {}, "_nodeVersion": "23.3.0", "publishConfig": {"ignore": [".github/workflows", "example", "CHANGELOG.md"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false, "startingVersion": "1.7.4"}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "jackspeak": "=2.1.1", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "auto-changelog": "^2.5.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/shell-quote_1.8.2_1732743217523_0.24614002980435967", "host": "s3://npm-registry-packages"}}, "1.8.3": {"name": "shell-quote", "description": "quote and parse shell commands", "version": "1.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "bugs": {"url": "https://github.com/ljharb/shell-quote/issues"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "homepage": "https://github.com/ljharb/shell-quote", "keywords": ["command", "parse", "quote", "shell"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git+ssh://**************/ljharb/shell-quote.git"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "1.7.4"}, "publishConfig": {"ignore": [".github/workflows", "example", "CHANGELOG.md"]}, "engines": {"node": ">= 0.4"}, "_id": "shell-quote@1.8.3", "gitHead": "487a9b41a7b6154d2a9c10bdffe65cf74d2c3ded", "_nodeVersion": "24.1.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-ObmnIF4hXNg1BqhnHmgbDETF8dLPCggZWBjkQfhZpbszZnYur5DUljTcCHii5LC3J5E0yeO/1LIMyH+UvHQgyw==", "shasum": "55e40ef33cf5c689902353a3d8cd1a6725f08b4b", "tarball": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.3.tgz", "fileCount": 18, "unpackedSize": 23744, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQClHFGvCxl0n+k8OsfO88hHyoO4w/p20GIAv/FgmgXwPAIgWUqVG8OHgpI6Stzp7dAcfoaqySGgFjJh3sacPULPtV8="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/shell-quote_1.8.3_1748840585688_0.8379361015771527"}, "_hasShrinkwrap": false}}, "time": {"created": "2012-05-18T10:42:05.440Z", "modified": "2025-06-02T05:03:06.059Z", "0.0.0": "2012-05-18T10:42:06.889Z", "0.0.1": "2012-05-18T18:25:42.901Z", "0.1.0": "2013-04-15T04:36:33.462Z", "0.1.1": "2013-04-17T08:06:42.055Z", "1.0.0": "2013-05-13T10:27:10.851Z", "1.1.0": "2013-05-13T10:35:28.131Z", "1.2.0": "2013-05-13T12:10:00.722Z", "1.3.0": "2013-05-13T13:42:12.633Z", "1.3.1": "2013-05-13T13:48:27.591Z", "1.3.2": "2013-06-24T11:50:12.621Z", "1.3.3": "2013-06-24T12:01:40.000Z", "1.4.0": "2013-10-18T01:40:45.325Z", "1.4.1": "2013-12-25T01:00:11.788Z", "1.4.2": "2014-07-20T21:27:22.803Z", "1.4.3": "2015-03-08T03:47:14.368Z", "1.5.0": "2016-03-16T17:58:28.755Z", "1.6.0": "2016-04-24T05:53:28.112Z", "1.6.1": "2016-06-17T20:43:12.824Z", "1.6.2": "2019-08-13T07:15:52.345Z", "1.6.3": "2019-08-13T07:41:40.441Z", "1.7.0": "2019-08-13T07:52:14.822Z", "1.7.1": "2019-08-13T13:35:05.409Z", "1.7.2": "2019-09-01T07:46:40.669Z", "1.7.3": "2021-10-21T06:34:51.309Z", "1.7.4": "2022-10-13T16:52:42.935Z", "1.8.0": "2023-01-31T03:27:05.572Z", "1.8.1": "2023-04-07T20:56:18.817Z", "1.8.2": "2024-11-27T21:33:37.690Z", "1.8.3": "2025-06-02T05:03:05.889Z"}, "bugs": {"url": "https://github.com/ljharb/shell-quote/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "homepage": "https://github.com/ljharb/shell-quote", "keywords": ["command", "parse", "quote", "shell"], "repository": {"type": "git", "url": "git+ssh://**************/ljharb/shell-quote.git"}, "description": "quote and parse shell commands", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "karissa", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# shell-quote <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nParse and quote shell commands.\n\n# example\n\n## quote\n\n``` js\nvar quote = require('shell-quote/quote');\nvar s = quote([ 'a', 'b c d', '$f', '\"g\"' ]);\nconsole.log(s);\n```\n\noutput\n\n```\na 'b c d' \\$f '\"g\"'\n```\n\n## parse\n\n``` js\nvar parse = require('shell-quote/parse');\nvar xs = parse('a \"b c\" \\\\$def \\'it\\\\\\'s great\\'');\nconsole.dir(xs);\n```\n\noutput\n\n```\n[ 'a', 'b c', '\\\\$def', 'it\\'s great' ]\n```\n\n## parse with an environment variable\n\n``` js\nvar parse = require('shell-quote/parse');\nvar xs = parse('beep --boop=\"$PWD\"', { PWD: '/home/<USER>' });\nconsole.dir(xs);\n```\n\noutput\n\n```\n[ 'beep', '--boop=/home/<USER>' ]\n```\n\n## parse with custom escape character\n\n``` js\nvar parse = require('shell-quote/parse');\nvar xs = parse('beep ^--boop=\"$PWD\"', { PWD: '/home/<USER>' }, { escape: '^' });\nconsole.dir(xs);\n```\n\noutput\n\n```\n[ 'beep --boop=/home/<USER>' ]\n```\n\n## parsing shell operators\n\n``` js\nvar parse = require('shell-quote/parse');\nvar xs = parse('beep || boop > /byte');\nconsole.dir(xs);\n```\n\noutput:\n\n```\n[ 'beep', { op: '||' }, 'boop', { op: '>' }, '/byte' ]\n```\n\n## parsing shell comment\n\n``` js\nvar parse = require('shell-quote/parse');\nvar xs = parse('beep > boop # > kaboom');\nconsole.dir(xs);\n```\n\noutput:\n\n```\n[ 'beep', { op: '>' }, 'boop', { comment: '> kaboom' } ]\n```\n\n# methods\n\n``` js\nvar quote = require('shell-quote/quote');\nvar parse = require('shell-quote/parse');\n```\n\n## quote(args)\n\nReturn a quoted string for the array `args` suitable for using in shell\ncommands.\n\n## parse(cmd, env={})\n\nReturn an array of arguments from the quoted string `cmd`.\n\nInterpolate embedded bash-style `$VARNAME` and `${VARNAME}` variables with\nthe `env` object which like bash will replace undefined variables with `\"\"`.\n\n`env` is usually an object but it can also be a function to perform lookups.\nWhen `env(key)` returns a string, its result will be output just like `env[key]`\nwould. When `env(key)` returns an object, it will be inserted into the result\narray like the operator objects.\n\nWhen a bash operator is encountered, the element in the array with be an object\nwith an `\"op\"` key set to the operator string. For example:\n\n```\n'beep || boop > /byte'\n```\n\nparses as:\n\n```\n[ 'beep', { op: '||' }, 'boop', { op: '>' }, '/byte' ]\n```\n\n# install\n\nWith [npm](http://npmjs.org) do:\n\n```\nnpm install shell-quote\n```\n\n# license\n\nMIT\n\n[package-url]: https://npmjs.org/package/shell-quote\n[npm-version-svg]: https://versionbadg.es/ljharb/shell-quote.svg\n[deps-svg]: https://david-dm.org/ljharb/shell-quote.svg\n[deps-url]: https://david-dm.org/ljharb/shell-quote\n[dev-deps-svg]: https://david-dm.org/ljharb/shell-quote/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/shell-quote#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/shell-quote.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/shell-quote.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/shell-quote.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=shell-quote\n[codecov-image]: https://codecov.io/gh/ljharb/shell-quote/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/shell-quote/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/shell-quote\n[actions-url]: https://github.com/ljharb/shell-quote/actions\n", "readmeFilename": "README.md", "users": {"dantman": true, "ralucas": true, "chirayuk": true, "magomogo": true, "mikedamage": true, "simplyianm": true, "flumpus-dev": true}}