{"_id": "@rollup/rollup-freebsd-x64", "_rev": "60-65ade9e64ded8fd204d808f120c9b6e2", "name": "@rollup/rollup-freebsd-x64", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.2"}, "versions": {"4.24.1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "7b2a93b961ff8b0faa17cbddb34372d2fabaa164", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-IfG1khuwe10V2EBfFIrcd7P6X0stdhHQM71NyaG5TPgy6dXr2nzAa5TMNFA35tr41gihUPqp/w8StayYG7jXYw==", "signatures": [{"sig": "MEYCIQCX87pKJxJCQzeri4sVUk6QFDDShLKmPeZbgBIZrSKf5wIhAKCMTJWjWE17DWb0YeF3POsv6yc5g1SleNg6T98YVBJh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2468699}, "main": "./rollup.freebsd-x64.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.24.1_1730011415577_0.8138678242354169", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-freebsd-x64", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c72d37315d36b6e0763b7aabb6ae53c361b45e05", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-3qAqTewYrCdnOD9Gl9yvPoAoFAVmPJsBvleabvx4bnu1Kt6DrB2OALeRVag7BdWGWLhP1yooeMLEi6r2nYSOjg==", "signatures": [{"sig": "MEQCIHc7kas0f6zjaUDigbCTzE5vN3N8N/O6jDbVYTxU9FwEAiBkUHIC/779ws4XPNKoyMfmGFbQS8+1vjxkCVdJNoKMig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2468699}, "main": "./rollup.freebsd-x64.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.24.2_1730043642740_0.5296823388966307", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "0db5e9843510c394cfa29c4b5b3dd6e94cbafce1", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-ItT/F+tdl1ZIoxqon4WGSYgk95C0cJjcJiNd3cHQaJOzKKymLRd4iG2KBJGGOcirHT4fX6ysn7JxxzWNsbApmw==", "signatures": [{"sig": "MEQCIGPc7bfniodsXjP72jW2Q0bi3qrV1Qy96W7LvecpN2kKAiAsYwUCDc0SIuNTobqIjaId4s4fB4hlmpZ3f3khFpI6og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2468701}, "main": "./rollup.freebsd-x64.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.25.0-0_1730182544755_0.9098217946169316", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-freebsd-x64", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "67e75fd87a903090f038b212273c492e5ca6b32f", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-F0nqiLThcfKvRQhZEzMIXOQG4EeX61im61VYL1jo4eBxv4aZRmpin6crnBJQ/nWnCsjH5F6J3W6Stdm0mBNqBg==", "signatures": [{"sig": "MEUCIQCCI7gDIuPQLds7mXQcz7eCYJ38m8Vvb6zH9GC5mZSWlgIgJx8tquwWXCVMupmk1WHCnVDZzpgpMvf891snlpYmEw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2468699}, "main": "./rollup.freebsd-x64.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.24.3_1730211283478_0.9138786477148182", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-freebsd-x64", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "3bc53aa29d5a34c28ba8e00def76aa612368458e", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-L7VVVW9FCnTTp4i7KrmHeDsDvjB4++KOBENYtNYAiYl96jeBThFfhP6HVxL74v4SiZEVDH/1ILscR5U9S4ms4g==", "signatures": [{"sig": "MEUCIQCFanR1ovJQ6szgsWF+0Y7mKThGYWNSn5HHPh3drq6gwwIgVUdRd+zdWyPiH2RG0eYHKbvywzg6S5UyCD40UU01zro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2470043}, "main": "./rollup.freebsd-x64.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.24.4_1730710061417_0.9796126314512146", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "f3a1ef941f8d3c6b2b036484c69a7b2d3d9ebbd7", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-sC5FsmZGlJv5dOcURrsnIK7ngc3Kirnx3as2XU9uER+zjfyqIjdcMVgzy4cOawhsssqzoAX19qmxgJ8a14Qrqw==", "signatures": [{"sig": "MEUCIQCZ36hw5tzELeKgklXm0JUeAUtJydMvR25bNRHFvuw1OgIgVaQAgbtYxxvt1Evgi5b9ApmOBn48N8sF6XfAN9tce70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457947}, "main": "./rollup.freebsd-x64.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.25.0_1731141476338_0.8134367710285357", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "51ca2e6d9ce72e63d5201607651732e5300a6f81", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-A/jvfCZ55EYPsqeaAt/yDAG4q5tt1ZboWMHEvKAH9Zl92DWvMIbnZe/f/eOXze65aJaaKbL+YeM0Hz4kLQvdwg==", "signatures": [{"sig": "MEYCIQCOrFOKgAEpNHorDXKBZejQpveVyq++VXsNtwf5FBt9FwIhALgGyJ0HyvknQEyUXUU+jPlioZBYd0R1T8/SLDYxeFnT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457947}, "main": "./rollup.freebsd-x64.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.26.0_1731480332431_0.5760873826432045", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a4a1570c56873cbc1fb8116b54600984c08f7c8d", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-wvL49GbXLSNcrZp2hr7n+j4VPmNkF945Qhm06z+1+GwoTVT4umCTCUj+r8S46pR8nu8csfebHNuhi/OEMjbOuw==", "signatures": [{"sig": "MEUCIFVXtSP+dbylWrgxEY3aoMExBJNb4AEa8F9pHj2BEdDtAiEA82wwp53zWpFGXAI5xMifKnhC3inENV6ae6br2ENkTnI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457949}, "main": "./rollup.freebsd-x64.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.27.0-0_1731481426978_0.9362139505124614", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "572c30f554470d9f4d20ae8372cd64f00272f4e4", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-TQDwqpG5kLp3BQr7TuaRwnBnbiD4d5AUAqdXFwzAgLrLCuoNKhIFy6V4B1CgGFhjnjedFjpEU99yH+ZL4H6tPg==", "signatures": [{"sig": "MEYCIQCnHe/Sy2/otxfq5e195sytKUmSb5wI/NmTrfsbkyxfHwIhAJ89kpxYfy1hWg67xo2sZqNqWO8LJSqpj448Da6/0IHY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457949}, "main": "./rollup.freebsd-x64.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.27.0-1_1731566023979_0.8314516662707123", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "f7d16182de6467c63eef1f855c7d095b722309fa", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-G05JNYFdjikD/2hJTf1gHdD5KjI2TotjiDn17amHtB5JgwrRF1EA9hJ3TRGIvT3zGXilNWWlR71R/2TT1pXRDg==", "signatures": [{"sig": "MEUCIQCaV8LfN7AHIIGzTegbSTlsUdldZ1s227l/SRaOI5CgaQIgD2HT1oULUgIMRV15UKiRY69mUCGcwVx21wIkTp97Mt8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469851}, "main": "./rollup.freebsd-x64.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.27.0_1731667268139_0.9906969131682837", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c5db643e9b46f228b7922d5dedf27ceead0ead62", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-3OzYkGsPNfTyFqH+7ktrGZdYeFKiSJzHAG14AoGhAUp7a59gtV3+nMN5Vwlg+USMZJL5jo3DtOkMcK5+ku41dg==", "signatures": [{"sig": "MEUCIDLGi/4DnE0s9AX/IFC/yZ7P8G9efsrMUsHYYh2RtMtYAiEA9tcWBC2Nw0QOJZnS9GjJ84SmrivJHByLARrYFUpKy3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469853}, "main": "./rollup.freebsd-x64.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.27.1-0_1731677323307_0.08593301098367556", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "9da4da0ff4af763c497227b7c674db84d86bbe75", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-irJ+AUHBfjNk7B1XBnQYqGFNi5xayX3NIgdwoiM6/o+pc5ACyeK9Y0o6M1HV5J3oEzRYer9MLm1dDJwdQR8coA==", "signatures": [{"sig": "MEUCIB5t8XdpdDEZNNUKbTU6DffVfBWSU5RkEk2BRol8dxItAiEArIGUsDjqnAWuuHGHt5hC9HrBuHChlo2CueCulAKkaec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469853}, "main": "./rollup.freebsd-x64.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.27.1-1_1731685118083_0.28701589655088755", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "8e2224e27271aa0092f3488412ae7ded4a2005ce", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-9tj94xM3QCXb/tJqrJj0UQWjYcb7c+VQM4YZDctRFfgVAc/edfmZUc2f/lvoZMmenllcN+D44bMxC8nIrEq6pw==", "signatures": [{"sig": "MEQCIBV/RyPv54+8O9ZC04lafvoaboqp0y8BG7t4mocNg/oOAiBnN882LB/gNd4A429u+etBoe9kkNN/jaOd5hWAB3EBeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469851}, "main": "./rollup.freebsd-x64.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.27.1_1731686896135_0.1463437550415756", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-freebsd-x64", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "2886b9ef04bca8e8334eaf378717eff87bf3be13", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-duq21FoXwQtuws+V9H6UZ+eCBc7fxSpMK1GQINKn3fAyd9DFYKPJNcUhdIKOrMFjLEJgQskoMoiuizMt+dl20g==", "signatures": [{"sig": "MEQCIEbTa2tYaCoeUMNFRgng0K671ZIrDYWOARi4fiISePWZAiBhYpRsfd0fWaplR4T/HEnOHRcvghPGu1RaByC23FlRTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469851}, "main": "./rollup.freebsd-x64.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.27.2_1731691239810_0.3991155971524991", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-freebsd-x64", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c7cd9f69aa43847b37d819f12c2ad6337ec245fa", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-78fohrpcVwTLxg1ZzBMlwEimoAJmY6B+5TsyAZ3Vok7YabRBUvjYTsRXPTjGEvv/mfgVBepbW28OlMEz4w8wGA==", "signatures": [{"sig": "MEQCIEAFuCQYH2mdIGBWnTbo6drkZfta7Qp0PYegDInA1244AiAAlxCam9T8Ix380xdSJgbNzmSfYWgZC+EbcT2uK5rcXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469851}, "main": "./rollup.freebsd-x64.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.27.3_1731948010693_0.9001001939354125", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-freebsd-x64", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "71e5a7bcfcbe51d8b65d158675acec1307edea79", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-wYcC5ycW2zvqtDYrE7deary2P2UFmSh85PUpAx+dwTCO9uw3sgzD6Gv9n5X4vLaQKsrfTSZZ7Z7uynQozPVvWA==", "signatures": [{"sig": "MEUCIQDWnRZOooo0hjdfVXjWTJmtczW3BAjskk4AMqr20Cp1CgIgXv1CluwdAwcvE9ayNB7TACt1pFRe/QmKZzFwt7o1L+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2464795}, "main": "./rollup.freebsd-x64.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.27.4_1732345252188_0.38676151616179144", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "b62a3a8365b363b3fdfa6da11a9188b6ab4dca7c", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-aI2plavbUDjCQB/sRbeUZWX9qp12GfYkYSJOrdYTL/C5D53bsE2/nBPuoiJKoWp5SN78v2Vr8ZPnB+/VbQ2pFA==", "signatures": [{"sig": "MEUCIQCJ6yVTmqQuyWoutFpSf9dIteYxl7mornrn9o6z0R9mnQIgICeK13mk+KY8U1pMAy5+EFJoxktkDQSsGyVnCCcywD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2461083}, "main": "./rollup.freebsd-x64.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.28.0_1732972580327_0.9899392558945985", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "57846f382fddbb508412ae07855b8a04c8f56282", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-m/uYasxkUevcFTeRSM9TeLyPe2QDuqtjkeoTpP9SW0XxUWfcYrGDMkO/m2tTw+4NMAF9P2fU3Mw4ahNvo7QmsQ==", "signatures": [{"sig": "MEUCIDDA7AEQ6CigFkXS5xdVpnwwRw050M4cBvF1jDvM+TulAiEAmAUiRuaBKTFlO5zGkmN88xc+LxzP0JilAUtXN/+lKAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2460827}, "main": "./rollup.freebsd-x64.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.28.1_1733485532400_0.32672376338305", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "4c555fe3b77cdb7f70f061d85fe7b71e73d513d2", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-UJK4nG9qKrngT7fOIzd/Wv/burf6m47yUQnS6vTEu4MtAKtd9JfXfLk3/4QFlFzc0Qphembo4sQl8j4FidmG/Q==", "signatures": [{"sig": "MEUCICd0S+bEC6uSwi1jRMCbJNwoOAdJnJKiP/SrUNvQ5UZ4AiEA5O8p/IpHnACWp4e7XrgkzGQ++LoL0bEA/ElOphlRqS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2455965}, "main": "./rollup.freebsd-x64.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.29.0-0_1734331230704_0.24124368225068338", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a8230d78837851fbc093f414e3fb94f75b18cf0d", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-UhX70w1eoNGiEIDVXOfUFBjbl6QxEua6pbsY9x/k+VCrzeBYLdDvpPDNzIL+MMjH9+G8HKfQL5t0+OteIQGYOQ==", "signatures": [{"sig": "MEYCIQD/8jZVxjxtqNOCWpsN6F3TNKoIstxeVv9PaxX9gulAdgIhAJC5IdjeNPjsYcn3k9LsQIjsMCxJXbJsmhaSYPma1bSA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2455965}, "main": "./rollup.freebsd-x64.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.29.0-1_1734590284568_0.7420573217040176", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-freebsd-x64", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "d033536b98357cb710bc4c950f5052fdf5f4e484", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-+Sf91CA/QX356Wc9LKsj3FTfEGCYvk5KecI1VZUPvEoloah114VXFsGZ9u+oeoziIhoBeCZCKB05//axd0jG0w==", "signatures": [{"sig": "MEYCIQCuiwYQB/+eFX+yltAPcLt5k5IyPbRBYWUDHuOQpcNVBAIhALc1lljQojc2BfJKpSRNqLLLNvgdRPQ0o5Jeh5E9ZS9Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2455965}, "main": "./rollup.freebsd-x64.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.29.0-2_1734677799581_0.39886143540005903", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "4c7085a8e478184c44f3698cd7df072ff3751356", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-Udz9Uh26uEE6phGMG2++TfpsLK/z4cYJqrIOyVhig/PMoWiZLghpjZUQvsAylsoztbpg0/QmplkDAyyVq0x6Jg==", "signatures": [{"sig": "MEUCIQCUuA1mINvO6igRv4xirn/fDQpGnBokqBTTjny+PSU1MgIgG1HvNdnIaqjWuaVrzRXEtcJ7zPe0a925dnP3+gC/a4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457755}, "main": "./rollup.freebsd-x64.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.29.0_1734719880662_0.263664409896629", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "1ef24fa0576bf7899a0a0a649156606dbd7a0d46", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-hEioiEQ9Dec2nIRoeHUP6hr1PSkXzQaCUyqBDQ9I9ik4gCXQZjJMIVzoNLBRGet+hIUb3CISMh9KXuCcWVW/8w==", "signatures": [{"sig": "MEQCIHnoSdRkHOdqetsAIH7bshe90UemI9138jCTpu9GvUg2AiArSy8QO87YcwUZgbKRi4TCxD2PBv9nZoCDYxL8i4dKcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457755}, "main": "./rollup.freebsd-x64.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.29.1_1734765399637_0.32124999482345107", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "f432de4bcb6f16cdb67e0f968992eb6c72bcb093", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-HT5iyz+gXwrgfvGaXHjvVFuXq1G4C+Wax0xTfyHZ87PfsrQf47XfQc/MTgg5Sv2Mdn+BFNjExRoIzHS8iP3lYQ==", "signatures": [{"sig": "MEUCIHWaLQe0xU9N2jxIeUU3/EJnT0EWGlL0kiDKJpEhxewHAiEApGO1IpAdvjZ0LzHSW0Y0tM6ZZdJu9ObmulWW4lJXvOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457757}, "main": "./rollup.freebsd-x64.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.30.0-0_1734765471314_0.4566084022552157", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "dac2233e863ef727115c76aedd5e359896310313", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-PB/m+LfMMTVSZ9dyy8/HOY7uiEqnoW54t7b5tl1IyM714f70hKaP5TKOo3alQQ1y1OktHe1Thll05bWEBhJQfA==", "signatures": [{"sig": "MEUCIFA+ajXlbvrN2iox5eVEZMzGMIy4nivzfDjMWGNzVnsKAiEA/R9Cm1JDcgZGAJoxoITDk5uMLKLOzyDUs+G+rotnUU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2454557}, "main": "./rollup.freebsd-x64.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.30.0-1_1735541574204_0.8445204965097621", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-freebsd-x64", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "d3d79a2b96e81475571cb9bb414910450bcebe04", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-eXKvpThGzREuAbc6qxnArHh8l8W4AyTcL8IfEnmx+bcnmaSGgjyAHbzZvHZI2csJ+e0MYddl7DX0X7g3sAuXDQ==", "signatures": [{"sig": "MEYCIQCKFgm1fnmVnFArefly2mqJtEKSbPypMi3/HtLOy+JbpAIhAP+PuCPWXiulIM33uAMzcaRgOzR/rZkK6gqR/ol/pWCH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2454555}, "main": "./rollup.freebsd-x64.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.29.2_1736078900802_0.20794009165474714", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "138daa08d1b345d605f57b4dedd18a50420488e7", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-ZtY3Y8icbe3Cc+uQicsXG5L+CRGUfLZjW6j2gn5ikpltt3Whqjfo5mkyZ86UiuHF9Q3ZsaQeW7YswlHnN+lAcg==", "signatures": [{"sig": "MEUCIDqKaqM8PtzE01zMkqXrVlvTfoRm8YbhCXZpMCC5R+nFAiEA4s7CCvCuGAJ+euZURC9Ko7AgWjCof2TYV2os6KCtJWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2454555}, "main": "./rollup.freebsd-x64.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.30.0_1736145433337_0.4810647604464675", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "dfba762a023063dc901610722995286df4a48360", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-1MEdGqogQLccphhX5myCJqeGNYTNcmTyaic9S7CG3JhwuIByJ7J05vGbZxsizQthP1xpVx7kd3o31eOogfEirw==", "signatures": [{"sig": "MEQCIFRjqvLuO4OWqowUnohZzVVq24q2QLVMltY7V/XTNaGWAiAbppiy6rMrGMV1et68opsZ9CC5JIrvjFq0ahuJLJXdsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2454555}, "main": "./rollup.freebsd-x64.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.30.1_1736246188819_0.22255745050868958", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "5b4477d51f0b06226ccdfa4f2a972cb39792ea16", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-z187r/kd5cnXAqu+2QbxnNk5OiUMkNlaJX6EGodi68ahy/sUYuYaEMNPBsogvJJnJ0FSfNPgvEwjKe9yGs3m6g==", "signatures": [{"sig": "MEUCIQChMsBDQ1bQbUq1MRQ5eBQ6zn10M7WSBtdBJYcon9iolAIgI+p/fB6bw+xa/H+NJK/TJJeehkHm8MFrw3gxkkY9ydo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2487437}, "main": "./rollup.freebsd-x64.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.31.0-0_1736834297922_0.5383189573013623", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "e59e7ede505be41f0b4311b0b943f8eb44938467", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-pCANqpynRS4Jirn4IKZH4tnm2+2CqCNLKD7gAdEjzdLGbH1iO0zouHz4mxqg0uEMpO030ejJ0aA6e1PJo2xrPA==", "signatures": [{"sig": "MEQCIGYpYf8jp3uBN01ykNRHZZBOf5A5DSTb6+hS1xYWCbawAiAUwOdsqWDr8DtzqblKhoA3nAjAyJk6sXjq0VHBDqIRGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2513675}, "main": "./rollup.freebsd-x64.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.31.0_1737291443938_0.5810804419938556", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "9d4d1dbbafcb0354d52ba6515a43c7511dba8052", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-wegiyBT6rawdpvnD9lmbOpx5Sph+yVZKHbhnSP9MqUEDX08G4UzMU+D87jrazGE7lRSyTRs6NEYHtzfkJ3FjjQ==", "signatures": [{"sig": "MEQCIBSOHTF9LjHEiaYBlMtny/IquDzTqx1xC+KPadDtm4K0AiBl91GZI5KJCqacKsxjrwrc+AfybQZafvjqnPVTrC/IVA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2508811}, "main": "./rollup.freebsd-x64.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.32.0_1737707290494_0.7445795994781375", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "829f4004f3f8cfa5eed4dcf5fb746b70f7af2dfc", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-jf+VAL1J30M8qQ9DBheL0BQhs9juc/vUOU1L2rSQH/rXfIPTgzL1tlKQbnH1xkvttQHuKV0kylPP52Sufskk6Q==", "signatures": [{"sig": "MEYCIQCMXfmNJw1vN/ZZX/bbK7nPASgWU4sT6rE2MwDj2LMXmgIhAOmu4LsFAJmMrZxhXL6Dqx0yauD+KfsNbFO4J+IbxelE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2508813}, "main": "./rollup.freebsd-x64.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.33.0-0_1738053044366_0.45448871520779854", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "35867b15c276f4b4ca8eb226f7dd6df8c64640db", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-JRBRmwvHPXR881j2xjry8HZ86wIPK2CcDw0EXchE1UgU0ubWp9nvlT7cZYKc6bkypBt745b4bglf3+xJ7hXWWw==", "signatures": [{"sig": "MEUCIQD4t/pWyu6L6u8KrLUHyheicXujESfT2MonOgOKMz2JWAIgdBiHhLDQC7NzbZaKvSy+H3YL/NpKs8gxA2rBnCVT+iE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2508811}, "main": "./rollup.freebsd-x64.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.32.1_1738053231159_0.04176091007624105", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "35bd344324570cb799168f047a40d3d886a1fb9f", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-v8zFftaPMqOMiobWgEa/2gZ9O1RrL/qnycxU19OiNIq6ayIziAnZ/WeKaqKiXrg7Riy1sFdXkYo3qVwd1DDVEA==", "signatures": [{"sig": "MEUCIQCinbAo4mrJyv17RTxzxoRjBMnYONI1cnrC73qOhmDoLgIgfgC1lUNmtuNb7f68wRrever6lCnuRPPKi6wKfT64T2o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2497915}, "main": "./rollup.freebsd-x64.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.33.0_1738393956945_0.5184775160243422", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "fe48166f889bee1996a1bfcf44e376d524e6e030", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-D0RDyHygOBCQiqookcPevrvgEarN0CttBecG4chOeIYCNtlKHmf5oi5kAVpXV7qs0Xh/WO2RnxeicZPtT50V0g==", "signatures": [{"sig": "MEUCIBWykX45YzXuVLQFxxZ+0AwxM/6ZxhESkyteO3Bk7L0OAiEA0LEXm5u90q4cku2Rbgk3CwlMfdAngCXIcmL95kelq+g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2497915}, "main": "./rollup.freebsd-x64.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.34.0_1738399257683_0.5019056112105644", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "1d0377ef7a06bb3a6543bbbf18b6d65b55074802", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-L3T66wAZiB/ooiPbxz0s6JEX6Sr2+HfgPSK+LMuZkaGZFAFCQAHiP3dbyqovYdNaiUXcl9TlgnIbcsIicAnOZg==", "signatures": [{"sig": "MEUCICYH7PpE1xHNiaF8le+tiSxg2qBzC/URWaOvYCu8KEdeAiEAp9HhpUhkX54+lPwkQHCMIc2WCbHvgJ6BrdClojR70gc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2497915}, "main": "./rollup.freebsd-x64.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.34.1_1738565928143_0.8443191031283344", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-freebsd-x64", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "5a511de732cf2f58e302a79926383960a280c277", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-lhdiwQ+jf8pewYOTG4bag0Qd68Jn1v2gO1i0mTuiD+Qkt5vNfHVK/jrT7uVvycV8ZchlzXp5HDVmhpzjC6mh0g==", "signatures": [{"sig": "MEUCIQCOIr4HJLADzqb+fbbwoJ9W8HYeKKLrcClqlh/+j6f5DQIgKIshYn6QDMu1baXYGSkmXxgEsZFNVHE2A2PMbsUZ1dw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2497915}, "main": "./rollup.freebsd-x64.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.34.2_1738656640342_0.31750992428158886", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-freebsd-x64", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "2312f47788b3e334b14edb7eee748e9d545fd856", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-sNPvBIXpgaYcI6mAeH13GZMXFrrw5mdZVI1M9YQPRG2LpjwL8DSxSIflZoh/B5NEuOi53kxsR/S2GKozK1vDXA==", "signatures": [{"sig": "MEYCIQDrwC8LG1+gQaZ0SGBL1o4gsqNcSWwL842tmk5pXpUArAIhAPU1up2LjvDndxwPJ1ZVW/0rid3n2n9hB2hqpiPIorH7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2497915}, "main": "./rollup.freebsd-x64.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.34.3_1738747362205_0.7115621999505717", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-freebsd-x64", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "4ec412756d87ea1cd6adae36b52796bffbc9625d", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-6Rk3PLRK+b8L/M6m/x6Mfj60LhAUcLJ34oPaxufA+CfqkUrDoUPQYFdRrhqyOvtOKXLJZJwxlOLbQjNYQcRQfw==", "signatures": [{"sig": "MEUCIAwKDR04aLy2jFdPsAkBlssEYXJotAz6OYgumsgnP55hAiEA7B6yeGyrdDiHulE5yqI19BwgOiy3zI0ecJUOYgJpxl0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2497915}, "main": "./rollup.freebsd-x64.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.34.4_1738791107334_0.022529958045791387", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-freebsd-x64", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "0dc91ebdde6973e3a48afe4f44d170b9d0585a5e", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-j6Q8VFqyI8hZM33h1JC6DZK2w8ejkXqEMozTrtIEGfRVMpVZL3GrLOOYEUkAgUSpJ9sb2w+FEpjGj7IHRcQfdw==", "signatures": [{"sig": "MEYCIQDqvZo6+mbdBseeI3nBDZsyq3I8Cr+6ol7nK30l5NQ3twIhAKaTdnex5UPutpfgmgehTSBGzD3nHohQu/gEtxVkF/Ft", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2497851}, "main": "./rollup.freebsd-x64.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.34.5_1738918420799_0.20425251451348747", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-freebsd-x64", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a8b58ab7d31882559d93f2d1b5863d9e4b4b2678", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-HEP4CgPAY1RxXwwL5sPFv6BBM3tVeLnshF03HMhJYCNc6kvSqBgTMmsEjb72RkZBAWIqiPUyF1JpEBv5XT9wKQ==", "signatures": [{"sig": "MEUCIAlj00/C7s5I2zlqfFgR0fbxtme13o5QPi70zvaysq36AiEAzUTR+RAuUZM2I9FPh4X6s7rALdK2INI+uBR+sa92cto=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2481723}, "main": "./rollup.freebsd-x64.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.34.6_1738945963903_0.21258152458544322", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-freebsd-x64", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "f24836a6371cccc4408db74f0fd986dacf098950", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-X++QSLm4NZfZ3VXGVwyHdRf58IBbCu9ammgJxuWZYLX0du6kZvdNqPwrjvDfwmi6wFdvfZ/s6K7ia0E5kI7m8Q==", "signatures": [{"sig": "MEQCIDa88gt9w4Wjm4uhsvWyv6NVUZeZyUMmUWNTn8KcY8ewAiADPfnkz7uRVB2fYd5zMcVxiIMIHfyLld9j3Izp0xHAzg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2487419}, "main": "./rollup.freebsd-x64.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.34.7_1739526874870_0.6945881505823277", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-freebsd-x64", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "39561f3a2f201a4ad6a01425b1ff5928154ecd7c", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-TYXcHghgnCqYFiE3FT5QwXtOZqDj5GmaFNTNt3jNC+vh22dc/ukG2cG+pi75QO4kACohZzidsq7yKTKwq/Jq7Q==", "signatures": [{"sig": "MEYCIQCo5OYm7b/0GMzoQmknG4jq5t7RnhRGgXTSlC4KbjPqXAIhAMD/JLVx7C5brFf3AolVwG+2X8kHP2DlkZ0iwcQ7pKnu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2487419}, "main": "./rollup.freebsd-x64.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.34.8_1739773620507_0.45635861283711243", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-freebsd-x64", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c3576c6011656e4966ded29f051edec636b44564", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-SLl0hi2Ah2H7xQYd6Qaiu01kFPzQ+hqvdYSoOtHYg/zCIFs6t8sV95kaoqjzjFwuYQLtOI0RZre/Ke0nPaQV+g==", "signatures": [{"sig": "MEQCIGqPuq9FgfAGTlQ2CXTeV0YJgQPo//k8ICCOWp00GWYRAiA+9F6uZfGhpuFG+ZgF03nUwOC+oRV3NNxWrjSQBQIOjg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2550203}, "main": "./rollup.freebsd-x64.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.34.9_1740814394476_0.17060551894917597", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "cd932d3ec679711efd65ca25821fb318e25b7ce4", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-2mpHCeRuD1u/2kruUiHSsnjWtHjqVbzhBkNVQ1aVD63CcexKVcQGwJ2g5VphOd84GvxfSvnnlEyBtQCE5hxVVw==", "signatures": [{"sig": "MEYCIQCBEfSZHTjjGGPoNcHGXtol4uO0CCoChFPVIsUSdbApIAIhAMsKSBBYjrw4fF+Wmmex662qhoURzHKeNBd9jMXscmD8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2558203}, "main": "./rollup.freebsd-x64.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.35.0_1741415126611_0.49842517292005706", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "432e49d93942225ac1b4d98254a6fb6ca0afcd17", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-dVeWq1ebbvByI+ndz4IJcD4a09RJgRYmLccwlQ8bPd4olz3Y213uf1iwvc7ZaxNn2ab7bjc08PrtBgMu6nb4pQ==", "signatures": [{"sig": "MEQCIC1Ylq07dAlu4XO5itK0B7B+0POx8fhlIvpbMBLHOA7+AiAa9lUMgpM9ObzVuTTU+t0m7TLBmPww8urDIiGk7t94eQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2563643}, "main": "./rollup.freebsd-x64.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.36.0_1742200583470_0.017140377703268372", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "5e56ffd4a0d7ccfcbc86867c40b8f0e6a2c0c81e", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-SZMxNttjPKvV14Hjck5t70xS3l63sbVwl98g3FlVVx2YIDmfUIy29jQrsw06ewEYQ8lQSuY9mpAPlmgRD2iSsA==", "signatures": [{"sig": "MEUCIBH4PGVtjViSdOSH0H4n+9LI1aJpvQd0sCAv5PajMcSgAiEAxiiLsjqeC3mRW3Jci8HE8RCtnlNx1RVVWB4Ybzc6Rss=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2558203}, "main": "./rollup.freebsd-x64.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.37.0_1742741862371_0.12323987970550476", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "64376ff0e1541cd8677e74898782ec4935277e02", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-hCY/KAeYMCyDpEE4pTETam0XZS4/5GXzlLgpi5f0IaPExw9kuB+PDTOTLuPtM10TlRG0U9OSmXJ+Wq9J39LvAg==", "signatures": [{"sig": "MEYCIQC8Dmk0TFPrRoTG1p94SPF24EtgIJzCogTZpiDzEB43/wIhAIU51VJF0e8lqVn1O5ii5efDOcD+PiAiP5JA8Tt1Nq5k", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2564219}, "main": "./rollup.freebsd-x64.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.38.0_1743229788689_0.48826667552342506", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "4826af30f4d933d82221289068846c9629cc628c", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-8RXIWvYIRK9nO+bhVz8DwLBepcptw633gv/QT4015CpJ0Ht8punmoHU/DuEd3iw9Hr8UwUV+t+VNNuZIWYeY7Q==", "signatures": [{"sig": "MEUCIQCw0P7Jvk+d3LHQP3u6MtOu+Ih+aMJNuYHbD0Z+6ErXKQIgHaOYLhcBnCOrovzSgXlcCjlCveU+fDmZlpTZyNumk+o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2564219}, "main": "./rollup.freebsd-x64.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.39.0_1743569413162_0.18220033374638045", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "96bf6ff875bab5219c3472c95fa6eb992586a93b", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-mVDxzlf0oLzV3oZOr0SMJ0lSDd3xC4CmnWJ8Val8isp9jRGl5Dq//LLDSPFrasS7pSm6m5xAcKaw3sHXhBjoRw==", "signatures": [{"sig": "MEUCIQCNoHhdZE5mPC0s+psa5xmA4x59AMYktS8ofkKnXe7NlQIgNHaxg9eAeA07G5vAPBQoB/el3KmwBB+SnqRK6x7lh6M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2675691}, "main": "./rollup.freebsd-x64.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.40.0_1744447215318_0.3115845198137641", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "f4b1e091f7cf5afc9e3a029d70128ad56409ecfb", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-VXeo/puqvCG8JBPNZXZf5Dqq7BzElNJzHRRw3vjBE27WujdzuOPecDPc/+1DcdcTptNBep3861jNq0mYkT8Z6Q==", "signatures": [{"sig": "MEUCIQC8fD0XRtTg+2MRtNDlGDubGlrfi6Hcsq20rPcYw2eZbQIgE6+V/6D5fkBZowEX3On1TAdUYeRAsxaJQi9mvA0rvxo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2701115}, "main": "./rollup.freebsd-x64.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.40.1_1745814963971_0.38192217709706067", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-freebsd-x64", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "28acadefa76b5c7bede1576e065b51d335c62c62", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-C+AyHBzfpsOEYRFjztcYUFsH4S7UsE9cDtHCtma5BK8+ydOZYgMmWg1d/4KBytQspJCld8ZIujFMAdKG1xyr4Q==", "signatures": [{"sig": "MEUCICc/vNbYSKmJL2FaP/+enHB9rT5k3iHcxtzGs/gnkcHwAiEA7ahq3xprknvYA3QlR1/nMn4JRLQ3HgUOpDiaNSZUa/Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2701563}, "main": "./rollup.freebsd-x64.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.40.2_1746516454758_0.30356985227694544", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "104511dc64612789ddda41d164ab07cdac84a6c1", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-KGiGKGDg8qLRyOWmk6IeiHJzsN/OYxO6nSbT0Vj4MwjS2XQy/5emsmtoqLAabqrohbgLWJ5GV3s/ljdrIr8Qjg==", "signatures": [{"sig": "MEUCIAN5AjlyE9kVOp383kcXFBwTQGcb/0g5/sYVse1xSl9TAiEAk5ftcYAipWpK1qqcXxnU6GGQo8l8h1rF2b+RtJMFqxo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2701051}, "main": "./rollup.freebsd-x64.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.41.0_1747546447524_0.8824417805331684", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a3fdce8a05e95b068cbcb46e4df5185e407d0c35", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-3FkydeohozEskBxNWEIbPfOE0aqQgB6ttTkJ159uWOFn42VLyfAiyD9UK5mhu+ItWzft60DycIN1Xdgiy8o/SA==", "signatures": [{"sig": "MEUCIAJ9gB/zpEqJwYfoJKZ9/jueXRzRxV3dvu+Pwe0n1nY4AiEAlUtcSZAZNDcMBDTOAOe1joVlZ8QdHYbpSLJIVecSYHE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2759963}, "main": "./rollup.freebsd-x64.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.41.1_1748067319515_0.5144486386516676", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-freebsd-x64", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "fe24342f05259f23751370242c6528e36fa462b3", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-ye4rQ80/fAIcUyIil7S/IzGpHvlPUoZvUQSq0OmU+LXb/I9E5Brh7PKJ/K0plsLCQx9Hx01526Uf4Iy8RqmLJw==", "signatures": [{"sig": "MEYCIQDHrwFSkOtdRO39b9ZmirQKRjVSz/+mQFGm12h64iY3ZgIhAI8ZEd0wZgvMOh3wV8BXcSwLoxyHJmp+Xpg1JB8JKrxq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2756123}, "main": "./rollup.freebsd-x64.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.41.2_1749210073502_0.022684679301688382", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "675808bf4fe7c7fc454326510ab3be0857626d41", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-CziHfyzpp8hJpCVE/ZdTizw58gr+m7Y2Xq5VOuCSrZR++th2xWAz4Nqk52MoIIrV3JHtVBhbBsJcAxs6NammOQ==", "signatures": [{"sig": "MEUCIQCYZULtdVwQa5OyWiFnvuITLu80jRaeT0wU5pOuncgRBQIgYDvhk5TWphtdIN7J8oqy/C1zxaJQOHqUr8qYntSSH5s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2756123}, "main": "./rollup.freebsd-x64.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.42.0_1749221333753_0.49681646384026035", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "88297a0ddfadddd61d7d9b73eb42b3f227301d30", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-J7uCsiV13L/VOeHJBo5SjasKiGxJ0g+nQTrBkAsmQBIdil3KhPnSE9GnRon4ejX1XDdsmK/l30IYLiAaQEO0Cg==", "signatures": [{"sig": "MEUCIQCYfP+94OIhg8P/o6V29E6C/ASY7ayqia2sWgaxqq2nQgIgAtzagatJXg4ZwlNt7P5ZITQa3Zt/pbpHJSONKvjubUw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2756123}, "main": "./rollup.freebsd-x64.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.43.0_1749619404557_0.7189533490942617", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-freebsd-x64", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "cb4e1547b572cd0144c5fbd6c4a0edfed5fe6024", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-qC0kS48c/s3EtdArkimctY7h3nHicQeEUdjJzYVJYR3ct3kWSafmn6jkNCA8InbUdge6PVx6keqjk5lVGJf99g==", "signatures": [{"sig": "MEUCIQDFJjWgd6TNVhAH59zu+IK8sHtMac/MjDKaEbTKlFTt4wIgF50C9QzuyHS9EC9qyIlM2BWBz7Ys3DDRxeuxWVbdNfc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2764763}, "main": "./rollup.freebsd-x64.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.44.0_1750314220507_0.21815257948590228", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-freebsd-x64", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-x64@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "cab01f9e06ca756c1fabe87d64825ae016af4713", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-uBmIxoJ4493YATvU2c0upGz87f99e3wop7TJgOA/bXMFd2SvKCI7xkxY/5k50bv7J6dw1SXT4MQBQSLn8Bb/Uw==", "signatures": [{"sig": "MEUCIB61V+VzR/tM2X8AeTeQh+Z5+DiPdolOneVZ+JtYLcdNAiEAxp3QcD607Pm3PUl0uUFSdess7HjA1/jRoMQiGuRhS+I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2764763}, "main": "./rollup.freebsd-x64.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-x64_4.44.1_1750912498837_0.555874259133619", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-freebsd-x64", "version": "4.44.2", "os": ["freebsd"], "cpu": ["x64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.freebsd-x64.node", "_id": "@rollup/rollup-freebsd-x64@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-tdT1PHopokkuBVyHjvYehnIe20fxibxFCEhQP/96MDSOcyjM/shlTkZZLOufV3qO6/FQOSiJTBebhVc12JyPTA==", "shasum": "647d6e333349b1c0fb322c2827ba1a53a0f10301", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2765147, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD+71dmqpcYsoJcOcICKVLx8dpHH43Q7BtLq1buo+sxWgIgQSi+mb0ZnrmtOp5VfYQvTlz9tKroaPCHdvvodcY6lP8="}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-freebsd-x64_4.44.2_1751633809912_0.9924855220514908"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-10-27T06:43:35.480Z", "modified": "2025-07-04T12:56:50.697Z", "4.24.1": "2024-10-27T06:43:35.828Z", "4.24.2": "2024-10-27T15:40:42.996Z", "4.25.0-0": "2024-10-29T06:15:44.956Z", "4.24.3": "2024-10-29T14:14:43.694Z", "4.24.4": "2024-11-04T08:47:41.581Z", "4.25.0": "2024-11-09T08:37:56.609Z", "4.26.0": "2024-11-13T06:45:32.695Z", "4.27.0-0": "2024-11-13T07:03:47.201Z", "4.27.0-1": "2024-11-14T06:33:44.141Z", "4.27.0": "2024-11-15T10:41:08.409Z", "4.27.1-0": "2024-11-15T13:28:43.542Z", "4.27.1-1": "2024-11-15T15:38:38.441Z", "4.27.1": "2024-11-15T16:08:16.423Z", "4.27.2": "2024-11-15T17:20:40.153Z", "4.27.3": "2024-11-18T16:40:10.992Z", "4.27.4": "2024-11-23T07:00:52.455Z", "4.28.0": "2024-11-30T13:16:20.577Z", "4.28.1": "2024-12-06T11:45:32.704Z", "4.29.0-0": "2024-12-16T06:40:30.906Z", "4.29.0-1": "2024-12-19T06:38:04.790Z", "4.29.0-2": "2024-12-20T06:56:39.852Z", "4.29.0": "2024-12-20T18:38:00.961Z", "4.29.1": "2024-12-21T07:16:39.898Z", "4.30.0-0": "2024-12-21T07:17:51.533Z", "4.30.0-1": "2024-12-30T06:52:54.415Z", "4.29.2": "2025-01-05T12:08:21.075Z", "4.30.0": "2025-01-06T06:37:13.538Z", "4.30.1": "2025-01-07T10:36:29.077Z", "4.31.0-0": "2025-01-14T05:58:18.121Z", "4.31.0": "2025-01-19T12:57:24.172Z", "4.32.0": "2025-01-24T08:28:10.700Z", "4.33.0-0": "2025-01-28T08:30:44.599Z", "4.32.1": "2025-01-28T08:33:51.388Z", "4.33.0": "2025-02-01T07:12:37.182Z", "4.34.0": "2025-02-01T08:40:57.930Z", "4.34.1": "2025-02-03T06:58:48.359Z", "4.34.2": "2025-02-04T08:10:40.712Z", "4.34.3": "2025-02-05T09:22:42.427Z", "4.34.4": "2025-02-05T21:31:47.586Z", "4.34.5": "2025-02-07T08:53:41.037Z", "4.34.6": "2025-02-07T16:32:44.166Z", "4.34.7": "2025-02-14T09:54:35.142Z", "4.34.8": "2025-02-17T06:27:00.772Z", "4.34.9": "2025-03-01T07:33:14.742Z", "4.35.0": "2025-03-08T06:25:26.850Z", "4.36.0": "2025-03-17T08:36:23.750Z", "4.37.0": "2025-03-23T14:57:42.589Z", "4.38.0": "2025-03-29T06:29:49.005Z", "4.39.0": "2025-04-02T04:50:13.495Z", "4.40.0": "2025-04-12T08:40:15.525Z", "4.40.1": "2025-04-28T04:36:04.219Z", "4.40.2": "2025-05-06T07:27:35.023Z", "4.41.0": "2025-05-18T05:34:07.748Z", "4.41.1": "2025-05-24T06:15:19.743Z", "4.41.2": "2025-06-06T11:41:13.721Z", "4.42.0": "2025-06-06T14:48:53.992Z", "4.43.0": "2025-06-11T05:23:24.784Z", "4.44.0": "2025-06-19T06:23:40.721Z", "4.44.1": "2025-06-26T04:34:59.083Z", "4.44.2": "2025-07-04T12:56:50.164Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-freebsd-x64`\n\nThis is the **x86_64-unknown-freebsd** binary for `rollup`\n", "readmeFilename": "README.md"}