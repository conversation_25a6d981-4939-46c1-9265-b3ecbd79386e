{"_id": "yaml", "_rev": "155-66d8860e742ac72ef6e3b51f7b6c3ef3", "name": "yaml", "dist-tags": {"latest": "2.8.0"}, "versions": {"0.1.0": {"name": "yaml", "version": "0.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "yaml@0.1.0", "dist": {"shasum": "56bf2e28ebe93efe9df58b3ed3f93ad8e16a10c3", "tarball": "https://registry.npmjs.org/yaml/-/yaml-0.1.0.tgz", "integrity": "sha512-iXPBV1d8WL9Rv3OBQU+48sUth47xI4hjCi6hHvIaqWLeF2G1dA+eWMnDjIF5v1zlqbaF6UKkLP2Wuq/UtVXWkg==", "signatures": [{"sig": "MEUCIQD0ESFUv9DhBaP516EpujhmByWxkVyfChIDi6B1JgtIVQIgPuB9N+M/XLTGmW3IRk4iCWKvFGOZWyfl7fEOuwpf14Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/yaml", "engines": {"node": "*"}, "_npmVersion": "0.2.7-2", "description": "Yaml parser", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.1.1": {"name": "yaml", "version": "0.1.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "yaml@0.1.1", "dist": {"shasum": "ac2a0ea9eb89f3d86c436763bd98b5bd92ed92f8", "tarball": "https://registry.npmjs.org/yaml/-/yaml-0.1.1.tgz", "integrity": "sha512-fSKygJKoSjMFcVbvQOPf4zPANpk/h4oVWr7EQUDWrC9IPYcKBbyd2Fjp8FSuaHj7/5WU3Am2kJCYZnsv9YeMWg==", "signatures": [{"sig": "MEQCIGSEJE0b+x56YYuwJQtMbkrvktcukg2HA4XN1awuXlHJAiAz69l5kuX6QXuxPWdajvvCC2H1a1i562XXj1Cbmxx1gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/yaml", "engines": {"node": "*"}, "_npmVersion": "0.2.11-5", "description": "Yaml parser", "directories": {}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "0.1.2": {"name": "yaml", "version": "0.1.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "yaml@0.1.2", "dist": {"shasum": "0459d27fecb4f015f072b15db8ec64ca64af603f", "tarball": "https://registry.npmjs.org/yaml/-/yaml-0.1.2.tgz", "integrity": "sha512-oVQVCZW+EJaviR/WZMpuJXgOdfGkHfYhXkwgXYcIz71jDmHZC/CWEKHt3A7aWe9VpHKuSntR9UiCtne3w7p63g==", "signatures": [{"sig": "MEUCIBu+zYrvFG35drmot5Dc6FZ3dvkYLDw98YxwZYBIPewEAiEAtqSv5wk3wGUr9CecLTEMOPL61qk8td0+plNGwBuvkcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/yaml.js", "files": [""], "engines": {"node": "*"}, "_npmVersion": "0.3.18", "description": "Yaml parser", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.6", "_defaultsLoaded": true, "_engineSupported": true}, "0.2.0": {"name": "yaml", "version": "0.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "yaml@0.2.0", "dist": {"shasum": "6c37d91f3081690ec0e11c1b7d624dcb0e37cbe3", "tarball": "https://registry.npmjs.org/yaml/-/yaml-0.2.0.tgz", "integrity": "sha512-KcL8Uy8WvJhtyi6JAO2/+YMoKpYZU/aL5XSK+WmDJYhZ66oeyEkzEd9dRaAwfv7aLH6fhYtQx1abjA+j1PM4oQ==", "signatures": [{"sig": "MEYCIQCvkGDcbgRD7QB1drJosGCaCMYds/IYDyH5sdd1ovWQkQIhAO6dnV8t+c1zU9MAcPsbQ9LgkQ7ruqC4Ei84rHp2+H55", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/yaml.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.3", "description": "Yaml parser", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.2.1": {"name": "yaml", "version": "0.2.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "yaml@0.2.1", "dist": {"shasum": "e925585765208789e7006d673d79e52deee92b54", "tarball": "https://registry.npmjs.org/yaml/-/yaml-0.2.1.tgz", "integrity": "sha512-jrkvOhYBuSFoMkPptnISCd3v5hge9hPfbIJ4VcVlk6RE00b9gougz83oj097DTmhA9Orj4aiIBaINxeHrFRmng==", "signatures": [{"sig": "MEQCIF7ptCaQtF3/OHE7ETBl1JbmlnGzfRf13jsuqLbvTptQAiBz5LbzPYWbpSUOT2j5Wed2lcVBUY0MlKBNRH03IR/KvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/yaml.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.3", "description": "Yaml parser", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.2.2": {"name": "yaml", "version": "0.2.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "yaml@0.2.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1c83dd0b800f2dc867d6d3e4b260907f8ea0a0aa", "tarball": "https://registry.npmjs.org/yaml/-/yaml-0.2.2.tgz", "integrity": "sha512-3YuIIDzBqRjTuvAcpDcTFm0+NjY/hxnoj5Mf+DpA5xE9sOSckpq2hrrmCcetB1o2hhC3iv+PxpfSpto5rEM1QA==", "signatures": [{"sig": "MEYCIQCtpeFj1C/oqUr7rykkP8RaTfoJ2YHCRVBMUBJVBbw4MAIhAIEGU/2SLnkHy9LJuk58b1+d7a9wVKVwkuU1zN1Y5bfB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/yaml.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.24", "description": "Yaml parser", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/yaml/0.2.2/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.2.3": {"name": "yaml", "version": "0.2.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "yaml@0.2.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b5450e92e76ef36b5dd24e3660091ebaeef3e5c7", "tarball": "https://registry.npmjs.org/yaml/-/yaml-0.2.3.tgz", "integrity": "sha512-LzdhmhritYCRww8GLH95Sk5A2c18ddRQMeooOUnqWkDUnBbmVfqgg2fXH2MxAHYHCVTHDK1EEbmgItQ8kOpM0Q==", "signatures": [{"sig": "MEUCIBFM/paJPFrrGFbfbXaq2/N7kfpKjY7nhw8WMFXXygq2AiEAxh+w2ImVFo9OHof2fH1ax3lS/4abh2FEXJI8GV1rWGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/yaml.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.104", "description": "Yaml parser", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.3.0": {"name": "yaml", "version": "0.3.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yaml@0.3.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/tj/js-yaml", "bugs": {"url": "https://github.com/tj/js-yaml/issues"}, "dist": {"shasum": "c31a616d07acdbc2012d73a6ba5b1b0bdd185a7f", "tarball": "https://registry.npmjs.org/yaml/-/yaml-0.3.0.tgz", "integrity": "sha512-ka+SiTLi+1YsgtqfXkS+sIiVnX1mHwcRd0Fzaby37piYmwA9wOj9dZLivZhTgKnzqhIkWcVIajS03CIGEfCKXw==", "signatures": [{"sig": "MEUCIQD+My2qjzVGn5EHRKf1ewRD6J/ehFE5t9BHNIh2q5uRcQIgfrt0b3DKlP9fLGFCqqLcRCgys06DosXjp10muFOo03s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/yaml.js", "_from": ".", "_shasum": "c31a616d07acdbc2012d73a6ba5b1b0bdd185a7f", "gitHead": "18814da3b49eea33186c48e3d5ebf8a4b7c2af6c", "scripts": {}, "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/tj/js-yaml", "type": "git"}, "_npmVersion": "2.14.12", "description": "Yaml parser", "directories": {}, "_nodeVersion": "4.2.6", "_npmOperationalInternal": {"tmp": "tmp/yaml-0.3.0.tgz_1456059619421_0.44368707737885416", "host": "packages-9-west.internal.npmjs.com"}}, "1.0.0-beta.1": {"name": "yaml", "version": "1.0.0-beta.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-beta.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eemeli/yaml#readme", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "48b24957df6a666fe77389f1fc72bfc33d857877", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-beta.1.tgz", "fileCount": 19, "integrity": "sha512-uuHMAUpqaEFfbNNMr+efB0YKHdPEz24C6yJ9pLppXmyrV8T7vcETppQh7CnvpXk9iGhwqaEi7jPwnFEHaBHWtQ==", "signatures": [{"sig": "MEYCIQCEXvi96JUogXc/WRThKc3h55IeFw5zLVHob8ESWyS0awIhAO141xB/fQGvS96enmfvAaBCe0P8KA0A00DmbkyCE6rM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67623}, "jest": {"testPathIgnorePatterns": ["__tests__/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/"], "gitHead": "ac6371fe45ee45da0dd50dd46cef702130922da4", "scripts": {"test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"raw-yaml": "^0.2.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.0.6", "@babel/cli": "^7.0.0-beta.37", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.0.6", "@babel/core": "^7.0.0-beta.37", "@babel/preset-env": "^7.0.0-beta.37", "babel-plugin-trace": "github:eemeli/babel-plugin-trace#babel7-build", "@babel/preset-stage-3": "^7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-beta.1_1518515923055_0.953793947984761", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.2": {"name": "yaml", "version": "1.0.0-beta.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-beta.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eemeli/yaml#readme", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "342c988cbe8a4e1bd5bf2e1c6217093bfc0d0ab4", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-beta.2.tgz", "fileCount": 19, "integrity": "sha512-uWoCkDejwfdi8Cc9DtAnPnevlrPStWnInzHhdV2Hr+RHu8X7ntIIpX7dV/w0b2afzKDoDun7MdNHObkaYf66rA==", "signatures": [{"sig": "MEYCIQCmGX885dPwNzcRwY4nAeeSltiZVg3Aezk2DarcfLOSWgIhAN1Yt3wipuovxSTpeYpuyrN7HkPrI0sAwu6VLYm1bsm7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68381}, "jest": {"testPathIgnorePatterns": ["__tests__/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/"], "gitHead": "5a668ef279fde809f47e1320773c5e0c05a062de", "scripts": {"test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"raw-yaml": "^0.2.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.3.0", "@babel/cli": "^7.0.0-beta.40", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.2.2", "@babel/core": "^7.0.0-beta.40", "@babel/preset-env": "^7.0.0-beta.40", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-beta.2_1519030800463_0.9062757794447585", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.3": {"name": "yaml", "version": "1.0.0-beta.3", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-beta.3", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eemeli/yaml#readme", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "87062d37a4f17b159c7a766b42d2dbec10615560", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-beta.3.tgz", "fileCount": 34, "integrity": "sha512-bcDtRmdEIq4rmYSpO6Zf9SRyE00ae5M5ifr1+qxVyszP2nO66EOPFaP+rZSr15w8Qw/2ulxoNWICIcwxYiP5pA==", "signatures": [{"sig": "MEUCIQDlYdfhPD+AOI9tcoQAh3L73R9gEtMJLVqgD5eLOoS5HwIgHAmnSvNnoy5hI1P2v5OxVEOqtjz077S6+mYlAgCCwx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161355}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/"], "gitHead": "ebedaa294bce0e807782687010a43d583755a0cf", "scripts": {"test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.3.0", "@babel/cli": "^7.0.0-beta.40", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.2.2", "@babel/core": "^7.0.0-beta.40", "@babel/preset-env": "^7.0.0-beta.40", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-beta.3_1519340574025_0.14518911144196167", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.4": {"name": "yaml", "version": "1.0.0-beta.4", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-beta.4", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eemeli/yaml#readme", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "c04bd5411e6126d773604c3a1725c8a025813d3f", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-beta.4.tgz", "fileCount": 36, "integrity": "sha512-4/ByzApkMcjsnml9sr1R8gpzw7WpnQMiKp7QVhnJzaJUUa4wjekR0eqJbFqM3Wa/r5QZLxaE9du6nTbVxVMobg==", "signatures": [{"sig": "MEUCIG6X3S42ATedqr3HOUHJL8D0UBaLVvRYUYKXjat6V9HZAiEA6w1HK5DmhN+EyTTG0Fbn3RY707jzfdYCtDLPNwaJlZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178033}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/"], "gitHead": "443ddb7898a88ae379862ce4f5c7457572000556", "scripts": {"test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.3.0", "@babel/cli": "^7.0.0-beta.40", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.2.2", "@babel/core": "^7.0.0-beta.40", "@babel/preset-env": "^7.0.0-beta.40", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-beta.4_1523192219652_0.9381838682754213", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.5": {"name": "yaml", "version": "1.0.0-beta.5", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-beta.5", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eemeli/yaml#readme", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "17ac674c225f72d74a9eeecfc81d30ec369ed1b6", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-beta.5.tgz", "fileCount": 39, "integrity": "sha512-ENvzwo7VBdyxTmIasviOYqC0Mqe1LnXH0/D7Z9B7E8K81SzBWn7ab7XbJ9B/zxurtPJVa84A1nQRC90xQilcxQ==", "signatures": [{"sig": "MEUCIQDTfmKsCR4hOarWl+xTNwf9lXzKyPjMQC3Dp5YFvMDahQIgBVtWLtowfQ4wzYU1jKAU6473ZeBVYKfz162+aXjyV5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+yU8CRA9TVsSAnZWagAAEvEP/RfNiTkLlZgk6+2WzhxQ\nzHsnBCHJ2MQZetsylBsMig/BPpSk36bMrly6vCEmzDZ8svHEo5XAZScIvqt0\nd0fpwfGsw4t+NbM676P95H0Wsevrs8ruJq6xoK48wfmCasjPaBIBbmIAvM2g\n+0DlKPNHoywnFHZsB+JlaVeAkLjnX/ljeUBlgW5NrYYTExmeLOx9rq4SLsak\n64FBJn2A574n5s3PconAcq+G6tvLTZnQUY+qmgfizwn8mbmK8gmHlKIHT2KL\nZXKGgukZRLI7IVZfFJS0D1NArhgC1u19Oi4qFGf3JCxcHDdK8CKMKg4nkTry\nUT6vulGqR/+/z0c9EsAdpz2jnk0S03hCbHhUxsHwjTr7wBRDHj79ogbcESz8\nOqB1sfRka41YrqEJB78GxXB72GmBp4tyjfK1lKofkdRElC0Ep68N8WoSOPjh\nA4WPeP23XgqOztUHvdiTiJ12xzi7LeItQti9ebod44Og50NnJ6PzE5Rjgomu\nTIYr/VaQervHfGMpIdBJspGraAn2nD7tAkAH/Q80gpfmTCkZXnBq8K0xAcIm\nsVN23MSWdDPnxXxNe15IYyw7dDWsro1+2MMoT+yI1cuXTxK12mafYsLyqgQt\npdjBev31ZYM4TwqOKRxgvi09yZXB5OjSQvh8ai+hZ+Z/NoCy1OcHbxSmGIMZ\nHjvp\r\n=mOCq\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/"], "gitHead": "f8705ab4f49d1c787d383136b4783a1ff390d749", "scripts": {"test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "postversion": "git reset --hard HEAD^", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.4.3", "@babel/cli": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-beta.5_1526408505479_0.08075331494999771", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.6": {"name": "yaml", "version": "1.0.0-beta.6", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-beta.6", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eemeli/yaml#readme", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "531cc4dd6327481f0a76c37a276b7eae4ef4f1d1", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-beta.6.tgz", "fileCount": 39, "integrity": "sha512-5AdGL7nfpjOu5X7Yp2WuBh/1ZDVLBHNiuB/5HR5IzZMI1bQR1EeYfDwt5yoqC+rax3MmZMLGPNn8ys9ssFWfkA==", "signatures": [{"sig": "MEUCIQChaKlL5dbI7JkROSkMtyn3RetRDHH/sj9fpRE22ybKIgIgUC8Sq1NRjgDHlgdH8yJfMgcdtF/BT8W0zL+itze80/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDbsXCRA9TVsSAnZWagAAOFYP/2WpJLuWe1WSU3M3a8vw\nGs4gFyP+/6b5h77tL30iKxxmWMDfS0rYcPxfy1cv3WvmfOf1ov2kZl05mA+O\nhU/hjFWFNyI53DLt9Sl+sDKHOd3HNDSwxDCgq4uwbAs/FWozKSU5WWqZ0YHQ\nTR7bOPj7yAIW8CdmvqF5mdgRSdKHWnYDQ//2oritZwOYAi2JpxderAUnYJiu\n68SWhFoJq9H1qcY7/5XrbhO2SMrCex1p0jY0YuzOAhV9ZhNVnhNe75mqRIiP\nXuzC+/BDnjxYi34thgMwZ3pR0sZdXCM93UV0jn5B7k3cZO3EIfSnu2aO7cyR\nSYNB9OeTLuIqMD+9uJ63bVVvg9l/c4EEo8MH+imCP8pJvvxc1RboQUgsTZPf\nVlpNNvdlNd+D9pQst9PtqVmUiKBD3wBqquP7fBF2U4SBx49iHtwcbzPgP/Wx\nRoqpfjo/NBeB8hnJe8o+nqynYG0Zr+RgnmmADtVylSzl7KUabyQT2CQmpm6h\nJWc3dCdjzR0MPQuD73SsJ9qmI4YO+H6RcN7lF6Mip0vUeIO067Gs25S4lHRU\nTXr72DaPt7EP2vVepGnd3QPgGd3yuawu3GGs1+ARDssuVjBnReQildSSxjpJ\n2W88dZlDaPT/RwD8giFoFg3APH0LOQ7y7QbPez6CH0bYHTz3swG8rviiex+3\nRPcE\r\n=oAmP\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/"], "gitHead": "872ce41b02827fea03987c1fd759556058ceea55", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "postversion": "git reset --hard HEAD^", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.4.3", "prettier": "1.12.1", "@babel/cli": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-beta.6_1527626518743_0.8261273455543487", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.7": {"name": "yaml", "version": "1.0.0-beta.7", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-beta.7", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/eemeli/yaml#readme", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "681937c5b2d85b6eb474d47e877f5e63a7efe20c", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-beta.7.tgz", "fileCount": 39, "integrity": "sha512-nnAWKkYkZUqP2o3D88A0tjIYpSazbv2LHYXoIVzqKOhsw18cQ9ANuNtbn055jijI8xssSbT+Z69Xb4D+Gz66pw==", "signatures": [{"sig": "MEQCIHWlhdg+5pvQ9S9riHCe6bwVS2ms/j52wneSW+DgSPmdAiBicTxLkG0L+zFqMD4c8c/RXu8PcrBtn76krcbCYqFU/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbI8d3CRA9TVsSAnZWagAAIlIP/iWQjvMWSWRYlxYZCMI5\n4TLVs4nl0qkCgal0G3uY74Q9vmdNlAlnM0nfapVqmC013TQCmePq4Wc+yo65\nQcP5i3TWn41qGessL71jwYAyMSuRmxeIf4IzEKNU8fEri6QyIYtyAXdyCXCA\ninqU2C+jPy8kkfqO5L0iD+lMEYKoX6YZ6LB5tqtnlE20MarErPPOIFbOz6Zk\nh1C9YMKNY7nXEf8xqL8jXbvWo2Wcveh5Iyn+xMkpKttZckcz096EButF0FLC\nWaLWD7S9YgW/39EQ74LWDt4ANNrgGij1HRCKwMPvuH74ppeK8KAFSbqUDgRM\nBnBLeDDxMw0PLSqKNLn8qutoCY8tGv/G/88unxLFwWR6QpWw4HBjjfizML9k\ne39wLA7jVGytqcD6YIUb4iuIFJmDVnDhCjqYsldVMmqqY5v3pRSJd1QTsO1e\n8sDoYq2z/J4g6PPpZCYF5lNjk6AuDyy3j0FihK18f2cWDGFnKSyPjQRLinKJ\n3QL93op2JYaGPAetLgDu2tRjsqHKIk44lHw6nRsYsJ37vrhsIm/HMO5E4UtS\nJdaOR0WZbrSPyyaoAREF0EHLfiDLLVu79F59o6PMT0VYi1WEKMu9I0tpq0RE\nPz2xMC77oEEE76pDtqRkDnP1SWFtwOWqmZvr+4kuX404HA0hwO8EELXfOaIh\nfnsj\r\n=214Z\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/"], "gitHead": "4286bdfe98609dbf600526930359d17b96f0c1be", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "postversion": "git reset --hard HEAD^", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.4.3", "prettier": "1.12.1", "@babel/cli": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-beta.7_1529071479501_0.20629537654386376", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.1": {"name": "yaml", "version": "1.0.0-rc.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-rc.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "0c03cab07797c2f55de3afd651e739d3cb941efd", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-rc.1.tgz", "fileCount": 39, "integrity": "sha512-xO+n1Q9TdC/TZIhjsbHZA7lU9fLElT1BmbD2OTR0TZdB7k25DdiGdhPS0mt+Vu1i98ftaGo4GdIbyH8xAxbrwQ==", "signatures": [{"sig": "MEUCIFEv/IU8vLWH5PYvGBJQzi9fxe/fOvWj4Nv6VDNh/qIxAiEA6D3Ul7AfJu9ETmgDBICluYk7l1nq4XEdsPK8QRD2JLc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKCCICRA9TVsSAnZWagAAq30P/3lKui8RjoUzQe66zFj7\nkQem9SamED9nhfAccPH/IU9AymUvG7Oqe9N6JJyi7lXmB6RN0+e7WiFg5WOJ\nrM2EMmu5y5ONN/fvv9PIZXiouQj6tlNfhaCMfrw7AiJzHJYjYRhyWQqCLM1M\n+scMnT4l6vUuZz+rtjtcGWsHUmcFO4Nu1KQRm6IGTAxzt3a1uv90UpCRlSt4\nf6VpNdfjRPyHOqK2/UMME2f+cejbdecR/C/lZ7fVUZLxPC7W0G7wubnTveM+\n5jPgYYXkyND6fLOOSRrKcmi0cEgB2hB991irk1Vuw7fcsEDzDqSZvF6E+9l3\nejdfCP8x6aOI8cO+i6tstqlCqCu71ED2K7m0RoRDNVYI/+/d+W1Us1L+/Px8\nP9eiJUgPBWOYNXPOjeLDRTQqkZ+hXbHVUx8NPxtwOkHxtXJWqfqUy2bChAhN\neKsoTZX0qqWA5vuCVvKbVzfV4pvNnuQXGV4OnRJFBqoAYgtjIzANPcREBB3l\nzgyNS3mPyweoim5u5hCcHvxYwxdlNZ4S/WkhYIynHnaPcKebpxDH3PjpN4O4\nmTDJF/eol3c29gyiqRGpqUvBcdiHL71OkOaZDwLdPCOSFOXIxXgPRWFG6f8I\nBj/xtAWYcuA3zoJF7EJGmWCGPSTzg2N2/cyKEucuB9WbMvRZ6TfIY0y/Rq1g\nrhj5\r\n=Tc12\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/"], "engines": {"node": ">= 6"}, "gitHead": "4ad0e6b81dc31b1e130b9ee5007dacde2a65d903", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "postversion": "git reset --hard HEAD^", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.4.3", "prettier": "1.12.1", "@babel/cli": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-rc.1_1529356423774_0.727602425586136", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.2": {"name": "yaml", "version": "1.0.0-rc.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-rc.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "aeffa1ee128b7babc8f154de08e824376b674f43", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-rc.2.tgz", "fileCount": 46, "integrity": "sha512-I1K6Pbys/yK3ghVUCMbfbeqXX/8OjpBWIu/OdMSlMk5pkBFUyx+O57W8Wxk4OttSRs/3FqQ3TVtd5i/ODugVxQ==", "signatures": [{"sig": "MEUCIQC/mm8Ne7zFx9iktQDv9Bdd3ATmqGA1ALpkfoAuIo3D2wIgVxWjE8yk2gEQXyfbz3exQBTWuFmnEJJKhRFtUS3OEpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKCHDCRA9TVsSAnZWagAAkWAQAJbc1ininYbWvt5aJUP6\n+rHV+WS5kwR+bTr/rDeT5BSaftwT6iht5ChhgdXZ52SlumxI+A5A0dhFsYdN\nNYh08iiSvbSmtb4g+RNmj71znKbayEAHNzkRl/7USC4DYXO/FlILWY6ib3Nr\neylaB7MUNeI7+37YFQdJbqCuDD1W8I0p5FBjRBkRN+kpPsyNiaA9x2A/xiAJ\nQHq9vmmzraJZkE2S//yzNx7UQ/Roud4KmeGVbix94wbjSrLqWsCmoaNV6GFa\nKsw4Rv/Cf112/o4spB2YHxesQemKaHb9Ou2stu9p9kNEp0+k5Quhw2uRl+7n\nr50ZZhRywzA8V3SU/bmzNLJ+XG4sCrPVBROjs7n+XxJggN2CK/Lf8o5WztOG\noQjNwAnDndB5WAxsPlmM70ZaPH6glpcB+OPv/cCQtUQ08/50/wN1Xa2jt4aC\n27oIOljCkE1gmkJOcTunNCPL7tZtlDHVadxKQ6I/cJzb1ASdyaLK5InFl/du\nLsZpMycm9CNLZyI0PbUKJZc2FKCKGfgZ3XiwBWNVhir5APcG8ZxGKgCmlYBq\nL1ve4Y+f+Lvk/UZ3zkMrF1cmKTy2w7X8zcCl4SVl+/A5JLeacCeQPn39RBGW\n17SI+9Nx6Han2yU1SiHLb1kGs0ZGM13fawZsKrp8pf9MmKiPFaWe8GOqM2NG\n6138\r\n=d955\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/", "types/", "*.js"], "engines": {"node": ">= 6"}, "gitHead": "f6fd3d463e4a1c70dd7c2b2e4ea8637ccb05f2c7", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "postversion": "git reset --hard HEAD^", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.4.3", "prettier": "1.12.1", "@babel/cli": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-rc.2_1529356738698_0.7431503725995268", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.3": {"name": "yaml", "version": "1.0.0-rc.3", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-rc.3", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "e4831862a0a89baca9382e772a5b7d34f60a3cf5", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-rc.3.tgz", "fileCount": 49, "integrity": "sha512-iQRr/5nKb4AGKgq26pnvK5wa/+q2ECXFnJwOByvjHh3T0ymHimnF/0+v9G1C56CXyQkx4ncHlPFJ4l9dSO0Vow==", "signatures": [{"sig": "MEUCIDKCsLtPQ4Cs4Kwab6eP/2+BvFGPaquOn8Br/qtWwDgDAiEAhIB+MmHYgNWaed1XtHt61EiSdKQWJOv1AZ8MDuINdVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMpAmCRA9TVsSAnZWagAARqIP/1+NoRI0YI+2zr7lrh0h\n9PfO4FcTdEUPmRdcTQ630mP0IrSDMZcCocHPo1TW/cI+Letei63tH3j1TwiR\nF8GMeICxZReybvFXyK8tOq8ViX0cxd8SdDs7ghpp7f9mTZqy2/PtpAKFhAck\nZH3qyzA3sxcPcaRc42g2Ldtn5iPPBcFe5fOTd40A/ZPqT1Q3EH25yawx6wA8\nBSahqYyppImjoJ3rrvjrZaR7ylXlX4w33RQEbOAEyHpeIkNZ0wSeYj0jJluF\nuo6sP0kErvuqoABwb1+XTCeslyRVKkcV5r6PKdxO8NNPVb1tDOSJFJ/waCU5\nG4wg9LSlVr2peSOb4Q453jN6g78Z/PtUIwvco5RPXusd1mR/HTc/vphZHPmI\nqV+prKidzGv2PQskMTwPMcUZcvz2iRzeKP5Tg6Y+3Zs6ZOyzEJOJzyc2XN7S\nm2fbyXEvWMwBuPHDTgB7FNRJ0VB0juuvLof+/rjhyphTVf3PKDqo4R43Le9M\nThkSCRj4j7F13ep+6RmlslAUFCvQOaHJTlp9dI2xXWXERMn4vUhUrNfsUOED\nG1gzJb1rr8+7YNivzBKhUlkqfEvOq47oizfLc2ZLilNzIaZue3qrMKPiWN5l\nHVtBOyb3SEPErQhcmtaWPzTmO5H03Ej+jyD5Ujgdwgjmv+zFwi5QQY2HwZi2\nSxBz\r\n=D7bc\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/ast/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/", "types/", "*.js"], "engines": {"node": ">= 6"}, "gitHead": "da10c705e55c03dbbeeefa7f30e86225b10a7b82", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "postversion": "git reset --hard HEAD^", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.4.3", "prettier": "1.12.1", "@babel/cli": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-rc.3_1530040358554_0.503312615896834", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.4": {"name": "yaml", "version": "1.0.0-rc.4", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-rc.4", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "850dc77d9b03975b5e5ea8a9b37cde252a8f1f0b", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-rc.4.tgz", "fileCount": 49, "integrity": "sha512-nK1wu7MJaiFwkoC24KdYo0PCl58AKaAyIliHKYt3m4jPanidB2fsPDQnr5BLhNiGdQTYFA8H4zNecLCN/mnHiA==", "signatures": [{"sig": "MEYCIQCGAu2lC+3osPZWico9YuWrNaoESIzr9NfzSPmmlgjzygIhAMnh15XbJX0HkSR74lEPyNJE4AaQ0YlooNEnsAvL9P6z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbM/+QCRA9TVsSAnZWagAAo1gP/j78gXUWZaJIWvZqdNdd\nQXhPfTm76+EDJWIZ2uJypnNyzEDeWMnP9/aGJFiyl5qOE7xMfbeu86H4pjML\nbexjrMx2bPGeg1SW3FrdE0ncO1K31GLfBnum/WHRp1QsNvi68ccFZclk22F5\nDpnKwAehKrVBpVVnYztw6gC6ZSm0zBmW2mUwZN1l0He4volLHt/4juF+vCqs\nFUPn/LzB/4E28Deh/GUzhhT2ZIfhnn6uEsmd6Wp+wpl3zFLVgWZtTfVyS7mD\nDY6AUnIlXmNKQNzRcdWVilscK1L1W9rTR3CvN9sKgoXTaw1n+YLMkypbK5k8\ns+oRb8xxazP9SrdBzIHlcVLuHYU5luQN0Obl8hg3J3Iv7NDcAxGgU73d3W55\n90MbufBb0YGF0YqSDAp4gXrvp6szOEnQo3uJXmLfhEUlCR89eGUj8yUYf2Vo\nnErtNIBT4PL7EJHcMy8yw73JkMtMnHHTc6StFigqI6HPgHEjNzDYa48SEWl7\nGRsulicvrCyX0sM59fFNfmoDiadFVSUgwVjP7GyHuYxCt9OD40COuc+M9uRN\ndpLQ29sPSMaFMCjJHg33mrRLnlEubSv1b3BnbI1XPNciWoo+hDKRP/fB7oqs\nHIa7AW/NeEl/UYDTeXYlv/djkvKmjp47cbcZ1IdGBZI7QdJCXbilGUZNf7Fe\ndGHw\r\n=qvtd\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/", "types/", "*.js"], "engines": {"node": ">= 6"}, "gitHead": "cfab593098fe7473f463a2b8c9a50e3f6a078084", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "postversion": "git reset --hard HEAD^", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.4.3", "prettier": "1.12.1", "@babel/cli": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-rc.4_1530134416486_0.0414128667357736", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.5": {"name": "yaml", "version": "1.0.0-rc.5", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-rc.5", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "113aad47e7a6c1c59759fb318459e42b96708021", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-rc.5.tgz", "fileCount": 53, "integrity": "sha512-pstAfmG+aYz992zHy34x0NUsYPIafD+FKKAVI40ZF05AhB1Uy6TqbZfSWdt+tV0Ae3JdMIzaNFHA9qXicxLDXQ==", "signatures": [{"sig": "MEUCICXzIw1sHzeBTVMqSGt21I+/OEfGYYGL9ezL6QeKWVYzAiEA7ExSAXx3V5E4xg3b2ZozNU8BAL71ZsYZlwK4sn1J0XA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 212728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN6JcCRA9TVsSAnZWagAA5uEP/jbjXR8LXd8zJ5cdq8oA\nmq+XtJ4f+uNZ/r+BHGuvukfutaoHLW/j40ICXgwER8Jp0aOWJPgMYC5hsrJu\nBKXZyX1MshFrV8Wpx3U0pavwtydHbfzX4Hookw86ZIoct3sqi5AIahF5i6YX\nF7zomcC2pPVfPjGZKPrLOsXoMeOlEr14ufYr/nbzIkdqba0PLtA+1w3K+Y0V\nwWqd0Li/sKVwLV+FiQD4fOrhEQgX+dOL/x8MMMlr0466mIcwfoeq1WkPZzYF\nwI0Tk1eBvHciwLk4Tb15EjLDRAezzj/mHdmpZGjbM/Lot1IhMmQENWnNYd5u\nfj4s1AnoMGqsC0OkrXkaLV3L6I0iuaQQhvlcVOZwZmLp4CPZeZCGeWfEJY48\nDYQYg4g81ceumu4zv/iHzBJTzu8ngl7k5tBrWbZ4ja3WyB2rqSt0FAX7Rdhj\n+8p38GIF3h8YXhGx8ECdvGMkS9Y7H2PdHG7puvZlf6vmffLth0t1dOEfJU9k\nfFwbdmyIFZnFkRP2+8r41xcBADQtJwt556vmaPJttPJZAlnMywEgj19GhhK6\nDWghsN+jni+WwGETg27z9cCJD2xKcapgSrugJE3jqPpjz7snTKbZHgKCP6/3\nb/BFIWUeTrY9dnkWj+3jjCqux+rRa0ER2e6mLaimxZLLuufyUt8LKFgaUpJk\ndXwn\r\n=lEew\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/", "types/", "*.js"], "engines": {"node": ">= 6"}, "gitHead": "d5f7ca612a84f5f31c5aad24f06d4b441a39b710", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "postversion": "git reset --hard HEAD^", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^22.4.3", "prettier": "1.12.1", "@babel/cli": "^7.0.0-beta.46", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^22.4.3", "@babel/core": "^7.0.0-beta.46", "@babel/preset-env": "^7.0.0-beta.46", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-rc.5_1530372700405_0.4796536218428211", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.6": {"name": "yaml", "version": "1.0.0-rc.6", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-rc.6", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "42beacf1dd7ea2e32e50456051bbe54ea54a1244", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-rc.6.tgz", "fileCount": 53, "integrity": "sha512-vjE4BkY5aEYMGLykB3+Uo5yWDOvMqfT0EWFHImMPLKXz6i18fSkf+WFEjG0rfamWr73jxRCH9u+a6PEQJox35Q==", "signatures": [{"sig": "MEQCICRXgJw/aDpTBahO15b0qaNViVsOd24j6wAONTI2kpbxAiBdtVZZRGl12mxIz6zUnaUjmwDVbAL/q9CkcBPJGcCoXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 221127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQgCLCRA9TVsSAnZWagAA/UMP+QGKdXHR7jSOT04TTZ/9\n80XFlDchAuUIsjwJ+fhRjrt6sljAXml6hWWFHdKZ1khWAJXSyY+N0WlvIHRM\nl9aHHz94uL8DPIy4/PJfdns5e81Y9fmo/3V1NBzW1rEtgWumLl77my3Coe6R\nSR0csgbZrsNtsnTnDW0zsTdIyT8AAxpc4nRlfDWe2o3r/lT4ELFnW0eu0t7r\nRXA1+jcpmNNkXGpkUaP03mlJRfqrt9IGI2KaRdVltzqpIDqUS5d/vwJp487z\nQG/zTQTwNnX7Tw3ycEIfCHWAUCzj9o4Ks3Klp/BoC9dExtR7xkgQZYnOozdj\nfG/0Zo+kWN7ozz7+E9J5pXq3dLnFAeECw2qrV4JwUqXgAob96g3/6A6w5zqY\nVGTJH4B01RoagOV/vV/SMid/MT1DG4qJps/SSXq3U8rvxdMwmtHHGn1P+DWt\no6hUGVwug7qxyBH+InKtSgQYaSI6Mk6ltrFHFmlr+g2Do5sSFzOpmTWrwcuE\nZeDFOx58CFO/aOhdUjay/qdTpBjORUBKXgfgfsF8EgFYpL+zJ3iBQ5P/A3H9\neUywRBGC1n/kVjWTHYqWoH0NMcJPeafJ+7LxryVOMpTYo6KSdWAsX2i/sE6/\nyzCV5cNN+aN7bDXjbyOBgjTYbUxQ00npxucE8BygS7KlpCinihI3nJFtU5SC\nIdjd\r\n=Iz6e\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/", "types/", "*.js"], "engines": {"node": ">= 6"}, "gitHead": "eaca37a799e7a74318a2f3aef2a3614f23180e0b", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "version": "git commit -am \"Update version\" && git add -f dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "postversion": "git reset --hard HEAD^", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^23.3.0", "prettier": "^1.13.7", "@babel/cli": "^7.0.0-beta.52", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.2.0", "@babel/core": "^7.0.0-beta.52", "@babel/preset-env": "^7.0.0-beta.52", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.52"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-rc.6_1531052171357_0.6230809306507668", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.7": {"name": "yaml", "version": "1.0.0-rc.7", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-rc.7", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "7cf9dba3c78992542b7a2d7cb9a7eeacbff63f77", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-rc.7.tgz", "fileCount": 54, "integrity": "sha512-tsTvdZxHPSgwv70qw0w2YeGA8VCqEpHRlYLb3T3ROhke8j92iC3t7kd2XHkiyp7pvxucKUgzzo+3Zq77WV82Kw==", "signatures": [{"sig": "MEUCIDHUseL8kSMO0EBYdQjk8hqNJeFnjfKjQ4ubhidIY/77AiEAuKIqfFwngoRlyC2gLuLS74w6y38WR9VMqJZ1SRRq1sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 231635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRiLHCRA9TVsSAnZWagAAQtgP/AwkleThR7V2lf/sRQB/\nKVYvbLAy+vjwInjZ5SjEKun4OBqaH6Tgiuprn6x5z1zH/C23EttYFqCLePt2\nzQ56YuaVQa76D5lQj7cFJojwk9WgIZryVwoo+YU13hzJifINqaiD1gHo2b9Y\nKCKBCa+PxV0cHunapMS5sk0cRdOW+ZNmyPkLBpbIf6Lskgjr+nN6z+WtjYD+\nuNDM2YiQ9v/C3DyQRiUDDehhgvw/2phgwyRnPoy4qSlUn29ujCDMN8hMXE2w\npp994+XhB4KzHNuWiFTzHyCEtENjwXUgbT/AyqJOALk2D/c4BehtC2wzPSt6\nOCgxBC9/eztYqt05PmVu2N1vZ1HPDMGvmgeb51+eHI3IKaTSO6Vv4tAhd/U7\nZmARbdNs7cA0H3T0M25Davw/2Eb9+6uMrQIiDBYr2T/pVb8qRKM22S53E0gZ\nOieEkGqTiVnC/3BT2dAoZ/Ak95Ygp4Vo3GUJbnTwbyILIFkMLlxF8wbKksMI\nObgd52e/0HtUQjMqojuO22ZM7On0GQrv+tncr3QkA70bU/0PofM932Khdipn\nSjt3bk1MGW+d93gdhY7g3avT29CeEkUP2wa3Mj0VxYXu3xxX5A5ZZlm19kQh\npl1P0NioNXMjkp4xv3CvctQ8REbRrG1QLbwqrt2v0BrxzDVlgUJVx8hi4YQd\nJ8X3\r\n=wmBg\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": [["trace", {"strip": true}]], "presets": ["@babel/env", "@babel/stage-3"]}, "files": ["dist/", "types/", "*.js"], "engines": {"node": ">= 6"}, "gitHead": "d41b9e71567b0210a1c09e1cdec7cf5fa9369057", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "JavaScript parser and stringifier for YAML 1.2", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.3.0", "prettier": "^1.13.7", "@babel/cli": "^7.0.0-beta.52", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.2.0", "@babel/core": "^7.0.0-beta.52", "@babel/preset-env": "^7.0.0-beta.52", "babel-plugin-trace": "^1.1.0", "@babel/preset-stage-3": "^7.0.0-beta.52"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-rc.7_1531323079119_0.8255308437424624", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.8": {"name": "yaml", "version": "1.0.0-rc.8", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0-rc.8", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "e5604c52b7b07b16e469bcf875ab0dfe08c50d42", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0-rc.8.tgz", "fileCount": 54, "integrity": "sha512-+6a1nAiwS141AYKYsGXp9A7uLsYwY5f2ql9BhpewjfLm8u8HYh43ZD+8GWY4FNusZjU5+oa9cg9Spx0orQSf1g==", "signatures": [{"sig": "MEQCIEkdLK6errP64P6yhz1g6SvHl2mTdC9pcK05BH+iWtPuAiADUjH0aJWeZmetS0o05LACQyJa3VfGJTRDCIbuMJ87nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfkAUCRA9TVsSAnZWagAAMl4P/0XyF9ckE7xyuDZSIpjx\n/2SQWCkqIp/cYNHwf9PgUHwrkfesFMGJtU8UeCjLPsv1wFwI8/+xW9hgsFru\nZvJiOfInVJ5g1z6I5BgOLZZk49WQ8RawMGT9uH8TDd4KzgNwjT1sOa9PkZBT\nPkMWz5nrn4ywxoc1iRl2icYw3P4DPOcrtcPMBXeGKcaEkkr3Ns8v4we71N5s\nkxUi5JJyf7x8yUA/ULyZfXuAPjqXTMcyECGTeOOqgW+EwRp92VAYOIxtbwPj\nxl3PZO6jEN9IjJLxTwoO0nE5rF+7vFKr0v6LU00rA4Xlf0nVAChQcvnUwl80\nBf6lz3mOv+85+HMpwCOkjd6MUX+rXkrnodLb7E35+bqTl3rfxDRoK0DotCtP\nFYsqJ1NVV8GmncYge3K/j7LqtjKfbVSB2uVQyrerUnDlmpGoUUk1M/njfLsJ\nbvHP27Ka8B3wbLT3rT5hE+um+ZUvWPx9o9fYpb63X682dRBj3EUwFkzj2nOA\nmZ895+CifjwykYefXypp8n+tLUJbkuu6d0XK8A0LrwDmMv7QfwmMAerBiIj2\nTfwSrBWlGCPiRpkgCtAtD8ak4Zi22V27vxpWxmLNZRaky2zXi732uUOALFSu\nhJv7lSNQA38UqmH59mXJRQwAvgUcVMX8ry968/bw+AfqZvuyu3tua1e8tucf\nvsde\r\n=Wo5n\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}]], "presets": [["@babel/env", {"targets": {"node": "6"}}]]}, "files": ["dist/", "types/", "*.js"], "engines": {"node": ">= 6"}, "gitHead": "21633235dfafe60796213e42b5ba1903413a2df5", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.5.0", "prettier": "^1.14.2", "@babel/cli": "^7.0.0-rc.2", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.4.2", "@babel/core": "^7.0.0-rc.2", "@babel/preset-env": "^7.0.0-rc.2", "babel-plugin-trace": "^1.1.0", "@babel/plugin-proposal-class-properties": "^7.0.0-rc.2"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0-rc.8_1535000596262_0.4265001788812868", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "yaml", "version": "1.0.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "9d428cd4641c87ff4b17b2d4f4ab96bb6dd17154", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.0.tgz", "fileCount": 54, "integrity": "sha512-HLMg8IQRQLLPZ/tVtR0j5ShAh4HJKt8soYsu0Fn3Y5eoIFJoh1cs1mvvOnRD236mjeBDarlk5Ng/b/IHQs+5Rg==", "signatures": [{"sig": "MEQCICBtdfGP9vPTtPBtcLsIjYanLBvtExRdX2sMel8VQa1pAiB84gmknb1xCorS1PB/rlU/N+FeCu6RMYXWtYboFA1ORg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboqoDCRA9TVsSAnZWagAABuYP+wWW0s2g1VFiB3D/zmX9\nPNirBX4obNwjc35HmoMblKDMi4efM1xBcs+lHw1sw04bhNewTUm5gm9fo1f4\nJPL0wTrPS73eTpSySNoBv5Ud77MmzjK0KU8Jay3vN85RUzF58x59KwIUNfLG\n17kWXc9Z0vbYzvhb06hx6c+xvnmwA5RKlRar4Vn3tDpq6A19DtyndVC/QqGp\nWs441Tsw/c/WPri79LlQzJ2DWX1e+gbrN0llVSk+wjzep0yul4ASGaH3kG1Y\nZ/GI2afTKmy3NKeoisZgRZBESBxsbX5OgxumqVf1dE+L/CvXqrX4t3s45a6v\n7pd/hKjZv1waH62GIkgs+Z0esvx7OKy195SFhpU/bMuNQZ5tko16GKNhXJEX\nZt6KGao52y+RotXkwHFAc06YJHU6YouyE9mfFALXLiRyIhfhRyfA0mV7viiy\nHFw7aXqRsZ96g0EF1vTsS5Fnys1JudC4Q6L9K/xLX1D8LpI/RMefvfpI/Mmj\nLt/ZNUWejiMHNbrDo6hS1oRniiDsvOtvj2pfk2WnACzCAqJL2ZZrB8gRkKH6\n5rdg9+Clnde09UpGmhQQ9lKy7efV4AHPMf+oKqeTk7R60AJuYX3jf9HYXe3j\nF49DC8ricyScoOGBQZSxO3g9QLV1m+UtYSNL2mh08ettFrmmMGZaPHSdUBPP\njvc2\r\n=kNrb\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]], "presets": [["@babel/env", {"targets": {"node": "6"}}]]}, "engines": {"node": ">= 6"}, "gitHead": "3649942472ddc6258415b4c8b697a73d86253207", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.6.0", "prettier": "^1.14.3", "@babel/cli": "^7.1.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.0", "@babel/preset-env": "^7.1.0", "babel-plugin-trace": "^1.1.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.0_1537387011211_0.15249343030622997", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "yaml", "version": "1.0.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "3a8b01e4dda66445904ee743a95951d67a47a0c5", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.1.tgz", "fileCount": 54, "integrity": "sha512-ysU56qumPH0tEML2hiFVAo+rCnG/0+oO2Ye3fN4c40GBN7kX1fYhDqSoX7OohimcI/Xkkr1DdaUlg5+afinRqA==", "signatures": [{"sig": "MEUCIQDZPvEw1bpoM8469CCqdRwwkDuBBrvcSlDTQPP17BkYlAIgGWot/489DywW5pdTvbprK7ydxC4PuUKDnRi7CVHmhW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4r80CRA9TVsSAnZWagAArqYP/RSCZU6/MOkW0nhopxxJ\nrpYTrB6xiLagfYvQdOHgCdj9m1MyQzI2SKP5fcrYvIZl0vh7C7p+5nteTfKk\nRYCQXnuQuq+8CeUdsx4owcE51p2Pvm7mFfaMKbQnrmul8BXaUH4BkYpknEjL\n1jgicxd1ZKU8k4oaJd7csIOIoRZjgbPw0tjyETJ7/iMCizUWlyTcEkECANaZ\nBL35QxbeKDULK/zo+Lov5vfMtbK3/tficYgQ2uVRvmYoNovFDpDGypizuZCq\nJudx4kyfktkq/KJGB8QTTRqiYrUa1QCNtCy6K0tHm4QyjXl62GJK2yaqaFeg\nB1Q+n5k/5hxsWVD3qcH8jF4KRUDsTymRo3tUyCNBd8oPcXf1IwDKje/m/v3A\nX2BSjUKbCpnwCUlSC1StgfvYjC+WONQFO9+uuVpQWfzHfGkd1q8NUqV4lq4K\np/EnxXctsTLWnKv3truQ69WZgxZM7100+3tG085mlWkEjpwv/dh/mJj2pHA4\nUKmm9DqFwRRLQYS4eeqJwCTYQ1F6dQHVD6W1bl/8gUwjhjR+yBtl1npzAR/R\nEzOl7O19DLOQNhokYHczgzH2Rsb0Q1gUPPAhuowvEOJiESgzH+dRoosJ9wC0\nkV4yXeY5jmhHxegKABTzlpiLi3rjjsOgY8rySlzqoe5td3Hc4F7HfeKdYUGc\ndces\r\n=0VY5\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]], "presets": [["@babel/env", {"targets": {"node": "6"}}]]}, "engines": {"node": ">= 6"}, "gitHead": "e48e5586bf0a4f9d577310b01cac395965877cbc", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.6.0", "prettier": "^1.15.1", "@babel/cli": "^7.1.5", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.5", "@babel/preset-env": "^7.1.5", "babel-plugin-trace": "^1.1.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.1_1541586739472_0.3313737259786691", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "yaml", "version": "1.0.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "77941a457090e17b8ca65b53322e68a050e090d4", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.2.tgz", "fileCount": 54, "integrity": "sha512-vPmXtExk6AV7F5l2N5jW4LhbhZZPPN+xOK8x/+EgSXsXoawuo19Iqa+9nYMwrltsi6nl0rMKiROA/SAfG2OgUw==", "signatures": [{"sig": "MEQCIAl8D5SglAJR30E1dYvPJC5mQ8Jc87o2StXFhedlNcSxAiBscCvK4RnKbTQrQDb2xRljUxBzaM1X3Jv14bHL2y/Ntw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7VAjCRA9TVsSAnZWagAAuLsQAJlW708/HFhWiqTEqX2F\nyF/2TrWxVHxq4Pv1uLIKtJKb+ds7LHdX0rSKzQTLCQ8ry5HOZxFPiE/HnP7E\nMw86Xp33RVwTkS6bqWppg0M4fj5YEEGYLKMx+1bwkDIJDk/0THAW4ykTRVsJ\nE3mYYTuOSTHLeNZfgNAEGyDPBYr6DUmIwi6XVY4suo29TZmPzJtdIHg2sl0+\ntuQFqkR92l31Nk7V8UGvudNf2WIGaq6qIAB+IZMeFaDpVfvTuwtVaxyoSZgV\nNusQot8EDDTUZLbnDFa61fTkdjMA5PT4xDFmRbf/JlYKen0BSJtNFI9OOUIC\neOlCnFL0wT7BEF9JMkPZxizui5gadre5Vpr8OmRJA+ll55hsscD9yO0FKCFU\nBA+FIjC3yjZ+xSowwNTvjov8MONuaHIuAXe4sUIR6kN3nX6crAl6U6TZ7nrm\nLzAktGR3MolqXYqFXi4s1bQa4j4wibo3mP82NYbaI6wayB6GXTql6M/zT/x3\nqOVnVaSZ0xQXg8gOgBFath7AMucugXL2RUmAX8Z830/jGfMNtigapjSk81Z6\ntZIELlIwK1RtERhLyOjTEVuXJjXEbbFzvIx2/S4Vn+iCHnffgYKlUFXZHQ7S\nYHZ3GkYUk/jHUNRsOI1Xq9yMmIUUoxfrxAeLSUoIO+k2E4wghtVhMynHeBfQ\nUV/y\r\n=1VhF\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]], "presets": [["@babel/env", {"targets": {"node": "6"}}]]}, "engines": {"node": ">= 6"}, "gitHead": "06195c068fc7a6fa5da3e6061309250c611f403a", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.6.0", "prettier": "^1.15.2", "@babel/cli": "^7.1.5", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.6", "@babel/preset-env": "^7.1.6", "babel-plugin-trace": "^1.1.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.2_1542279202769_0.6724954608891509", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "yaml", "version": "1.0.3", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.0.3", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "6eed55f60edb3ac1402283fba94c91b6a0a026f7", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.0.3.tgz", "fileCount": 54, "integrity": "sha512-GDYiMkQRa1894pgIQTIK5CGEpcjbozJb0IcSJ3WYoGyA9kZupfpRQxpSYFYfFHFKxlkaN4GgnhEF88J/VelC5A==", "signatures": [{"sig": "MEUCIQDg8Ol6Vhc19rHNK10gMFf/iRstEf3tDcyR39B0UrtUggIgSPlNJ25RPML+uBOFlH50HkB+8xbUEDbWqqxqFqLCsk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/wOxCRA9TVsSAnZWagAApdYP/1RvMe1Kdm6v/BOt45VE\nHvS3m3vm7UdIljgvl6+cCrWv7CvzM5q40xRdAgG8NAucvJfSWLxNnSTZAPpW\nnq6g70prNe3i5e0DmpgCaxYCtIMjMcatfzymCy2ZJlvgEFUe0x1LSrG9kBKa\nk1VQla99IRfe7wOGbJVK2ocK8cPOvU+fQmoS6x1l6AJphUEP5snAlXe2FFEV\nojMf9bH6HeXZgt0dQ+hsILxUysRCfdxEg0HKwT4xX4AlNlj/C/YWE8LF/GDj\nCui87ottR8DHYlqf/DYQ/SFhcgATP1/ewWqAtt1m3Ur46AneATegqcGHnybG\nOKpBEAd/RRrIxU8RJWtGKSvaRSWza4OLe5Oi5C+ddYJAU/xDn1lprkJslSUA\nm6HjotvFHro1C2Q+HVT6wN86C4bZsGIKI1tbm7RBh3KKzJfn5gIfZ01mRFxz\nolzVwJsZX1ruqJRTWmAyqLMsYhHCWGet0CZbd8cdYZ+0JfvSaGsBqp8BfQMI\n1YXREL8MFb4K+63JTz7sZtDYPWKXZBYvbRspi5k82WwJBaL09kLjxHWAKcTL\nlVyVB+h+6+CFbBvh+dZef/oS+hq6VsVhntAzn55vK433K7FM1EVBYPMbTGLY\naJliRyecTHmyx0kskvJgEomvkNFAJIPgCVZMdBYrnNHf9xqlQogqM7ukALDA\nGdxR\r\n=xD3K\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]], "presets": [["@babel/env", {"targets": {"node": "6"}}]]}, "engines": {"node": ">= 6"}, "gitHead": "f4e7c1f8d3ece09da5725bbe30ccaf4010095a9d", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.6.0", "prettier": "^1.15.2", "@babel/cli": "^7.1.5", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.6", "@babel/preset-env": "^7.1.6", "babel-plugin-trace": "^1.1.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.0.3_1543439281126_0.8664904182340489", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "yaml", "version": "1.1.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.1.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "6168f2beb28e80ee88f82efcb6da01d8e496ba30", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.1.0.tgz", "fileCount": 55, "integrity": "sha512-MD5Ptelnnjfj/w4UqdNguD9Ipzm3ws6bNiYkGkl4lkfGMU1V7QYyHkRh28EiHPdprSvV+xAhvLJ6ifyALYw7wA==", "signatures": [{"sig": "MEYCIQDI3H4oyaK4YAJkxdOKKjlyhT8ZO1XrXqxiZu73BVCxXwIhAIM1BRVOzwcclRiIiNF0f3/dJT1FoOyz4Cd3U+jynxN0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171951, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcEEF5CRA9TVsSAnZWagAAttwP/3YK33WCHBcMwwzGnq7x\nBduJuC5cd6eOoKbbVARXYBY1lZewQyHacNrz/8+gR5e7yNDRwxCEXnqdTwJU\nLdBSF4BdsKC/b8VVKaz03eHRtQAoGK1EgNVed0yNg7bgkLcBPHY/+ztLZ4Nb\n4S6iGxZNoRYZxfQ1vlYfQW4RJO3t3vdJ7RKOzRASJ3DC7Mcsi13yydQFxjM1\ntYUdb7vdQe0NRa8iV78DtYjQF7mx3CvNcUukrpy+W880kUk0tdWatKjEPV4S\nCPXReWdmjcMKfAR1P34TO2EpFuEQnMkii3ltrbbNJCgg/jnbOL9TBBpVXUEv\nPQbE4nfGIuuPBQAyvVoH6gxmwmH5MxlDHT+WrQ1feqJP9bfYC4fifSATwdmB\negZkMZSc+4WZulkKam15DxSyh9BEwevJVhvNEyVk1KrsgIwuFavRptbMBzNq\nz/xAiu/ZpOA7VsDhh7I3hkgY8+b9oUtq+dvskc7C9qBMmc3KTgQVzPBD4NJB\n7Mnp1/+XOlupfQ7KY2GNAMw+g4HxJx/JbN0FyxaVJly6h/zC7g3DZqIilDuB\nrIXSZgGUj3zXl8fjz8mAZJlLfA2zVuLFFNMhTKrJaJW1tpy5kQ5M8eKIBG8M\nWppI5waYDYBzCkwkJo57GTAq0QWyantMcHHisC7d7zSwjZ/T2tJ0gBj9DPiI\nOzf8\r\n=nA5e\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]], "presets": [["@babel/env", {"targets": {"node": "6"}}]]}, "engines": {"node": ">= 6"}, "gitHead": "31a2993c2693b443a4cab995c762f10c2e69506c", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.6.0", "prettier": "^1.15.3", "@babel/cli": "^7.2.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.2.0", "@babel/preset-env": "^7.2.0", "babel-plugin-trace": "^1.1.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.1.0_1544569208499_0.9677343781674859", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "yaml", "version": "1.2.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.2.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "aa105d0794cdfeb1db5ec9f3a9efe73145dd817d", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.2.0.tgz", "fileCount": 62, "integrity": "sha512-nKNUuxmvbrRDQbPCVqUDvjgnOvpxWFRK6O4hO57Q3ie07CmTkGrcpoFHEN7PUTkvhhJD23CR2WpKgPyIVoj5zg==", "signatures": [{"sig": "MEQCIFA93O3B9OTzPi9CAX6c0Oq4za/9FSZW2n1M/hPkH/O/AiARbYX/1I5Q7Z0BWibgBRfRaSHMqoDZ9Lm1Y4gPH53hkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 204782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcKzQRCRA9TVsSAnZWagAArowP/2KsMqaNA6gUe5gev0Lv\nY4V3D0CQD/GRMINIHzORVQQqYNSirKSs27yifrO6pNDQYhAitcWr7cWS/ICn\nVR88ElJCh6VI16vwMYTWm6qx0yADCAkhwBkafSn2n2dPZycx1fy8tVL413EA\nPo3jtMaVB5UnwmwNhuD7ZcPg9Tpn/Wm3xomrxBF2eS9GDdhbGuTYGKTadoa3\nsvIg35CfaUi/DGhGnjAMrAwoNJpkSaKHZptDSPvW8YEalRcp0aW2LcZhQYHl\nu0aFrQFUCUx+K7qDzjtFWDsmcwy22LOKRgWI1sjkbnHMCfPfsvQN5s8mL0K2\ndWJioc/tYuh4T1t6/fFCnTFQXbUgUa8+VFJWvr2p13yRVtGaj5jza6Sw8p6T\ndN1bfQ603xF3G+lqbuDAhHoDfEYTC2XhrZzVgJyXv8F1/wMM2DPKH7TZK23e\nU54e5ZFO/GnfJnTHcdQL79vwslq7FHh1Gtj0k6KuNgeuoXw8UV/J0sO8/dEL\n14R/solJqdhgIzbDBkw0VhriSQk2+xXWM1kPxgJJ8Sjd4t7MDR0kh4GQe7Qt\nHcEHrITtILo3Pe2owPyto/OHY83jGPeOHjAgJlaTIXo/nmsAAoqlAjiYNA7S\nNIzULL6kPlcKLTZcbK4kf1pKwwOVS6K0qZmQiCAg3fMMLiKaWVxXsRe9EWvd\niETJ\r\n=Q5KY\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]], "presets": [["@babel/env", {"targets": {"node": "6"}}]]}, "engines": {"node": ">= 6"}, "gitHead": "b6b760920c9acb6eaf24d2f39ec2a246c7cf6eb5", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {}, "eslintConfig": {"env": {"node": true}, "root": true, "rules": {"eqeqeq": ["error", "always", {"null": "ignore"}], "no-var": "error", "camelcase": "error", "prefer-const": ["warn", {"destructuring": "all"}], "no-control-regex": 0, "no-unused-labels": 0, "consistent-return": "error", "no-implicit-globals": "error", "array-callback-return": "error", "no-constant-condition": ["error", {"checkLoops": false}], "no-template-curly-in-string": "warn"}, "parser": "babel-es<PERSON>", "extends": ["eslint:recommended", "prettier"], "overrides": [{"env": {"es6": true, "node": false}, "files": ["src/**/*.js"]}, {"env": {"es6": true, "jest": true}, "files": ["__tests__/*.js", "__tests__/cst/*.js"], "rules": {"camelcase": 0}}]}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.6.0", "eslint": "^5.11.1", "prettier": "^1.15.3", "@babel/cli": "^7.2.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "fast-check": "^1.9.1", "@babel/core": "^7.2.2", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.2.3", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^3.3.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.2.0_1546335248440_0.4342656343825382", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "yaml", "version": "1.2.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.2.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "7057ee224c96e7dcd591a83a871ad89d3df1c6ed", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.2.1.tgz", "fileCount": 61, "integrity": "sha512-LuOeoZc6LYguU9H/XCbrPfuDpCGJVf+o/r9uLCGnsHiHk/8Cr7IVeXOilO8yK0XA8QJPlX22KVofINt8xhdwqg==", "signatures": [{"sig": "MEUCIQCdUqWSUxAldyCLL3nZS7l3aagfnd8Z69LYXDAQEppIDwIgCp7SIPbEStCNAh742qCz1zqV+8YC6+98hyGh12GZsVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQFXKCRA9TVsSAnZWagAA6MUP/3hHg2gf8zF4CH9jRha5\nZyYkwqKo9Uhu+ogdjyPHYAo7q9lv4XzHPV88OzeyV2whIJ+8MVUHAVELYUCy\nmzJ2Co42Zbeiy1A5QZhODjcbuBy72naBsC8Ync8+YZzjzjkPjxrpCXM1iP37\n3kQDOxBvqoy2WYPJHkYwN9h+qBR3nc4CGW4dYSGMPbK0FNE0Na9gZQR8YqwU\nHO5E5s7pyxKdmtsqNyg4TkxNX1syUwKapa2DGDBhXzd9WkmMWoy8kAF5XRnk\nGlHoUXsWDGwQKDfY+dfxqm5lux12bDYV2qkMVGEnMOuxsMbcz76MchIs4t13\nXVYxfGjYq5lkM4gdtxgasM8SmEaFDf9zskqrRtHkeEXWQ4g15e4G9tailnCz\n07B0dslonp/oZNoxLifjRQnPPCRBAdYUE4AqbyAJcbP7qWwS1tClGB8ogB/C\nhDJl3bAtRQBeqwUBXFcd5TxsVDp12amZ3PVJkJ7AGUaahKwiNdeU7cUZ0b3y\nDh2DvzWQUAE2iSp3d0TqdfNd1ZAbq9Qjlbg/XaTe2MroteS+IyOQxs5lW/xI\nubLCKmnDXNJomh3jYKhVFWDCwA2yGxOF08w6nN0ILcRgkPK8/8dCjcvMQHMq\n1/qTPg8b8Dxa2x8owuRlJsalC658WKWlZmfN0y9YCmx5D0DVRopZPpXs878s\nW4N4\r\n=IUCl\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testPathIgnorePatterns": ["__tests__/common", "__tests__/cst/common"]}, "main": "dist/index.js", "babel": {"plugins": ["@babel/plugin-proposal-class-properties", ["babel-plugin-trace", {"strip": true}], ["babel-plugin-add-module-exports", {"addDefaultProperty": true}]], "presets": [["@babel/env", {"targets": {"node": "6"}}]]}, "engines": {"node": ">= 6"}, "gitHead": "978a42a4d8184bbdddd3ff4cccff7f8cacf3c062", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "babel src/ --out-dir dist/", "prettier": "prettier --write \"{src,__tests__}/**/*.js\"", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "docs:install": "cd docs/ && bundle install", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {}, "eslintConfig": {"env": {"node": true}, "root": true, "rules": {"eqeqeq": ["error", "always", {"null": "ignore"}], "no-var": "error", "camelcase": "error", "prefer-const": ["warn", {"destructuring": "all"}], "no-control-regex": 0, "no-unused-labels": 0, "consistent-return": "error", "no-implicit-globals": "error", "array-callback-return": "error", "no-constant-condition": ["error", {"checkLoops": false}], "no-template-curly-in-string": "warn"}, "parser": "babel-es<PERSON>", "extends": ["eslint:recommended", "prettier"], "overrides": [{"env": {"es6": true, "node": false}, "files": ["src/**/*.js"]}, {"env": {"es6": true, "jest": true}, "files": ["__tests__/*.js", "__tests__/cst/*.js"], "rules": {"camelcase": 0}}]}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.6.0", "eslint": "^5.11.1", "prettier": "^1.15.3", "@babel/cli": "^7.2.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "fast-check": "^1.9.1", "@babel/core": "^7.2.2", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.2.3", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^3.3.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.2.1_1547720138016_0.5830178511833091", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "yaml", "version": "1.3.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.3.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "61abb4269417185410d91f78a45cdd1f1cf3a0a5", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.3.0.tgz", "fileCount": 124, "integrity": "sha512-13t7beeiJCFWrZZf+a5WLRWVdz5C6oEXdfwOncnAynHr5fMqZg/njVUZjPmOskq7wn6CwS1RBRNavFvLok7vWQ==", "signatures": [{"sig": "MEYCIQCdtQIeMRyrYxdh2IRCPzKZ19Ku9ThuaUHXD9nlNFz04QIhALaF/JUp03zXItIbiKLHr4o7u48BgdUol3bZJPQBxhMU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTWziCRA9TVsSAnZWagAAUcAQAJ4G30oE8rWb6wLaF2CA\nM21Tww5LJqON9kHIylRTXeNzPQpGnQDBmAXguZJLoL+xzXiQPDRqNYQSdxCv\nvjImaEKGVBhC51RHSx+hDXsEbiE1D1FhiJd5hIvN6CBNvB2nPdDOWWWhxLWd\nmfZmLeMI2PTHk/zogFLZr9D/AYVkXDxtPCcQLRRaTyaUJ7PEBSDAKT+xtAU5\nyaqjCAN8yTjUi4Nkx9u43h8Rt0LMLRNV6rOeR/gEnZz9Q/Ao4SKWpKon1MMk\nR5l1QeuEewQTqCufNHeljRfYKiVTQNESUs+SF9z+PCh3jIqCZlCQ598nwYuR\nL7abZcoDVqJysfEtx8e64azg7N0fvz+gyLRDL9+NfAHuQhFRQLTvPU0ihBxV\nDJVwyGJU/oP1Co7Jqnz7U1J47bYqpDJO/UEkxbEkI0hIFWiae3bpvNs7tUv7\nSMeMRoJclPlYv3DUS6fkKxC1CeDpZ8Xj82kxE+B+kNWq22g7GplbMRHPy8Jn\nZ39z6WkGKB2S+DhEPv017a5gRe3xdbVL9fpgePcu++8DVB2ZpXdEtOaGdJKx\nr1Z2lEXQjPwNt21LsGPobgDn0ckHv+X4zpwbu0C2eYz2xbyPHFtJtpajZWkE\nnuNc7jDTRdq2EICuAIeZWgYkp8iyNZ8rRIfzOwEtDOgSKSi5vBQWO+KYxRJM\nmXCB\r\n=TeQk\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./dist/index.js", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./dist/index.js": "./browser/dist/index.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "gitHead": "2c2fbe89631bcba08012a3b0cc411f0982477862", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "10.11.0", "browserslist": "> 0.5%, not dead", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.0.0", "eslint": "^5.12.1", "cpy-cli": "^2.0.0", "prettier": "^1.16.1", "@babel/cli": "^7.2.3", "babel-jest": "^24.0.0", "fast-check": "^1.10.0", "@babel/core": "^7.2.2", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.3.1", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^4.0.0", "@babel/plugin-transform-runtime": "^7.2.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.3.0_1548578017342_0.08806500083654667", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "yaml", "version": "1.3.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.3.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "058453683f6e459bfa1854ae6110b68c72438c8e", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.3.1.tgz", "fileCount": 124, "integrity": "sha512-2AG732tOqaovhv9HNm39BjaEnEDaHssbAxh4atw521ohOqL4X8JHjcbyf2W+dYSVl6zG8/Ayd1a1J36k8eJQZw==", "signatures": [{"sig": "MEQCIDtbpRtYF+L2paMkmfNOJ2+xIukEyB8qPYDMcZ2G7PB8AiBqxL0bF+O83hjPnlPsZlKGD+VTwcgQTc80WwnVoFQkGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTZk0CRA9TVsSAnZWagAA5k8P/A40AHnOroYxB6R4ohGZ\nkyICb9aI3OFLSWm9IYE35xS4di6oFO1Uosyr74BO2rxPZyZiiy+BdVPNAMbG\nEcLFQSe3W12jrjP1p7l4JIDgJD+kUJM+VC9WBMx0tLcB0Ijnfeeuxb6lgJZ3\nTf+nQ32YnokOYJeBHMr3ibUKKPdBjdeyN1fKwew/R8mdQ6w0MZ/TGnaPFikJ\nTW3QB79P7kN4pWpBd7OrZy3Zx0T+El6Ep4+WkRinkwidgL6g886y6CD1UNPd\n1a4TiZCAH8XUEft18bMaQnCrc3cKG4uMghQC9d2E1IaEc180RowcfHZZ5I3H\no7xnABMUYsz6nUD+IF5oOQxv7b5uJ1ejzv+Mwaq/LZLZ7VJ/BcVXT0idGxSH\nOQ6TFoGw2Oy8TSblRZ+pA2CcAIXM/Aqn45DYo/wE9BkbZiZ6NccpD6UdxSZA\ngHAV9Nw0XZgnwOEZp7JxuVPF4jt0/54Gvb/8eB+BLwr547iVuSCnpR+txgr/\nUf83/i4mO/lF9AsqaUNs5UGVAfT/pvNGZMBHWwuHHOmJ9ZZkmz+CMMfJPCS/\n4IPAfr3bn41vvsaSSZOyR8df8IHfER+ItLWBvvCTYAs3G5yeGL5qrz9nbbwN\nfyvPRw8vQIcQIYUhgjcnabYT9J7rcS4s3dAMgOBcZ/MjYTkZkhClB63bKlNA\ne93p\r\n=x1Mk\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./dist/index.js", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./dist/index.js": "./browser/dist/index.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "gitHead": "00a411df872b7e80ca9fcfbe86ed6afb0fd05451", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "10.11.0", "browserslist": "> 0.5%, not dead", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.0.0", "eslint": "^5.12.1", "cpy-cli": "^2.0.0", "prettier": "^1.16.1", "@babel/cli": "^7.2.3", "babel-jest": "^24.0.0", "fast-check": "^1.10.0", "@babel/core": "^7.2.2", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.3.1", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^4.0.0", "@babel/plugin-transform-runtime": "^7.2.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.3.1_1548589363639_0.8162751298100426", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "yaml", "version": "1.3.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.3.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "3de83fafed3799fb0b0029c12f80035bc7611303", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.3.2.tgz", "fileCount": 123, "integrity": "sha512-ZZZIdcApMRcAez37EVrtCim+8JUESX0zRcsv+HMfatIX79cX22CAnVkxDrZhAmzsnka2nb/mvaTybzDYcnrIew==", "signatures": [{"sig": "MEUCIQDBQuV31DYmA9+UaXNPk1FUpI2rCK2jOA5WsS1f8qHdUwIgN94ALSHqYVTSxzHePuGZeKjGOBePVw3toFFmUdVGjKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcXfRPCRA9TVsSAnZWagAA2R0P/i092ab+Y52/BlQAqLBw\nZSZ8rt0rh4hCD2sCt3XiO1EX5emF51f0VSv9fe2PMRr3OxB+F/Mk59ctM/SL\nmzn/88rIJW6GhbAMrYds13uptBYkRPSJqL84c24ZyVbKgU4QXJobCO+w7ajl\nyXTAQYBe/HeZxcI0uSuTWbrv+qMtsQ/L3tvnSgIt06ogMX8U6XXsnkyJf1Ff\nQXXEiqmwynuPe+m0ZW4XQ4Nba5lAwtJ/nsBNeCgtnlk9gSbLy4CgyfjubAqB\nTd+bKghu3YHkVaMB7ZLDytSlqkSPWEBlPxTbBg2Wd4CRsPb+fd17MprAtHj9\ne5E8gI1PKIJ5OVGioYQ48vVsJd0Jg7pk+kP65lcK9h/Ydn9dIOXMj9rC2uII\nKfFKGD2PvKKcWoC8NMogBJJG2xIHJRCFHlxvVV/jUwSWu1EVOsEIXqcmDsp7\nnSXuyHJrEI4GFazgU0dPy8Jenx2dRWWyJXyZo7diRIzgH0EQVYexStUPfbk6\nEW3r22blIc6gNB/v42Ic3eR2W81GOc9pzLDagXii8KTZeQSL+omAfYgaWYAu\nUabm8vdwaTpF8duhC9eneNqELOqXuMrcxgkDYACUQIf/5bFlCT02IgVrsnHR\nGWSzAnskhhLs/PnaziUgzjouJpQ/4mztlXR7Y8uYj3/OqsyhmtRC0XAD1F/V\nlScY\r\n=KmZc\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./dist/index.js", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./dist/index.js": "./browser/dist/index.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "gitHead": "80e13b723f639ba4fce6e211e50ae88e887c69d8", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "11.9.0", "browserslist": "> 0.5%, not dead", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.0.0", "eslint": "^5.12.1", "cpy-cli": "^2.0.0", "prettier": "^1.16.1", "@babel/cli": "^7.2.3", "babel-jest": "^24.0.0", "fast-check": "^1.10.0", "@babel/core": "^7.2.2", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.3.1", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^4.0.0", "@babel/plugin-transform-runtime": "^7.2.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.3.2_1549661262361_0.7309622508498113", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "yaml", "version": "1.4.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.4.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "b729a3ef7e35bdc5ece8f28900e20a9b41510fc3", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.4.0.tgz", "fileCount": 123, "integrity": "sha512-rzU83hGJrNgyT7OE2mP/SILeZxEMRJ0mza0n4KFtkNL1aXUZ79ZgZ5pIH56yT6LiqujcAs/Rqzp0ApvvNYfUfw==", "signatures": [{"sig": "MEQCIHmQjJ0PcaPA9v0THW2je14noMlMWI7JlRkWTKG1t7woAiAzusFb48DYIrxcdrkiYrKcvodx9GgZ/3g4uQfp3eO2xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 447815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgXWECRA9TVsSAnZWagAAYZEP/3+nifvf8S+g75P0BmhS\nVkzPwczlU199tUKsBNh7XRRXJR39uWZ1B7VCXPAiJMms/iJS21vVDn28milc\nWGWcVp4fm0GQ+BqIAiC2AY8sfsbH5s7jT6Db6D3k5/YjMF0ClWi9ZcYabR+v\nGE98XpBr/5rj7A0b1BygcWdOH8cHMILGT/hUcwY/U03Mxo9LoDooOVo3Mrov\nWFNSkRqO5s5YQO7xjg0Qgv+osyEX39Is7J1TpfSRL4YN+4mq9A5i6oajAnEF\n+IrJZfQV49yaxv/MQhDl1sY1m45cK400POkMX2l7WZzHNcd6FmQ/Wo1xsahw\nf/PZ4wgkd7lBOrjnhXwwiFzJAgTAClc6dsVox8EAkIw+JwSmBhhMLLsaDTSm\ntEAqFyr51J1O5Dz4KuHDxRN0+CK+xAdMKN+e8v+y6HxQz2w/F3Tq1gJjydnu\nM6h0Ky13sLUgJuXdhvVPVUA6PF1OKcwMAUpJs9lIvDciQuRNM0+vBQzgyGl4\nnoYAt4z2t+1nL+iKowA4+R9Y/Zg59IJaAJRDSPyk9AdgfXS6Fa7bma6MVKi/\nzDR8XCJU8MlgPWJm5FZrrDiZdJ0hV7TOWMhUX1R6iJrm7W+IDvF/8i6CvKJl\njsQQynb2lSdo9EZrt1T7y0tM99hfJtrYHX8QubSdOs6kPIdvGRExoqcH+mWZ\nHHr+\r\n=/Wbn\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./dist/index.js", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./dist/index.js": "./browser/dist/index.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "gitHead": "d677a97aaa86aadf35bf3793cde8880d97d9f059", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "11.9.0", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.3.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.3.0", "eslint": "^5.15.1", "cpy-cli": "^2.0.0", "prettier": "^1.16.4", "@babel/cli": "^7.2.3", "babel-jest": "^24.3.0", "fast-check": "^1.12.0", "@babel/core": "^7.3.4", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.3.4", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^4.1.0", "@babel/plugin-transform-runtime": "^7.3.4", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.4.0_1551988099594_0.1660201266175827", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "yaml", "version": "1.5.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.5.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "1d879dd069ce1036041c23229cb5d7ef47a58ec6", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.5.0.tgz", "fileCount": 141, "integrity": "sha512-nKxSWOa7vxAP2pikrGxbkZsG/garQseRiLn9mIDjzwoQsyVy7ZWIpLoARejnINGGLA4fttuzRFFNxxbsztdJgw==", "signatures": [{"sig": "MEYCIQD5rjDgaqDiEn6L6vyKlLDUAkq2TLY06pCwoNS7gW1qzwIhANTH5OEwzL2AEl+E4/GAiV+cDhC9yGT94OGEMKPw8gep", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 456220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqExRCRA9TVsSAnZWagAACSMP/0OXfES4sdH8BD+ZdlLH\nDSKw7phpzLym1nDb+CRj5PHeqaQUFqYgqbGuvT1ht2BuVdjgkYUlJQVD1WJz\nzob5rC1Gt3Anodlusp5e8phbzPwueJCBLlOebW7MyltgbN2H178dW98LOnWb\nQOPGW7cnkQ4YPL+yngvuri6rYw3MwfWRLrNpCWVl5K99CsAQj92M2vbEA9vD\nw3K9SXrYkdYEgmQmibi4rO7mp8/odzmbocacKyrrj5M9L1B7EOyFz04iB+Rl\n/pp8sTB23JHvEQZ2kb9ciJ+P8jw8Kwq+8qnOlGiY6Ce5t4k9SNKEkj1QDnWA\nAFQXBwDXDPVQRa/KZ5CZW7ZvoODPRHulWfke/EFI7fk9lvao007ufQUZbCSY\n9YaFdvWfHUOe9BTRmLxrxrXF0y+o9Ac4euqK9hkaA0dNIUuKU3jo7buTEwqA\npu6eD83779w/Ktaj6xQDRYdmVx1DtjOCe//SWrDhV2Jns0I9UCkOPoU8eAnK\nnELqwbx+Sk4Ei8pu1DVZFAUbbsPxJukXiZpB+ShjBTXx2IP9lt/51MHfwgpH\noqCv4Q81XhqpTodRGA5Mta7bgTVowrq/4XMhWtFCQj20U+DDFiMz0vqcQqdv\nhB5rdpGXNVqoUvE4oh0SYVfgxfZNdskhrrBc8AQdU3nqJc9NaF2zpWDqhACH\ntF7X\r\n=ZJTo\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "gitHead": "a9cea169f5df4ce91bc9b385bf6cce06c7ff2daf", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "11.9.0", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.7.1", "eslint": "^5.16.0", "cpy-cli": "^2.0.0", "prettier": "^1.16.4", "@babel/cli": "^7.4.3", "babel-jest": "^24.7.1", "fast-check": "^1.13.0", "@babel/core": "^7.4.3", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.3", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "@babel/plugin-transform-runtime": "^7.4.3", "@babel/plugin-proposal-class-properties": "^7.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.5.0_1554533457198_0.9067313453742425", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "yaml", "version": "1.5.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.5.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "e8201678064fbcfef6afe4122ef802573b6cade8", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.5.1.tgz", "fileCount": 141, "integrity": "sha512-btfJvMOgVthGZSgHBMrDkLuQu4YxOycw6kwuC67cUEOKJmmNozjIa02eKvuSq7usqqqpwwCvflGTF6JcDvSudw==", "signatures": [{"sig": "MEQCIE8ZFdztAMHRsMmoHkV6/7wW42E4VqXU0xnSvZMZVBCsAiB53ukMnFvmCwG7WCA1BXLVdvmDRmDl1GMfB8V5T5dOgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczuGwCRA9TVsSAnZWagAApUYP/13wwPigQS/2VDJaIIS7\nqkERbXqczIB8JpWPhlZsU72rYo88R2XqPlvOnavyfQauHTJenVzFEXpULetg\nqIo3lbjFRdGoSDRUIEs0DH6MVIiRF25siDKxlaUepP7RikJ6ATdOZU9/2tVq\nPCAw8zuMolIhB0IY75W5ACy8/z3R8iwwTZa1f7gAYtbgvdoloqP4r1S8zLMA\n4VXdVotTSOgjVd8zkEaMi9X+UB9aAlTrb4YDdIAd/MhCDFlgLfMyA3mBKUlV\nYcFgjYWLut9f2IWrT4fky1pu7/PxWMwoo5XC676VsSOIVzZ5hgYeg9wJkPme\n9TMM0C+wEruHkVvjHLAJvMyCcMQ2ZVsWP1OzJYoYPBYQ3sPJM2ZYzQ1mecOZ\nGsUVycLQDOtMlzQEwehLy3A+eziA8khAT7eaIiW5r8vwKuK8WyS7AA38NQYZ\nF9V6ZXFsexPo26ZGoFS9apdOARVWJY+uTSHmi6+qXNXf1elVFjRjoVLokPQM\nbLMBggM3wXfMg/X8cIxNpwDCD8ryBHppbthH4HRA6hpXci6pNsTWZkZUzlRh\nyEVVK1WwBmZySISKBcXyHyz1Kpc1tSunJtzTEuwOB7kh5TTw6GzPUF0gDnC0\nvYjlxjujkI9yhBvZ/M3ElIM3kvvbbK02qXvJqpujiOWgfwZZHH+vqFsEksU4\ncWP/\r\n=YAiG\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "gitHead": "9e1c4b96d62f0ada331d426889467d5628e8a4a3", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "11.9.0", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.4.4"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^5.16.0", "cpy-cli": "^2.0.0", "prettier": "^1.17.0", "@babel/cli": "^7.4.4", "babel-jest": "^24.8.0", "fast-check": "^1.14.0", "@babel/core": "^7.4.4", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.4", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/plugin-proposal-class-properties": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.5.1_1557062063993_0.5923152821557844", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "yaml", "version": "1.6.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.6.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "d8a985cfb26086dd73f91c637f6e6bc909fddd3c", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.6.0.tgz", "fileCount": 141, "integrity": "sha512-iZfse3lwrJRoSlfs/9KQ9iIXxs9++RvBFVzAqbbBiFT+giYtyanevreF9r61ZTbGMgWQBxAua3FzJiniiJXWWw==", "signatures": [{"sig": "MEUCIQCYKQUriRMTGC8OS5A2n/kTmOhWva4nJGDdTS0jzO42rQIgFse1ZZx+fivlk11FLH8Ci9mcR2oLyLALDECJD7PsCEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460358, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5qk6CRA9TVsSAnZWagAAAVUQAJUokcLphC69coZOKctz\nJj0GASyFPL1gSwqu3KToUT9isV/fkCA250VKYKiHn1m+bPkZ4SUTNxJN/Mxv\nG/QURPJHvKej/HunlqfEbEswtFT+mFBpP3bu9+o3UQWB8PCZDkzuNCpv8+WQ\nZunFeLh9dOSEqNUoUMVueprhBHDD3yAMQk1YY497F+MFkcHTa0v6Kh8dEFw4\n1Q7cznWGqWgjE5HycobRsQgsqp+BEDof6lHCMF6JkPApch0rUEe+mxtI6COA\nmo3XmKtUcGnoLHpktB19H2JE8inMlRFW1GSJlZSqBAdbFytbFIu76q4Bz5IF\njtH3VtaN8acpROcmxIbPu01id5y0pAdHYAE/Gmek/gt4DITAe5et7EM+BcRO\nIgjqA1H3OkNoQhBq2bbBcOQXRczESHZd2egRCYZmruBOcHUQCjaIC4yFuIQj\n0ORwCrK3ufq45OFu9mW3awTct1DInILMg+tXWHb0umIn2im8XCW/XWR1ruIO\nbQDjQJTTTHmTA1yCNe6F8KMOK7Uk8tleTlaTo9MtjuB+GxI8xUvWvwIxSBIk\nSOzaxlKmAwd20vJCTfOgQZFhPMW5bhSlJUjRJoDNK9ac0R7SfqgKavC6bbIq\nU9XAa2TTc5CMw5jFcsFwwKcYiaPwxjH0ZeSii3H/xXdSbCM5x6O2ow1bC2On\nNDWs\r\n=gZTm\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "gitHead": "f8d35f4b01f97ac42b12f08f7eb5db80f48e2ed4", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "11.9.0", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.4.5"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^5.16.0", "cpy-cli": "^2.0.0", "prettier": "^1.17.1", "@babel/cli": "^7.4.4", "babel-jest": "^24.8.0", "fast-check": "^1.15.1", "@babel/core": "^7.4.5", "common-tags": "^1.8.0", "babel-eslint": "^10.0.1", "@babel/preset-env": "^7.4.5", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^4.3.0", "eslint-plugin-prettier": "^3.1.0", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/plugin-proposal-class-properties": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.6.0_1558620474283_0.07865803018687756", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "yaml", "version": "1.7.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.7.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "b4cddb83490051e6c4b6ffe2bb08221c23f4c6cf", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.7.0.tgz", "fileCount": 145, "integrity": "sha512-BEXCJKbbJmDzjuG4At0R4nHJKlP81hxoLQqUCaxzqZ8HHgjAlOXbiOHVCQ4YuQqO/rLR8HoQ6kxGkYJ3tlKIsg==", "signatures": [{"sig": "MEYCIQC8qID32QOMZuAL89lIkRhGW5yX2RMaDs1rNdFR0xKCGQIhAJxay6fhin3LyHoJwtVq7SU7/tqDNclmuW+3gCzkYRMI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 482017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdi2iCCRA9TVsSAnZWagAA7/8P/i+cXwbixWKgdX+GjD0H\nunedgROQaUKbYLZXE2Frla2iq5UR7g/wcefAU5b2gjzNpXcYUviXsJyk0itH\n+y0yQbvJLzyyrAi3KVmoVnZRZll2Np8UJXKDOVFAmATN5VBXqcHDO3wILETN\nTSqBQWwGGB0yG6OW8832uDmaUkXpZK9aj44+lOcfJN45o6loZ32T45iw+Vdh\nyTQKle/7idR27+dVt/uopg34lE8Fy/8ynFJMxmlJGIPGpS85H2qSeXmNm+n4\n2MMNpisn8NS/hukb7xW2LhQyR4PKJxmDoXSuUK6V99A/kR0DZoe66hww22C/\nylga929pXTUB9ILTaViU8yFSUHDXMTbw5/hRGDGkh1W0D0eqzbaGStvxV9zD\n5CvzNU1q99/6PudDnVE5bpoTIj6nb2Pa+wRvuQLZoQ3axbdUKGcPzlJMwDhK\nqUZTlaNYuZjyH/zxBi3Gq/qZ7gTVzPgNZXLHIgtWtvRv/NGHlFWkL8FP9zAi\nJgLT3kw2m4Qd6hgixRF3WhrczCmuD151ZP1rR0ybsTpM6lKVEPg5HFGmDCop\nnBuf01bl6jiyydayNHG9G7cWalBdO1ReulTyOzJCI9OjuR84ih+qQQtmKDqh\nlSgk9w76Nu/pb+G3NQZa52Y1czQzV6gjYy9xSrQa1rvTkiw1jwbj6ijJ0ad7\nyv8u\r\n=CjJ/\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "gitHead": "40acaa76b727b9f3dd028f283e3f10edd8321909", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "10.16.3", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.5.5"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "eslint": "^6.2.2", "cpy-cli": "^2.0.0", "prettier": "^1.18.2", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "fast-check": "^1.16.2", "@babel/core": "^7.5.5", "common-tags": "^1.8.0", "babel-eslint": "^10.0.3", "@babel/preset-env": "^7.5.5", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-prettier": "^3.1.0", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.7.0_1569417345729_0.8224342352563792", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "yaml", "version": "1.7.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.7.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "86db1b39a6434a7928d3750cbe08303a50a24d6b", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.7.1.tgz", "fileCount": 141, "integrity": "sha512-sR0mJ2C3LVBgMST+0zrrrsKSijbw64bfHmTt4nEXLZTZFyIAuUVARqA/LO5kaZav2OVQMaZ+5WRrZv7QjxG3uQ==", "signatures": [{"sig": "MEUCIQDQje0RwjI49MxfgyM+xWa3ZwgwpRrDydQyqaa1fH2ogQIgaUfMao1pnWX5rpug3V4dp+Dx6VwZmYdYLKvREJiK5Xk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 475655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmzIXCRA9TVsSAnZWagAA/wEP+wd7RVyAsMODjo1WDZux\na/9TYQ8eS+r2OUJWfe4GnoG+NXnJ1vNGWhWxv+d10SyzAZloppEEVoPic9DS\nWuLyqTuFaFbkyoQsAoN2TQtfBcwWcRkympqOGP50vqnirT+vQkIl7XwdULK+\n24mqLz19fvmAEj4Ci+Rq0mtxt0+1xTkLs6sZlESj1GUB43reuvUhuTAAMQE+\nG1xd2mz9loACDVhF5UZ/W20k58SNkU6KqqC0iN69g59GlUv7z2gvd0xTyvRM\nEnv1x7oiHXJ6mefGfYl63Kw3c+cBq7cqynUx/cPmvfLS+CDygRqA6NP71g7L\nDetsLM/bewlMds+mt0Cvn/wf/Ipwt9XYnybIKpqMpmY4Ddqd7W997z7kPHjJ\n+kTjt/H11tTWB6rML57oQAGRX8E7GqvfAQ6DR3waidJ/eQDYK+bKLLgA4G1W\nnvMf2uwusJTYmC52ja2HGfnK08jFz8MrFjKVxBf3psAmi1pnhvJd2Z2UdCmh\nBG9NiMfjSDrPeGvNCOn2YiguNmc/RXA40fOcVwKlgLHCSQ4C+sWGv+iZFXva\neSO91NH/r+nRZvEYkDW1jXiIPe3uRrQh5yIzZOyfCbaD4vT07oCCvjD9lnGs\nyfygisrdgXEMzYY+rNZAUIsdXrmue9gR7v8S+kpaAAjlFrtTfKevp7lSuNPF\nRhTA\r\n=w32l\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "gitHead": "ebc640a0b127caad8edb91a098dd410171fdd5fc", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "clean": "git clean -fdxe node_modules", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "10.16.3", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.5.5"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "eslint": "^6.2.2", "cpy-cli": "^2.0.0", "prettier": "^1.18.2", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "fast-check": "^1.16.2", "@babel/core": "^7.5.5", "common-tags": "^1.8.0", "babel-eslint": "^10.0.3", "@babel/preset-env": "^7.5.5", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-prettier": "^3.1.0", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.7.1_1570451991117_0.25252138960074433", "host": "s3://npm-registry-packages"}}, "1.7.2": {"name": "yaml", "version": "1.7.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.7.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "f26aabf738590ab61efaca502358e48dc9f348b2", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.7.2.tgz", "fileCount": 141, "integrity": "sha512-qXROVp90sb83XtAoqE8bP9RwAkTTZbugRUTm5YeFCBfNRPEp2YzTeqWiz7m5OORHzEvrA/qcGS8hp/E+MMROYw==", "signatures": [{"sig": "MEQCIDHTKmL6egJVKlIqORz2g8F7nphzQKnUAUAKrPnFAlM7AiALH9yDuNyEaqBAMHlG/nxS4ajbs3n601ZXgKdjYWk02g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 477156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpaxbCRA9TVsSAnZWagAAYYYP/RzddvL9fVVZT7Wjnwpt\nDVa2B+Ov5ADyie0JTW8KPOldLBqy/WmHO5InWRkg2BenehYzaX9MzU4hkRCY\n1dbMXY30+ryhDLWFrG03az446zyBl8Rkg/S0H3RpHn+WfSt93hDI0V4k7+zL\nvGwZ1RaoN4cZ4xFKSQH7eTes1+xUcnuQjt2/Cm9XZuyIpYSFl6A/iUoZ5fvq\n3lm3d4dboO2oyUeQuYT+wSEpq3ng+m/VkXmVBbxeHxudH3+9cX7rN7rD+bL8\nQRCsmygkTWToELiQKJbSt+lyxTH/uhHkHy2Ebl70PhXF7ze+58vd1lncXp71\nanLPrUhnEpdJB5zCtxMYsykGJIeiYCELrcjFpQbsROzyDMjC9c4LKSofg69+\nHd7Y0YuxH7qKFSVSs0jQF9SYaHGYVHS8NTSeMEdF1t4TP7p+HEBnpJKiobve\n3EmMXaJBvM+d8JDqUD3duwoKF0YMJSWorQy+++t5MwdrujW6gzHSqC5p5aBY\nIGVx51U6iZre02bFaN4NhHfAc0nkt+85L4UNFSU/a4VCCAaTwr0xLmk0HFJD\np2Z/gEXSjdSzxRr+VLGcA8lyTwBa+1wxVz8YZV8gRZczWJY02Qtjk0vgg7+e\n42BftB164pX1LFVAoXbiLtQCGOpaMZjeV5u/8JLDdOIHRmXFZVVf3T1IiB38\nbkjI\r\n=kfaq\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "gitHead": "752d9e0f4ba0b38e203e8a731b926d8b81e5f9e4", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "clean": "git clean -fdxe node_modules", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "10.16.3", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.6.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "eslint": "^6.5.1", "cpy-cli": "^2.0.0", "prettier": "^1.18.2", "@babel/cli": "^7.6.4", "babel-jest": "^24.9.0", "fast-check": "^1.17.0", "@babel/core": "^7.6.4", "common-tags": "^1.8.0", "babel-eslint": "^10.0.3", "@babel/preset-env": "^7.6.3", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^6.4.0", "eslint-plugin-prettier": "^3.1.1", "@babel/plugin-transform-runtime": "^7.6.2", "@babel/plugin-proposal-class-properties": "^7.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.7.2_1571138650658_0.6368482969622014", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "yaml", "version": "1.8.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.8.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "169fbcfa2081302dc9441d02b0b6fe667e4f74c9", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.8.0.tgz", "fileCount": 143, "integrity": "sha512-6qI/tTx7OVtA4qNqD0OyutbM6Z9EKu4rxWm/2Y3FDEBQ4/2X2XAnyuRXMzAE2+1BPyqzksJZtrIwblOHg0IEzA==", "signatures": [{"sig": "MEYCIQCMZV1IyE5O4hZDr38v5z3RlsI5+4I6WKUSm3TTOv6GJQIhAPslEeKRGoDzXHlt4RHdcPjLFKBI+q4/uNl69fan4HAy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 453256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeY/hgCRA9TVsSAnZWagAAjykQAJDT87mhXZK/ATkLWcHE\nmUIDOaRN5WU4fZ73jrTIsKESLS8DSu5FiD615jdazWlMRff6lnuqA7yQHYpu\n110sHxR3XonoYOAHhvFuR+5I/zmty2noiTZNgGAOolyrSP4gH924Cj6xVjZT\nclAGrkdPY68OtnUf2iybxWzmRkhyyNKzEMPI6ybylPj4xEd2DZkKh2YV5MqD\nf+hxGaWA5JVeMv+zE+/iz90sBdIa3UhsyAHpwIiCtPZaFTbGsVRhhRNPtXlS\nC4g6pSaxJXHyoUPdfx145X3xurSTVaRAb49f44KzVB7Vh2RWe8b9+7RdDY/X\nnHGGbaeQluxd80CxVkcFZTuX/LSusRwG38/s+Kg8eFqoTfkM0QJsGkS42U/I\ntv9sgtZMSnm2R0wmiRAyfLbOyEmwNKcnTm9ppVsEagUk+BP0BpmqFnwAeyT8\nRA/JbbpkxlRCGJFsN80MNshlASYDAKekKOsMPPpRM38mw9Ng/OHosHYuUUUz\n6WagLLD/DYu721YbO2lDu8DVZElfYIHZ5lUEW8PVkYdLGFl2z/NwhktFaM12\n5R8lI/zG7YWd64NXmRmpFiWT9VVgDcU2l056yPNj05sHxyvKsaK3RP0EgAoI\n63KI4sagqypay+SdcuOr/bmzaiu9WIM7I9XPiPd546+KnFNMUgXmFQ7bKy/u\nsw/c\r\n=d5PB\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "collectCoverageFrom": ["src/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "type": "commonjs", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./util": {"import": "./util.mjs", "require": "./util.js"}, "./types": {"import": "./types.mjs", "require": "./types.js"}, "./parse-cst": "./parse-cst.js"}, "gitHead": "a8c465b83ccff0dabef36c603b1ba66b70aa969e", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "clean": "git clean -fdxe node_modules", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "13.10.1", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.8.7"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "cpy-cli": "^3.1.0", "prettier": "^1.19.1", "@babel/cli": "^7.8.4", "babel-jest": "^25.1.0", "fast-check": "^1.23.0", "@babel/core": "^7.8.7", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.8.7", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/plugin-proposal-class-properties": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.8.0_1583609951610_0.28173500730412715", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "yaml", "version": "1.8.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.8.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "3a8cdb877ec9da2350f22b476a117e28e30069d8", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.8.1.tgz", "fileCount": 143, "integrity": "sha512-vIXHJILY3e2Ru5s+hFwmO0fSHo0zm30AJ/eBaIUd/54xVocvjzix4bPOtjIGxKm5VDSOt5psTKW6CEv3WHzWdg==", "signatures": [{"sig": "MEUCIBbACfgrOJHn5cDS0CZFS2B3nL4KE7Gsscy3rpZHqmTyAiEA9fUrdGYLNsz1/PkE1Bsvm52yNJjyyLiMjKkgkXMLupc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 453256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaGw7CRA9TVsSAnZWagAAv6oP/1Y4G3IIHWb3cZnKLkbk\ngm278MwVOd2mhmAxOWD6Y3TPooXKU/fu+tHlB/yxxqxCKx/FC+jiApr41j0b\nlEJ5L+yIitrg9MZDMVMyzTw8pjsqie0iH4k91rOXJ52+xdbttu1G/rtPPvej\nIlL7kRM7UC9Uq0tclCZG4C/amQnWmI5h03cRvGaysgXZOyzwGYCz8EEnXVi6\nIwDDCiXSaL246NEDOJMz/+aT9BElZSW0h9oB4IHNO34XPotTKYoOTo+kGiIq\nwzpE6si8JmlV3V8PmGso5Df9xuYpyUSMAaBkiyz4+Vi1to8tiLNSYyZ03Exz\nCex6YFKcm0rh5JPhPZuRen1pGxJ8N6wWLRSCvrDJ7HKsAEcEVgfreJTobwQk\nZvwwzmiv9yIeZ32xE0FmEx4WzFLUVSW7McRN0C2nyw/2YVyo6Rim0L7kqxJz\nHffqXVn/vMHYb4EXZg4duILC2uF35V9F+uxDXnXHEfr7lzuF/iTt5qDQQUaM\nECC/00a2CF5GMOjwSsHDGb8XbSXrMnNyP9csimeoSKjy80e37kWr3C4F3sct\nikQtNVd5KTR0B8C/Ksbn8Qa0x//r+Yw+irJK8cUSu9A1sYI8ftU/UuUFffy/\ntrALaqxbLKhC/1fOMzxXxW6yD9hYxgGTxwtsJs0yTF13Le5Fl7kwMHQv5gTM\nZaJg\r\n=RjUs\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "collectCoverageFrom": ["src/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "type": "commonjs", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./util": {"import": "./util.mjs", "default": "./util.js"}, "./types": {"import": "./types.mjs", "default": "./types.js"}, "./parse-cst": "./parse-cst.js"}, "gitHead": "99f0ebc93a883c815f60a3ac30e0ef6f60fc4deb", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "clean": "git clean -fdxe node_modules", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "13.10.1", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.8.7"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "cpy-cli": "^3.1.0", "prettier": "^1.19.1", "@babel/cli": "^7.8.4", "babel-jest": "^25.1.0", "fast-check": "^1.23.0", "@babel/core": "^7.8.7", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.8.7", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/plugin-proposal-class-properties": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.8.1_1583901754991_0.8059014978576564", "host": "s3://npm-registry-packages"}}, "1.8.2": {"name": "yaml", "version": "1.8.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.8.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "a29c03f578faafd57dcb27055f9a5d569cb0c3d9", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.8.2.tgz", "fileCount": 143, "integrity": "sha512-omakb0d7FjMo3R1D2EbTKVIk6dAVLRxFXdLZMEUToeAvuqgG/YuHMuQOZ5fgk+vQ8cx+cnGKwyg+8g8PNT0xQg==", "signatures": [{"sig": "MEQCIFV/tAENzB0mqN09cnfYt2O7e9z9fO2Ff5mJ5GstOc2HAiBEC4GO5HtUUjnZe5j1B9ooTrcUTsQ6k70js3wQm5vVVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 453351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaHxxCRA9TVsSAnZWagAAgaAQAIrqdFZSz9z1KqhfEMB2\nVLIshigYJLDXWuikRXfQdBJG2cbNGSkuR2U2OzBQXYGn4c9EgsSPg+CS+Pwz\n8eo7qOndvJSJ+OnHNr9Ac7KA5IgTMZwbRIQt7HQ4MnsMtCPBqDXCttnZD/Ia\nSoM6q5XuUFPYTNWvmWFRRa1Se37as9pmkuDcE0yrA92NQv/Z/IyeEOeabKnw\nVdD4f5tXfzyPffBPKiNn7h04UHk4AdKIP1uTvjENLgBGVaslrY8N9wh/UEBD\nuyMlEutRio46ETrLokb8SyR8J/5MeMtqilQvxtqCUWZF/nHEsh4NpAjaEepp\nIhPEVHama3/S1kYB4vrF63wKj88I2rD/LBoIyVBGIvqaGdzaKapEeW0yEVNH\nRyP/qyhxuD3FzpxEcr/RlSWLh27TSvhLpA1BAoxCyMVfyv2pMb3YleKhK2Lw\nImdhAIHznPZR9MznA7+pmFN9n/JE06D2FeBetdGTkw3X192j3HIC/Hypqkyd\nBiNjGtljtTBkOv/0dlcBpYrDuI93uEeZO+psXBx/oiUh2b8FXHL6TV0YXUXM\nAKxitHWo3uV3hXUyhxPCunsNrNasDRfqlZO8Tt35ysxf3EeWcIb8OBNeSm3b\ndkTlj5C13YB58dRrsws8v1W3rtJW3kgRIPcsrSeN4Fp3i8sLnuyFHHi9z3Bx\n97GU\r\n=iJtY\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "collectCoverageFrom": ["src/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "type": "commonjs", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./": "./", "./util": [{"import": "./util.mjs", "default": "./util.js"}, "./util.js"], "./types": [{"import": "./types.mjs", "default": "./types.js"}, "./types.js"], "./parse-cst": "./parse-cst.js"}, "gitHead": "3f53937a5911ecc09a2eb12fb12a0b74dc5e7c9c", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "clean": "git clean -fdxe node_modules", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "13.10.1", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.8.7"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "cpy-cli": "^3.1.0", "prettier": "^1.19.1", "@babel/cli": "^7.8.4", "babel-jest": "^25.1.0", "fast-check": "^1.23.0", "@babel/core": "^7.8.7", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.8.7", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/plugin-proposal-class-properties": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.8.2_1583905904852_0.5659281491877828", "host": "s3://npm-registry-packages"}}, "1.8.3": {"name": "yaml", "version": "1.8.3", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.8.3", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "2f420fca58b68ce3a332d0ca64be1d191dd3f87a", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.8.3.tgz", "fileCount": 143, "integrity": "sha512-X/v7VDnK+sxbQ2Imq4Jt2PRUsRsP7UcpSl3Llg6+NRRqWLIvxkMFYtH1FmvwNGYRKKPa+EPA4qDBlI9WVG1UKw==", "signatures": [{"sig": "MEUCIQDuMkpUIiMRH7l9liht89LCMP0avqaNEpEBf+aNFFh2XwIgTBbcDgoMJ/HR7DWGu0AgdVR9jFBgXtMrFE9zVOEBjX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 453651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedWULCRA9TVsSAnZWagAAxUwP/jhZESqdI26YYn+LPtwG\noenTzYT98sfg3sQF/mTr8hzpbBSCyEEvrcnIT/IdM0cUGWJG4uEs1PhIGIdZ\nCLRUFnguzfAx8TGIAflojwq0koPP3GOwoGQNqS9yiSAvcdEjDqPBLLWeGr/R\njRxXUvk+95jws2PK5vskaoKNEu9j9N4kxA06hapRmyS2YwWJgjsQkA+n5bae\nNEoWrt0HQrQi0wGIdESoZ+3Sa1D1X2G2bZ7b/PS+EkFQRqCCfhNWl3YYJ61I\nFIelb7NwZ9Y0VItDTxmWS0bmlwUnAeATMlIbrW0/Vdo9h9AItdSe4ZDPDfef\nxySJO8jPmsi8iTUgd12FAbczcEBtUlVOeHuC8NOjzYcp+0zc24k4smDw7N/a\nvDcTYt7qKy58+Pd/VVq9XS6VfI9Le7Z+I8aT4rb9pN5Wy+FSVi5FJh/E4ZKg\nYm4NWnqmNamAzIpn5U03g2VOU3UyQkebQmQ+49ngPfKuuNduAcBpuS7r6mE6\nkROjpWLki7l+YamiCXchHfMa69imDxhD2OIUNgYW+4X4Sd+XImrPAyenINBd\nd93Xt85b7WnYXQ8DeRT+A+eapFMqius8NpP4vt/c74XDyyffqLmXmVq0cxy8\nd/ojGuXhNbYOz872zQ5vpzr9/iPE3XdyVzJWlZMO2u7XgAXRRsnGW04qmoxR\nLxQr\r\n=e8wM\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "collectCoverageFrom": ["src/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "type": "commonjs", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./util.mjs": "./browser/util.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./types.mjs": "./browser/types.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./": "./", "./util": [{"import": "./util.mjs", "default": "./util.js"}, "./util.js"], "./types": [{"import": "./types.mjs", "default": "./types.js"}, "./types.js"], "./parse-cst": "./parse-cst.js"}, "gitHead": "21d6a86975660162c1a5080fe198d8d29ddf17df", "scripts": {"docs": "cd docs/ && bundle exec middleman server", "lint": "eslint src/", "test": "TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build && npm run browser:copy", "clean": "git clean -fdxe node_modules", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write \"{src,tests}/**/*.js\"", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs/ && ./deploy.sh", "browser:copy": "cpy '*.js' '!*.config.js' types/ browser/ --parents", "docs:install": "cd docs/ && bundle install", "browser:build": "BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "singleQuote": true}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "13.10.1", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.8.7"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "cpy-cli": "^3.1.0", "prettier": "^1.19.1", "@babel/cli": "^7.8.4", "babel-jest": "^25.1.0", "fast-check": "^1.23.0", "@babel/core": "^7.8.7", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.8.7", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/plugin-proposal-class-properties": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.8.3_1584751882929_0.9107149528040019", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "yaml", "version": "1.9.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.9.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "dc1ff3e24837b62bc3c8ae02c28e16ee5742b9d6", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.9.0.tgz", "fileCount": 147, "integrity": "sha512-3GLZOj8A9Gsp0Fw3kOyj0zqk4xMq+YvhbHSDYALd2NMOfIpyZeBhz32ZiNU7AtX1MtXX/9JJgxSElGRwvv9enA==", "signatures": [{"sig": "MEUCIBB79sL8p9yhdx10XMV19F6jMdKFyEwQwbOyYD2WbwEeAiEAngzBcSBoD/hCl59MYLP11CznFawDvhxWsesw36/esqg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 499490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemdpyCRA9TVsSAnZWagAAaRYP/3HEK8GmoyywPQHxVz8j\nlrm0ffYXPSY2SNnnJxCTfIE3fYQSLbUFSwjNKyTKGh41nLRN1sp279BmN3cN\nH2amCIYNwrfsj82ere9BNBDMEuALYbr6O9hC1yXeYacxXRqh5cna5bWX6TUr\n/Lc8LLiIQm16AH1vci7JD0zV3lMTYR1wV80G3fm40ZYmsyRDAUEqhzDiYi7+\nQqzkdHddHgxpvds5WQf/cpaYd0bPMRIy5JUbnCvVMcS/e8+xRfhfU9ywjqAH\nOAzbIxEW66kLnx0xhu3FF+Zic1kFm3FsJZxAADUTc0ssguKgMBXciErINJpy\n/pjwK9O+H+hC+M2M9COGyEpmSWSCoW/i7OcuYKGB9W2I0yBcl3twDGbDdA8y\nfvbaB/GAwc0fKo7CejZdU6Y/75pLvqObIByVjHJJlHTi6cxXHPqWCzbGbRmb\niZsU3SmostiDQ6iY/2tdwiujnV7pClI5TFByyDumGQd1zcFvF7ountvmlERl\neAINIXS2eDwTYaVmFx+nMxBL+GKGqm5ydTKb8LXTc26KnxA8XIqxuVqmfhap\nyoCF7PQzUaWu+LLD/LwnDjGHUX/StktHWZG3KEc51IQT/bf30Qg6+Ino3F7h\nPDTpgZVu0Z1PBm5snQRlkqg30zWZGUHLdlFZijm07nGuCYzAJ4iGkMnFUZ/W\n3wSR\r\n=guPe\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "collectCoverageFrom": ["src/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "type": "commonjs", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./util.mjs": "./browser/util.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./types.mjs": "./browser/types.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./": "./", "./util": [{"import": "./util.mjs", "default": "./util.js"}, "./util.js"], "./types": [{"import": "./types.mjs", "default": "./types.js"}, "./types.js"], "./parse-cst": "./parse-cst.js"}, "gitHead": "02f9e13620946ee0a7774e068b2083da5dd3721b", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "cross-env TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build", "clean": "git clean -fdxe node_modules", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write .", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "cross-env TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "browser:build": "cross-env BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "13.10.1", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.1.0", "eslint": "^6.8.0", "prettier": "^2.0.2", "cross-env": "^7.0.2", "@babel/cli": "^7.8.4", "babel-jest": "^25.1.0", "fast-check": "^1.23.0", "@babel/core": "^7.9.0", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.9.0", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.9.0_1587141233959_0.9280112351329557", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "yaml", "version": "1.9.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.9.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "2df608ca571a0cf94e25e417e2795c08f48acdc5", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.9.1.tgz", "fileCount": 147, "integrity": "sha512-xbWX1ayUVoW8DPM8qxOBowac4XxSTi0mFLbiokRq880ViYglN+F3nJ4Dc2GdypXpykrknKS39d8I3lzFoHv1kA==", "signatures": [{"sig": "MEUCIQC+gh5IEbtqJpCqo8Lt2KPrSEl+ycQEnVzOmEfVZQrzagIgHVBowu2Z0PLb1ZdqPh5s6FueI7rAH77RvSvwd5LUby0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 500411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemuwdCRA9TVsSAnZWagAASOYQAJQkc6I6IkX4Bi50Fu2h\nhOGD5IDEd1qQOWe1SAhC6Y0m9S8rxDIIuDwfgS27lcHvHccVmcsv5O8BfrT9\nKFmQzd9OIPCFzzpI8JBEe1O0A2IMfBxp03QfH9PMAEUNdkESEPfis18ODJEP\nXw332JQUHCDi6c3kj1BtND27sdkG8CeCKSW6hRXE1ECJP/rN2RB0dnGBw7MJ\nZQIptQpyf6KNnHDlMGuL3EMWKTjN4DtztjlhHfUIlw/jJOTLIzbj1eei0ueA\nAHTiuJAwzCt+hdrBHA6e39y8O9DzNtouccxFYB3eTJXITQPhcDuiNABmrq64\nZebDb/1jpEFrO8HP9p1zTZcPXdCR9NziOZlZCBdlFQ9OoalIvC08Guy5SiOO\nWqTim16R2OjKObgbwK/G10r3rrO3ZDSmhtzk3dEuEDlcbBZzuhVCSBbxrTly\nW4SDYNUsaiD/7Py91MnYNE+ZQR/bfMhWTPU4keud1CKRPSISqPX2QhhRe+D2\n5IovjQEL0Y9DOtIQdrs0bLbqpyU2yE+NsyvmtYuOF3dHuF/Ei/4o3cNzxygo\nzxxf+XxvmoV3LZKh5FtfHMWQBKQuPU3TDgwiee63BsgPAOIEDKi2b6jOVccu\n155y7gLAphG8vNoro5zk1QAlamlMESYrc4lgTC5Xsa92mX51MEIlmVwVIpaf\n3IX1\r\n=/iwy\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "collectCoverageFrom": ["src/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "type": "commonjs", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./util.mjs": "./browser/util.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./types.mjs": "./browser/types.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./": "./", "./util": [{"import": "./util.mjs", "default": "./util.js"}, "./util.js"], "./types": [{"import": "./types.mjs", "default": "./types.js"}, "./types.js"], "./parse-cst": "./parse-cst.js"}, "gitHead": "c42c25e8bb687b1e7af5943b2e44e80f193f723f", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "cross-env TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build", "clean": "git clean -fdxe node_modules", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write .", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "cross-env TRACE_LEVEL=trace,log jest --no-cache", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "browser:build": "cross-env BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "13.10.1", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.9.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.3.0", "eslint": "^6.8.0", "prettier": "^2.0.4", "cross-env": "^7.0.2", "@babel/cli": "^7.8.4", "babel-jest": "^25.3.0", "fast-check": "^1.24.1", "@babel/core": "^7.9.0", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.9.5", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^6.10.1", "eslint-plugin-prettier": "^3.1.3", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.9.1_1587211292759_0.5095083774998896", "host": "s3://npm-registry-packages"}}, "1.9.2": {"name": "yaml", "version": "1.9.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.9.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "f0cfa865f003ab707663e4f04b3956957ea564ed", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.9.2.tgz", "fileCount": 147, "integrity": "sha512-HPT7cGGI0DuRcsO51qC1j9O16Dh1mZ2bnXwsi0jrSpsLz0WxOLSLXfkABVl6bZO629py3CU+OMJtpNHDLB97kg==", "signatures": [{"sig": "MEYCIQCHdpNhtr2TMSXI5x8XTG9zXaZ83sI7eqsvuwX98BmYBAIhAM2ardnDxkVZgQIMGzHYFVnm6p+scSJSgmHmk/8vUla0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 500532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenWshCRA9TVsSAnZWagAApwUP/A8hqvRTuCZ9PiEXy5Tr\nzmxyOp2hp+CgzbDzP5nElSjE375eYTuQ4faTZY3tanQVMeyDNDDsNDIh+C+W\nv1b3DIb8qyRmYD0Zlw2M0JXFpMNXST9lFHKHqFKlBd89qL6rsoqEJt4d5lIh\nMlTANMB94PYXjfzjo6DYJgyf3VcIq9OBoWJR6zK4HYDPmMJpvFLuO1C8z0GO\ni1toFV4Vx2smcDZmOJwOINzZhLyY2nKIvGXmlLyK2YW/NDSKugjYYICb/7mx\nk7WXu2Bg7P03U41RuIV5B4VboVPQrsjshoHMnkn8fQFh97KA4cXKAo0XcrDu\nY4Hs/nb5R1nG0DMgD8nGyNGfkZaURO7m0Wyp/Iwiq7B+HkZdL3HXweOUNPfx\nj1xr1WQZwB0qNzr0Yu+SZK/cB1Rja7rZyEjxVuExpKZdkDOoJyIvWdWKe2F9\niQ/V6mziVOpZbBsXLi7NwG7IvhtmoZzLH5ZT/07Oli3BHnw4/4EgSlKwMz0T\ndG/OCj63lVCrOsG0brLuKSQzbn3x0XpgshFlh9CsmQYwktrzomZwT62a+cxf\n52rj4f52EeE8jni0T1dW2FUSr8LBud4MKAzguhkWV91nZz6gZ+9i43xXVWx8\nQ+U1Gc0W3Rvt7jYWr4QOhHCqD8X6cgrQgt398oPHrEz/hbb7e+S48rdmgsXi\nxMAg\r\n=LHGr\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["**/tests/**/*.js"], "collectCoverageFrom": ["src/**/*.js"], "testPathIgnorePatterns": ["tests/common", "tests/cst/common"]}, "main": "./index.js", "type": "commonjs", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./util.mjs": "./browser/util.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./types.mjs": "./browser/types.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./": "./", "./util": [{"import": "./util.mjs", "default": "./util.js"}, "./util.js"], "./types": [{"import": "./types.mjs", "default": "./types.js"}, "./types.js"], "./parse-cst": "./parse-cst.js"}, "gitHead": "0013206868966868a539efae6566e11a7e6811c9", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "cross-env TRACE_LEVEL=log jest", "build": "npm run dist:build && npm run browser:build", "clean": "git clean -fdxe node_modules", "start": "npm run dist:build && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write .", "dist:build": "babel src/ --out-dir dist/", "preversion": "npm test && npm run build", "test:trace": "cross-env TRACE_LEVEL=trace,log jest --no-cache", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "browser:build": "cross-env BABEL_ENV=browser babel src/ --out-dir browser/dist/", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "13.10.1", "browserslist": "> 0.5%, not dead", "dependencies": {"@babel/runtime": "^7.9.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^25.3.0", "eslint": "^6.8.0", "prettier": "^2.0.4", "cross-env": "^7.0.2", "@babel/cli": "^7.8.4", "babel-jest": "^25.3.0", "fast-check": "^1.24.1", "typescript": "^3.8.3", "@babel/core": "^7.9.0", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.9.5", "babel-plugin-trace": "^1.1.0", "eslint-config-prettier": "^6.10.1", "eslint-plugin-prettier": "^3.1.3", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.9.2_1587374880832_0.4887206141569671", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "yaml", "version": "1.10.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.10.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "3b593add944876077d4d683fee01081bd9fff31e", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.10.0.tgz", "fileCount": 57, "integrity": "sha512-yr2icI4glYaNG+KWONODapy2/jDdMSDnrONSjblABjD9B4Z5LgiircSt8m8sRZFNi08kG9Sm0uSHtEmP3zaEGg==", "signatures": [{"sig": "MEQCIFEs9roSZug6ArEHUv5qC4YHHSf+r1FUkB04BFt6dojJAiBEruE60qQUumFR/JliMglvIMxcEKyMZS1mBp0/qpFLUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJev7jMCRA9TVsSAnZWagAAteQP/ikhrJdhNfvnOcpL+YBm\ngXzReBGKR14RaQ0LKsokub+jU8kBTpCLIvckXH37/kKfJfAHMKcGkNtIfil5\nGEC7B1etliqM9R6YHKCEJvEigFgdqBWX8DzdFHADRxLEUu/VZ1pfedXzL2RI\n5TFrPWPrhwag3gW6vLSGybf4/+LTiSaEFORMc3zzE+ZmVeNclSdlIO4Lxqg5\nLT08y+6dIP5vx8R5PksPoz79AfGYrNW9UODUlyXg81g6sC5ATlzWjoFt1XaW\n7YlmxiD8VceKf+ntXAfceKCAR5ZBEkh0u2KLLea7pJ+09oI0h9qaOc1xhw+Z\nLMcZtNJM59Fieb6Eesbjn5QdXhkn1FIoiE8Xsdc9Jp0nzZzzrh/oiIeWEysv\nM11x0jHd4/P7XpEQJhk3yxRC/O6RzSbejHjirh7LybyskZhlMSfOOHp4XWYz\nosLIBr+NdGHqRNdnnq6cDYP2JAmssw5C0RSoT3JxAhqG3mRGJDHj2+1l9+6V\nNvGWmgyIZBIW9WToEb6Vp1vaPeFrDT+Tum8aIQc6ehc5yGuEchGr7rApFvDI\nz9bWRWtrekq/mlyErTaPiEXzqJY5xo5Zd/ttMvhPGgUH1/ytSHEVgu9yyQ9g\nyFYM+WOlU0qYAgAyeVYDCbSgyKF23m0u75lsW+BKkqxLuKcSUYZ2miN5nNbH\nEFHT\r\n=5W6u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./util.mjs": "./browser/util.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./types.mjs": "./browser/types.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./": "./", "./util": [{"import": "./util.mjs"}, "./util.js"], "./types": [{"import": "./types.mjs"}, "./types.js"], "./parse-cst": "./parse-cst.js"}, "gitHead": "56b873be923015bb367990f04578b6ee9895bf6e", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest", "build:node": "rollup -c rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c rollup.browser-config.js", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "13.10.1", "browserslist": "> 0.5%, not dead", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "rollup": "^2.10.2", "prettier": "^2.0.5", "cross-env": "^7.0.2", "babel-jest": "^26.0.1", "fast-check": "^1.24.2", "typescript": "^3.9.2", "@babel/core": "^7.9.6", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.9.6", "babel-plugin-trace": "^1.1.0", "@rollup/plugin-babel": "^5.0.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.3", "@babel/plugin-proposal-class-properties": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.10.0_1589622987762_0.2502280120579963", "host": "s3://npm-registry-packages"}}, "2.0.0-0": {"name": "yaml", "version": "2.0.0-0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "432108d836b6715fd33af2e324a8d3980e96f7fe", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-0.tgz", "fileCount": 35, "integrity": "sha512-+n+AwzE2hK2j21p36UB2foND6RYQU4PQkdPCK4YF2OKz8BbiNI+NvKS79CpyDChw3k/0L9jnChkjEzKoiu2C7w==", "signatures": [{"sig": "MEYCIQCGylWqhRqrvjkJYTPbP7AcFTQCYY6p8XD84YiDVabcagIhAI9BPO/pEIkw2KwCXu0Pe3yG2B4/ENfug7OeI6e1FR33", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 440813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQoT4CRA9TVsSAnZWagAAbM8P/2pI0z90wqOucRD7FAN0\nfV9FdPuWpdXuBUnG5S5shLRzBYQ2zg4lSTsZQ6JDEEZYtCx7vgRMWrMkoleC\n8VN44zXAejyEyHr82oVwWMLh4RnV5LxrkTZitmWc6FHQZpnBUFGk+Jo2XYC8\njjqxhjMCRbJ4ufXoQq4F8u5pllshb9CaY4T07brjzZsHBhRuukAk92fq7MuC\n+QT05SWIn+RrOV1cnRaL1/omD5za1+8cnFgxSxEub4Uf6AO+w9TpJnkolj/B\nszvNVYDoWHyUCpI7ct9E7JPErOWbtta3mz6AtGtALWCFJyHfj/6SD3cuP0c8\n9BNMF+s9sjMQYdl74piq4x/D/AO9u0hnTN08rbkIJVjB9ngHRlgeGwlAcTVf\nL+uu2SUnb/Xd1JvT7bVossMMtgolNkzBcGCzyrplT54RIbj5eU/GKexuzQ7C\n0AuKjx7N1sYD/GYzcwfEf3psHZy2eGrcN2nXbl8Qk+KoETabzcSvRM3oEE47\nojoQQIQONAWzwlywBn4YbvVMnLM8sewTkgtr9D5Gn0RmoQHnuqdwov4zc/Z0\nGb2QQQASO6RFPKh8y91vkXcXA3BfBUrSV9RWKofmM05YdgPXqJrd+RkdUdNQ\nG4iqDHiuERINX4tM/xNsEDja6hrqJGLzqJOma6SjEp1BP3uWchsnEizRM6jF\nZWha\r\n=ajaD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "browser": {"./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./util.mjs": "./browser/util.js", "./types.mjs": "./browser/types.js", "./parse-cst.js": "./browser/parse-cst.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./util": [{"import": "./util.mjs"}, "./util.js"], "./types": [{"import": "./types.mjs"}, "./types.js"], "./parse-cst": "./parse-cst.js", "./package.json": "./package.json"}, "gitHead": "0a4d4cc3fd821807b652aa73454983d29b429210", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest", "build:node": "rollup -c rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "14.7.0", "browserslist": "> 0.5%, not dead", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.4.2", "eslint": "^7.7.0", "rollup": "^2.26.5", "prettier": "^2.0.5", "cross-env": "^7.0.2", "babel-jest": "^26.3.0", "fast-check": "^2.2.0", "typescript": "^4.0.2", "@babel/core": "^7.11.4", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.11.0", "babel-plugin-trace": "^1.1.0", "@rollup/plugin-babel": "^5.2.0", "eslint-config-prettier": "^6.11.0", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-0_1598194935750_0.7647945892021262", "host": "s3://npm-registry-packages"}}, "2.0.0-1": {"name": "yaml", "version": "2.0.0-1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "8c3029b3ee2028306d5bcf396980623115ff8d18", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-1.tgz", "fileCount": 35, "integrity": "sha512-W7h5dEhywMKenDJh2iX/LABkbFnBxasD27oyXWDS/feDsxiw0dD5ncXdYXgkvAsXIY2MpW/ZKkr9IU30DBdMNQ==", "signatures": [{"sig": "MEYCIQDSiZSZM7JhGTPQwzPw0TegSaeIWQgk3t2c4A5wcj/rsQIhAJwo8NTM1ljCsHEj1slGC/QT3e/k88QBntWDx89jcYP1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfe54pCRA9TVsSAnZWagAA5aQP/iJNWlse/5Ox/YbeCqeF\ntPo9DdcyYbBytiBUXDLwatAF8XBPOGGMaHMFKQ1vEeFjvdeGNPMD5PDFWVAo\nCNIZMbotjubnRVqwux7Pxozjqwa5xCjpfKWEqKfhSIDdPIFzHPQU5vcKl2Vx\npeDsWJyHbj7077+LqgTz4jmBOMxTdH8SAxJQWOtiHv6ihhKTI3NMmPAdLplc\n+3k7Z/kCFTwRTvYp1zirFOBaWuPGUx6Uiz9OfvDOlMGPdKAa1iWV8iCfX9Js\nUqR+DwDcMrL23JTYbxNXQloKL0VA6XeZ7Q+7n/8qqVK8TQxf0MRGqkRQRoBv\ne+K27ucI+rtRMlNoPwPMI8XGGrkck054Ipat+fzWiTouyHlTVQBYPGXHGFBT\nztOCmZBElGY5vG6sIiNzXssv384zmEPaVVKCS2gEL5xkDnAo0ai5lm/FGZiN\ny9dxJFw04GDslQmYjipU/+RqDUcZfCENY1kkzd2oVjUBueSfHfXXVnRE2pA5\n3v5aLkmdvcIW5KJ5yfu1gbFh/lcapsSvFQDwXQEcONFO2jxK7fhNznFplZFJ\nLCm4SCGIbFqUqpND47U/UW/V1XsCxDpw48F0+phnEwGX7NYHL+2NxrZoi+eT\nmTTNqowkWB1X44BZHimMQ9cpCOVlx2qVl5BFBGkeTTIzZMAI6FKXLjhrtqYs\n5bBT\r\n=7sdx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "browser": {"./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./util.mjs": "./browser/util.js", "./types.mjs": "./browser/types.js", "./parse-cst.js": "./browser/parse-cst.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./util": [{"import": "./util.mjs"}, "./util.js"], "./types": [{"import": "./types.mjs"}, "./types.js"], "./parse-cst": "./parse-cst.js", "./package.json": "./package.json"}, "gitHead": "8dbce5235dcfb366959c4c5249ad9875daeeb7d4", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest", "build:node": "rollup -c rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "14.7.0", "browserslist": "> 0.5%, not dead", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.5.0", "eslint": "^7.10.0", "rollup": "^2.28.2", "prettier": "^2.1.2", "cross-env": "^7.0.2", "babel-jest": "^26.5.0", "fast-check": "^2.4.0", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.11.5", "babel-plugin-trace": "^1.1.0", "@rollup/plugin-babel": "^5.2.1", "eslint-config-prettier": "^6.12.0", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-1_1601936936688_0.7571554792879898", "host": "s3://npm-registry-packages"}}, "2.0.0-2": {"name": "yaml", "version": "2.0.0-2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "1cb85be774b4b133d522cceef3dd841f22f5759f", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-2.tgz", "fileCount": 157, "integrity": "sha512-owqW8Vw4Zi16SxpQt30O9cCcvZcKmZzUvPYm7BAeNQY4lzMTGFDkI7IP+wKtDCuY5hSp5kxl8JseBHlTBkYkiQ==", "signatures": [{"sig": "MEYCIQCWmRkg32kcpmsxXdeUbTYfuo105RxQOTSdLs0wObmqjgIhAOHVwSvqqfQyGzUbUd3tUzdmLVjKxq5RUxSycJRWpfm1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFxQtCRA9TVsSAnZWagAA1FsP/igre6o+JyL0s9Zzemcd\nTSG0ewVVTvyZUvjuiMIF1uE3rWUSKA46ER61/yjFt3amr91JvRAx6T6ZYwuz\nhJgzgs1eLl340R2ddrZLX5cK081Eg0Y3m5URNJsAykn3SKXeC56wnOhj658V\nMQV3hzYBVFeXrZkbAhGEwxmiPaCtdr+74PagWnoWdcW0NLmiQfUQxXYdtgpo\nkVk5y3uL0/djbubiExYotUkAyZAdilmSfAOfMqA08mGj+XfSJo4gHiaiFBjQ\nHtq+XZWVYF3NkceyNsc8NgozClIJ0HvDbL3M8Lq5jDt46uGe/JgPrqw8R76a\nehNcOCyfcqVGvDGJPRZ7CUcvjScXq9tdu9H/bO2ugOBROtZM5rF0NdqXjvlM\nQuV/DZokuIhkO56l+c0QSMawiAlvDBe/e5vsurOK+rliDQAqpT6Zh1nIRehd\n4f9dRkbwVEKpJctl5DkMYFzyzI9jYrUUxNGTxApD3TH6gCOM3HTYYriyITw/\n+bB8P7RMfLSWB/zGAe3Lfmudlj+J3PL44vAKPFO8f+h/X9t6jtPMGNnGWrJa\n1z1q+S6tog9ZZimnxhbDs45JOWCT+YfI6AdJ3dRMxvw/+iRJwsYVyaGdLPBA\nJghbaI8/4jxr7LePy8OcSiiQkWocCHRMOeoMa2KcAzvG05CMQgIT18T2Jgnt\nlRWc\r\n=qxei\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "browser": {"./util.js": "./browser/dist/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/dist/types.js", "./util.mjs": "./browser/dist/util.js", "./types.mjs": "./browser/dist/types.js"}, "engines": {"node": ">= 10"}, "exports": {".": {"node": "./index.js", "default": "./browser/index.js"}, "./util": {"node": {"import": "./util.mjs", "require": "./util.js"}, "default": "./browser/dist/util.js"}, "./types": {"node": {"import": "./types.mjs", "require": "./types.js"}, "default": "./browser/dist/types.js"}, "./package.json": "./package.json"}, "gitHead": "bfd88551df003087000599ff7cc8cd79e8968731", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest", "build:node": "rollup -c rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "14.7.0", "browserslist": "> 0.5%, not dead, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.19.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^26.6.3", "fast-check": "^2.12.0", "typescript": "^4.1.3", "@babel/core": "^7.12.10", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.12.11", "babel-plugin-trace": "^1.1.0", "@rollup/plugin-babel": "^5.2.3", "eslint-config-prettier": "^7.2.0", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-2_1612125228770_0.5865248696921088", "host": "s3://npm-registry-packages"}}, "2.0.0-3": {"name": "yaml", "version": "2.0.0-3", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-3", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "dc126f2fac9b845ee696ccbd13c8d303e247c471", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-3.tgz", "fileCount": 157, "integrity": "sha512-gvtVaY+/mQ0OsXgaWy2Tf830JuXN7qEUYdXWsuiJVSkMRsBBQ90YVpQQofaURbhoA1xSbLBf7965oH6ddzNbBQ==", "signatures": [{"sig": "MEUCIHALNwvLO0vvN/h73BsmJZvUAMbI9smfvDLMEWEgHkzDAiEA01mOr/pCTDqlpQHVqGC/m306NZrbyakM4pe3u4NH0y0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFyEJCRA9TVsSAnZWagAATZkQAJPKZInEWTm84NSOQhSe\nq8Mu7KJ+0ZiTwdaIto+rt4xrirh+iE3wXsoxyeXVD1bsLHI6779/55G4GAeb\n2Ym3aAulJlk2ONwZYQOQTm9itCn8+Fa60K2Z1rw1GRIVP2xMJ/om4UxbtNGl\ntibbIaP9rT8imylgCpTUSlpGFHHj5sUv+jv1S0jHNQqcv7gE5zYomiDmvyMu\nuUb6M6xCnKNBtjSHagM2/dZpd04emY2lGrH84/wLI/II7wy8gTeXyJdgmNrR\n3SHKi70paQDUPpBQ3+MWewXY10Q7fd/vk7mRgEQWbMzqBnMpGArL/a8c/kps\nD4wCkuwmStTIGaupPc0Ifm9ndYrP9m2NLm2O53dJfISP8VzCDpuSOCVM0Kqo\npyXHgABSfey+GSlpXNQXXnx5kDtRZuzkEU04+l3Zf1eIO3kwgLFywRgAWDKM\nEYyHf8mYX6KLZ7IUbjsrAPD2X6w8EsiK4xxmauwG6pAK5/uq+NKGbEwhEavn\nakXHeyXajfTKfU8UanwrfO2SfpqNMU3vfHhVBJFw5+4W4ifHEXcxk64nZgio\ngQVbwa4Ngglqphes6bYiwIGsiLD/DpCz3XYc2xHu8EuRQmG4ZPAL96q8Fap+\nSoxZdllOzkAENiYuNnsEneLmHGb8/bVokD1kKD5uXXrN2aA2/wfzhhkMB3i9\no93d\r\n=qI47\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "browser": {"./util.js": "./browser/dist/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/dist/types.js", "./util.mjs": "./browser/dist/util.js", "./types.mjs": "./browser/dist/types.js"}, "engines": {"node": ">= 10"}, "exports": {".": {"node": "./index.js", "default": "./browser/index.js"}, "./util": {"node": {"import": "./util.mjs", "require": "./util.js"}, "default": "./browser/dist/util.js"}, "./types": {"node": {"import": "./types.mjs", "require": "./types.js"}, "default": "./browser/dist/types.js"}, "./package.json": "./package.json"}, "gitHead": "b2b35758b45e0cfc30be7443b42adc71b40b193d", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest", "build:node": "rollup -c rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "14.7.0", "browserslist": "> 0.5%, not dead, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.19.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^26.6.3", "fast-check": "^2.12.0", "typescript": "^4.1.3", "@babel/core": "^7.12.10", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.12.11", "babel-plugin-trace": "^1.1.0", "@rollup/plugin-babel": "^5.2.3", "eslint-config-prettier": "^7.2.0", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-3_1612128520803_0.25239068729749325", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "yaml", "version": "1.10.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.10.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/v1/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "bb13d805ed104fba38f533570f3441027eeeca22", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.10.1.tgz", "fileCount": 58, "integrity": "sha512-z/asvd+V08l1ywhaemZVirCwjdzLo6O1/0j2JbYCsGjiezupNQqjs5IIPyNtctbHjPEckqzVGd4jvpU5Lr25vQ==", "signatures": [{"sig": "MEUCIE93kIX9shCj3nL1UVFrpW085bmbOqHlg5pDwRfwNLVkAiEAqZhk+zbV7RSoYmlIDVQeg6fLWhASTklrYI1ul+yNKes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 448105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTJweCRA9TVsSAnZWagAACPsP/RMcVyffisDszyH+EcC9\njRyrL9UXXXjwIrfa75+qhEsd0cQhr1BK2dxUNEAC+Z+rnF8kFHJRY+4hbtxt\n5beRNeTFGkMeqc6VmwpY4C9G1OrMNcmElBYNekrpj/dqZHGpMv6/wy2FqZMS\nE3uC0/LIby15CdS2PIMj9PPpQmP6a30c8ctnlOTA0BE3ESfokwjRZaAiztc1\n+OCMJo8tOAxR4LO6uPWLj9ozt1/enbKr3D0h90LX4ayIhnj8be04bRwurZ4P\nCXg5eofmxOqiTUH4HRJ7LycMxBeK8E04iSB3u/Z7oeivtU7tvjGOCwrykcND\neZBXuOU3jYy2eiYwAdqGN3fwth0IIBfIELt8W2JG4/ItjcSzOJrOqUFh8JaN\nsIbOU8eF24c/VcJgZ7wvUKXt4VQg3fTeeb5YgHcU3VVTW72SytEFCJwV7j5R\ntwro4q3UhT2S9FinPTYpIr94dAqP1779V1SuAXVSDMlwiPIDw+pLBX522QVr\n616PLLWdQCAlseXFbPUUIXmQMNepZIT3pE2P+kW++rtbob5z5n87ExFX1XC8\nLQgoiuqyKo5ddaOl3K/H16CrH1+eXE23KD910IUKUZ6PSgmUpDaODSmI5YAE\nUunwJDDNVhIiL7yuoC8ROqxh9Qi0I1aStmzPPk08wgDm+JsC+lt1Et5C7EGm\nUrnl\r\n=4njB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./util.mjs": "./browser/util.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./types.mjs": "./browser/types.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./": "./", "./util": [{"import": "./util.mjs"}, "./util.js"], "./types": [{"import": "./types.mjs"}, "./types.js"], "./parse-cst": "./parse-cst.js"}, "gitHead": "8ef015788b219a4b249736f4bb8968dafe68dcc4", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest", "build:node": "rollup -c rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "7.6.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "15.11.0", "browserslist": "> 0.5%, not dead", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.19.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^26.6.3", "fast-check": "^2.12.0", "typescript": "^4.1.3", "@babel/core": "^7.12.10", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.12.11", "babel-plugin-trace": "^1.1.0", "@rollup/plugin-babel": "^5.2.3", "eslint-config-prettier": "^7.2.0", "@babel/plugin-proposal-class-properties": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.10.1_1615633437918_0.07375426558649667", "host": "s3://npm-registry-packages"}}, "1.10.2": {"name": "yaml", "version": "1.10.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@1.10.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/v1/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "2301c5ffbf12b467de8da2333a459e29e7920e4b", "tarball": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz", "fileCount": 58, "integrity": "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==", "signatures": [{"sig": "MEQCIARiB1B72AX0dpaxjNVxQyj72VJoz9RC0lByfo2DeJ2jAiAYN5J9l3vH2JTUdSYuhybuSU3m57Ww07CzCK9c4/vkNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 448149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTSiACRA9TVsSAnZWagAAh3wQAKE5IvIw2W5eSY7PKd3B\nkJAyHHeUEY4wPPqwAQ2Nt5dC3/vFRin43nUyRXcFdcFnodotLmCtnhsKKZOd\nPTVlPh+ykL0Z8vMBDIMtztxE2uakX58l4WIxM+b7i5BUfP9FuQoss8xx/Y+V\nVemmvD6a87jEH/6F+kCHd320l90gpBZAsyxohc/A71z7B8l1Cw8Y+zOb+N2R\nhiz1dpcwyzAjbVi47Eaz6EIMaVr1qmLgYq7Oq6YCzAyRE8BOGS5Ikf0iioav\n5oWM9/qGH21ip7fePw58WoQNZn7b1GSM27hKZQ/P+5R7FSZL03UA8kmmZ24k\n58f94SYVf/7pNga+5N/jtJHwbJ8GZC3QkHCos1kk7kGOJQ+4v4dFSziSgYWO\nPiLkoD7WENtvdCIUO3DNyzBI6LRY94Ae/bTnZ6YuGtLIv6iSR/+OlTdYEEld\nV4Tf/R7zt+FaRDsHup411QdzJAyc5HqCjEyAdsBTXUENozAyR9LYY1mG9nE1\n0QxJR2+xEsYejK3NQPQG1coTXutZVUwcCfBTlLCSYPZixD530/v8Lw/ATevi\nYAU8ZdqxPt+ioUGiaTmZ3NRVgB3rUl/7oLMI057Qk79thgu2UBGwN8LwrYWz\n7AK/ylAPh7wETDwaGb6epEf7J8m9O6qGoyNpRoBBGeuMuJ6fhgalqfK/YDPn\nwsi9\r\n=mchG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "type": "commonjs", "browser": {"./map.js": "./browser/map.js", "./seq.js": "./browser/seq.js", "./pair.js": "./browser/pair.js", "./util.js": "./browser/util.js", "./index.js": "./browser/index.js", "./types.js": "./browser/types.js", "./util.mjs": "./browser/util.js", "./scalar.js": "./browser/scalar.js", "./schema.js": "./browser/schema.js", "./types.mjs": "./browser/types.js", "./parse-cst.js": "./browser/parse-cst.js", "./types/set.js": "./browser/types/set.js", "./types/omap.js": "./browser/types/omap.js", "./types/pairs.js": "./browser/types/pairs.js", "./types/binary.js": "./browser/types/binary.js", "./types/timestamp.js": "./browser/types/timestamp.js"}, "engines": {"node": ">= 6"}, "exports": {".": "./index.js", "./": "./", "./util": [{"import": "./util.mjs"}, "./util.js"], "./types": [{"import": "./types.mjs"}, "./types.js"], "./parse-cst": "./parse-cst.js"}, "gitHead": "4cdcde632ece71155f3108ec0120c1a0329a6914", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "cross-env TRACE_LEVEL=log npm run build:node && node -i -e 'YAML=require(\".\")'", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest", "build:node": "rollup -c rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --lib ES2017 --noEmit tests/typings.ts", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "7.6.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "15.11.0", "browserslist": "> 0.5%, not dead", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.19.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^26.6.3", "fast-check": "^2.12.0", "typescript": "^4.1.3", "@babel/core": "^7.12.10", "common-tags": "^1.8.0", "babel-eslint": "^10.1.0", "@babel/preset-env": "^7.12.11", "babel-plugin-trace": "^1.1.0", "@rollup/plugin-babel": "^5.2.3", "eslint-config-prettier": "^7.2.0", "@babel/plugin-proposal-class-properties": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_1.10.2_1615669376185_0.9559963702192358", "host": "s3://npm-registry-packages"}}, "2.0.0-4": {"name": "yaml", "version": "2.0.0-4", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-4", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "0b8089fecd1843d1a8eb8d0aff1470c471653e15", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-4.tgz", "fileCount": 183, "integrity": "sha512-MoQoNhTFI400tkaeod+X0Vety1KD2L9dUa6pa1CVcyfcATjC/iDxoMLvqZ6U3D8c5KzxBrU2HnJH+PfaXOqI7w==", "signatures": [{"sig": "MEUCIEKrEU6eeZhIpQdkDAtWDU/UlJQfCYq0L/+emR9y7oZvAiEA0ugfpNqVbaBKMYni8AOv5i41DRpE/PCm7g1TYveW81Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 528998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTUYHCRA9TVsSAnZWagAAWEIP/inZfW37+HNOBa6b7AaP\nNJUvciA67FE6uL4J9syItx9j6ui5z7Wqsiw0pT4klzyLe9xowCfeWezA/fc1\nEQJJ4s8x7Xkp46IbOgN+CGFHnYh89q3rHxJwb4bKXzAr0pacz2TlxhXWH0J2\n5sJgcuwWCyrCoM8lqtz+8pjXsmGJvg5YOCzXDlG6STfuCvDUaPVS9TAzy5ke\n1pLxnk56B6yO7uAKr0yrfDh71zv7fgkcD70pd+x0Cg9NTXGSmxGiu5f4vwuT\nOeJsWUAnXNrs69cssHnfsRZ6HdqTE2Geij/+x+o3OtdYXwniQhtvFVczxYbT\n0rzEK3dp0mXjcsW3xOL5NgxaEbNWMWR99v3ePu1ABN/eoerVVyzKNZ8710oQ\ngovXTCFjUrAk4LESjISAIsb2PwJop7DNRk/aqKUlBHpy8HV95hP9tQvDrsb8\nGkKlkTICZSOeXJ/ojFBRC/z65K+Gal2C23Sb4Yy9E6J8iEUxPpilFeBfjm8U\n6MDEgSMKI07Vn7sjVVFYAqIWRHepte32StFeLn0BWxPhsBL+HV84/yLbTTmu\nr/9iiPaF3/rD6BCYU5OmI4Dtd3se/EU51JTn/L7/mrdz25qU0IzxgYXqY3Uj\nhQKhhIjfaT3pHX97CwrripAjTxz8zO9T8m0nFCZsmNVtZNfMIKi533bqn8Ph\njSY9\r\n=3WeQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 10"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "250f62177a1ee4ea1ff22514784fd578ea1219dd", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "7.6.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "15.11.0", "browserslist": "> 0.5%, not dead, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.6.3", "tslib": "^2.1.0", "eslint": "^7.20.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^26.6.3", "fast-check": "^2.12.0", "typescript": "^4.1.3", "@babel/core": "^7.12.10", "@types/jest": "^26.0.20", "@babel/preset-env": "^7.12.11", "rollup-plugin-copy": "^3.3.0", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^2.3.4", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^4.15.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.15.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-4_1615676934662_0.6606132487250003", "host": "s3://npm-registry-packages"}}, "2.0.0-5": {"name": "yaml", "version": "2.0.0-5", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-5", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "00906046dc119427b2b4f7cf9041d34682d1480b", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-5.tgz", "fileCount": 219, "integrity": "sha512-qH5L5eqW8cyv/N1U6rkK/O0M7kOK3BSo48d05Ptm03ITNsVFwg6TQ47wR72Db/ULWH5RfNJv+CqnG17Pyn8eqQ==", "signatures": [{"sig": "MEYCIQCjefdLt5+wDHDmLtdT318y/rH8HkwTdoOuinlreS2nAQIhALrpqUZmvl64tBnqAgM62RdeDHXBQvw8d5L1J5rCJRRg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 587335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge/NQCRA9TVsSAnZWagAAZEAP/3O8KS27xwzKZuvScqGM\njuKQ3hEHF/BQgX5GgP1QRLXQK1fyhMMfJ6DWesqFxphBFotANFfITU56/AnN\n2VBbZQky7mZZIi6nbdFwaSsI5EfAS5KvTsORZe2VUn7uikleB23vhdJsAyfu\ngKiGjYwgPx3vq7zCeXnD4CB/5cNfaJQdlVPjnTspNBvxvg+6y7Mt5MSSKhQC\nL5FZ6Kp82hfehTjvXHrSxD8EeQs1nnj3tX7+LXvoJAlwEcujre46KVf3xqN2\n8Sk5vU9WiQ6jw+BAaNR/v2WZkXYDd0r30tPyGTj7OhT+Z+gBel1QtgKBe/5Q\nwA1iUAnDd3Rb1SU3kdIgJLyMh0dGRniEM8reDXgLrF20vo232SigNoWYWuka\n3qpInVaZ2B2xFwSw+dhstoy7XYXnEFVnss0rn92VP2GfotPPxoJxOOk/s5Eo\nkD2j3Zhdr/HqpGEHfxtoiUW6smFVylqTh4Ll3aRP7ml6b4deV3TtXLm0+6Fw\n/H4ft1zVg0pb4r7zEBY7bhfCFN3gVUVkc5UI3W4IoeQoRnBUTg8zM2yetmTz\nqKxbimafwfG78PN1mAffvyZGsjG0zDAlcr7LnBQmatWvQP+0U38B+5cXswJV\nxcJYL41B+hvQPv++qANw8tOuTS+NlDEa09sC2zGtjSEnox9nP4ZAPDB/RNpU\n2SWI\r\n=aSJm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 10"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "89f6101297cdeea41641488b49765c2e4ec451f2", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es2017 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "7.8.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "15.11.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.6.3", "tslib": "^2.1.0", "eslint": "^7.20.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^26.6.3", "fast-check": "^2.12.0", "typescript": "^4.1.3", "@babel/core": "^7.12.10", "@types/jest": "^26.0.20", "@types/node": "^14.14.37", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^2.3.4", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^4.15.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.15.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-5_1618735952113_0.23361353223719283", "host": "s3://npm-registry-packages"}}, "2.0.0-6": {"name": "yaml", "version": "2.0.0-6", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-6", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "3d858d656b9ef13a95c77d083b816e6a8d16caa0", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-6.tgz", "fileCount": 222, "integrity": "sha512-YPUm0Z0sei53zauT7HWkkxyIBJhb9Gnf5jv4w4ahw5/v3PjFGhZOt4paXH6g9hzcMJqmNxZwoGfF1JzE2jvSgg==", "signatures": [{"sig": "MEUCIF5HRIvrT6a3L5HbGk6luL4uyufuU+KMaX106THluoloAiEAip4NQ16MZxpQ1O6M7ay2P1sl4ggIeVih3LPjaKFdxLo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 596024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxv1/CRA9TVsSAnZWagAAL8gQAJgAEwuaIUIe1Lad8G+M\n9yPIiWZfGrVQiisThBpejEZX+/tbYAlJqfqBH3W4jnrkXkUYQwSlRI2u4faD\nNyIqY31kKwn8gmuq+UjcVRzzBKL/zhQkTdvUUnmYOvQKGX8ri6aCCLZniRzu\njaW05ofWCgXKXZfxkVG/Mfk1lac1twSQ9uIMHHpl0Y7OW1MTZfbWWiLa8/mc\nqll1G/Srjcf+gH2hcF47xm526toTtokU+Dcy5tTnlga0444w/NQLWvwBHhNM\npJV0FuGJ6naUbeLn65DVerToUkWsM2y9BWhT538gHZoYPvut1VfQ+rFf0IJC\nhF6/vv7GwWV4JexgJlW9+8jnLD6QqIDe1XsJPeLmOcpIA1+B26QOtfkXAU5T\nphqcTmgsBGLbRssi96aOjMzmFHScSI0DyTATTV8u2vlSG8SrwFfxgOdByLNX\nhtmxN/e3BvQ5LTcoViFEXPeFVlzqCJnAAAH5vaIi28W1UuRpqHTUBRHghP/v\n/9CFONQO+u1rNPYILgyS+jD2X3JYnRNXVSoumyHlKEPHT5qhEDrS+BzVqMLV\n09KjbVUt9E+Lbgzw044KdSuxxuwwV0Aw146vECJFuRGn2J7K8vQNGZ0DJwTS\nYqm7g8ZuciWNHH9iuFdytfjoVQCC02IwfNTO5J208KZd/yVIfr4/YSC6Yc1U\nutom\r\n=Pp0/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 12"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "b07e9cec928d24406d515580099df7dadd76a313", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es2017 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "7.12.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "15.11.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^27.0.1", "tslib": "^2.1.0", "eslint": "^7.20.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^27.0.1", "fast-check": "^2.12.0", "typescript": "^4.1.3", "@babel/core": "^7.12.10", "@types/jest": "^26.0.20", "@types/node": "^15.6.1", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^2.3.4", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^4.15.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.15.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-6_1623653759007_0.2444526931181159", "host": "s3://npm-registry-packages"}}, "2.0.0-7": {"name": "yaml", "version": "2.0.0-7", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-7", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "9799d9d85dfc8f01e4cc425e18e09215364beef1", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-7.tgz", "fileCount": 222, "integrity": "sha512-RbI2Tm3hl9AoHY4wWyWvGvJfFIbHOzuzaxum6ez1A0vve+uXgNor03Wys4t+2sgjJSVSe+B2xerd1/dnvqHlOA==", "signatures": [{"sig": "MEUCIB2Zcg7pBoDAqieKyA8t9GZ+6QiKR6xeRbq/JCToqQW9AiEA7CFF7DXQTv1C884Fji6mMVju4rdQppD0e4rD5SaVc7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 598527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg8q+gCRA9TVsSAnZWagAAlZAP/3TcW0TC2rK2NioU+5uJ\naiHpx38mpZMSB1yiAfaUx95gqIHwXs4zgiYw0/oEZTKXoy2xeppZXR863kR2\nAGkwDcQ1QZnw61yXBw9vuuWcuOEOyICnQFdY6cG6k9Raa4wSvvQ84gI3NBhY\nplC32S7+3Ibs6D8LIR0e6GIhNYGsfzHuT9hBGApW1WTZBo+vkbyeBOdvgAJh\niEHAmDlIzIaux1nWGM3nNov9L7ScHvnryvqp03NaBQxIFu7/SardNF3MWigJ\nTXkyAlBA83xL574xkAE4rDMRsPmgDWiHDHgq4rmvSp3FPcLGd3XuzukTgLuR\n099r+xBdtqYQEhyzvfLmlTXLrU+b07BgYxrr+znGC8PpjsZ6sWipuAX0qx3X\nQmew9OuRhDJO/Dzx6RHJVZFfJT/U3LqogqaEwggzC7WZq+mUTzqz6sw/jkMn\n3bfwndF6aWhjqKbJjeyWeaT2pRpd05qLDu7JBeVPZE8QIipCDgLb2AWSX1Zh\n9ZLffMUTi8FKhs4VR6Bu4GLslhRLaFbaRDlsx2Atv4+1AJgXVWtUqiINkTxK\nz2wzAA76pkQaMSApTjEG/vz9IhsgKHWc74F2T1hrvjoT2EbEEukPv8XjgwIp\nWqC5FojixOB5u8/lIjQaacnHJFyadzyCWY524BqkuKA9YaQbwgQNsPsgDYGi\nVuoV\r\n=rNrm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 12"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "a0b5cdffad2af5de497dd0e58eeb3a1878eaa93f", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es2017 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "16.4.2", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^27.0.1", "tslib": "^2.1.0", "eslint": "^7.20.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^27.0.1", "fast-check": "^2.12.0", "typescript": "^4.1.3", "@babel/core": "^7.12.10", "@types/jest": "^26.0.20", "@types/node": "^15.6.1", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^2.3.4", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^4.15.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.15.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-7_1626517408734_0.5989743176531204", "host": "s3://npm-registry-packages"}}, "2.0.0-8": {"name": "yaml", "version": "2.0.0-8", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-8", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "226365f0d804ba7fb8cc2b527a00a7a4a3d8ea5f", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-8.tgz", "fileCount": 222, "integrity": "sha512-QaYgJZMfWD6fKN/EYMk6w1oLWPCr1xj9QaPSZW5qkDb3y8nGCXhy2Ono+AF4F+CSL/vGcqswcAT0BaS//pgD2A==", "signatures": [{"sig": "MEUCIQCmpkhfUiZW0BVfYLDU98np8CJV2lMjk2b70PGW99ARIQIgNHvMRSP+6Q0/lhGMUwFMZwiKBzuLwqDFtM0KDzv4Odw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 605703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNk96CRA9TVsSAnZWagAAcOUP/ROgm15RQdsS84P+wOte\nRFnsGmRGkdBchJQvXlU5iBY6AmvlUIL+2c38uQlSlidTc8IAWaMFqS+sE5dE\nDEuZHOhgrjWJ/X510nP2kRuWfSfpfRnQK+ktb+kitOsjTougjlNWeciLA3gq\n6wuIlOf3NoynnZdMg9pSjc0PMbEJdXJMyjoyRD6XWlpP7kV/ntQHbv/VVRFX\njBx1q4Ec0wt1lSj8sWjeWVjrCLb7/j3FIxnnznk2FjqO1HA/u2Bv2aEEm5pL\nmcuytYMxjis5s/9yeylRq6ZCuo7Kly8RcxmroaP5/px7P8XQQ7VXop+M12RM\nj0dGtSEvMB6Wu/rOHKrG9nF94WBL2SKvarZCW2TMIo8mbgm64WQ+706caADB\nE9Kg1o1MSbK7GCihbl/UY6HhvxQz543vv6Njy4uqXsBm4zWckRhcREo0jrf7\nF+qz8fdj7u21tsN98mQAlZCpCbEvXhmAPKIDDIkoUMspEhRzpSW32JJxIp0C\nD5qjZxDkKFX6GLeVhUfaJYcYkPrYaGlcT4HycvsrG59CuC0tpuLT61Fs8d9n\nxf23FaOsXu6mH8hy0iB5nQxTR6X8rYWATib9y55qptv+MdF8KzbzUU5RLPBZ\nn+TE9tvz51D3iIvwSXobiGZmcXAd+4gj8dUG+e3VEry7j4K95eT5ptNgLlFA\nfx3d\r\n=W6Qp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 12"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "3736cc59747a0a99e4bb0516d240a8b5a0dc8785", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es2017 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "16.8.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^27.0.1", "tslib": "^2.1.0", "eslint": "^7.20.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^27.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^27.0.1", "@types/node": "^15.6.1", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^2.3.4", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^4.15.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.15.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-8_1630949242101_0.7057540612045665", "host": "s3://npm-registry-packages"}}, "2.0.0-9": {"name": "yaml", "version": "2.0.0-9", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-9", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "0099f0645d1ffa686a2c5141b6da340f545d3634", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-9.tgz", "fileCount": 225, "integrity": "sha512-Bf2KowHjyVkIIiGMt7+fbhmlvKOaE8DWuD07bnL4+FQ9sPmEl/5IzGpBpoxPqOaHuyasBjJhyXDcISpJWfhCGw==", "signatures": [{"sig": "MEUCIB9I9bveOuT1E8pp2my4iLDvQ/WVQ/mPaGfdjcq1Lps8AiEAvlrc50bqvI//QnvMhOY9UU7BuVBkKE+D+gi9FPb6Lnk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 617754}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 12"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "6b2b8f1fe76bdc2aa129c818ceb4ddb0ff21e0f9", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es2017 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "17.0.1", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^27.0.1", "tslib": "^2.1.0", "eslint": "^7.20.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^27.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^27.0.1", "@types/node": "^16.9.1", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^3.0.0", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^4.15.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.15.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-9_1636797143657_0.1212236176435546", "host": "s3://npm-registry-packages"}}, "2.0.0-10": {"name": "yaml", "version": "2.0.0-10", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-10", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "d5b59e2d14b8683313a534f2bbc648e211a2753e", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-10.tgz", "fileCount": 228, "integrity": "sha512-FHV8s5ODFFQXX/enJEU2EkanNl1UDBUz8oa4k5Qo/sR+Iq7VmhCDkRMb0/mjJCNeAWQ31W8WV6PYStDE4d9EIw==", "signatures": [{"sig": "MEUCIGS+i89j1aXvQRQVNFj78+lNm/s8TvFVESj0pUubkWOJAiEAsQr3Hdu4dfTPR7tKOrLnaSLTX6ciKZbSXs3T0i6qrds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 633001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzxiECRA9TVsSAnZWagAAw1sP/jW+I3uWzl0wNMUdyJzb\nkOCXNi0nAaOFJxfsRRjiny7kytk0DEPGcTMsR9/dW47ya4OwmVWjZ6w7wAt6\ne99gxYFkKPNQnYedePdR+XyaDY6pIEBGHykDxI+8p4Bp7krmkJkZqnV4KHXo\nK20AvhER7ww+0w6KFUUIjxTFIocvFdkCEifxrKSkKSAEtw5AszwaDwA/XUdA\nLBD7JZ00Sr6t8T5cAe5v0MnS6ZawxXz4YY7zOoDVG2NQFbotyPp/RicL97eU\nMaT/i3doKSm6EDu2ZmNWb4XkKWBDIyTKJDqyecyWs4w2ybxwl6DlUyHwuksE\nECuNZ02kSAJq+ruEOLSB9SGXBHpyYTzuqSrPMlHDGL/isNt+132naF6l0p8d\nRvc+z6pdEP0b2KhAjenghbpuJF0k/OXOCvwENB3pBQiYdG7G48xc0wwh6MMN\n3BxVEc7OxLKWWGbJ2M4PivytqbnIL5dQ2Wi06aIBIXtdFAMm/dDnd4nbBa88\nhz1hCeIIKaqMv9CU+47HzSwpfryy8hDmEGUsNJ2vadrPRwbEoGviCQhTCj+M\npGUnU88YKKyUtZfNahp0wlxQS3skLidYnFJIwPsZiRt2+wO2PDkUbbJ4u0k8\nX8q8VJkq3G/2poyn8IFy7VgUFHnD4FAXi8DP9Ljpr1t7k3XmBHfz++l3oUut\nw6J8\r\n=jHlm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 12"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "b70f07a2b7fc6d3796b13390e1761a72fbabec9f", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "17.3.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^27.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^27.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^27.0.1", "@types/node": "^16.9.1", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^3.0.0", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-10_1640962180170_0.22082213469053258", "host": "s3://npm-registry-packages"}}, "2.0.0-11": {"name": "yaml", "version": "2.0.0-11", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0-11", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "269af42637a41ec1ebf2abb546a28949545f0cbb", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-11.tgz", "fileCount": 226, "integrity": "sha512-5kGSQrzDyjCk0BLuFfjkoUE9vYcoyrwZIZ+GnpOSM9vhkvPjItYiWJ1jpRSo0aU4QmsoNrFwDT4O7XS2UGcBQg==", "signatures": [{"sig": "MEUCIQC7OW+bd5Ot/meYuLgQ3PHQ+RRLTOlilIslLU67iW5LJgIgRJ5V6e1GD/M4SNnz6fSnh3klH4EaIYhZDAHjL9cLXUM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 649241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOY8FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqadg//WIrz3uKtqKNi5ClBKioR8cRpOutOfO11T2zZCP7ahhpUCaVL\r\nyx6qcQUwCO8/BDQcPy69pDy0ZnnjEpZ4wgWGwZDG+kOA17P0Sox70N/9B2ti\r\n7/838rXfbKOI4g/hmb4SnI1dhsMmORu50hZEtdUnX6CEkSralvV7ZYCphAQR\r\nnZpOh7N3dNAG/wpqw8uKmelqnL2pDykZcygMifQ2YrTFg/PDwMFxGYRu/loD\r\nby7zc3WAyMGUk6gfJdkOHxM8bEFw2KaaaellmjmAcru5gTPiyNNdNWvf5zHB\r\nmHoLJhn0z50V+7nlq71sQZP3aaQ1PGYPPPrEDjh+JoNsQU4B/W6xC3os5uiZ\r\nS7maNs8PK00bGtzUzXPgbLnp8V1LySG8AkL6jjOy3DogpiDbm9T2mLDTiau4\r\nRL55Q2s3Fjw/aPg3zrQ02lqHe1On859FWka2xQYZ4g1OWDCp/sBV6NfU7s3W\r\n3nwOSc61k+j6NxCGDGZTA4NPAmjoB7eFzCmSD/UmPtYPSDgHF9MPEZYTScxI\r\n0y1+MQSJBVj1tbcav1YWnGEGZ40h+OyGOi+JtVU7nkHX/inSr7neASGzctSb\r\nPepPVGWY6oZwUqYzQsYT80VutLE9CQ1gTLiEo632XWuE5JhYlY6prqFnp0YL\r\nbjt1ssqilWoILc6IUOzshkzA3GD6vFpTeCI=\r\n=uKYg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 12"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "ab8ae8639b935dd11a3dde81b570db6ee2ec3354", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "17.4.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^27.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^27.0.1", "@types/node": "^12.20.47", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0-11_1647939333001_0.7017416418236644", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "yaml", "version": "2.0.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "cbc588ad58e0cd924cd3f5f2b1a9485103048e25", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.0.tgz", "fileCount": 226, "integrity": "sha512-JbfdlHKGP2Ik9IHylzWlGd4pPK++EU46/IxMykphS2ZKw7a7h+dHNmcXObLgpRDriBY+rpWslldikckX8oruWQ==", "signatures": [{"sig": "MEUCIBWy45dGRfgeY2xmwey6Un64te735S8zgfUHmfwjIivUAiEA+308zty+ZOVdfwe1rr8O1B5VuwJ9xE28ofA9X/7z0eI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 657211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTVOUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTNg//SwHrgeT5cc8wzEM/aV9q0VVzNu+XpmvygF9lD6OrtHV2T3Mq\r\nQADfZIC840jDyCB8eC3nAZZwl0OIl+QbV2sbUWxWkePsuN+uGgd7vsxss9+v\r\njEQYHUxoUEmNKEE+L/E8YxnywLEKsDZELfnwAXTjlx8GjQi4mH7BXoAA/jYO\r\nEZCqtZcJ2kC8SQAqrU2Hajol8pavOWpuw+QUPbwsuDPtQNuXSNOwwrZdJ3Ni\r\nr+I/tg3i1O2SScSvh2HOgRQWYmo3soBwlT/h+jPs6rQmrXNvB3upEQaI0lTl\r\nX0rgfoSgvxuatXTZ/aMvVRyPJmOy4dAFKW2oOYiEx2fpQHsqPLMYDYkkuJLP\r\n5Xhc9G4tQgDAF8BMcjPL3Sk0APYV4ymE6ev2ZsYGvJvJdKWmIiLMZuVEMPeD\r\nbVTGmZej66/b9Pohu0KvghuUcpSSbiOAeMAe1HSx8G6AJjHrXLKTMyznoIo8\r\nxlOluw/IeyEhD7bcQHyYKYBeTzTsbTAHk56+WAXKKIRQJI8Zt9/oxWOuo1y5\r\nZEwrDpCRKZzkMUJ5OfohbjuZjZsm1TNEsu17ZePE3J6LmsFUzqYqDrJXJMjR\r\npJ0oVnRyRuuCcBUJAg4BavjoZEEhQJZ3qBJso92cG/fKzKqhtQTEQEn7tBtW\r\noSvhM6t985yGvSqnhVj3kdxFGqj37d4vctI=\r\n=BeUm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "85aa802a406dc5b8ec69723e22da7b7fe07ff9c6", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "17.4.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^27.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^27.0.1", "@types/node": "^12.20.47", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.0_1649234836114_0.7358295768032741", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "yaml", "version": "2.0.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.0.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "71886d6021f3da28169dbefde78d4dd0f8d83650", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.0.1.tgz", "fileCount": 226, "integrity": "sha512-1NpAYQ3wjzIlMs0mgdBmYzLkFgWBIWrzYVDYfrixhoFNNgJ444/jT2kUT2sicRbJES3oQYRZugjB6Ro8SjKeFg==", "signatures": [{"sig": "MEQCIDjpD4ZWxj396hhInyo1CtjqcjW8BOdjUH81HB6m6eXtAiBJypDoFaVl/81JEMaWZ9tZUInbwIl6Dxc57NOL37Frgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 657731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWdd2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkkQ//fm/pEi6OYqDwD7C5WG9yyvaoGnZzoR9HlxJ8jfP3VcaSanYK\r\nrhZQZ4sWOxAsIjNwjlkzVQmxDoYnH0fSyBnZBJ7p/qfJ0MJUXVkT2nrD1XaR\r\nXx58BvhSPCnZEa+mmYFxpRparJQq/ee5ROtJyQhkxYgbg6UIP4zVzR3M+3zV\r\nf+35bnB5McgJt8yaV7NSqplYFX4YlkzLNo/iRB2d8twj0qHhq6FXiG8Im6d1\r\nT/9WZxjgLchMj+RHQZ6T3dlO0x1Yuo7KCEepI1VrSmX5jCB35myaFhDI8utU\r\nC88QOrsPNe20DZMWtf5AeThkX3k/+P1t28e4Y/ePjaVM4lHfmuT68nbWFcaw\r\n/1FE7S7kuXIrLbNThBiNeZPIXoxDGzNsqe5vIuBGl5QEhRv12fZ+YaKWt9Da\r\n1GDDZa/3orF4n6tONhMEb/JYmgqTrvxAtLjMTMM9AzltHsvbMNgpWWleiSuM\r\nGfiChMtj7nB4D62jRpst8JYfTbYRBQMuAq4iTFgYNX/SHLxGl8aIVIg8s/we\r\nNgGOsEsvAgoxFNVt0gPVJ3L+DLKMmSS4xbYh+cayhPU9j439JlKoJD1FhOzW\r\nr550nXjI7jfQcLjxfYrWcSN7NfdIqH36oVAHd0mfBDKJaKiE/Oqdi3DJG6wD\r\n6874YnhC9itUFh7Vr7zpsnZSth6Z0DsoI/s=\r\n=Gd3v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "6e969ca6d1fad40cffbdd107681ba560879fc1ff", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "17.8.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^27.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^27.0.1", "@types/node": "^12.20.47", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.0.1_1650055030736_0.038965856380512864", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "yaml", "version": "2.1.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.1.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "96ba62ff4dd990c0eb16bd96c6254a085d288b80", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.1.0.tgz", "fileCount": 226, "integrity": "sha512-OuAINfTsoJrY5H7CBWnKZhX6nZciXBydrMtTHr1dC4nP40X5jyTIVlogZHxSlVZM8zSgXRfgZGsaHF4+pV+JRw==", "signatures": [{"sig": "MEUCIAh40PK9vpIKCyjVYXs1QJewyqCkhnnk7Bm640K2VBLpAiEAiP3zubk+IU7qf2HxmQck5/I8nj4gqQQnzw6jMyq8KKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 648556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJif3krACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoA1hAAmgkgkbJUSVWflfYzU4XeReS+KpcOgFE64jlxgqi3p9oMvTLI\r\nMKlWaUSdYoLwPOW7Kf2kj/OPdd2fUVOeR5PGaNHIrJ1EjDalWQRQKe1rMYWF\r\nc5I2ys0hjPiAI6W05O2oG+TwqRNiChP6RRfEkc17IoEIaM6s9rrCaxy/pCba\r\nB0DnFxn3yonfMSh/0FU+6AnEvpPd2cjrbc8m6ruaCYRpqG4i2Bca6bo2N+2X\r\nfFENCBhkCyblF/DRQRB13WCzq3MklR2z41nGXgbhCqSHD9eYkhW5QEdOO3ER\r\nzXsCYpESjStwEVyGP2bPeULUeuunDaGk0IPwv24ql2bTfknj9E65cWqQi+qo\r\nkVIeI1MWYepJ198KNzD1CwBfYEianqraQMztZ64W6ULFOORzCtiRkx+7JZzS\r\n7kDXt+aeocQFtRoWgY1JDdAoQ5lPXrmZgiVvmn6kiW034D5lbXaLBVreuQjs\r\n4inEwQl5WD5EHHZgiaORjqRkfU+1/auSXf56W18eXTKofxj+X5MtP4R9eTEW\r\nvgMx9os6rXHiVEB0O9uykvxFmXyBOeX4QNBl+1oZHhWJjGVbxBD25omFwAMo\r\n7g6y3iDJZqY8BaoWsMyElYNw8oryrOar0YV3HcaTjrImTSup2VA5qN7Tp8c8\r\nPfHafeGiGLF8l9VYDtB5qt6K2uJYIEW9+68=\r\n=nJJX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "c80d4c2ba972e53cc60ddd2d7136446bd9dae740", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "18.0.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.0", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^28.1.0", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^27.0.1", "@types/node": "^12.20.47", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.1.0_1652521258910_0.5815607687292765", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "yaml", "version": "2.1.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.1.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "1e06fb4ca46e60d9da07e4f786ea370ed3c3cfec", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.1.1.tgz", "fileCount": 226, "integrity": "sha512-o96x3OPo8GjWeSLF+wOAbrPfhFOGY0W00GNaxCDv+9hkcDJEnev1yh8S7pgHF0ik6zc8sQLuL8hjHjJULZp8bw==", "signatures": [{"sig": "MEUCIQD0oxHZGZNuh9ZntSjqzyFh2ubXBbPITzuttudzE1gPzQIgXTnd9Pw2vZjE0M2Inv7yT5j1M4/0qO5BUbMlp4P9LOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 648639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJik/MkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHCw/+K392tfnXyE+MjUIsB+SbmG1YEiptDoVXT7QTvmtev4gtDC15\r\nEZBeGZRruewuScB4kYpHKPma4xLV93LOIUPB4Ti/leGZE2DqJDX7B+4AHZbg\r\nz20bgVpDhDA+h4ToI9Gpf7T36RSmhIf9pqIKYHkAjipnwr3aD9HssFH9FDaU\r\nXZyyFUi3nZMS6e1sjL9fSOG/jVctt/m0m+w/BZ2bd09+fuB1wX9CtUiSWU4M\r\nreAC5OFo7A6JMDzx0V5hyghbAsr3upxoDMoNN9vyapZDlKwCCFnlWN2lAQoB\r\nrgiSKxCvSQFZh7RIsR1Y/GGgw5AXLe6CDQ5c28kwWbkV91aZrB7bZQK6bTmF\r\nHRlTCaAFJNVRQJVWGOX8JWUOSvww9q4x51tmc50+ugHh8hvczFcIq+Q0o+cf\r\npTIgxzxfSAbXYNLgjjbZV0X1OpSlpPGAHKKy5bI9Jo3AJHTdR62x7uWici66\r\n2tMZZN2H9P7t2sh9Vyj3GhfTbnzEm5aPIhl31u5uZwMqpnsj9vfYkPNsLcF5\r\n9K0OJptHUl7tTm20ZhV8tam7ICZ61a/LVLoFGLnyzGCJQKwODW+0Df+FP9cb\r\nNpJzwoHRVfaRe4iOj6dHHSdH6eZ4o56P2Is2W/P5kQtZfschDbgw/pnnKGym\r\njsFODEMm5pzRQK15IQONY/U4U5+nZNjZOLA=\r\n=D0ai\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "96c699375abf6980ea5158e6ed2e86cff2927636", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "18.0.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.0", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^28.1.0", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^27.0.1", "@types/node": "^12.20.47", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.1.1_1653863203891_0.4705508587556806", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "yaml", "version": "2.1.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.1.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "eb0f535eb309811b60276a9cc8c02af4355db420", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.1.2.tgz", "fileCount": 226, "integrity": "sha512-VSdf2/K3FqAetooKQv45Hcu6sA00aDgWZeGcG6V9IYJnVLTnb6988Tie79K5nx2vK7cEpf+yW8Oy+7iPAbdiHA==", "signatures": [{"sig": "MEUCIF9fitZUo7MWnBfaxDQJVU524Wf0X1HhdysCNLclN+qdAiEA+Dm1mpmJlec0fnkfVzQCeYW5so9HoXPhisQmrUqn6tw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 648829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOcEDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrbuw/9HAm3J/nVWovo+foePg5l0THqOWIGzfxpeIyys1lpF3hIzh8i\r\nzSi9rX9+PF6WLjNPb3cnHhH0BpRkHBbhtQGwJWxR5Q5dLLOxKLBZuEkrACe3\r\ni4LOmBL6npgV95CxPRt0ha5jnusfoLgmXRZyaCjc3DM6GPUEpiUX9jw8D+ys\r\nbtp63a7zDGYSOTEnn3xNBu0g+qcGVgbQcCLe+FphGK7h1nN6uCZwgMHXcjDi\r\n0OfA+2xnBOZgz3c8jsNtvoWMeG4OB+W7aq7sUFru2Wla6uAAOEJ3aBvK3k3s\r\nXHtOeFCPJnAWK6QCgcL7v+znv0MSLTECAn732M5VGlRKDWohoEdT+0AY6MSk\r\nJQZgT4Tosij/fmZvK+j92hn843D+Tuj0woTn57MnlEG351sPakpihJq/EIAq\r\nqky9jomQKlfXva/VD71m+Vj9DMsD7fdnMMQztCmM0FOyMUn3UjrkOnv6JzY+\r\nT6yxKupkmKH6cGfuyivL4TT5Mg9ARexmseVE59ViwUjQx45OdLrCpJlamOpa\r\n2yCUmATSiMRUrqk5+TudzNxilcMwVhhqApNJNVh+aHgJ9g5gACpQ4HnZjsMN\r\nnDSLK3MCqfE2tkjQo5SiFHymIl72luSeI429Hb5/1DDprwdnRYFGTA11lfxQ\r\nJruh0a7rcBH5+AK5YosahJqTAs8F2dP8dqI=\r\n=jjXp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "acb5f47ae20d597b565a014866bfa44ebc67cffa", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "18.8.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^28.1.8", "@types/node": "^12.20.47", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.1.2_1664729347020_0.785085770859173", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "yaml", "version": "2.1.3", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.1.3", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "9b3a4c8aff9821b696275c79a8bee8399d945207", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.1.3.tgz", "fileCount": 226, "integrity": "sha512-AacA8nRULjKMX2DvWvOAdBZMOfQlypSFkjcOcu9FalllIDJ1kvlREzcdIZmidQUqqeMv7jorHjq2HlLv/+c2lg==", "signatures": [{"sig": "MEYCIQDOCJk3F4R0uvdGgkwiNHd3AeHpB+wYz+bgZpoMM1oYhgIhAMRwikGs9o2Qe1JilVSFYXpRR98EtngT1zL89FADmmdS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 649153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPTMCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhHA/+Iw6m2Tl/kDABvSwCW3xDk4sSQ8OnYgUixhYwuXc1yOjM4tOM\r\n+RTAAigjC6vmCmVsD6tR/SjHx3Gra83Z+SAd21wR+OEMPB9jKFTYDSV9Hr7M\r\nr+Poox3NJ3SBeFQmKFVjUDhdRxBtYAw5U0l0NLOI2AvQaXn+7gjrRnFSylms\r\nSVYPEwD/Sc68qnbnAygNgRL+UGBbZZlGFZwEwYr2OOGgMSNoQz1n0P5PJQEY\r\n7IN4MrmxuSBAwWPO74tvXrcZODNGV+7VDC37j9qPtohyqPjIYdUpQtflI1aZ\r\nw6c6uvJAXrFYvK0uJbvTYrLsSHTwknBHfcjof/medHzFFUIBHcAtOQ1KbU0N\r\nHvkCQVOi9VVgcy+7bGvFTKZAQmrItIkyV1BInZ/nwNsIA2i+ZataupdAWVN7\r\ni0lpQ8GJdYPWVzY6Q7/Srg1WZbhx2nQMbFonzwt42Rxw/x0vgxvv91aYzsxt\r\nauAKEEDRxrSq3MnmX7EpBRCBXtAAlVCVW996ByHO82EvrhkpVvpLjuMFjRJL\r\nXIqC9tKHLVHQMjTZyR6BkuvATrEqh2f8gGP4OD6xhPaUEFG0BytSJSA+ZkTl\r\nU7xhtLd5wGB1Ja3nGJBxVu5OvJogg0K0HAxfrAVqvh1G7D4OMrQ+6TW1wR8n\r\nf8u865O4QOICUjrTJsvfkVMjGvbBJiQwNts=\r\n=XEK9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "8e7e57f2ed478213509af2e97bbe7b2395ad5a91", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.js", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.js", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "18.8.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^2.38.2", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^28.1.8", "@types/node": "^12.20.47", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^5.2.3", "@rollup/plugin-replace": "^4.0.0", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.1.3_1664955137815_0.8699750849046803", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "yaml", "version": "2.2.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.2.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "882c762992888b4144bffdec5745df340627fdd3", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.2.0.tgz", "fileCount": 226, "integrity": "sha512-auf7Gi6QwO7HW//GA9seGvTXVGWl1CM/ADWh1+RxtXr6XOxnT65ovDl9fTi4e0monEyJxCHqDpF6QnFDXmJE4g==", "signatures": [{"sig": "MEUCIQDMF5FPGFFkVZ7srOpz6haU5l6X1avkzzMm9YnRw8k+VAIgN9uzkd2xAox7Yg592ifysKjMEIee1en4bvGdY1wTnuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 651110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjovyvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmob1w//Q2xPtikuF23J24hwLja0NO6Zroaislzqmz/+6ea7JZmlhMpo\r\nrGLuT0dd/v/AfBu3aEqxIDm/9u/Gmxr/Why7Ovxp5Ag2QW2LlZ7dqddeUVd8\r\nnOshC/BqeNm2ADXBPFDR+VymRVKFOQIdH2WJniCKFcP3180/k/g820o9O72G\r\nFBuq4vThNnZ9zd74i+OSjz/MXG/Vnk8XVPMxYadUQZE8PWwDlCGcgnkIDLjm\r\nLTtmLmtgxmoiqY5yk2Y5W28zzlFssNbBt5242bjPQRoXwhE5md9fP1HfQgU+\r\ndAS/Fm6lKzeUWEU5zU2p9QL20Xs38tI96SHk3Meo4nO7It/RWtg8EcQ56I7M\r\nHmUm1Jdn9cczeHBZwzGn36TMZ+jSMclwHrBWqFlgPwcL0N28gX37xZS58QgA\r\ngup86mMQURW8yA/QPOoHybraCcfH3/cRBc0dtIvT5nhYXcASbEGOo/+nOkYg\r\nboQR2Tqxr5SqWOmD6n3h7WnNEPF3nxLg1qCVFKbiVFDxH5W6xo+jZ95RA7QA\r\nXzCY0cjOgomV9edNGxigk5yeGwD+mnmgTwxH8kvfm0mTkp8dmJqRlCzu957j\r\n9F7VYaaBGunj+OApPMW9O0vyaR8dlvx1X4nQGH2AV8ozgqb+Acvk6gnZZMTk\r\naQKihrIFvWMo+YZaQGKTKXoqtLRgjZbuyO4=\r\n=CNmn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "35764085bec81eb075ad16241508de0934ec3477", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.1.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^10.0.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.2.0_1671625903718_0.31332421603222826", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "yaml", "version": "2.2.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.2.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "3014bf0482dcd15147aa8e56109ce8632cd60ce4", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.2.1.tgz", "fileCount": 226, "integrity": "sha512-e0WHiYql7+9wr4cWMx3TVQrNwejKaEe7/rHNmQmqRjazfOP5W8PB6Jpebb5o6fIapbz9o9+2ipcaTM2ZwDI6lw==", "signatures": [{"sig": "MEQCIHDUy7tBE2G8V3uAp6DZsjJFiIHgXJytBF5DU5zu8/6hAiBMmcQYVz8zM0wl8R6VvQCWeERX7LnzCyaC87Hxvm2PiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 651418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrrY1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEsw/9EI/tLxEh28pGjnn5gSuKGnR/1jAAJDx1RLuhh2ywS5TQTWZ8\r\nL/FA0+q8dPE9W+Gdsdla2JF0KevnjQaQ7EdqQsbNhQWaLsddIKYyuIKcSrXN\r\nSTy+UGAF5YeqLwol0lxFhgFgsj0we1DmMLlOdCTaGPm/61hwgn4EqfZArzWj\r\nf3Eokjlh2HI6eB6R6Zgzs7tMbdNAv4IXmqeTyeiPFaN286XL5bqQX5Am0YdE\r\nxgT5tyPUBqOCiEfz87oqMXSUa7eK1fHQON24IW/TEWCkPT0AsGc4bpNLNO+S\r\n0R5OgWGbKea5LqQFdTF8SXjpdAsKuAW7EacFZO+IaYCS59CXU+PI8iG6mueN\r\nqpes6Ra2tSTaqWLqtdCuuHxZPw192bB7vHYBxAHUnhyJ4Rv9YxxpKQA/Gq0p\r\nDyZw6wSYP/CS1zvyWgSTZ05zbzktGWlBfcQr4A44gnwwa/aXvkI3vmNPa1e3\r\nLQg7Ydi5Jvk+vYpkmHthxo8065XOjVzarQta6QxI1a5ok+pEQtxRdwm4NCfi\r\npgAzTg+tgzcgpRddDE97PZrCKppx5MiNDGZ/Udqs4GqV6jc3a2CjKX0e+5MG\r\nsrO3CFCVqksTEqLXYUCuFs88YgEEWz8daMIv5rkjVJp6WPZinvgHu01VMcEg\r\nfmpf2hq/gLiIwDJJnDDfMipRcJq7GuA6gwo=\r\n=yBEa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "c914dcc9dd19c9c4da065fbbc4c920f244304dec", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.1.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^10.0.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.2.1_1672394293215_0.47164168289875663", "host": "s3://npm-registry-packages"}}, "2.3.0-0": {"name": "yaml", "version": "2.3.0-0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.0-0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "0638f13167d2e82b25173d0192e588ead1c32d68", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.0-0.tgz", "fileCount": 225, "integrity": "sha512-XBMVJHQ9YI5kPrf+yMD0P3bXHjEA7M8yYsJhqh8pG7J3fh1Gg/hDoAnvVorqO4SNBVblsLfs2qfJ7Kl+7pknZQ==", "signatures": [{"sig": "MEQCIA0Vhr3pY4XmzVkmDn+Y/YulVAvDh6VH25YB7LFmFSZqAiAtyT3jV8lY91cp8QDgG2pdL5e4m5WbAfY5SiMvZzTFsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 654029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDHq0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYFBAAjhjxEpECvKH/ZKTWhMiGae0CIGdJmXeTOOEdEW96rN9hw/+1\r\nbOL2+xod8fk2Q8nBUEJYlatzPgP9hGpNtMJTpyVUlUhFY3ATdvR90FeC8kjp\r\nJStT9fn/UQBnX2bbSB1KHngnUG8xkTQyG3NjXmXvpbBBV/xP1jrZHKPc6Zyd\r\naQvp0LAeA/WrzoJqd2TFKgrPTBxJsMBf52UmQPj6hzIl0LROERpWnwbZVk8+\r\nL9O66tME4mqCWyuhF5rXOm4t+7H/iidCuXsLHBFVae710dBKn5kUMjl3kVnL\r\nMqWnS+MCIeDuHwS7L9Pr6321SuD2ymD5b/kDHfug8zjh17ZQe23jnnDunatF\r\nbsPB6Paj6lT3q8PaIE61Cx+9wriXFnfBIegiNI5IK+Lk56+PxXjxDZcVXbeR\r\nFO/HXfFUC7e6+iZsf8cH8alcDZwT86/9Xn1pGcTGJjzOEYdmec+cNBN2EyET\r\nmqewhFUAKUJfPhk/b66g7ToGGVit2ktHwSBFp9NJxG/0tQ+LlM6l/UL7a+S5\r\nZl2WsVoXlzWOB5dnoqHKbwnAtsbU+M9OwpKD6xknF8CD/Q1btBphiPZXtgNS\r\nEHb998Qf0MmwsqmUcKORYtSF/mHQQ3swub9wp+9jWRaKYdnGaV3ErfnNW7qS\r\nMwfJuegTmv8GnkcTRux5RTjsrNyIQT/78S0=\r\n=g64d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "72b107e2907229fe18dde084b8703e6231ac843b", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.1.0", "browserslist": "defaults, not ie 11", "typesVersions": {"*": {"*": ["dist/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^10.0.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.0-0_1678539444211_0.9619085793232565", "host": "s3://npm-registry-packages"}}, "2.3.0-1": {"name": "yaml", "version": "2.3.0-1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.0-1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "9dd8a1b2d0bbf780dee1be83e0e20ba7ee953d0b", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.0-1.tgz", "fileCount": 228, "integrity": "sha512-nNIWKeoO+BCmuQ3y3/S55fduk1BbCmDu1H3S3NKg9c2bv3cup5TYG6If2181SWbWmSVBV1o78ysoIzV8dxqfcA==", "signatures": [{"sig": "MEYCIQC4JPD5NyoYVVsldKTMte6dP2mj1oZRhf84awL8ixkDkQIhAIYuE2Y4YqGep9sS5+gZyoTSCiNt9oN2ucIQDgme6dln", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 658130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkK99sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+Ig//RSLBrWF0NeJDQgOLtRCjRxA1I0CN4c/fWiF1E+qYOpIXy+Nw\r\nNSSTJH4QRs/Mz/Vct/EXIGN2R9QxZzmPDms9jORc9o98kLr0Nq+Pnh/ysfNR\r\nrDpiZFLeZ1nDCYM1q2gChclGLhQU8IDX7dNeJqIZNxV5IWciIK4b8MtY1qKF\r\nHY9mCMMCiR+wJ1DAWs3Bmf6AVG+vD4NJysuICYeAxqU5EeWyf9ZFqWJC9wB9\r\njWaMw+A2ElCdmnSL1alnsGGR3LCpnilIvuFZhuvvVKPkF5feN5axEwz/6uLL\r\nYclNwWjJAjgjCfd5x8fOdGuldoWms7KTulG8vSQHYSk3akmRTIDF93dFyzWJ\r\nf7Lc8XtMFMWlE6gMOw/dvz5y42ROI3kyo/EOQ/+4RPaIQLfeMAe/Tsl5XnsS\r\nwEysdnVI3HGOjL6YUgnxxxSiXPLI0zoyQVEuVtUGGjkhMRT4J2L6xuV/BSSL\r\ntQYQPSr4su5InDsdggsRL2UU6TMrUa8YG+rVFnm+CVRIXQ9tLneJEDf6SfF/\r\nny4vZQd2FErD9bjhtbMWudqzGJ3siRICAP46WlykzvYAXcwdsTsNHIn7dY7v\r\nw1skSbrnO0McS6h/aUZPu+VbgLn2gcqI2IvrStF1CoTA37UtB2/634rg935Z\r\nYcjkfpWjG2i2okiuLbDxFzxo2Oj/CbxJHPI=\r\n=ab5E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"npm": ">= 7", "node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "84b38114113d49abea3b80f1b4f3be680d736416", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.8.1", "browserslist": "defaults, not ie 11", "typesVersions": {"*": {"*": ["dist/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.0-1_1680596843931_0.21392240450844513", "host": "s3://npm-registry-packages"}}, "2.3.0-2": {"name": "yaml", "version": "2.3.0-2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.0-2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "7080325d8263dddb9290a11de39b0b5c18c9f39a", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.0-2.tgz", "fileCount": 228, "integrity": "sha512-mUUole/YAG8a8cx2qA1HtIEL+NqYHlRz1SpR4KNM+6KBcpSW5W6D6mZ+xCffqTodCxUzV6CXYHv5zH09AjrZnQ==", "signatures": [{"sig": "MEYCIQCw+vtbH3hbOxjQlX74MoH0cT6ee0CK2rYyxqmc7iZLbgIhAJB18oUgMFJnBlVNkbI23d54OxPksxDC6FbeuZSn0qtm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 658418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkORZNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpB7Q/7B+XPO0Z5Mw/A1ixZFvBCliiDIuPPZ+yOlb30wabUo1sCxz0F\r\nXRiwdOKJ1LzIb4IirstqLRNJ2JBq/BXitIJ/GtUUOc5sT8AVZpopW9wWMpCc\r\nCBtJinD4uNRJSkSCwFSEOntapL0x+i+THT0ui/ifihk+7yRTae8QpzrWucEO\r\n4wO8ona+F+4bfjbt/fUuXqzNkR7HUCxfz9DQ4Rv/Ak9fCWeh+FBIsGZzAGj5\r\nZ74XbqrX09YOiRr6CwOZszUYeD3TUFVLehcQfNKSAgreeVjQVBJbqtWIZuWs\r\nm3oSNpugSPdDIOHhzuswV+OdGPyPsqH+v2wpWtI6ECukHANxsOU5mHzO2YbL\r\nrRyU4Ekxxe5ZAUEoSv9f3sEVqTDJ9Vs2Dov9Ks7u30GImmwSnvFa0Up/ebnm\r\nnIZDE88O5nJAFSZSo7cJ1BGVo3+wKmiyvuPuO8unJ8RrHnMef9LctiCrXZcY\r\neGcWI6uhj7UEkTTAKNWDZMTynosR8FcKDZ6gcVqm5E26KAUWsmahDvfoSV7W\r\nygDf5aWjxybNCSnifxJmAoEOM2hfUTlTMw315MtUVlReipkSXP6hyCHg4pbJ\r\nTn3r2FEt5PUZbvP2PxJAgNw3889/73huGPf3kgTnlr/U+2NOlz0eEl0vaBFp\r\n5XuWBAa6TNLy+hpOU3gKUKlQWwjwwQDgs+s=\r\n=oCMZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"npm": ">= 7", "node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "a2afb000c399a9a00b4223113c3cf60fc76fc443", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.8.1", "browserslist": "defaults, not ie 11", "typesVersions": {"*": {"*": ["dist/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.0-2_1681462860790_0.05139397830136705", "host": "s3://npm-registry-packages"}}, "2.3.0-3": {"name": "yaml", "version": "2.3.0-3", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.0-3", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "b68a2045e4192a63be99d1abdfd515cb6dfc228f", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.0-3.tgz", "fileCount": 228, "integrity": "sha512-r9pu/CNeaefF0QtpSUlfmhdrLKQS4mMKwXleeVcaH5tAoPGMqX2qrNPtiyRzS8jJZTzmul5/FbRAb9wnh0Ux8g==", "signatures": [{"sig": "MEUCIEqY/OTnEC7Ccdjrc7+xJ+ZgWHJ4IsaPGCJ7vlxTOHxUAiEAzlocE39tA/awuwU/URBWUj2dUuFlxktDByC/Xr1cYms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 658337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkORlZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoH/hAAoUrIWhPC/g4rYhxGE2YBtFlkLzQwpjBxyEeFelXe8EODOxeC\r\nMxVxzGy+jxCoZtLTm6MsWFxCi0u8DBc7/y9bKDAqpSfog9FmMb7aYomg0y0g\r\nPEoWnvPj8UZPWSO47OXP2kM7byz6TdtfrKPu93Ec52OchFMA4ro87zGQiXce\r\nKxhTcTn20L+/EoiRV1Dr5hYadDViVSr20hjGUSJ1lxtHVihb5/yd8q3/5Dzf\r\ne9a4lM7xo95FeTF2J7bDxldbla6NYRtX1HUxbY7BfCOUfKuPFnGnvLaoeDWg\r\nFU+XJQ5M0nA6IQ3Mq8mVXJFBH6nfVoS0Tng0TAWf7H8LQ8/C/7rFjIZdO2uP\r\nijdO0W01xP7O2D9iHbOI6iwHbPDdOopn1j3MB23rO4BsWNIfwrBIoCbr1r/L\r\n1CEvAULDYYIjSdota+Sn2X/Pc3+vPMJrVg8ETAVu+aiELL8bAp3DhBeSKNKa\r\n8+JTbywbbxiIqInUY+ZkHOx46q/+akcwf+nDn5wgkQnlubzNxgzRcXYe5SWJ\r\nwtinzboltS9UaS8f0NIHY9HnO/x5Ammc/tweKkvAyaUCKlr4OfENZfa01jLb\r\nRlPxH5/ltE3ySyIw+GKGfXWTM9WieWcQLELGVdNPjBq6DpOv1g9BK6C4a6+t\r\nhdg5yH3pT04tv0kXPL79CSnBRxD0MUqXAO0=\r\n=D6b8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"npm": ">= 7", "node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "55e99ab0342b754c509ce6988389c681032f74f5", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.8.1", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.0-3_1681463641559_0.788746694608363", "host": "s3://npm-registry-packages"}}, "2.3.0-4": {"name": "yaml", "version": "2.3.0-4", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.0-4", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "861e97ceefb3b98dcc455f20565b91bcdaf5bcec", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.0-4.tgz", "fileCount": 228, "integrity": "sha512-U7vZk3D8egK0Mu/L26P5hoRPBkj/dhJjvqW/niw1xqPkNAVwOleiOtbgr0HuFini+1AxM1w5xT7dwCLI7atx0g==", "signatures": [{"sig": "MEUCIQDUiZJ2fF9roMtSxNXvGSi0yTls3FE8B/aXcojRQkn6DwIgGrtFbAUd/FZwAHf9TQr8CRYG+drHkvIgxGgQk75XlFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 658520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPmEkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqc4A/9FU5MiSdSSuXBAxsrok8JyQrVd8MaTUBZK8uk2b3qDMR3yAJ0\r\n6s8tKqIiXp+JMWclUOikXGSqQq3uTwKxR04xEDBmrEwOMZjCMktXBZqxBdoj\r\n0JXgV0UtovRXyThQUZyLGzwTQ/15RmjA3jg4m9101Qx7UPGQ767NECJyMqpr\r\n/8b/ElB/wQpK+pnFqrbHeMrhN++hAOF1PcqstV2/OQjjgQ72WVX5exgNiPwt\r\n+xEYuHtoT0F2h6edhXdAlkk0P7i85A2tvajg2pwSjNoCbZEfPnd9qMLj+9z9\r\nTn7CeAI7Io1eajxb6n6ZkRLks5IJrkqEsX4apfnyWxvpxTVlPszlCGsWxfBy\r\nCdfHawIEMkLlCKU31a55dEps8PTsHYAhMcUoyeyNByJaVjgJiEUp5rGg7v1v\r\ns7uHY9sB6rHxVQh1K2qUEr54tc3Lpw2jy4pVin3Rf/dr6uW5+VRGBxtgTwEn\r\naFdOJDt5Psct+H+b6z90axhgsn5JCGa+9Un9EtpEt4VjxZhGXONT+bN4M61U\r\nuRV0QH9E4LSzLvO866u/Gajf3d6HSZhpBMMsZNiz4CI3nwuDWI2iNIFPAr9C\r\nfW0OpS2c0hRhcsteq3FvW4HaVXQEeh4RU2vbrNGvh3BBLRZKgsWcUX7Vj0mx\r\n2lfqXkgGwzN6RU/yJvD/LFrpiUz2AvwdBaY=\r\n=dYSt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"npm": ">= 7", "node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "ffdd2fbf0df059ea3af5929533d316783f339fa2", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.8.1", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.0-4_1681809700499_0.140187981559867", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "yaml", "version": "2.2.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.2.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "ec551ef37326e6d42872dad1970300f8eb83a073", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.2.2.tgz", "fileCount": 226, "integrity": "sha512-CBKFWExMn46Foo4cldiChEzn7S7SRV+wqiluAb6xmueD/fGyRHIhX8m14vVGgeFWjN540nKCNVj6P21eQjgTuA==", "signatures": [{"sig": "MEUCIQCmyeKnlHVmJIPZEAinPP56ro+yAhe1/nZK5YWGFZllIQIgNjsnnKxfV9x6yHv2P4NBETJf/u0qLxtIMu8Ljjtvtkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 651576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRn3WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8PQ//V/Z7YlEMtjGLPA5yMPIxiImM7bV/yUrzvqb/DCSXiouBJrf1\r\nS67gD937Z9Op3f9RVa9zVOucMTv09YoohS0WxFeuK1VQDr7OVdVHCUdxz7w+\r\nJClJ4/QM2pimYWVazcPhd6xyV0k2iBPa33JpCKF96j6KLhcfvWkTxfnIUEjV\r\nZpbITgIOZ1M0a3OW7CGAuLozzfZMMEEtcboxpNOSwvLadQtPUpNO+f2LR8FZ\r\nxNc9mi9qf+wlszebG7T0QxqRroOJdINBcuewfUVwZ7Vhf5fdW3B3cO6/mjqt\r\npPNbAjWpCkimtLQ9W6sJRX2hLlsS8vBRS2sbS3HVrA8iXd4Le7o7Kw1SfS5u\r\nHGkQKe9mPq0neHKrgAzExTNtkWEDRXBIFQE7fEveTPHgWjATLzmo3Buxhp0b\r\nFqpoepFLz9MffwPc2iNcuIXn2NA+9FwgZg1hXNH5l843KwXXuDViJmqR3Ffb\r\njDvHon4NsEcjb4mCppp7PiB91QsdXR3p4zHySnXQTdxDF0hqjJsy3onYCCxh\r\nG4FbSfGHWPY67Jmo4Np7Ha1LBnr+jCreYPsESe7tImS280ruJUa95ijl/ngL\r\nGz8QAM+d3xBiMW+IR3YJBFmrrOtiKZvoCIUVRMpLtwg+hx+KN8qSesqhYuJ2\r\nqcUGEsZSnhW8Jrn9C0chRSQa6Sq7o92Xfto=\r\n=0DzZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "f21fa455b8bb08aa3b20f07968aa923544635c2d", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.8.1", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^4.3.5", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^10.0.1", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.2.2_1682341333858_0.8924048866185064", "host": "s3://npm-registry-packages"}}, "2.3.0-5": {"name": "yaml", "version": "2.3.0-5", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.0-5", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "52a1ff3ddf29567f27029045eeaa55edc07585b5", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.0-5.tgz", "fileCount": 228, "integrity": "sha512-Y2NEcp/A4OjsMxDenJrPqucy/ufkbfQu7RRjKYWLfyATH+oGJKkPWp/4mnHPCnBpIUH29P/F8uemt/g0xB3VQg==", "signatures": [{"sig": "MEMCHzCH1RQho4pnU6V4OdabAeeJYLbqNssmd6PNWyxKDNICIEIqP6PmN5i2M4YszJLwisOXlgvTeRQE5Qn8uLgTQDhi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 663241}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"npm": ">= 7", "node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "96daea578b9432a8293c1f1e52f461e1ca90f453", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.8.1", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.0-5_1683394737908_0.9256431153315277", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "yaml", "version": "2.3.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "47ebe58ee718f772ce65862beb1db816210589a0", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.0.tgz", "fileCount": 228, "integrity": "sha512-8/1wgzdKc7bc9E6my5wZjmdavHLvO/QOmLG1FBugblEvY4IXrLjlViIOmL24HthU042lWTDRO90Fz1Yp66UnMw==", "signatures": [{"sig": "MEYCIQCjYjYOGoX33lnrObHBad8a/cOsxTm7VzizqcYqMUc3ewIhAO2JolCyZUOI9QYO4z0IAgYt4adJgEMuBKhycZKfCLrb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 660486}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"npm": ">= 7", "node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "431ce2231600485505673b76fc1709fb66213b97", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.8.1", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.0_1684840582064_0.16773385937846919", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "yaml", "version": "2.3.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "02fe0975d23cd441242aa7204e09fc28ac2ac33b", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.1.tgz", "fileCount": 228, "integrity": "sha512-2eHWfjaoXgTBC2jNM1LRef62VQa0umtvRiDSk6HSzW7RvS5YtkabJrwYLLEKWBc8a5U2PTSCs+dJjUTJdlHsWQ==", "signatures": [{"sig": "MEUCID1RBRZDw+1A2MSvhgnRgPVFLD33QjRpb9uVASaQnZexAiEAuw00zcyhqnEZaT1AOZhA1bkcrgqh9KpxBdiN/nFmcvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 660467}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "a442e95ad6bb05615755aa2015c790e3600b3ab5", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.8.1", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^2.2.1", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^8.1.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.3.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.1_1685097588498_0.01671935683675141", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "yaml", "version": "2.3.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "f522db4313c671a0ca963a75670f1c12ea909144", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.2.tgz", "fileCount": 228, "integrity": "sha512-N/lyzTPaJasoDmfV7YTrYCI0G/3ivm/9wdG0aHuheKowWQwGTsK0Eoiw6utmzAnI6pkJa0DUVygvp3spqqEKXg==", "signatures": [{"sig": "MEQCIA/P4MOa/A5oMydeBbcS3GnbISXQzNOZaTAOoYkwliJLAiA82NkRxRfm9As7J7NrS2OLXM7ro3fGhre+w0gcO5a1dQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 660819}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "a4d8569c2139eee8a0324b6b861283f7b3b508f8", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "19.8.1", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^3.0.2", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^6.4.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^6.4.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.2_1693234854612_0.6336227149566043", "host": "s3://npm-registry-packages"}}, "2.3.3": {"name": "yaml", "version": "2.3.3", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.3", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "01f6d18ef036446340007db8e016810e5d64aad9", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.3.tgz", "fileCount": 228, "integrity": "sha512-zw0VAJxgeZ6+++/su5AFoqBbZbrEakwu+X0M5HmcwUiBL7AzcuPKjj5we4xfQLp78LkEMpD0cOnUhmgOVy3KdQ==", "signatures": [{"sig": "MEUCIQDhCMzucn5R71ZPpM+C0Ypfz2l+IFRmobPFD5CrzLEA0wIgcmg0izRu1+3/zFytsr26mbwiaHtYj2SbpE7ZSLgEK1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 661137}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "e8576e852035900844d2cc2d502a2139a1610f48", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "20.8.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^3.0.2", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^6.4.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^6.4.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.3_1697272630757_0.9819863829113027", "host": "s3://npm-registry-packages"}}, "2.3.4": {"name": "yaml", "version": "2.3.4", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.3.4", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "dist": {"shasum": "53fc1d514be80aabf386dc6001eb29bf3b7523b2", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.3.4.tgz", "fileCount": 228, "integrity": "sha512-8aAvwVUSHpfEqTQ4w/KMlf3HcRdt50E5ODIQJBw1fQ5RL34xabzxtUlzTXVqc4rkZsPbvrXKWnABCD7kWSmocA==", "signatures": [{"sig": "MEUCIQDAFMiv7303+Euj4jEgSviIzRNsnat2WCfHosR0y1qv3QIgHgYP6PuIPClAbl+OKL/nr6SpgPOUme2eW82reDeqyV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 661139}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "b7696fc001837a2e9d66ad78d7c04f47943daeca", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "20.8.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^3.7.5", "prettier": "^3.0.2", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^14.18.35", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^6.4.1", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^6.4.1", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.3.4_1698975688625_0.23699841080775874", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "yaml", "version": "2.4.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.4.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "2376db1083d157f4b3a452995803dbcf43b08140", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.4.0.tgz", "fileCount": 231, "integrity": "sha512-j9iR8g+/t0lArF4V6NE/QCfT+CO7iLqrXAHZbJdo+LfjqP1vR8Fg5bSiaq6Q2lOD1AUEVrEVIgABvBFYojJVYQ==", "signatures": [{"sig": "MEQCIEHxnQ+3p4XnBULBayIg+fBMJ3Q8kKaH7ondGNXeEZVWAiBwDqaGEi8zMi3+ULGH+TldI5ASYHMpEXwzLy5+9B1iiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 670405}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "8d8cfb70a19148ba16958a9d83ab14a73a809ce4", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "predocs": "node docs/prepare-docs.mjs", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "20.8.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^7.0.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.4.0_1708875240088_0.6605808463334017", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "yaml", "version": "2.4.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.4.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "2e57e0b5e995292c25c75d2658f0664765210eed", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.4.1.tgz", "fileCount": 231, "integrity": "sha512-pIXzoImaqmfOrL7teGUBt/T7ZDnyeGBWyXQBvOVhLkWLN37GXv8NMLK406UY6dS51JfcQHsmcW5cJ441bHg6Lg==", "signatures": [{"sig": "MEYCIQDOZY2LzakuefNlEBOU9+ilX+5v5WB+3kwoYp4CTmfAVwIhAKeQ+KgcpL1ftmz/QiHNgcUH7JUmU4K/r5xOONM7LuhI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 670702}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "4aa56d337dc5e286eb0c9111a3b370f21e321117", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "predocs": "node docs/prepare-docs.mjs", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "21.6.2", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^7.0.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.4.1_1709725952680_0.22710035557221508", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "yaml", "version": "2.4.2", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.4.2", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "7a2b30f2243a5fc299e1f14ca58d475ed4bc5362", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.4.2.tgz", "fileCount": 231, "integrity": "sha512-B3VqDZ+JAg1nZpaEmWtTXUlBneoGx6CPM9b0TENK6aoSu5t73dItudwdgmi6tHlIZZId4dZ9skcAQ2UbcyAeVA==", "signatures": [{"sig": "MEQCIEgqP0O+YwvrUxkoENdpDULlR+X/DoevXXjY541fABlWAiBwvfLsfzxITlGAMBtHRYaj46pJp+7XMxb6+DiRRpl4BA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 670948}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "f792d1b72fb98792a66ca2dcf5b606448e762b16", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "predocs": "node docs/prepare-docs.mjs", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "21.6.2", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^7.0.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.4.2_1714296775625_0.01112779352203308", "host": "s3://npm-registry-packages"}}, "2.4.3": {"name": "yaml", "version": "2.4.3", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.4.3", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "0777516b8c7880bcaa0f426a5410e8d6b0be1f3d", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.4.3.tgz", "fileCount": 231, "integrity": "sha512-sntgmxj8o7DE7g/Qi60cqpLBA3HG3STcDA0kO+WfB05jEKhZMbY7umNm2rBpQvsmZ16/lPXCJGW2672dgOUkrg==", "signatures": [{"sig": "MEUCIQDnMN0H/VTDC8S4KrgCVxI30EhCYy5EYT/b10RYbfnyGQIgMFnMR+i+KA7pjTiVaOPrfofCrkKy+X+qsZILE9KLz4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 671567}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "208d98f0a42aad737bd019bb59d85679e2d9282a", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node -i -e 'YAML=require(\"./dist/index.js\")'", "predocs": "node docs/prepare-docs.mjs", "prestart": "npm run build:node", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "21.6.2", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^7.0.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.4.3_1717319327452_0.601135222389299", "host": "s3://npm-registry-packages"}}, "2.4.4": {"name": "yaml", "version": "2.4.4", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.4.4", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "e463681ec48fe9567f1ce35cf1e3a25e14b7b7e7", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.4.4.tgz", "fileCount": 231, "integrity": "sha512-wle6DEiBMLgJAdEPZ+E8BPFauoWbwPujfuGJJFErxYiU4txXItppe8YqeFPAaWnW5CxduQ995X6b5e1NqrmxtA==", "signatures": [{"sig": "MEUCICyaGpPlkXf3th0V6WCMvXz0z1lOFu+K64Bfelw72IP7AiEAhly2ArS66aFkwJim1btCLmYcF/hx5KhjpFJEhgbfOyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 672373}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "d06f3867ae9af453ad115f73b83bad0095b65125", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "predocs": "node docs/prepare-docs.mjs", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "21.6.2", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^7.0.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.4.4_1717863779540_0.16336070983871886", "host": "s3://npm-registry-packages"}}, "2.4.5": {"name": "yaml", "version": "2.4.5", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.4.5", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "60630b206dd6d84df97003d33fc1ddf6296cca5e", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.4.5.tgz", "fileCount": 231, "integrity": "sha512-aBx2bnqDzVOyNKfsysjA2ms5ZlnjSAW2eG3/L5G/CSujfjLJTJsEw1bGw8kCf04KodQWk1pxlGnZ56CRxiawmg==", "signatures": [{"sig": "MEUCIQCn9ywj18m2oD02hbzi5figW1Hf/1phPpNVFQP7uZ4eNwIgJJ6lcUyJm35UjGlMdpYFOL50sLXE6Wdmj0i/WU7y+NY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 674680}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "1b8fde6717c096446d4b1cf9e21ef1fb87090385", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "predocs": "node docs/prepare-docs.mjs", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "21.6.2", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^7.0.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.4.5_1717882946551_0.44597381615828335", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "yaml", "version": "2.5.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.5.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "c6165a721cf8000e91c36490a41d7be25176cf5d", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.5.0.tgz", "fileCount": 231, "integrity": "sha512-2wWLbGbYDiSqqIKoPjar3MPgB94ErzCtrNE1FdqGuaO0pi2JGjmE8aW8TDZwzU7vuxcGRdL/4gPQwQ7hD5AMSw==", "signatures": [{"sig": "MEYCIQCqAvW6CsS+Vh43MjjDTPRrKFaycTt4KL35tR0wYhu/8QIhAL+qXCj/3nyXHnuE2yBvFNfQfM+pUA/DKxb5sCuj9UU4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 675753}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "b309e23cf70f7502acc7bbc5eb389095ab87d871", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "predocs": "node docs/prepare-docs.mjs", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "22.5.1", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^8.2.0", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^7.0.2", "jest-ts-webcompat-resolver": "^1.0.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.4"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.5.0_1721817400355_0.4683311125545344", "host": "s3://npm-registry-packages"}}, "2.5.1": {"name": "yaml", "version": "2.5.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.5.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "c9772aacf62cb7494a95b0c4f1fb065b563db130", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.5.1.tgz", "fileCount": 230, "integrity": "sha512-bLQOjaX/ADgQ20isPJRvF0iRUHIxVhYvr53Of7wGcWlO2jvtUlH5m87DsmulFVxRpNLOnI4tB6p/oh8D7kpn9Q==", "signatures": [{"sig": "MEQCIDbpVxK2ipfIEeVR7CJE6YctZD57H2MiZgjJIheAoNqaAiB5LwuIg9yW8qsAIupThJDcgm13FEdHqW8eqUN0hFQ0Pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 674664}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "5adbb605b64094b57bc95cbc24587e6f36b3f9a8", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint config/ src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "predocs": "node docs/prepare-docs.mjs", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "22.5.1", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^9.9.1", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "@eslint/js": "^9.9.1", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "typescript-eslint": "^8.4.0", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "jest-ts-webcompat-resolver": "^1.0.0", "@babel/plugin-transform-typescript": "^7.12.17"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.5.1_1725405207330_0.9933069423740062", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "yaml", "version": "2.6.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.6.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "14059ad9d0b1680d0f04d3a60fe00f3a857303c3", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.6.0.tgz", "fileCount": 233, "integrity": "sha512-a6ae//JvKDEra2kdi1qzCyrJW/WZCgFi8ydDV+eXExl95t+5R+ijnqHJbz9tmMh8FUjx3iv2fCQ4dclAQlO2UQ==", "signatures": [{"sig": "MEYCIQDRB/SQezAh4DB1Zefj91yCbsNQmGBrOfEhAPGXJq4ceAIhAIj13hq9wV7AYq4ISf0IvEe4X+3034OemTwb9U+0Er6I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 680893}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "808fba3cdef86b5d12085fd9c6343d47f168cbc3", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint config/ src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "predocs": "node docs/prepare-docs.mjs", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "22.8.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^9.9.1", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "@eslint/js": "^9.9.1", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "typescript-eslint": "^8.4.0", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "jest-ts-webcompat-resolver": "^1.0.0", "@babel/plugin-transform-typescript": "^7.12.17"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.6.0_1728815781097_0.5789709535367382", "host": "s3://npm-registry-packages"}}, "2.6.1": {"name": "yaml", "version": "2.6.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.6.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "42f2b1ba89203f374609572d5349fb8686500773", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.6.1.tgz", "fileCount": 233, "integrity": "sha512-7r0XPzioN/Q9kXBro/XPnA6kznR73DHq+GXh5ON7ZozRO6aMjbmiBuKste2wslTFkC5d1dw0GooOCepZXJ2SAg==", "signatures": [{"sig": "MEUCIHNfQHuurcbUBdQHYfP/c1MmL5ozBFT7RdXHPbK+iTyrAiEAww5ls80YtBnhaOQ8VCyxZf72S83DMDYbELczqfKsZm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 681592}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "aa1898ae61605ea09bb79621d25ad5e7fd9b4217", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint config/ src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "predocs": "node docs/prepare-docs.mjs", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "22.8.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.1.0", "eslint": "^9.9.1", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "@eslint/js": "^9.9.1", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.0.3", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "typescript-eslint": "^8.4.0", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^11.0.0", "jest-ts-webcompat-resolver": "^1.0.0", "@babel/plugin-transform-typescript": "^7.12.17"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.6.1_1732013272350_0.41795267368019196", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "yaml", "version": "2.7.0", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.7.0", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "aef9bb617a64c937a9a748803786ad8d3ffe1e98", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.7.0.tgz", "fileCount": 233, "integrity": "sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==", "signatures": [{"sig": "MEQCIDfqM1WIkKFlAQQwkvbhVRkAbJ2m0qXGAt/Si2T2/7EfAiAFasdPMDgF72eAh5HvoPHPS1J5OzzmFrrgXkUyTRNT4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 681128}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "8f512b526a52e245e770be257235f7d37059ca39", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint config/ src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "predocs": "node docs/prepare-docs.mjs", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "23.4.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.8.1", "eslint": "^9.9.1", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "@eslint/js": "^9.9.1", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.7.2", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "typescript-eslint": "^8.4.0", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^12.1.1", "jest-ts-webcompat-resolver": "^1.0.0", "@babel/plugin-transform-typescript": "^7.12.17"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.7.0_1735620047260_0.7976759325899112", "host": "s3://npm-registry-packages-npm-production"}}, "2.7.1": {"name": "yaml", "version": "2.7.1", "keywords": ["YAML", "parser", "stringifier"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yaml@2.7.1", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://eemeli.org/yaml/", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "bin": {"yaml": "bin.mjs"}, "dist": {"shasum": "44a247d1b88523855679ac7fa7cda6ed7e135cf6", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.7.1.tgz", "fileCount": 233, "integrity": "sha512-10ULxpnOCQXxJvBgxsn9ptjq6uviG/htZKk9veJGhlqn3w/DxQ631zFF+nlQXLwmImeS5amR2dl2U8sg6U9jsQ==", "signatures": [{"sig": "MEYCIQDSSXWeLbF6ibJFe8oktVSypTWe6mZUHlVkzIc9OqeoQAIhAPD8KURvePIfYsDFnla1zHvcogpEdATdP/uwE/QWG5Gt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 682531}, "main": "./dist/index.js", "type": "commonjs", "types": "./dist/index.d.ts", "browser": {"./util.js": "./browser/dist/util.js", "./dist/util.js": "./browser/dist/util.js", "./dist/index.js": "./browser/index.js"}, "engines": {"node": ">= 14"}, "exports": {".": {"node": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./browser/index.js"}, "./util": {"node": "./dist/util.js", "types": "./dist/util.d.ts", "default": "./browser/dist/util.js"}, "./package.json": "./package.json"}, "gitHead": "a141bc0f00b194e9ac5146f52e74c0e8236784a6", "scripts": {"docs": "cd docs-slate && bundle exec middleman server", "lint": "eslint config/ src/", "test": "jest --config config/jest.config.js", "build": "npm run build:node && npm run build:browser", "clean": "git clean -fdxe node_modules", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "predocs": "node docs/prepare-docs.mjs", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "prettier": "prettier --write .", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:dist": "npm run build:node && jest --config config/jest.config.js", "build:node": "rollup -c config/rollup.node-config.mjs", "preversion": "npm test && npm run build", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:deploy": "cd docs-slate && ./deploy.sh", "docs:install": "cd docs-slate && bundle install", "build:browser": "rollup -c config/rollup.browser-config.mjs", "test:browsers": "cd playground && npm test", "predocs:deploy": "node docs/prepare-docs.mjs", "prepublishOnly": "npm run clean && npm test && npm run build", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js"}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "prettier": {"semi": false, "arrowParens": "avoid", "singleQuote": true, "trailingComma": "none"}, "repository": {"url": "git+https://github.com/eemeli/yaml.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "JavaScript parser and stringifier for YAML", "directories": {}, "_nodeVersion": "23.7.0", "browserslist": "defaults, not ie 11", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.0.1", "tslib": "^2.8.1", "eslint": "^9.9.1", "rollup": "^4.12.0", "prettier": "^3.0.2", "cross-env": "^7.0.3", "@eslint/js": "^9.9.1", "babel-jest": "^29.0.1", "fast-check": "^2.12.0", "typescript": "^5.7.2", "@babel/core": "^7.12.10", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "@babel/preset-env": "^7.12.11", "typescript-eslint": "^8.4.0", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "eslint-config-prettier": "^9.0.0", "@rollup/plugin-typescript": "^12.1.1", "jest-ts-webcompat-resolver": "^1.0.0", "@babel/plugin-transform-typescript": "^7.12.17"}, "_npmOperationalInternal": {"tmp": "tmp/yaml_2.7.1_1743260401194_0.2802605330203325", "host": "s3://npm-registry-packages-npm-production"}}, "2.8.0": {"name": "yaml", "version": "2.8.0", "license": "ISC", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "keywords": ["YAML", "parser", "stringifier"], "homepage": "https://eemeli.org/yaml/", "type": "commonjs", "main": "./dist/index.js", "bin": {"yaml": "bin.mjs"}, "browser": {"./dist/index.js": "./browser/index.js", "./dist/util.js": "./browser/dist/util.js", "./util.js": "./browser/dist/util.js"}, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "default": "./browser/index.js"}, "./package.json": "./package.json", "./util": {"types": "./dist/util.d.ts", "node": "./dist/util.js", "default": "./browser/dist/util.js"}}, "scripts": {"build": "npm run build:node && npm run build:browser", "build:browser": "rollup -c config/rollup.browser-config.mjs", "build:node": "rollup -c config/rollup.node-config.mjs", "clean": "git clean -fdxe node_modules", "lint": "eslint config/ src/", "prettier": "prettier --write .", "prestart": "rollup --sourcemap -c config/rollup.node-config.mjs", "start": "node --enable-source-maps -i -e 'YAML=require(\"./dist/index.js\");const{parse,parseDocument,parseAllDocuments}=YAML'", "test": "jest --config config/jest.config.js", "test:all": "npm test && npm run test:types && npm run test:dist && npm run test:dist:types", "test:browsers": "cd playground && npm test", "test:dist": "npm run build:node && jest --config config/jest.config.js", "test:dist:types": "tsc --allowJs --moduleResolution node --noEmit --target es5 dist/index.js", "test:types": "tsc --noEmit && tsc --noEmit -p tests/tsconfig.json", "docs:install": "cd docs-slate && bundle install", "predocs:deploy": "node docs/prepare-docs.mjs", "docs:deploy": "cd docs-slate && ./deploy.sh", "predocs": "node docs/prepare-docs.mjs", "docs": "cd docs-slate && bundle exec middleman server", "preversion": "npm test && npm run build", "prepublishOnly": "npm run clean && npm test && npm run build"}, "browserslist": "defaults, not ie 11", "prettier": {"arrowParens": "avoid", "semi": false, "singleQuote": true, "trailingComma": "none"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-typescript": "^7.12.17", "@babel/preset-env": "^7.12.11", "@eslint/js": "^9.9.1", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-typescript": "^12.1.1", "@types/jest": "^29.2.4", "@types/node": "^20.11.20", "babel-jest": "^29.0.1", "cross-env": "^7.0.3", "eslint": "^9.9.1", "eslint-config-prettier": "^9.0.0", "fast-check": "^2.12.0", "jest": "^29.0.1", "jest-ts-webcompat-resolver": "^1.0.0", "prettier": "^3.0.2", "rollup": "^4.12.0", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.4.0"}, "engines": {"node": ">= 14.6"}, "_id": "yaml@2.8.0", "gitHead": "c000eb708fc04910a0b034572c6febb090ca7035", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "_nodeVersion": "23.11.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==", "shasum": "15f8c9866211bdc2d3781a0890e44d4fa1a5fff6", "tarball": "https://registry.npmjs.org/yaml/-/yaml-2.8.0.tgz", "fileCount": 233, "unpackedSize": 683419, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDhFTgJ6Px1HOUpnBEteaoTACUC5bkY6gTUfNcecHwYbAIhAOWfEGjbAv5+i8QVfYDa41iDjfZUfehqzYBzY++eFIT5"}]}, "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/yaml_2.8.0_1747295895612_0.7956807244103377"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-04-15T16:25:52.216Z", "modified": "2025-05-15T07:58:15.973Z", "0.1.0": "2011-04-15T16:25:52.216Z", "0.1.1": "2011-04-15T16:25:52.216Z", "0.1.2": "2011-04-15T16:25:52.216Z", "0.2.0": "2011-05-21T19:08:13.195Z", "0.2.1": "2011-05-22T17:20:58.242Z", "0.2.2": "2011-08-31T17:45:55.087Z", "0.2.3": "2011-12-05T20:18:09.979Z", "0.3.0": "2016-02-21T13:00:23.937Z", "1.0.0-beta.1": "2018-02-13T09:58:43.794Z", "1.0.0-beta.2": "2018-02-19T09:00:00.612Z", "1.0.0-beta.3": "2018-02-22T23:02:54.112Z", "1.0.0-beta.4": "2018-04-08T12:56:59.760Z", "1.0.0-beta.5": "2018-05-15T18:21:45.622Z", "1.0.0-beta.6": "2018-05-29T20:41:59.158Z", "1.0.0-beta.7": "2018-06-15T14:04:39.596Z", "1.0.0-rc.1": "2018-06-18T21:13:43.889Z", "1.0.0-rc.2": "2018-06-18T21:18:58.840Z", "1.0.0-rc.3": "2018-06-26T19:12:38.659Z", "1.0.0-rc.4": "2018-06-27T21:20:16.651Z", "1.0.0-rc.5": "2018-06-30T15:31:40.511Z", "1.0.0-rc.6": "2018-07-08T12:16:11.502Z", "1.0.0-rc.7": "2018-07-11T15:31:19.241Z", "1.0.0-rc.8": "2018-08-23T05:03:16.366Z", "1.0.0": "2018-09-19T19:56:51.387Z", "1.0.1": "2018-11-07T10:32:19.623Z", "1.0.2": "2018-11-15T10:53:22.920Z", "1.0.3": "2018-11-28T21:08:01.305Z", "1.1.0": "2018-12-11T23:00:08.695Z", "1.2.0": "2019-01-01T09:34:08.556Z", "1.2.1": "2019-01-17T10:15:38.143Z", "1.3.0": "2019-01-27T08:33:37.515Z", "1.3.1": "2019-01-27T11:42:43.790Z", "1.3.2": "2019-02-08T21:27:42.462Z", "1.4.0": "2019-03-07T19:48:19.735Z", "1.5.0": "2019-04-06T06:50:57.336Z", "1.5.1": "2019-05-05T13:14:24.175Z", "1.6.0": "2019-05-23T14:07:54.388Z", "1.7.0": "2019-09-25T13:15:45.991Z", "1.7.1": "2019-10-07T12:39:51.322Z", "1.7.2": "2019-10-15T11:24:10.790Z", "1.8.0": "2020-03-07T19:39:11.743Z", "1.8.1": "2020-03-11T04:42:35.186Z", "1.8.2": "2020-03-11T05:51:45.069Z", "1.8.3": "2020-03-21T00:51:23.126Z", "1.9.0": "2020-04-17T16:33:54.113Z", "1.9.1": "2020-04-18T12:01:32.964Z", "1.9.2": "2020-04-20T09:28:01.007Z", "1.10.0": "2020-05-16T09:56:27.877Z", "2.0.0-0": "2020-08-23T15:02:15.918Z", "2.0.0-1": "2020-10-05T22:28:56.807Z", "2.0.0-2": "2021-01-31T20:33:48.902Z", "2.0.0-3": "2021-01-31T21:28:40.953Z", "1.10.1": "2021-03-13T11:03:58.138Z", "1.10.2": "2021-03-13T21:02:56.363Z", "2.0.0-4": "2021-03-13T23:08:54.849Z", "2.0.0-5": "2021-04-18T08:52:32.301Z", "2.0.0-6": "2021-06-14T06:55:59.132Z", "2.0.0-7": "2021-07-17T10:23:28.886Z", "2.0.0-8": "2021-09-06T17:27:22.328Z", "2.0.0-9": "2021-11-13T09:52:23.807Z", "2.0.0-10": "2021-12-31T14:49:40.336Z", "2.0.0-11": "2022-03-22T08:55:33.137Z", "2.0.0": "2022-04-06T08:47:16.324Z", "2.0.1": "2022-04-15T20:37:10.896Z", "2.1.0": "2022-05-14T09:40:59.138Z", "2.1.1": "2022-05-29T22:26:44.097Z", "2.1.2": "2022-10-02T16:49:07.255Z", "2.1.3": "2022-10-05T07:32:18.071Z", "2.2.0": "2022-12-21T12:31:43.946Z", "2.2.1": "2022-12-30T09:58:13.392Z", "2.3.0-0": "2023-03-11T12:57:24.376Z", "2.3.0-1": "2023-04-04T08:27:24.123Z", "2.3.0-2": "2023-04-14T09:01:00.965Z", "2.3.0-3": "2023-04-14T09:14:01.768Z", "2.3.0-4": "2023-04-18T09:21:40.633Z", "2.2.2": "2023-04-24T13:02:14.045Z", "2.3.0-5": "2023-05-06T17:38:58.086Z", "2.3.0": "2023-05-23T11:16:22.320Z", "2.3.1": "2023-05-26T10:39:48.703Z", "2.3.2": "2023-08-28T15:00:54.836Z", "2.3.3": "2023-10-14T08:37:11.077Z", "2.3.4": "2023-11-03T01:41:28.842Z", "2.4.0": "2024-02-25T15:34:00.239Z", "2.4.1": "2024-03-06T11:52:32.864Z", "2.4.2": "2024-04-28T09:32:55.892Z", "2.4.3": "2024-06-02T09:08:47.664Z", "2.4.4": "2024-06-08T16:22:59.714Z", "2.4.5": "2024-06-08T21:42:26.758Z", "2.5.0": "2024-07-24T10:36:40.581Z", "2.5.1": "2024-09-03T23:13:27.622Z", "2.6.0": "2024-10-13T10:36:21.312Z", "2.6.1": "2024-11-19T10:47:52.527Z", "2.7.0": "2024-12-31T04:40:47.460Z", "2.7.1": "2025-03-29T15:00:01.431Z", "2.8.0": "2025-05-15T07:58:15.800Z"}, "bugs": {"url": "https://github.com/eemeli/yaml/issues"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "homepage": "https://eemeli.org/yaml/", "keywords": ["YAML", "parser", "stringifier"], "repository": {"type": "git", "url": "git+https://github.com/eemeli/yaml.git"}, "description": "JavaScript parser and stringifier for YAML", "maintainers": [{"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# YAML <a href=\"https://www.npmjs.com/package/yaml\"><img align=\"right\" src=\"https://badge.fury.io/js/yaml.svg\" title=\"npm package\" /></a>\n\n`yaml` is a definitive library for [YAML](https://yaml.org/), the human friendly data serialization standard.\nThis library:\n\n- Supports both YAML 1.1 and YAML 1.2 and all common data schemas,\n- Passes all of the [yaml-test-suite](https://github.com/yaml/yaml-test-suite) tests,\n- Can accept any string as input without throwing, parsing as much YAML out of it as it can, and\n- Supports parsing, modifying, and writing YAML comments and blank lines.\n\nThe library is released under the ISC open source license, and the code is [available on GitHub](https://github.com/eemeli/yaml/).\nIt has no external dependencies and runs on Node.js as well as modern browsers.\n\nFor the purposes of versioning, any changes that break any of the documented endpoints or APIs will be considered semver-major breaking changes.\nUndocumented library internals may change between minor versions, and previous APIs may be deprecated (but not removed).\n\nThe minimum supported TypeScript version of the included typings is 3.9;\nfor use in earlier versions you may need to set `skipLibCheck: true` in your config.\nThis requirement may be updated between minor versions of the library.\n\nFor more information, see the project's documentation site: [**eemeli.org/yaml**](https://eemeli.org/yaml/)\n\nTo install:\n\n```sh\nnpm install yaml\n# or\ndeno add jsr:@eemeli/yaml\n```\n\n**Note:** These docs are for `yaml@2`. For v1, see the [v1.10.0 tag](https://github.com/eemeli/yaml/tree/v1.10.0) for the source and [eemeli.org/yaml/v1](https://eemeli.org/yaml/v1/) for the documentation.\n\nThe development and maintenance of this library is [sponsored](https://github.com/sponsors/eemeli) by:\n\n<p align=\"center\" width=\"100%\">\n  <a href=\"https://www.scipress.io/\"\n    ><img\n      width=\"150\"\n      align=\"top\"\n      src=\"https://eemeli.org/yaml/images/scipress.svg\"\n      alt=\"Scipress\"\n  /></a>\n  &nbsp; &nbsp;\n  <a href=\"https://manifest.build/\"\n    ><img\n      width=\"150\"\n      align=\"top\"\n      src=\"https://eemeli.org/yaml/images/manifest.svg\"\n      alt=\"Manifest\"\n  /></a>\n</p>\n\n## API Overview\n\nThe API provided by `yaml` has three layers, depending on how deep you need to go: [Parse & Stringify](https://eemeli.org/yaml/#parse-amp-stringify), [Documents](https://eemeli.org/yaml/#documents), and the underlying [Lexer/Parser/Composer](https://eemeli.org/yaml/#parsing-yaml).\nThe first has the simplest API and \"just works\", the second gets you all the bells and whistles supported by the library along with a decent [AST](https://eemeli.org/yaml/#content-nodes), and the third lets you get progressively closer to YAML source, if that's your thing.\n\nA [command-line tool](https://eemeli.org/yaml/#command-line-tool) is also included.\n\n### Parse & Stringify\n\n```js\nimport { parse, stringify } from 'yaml'\n```\n\n- [`parse(str, reviver?, options?): value`](https://eemeli.org/yaml/#yaml-parse)\n- [`stringify(value, replacer?, options?): string`](https://eemeli.org/yaml/#yaml-stringify)\n\n### Documents\n\n<!-- prettier-ignore -->\n```js\nimport {\n  Document,\n  isDocument,\n  parseAllDocuments,\n  parseDocument\n} from 'yaml'\n```\n\n- [`Document`](https://eemeli.org/yaml/#documents)\n  - [`constructor(value, replacer?, options?)`](https://eemeli.org/yaml/#creating-documents)\n  - [`#contents`](https://eemeli.org/yaml/#content-nodes)\n  - [`#directives`](https://eemeli.org/yaml/#stream-directives)\n  - [`#errors`](https://eemeli.org/yaml/#errors)\n  - [`#warnings`](https://eemeli.org/yaml/#errors)\n- [`isDocument(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`parseAllDocuments(str, options?): Document[]`](https://eemeli.org/yaml/#parsing-documents)\n- [`parseDocument(str, options?): Document`](https://eemeli.org/yaml/#parsing-documents)\n\n### Content Nodes\n\n<!-- prettier-ignore -->\n```js\nimport {\n  isAlias, isCollection, isMap, isNode,\n  isPair, isScalar, isSeq, Scalar,\n  visit, visitAsync, YAMLMap, YAMLSeq\n} from 'yaml'\n```\n\n- [`isAlias(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isCollection(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isMap(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isNode(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isPair(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isScalar(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`isSeq(foo): boolean`](https://eemeli.org/yaml/#identifying-node-types)\n- [`new Scalar(value)`](https://eemeli.org/yaml/#scalar-values)\n- [`new YAMLMap()`](https://eemeli.org/yaml/#collections)\n- [`new YAMLSeq()`](https://eemeli.org/yaml/#collections)\n- [`doc.createAlias(node, name?): Alias`](https://eemeli.org/yaml/#creating-nodes)\n- [`doc.createNode(value, options?): Node`](https://eemeli.org/yaml/#creating-nodes)\n- [`doc.createPair(key, value): Pair`](https://eemeli.org/yaml/#creating-nodes)\n- [`visit(node, visitor)`](https://eemeli.org/yaml/#finding-and-modifying-nodes)\n- [`visitAsync(node, visitor)`](https://eemeli.org/yaml/#finding-and-modifying-nodes)\n\n### Parsing YAML\n\n```js\nimport { Composer, Lexer, Parser } from 'yaml'\n```\n\n- [`new Lexer().lex(src)`](https://eemeli.org/yaml/#lexer)\n- [`new Parser(onNewLine?).parse(src)`](https://eemeli.org/yaml/#parser)\n- [`new Composer(options?).compose(tokens)`](https://eemeli.org/yaml/#composer)\n\n## YAML.parse\n\n```yaml\n# file.yml\nYAML:\n  - A human-readable data serialization language\n  - https://en.wikipedia.org/wiki/YAML\nyaml:\n  - A complete JavaScript implementation\n  - https://www.npmjs.com/package/yaml\n```\n\n```js\nimport fs from 'fs'\nimport YAML from 'yaml'\n\nYAML.parse('3.14159')\n// 3.14159\n\nYAML.parse('[ true, false, maybe, null ]\\n')\n// [ true, false, 'maybe', null ]\n\nconst file = fs.readFileSync('./file.yml', 'utf8')\nYAML.parse(file)\n// { YAML:\n//   [ 'A human-readable data serialization language',\n//     'https://en.wikipedia.org/wiki/YAML' ],\n//   yaml:\n//   [ 'A complete JavaScript implementation',\n//     'https://www.npmjs.com/package/yaml' ] }\n```\n\n## YAML.stringify\n\n```js\nimport YAML from 'yaml'\n\nYAML.stringify(3.14159)\n// '3.14159\\n'\n\nYAML.stringify([true, false, 'maybe', null])\n// `- true\n// - false\n// - maybe\n// - null\n// `\n\nYAML.stringify({ number: 3, plain: 'string', block: 'two\\nlines\\n' })\n// `number: 3\n// plain: string\n// block: |\n//   two\n//   lines\n// `\n```\n\n---\n\nBrowser testing provided by:\n\n<a href=\"https://www.browserstack.com/open-source\">\n<img width=200 src=\"https://eemeli.org/yaml/images/browserstack.svg\" alt=\"BrowserStack\" />\n</a>\n", "readmeFilename": "README.md", "users": {"pid": true, "gabeio": true, "monjer": true, "naholyr": true, "rdesoky": true, "tamikot": true, "coalesce": true, "gejiawen": true, "semencov": true, "fgribreau": true, "jbnicolai": true, "bphanikumar": true, "flumpus-dev": true, "jordanskole": true, "vibesharing": true, "stone_breaker": true}}