{"_id": "follow-redirects", "_rev": "92-84f67ee5f87a7d769b5aa14478f59592", "name": "follow-redirects", "dist-tags": {"latest": "1.15.9"}, "versions": {"0.0.1": {"name": "follow-redirects", "version": "0.0.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "follow-redirects@0.0.1", "maintainers": [{"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "", "dist": {"shasum": "b0b9078dc855b13a7acfebcb099b9f98f2366772", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-0.0.1.tgz", "integrity": "sha512-wNdWqzXDz/O7I4SH790v3A8g9uevFrBzsrvqigUsSWueLs/ZQGNNAHzzsnTdlD+n8ks1dQ63IBEW4NThEmYXrQ==", "signatures": [{"sig": "MEUCIQChLBdTayhpfcFuwThP3yu0o18BaSlW+1WTFJSgcKo53AIgMhWtd9eKMOrwDtXbvObacs9NBLCKfwWsa26bEnI1jX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "dependencies": {"underscore": ""}, "devDependencies": {"colors": ""}}, "0.0.2": {"name": "follow-redirects", "version": "0.0.2", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "follow-redirects@0.0.2", "maintainers": [{"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "", "dist": {"shasum": "06c81345f422a3bfd93583bd0b6c0daa58cb54b9", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-0.0.2.tgz", "integrity": "sha512-CYnZjjtuha2qJ71fhpmZpziSmw5bkFbNG+Bb4e382CiN9WFZDdFvmvfS5v8+S6P6HuURgu3ngtMei2ORr4q92A==", "signatures": [{"sig": "MEUCIQCS3Jl/5KAljY2970pipHat/BO9gp/pIGK3I6GXpC+ibQIge/n61A2ngdcrFderVl3O9FvSR3QrmZeNy2uuDLxNwIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "dependencies": {"underscore": ""}, "devDependencies": {"colors": ""}}, "0.0.3": {"name": "follow-redirects", "version": "0.0.3", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "follow-redirects@0.0.3", "maintainers": [{"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "", "dist": {"shasum": "6ce67a24db1fe13f226c1171a72a7ef2b17b8f65", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-0.0.3.tgz", "integrity": "sha512-ZyO6tx9xafGe5n4SdO2CN+3Gmm0ntAc8YRyRaaDT4eNbE0eTv7rjLKJb3iGTtVJ/SHhPPHXWGGY5h21nMxJnbw==", "signatures": [{"sig": "MEUCIQDX0UbOBQaGYZPupNG87esib6PA7rnxxc2WISa+hwwjmwIgRYGgwcKr9wSEs1VPQwxqq6SomDCH0v6zulQkO6ydqZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "dependencies": {"underscore": ""}, "devDependencies": {"colors": ""}}, "0.0.4": {"name": "follow-redirects", "version": "0.0.4", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@0.0.4", "maintainers": [{"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "dist": {"shasum": "78d2faec253da7bbd4bbb6733e05132a6ae9b483", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-0.0.4.tgz", "integrity": "sha512-H2AMyUBnfYLN4J/nz3ZRq9Aj14gGfT7QzBlVZnjDtpcTETWLcACzCMdKuT48WvwQFQcDBKvb6JpXT8eXmrY8mQ==", "signatures": [{"sig": "MEUCIBphvOLI7H/T6KN7nem68E3Bht19J/aYmysHzAopL01rAiEAtAp8H+yk6AlW1QK5u50Z6ukzKl4Wn/6771ybNe4lqac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "78d2faec253da7bbd4bbb6733e05132a6ae9b483", "gitHead": "0eb72aca9f61ffdf0b064b3582a4591756890853", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "dependencies": {"underscore": ""}, "devDependencies": {"colors": ""}}, "0.0.5": {"name": "follow-redirects", "version": "0.0.5", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@0.0.5", "maintainers": [{"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "dist": {"shasum": "c4d40dddfd8a9349e052c50c00222a5e393bae32", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-0.0.5.tgz", "integrity": "sha512-cUHLp6/aQJoamdNwE2fpJxDgM4ZxLPOcJr0E6gdhP93ugR8QYmk0qqip5nZ1yaMIYHi82MQ0xn0JZ9ew2jh8Ug==", "signatures": [{"sig": "MEUCIF+vJwcaIgfrBjn+K/VA7Ggogc4a4UcL9fx45ZAWVeDfAiEAireeh4TtigpzBjpEF7+PiEDfglCPMTCZafsuITNYSPw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c4d40dddfd8a9349e052c50c00222a5e393bae32", "gitHead": "1dd1218dbbcbea595b00c107a7fc04aab17e900d", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "dependencies": {"debug": "^2.2.0", "underscore": ""}, "devDependencies": {"colors": ""}}, "0.0.6": {"name": "follow-redirects", "version": "0.0.6", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@0.0.6", "maintainers": [{"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "dist": {"shasum": "a3d57beaea728d96a5dbb6f1e790351d2ee18331", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-0.0.6.tgz", "integrity": "sha512-VQPQihvee38CT6n1ouBZWNJpNX00u8uftahHog2nIst8YvRCH5IQdIX892xwST2iyZvpmS2l15bIGI7zcxGeHA==", "signatures": [{"sig": "MEUCIA5bec0yTC5gms9TvLjbgQvr2yWq76nGgeCDI5ijsrPYAiEAzQCNqaKouMiAckKrLNSIPa8RAQNBWD5CeXW648ekRR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "a3d57beaea728d96a5dbb6f1e790351d2ee18331", "gitHead": "795bae039c7a756dbed9ce7c4a45cf2d2ed7dd83", "scripts": {"lint": "jshint *.js test/*.js test/**/*.js", "test": "npm run cover && npm run lint && npm run style", "cover": "BLUEBIRD_DEBUG=1 istanbul cover ./node_modules/.bin/_mocha", "debug": "BLUEBIRD_DEBUG=1 mocha", "style": "jscs *.js && jscs test/*.js test/**/*.js --config=test/.jscsrc"}, "_npmUser": {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "**************:olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "dependencies": {"debug": "^2.2.0"}, "devDependencies": {"jscs": "^1.13.1", "mocha": "^2.2.5", "jshint": "^2.8.0", "semver": "~4.3.6", "express": "^4.13.0", "bluebird": "^2.9.30", "istanbul": "^0.3.17", "coveralls": "^2.11.2", "concat-stream": "^1.5.0"}}, "0.0.7": {"name": "follow-redirects", "version": "0.0.7", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@0.0.7", "maintainers": [{"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "james.talmage", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "dist": {"shasum": "34b90bab2a911aa347571da90f22bd36ecd8a919", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-0.0.7.tgz", "integrity": "sha512-RxpX808lAA4IZ2cNqzRedcsPfVuo2AJEL8mmGvGeN0KGLJWZf5fidmUkcB0DWUCrmLD+GAQ0J2WOBORw8BS/Uw==", "signatures": [{"sig": "MEUCIEpqhozOgrkXZueGdL+qlONg/uKjfgwFoIIeBdrawYNLAiEAkMSOMi3I0eRYLw1vC+kJEhwKThYLdIVVPsSrQ53p+/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "34b90bab2a911aa347571da90f22bd36ecd8a919", "gitHead": "5137f3958a179f0bf9310886edb77efd3b85a208", "scripts": {"lint": "jshint *.js test/*.js test/**/*.js", "test": "npm run cover && npm run lint && npm run style", "cover": "BLUEBIRD_DEBUG=1 istanbul cover ./node_modules/.bin/_mocha", "debug": "BLUEBIRD_DEBUG=1 mocha", "style": "jscs *.js && jscs test/*.js test/**/*.js --config=test/.jscsrc"}, "_npmUser": {"name": "james.talmage", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"debug": "^2.2.0", "stream-consume": "^0.1.0"}, "devDependencies": {"jscs": "^1.13.1", "mocha": "^2.2.5", "jshint": "^2.8.0", "semver": "~4.3.6", "express": "^4.13.0", "bluebird": "^2.9.30", "istanbul": "^0.3.17", "coveralls": "^2.11.2", "concat-stream": "^1.5.0"}}, "0.1.0": {"name": "follow-redirects", "version": "0.1.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@0.1.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "dist": {"shasum": "8333fbee65d2fa8585241ebb1372fc01e5b1f671", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-0.1.0.tgz", "integrity": "sha512-9jwarxMc6BDDZC1zfU/fNt6Y8ERn5G8eHIVTTcX/skHwk7BiHW85M2FJTyL7Muqjzbx97mhuuZ6JuQQfMuRoFQ==", "signatures": [{"sig": "MEUCIGFmbdhPO00ytLhLQzLrIqLniCKiBCQTku4rFifcp/xyAiEA6c4NvyWVR1IT4qPHQLhq/VOssJEcTllzZ5xVD6Uv8nQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "8333fbee65d2fa8585241ebb1372fc01e5b1f671", "gitHead": "20537d21a1e4eb9371a0c2d74cff5a041ee2e22a", "scripts": {"lint": "jshint *.js test/*.js test/**/*.js", "test": "npm run cover && npm run lint && npm run style", "cover": "BLUEBIRD_DEBUG=1 istanbul cover ./node_modules/.bin/_mocha", "debug": "BLUEBIRD_DEBUG=1 mocha", "style": "jscs *.js && jscs test/*.js test/**/*.js --config=test/.jscsrc"}, "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "3.8.2", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "5.9.0", "dependencies": {"debug": "^2.2.0", "stream-consume": "^0.1.0"}, "devDependencies": {"jscs": "^1.13.1", "mocha": "^2.2.5", "jshint": "^2.8.0", "semver": "~4.3.6", "express": "^4.13.0", "bluebird": "^2.9.30", "istanbul": "^0.3.17", "coveralls": "^2.11.2", "concat-stream": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-0.1.0.tgz_1460316696773_0.05581280658952892", "host": "packages-12-west.internal.npmjs.com"}}, "0.2.0": {"name": "follow-redirects", "version": "0.2.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@0.2.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "e0229d7a388bb5ff7b29f44fc1e1b62e921272df", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-0.2.0.tgz", "integrity": "sha512-X/Z3zCjVbpRPEttA1+m3VEmaYOsRUnuUkwOlGvUQ+Cq5RQbwNuoQA+og4X0nK+bsG4ivJGMT/kRAG4Gi5fWBwQ==", "signatures": [{"sig": "MEUCIQD9zaUERFdcD2/p6i80Glgnv1cZ2WFTYoT864TgfsT5AAIgdcmnCKkfX+qBamxVQdln9PuNJuU9Sogt8kJgWFRzkps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "e0229d7a388bb5ff7b29f44fc1e1b62e921272df", "gitHead": "9a13ff409ce5b05463ee0785cb4c048c21800561", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"debug": "^2.2.0", "stream-consume": "^0.1.0"}, "devDependencies": {"xo": "^0.15.1", "nyc": "^6.4.4", "mocha": "^2.2.5", "semver": "~5.1.0", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^2.11.2", "concat-stream": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-0.2.0.tgz_1465250402713_0.451352697564289", "host": "packages-12-west.internal.npmjs.com"}}, "0.3.0": {"name": "follow-redirects", "version": "0.3.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@0.3.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "6d9935db28386943b19730cbc0ae1a3b72ef0bc8", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-0.3.0.tgz", "integrity": "sha512-kYj19j9SF1YWjzO04eaBB5Ur3Tyo1IspbK3XmYvza816nbkEQ3kfmrcGfcQyf+2FVjmhZv7+b00T9tagLNgVrw==", "signatures": [{"sig": "MEUCIQCyofWxnl8bJKirJyAiuZXa3V8mS2PQ7qOoevL97zka9gIgQof+T9I69EpGMhT/PCFKn3x1pBSa43XHz8fQtByPitc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "6d9935db28386943b19730cbc0ae1a3b72ef0bc8", "gitHead": "3a3f8b040b32098606630278176e2dc54cf29723", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"debug": "^2.2.0"}, "devDependencies": {"xo": "^0.17.0", "nyc": "^8.3.1", "mocha": "^3.1.2", "semver": "^5.3.0", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^2.11.2", "concat-stream": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-0.3.0.tgz_1476978547120_0.37060856400057673", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.0": {"name": "follow-redirects", "version": "1.0.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.0.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "8e34298cbd2e176f254effec75a1c78cc849fd37", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.0.0.tgz", "integrity": "sha512-7s+wBk4z5xTwVJuozRBAyRofWKjD3uG2CUjZfZTrw9f+f+z8ZSxOjAqfIDLtc0Hnz+wGK2Y8qd93nGGjXBYKsQ==", "signatures": [{"sig": "MEQCIEdy9oOzxLj/ccjuCPvn0MhVW1viRjndWKm6l7oHhLWJAiBgY7B2xsLl7+N1zHO8rz4As/0CNgNpU75XkDxHeWXRAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "8e34298cbd2e176f254effec75a1c78cc849fd37", "gitHead": "4010b44f4715c89dd75037f66458b93fd59599a1", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"debug": "^2.2.0"}, "devDependencies": {"xo": "^0.17.0", "nyc": "^8.3.1", "mocha": "^3.1.2", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^2.11.2", "concat-stream": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.0.0.tgz_1477238992406_0.5941755524836481", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0": {"name": "follow-redirects", "version": "1.1.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.1.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "f0a07e20b96396f096873f16ea2b9638a79e7b0c", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.1.0.tgz", "integrity": "sha512-d+YQYNGVu+1qTu9jFjv+x2OB2+AA7czt+72u9CvTaygPFYydpBpVpX2j6C/aP1huFGJRo/qNWBlNLIDFQ/zIBg==", "signatures": [{"sig": "MEUCIF0q3xWD99Sk1hSUVRkxRxAa368z1dlkr/IUEFn6fblOAiEA4hPWaEP0Oh9cGMjwjAj0hx+whU4vJ9Jchsj0xJteq+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "f0a07e20b96396f096873f16ea2b9638a79e7b0c", "gitHead": "6626d432f0e6a9919dab492bce484e3d9f4657e4", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"debug": "^2.2.0"}, "devDependencies": {"xo": "^0.17.0", "nyc": "^8.3.1", "mocha": "^3.1.2", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^2.11.2", "concat-stream": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.1.0.tgz_1478450677901_0.6304203481413424", "host": "packages-18-east.internal.npmjs.com"}}, "1.2.0": {"name": "follow-redirects", "version": "1.2.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.2.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "e1746cbf9c9c1d5fbff07de315d0c8730ad68172", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.2.0.tgz", "integrity": "sha512-GVzB3S8JBtO9br+nUoJV75DIvq0mjsQFWhDRUe1XD7OdurH+co1qn5eG72t6shhUgsH3cNUGyq58uds9HTio9g==", "signatures": [{"sig": "MEYCIQD9NDfNz4rY2IAAbNQuKbwV8Wj4pxbLXgO1J7llGAwt5AIhAOY5FbIVeExAgDY+eTpmxsqDM9ZK36hD4R6jkcVfJZWG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "e1746cbf9c9c1d5fbff07de315d0c8730ad68172", "gitHead": "7dd0cb2a92cf04c8e95432c341a3ec881abeff41", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"debug": "^2.2.0"}, "devDependencies": {"xo": "^0.17.0", "nyc": "^8.3.1", "mocha": "^3.1.2", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^2.11.2", "concat-stream": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.2.0.tgz_1481147454200_0.2246841248124838", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.1": {"name": "follow-redirects", "version": "1.2.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.2.1", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "796c716970df4fb0096165393545040f61b00f59", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.2.1.tgz", "integrity": "sha512-TeV/6l6vi5a8IfvtgEaBe8YiogCVAS4paZXSTchjIpIQmPtU6NhLX6jgSMgMIjkzra7do2TOOlHDKM6zKQlxGw==", "signatures": [{"sig": "MEYCIQDD7cuuwk29GbzU0BaHA2/hJjTKUHcSsgVNrwb71YkAWwIhAPaVLQB7+ga8izHMWUi4FlkS2EjcJkq3fnxrcQS5ApZS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "796c716970df4fb0096165393545040f61b00f59", "gitHead": "a3cca068fbc3ea7377f8aaf658982f997c1c6dd3", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"debug": "^2.4.5"}, "devDependencies": {"xo": "^0.17.1", "nyc": "^10.0.0", "mocha": "^3.2.0", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^2.11.15", "concat-stream": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.2.1.tgz_1482065567827_0.582136373501271", "host": "packages-18-east.internal.npmjs.com"}}, "1.2.2": {"name": "follow-redirects", "version": "1.2.2", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.2.2", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "45e4248c741128874f4a4900fd7e14f644b0bc40", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.2.2.tgz", "integrity": "sha512-rtaimWqzNMpH2N7YhuKuiC6YV7SVkYkEma8UpX1cU8iBjjnyppeW1WeSJVrCj+hYR/ca5asat2yDn7iRUyLJfA==", "signatures": [{"sig": "MEUCIFbMztar3+0+RfB//0RkN2p7bx/G1+kQofC361QRSTZNAiEAutwK20QpsOz8Q7waMzb1fwPfi83NUhI4mBEOu3+2LjY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "45e4248c741128874f4a4900fd7e14f644b0bc40", "gitHead": "f6e923e03ff734319bb0dbbfe50112ed4518aa7a", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"debug": "^2.4.5"}, "devDependencies": {"xo": "^0.17.1", "nyc": "^10.0.0", "mocha": "^3.2.0", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^2.11.15", "concat-stream": "^1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.2.2.tgz_1489096432046_0.3155715446919203", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.3": {"name": "follow-redirects", "version": "1.2.3", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.2.3", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "01abaeca85e3609837d9fcda3167a7e42fdaca21", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.2.3.tgz", "integrity": "sha512-JbGgs8HPdhc7oDy/UunqNoJumOEbHvuSmmdbwtForJYcjUxU6wEKaCO2BvfDH30kPFOEEVotnQZ/1z7sQ1olnw==", "signatures": [{"sig": "MEYCIQDaSA9MvlFRlP81RHnWWPtKjuWha4O1G8k9o21ydh6yNgIhAM2xokPukJasiS7fNng5nK1OE+LwGUTvgCPkDVqvk1or", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "01abaeca85e3609837d9fcda3167a7e42fdaca21", "gitHead": "ad35f4c9ab0c3fbf23c8486ba5e241d08d6b39b4", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"debug": "^2.4.5"}, "devDependencies": {"xo": "^0.17.1", "nyc": "^10.0.0", "mocha": "^3.2.0", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^2.11.15", "concat-stream": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.2.3.tgz_1489164681760_0.5944589097052813", "host": "packages-18-east.internal.npmjs.com"}}, "1.2.4": {"name": "follow-redirects", "version": "1.2.4", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.2.4", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "355e8f4d16876b43f577b0d5ce2668b9723214ea", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.2.4.tgz", "integrity": "sha512-Suw6KewLV2hReSyEOeql+UUkBVyiBm3ok1VPrVFRZnQInWpdoZbbiG5i8aJVSjTr0yQ4Ava0Sh6/joCg1Brdqw==", "signatures": [{"sig": "MEUCIQDBe0o7xV0JTrcsQS8EquMI/kHd/POqVZxIojB0XyrVDgIgZE9GWaynmZLKiUio2khi9V5ywGZy8gjfwenO3teI+Bw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "c3770de92eb2898ccc638f269a1046919329bd81", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"debug": "^2.4.5"}, "devDependencies": {"xo": "^0.17.1", "nyc": "^10.0.0", "mocha": "^3.2.0", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^2.11.15", "concat-stream": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.2.4.tgz_1498073966897_0.48846928821876645", "host": "s3://npm-registry-packages"}}, "1.2.5": {"name": "follow-redirects", "version": "1.2.5", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.2.5", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "ffd3e14cbdd5eaa72f61b6368c1f68516c2a26cc", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.2.5.tgz", "integrity": "sha512-lMhwQTryFbG+wYsAIEKC1Kf5IGDlVNnONRogIBllh7LLoV7pNIxW0z9fhjRar9NBql+hd2Y49KboVVNxf6GEfg==", "signatures": [{"sig": "MEUCIQDMrzGZ/FLd7VJ4B0omvgyTxaux+cbDZUTmercj+7GbUQIgFYxEPdMCT5eAJXrAP2qZE6QzIzAxtgBI7HnSpCUnPHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "be81edd4dd413bd8f7f12344809b982df888e061", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"debug": "^2.6.9"}, "devDependencies": {"xo": "^0.17.1", "nyc": "^10.0.0", "mocha": "^3.2.0", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^2.11.15", "concat-stream": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.2.5.tgz_1507223053004_0.14782698079943657", "host": "s3://npm-registry-packages"}}, "1.2.6": {"name": "follow-redirects", "version": "1.2.6", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.2.6", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "xo": {"envs": ["mocha"]}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "4dcdc7e4ab3dd6765a97ff89c3b4c258117c79bf", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.2.6.tgz", "integrity": "sha512-FrMqZ/FONtHnbqO651UPpfRUVukIEwJhXMfdr/JWAmrDbeYBu773b1J6gdWDyRIj4hvvzQEHoEOTrdR8o6KLYA==", "signatures": [{"sig": "MEYCIQCJI2r1EhbDOgvM5I2oGHFXXHdQxA9lIw6oXY6YXuD1RgIhAMj03MBNcgJBw9UxZ3YJadsP0vfffgvDlra3RXl+4bta", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "99bd703a765992a260f07e1a2721005fa8b7501d", "scripts": {"test": "xo && BLUEBIRD_DEBUG=1 nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"debug": "^3.1.0"}, "devDependencies": {"xo": "^0.17.1", "nyc": "^11.3.0", "mocha": "^4.0.1", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^3.0.0", "concat-stream": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.2.6.tgz_1511346901294_0.8867264720611274", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "follow-redirects", "version": "1.3.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.3.0", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "f684871fc116d2e329fda55ef67687f4fabc905c", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.3.0.tgz", "integrity": "sha512-Tjb30GUF38EixftdTeXprhgYB7m9FVFNGyGF/rrXDJR5DcuXOG8AHPRDzKxuunw1iPoBFWDI80GKNYPRHzHdbQ==", "signatures": [{"sig": "MEYCIQDxlwAiDO/HktjeTke8/nBXGqJATlfxZm1jTCXhFGHAxAIhAPiYlfD5LPVjSUVxVeyi9C58WWN56Zk+JSwu7L02fw2y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "create.js", "http.js", "https.js"], "_shasum": "f684871fc116d2e329fda55ef67687f4fabc905c", "engines": {"node": ">=4.0"}, "gitHead": "dc709643b367ba1fd8039a83431f844ea8f03b64", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "4.8.7", "dependencies": {"debug": "^3.1.0"}, "devDependencies": {"nyc": "^11.3.0", "mocha": "^4.0.1", "eslint": "^4.14.0", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^3.0.0", "concat-stream": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.3.0.tgz_1515178643188_0.43387531186454", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "follow-redirects", "version": "1.4.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.4.0", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "a146a3a5d402201c7a3e6128643f0e336d212b10", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.4.0.tgz", "integrity": "sha512-SLUmsiaGeQa2qgJJzJgHpQ6lARP3uyVr0SkMryJmoE86XvUeM7RkYD5FT0rNyjCV5zHlNUpcp3l/6oUkqMEOqg==", "signatures": [{"sig": "MEQCIERunHCTFH1eMflD5Sud7UKqRlSly6gECLBjdOT4StPAAiAQw+IQJqKTZMLEIlkPC3AsKLSupY40faMFX5AOxvV5Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "8faff2e73cf2a760f1e9d87bc193209042ac5364", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"debug": "^3.1.0"}, "devDependencies": {"nyc": "^11.3.0", "mocha": "^4.0.1", "eslint": "^4.14.0", "express": "^4.13.0", "bluebird": "^3.4.0", "coveralls": "^3.0.0", "concat-stream": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.4.0.tgz_1516571750943_0.4768338876310736", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "follow-redirects", "version": "1.4.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.4.1", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "d8120f4518190f55aac65bb6fc7b85fcd666d6aa", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.4.1.tgz", "integrity": "sha512-uxYePVPogtya1ktGnAAXOacnbIuRMB4dkvqeNz2qTtTQsuzSfbDolV+wMMKxAmCx0bLgAKLbBOkjItMbbkR1vg==", "signatures": [{"sig": "MEUCIFtbaUYOtW2V6P+uu46cfsRf3Ci+O3mjfQstQAPNLVKIAiEAq5EBCtIRGCqZwlewETYAraXXoYJyq+mWPzlZlN8o1Zw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "1b6340f83ad5596a0a38c16a7113692bd90301f2", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"debug": "^3.1.0"}, "devDependencies": {"nyc": "^11.4.1", "mocha": "^5.0.0", "eslint": "^4.16.0", "express": "^4.16.2", "bluebird": "^3.5.1", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects-1.4.1.tgz_1516754560314_0.16931934002786875", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "follow-redirects", "version": "1.5.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "234f49cf770b7f35b40e790f636ceba0c3a0ab77", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.0.tgz", "fileCount": 6, "integrity": "sha512-fdrt472/9qQ6Kgjvb935ig6vJCuofpBUD14f9Vb+SLlm7xIe4Qva5gey8EKtv8lp7ahE1wilg3xL1znpVGtZIA==", "signatures": [{"sig": "MEUCIQCxizWDNnm5337cYvGMlGHXn71RTmauIzv+Pkjg41kcHwIgNeYV7ZloV9kvlVnaho+lxkmoBCQtxuLpj58bhIbqC20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAE4ZCRA9TVsSAnZWagAAhUEP/R+3IwcS8XgjikEcHeTF\nXGCAZ633ZHqPa3ZsXeCSQ5HtFfOef8nx8jIKsrOzNVTHZYMfiV4LqAZku8+Q\nIJD1sL395TeB9ysOg8iFINaLOC4VtChUcq9mwgwSIlacsKt6RoJ7Z9eZBjCJ\n2/mdTYxr3nXKBqrsg9lSkvkfeR8ysIHSgI3E6UTCVa6Q3E2c9vZqGbYSbTr0\nKTvf+ZWGfeWvc5xGj0epAISiJEhtdp9d+ATRBsJH8LL+gpGtwsM/PDp33TNU\nbAbNfBU73Brhu2XUtOABaKQYCWoDnb2DsuOphFnfZ9MLg0ZfmopR1QohaSPV\no0BBAhmnE73Ksp1xhk5JctTmLTH+zO163eUE9MdZ23i64Rhvk0ozZ4SRy6r6\n6d2Y0lt3E/A88iXTJZIMtlODgWm4M6dXneNJV+0e4SKDxmiJvzCHbMjgUVhj\n3eUMVPJ069yUHYa+vWR6MIeGyrCz6v21ip0Et/uvpql8kmYPLFTzR98SFDc/\n08DUTLBZIHFuBQrQPWfNUzaMLU5ziaE8x1d3HBwKjo/Gz8FV7s1i/3cXMF3u\nP+rBj0aALpynKEqligANpN5R01zcAp1qReyHsMvGjMuwl3KDpX8X8HIeciXi\nYCzGOWIGbC47080rnsJ4ct2hFifL8Ee3pmfYHet+Kn1urcFH91Lyb/oOxj9m\nJ/HX\r\n=oo9R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "23f66feabb9eb22e3b251251b5935725fb1845bf", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"debug": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "bluebird": "^3.5.1", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.0_1526746647796_0.20553048579519562", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "follow-redirects", "version": "1.5.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.1", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "67a8f14f5a1f67f962c2c46469c79eaec0a90291", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.1.tgz", "fileCount": 6, "integrity": "sha512-v9GI1hpaqq1ZZR6pBD1+kI7O24PhDvNGNodjS3MdcEqyrahCp8zbtpv+2B/krUnSmUH80lbAS7MrdeK5IylgKg==", "signatures": [{"sig": "MEUCIQCLoTOR9llDtJTLzmU9vKFAcpgp3GnnILsFgkfo298KeQIgVSQLQQL3P4NoDhXnweg3mHm2wD4CNKS3/xgIJ3rs2v4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPoNFCRA9TVsSAnZWagAAg+4P/0/cIO3/7dHL/bUgTB8X\nhJH/teo2Iy5mv1mtqE9OEltWx2JsrEUPC5ZVZDCCc8ufKsHvobs/84mH7cS/\ndCHG9xDIFQWMifr2MEDEudI0Zh+7z+0jWpVi3siXWJ4Ka/rY88jFV9gjKRH0\nyLL8pc50e6ibuAihzAcDqgu2TxRMcB+9CrG2rBOIIMC7ln1DIlKDhPBjtxeZ\n5dSOHgrk4SgR9nIMWXjXbb+vp8n72VHg07ppqXofLOZopqHvfuMXZdk8+g/b\ntHPVd73uyReXGjdnmZNH+NXptKTGEArL3elRC86L6no8jdtco7aFDG4EOGOl\npDgShyy4KWkl5JNglsHEW0J2C09TdyE5vYM3q0PBo6SC5TczuJsxTbeCM//4\necLAFGrrw0ce8D/WyMAp7MmcYmcu5UtjcrooxJ7xPNKcxMKKzR+t2s3XnrZ2\n0+asUDIotwKU7alW4k3FyeDDEI0gYtZhRXU4auR5nkCZJEashKxN38kDeJXa\nnBvejV/qcyX5zkVg7ZnJ1V9QAkeR9lXlxYSM2v3jwDuCZk0qkQ+Dj21VAyJZ\nUp5xJ1/VPcMPeATGFrOsEkruutDhriV/rYG1cGyyBg1ty3iG0MgELM9BzqAB\n6yT8Iq/1GV4f4drWpyAZbj1cTocw2K/PQ6+t2Ojt6iywQI45JEQCski3qznU\nHqDK\r\n=411h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "6a74f5480e2bc0f93d03577865e371a4817ceae2", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"debug": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "bluebird": "^3.5.1", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.1_1530823492901_0.29396077553661915", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "follow-redirects", "version": "1.5.2", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.2", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "5a9d80e0165957e5ef0c1210678fc5c4acb9fb03", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.2.tgz", "fileCount": 6, "integrity": "sha512-kssLorP/9acIdpQ2udQVTiCS5LQmdEz9mvdIfDcl1gYX2tPKFADHSyFdvJS040XdFsPzemWtgI3q8mFVCxtX8A==", "signatures": [{"sig": "MEUCIFtyT8iS0yOyTc/EmxY/5yh1cB6Y7suwvtNEMi9auT9HAiEAlE7u2rX7M1Y0+4pQoRhdDOVLdzct8AUxDISCmelMkbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYf1SCRA9TVsSAnZWagAAB1kP/ijEe8beBvmA/Pu4iq7K\n9ZsTHYQ5enR+xtzFpcdL7OepgE10ztRak9YTWx9nzQmQkqbBlUpTWSp4x4IE\nKDGGzat1qPSn5nJSgonotfNyuK9fVlE0B58CHRdvV9whb+9X0brs8DuKQcPb\nRQyuszljkz9UVnBhKmGKwLQ9WiFTlgywiMfY0yArqyVtSkyaOlwSczUZUA0+\nMhuAUU2cq8MUrYFDMTurV2258jRLFrkojiMYrwQlrA7Rsssen5R6lKGZURH3\nvePD8L4ORf6HAbkJUckf+gaag1HPKstz27PsoXp2nRKF+Lr7UFxGUMNlAxIP\ntZTAdrn/+026wDSf1MZHOlIx5L87yQfrUz93QmIkF1eqrgnOO1uc2Vfp+Fi5\nGlpgZschXo22v/fXjfM1+qHdlSbc92ZjUBKhu7qWUV00tFhqxkExhFj7i5dr\nzcb8EHMqfHlfrwhCIG3TBjVJBOwpl6SvTE5o8So7AutpJU4dfg4Dp9AGC+mh\n3sp3hMTtwpK3XNAIGHP+/tPn1S0jXCbiitjvsEv+S/kcx0QakSx09YkUkA8Z\nfegWmqthuT8w3N99VVY2H9kK3Ci6TRAzqUPqGR8Q6jDRp5GiOQtqEw1lTxTv\nvT+lpOrpS7ND4lmmkHpILBzMzBrGc7xt5pWVrn/IEQ7cN3TCFgiChJybIJh9\nedJE\r\n=PcuT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "4d328f0ef3e07575b7e5b950f9dcc521906104b2", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"debug": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "bluebird": "^3.5.1", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.2_1533148497739_0.4948117273541661", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "follow-redirects", "version": "1.5.3", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.3", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "ba150caf15e2f0b30c789993be9b912db553855b", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.3.tgz", "fileCount": 6, "integrity": "sha512-Q19GwKpz/zVkwVzbEkICtentX0NKcCix7g1l3vNewCLZ6KwGAU7bdJEQEZb9NN0D9kapySzQPMrOuj6rvrLXnQ==", "signatures": [{"sig": "MEUCIQCvxrh1hX2tLRWNiinJKzcRsfKv927BSOw9EfLmdXdoMwIgXQO0N3tPmidGcnwvefz8igWRc5VrvU4iSLfzpmAl4vs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcWhVCRA9TVsSAnZWagAAKNsP/iXcKhxuZgTu7/e1Eozm\nyhhM5uZzrEsiGhv2e8K9ZRjpldwvKJ6kUMn+MMJx7ZtioK378IBSereCBGuu\nHv04XJC42RZjEeBqu8CkP1NBM4wQgap9sm8cmTol1zzx2G7sNY//ekfbJ5mz\n4c8eKqC3De3LmGv/hdC9sdWWDyKET/52nhyGcbQDYAlT9dSpmGOHI8m9MkZJ\nhGUjip1vVqBxndrc47b7AOvcTS+fcyd2yUskxK8wq0nDfT6vJ38pcXOjIj7N\n62etsAg7KE/gfEED+vqU5MCBh/D4NuM4JEJggdVnUUo+gEkyh3R0ii/xvtju\nujU3CPY7sWL0uTIv9I5nkUQijnxwau45qsxGINXuvTiCFc7c9ILS/8DRoW0C\n34Ck2YYhMi9EkDvh60A4CAxvWyOWcIM3vpQchjd6uM7kCtF/4MeWqbDhB9Ok\nPbf3uXNC1t2CbNxPaQAZ/u0DpUXzU/eUKygcxh7i6tK0L0XN4sTTF30fJv8B\nY19O6/KJDqRqqrJYUt7qYLD006THwx3/lnBzt6pdsFNmZT3CgaGe/W2+fCDO\nE5qGr4mKlvyTUUztXbsLXXSn0j1EsFrVZoe4UiocAt3vwkBMfAU040x77xPd\nZVmC6c+txQXcUnx71cnKamp/dEri8Bm9M/iSj9RcUYm4nOWxK0Mik7zD5qMN\nE2kz\r\n=v4+N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "20c5169d1a6dc76ab78c69ccd78d2d884cd16a27", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"debug": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "bluebird": "^3.5.1", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.3_1534158932609_0.26887812892104646", "host": "s3://npm-registry-packages"}}, "1.5.4": {"name": "follow-redirects", "version": "1.5.4", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.4", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "86d1bb946f24cb988d660aaa2ca2478c0772ead1", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.4.tgz", "fileCount": 6, "integrity": "sha512-qGsq5ExgV2z6YTV2mkqemjo6sy6BaXWwKlNYSmpvBONGdFDcpML0ieQ7iQDDlu82JrMTmft4zrpSFfS8PIPoHA==", "signatures": [{"sig": "MEQCIDtF3RbF3VWEveK7Nz3kkx+Nv7pn56GpVmN+mut0qettAiBq4+Wt721eY+dUs2esOHiCIt/6lDL9Y+L7XeUHfkK5ZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcYUPCRA9TVsSAnZWagAAoPMP/ROCKpdJmF1Ml1wQloU1\n/97jDpo+EpL8l6wRZjTbFaGbNT5Wn5dK1wbnhEoWt8lTc19kvCOAWktSf2Hx\njel+Ci1jOfDxavFrOaYadQs8iHLL0jFVeXzKh62V2ocme3474LIhLqIgD4YQ\nJ0qtAIdlhe3AQQf0lRUjDITHjYixd2OUUIAOpk51gt975SRQam37tIUNAC3x\nZkaKm7aiiCpzzS5fR/cI2+Cx7kboExrFqmi7GUY+aPtmpWn9rTdD2kKpFFUU\ng1uSUTza34ytYltu9TvNXiHOH3yFpl1eG/o6XJikf/d3ds0jwr9lV6glvel/\nHqSp1Nlu06YjlddML++i/osnNkPzdHhnvvWQKtMI9ls+bXcYHWpEApO49Wnp\nasCtK4t0HWyNJJnGri8+gr4PVIU3uykAyM1JKAFl4b1qnPkg1KOsxRRbx5VA\nwBLtoN/CZukQGga5co01/OiENJssfp6NS0yUMYMni0Myi5k99B6JSZUaG8fi\n4VtrLR657NnZ/Fcu1D5csqR0fjFFrWaxZT32w6shra58VqWtubgEWnXAdy/R\nwjIJON5aETNTciEAmGzqRxz1bNHMjUCpeNIx+UXbnhAEVvm23GkjRrsRZ0FY\nD0XeX8a0XkxJX+tgfF7X39GqKf2XzRvTgFYejPM2khAL7nWJuMkOtprIc9YQ\nxYFu\r\n=0WBo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "6dc19f9f17e087c979c44d91215c445dd34d8a20", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"debug": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "bluebird": "^3.5.1", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.4_1534166286574_0.17784307842570124", "host": "s3://npm-registry-packages"}}, "1.5.5": {"name": "follow-redirects", "version": "1.5.5", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.5", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "3c143ca599a2e22e62876687d68b23d55bad788b", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.5.tgz", "fileCount": 6, "integrity": "sha512-GHjtHDlY/ehslqv0Gr5N0PUJppgg/q0rOBvX0na1s7y1A3LWxPqCYU76s3Z1bM4+UZB4QF0usaXLT5wFpof5PA==", "signatures": [{"sig": "MEQCICpUbKRh6Wc4SHJ8TbKQt/tZsZ5yH+K1XjudglZz0O6FAiAzHfWAZpYCjLr3Oc6fs5NV8cZ0xHt92U+ylgUynulzOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbccemCRA9TVsSAnZWagAAtCEP/A4f3eHwGBSRl6Th3xx1\nwKeTmf6qczJkIWGE7iw9L3DE1B6sGW8ttCn6Qj/AQVWGDcWtYP32az2Kso4z\neJ5Jckbx8hHXVtJLEEHMJk7kymPHXHQenhBoe6u5cpdqcgDzILcHWFQ9mGcl\nVVMqMEzc7uuVvY6akf4vxmLAn+lOGqwlQz/cql8jpOVP0CuIFhFF//VRS0vL\nv3yxF+1CjKv4DX2zapo6H/KmbMMPs//7ofNbg87qR8DGT3BCY9XO6sU8Ze46\nn8/jDcYLmF6vjzMjJwP7qmeoX5PpK/CmGy8KJt+EEfFNi3KF63FGteUBJ7NP\nw8fquRpeDTHTOQRnTDCyvYM4kKqlZK1IrqoFzo5DNh6RBUWLNR/Abie/Vbke\nqvq5njsSghqLvJoTwgvMyxj+Bh54m5FQWw4xvh93uF8SsmFOGukvut21BbuC\nGD5QAWghlv2CcKk3i2UE86z1iCnVuWCTNwmhJHIoX98XKNnDGRbB/iAETe7Q\nYsCoZOLRgCrM9ZR892A5OHSGo8w0PqI7cVPYSGsVALUlc39CVCJXDONJVVvp\nm4Hjp+SEWpwXAA03JO8RLMTnzFs/pyRKx44PrUcCMBziNO495bc68qpGoiAs\npt9LainlBMUzdrqxk9ELIWgeR1ew96AddAcBV0e3tWGlSBq8JPO/mgeAAbhN\n6PVf\r\n=A7dn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "de80dec9d8e7a1d99a242e357e653039b96bae9e", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"debug": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "bluebird": "^3.5.1", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.5_1534183334213_0.1548457281029405", "host": "s3://npm-registry-packages"}}, "1.5.6": {"name": "follow-redirects", "version": "1.5.6", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.6", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "44eb4fe1981dff25e2bd86b7d4033abcdb81e965", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.6.tgz", "fileCount": 6, "integrity": "sha512-xay/eYZGgdpb3rpugZj1HunNaPcqc6fud/RW7LNEQntvKzuRO4DDLL+MnJIbTHh6t3Kda3v2RvhY2doxUddnig==", "signatures": [{"sig": "MEUCIQD3UXTSTXjntcgnV6uyxiWxG4WtIUwvW7GbOja5nmU5RAIgLqqsgF3LThS5FTIUOPj0eYIajTgw6HcObEKqflaR7GQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbe26UCRA9TVsSAnZWagAA/OgQAITs7BjctCy1nPMbBS/a\nekq3MPD9/qd5I7q34MIC7x1ryS1VJRhB5bcUew7xham6tzgX5kb4kzSs7S0T\nOCnydQUxmkHqJ2ArVek0GL1BoAcv1aukzGBUsA0DfM8glWDezF0l2ovBGsSa\nd8VcoxsU3Ez4mxY57bWzJWo25JxhfHsEByFv0Q6NLrRmetA6onzTvUyqmRQw\nhG07wzvjPWTSMEMdQM9OPAYd6Kj3U/AoPw8mnF5gurBCZx+raoC3knbRzW7o\nUzRjyo5x+nTLIK/5VR2UuZACaD4KxZLP+K0x94vZu/pwkQcsqJ0vNHQfTsdX\nu7BweUZrG0tPqtyw6JIBhW7CCxs7GoUlNiBnCP/ssrZEOlVuFCAKPyzQ1+sf\nd93p9eHBxTAd05b59uKqWrWTCIRSwoEt6C3EXKdkfB+LNyVahwsCx3jL+wMN\nMnHtdQ8K0ypa/6AsopMEYNgFNbgYdiknkCtVQW52B+uG71Ej+PTpqJ5e/J8R\nOP0xUoiczr4fP4LxHSJmP4ZmQexSnkv9dmkoMk7dGyWl/RJWT9KfAfGqXNnV\nq0swteE4yEy6LSV64y6thouq5UTSdGboTNxuP1OyFA/8RJDwCmC2zUG5+T7A\nvytO94UmDI15jAT76mppeeTAl0hzOq8LOLJOWk/NZ8e5orhVJA5RVoJyBXnc\nktQI\r\n=u2AQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "7d4a56aa9d33dc91895f75ada95d725b6ab98083", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "6.4.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"debug": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "bluebird": "^3.5.1", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.6_1534815892063_0.5635425139833941", "host": "s3://npm-registry-packages"}}, "1.5.7": {"name": "follow-redirects", "version": "1.5.7", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.7", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "a39e4804dacb90202bca76a9e2ac10433ca6a69a", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.7.tgz", "fileCount": 6, "integrity": "sha512-NONJVIFiX7Z8k2WxfqBjtwqMifx7X42ORLFrOZ2LTKGj71G3C0kfdyTqGqr8fx5zSX6Foo/D95dgGWbPUiwnew==", "signatures": [{"sig": "MEQCIGPPynTilw6nBu9EhJs7Cq7ulmODYKsGMV2/D1gh9YzOAiBhTuc3R5/x/zwfl1jVR3Cjdl7GEBYSB0QbujlQMCPmLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfaVBCRA9TVsSAnZWagAAFGwP+QBVZLosWW0bEwyhgXka\n64JvK0X82Gz218ZTzxYzkXPVYtoCZm+gLAppdaAKS+xemhvw7UFYoYSopVb4\nLF4ZL7JCTz7pyn1sDuWHnqfd/xNqUPYfHF0ylE03tjEfSJREM/9q2tnt9ruJ\nfE1hsFa3GGuUNsjbntMwhJ+o/3kiXr6dZqTStsOg9W2oWd/vzP5cxWCX3RxK\nSVISYcSZrVu8e2vYBhtedEcHjIC2PVUufEmHrcOQ8DPvpliOT/L4HQbXwrfA\nQx6ElABUSyD/U4JvV5799sdV/JTxJ1HKMTqKNqeeO0j3D8HeAB95ykqI1zwD\nUocEz4mSPNDIiKYr1wcP6B0rs7i/pqbSj8v9mlu3385ziMV2sVJJ7zfJj0Q5\ncUE59dbDusFxe6QSaliPbfq3kjwgLiiFI3fVTEXDnwZJ3y9gDgripyHpx9St\n9CrkNTe/vT90tnXb3Ak8C+exNC/yZcfFd1rXz29Akk6uRHQQR4536bEL/kb9\nQ00ozRuZWJDXev8PZRnO0R/PLR+NP4+ZxH4l3nkF+RVtJXOzn9D7IMw0MV1Y\nOoQwyxDdokTbgvNSlnEfJa3jWxJ0aYrJTnCnnIF9dyUpvizFNGFROifuImgH\n6K7dxHQtH4B//2EvWghOB51u66FS7UglRTRArpJ16h9CrTETx+2I7Y3q8yNK\nhY3M\r\n=G3OF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "create.js", "http.js", "https.js"], "engines": {"node": ">=4.0"}, "gitHead": "7fd6bf3a56cd1632d2171d7e11d3386e1bccb949", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "6.4.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"debug": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "bluebird": "^3.5.1", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.7_1534960961042_0.010777989922793552", "host": "s3://npm-registry-packages"}}, "1.5.8": {"name": "follow-redirects", "version": "1.5.8", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.8", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "1dbfe13e45ad969f813e86c00e5296f525c885a1", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.8.tgz", "fileCount": 6, "integrity": "sha512-sy1mXPmv7kLAMKW/8XofG7o9T+6gAjzdZK4AJF6ryqQYUa/hnzgiypoeUecZ53x7XiqKNEpNqLtS97MshW2nxg==", "signatures": [{"sig": "MEYCIQCcPopuPIRu7rhDy96AX9OzGGCvTNFdqqcIHWrE4q+MowIhAMcwtkqAOLIcnnCcLIuNCQv6lsIxahxiYO1063WdsbPh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmBa/CRA9TVsSAnZWagAAuTsP/1UQNUc2CA5UKKrSLI5E\nStiPsQXJI5nmVuNOyTHxFb6bWzRa4rXlLxJ/NkPyGXl6Vc+DMof1h9AU0vRX\nZwpSP4wp/kB49/ZS8+t/0GZ4QSoJlhtOpubL7XyHGsssMjEsMcTBWKQc8MbL\nUobJnJapu3ryyuj2sBh0UwZAaUBL0PsTCtoDWzj5um5sdsU/2wFjKMVUFBl7\n6DQolXWUFcUAnwFC9UnPMxKIPuVIreksVTpx6SW6l26OSEmAm3AWKEmD/mbD\nIHiIUowJ8VSwSRaOCRUKSwfrFp494nIJESDZAU8yMB7m4krN4+l51I80jZeO\no33NpB3NBBWz+Tfj5ST+vIz3U92FaMZop4Q4qSXR3fXXg3iaAB9DsNRvMlIz\nbDQYRCWSBpnvOBzPwDVPZmKexQM6UpQphlLTTXu4qtuVHG8byc3z5oWY8MFw\nlgqh1PGXjXmwhtFSnd2cXd1voUK1Ljrzp4ei356uScjE/8kzNr9shgPz5y7W\nJNqlsmnTaS7LWdVFKcxbbFY53mxxEh6Rx0CuH6v963IDOWWBpyyIOi2PO2em\nbS52oglk/h3NBiP55hdDQ+dsj28h2Dw3Na8LIyDQvO0vhbSiuKEKNxRC40SP\nYCtpuhXluvtYeEvlFzWcQaUNLa1w0wZ1cERWaq5PeilVA3sS57Ib9RXZcd4k\n3pAC\r\n=JSYr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "0a75768c122cf693dddfa85fd218f35c29bfe81e", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"debug": "=3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "bluebird": "^3.5.1", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.8_1536693950574_0.5199163289035582", "host": "s3://npm-registry-packages"}}, "1.5.9": {"name": "follow-redirects", "version": "1.5.9", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.9", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/olalonde/follow-redirects", "bugs": {"url": "https://github.com/olalonde/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "c9ed9d748b814a39535716e531b9196a845d89c6", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.9.tgz", "fileCount": 6, "integrity": "sha512-Bh65EZI/RU8nx0wbYF9shkFZlqLP+6WT/5FnA3cE/djNSuKNHJEinGGZgu/cQEkeeb2GdFOgenAmn8qaqYke2w==", "signatures": [{"sig": "MEYCIQDg82nzO5mA7s/82cftc+dMlVaivTUaS8jeiA+1thBptwIhAIxXLDomuO/qhyipTeM1fVphPsiLVjrJFKOO6Jf7ROSP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvNDqCRA9TVsSAnZWagAAvKkP/j2g9UU23tkeBbnBBcDU\nfk0cBX6C+0D6Jo4FFXLr6ZBCZ77yHwZLHcbWGcHJzGaBDRG71aQC9DpoEMXq\nPeBGtp1Ag8tS1uC8SgCq1jYKqv3J84NzkpnKpzPA7PWcU6uG9erGjcRKenpB\nyWnF8EUTNLFA52WBhfE+lzn5yNx2nEpdxV55uYAwTyMZkub8edh/l48EwG9U\nH1XsySLz7MsEXr+1zsKl9MwsW6Z/ukFXFSZhiIJVM5ygq56AMiN57644uEbQ\ntXet7FNj+JafBXSuQnqrhatWnqu6oCxuuNYGpYiYjqbE53ExF3UHHRaHFMpY\n/IUbGUg3NsfGxgpBw9NGrai80eFhXr+m48dpWHGD3RjBRRVsbkjM1klxLo00\n7/o/nFGckmmf5w6GO0ev960QNoVJuDhDeM7Z5aDloEmazfUA6SvsoIHSCxEf\njxYh5RfnuNq5f8neqgvxp4TZ4WIzwHt9qL17YYAj4yqFEAZAfceZ3rrL4dcJ\nXmrEistf5DAiChl92vmUMT+hGpFUrUbk8cQ/DM4w0+Gk3Ikkn3J5fPBEHiBl\nE9HQNzkDrPzGTgnH+rRPw+lO5CDBMS6RZ/uoiQI+dDETUKtLg9+uCsfIaGBN\ntXtlW4EvzTLAASofbQheIOmg188y6vg4R4iNB0vDeM576QWpV1Jn9MSw6BEO\npP1Y\r\n=Zxsh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "90a52f7c9bc084c20ae167d7fcd0ede4770f582e", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/olalonde/follow-redirects.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"debug": "=3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "coveralls": "^3.0.0", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.9_1539100905320_0.008006507326728585", "host": "s3://npm-registry-packages"}}, "1.5.10": {"name": "follow-redirects", "version": "1.5.10", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.5.10", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "7b7a9f9aea2fdff36786a94ff643ed07f4ff5e2a", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.10.tgz", "fileCount": 6, "integrity": "sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==", "signatures": [{"sig": "MEQCIDo7OjZJ97DenT7I8gMpUfNimsk2Gh9gxUAK93i33JeKAiALlniEkuxxYbtvt9LD8AY2a2c9UI6Fv4USpepGKHcBrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb8ypeCRA9TVsSAnZWagAAR7AP/RwuZoZJOzKWB2RcWOk6\n+U2yuf3DRHCEEZuaynkOZTBc5B+xIpYFkDWgO+3lFQ8mTFVRfl9b87vJ4tQ2\npXeYcbWvt79hB+K4jh2VavKxsA3zByh4o0AVOWoaSn5qzull6Z4n9InAe2hN\ned5R3PZpMqY4YXuWMtHCLn+NJePGnFyEY2ESP0Ehs4DKDypj02HO3s9qCuqO\nlhSw9VmpQ+imJMge8o7EyvwfZaWEmb0ERaL1thLUOY2trHDKVO85JXla4h38\nM4oOc1g7o3yc0rVT8vZk9C56lkTQgULBIKc5HXUHjOkTO7wfbR+vq+cuUMaq\nBqyQb2qsy54nPC8EK6C36nIKXIy2F0UvARc1x/302XQkCjNajEIoPtX3vouo\nU3MIg8UKm0jJxjf6/PAgeDN4rtHaDUgK3N3YMzvhcIU/H+Bz00rEyn0GMCTM\npi6zo6aa3VXjyFP2+vQghXax6PYcbyec2/cBuUk9KZOfORvJ5gmvCkbLWWZC\nQUapjl2+4sWA2j1kT+m8JkUo969yiayjmKeI4zP0jLPFwZ0IPcN1+/ekMnD0\nAtwF0k7RPz06GrdleYaKk/+3+88ViuEkfH0tBomMIgSZ7Zui0SsPS6ToQcxK\nPHjOCKAFOcYs10ZFhfhgV/wF8LR16rIRwXnlYREKQE6YuAMvhNN5UyLEqwNE\nqVU1\r\n=Xtc4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "91842752013b26ed780f7b04e557c72f02228cde", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"debug": "=3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "coveralls": "^3.0.2", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.5.10_1542662749815_0.6076235029881887", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "follow-redirects", "version": "1.6.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.6.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "d12452c031e8c67eb6637d861bfc7a8090167933", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.6.0.tgz", "fileCount": 6, "integrity": "sha512-4Oh4eI3S9OueVV41AgJ1oLjpaJUhbJ7JDGOMhe0AFqoSejl5Q2nn3eGglAzRUKVKZE8jG5MNn66TjCJMAnpsWA==", "signatures": [{"sig": "MEQCIED8LWxmMkVwc1Dvbl95Xt5d80xiGDw1ldRdytJFOIX4AiAPezvHQW/QhsPMkMAdIjibbeztnz5J9hgXQxw3X4IYFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIq7WCRA9TVsSAnZWagAAuNEQAJWykCTynA9/C+60/9m/\n60s8Un+AfmRb/EDi0juPUuJZZQ0W1/RQfN3oCBpx8vMMjuztK/zUuLa5wLI/\nsX0Eaa2dG9TEFqEKWoQvhXv5mP33uEoa25yYtku64usRPXV6cw/1oggBKKlA\npsYNr0TZz34baoTN+oT9A0FpsByRA+Tn+HGtzRXY6J55DxZAbWoJAS5jTIOF\nwGvYKFPj+UIG1E0o8QiepgslMM6D2P/Xx4AFN25E8uMcF4mEkIRS979UIyTl\n4oCJch98KgwOqcb4ZiKO/bDXdXig1S8YUwoPAK88unGATIgitLLQtpYNspWN\nx9TJen67V8askFhyDng9a3dWfHtYTi3RDORCx4j216MD9jHGPetArQaYi0aZ\nUTLvuYtVqHvwHPZ8UYzmuMxyPhMTZEPhRTbtpgAkCRBMhrWy+gaz6nkWU2aW\nqp80LHIIdjtCDtR9IWEnRmX+JMJx2s/0N+hMGqXTXpDs0M2HxKkF9n3wPXVI\nlprD0dPuMNY1gR9cunSzbl+AF5AH4xif5QVdHGoSkichuhVFUZNub9O36sTA\nc6Vw7FIVLF6fcAIk0SM/VLMq4G956rWiFcnhChrmvXjMqlEnmZWL9UrP1gwh\n4dMwM0hcd6313x0PgiIWfGCFFP/2gAceHD6rsGpy1nv/VRhBYKHkujNrAoWO\nMaEu\r\n=8Rks\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "10476676a84c9f1e44b93e4ca28a1dbf7145e673", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"debug": "=3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "coveralls": "^3.0.2", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.6.0_1545776853871_0.9355239754490214", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "follow-redirects", "version": "1.6.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.6.1", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "514973c44b5757368bad8bddfe52f81f015c94cb", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.6.1.tgz", "fileCount": 6, "integrity": "sha512-t2JCjbzxQpWvbhts3l6SH1DKzSrx8a+SsaVf4h6bG4kOXUuPYS/kg2Lr4gQSb7eemaHqJkOThF1BGyjlUkO1GQ==", "signatures": [{"sig": "MEYCIQDPIpN2aJ/e44kpLyQ8SAqFDEM8x0G4ZJui0pAmR9tSugIhALtLA1Wh5unMLu/wmF0JQnOATRFu4+wxK+CxEdBo0xlp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLeLrCRA9TVsSAnZWagAA7GsQAJGbHWOu1A2HhmqPwWGG\n0xWiMBnggNuxljneb+J4JXp+2gPrC6ZRnWoGijY4yu/d5zv9Z4Yjt1/7MgfA\n7sbJ110OIyinX99qEbB75R9xho4kmm6eLfZX9xjMiK2uNcMCtdUiZMUhE5dy\nRYZDT7SW+/9JAnqWo2FyyR1aC/ypZWSVZ4p6kSLR4tL841urFyk3WmZzsHQT\n9+FAkmSlSkzGHfiDitzh6nHmncztCXZ0abClV2q0/BdYaGkwlJUO0F2uagBF\nhDIyiQnIhiN8/8IRJDozlr/an5PM3RTZs3S29/RoeLnURpTkWw0Hk0XN6ZVH\nf/YvuW5sHGwJU36pz6OnfJeLWUcRcxr8EDQJBqAaGqZURr9f9Rsv4/r/D0l2\nR52pOQSbOxwngYVITDGmXPkYwH/ZEjNtiZZW2RVuJx1joIzNOE7s93Mvu79i\n9WPjcc0KXhK7ANaNE77ZqB5v1fIps050vlaYPVnbr5n+4hNYvaZf1Ib4cTTW\nUJN5p3NfsMxnQOVD26Sgs/QtfV3BVC6xTJDyS398RWDauWXZMv5r+LSLHJYy\noiAw6YKYcjpv8Dk4kl3Z8BAEwHVr4ftKIPfu7fGSGbXqvh+9dKHeFIaUdQGE\nVYJirggQPnUammloIB4cH7ZGKgv2QtfEy5CeunhQk9ZEGVMZDhvpaYb/Ed2e\nTg8m\r\n=2SpE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "0cbcaac65e5c0fdff425af75939d0e92f54cfe73", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"debug": "=3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "coveralls": "^3.0.2", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.6.1_1546511083043_0.9607381997588877", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "follow-redirects", "version": "1.7.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.7.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "489ebc198dc0e7f64167bd23b03c4c19b5784c76", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.7.0.tgz", "fileCount": 6, "integrity": "sha512-m/pZQy4Gj287eNy94nivy5wchN3Kp+Q5WgUPNy5lJSZ3sgkVKSYV/ZChMAQVIgx1SqfZ2zBZtPA2YlXIWxxJOQ==", "signatures": [{"sig": "MEQCIEu+c7zD9ie7D7sxvwGDLBxluzM28HIow6nsxDLJtzybAiA67MJfF7U9iDqQFl6XsRl48lOMCjGDTuBe4KokKcVS8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZI7aCRA9TVsSAnZWagAA4wYP/i0UEluhn6QsNUW7FsQC\nV6csdzuDsSeSgskqm/CI6rqTxpXxSMUMG4GtFfnynrAEYz2dwa+KNonZOvhc\nP0DRvRJerUwyblF6deVA9cYDNj8WSE1ftOjQp6X7r5LW4jud+vTM+tCYFvIo\nbJW1IaTunyz1HNemveYSwC8W/qeCuDY7UlLOoz7IWypivuLZSwjKfcL7BVjO\nVwn77IivSNNLSbDtcmXIiR6zGVBSkY9JMXPM4bBC5kJytWO5QwE6OzGY0xNq\nTDnBh0hHNRw3RkcIotM/U0YVais8HuaDr2WO7/N3pGfqtLVHT92hxZtIcnSu\nDJFzs8ptzBB5jzx2/uD6wpXq9+fNxc/EcbeH9GFNBxMA9Ks4rT2VdbjJnkFu\npWfAwaYKcCTz0CRffkeN64Py4NDiwQ0MuNYlKlGthTzz08f3c7Z/DssV+ffD\neVrZ8C0Z400+dsMdvGH74DKw+8qYaVXZ++Yaxc8PoG+mKnW49IAa5nN7huL5\nvNNFGES6lVnOtHPSJpdUK9MAJGr8L1PTBqZYr8ALVcUkHeKzb/3Z3Z8NfE/i\nCUdxcsCoP+C52bmESwhJIN3SLSuMb4Nbx+/s3f0zx+gZ2HieGQicYyRybBEj\nkkzparnDuGJ+63+IWc4rbEv0vD7iw8EX5SazL+/DDAH0X9jsU6gkZYIVc6SK\neyjn\r\n=xy7d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "301e0b45122faeb8fad8b887b6c73ef36538d628", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"debug": "^3.2.6"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "lolex": "^3.0.0", "mocha": "^5.0.0", "eslint": "^4.19.1", "express": "^4.16.2", "coveralls": "^3.0.2", "concat-stream": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.7.0_1550094042182_0.6861824070554166", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "follow-redirects", "version": "1.8.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.8.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "nyc": {"reporter": ["lcov", "text"]}, "dist": {"shasum": "dcf34930bcfdb6c1eb22b8eb7c457ec95a3dcf40", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.8.0.tgz", "fileCount": 6, "integrity": "sha512-e<PERSON><PERSON><PERSON><PERSON>+vwXZ6LfSQicvqFwaNEF5xTvnB/rpzRLuqwK45u7WbBEnQ/dDic66KD/A8IzTXFlj2ROAcaP0f2v4lg==", "signatures": [{"sig": "MEQCIG8VGOy6eG3uETe5sy+0jcdOaotPVEJospunWbYXJMGuAiB+DItCbGU4QKlCBu30FqRszhuYYCIm/HQs6EG2BauN3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZQ+vCRA9TVsSAnZWagAAwA4P/iVHFFfoYSdGJ2tiqvXX\njtykXmGCqIgwK1pZ6tjotSJSVDT9VZNFIVZCuIcPIjGB/TbH7kWCF2bpvBnt\nfp3Z3BW9+NGv7/zBSymQWSzYapB881VWf6O1Nuz5t1T8BH9TNb56R9L1ooVO\nZQYUOKB3hw3EyhQRQWl4InUzSpNwh2oLr4l4TotHx6/AUEq29DPp8BxoXe4F\nV9IOWW+ViVphXnDtHSi8zulHKv9ZwnoknbkSnD+8PKFMyEpDYM2VXqb2Oxsn\nYkxp9OSCZioJtafzzOWfow4Fo3R7+E7IWlTBlhXr+QzVOYPS9eg2QXgY/nPr\nwokYdQIMeEYOy906Xe8QoRiOJgcqWutcPivFPeodp6gAzNUsIAKm1qAeNNPM\nMhj7Lml3i5eTa5zuIGAZEIwhHaNqAyPB8seaih140jIarPxhTzSN2vCqCgmN\nmLyMki9CM+v0jiljxrtdBrj1X8DZEMMOYTKk81cdpc/EFhgns1MG315lZm97\nMIYNbPU18IdYTy7T7iSdlTdVOufwUiDmaayZDPQlINb53TgA8+Huck7gaTGs\nPSArjdXVkpVvDDcuo7QEX19tSq18hc/kgVWFndkmZ+v5WZyv/kskh7GA8qZZ\nNlcRxdW2K0CPGHOCSAl1hLaj2gmWXEwejm9wn0zskSq8sbm5T50iLxTjNK6W\negO/\r\n=HCb6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0"}, "gitHead": "b7a8216d3014dd82a605bd36e8756cd40936878c", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"debug": "^4.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "coveralls": "^3.0.3", "concat-stream": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.8.0_1566904239145_0.5933962124783387", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "follow-redirects", "version": "1.8.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.8.1", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "24804f9eaab67160b0e840c085885d606371a35b", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.8.1.tgz", "fileCount": 6, "integrity": "sha512-micCIbldHioIegeKs41DoH0KS3AXfFzgS30qVkM6z/XOE/GJgvmsoc839NUqa1B9udYe9dQxgv7KFwng6+p/dw==", "signatures": [{"sig": "MEQCIG4pBTFZNlxoBnaZWVCW4rbddfa8TOCOsezk8h75nI7FAiBU1fF+sS5jr+hnZLjFLccqHSQhJ/UU4f41Be79zN/WvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZWpxCRA9TVsSAnZWagAA8CkQAInJyjtAmvR/Jmc2H4m+\n+GLuWGtxQXAh5jAVfLFVBOYAogY5OGqNQvkKLWYuPOp5Q5c5WYQYnXz3uifI\n647JAXSjFOIEIDDHi3qWGvQbA8NlT+XoHRICocGk3/ogir4x7KMx1L0UZ2Yj\nwSw5zdJB9iP4LoGMuOx7/06mgv9Pox9QOruKM4tXqAaraPyTRAAw4qf30kq5\neRAKGujNeaoeOaCqHym9mB2p20dq9VvRWm56QDC/lIzuDip26XjbduQZABjM\nOiyI0NB54FjT9v91q6ZgIqSIwSuU4/MKoBDymvr95/DaQgiEcW3P3IhTHzyv\nGQ0CNEF+ruPBbw6DhiBhWB38Tv7LvAvSIb7d6xOuhrYIFLYPfkIZIfSNsigZ\nZPxILHiPShHQd1rJUZoeu0Ek2PVRuL8znnXsIj/B32ZjN11gRii2NfWZBILq\n5cK8YTBfnCXbUhpiBhAjfr93P/CGZ5oJSBaZCG30XvAn2WU5AUw93jwen7HQ\nyPnvUSR76UYdMQalugCyn9oE6fq150XFQMI9rNNN1GfxeTEPTcefvAG2rrtK\naakpIfkcNIZRPvntKM5KDeB90ah1KhNTVosZ3mPk89mXJC7rXCls4cOh+wxq\ntXQ7G/pLy+qrzkhlCyh0r++UHtM3i5dh+bEcIebnD85GeqL+YI6S+37uAEAY\npOoq\r\n=SrNP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "5e38b2279e21086952aaa8590e0d83adf9c028f9", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"debug": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.8.1_1566927473228_0.8108710547104683", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "follow-redirects", "version": "1.9.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.9.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "8d5bcdc65b7108fe1508649c79c12d732dcedb4f", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.9.0.tgz", "fileCount": 6, "integrity": "sha512-CRcPzsSIbXyVDl0QI01muNDu69S8trU4jArW9LpOt2WtC6LyUJetcIrmfHsRBx7/Jb6GHJUiuqyYxPooFfNt6A==", "signatures": [{"sig": "MEQCIFEPz6A9NGCVQAdjP5HbN277TFtVj1+PHBZB/mHxC8wpAiBlOAeEW0JHsulOVMWkQKQ2UcnOWuhwbWCYCZazxcb2fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdclssCRA9TVsSAnZWagAAz9IP/2MzQdIJBkQMV2fMW8eX\nejrm9NKiRDWy5iHHlMNP4Ab5Q/L6EoWQfIey+tsvVXdeKg4yX3CmrGHAD2YU\nRPDD2m/D+9ODwl6DkA1eJgB/CeFqOuPXLdAO996N/HNJraF1D13X7eakNr0X\n46Q2l4fXatrHbFnjCAQXDbn7Xc27wgqBYWz0QPFhllQU025/vnk9p0mpgb3F\noUaSiK0YyHpWTlFHTI9Q4T4JzLn4UOtrWKAII3OcfxDcoKvM7HHaBT+Pm4aF\nQrx8MrC/KWgARmN3b//Na4qdjvVSigiXfG2jyVXx+KrryHCmN987439BsvYP\nwM172NFHOav+GCL5pCvNnrLVmtyO75TKpd5yPS4usNiOlZYl8lVrJGBo7e4W\ng2p60W6ihMfVBNFagXK1B6MXFi8ukgXFk+Y0M8peY2ZpA3PwBuE2RIf+fkYn\n74tghvFa15FZXO7Fm8G7sHHA1TNj4/6eehTXir3+P4TQwMCTt+WHJpbCcxfk\nCryarN58fnkYI6ErmFYZsiPmuqsuFE6ZG3XTXu7xq1oSmIJneByyc8QhZq2V\nGc0THH7minRYYN9QAFYNDPORBdHAgwyFctICpNFt3rqFVK4eryLqbMJKqwSR\nLgVuqL2uWOd4K3zxeLYsqmDp5Ww8SdITJFtVXkyJfK5T4dSwtiMyQwpgJTfk\neWy5\r\n=NYvs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "2215c54e527663cf39b2c4e8c6dd862207390824", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"debug": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.9.0_1567775532196_0.9615187287965778", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "follow-redirects", "version": "1.9.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.9.1", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "26e329669886f8b3baccbaf352067b0253da6c2d", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.9.1.tgz", "fileCount": 6, "integrity": "sha512-oUNbrdUjHItyCytZQrHxWQ81ebL4xCFLH10sG0poUMgbKWoBnswpICjUBld3PLJ1lF6cCYVUBG7hAdLro0JNvg==", "signatures": [{"sig": "MEYCIQCNr80fRF69U3Q44UJhqSrmLg8icflO3Fe1S4fhRX4WQAIhAK5Br1yZ8FQgvZhFstNZw7Lm9xehzBpn2xgTIv1Qk3tX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLNU7CRA9TVsSAnZWagAA9OwP/0q/xlzGdXLvctnmG22L\njYFGGcAzMS04Jve+FmofdtUKQG5YVyQTjJsy1Te8mJkggc0GYHmxcpQFSOEp\n6WxT1jjc0aQd1utL8T0hANlEvtPYcqV2o6vn14GQwOgbbcAc3FhjAeK96+Wl\nsgk51vwTlVXqoI1VUrq8ckxzzs4zGI2LaHINtIeMF5fBvRH/FkHZMdw1eUMs\niJhP5ydEBNBXSj/aD8d5SybbwW3Dsj2/ighXIIjck9ssoWBTVf6UtxeJR9P7\nGF1Np2W0psLbGfMQKE1k3XS+UTOhFAkXrLT70pL9YzcZskvGOlzws6aN6BSB\nVDSitmNmiSav6b2aQvosG0aH0K/PFD+wetCwo42K3AlTP8jIwZUKiHmPenJs\nezE8hpJ8kHra73GAzsAkQ5r5P/2Ic7rrzJxElLkgXOwK3ld4bEK3StsmSJwO\n7gCNGFSGXHk2if7cK5lwA430rRmgAdp23EBIGg1qanko/BaiuhjXvEo1gYnp\n5SSwy7cHdzG1t6AYBhiic0TZADNDifZB+pHd4dCZjxibY9fHHg6Q88acbiKr\nkDzJQWZ4yGkZp1+3Vg1WlnQxRn4BfRu63ux7zm0D24Gk1GkKu6af80uz3/Um\nrbxTIhaObp0FaD8eQsk+uaMAIyeOACqpaKrzngTmeS9utLygkr3DPYYJayXw\np3Ym\r\n=bIRS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "9df47ad6d7aa4c54fe293ce5ef9637650ce9c460", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "12.14.0", "dependencies": {"debug": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.9.1_1579996474755_0.05050984072533149", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "follow-redirects", "version": "1.10.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.10.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "01f5263aee921c6a54fb91667f08f4155ce169eb", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.10.0.tgz", "fileCount": 6, "integrity": "sha512-4eyLK6s6lH32nOvLLwlIOnr9zrL8Sm+OvW4pVTJNoXeGzYIkHVf+pADQi+OJ0E67hiuSLezPVPyBcIZO50TmmQ==", "signatures": [{"sig": "MEQCIBtTOHarxpc4X/X0etT9glU7wL+zxb3WGMUGJyAgV0R7AiA8jLlbpAkI+HdwKoQqR2DAEk2unYO6U8mDO/LEf9+63g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLiJfCRA9TVsSAnZWagAAMCoQAKUGsAjNMrjM8v1P+Rrq\nCQkexRzO2E3kywImR9837V1NvO/qjrGs3B6fHcP4uvWi237W6pNNpi+9HC2J\nhwUO1AugHjdlGBmZ3xpjZLGgI0oy7Ws9UrHTAyHZcfY7B4YzuF8//P65W6LE\nW9JlqfIPIC8dYX2Y9fR+sqk3urlRSf0SyulYvOMeZYe2+/3PaiKG/JeGHY7T\nsfzi52wk7Y4sQ33VPWa03umgoRWlZ75KTQLcvLKtbR7upBAp1mbc7029PJPB\nPR05STVaOa8di3jvJP89pUs7V7I3yLoSdtKxPatoDPFsAZPWh7seQMJJjzfa\ndo8MdjA1DLNoR7sMUmYsu8dIVd1JHzGHH2HDnDrEZrsIKUQTQC7ovw4MyZ05\niNo/vRg7Pg7xYEqmYq/7YM4TERVWQzf/C2yo4Q2iQsER73FXW3zTglW1uA6g\n4eMgxPKf+6390oxrL8pBAONxM11WeRjJDt7Rrx23nfPCMkVky9vRPTUapsv0\nELVkb+NPTDs4wiEO0t6TQNFdClmSUwfVotSLD6Z79YagB1jrxLJ7zK0Ntz8m\nflaZCpG0SdLC/YFIX7pCc3K9AsZSeNkrt7ejuMTCxFAPXWGfPerSGbEuv/8X\nLBApiH7cvjha+qG1shEA+unjAr6/l5PHX8/bFFDYzI9X3HHxv5UJf/7uXm7K\nKiJk\r\n=0B6u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "670c3a7b38e0707a22d111fa766b4e82d277ec41", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "12.14.0", "dependencies": {"debug": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.10.0_1580081759334_0.796661405877777", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "follow-redirects", "version": "1.11.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.11.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "afa14f08ba12a52963140fe43212658897bc0ecb", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.11.0.tgz", "fileCount": 6, "integrity": "sha512-KZm0V+ll8PfBrKwMzdo5D13b1bur9Iq9Zd/RMmAoQQcl2PxxFml8cxXPaaPYVbV0RjNjq1CU7zIzAOqtUPudmA==", "signatures": [{"sig": "MEQCIBNKzsS+T5RqfJt3x1R02vaW0OFi3hW3Dfgd9wpdIOn3AiA9x3Y0XIIcteQ0lKfcG7e57W/7TLvM1moTgUItUSZeoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegH5gCRA9TVsSAnZWagAAnLMP/jR2M97OJB0tx9WA781R\n9n6DD/Ikjyi6coHArH6vMnqW0K4IK1Jsn29rR13M/uc/hZnn+Y/Y9d1lCBuy\n5hPm13ZCtbfNDaCqYMqSd6WGhzj/S/FDjjDHZKyoLlA5JxRpBszwNL6c78SZ\ntYJJMMRAOfZo4apotVG+fvpq1eZ/qw3Vrxsrihzc6v9Xd9RAdLsOh7345RKN\nAJEfm1XcQl54HaRv8jrrLtb1ESHjIeMWcLYS8tI0koCvS8eO1Jf2Hl0Bonrz\n8QDd9YfIS/5qRRXpubKvZ21yKFJ8rWRCJRgjvCSn1DM3fbp66XvjESh61lHN\nLxeIjsMdwv1vGumyJJtcej8DjGfwsBUAcMlrqMo1fpwGJq5u2Zw2TMAGeVK1\nu9myTSynjp/fpv2smZvE7taiYb2Ws01HRadc1CxiRcxHU+UV9SwLKAWaPGIq\n50uF0VfXswFecMY8Mblh5BudCpejf5KBpHKt6Fr3boQFj5jqXlZoGZkSwJea\npZhT14ELTkFqQ4I9EE1wyI8smtXfVVKrpZSoJWuKZDg8TLYf9EHZvu5zcBnT\nHoyhM1SUy6B0AowPDmWBJ92W2d10PcuWFoKFb4bEyzpfYSHaJGoeBiYvboDc\nFBlnbCiamAASn+RzwGu/u+NkxodQUOI1UavxfVCq4qoctRzMK3qU9aJTSVGM\nA+64\r\n=Qy8M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "0de31a8a0afbb859eca1b522fb3306121ba7c0a4", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "12.14.0", "dependencies": {"debug": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.11.0_1585479263559_0.7488129880046406", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "follow-redirects", "version": "1.12.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.12.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "ff0ccf85cf2c867c481957683b5f91b75b25e240", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.12.0.tgz", "fileCount": 7, "integrity": "sha512-JgawlbfBQKjbKegPn8vUsvJqplE7KHJuhGO4yPcb+ZOIYKSr+xobMVlfRBToZwZUUxy7lFiKBdFNloz9ui368Q==", "signatures": [{"sig": "MEUCIQCIe/jzrnjBN7HC6paVYUItBrfGFnO+IzlMkivwie0NjQIgAcUH+9zWhrH0HPQNp0xIjOazvYC2ew94aPqLeQIRWls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6S2GCRA9TVsSAnZWagAAfPgP/1NdDvODI9cnOFEXtzyJ\nRFtpkrpuWSpZWxnv7nyQBuPiXJRLSOqtGaP1CfOJjL2uP9Tbf2ogZLnMA5oA\nz2Ie6pCh/U1p/bvxNNXRBAoSGOgzB1/2IZUTuR0/LK6Gt321uF8i/yQKmyug\nzGUKQjz3GONyheTfyhLrcy5PCZwfSADLyAILsFMuMI1AB4ifm/6Zp0oshQw+\nUuE0SiftQvVG0BVxS2vYS66fAMKktxc6DPvUbn6EKAfaM7Ggj48OqHu77TAD\nJEem8+dYZ9Q2+eY4UyiaZwS3+grO49oymzZ5he6XyRh+6BLclTs8FPlfb+da\nTatRA+ksV9AMVdr3vAoCnxu6I5QxXYnlsNgan1TOzm+edV4zXYwtg6MP+luk\nSD2VQJXi3q/IReADnqWtBfZ9GLHMlAEX9RHubzNsY7bL2F0M6qUSMSIfdHmW\neTTB2YgD5VIVH6HoM4GiI6vwcnupBLrI0wTuBUjswTIIHC3DXsZ+/l5bdKiD\nICcV2MURZ6P7I5HNtdkJVCgydIOY+4vw7y7QNmdyMvq+ALM+tjhAG0N1RvL2\nXaQzfCkG3DaGLEr4h/nfN7yU/NLdi7EDUyYT+vEV0owvoGXrSBoyVEAfVbwe\nGRXRw4RomjGZK5qrnJkOXugSm7zcapK0bz3Do5u96Pp+gLYHCBVy1ka3+vq0\nf8iI\r\n=7ygN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "gitHead": "89fc462674c9a1b88262f1b7d0e828f4ced1b890", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "14.2.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependencies": {"debug": "^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.12.0_1592339846541_0.940755220987272", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "follow-redirects", "version": "1.12.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.12.1", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "de54a6205311b93d60398ebc01cf7015682312b6", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.12.1.tgz", "fileCount": 7, "integrity": "sha512-tmRv0AVuR7ZyouUHLeNSiO6pqulF7dYa3s19c6t+wz9LD69/uSzdMxJ2S91nTI9U3rt/IldxpzMOFejp6f0hjg==", "signatures": [{"sig": "MEUCIQD5PFESUlpyrUvCJiwX825x59rg74jdueauV99qnY8WvQIgUdTw2R0nLVd3SdKOUrAnZiY2r9UzpiWtaDouc65NS+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6+ybCRA9TVsSAnZWagAAsAcP/1TfXM557wagSC8+bQ1K\n9rHGMRSLAFf7AI3mc6sWLgNPGnh8KiLph98mCy5u7RZmeHWEwNiN5WYcXKPD\ns/q5GnacO6uRdxbC69YwUvY5VljBpdAGqLn2rg85kRF/MkoWWGIe0OXI+eQm\nHZKTlVrRoYA2nOfMjNkws6/3pRSX1FHdgn/uihauW7Z/yHEMR4tt9hYsHtKt\nl9wLbBF+nEq85zSLesBWGyZnAo4JCSaHhRRuRYVON+YYYgi0JXtMgIw01K3k\nw2/DRCzs1gWBgwp2Xb/lIkfY530MQg6ChaC0V+YwSyLyH9l/KMho1qFoUTeL\n1gFPV9qd0+N/ZsjyFOX11HEcoj2cQNZqAkrUKNX0xPLKcwoKlI5B6zCIRvIm\n/3ETO3sFi4otJIzY86DwNWFku+0r9FNftKEiXLlTskh2omdX5Rm/f8CyAChU\nnXThZKYAS7/+miM8aY15WN/yYuxK0nFfNNCTDwMb5sgqTYWJ6jCoIHchxbIK\n2VHK5XT/aezSLmLEKVvmE4LqaaEONqhqnfBngx6fcCDHhPlo5oDDwWQGId/u\n5MlJI3V/YA8zDFhqR8L445Y6w/bMRFIBI7E2xvUij9AZ5MCN/qzZyUcaiGhW\n81gPvWFXoCP6KmAKKAk/FvRBPibk+YLp1sQNaDZlerVnuHpt9jjcDyB0aPmJ\n40A/\r\n=nX59\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "aa6ce01c8145b1f968031c663d9aa87a692cfca7", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.12.1_1592519834666_0.2575521981101132", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "follow-redirects", "version": "1.13.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.13.0", "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "b42e8d93a2a7eea5ed88633676d6597bc8e384db", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.13.0.tgz", "fileCount": 7, "integrity": "sha512-aq6gF1BEKje4a9i9+5jimNFIpq4Q1WiwBToeRK5NvZBd/TRsmW8BsJfOEGkr76TbOyPVD3OVDN910EcUNtRYEA==", "signatures": [{"sig": "MEUCIA76MI74au/hYHg3UfVmKH5+uCGeZo2LUjUzT0CIR2NhAiEAsP0nbPwvmsslKbj2Ytd4lHOhTwcFu8yPdXZE1F0OjbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTJXCRA9TVsSAnZWagAAH+QP/RYg/ow5bVtgXIxGYoBT\n/mncbw9r192KpPWj/3C7vn3izLTf/GvWuU6DjETTYenRkpOF1QkjlRFyVDBJ\nEYrDlXmHL5AIdQRPQoRFXIRgdOx+bMjYFdeV4KVTqMq9Yh3vFFqho63oBnTP\nRo8iGZ8KGzF2ZVNXRwEnoGSANERPXKsoLKlvBMA0MAXtjQCgOpdzYlNv5h5j\nSXLwxZobsc/4piJWvRjdrITE45UYR5HnknUM90Pm73kVfg5kNdrPqLWUCJ1+\nO/Nf9lzFXrgO8j3fNLw+VB0i8gsAZZz4MOmn/AUUERx8c2mvU7g0R3c5NM4o\nbL+CSNutmz8Vtlu7wxXA7kCJKTPxHQmpfHlZR3B3pKH8nKqGw3lto6XdjWNn\nYZu+YzzJg7v5WVVp7BY54FnVgS+JHz5qog1YqG95IOeErAlA1JWupA7TZrdG\nmm6H5zVI3+qKMUwMUk8TDwpzrInbYbndEcOfgC5OIn7Fc7qxn48duwqcOCBE\nX3VmJJJWil6GP6QsL2DkMC8fEbFluXPeB8BNzxY1HfWWetTI708oYRBVqEQt\nhBqTgF/Px5EeA6BJ5cVg/swOnRYEQ8jL7Bx867bBsfzhE/hC8bmFu29AWXU5\nIM1yHtvey8jgaPVmbFzoWbW9cObfsqmdwIDNamtjhMGDrPeKEhb9CVDXaB4T\n0n4A\r\n=QqNW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "b2f2ea52edb4d5949751e2467d9e6d42ca24c8d0", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.13.0_1597059670652_0.3409890757494629", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "follow-redirects", "version": "1.13.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.13.1", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "5f69b813376cee4fd0474a3aba835df04ab763b7", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.13.1.tgz", "fileCount": 7, "integrity": "sha512-SSG5xmZh1mkPGyKzjZP8zLjltIfpW32Y5QpdNJyjcfGxK3qo3NDDkZOZSFiGn1A6SclQxY9GzEwAHQ3dmYRWpg==", "signatures": [{"sig": "MEUCIQCZhaCFOfjiPnrqbLdF48OLWXbhVs6TazAQ+CJnCXIIFwIgfMe7XSnsUBe3rhJNO0xQURHRW1yGv2ZTYWSiCytOt2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1juoCRA9TVsSAnZWagAAcUEP/iBwJd0839swYGNqnSCE\nIpOfHXql2coB7aFTB3YT5qrtC18+o2R4a/gEBY3mbf1ZkxNenSsf45GcMi4w\nUBipZT9qKmkgCYWtumTr+PhjnrUtaqQs+hQHZ0aFFLTugEnliabLFrOiFJoB\nxnRURCqlocyF3aWdoqBdNZRd1gMtM2U/AwdYGy4MHANBUA0tvI6giTpnHoCR\nVxEJch66QBJ4XTRTXtb/2I6iYJeev91fLNP6NjfpdCRBvmjsGD/5JcZo9227\nJqF2vofR7tfhxSQr54zS0+uXp6xVrKYkyFzwY6wUlK2TeEYoESKoiD2Jxv19\n5NTDv8/0/LXnZsOGc400PEplOMiDW+gX5by+vBTb7e30OQUWnZJbiUPn5KRB\nX7pUbspjadbahATQJ4CAenh4RBjd/6OC46UZw//cZjCclPKgN8Xt5XZMLtuF\ngYvU1soLWfVUe1Z3paBUM+Q3LlzKgZjUi/nhYvuEz7Mx8hgzd4aeiklrn2jq\nbPGYPYYwtl5tAeHk5U198ChkT7S3Z3pMZHaKN+GHrosEOTRa2AzazmRiEfB5\nzw3X5N8fpUYUegqyP1p+wR4pUYC5r8JiY0DfJpUFfOJNyR/xGEdCZo4L3Avd\np/Z+QEsaOV8OBlA5S5VTIlSK2ePP0CHWg6znHfZo/ugxzR+NxqbeMqg2iVPk\nhqzs\r\n=xRmT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "db771b18552b9f7663fd28be86597ec753b360ed", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.13.1_1607875495659_0.9702203750516365", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "follow-redirects", "version": "1.13.2", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.13.2", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "dd73c8effc12728ba5cf4259d760ea5fb83e3147", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.13.2.tgz", "fileCount": 7, "integrity": "sha512-6mPTgLxYm3r6Bkkg0vNM0HTjfGrOEtsfbhagQvbxDEsEkpNhw582upBaoRZylzen6krEmxXJgt9Ju6HiI4O7BA==", "signatures": [{"sig": "MEUCIQDEe59W/kYO3raeNeBzgDeoIp9LQEDbTByzJbVph5YKhAIgWcM7nwGoYahMqXypObkzuTIlASjKKXTD5eiAWyZqSvc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDyqvCRA9TVsSAnZWagAAFJcP/0IYTTcjiqyFHihdRRRd\n/ufjHDj8fy4KYGk/JSV/YA11u9f89Ck6wXrLW/ufjfURj5Z7kHW/Nq4ed8vC\n6jXqDifBW8PYwLgbY5Y6WfkPng8gQwTkrQN6ENyQDX/E9FBDbs/xSakpkpPJ\nKpxJtSaam7Awp+GCvsWSj2HiwWtWThtGrDq30ZV25By6KUqPoj02W5P6jr67\nbR7sAAfNFIyOFFzCMqHKy4YFXAM4EFSar+Wu6gBGL7y7HLonS1KZ7S1uqxW9\nhi4/yQMA/AejM9xT9t7YQ4+C9gg4ZrW5JT+f+M2NXDnucCUFiwRA3vBbEMAN\n3FcytjAWiouxag8nB+ioQiG++fq2B/qaVKTJDiM6CIrjFz3chpRijq0IOXUJ\nEPp5dbOEPWg0qqXwhcdJ14bkmVKDsHUDFuNg8KVOcVoYA0rEEqRf9kOMXs0r\nALUK5seRolPOJ9GJ46LtRROy0qZEOrypfrNevrl1hGIveB3ZMy7T4+JAcNhV\ndIUVunOannGJfTCRBxuQSEim4H2LPWRl120EdSAeAXaseINmLbJNIOLHMof+\nzg/GIZU38/COJISAjp+o08Ptvs6K4lDToSa5r055JHl6HZLryKONtoabw7oQ\n6knxcNYaXbT6SX8CJzkOXX8pq4s+FxtN9L3Lm/JO5Zii4PAib0UtwI3BHRcQ\nkTO2\r\n=tgk7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "41b0f8f1463a4ce813b73792cc183c4799a6e540", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "14.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.13.2_1611606702923_0.5674103930388479", "host": "s3://npm-registry-packages"}}, "1.13.3": {"name": "follow-redirects", "version": "1.13.3", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.13.3", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "e5598ad50174c1bc4e872301e82ac2cd97f90267", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.13.3.tgz", "fileCount": 7, "integrity": "sha512-DUgl6+HDzB0iEptNQEXLx/KhTmDb8tZUHSeLqpnjpknR70H0nC2t9N73BK6fN4hOvJ84pKlIQVQ4k5FFlBedKA==", "signatures": [{"sig": "MEUCIG91POOvs1tGKd2OSMTZCNi6kgFm7R3uj0mAdAaGgzQfAiEA/nmVV+vLSvU8dUC5XQqzXHYiyBffBHl5Nc7M3u1eeYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOloKCRA9TVsSAnZWagAA7I4QAIGv3HDAuRLIGj8UJ2Nb\nL9Lxa+3bu9iH8l7AUOrhBkZxMTTqnIqiaBuXw6G2v6y4hPAoMLhE4wb+7nzm\nHGzqUFVu2K06eXfmSAtr5VoYQ6c3jBHvgwVaQvwZeEeqjJaoUmID+SugL6dN\nNVtL29aInons4K44uxb8d4S2pRcSVTTE1UTXAZBQh4QrXX+jnLMh6imK0M5Z\nHe2pz/hgPTiJ5lDihvMh0gmEaLEOEZ+D0gI8+hQ9KuQ9jvIJkjbDYMNo2mWo\nZSvjwtDYp0qMQm/LEIQQai4nIQs6IaQDe/Ms0uRQrlxKasUnMAMPhjCetYLe\nEQgEJdHeU/jTjcJAqJ7oQKMPS1q+IAguE1OoXOj+WkEgqg/QubVh+cM9u3nB\ngoSi1xC+1QD5C8dIDV58WT4qPabRXBb7WY9eSVAU561EcXOcZRH0NVpHGtY+\nqiBzCIxsUqxU+bUhZ1Tm+ZN21JuwyBwFjJx5GJRFSbt+/bTEk4Zmw68PtOBr\n/DRUFyi8fC8/rd+yoYjTpllxPTyJqGmVRwc3h/HLt9HTHBLJSNvaXd2e+efJ\nuU9gC+fth+a1uTXzRz+Pwblj2ao1+eLzqlgEA24LqbIBGKQyC9MZWKG2YsMt\nbB1AJ7WyYIfGW4CGRGx2xzaamzNOdPTsDMCqBxGj90qgyRnF64hu/B0Zf8vF\nrHKw\r\n=pJms\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "2bdeedf70c5abbd631e5106848cb0732bcca794c", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.13.3_1614436874039_0.265570528092943", "host": "s3://npm-registry-packages"}}, "1.14.0": {"name": "follow-redirects", "version": "1.14.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.14.0", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "f5d260f95c5f8c105894491feee5dc8993b402fe", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.0.tgz", "fileCount": 7, "integrity": "sha512-0vRwd7RKQBTt+mgu87mtYeofLFZpTas2S9zY+jIeuLJMNvudIgF52nr19q40HOwH5RrhWIPuj9puybzSJiRrVg==", "signatures": [{"sig": "MEQCIFLcK6l9TwYKoToei+tnLIpnVmRV6K99xo/+3M/14wPkAiBwp7uITtuiaUbe+gw5ED/p6MgESTgS5Jw0Bh5WJpnjvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghZpBCRA9TVsSAnZWagAAo8kQAJXuGCKZjOh2wL6h9YcR\ntR/3QVpr006MYPCrDIEO/z3vp8pkJZycD3DWpuGFs3I6YErQliIfCvLX8Vef\nMG4HPR0FNyI9FCUjXPiuTHnx/CFZKlaqXpTTUPHp9rSZ5kog6pTS9YBZ+Q1y\nMUniI1U2+fdgEtktNQGj4B4a+UQOgW8JqR8cZ6lc+WnM6/3vrul+EF//xfMF\nMAMb7U9jDsJDBNPgHi9PcxCWzlTk43n0YyEfD2efpEejWVNTCymFE0G2jYZz\n98rtAppPjOGtGvzPGG+z+Q92xKU7TirxZCYoUBRBpktaMZO58woIUbwCom19\n0KpKK1RMtUlAs5ft7Z6o538cal4bOyNoHpMmQOUU8JPaK+7Cn3JaYMHvrH3Y\nBgJ+i8DV54zpxovO83/7uV/x/xjVEmve11YnexTP8Um9BBtKgsB30BQUiVlZ\nTnZZZPt3ppweMyuF0GgHYLh8P+NdD/yJD2fBsMkLwm7yB0A7zXqds2c9icmC\ndwuwRdxUbLR4k74miHM8G02o3FB4h+2hOFQm/JiWUNj/+/3cMpe7mPeru+15\nNb3scfk7hDlGw6vqFzJw4z4zvQainnRdmcAzhcN+uA8Djrvbn/PF486XoosP\nNO0qWFA/CgWMZ42ty8ubx+a5l7mVtvLRbtRn2+ZNb5OWyFUOuyuXFgJlArAB\nBEFZ\r\n=nSa9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "bbe5769c556f797070c5281efeed3bd2b3f8110b", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.14.0_1619368513258_0.14064315645904757", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "follow-redirects", "version": "1.14.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.14.1", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "d9114ded0a1cfdd334e164e6662ad02bfd91ff43", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.1.tgz", "fileCount": 7, "integrity": "sha512-HWqDgT7ZEkqRzBvc2s64vSZ/hfOceEol3ac/7tKwzuvEyWx3/4UegXh5oBOIotkGsObyk3xznnSRVADBgWSQVg==", "signatures": [{"sig": "MEYCIQDRsFbo66eEURm7pHOY2ypXCYqGl1QPlUUH3pPMCLcHwgIhAM3UZrA0LFpEfi+tT73SkTYd9DzBegBuxNmIDrb3BKaD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgl7XeCRA9TVsSAnZWagAASfAP/0szhwOKAKj3m9KyqlQU\nZRoK8sxVtIyHDw+E6z6pANy8ARz3D4h6DGY34aE2ogcztGG5+ZfOjeCIi9DL\n9ZrFBnBJEalM455b5bIIuD5wjKAUdkoGPkKCgOYhuuPTCPySvRK+RZc/14T1\n+1ovA6+wnod5zRO3tqAOnXQahP5WJGUhe8ac+7zK+UowgcFjpz/ETeeAvw10\n22yJyqoVPHrLFPqp700d0YwKHnoGqKkS4VaYqzZU2b4I8m5p6TR5fz8HcHr0\nSLy4pajJD2WkvUxPHBq9QqG/YVfKoAedSG2PNYI3C9JsDqRy5vjTd2EeocQk\nONilVAGIQJXCbs8622yyRjf364BEXoMtSBOwJKfgvenugC4y9Vl7iIDOMPFM\neifM0HLvh8bM6qWb8sEr+69jnvlXuzLIfAKqwkY/qR18MBfQztYD+nrUWMq7\nsbbm5xixSSGFoOENw8bEQP9B1V1NaGhU0uAmGcVk5iu7YLqLpbbgN+9uyvsd\nhvjUv2yEtFsueqJNO7i9+yeqoq/wl8FQ/46lZw62BP8/3HXP/fxqhBJ4epqi\n2OE02N7igtknriNQvcvX1HNN/dg/JzdwoRBerXKjhqn3xxpxBJKrE0DUHdWc\nVxLU/9eC93PocESmCk6AcMa3+Sxj6hVxSfF94k7W2gBZ28vmfLLxLNXdXhJu\nsgrB\r\n=lWxa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "889b0ebbcb1c90ef1941f0a72a8d1df922fb1d58", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.14.1_1620555229658_0.9441398766099356", "host": "s3://npm-registry-packages"}}, "1.14.2": {"name": "follow-redirects", "version": "1.14.2", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.14.2", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "cecb825047c00f5e66b142f90fed4f515dec789b", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.2.tgz", "fileCount": 7, "integrity": "sha512-yLR6WaE2lbF0x4K2qE2p9PEXKLDjUjnR/xmjS3wHAYxtlsI9MLLBJUZirAHKzUZDGLxje7w/cXR49WOUo4rbsA==", "signatures": [{"sig": "MEUCIDbWuqobkI6XUX/vR7gXkhCcvQYYrZZVwA4xz0v5wTEvAiEA4jOf4MTfdei5Z4RDLl1Dtca5iEiZtDQK8PlBs9U8BZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHPfaCRA9TVsSAnZWagAARFEQAIZXkqVz5Qof092aewlG\niMu/DyNB2SMfIxv25oz0Pi478sUT1UAX4gwVmLSEGtMx/kWmITeudGChlg7r\nKgHAxmGjKgFg6c+xnAFyWPqSpIu9xRTNOYmK3wtvXq3a2ujk9XuZoouaCWh2\nGzyPHTHhzN6NdzpRbXSEEFOYMNOgnWrfV4DOU/oLD3vAXGVaD7TJNNO3d74r\nSZ+ZYnLbGO6kJvl+0gJRIqpUSyd6pVUMhmRPtm8VPATiI5ucjTi/YuZWmduu\nLMB+5GMQcSegs74CciXN6VMzFwq7u1o36w7Z0S16MFVCNSb9MfA+L3VdRPln\n5vi6XZUytrMZOvPCNN7lE5RTHQuMOrLs+aBc1yJmINr6KjameVBHPBdZN1o0\nSe4iQQxdgL6Pp+NM0p7BbWcxSxyMTSFbF7eU+ThOFT2nVtqCJvy01wjJZW3p\nLC7lV1dqLU+zaxgYOR47lM+pMOi41VDMvhYxSWRhNtxkURydHyuLuVcbnQW8\nC2GCvcYyaEUsBDRJr3Gzol2DrxwXT72NL6o1e85waOHEjgH0NdIJXr/gmdbP\nBDKx5Ge4Nm//M+Kj9V3BBar0QRhXPgDEjc1qlqJ28Qmdy0UNH8jqOfM6arNo\nQex0G/0aiqHivKAVDiwod2rtoLn82qSBrjPeQun9uCmbsYCBpCl9hGMYN8Q9\nqOFv\r\n=Rp25\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "422890dae07034ae762b4a83f447833ac97b05dd", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.14.2_1629288410256_0.47113077699400807", "host": "s3://npm-registry-packages"}}, "1.14.3": {"name": "follow-redirects", "version": "1.14.3", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.14.3", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "6ada78118d8d24caee595595accdc0ac6abd022e", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.3.tgz", "fileCount": 7, "integrity": "sha512-3MkHxknWMUtb23apkgz/83fDoe+y+qr0TdgacGIA7bew+QLBo3vdgEN2xEsuXNivpFy4CyDhBBZnNZOtalmenw==", "signatures": [{"sig": "MEUCIQD+cTp8gLdg5hHVnGBU2iC36Lm6EU6DlEK6F2UNYRRKqAIgcCUv9SBULhu3eQ+HVpDzzr7kCSexsLJVs+qZJWSvnAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMNprCRA9TVsSAnZWagAAOkMP/3cT4tXWNsBGGkmf8OFG\n4s6RbNeG6beypGDFilqo3lkNoOs2Dp54uNWJQmUtR/QQu8FfdygXLGWsGiHQ\n4u8fKK0u31UYckGbKxm/RFUvP7u6cpARDWfTC0vMuVf06Tg3FG7ysO9xfzt8\nWwsdqW6aP0f6f7Ln67mVlSbwb9wgkvwzX36gjXtq3iO08WfKKPok7E8sOhzn\nZgX28vLml7TUE5g+S/eKsvCMyUJVYE7d4aVbMUlYqCmR7236r0S22DRrWqNV\nSS480dRS5f+ihd6tG6akZqyMUnAS9EgNMKoGUovercg0AVpcColFlPtEry6H\nedAiGV2aCrjpXNZLAM+3pFGCCiw/Ue9+LnLnRj44PnUUsPKZM3koGlwuKgFb\ns/NgtCsMY0tGGE6+270uBPVRIrUstG3v4RZCs2MH4Ju/gO4f2BsHmB+bDIu0\nEDlhL0AZ1P8pRiXA+KfLx09rQ7DE3TBnE6Hg6xJCx/utuAP60hQVnR9fsFHY\noJvQpUpHDctV9Ofhyvi3i9bS5ITshfq5yFyZ+vIIx/QgQzonoRUb1ss9DWps\nrj9deMHJyLtS0XibQoZJkJeEb4Dab2ch0Phn1xy8nAKtEG93zjKh9Zw0z+c+\nMGZYDJnWky828xIPC94MNkmdNrWG2OiM7Q8gX2cwoSAU8v9EMXjAhER5zuss\nfdp+\r\n=54z/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "f26c6c627106cb002fa39cfc327f5a541223a1cf", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.14.3_1630591595557_0.01770534876509111", "host": "s3://npm-registry-packages"}}, "1.14.4": {"name": "follow-redirects", "version": "1.14.4", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.14.4", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "838fdf48a8bbdd79e52ee51fb1c94e3ed98b9379", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.4.tgz", "fileCount": 7, "integrity": "sha512-zwGkiSXC1MUJG/qmeIFH2HBJx9u0V46QGUe3YR1fXG8bXQxq7fLj0RjLZQ5nubr9qNJUZrH+xUcwXEoXNpfS+g==", "signatures": [{"sig": "MEQCICl4Ywg5GVqglbrP/JLazhP/RjZ/6zSieQ0o1c2rRxtLAiAo5K1kO0VGc3ZHBJFLopVnGEasvNpMXkM2av+E8LNi5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQJPDCRA9TVsSAnZWagAAYoEP/RiK3yQGyNDnfSfs69+a\nd9LarFE+XhlUQHSyTxx9ivS949iN0hLKZvy7hiRZP4E7H0633vIQQ3mRpBlz\nQ9bE8hhUo7Dgpcj6kh7T6SmARanK/iqj8p7yuk//oP3XAExxNaXaScy8DCig\nltwmAEbbhEDJaME+/hbDVWev+euFVZ3GuJJ4YxnLRB1W4UWWxXsf9yDAf9mu\n6zmUCvCPzL33lDpbQAQAl3oU4Qc9ak+fpiSDb3HYQqbH/g+1+pjwO511rq1N\nJSTigaimMTfNQeJVGIur/mt7D8mViv8fHI96V4nM4RSsZZvnnA4FaTh74O9Z\n4pe379VT6YewwBOwODq1StGVy80wTQErq7KSVSzU/Zewk5kYDqhMpLGfnDue\nljbLtWYvc8HgsXXHofKjyQMtLSaF6nvR7gN8L1FJ/UIBvqnenTirMTfj2tiV\nM+jpb0QTEDJQYXbQ/x6qLwNpOhhj39i9afdYWihkPPWYxfoAL25yKakdk4x2\nYSAVahrBm5Phrl9b5fZ5gXANhvxunUP1mHo8niXSx5s9tuYF2swH5JVQ1A24\n4mXWcebnwTsFpcoaCrcOcP5ege0L5fiLsmPYcsAQK9UaszUTEKnzwOAyLnXR\nTqCeWMJmjLIro06wV9cEVIbmyv6RVWyf9pTkUF+xEZwkZSgrynNOYJZiQgbW\nDgHW\r\n=tT/r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "77e2a581e1d1811674b7b74745a9c20a5b939488", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.14.4_1631622083804_0.3454199755376193", "host": "s3://npm-registry-packages"}}, "1.14.5": {"name": "follow-redirects", "version": "1.14.5", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.14.5", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "f09a5848981d3c772b5392309778523f8d85c381", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.5.tgz", "fileCount": 7, "integrity": "sha512-wtphSXy7d4/OR+MvIFbCVBDzZ5520qV8XfPklSN5QtxuMUJZ+b0Wnst1e1lCDocfzuCkHqj8k0FpZqO+UIaKNA==", "signatures": [{"sig": "MEUCIQDwhRSiwXDiaNEhqgzO5+LgmSBD2+471wFB6yEuEXD08QIgeaDoJKlHCiNyAnOkWFPj5kAlyLff4lOGbjOggLqQ/r8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26446}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "d01ab7a5c5df3617c7a40a03de7af6427fdfac55", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.14.5_1635618572660_0.19128960737993905", "host": "s3://npm-registry-packages"}}, "1.14.6": {"name": "follow-redirects", "version": "1.14.6", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.14.6", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "8cfb281bbc035b3c067d6cd975b0f6ade6e855cd", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.6.tgz", "fileCount": 7, "integrity": "sha512-fhUl5EwSJbbl8AR+uYL2KQDxLkdSjZGR36xy46AO7cOMTrCMON6Sa28FmAnC2tRTDbd/Uuzz3aJBv7EBN7JH8A==", "signatures": [{"sig": "MEYCIQCDKJ+ZsTCFzrdBmDw9ksB7suwml1cUbq+NZgMYKi7mqAIhAPkB/fW5CO2v246qMwocnjB0Q9C8Ocs2AdSXpSjBIWdD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsPcTCRA9TVsSAnZWagAA2voP/RRjLodx14aoOCmQrQhH\n/ihLOLn2uiTBpKwSAVa8M1jE2o8M1JTE2oX3ZzYqGS8jSRIGnJuWubAzHzZ6\nYrJ6EBFifmp6B0wGODU5Jux1YpmVaPe8jDnAbf3kLMiGuAylnq5KtlH1dsJL\nPtfkpGBorb75wJV1AGDx7dLWOatzKVx2lrV4DRTqHm61qhgBWuHQM4Iu55MY\nj1CsDxKF3PtVVv/y/U7c5oyxZ7SCrck16+1Xqf4zE+kYtC5W29QA1VEraZUw\nmjq8JdJT6/DKA5PH3TjlAa7C23LJRa1J7Xg7aNPCUHV6NORlq++LmNtzGnFC\nqig+ZHZz+s9iBlpdZKQVeTXyD8kw8oKRw117l7Cgd3Vgxk0O4u7bN9sKmTzH\nDMUyx4xLcBraUct5QQMCo6A2SYJBwPJR0T3p63RHMUklDdGk92lQOL8xLxcp\nx8GjTZYhlj2hfBpj3jO6T00pRrFJSIqqqhLrtQ5q2QH1YRNQAYfuEAVSPYQN\nqKVLA2R7Se/qowgbqyyGDwuYNDjcXZ59SaBDX46sgWz+b+4RbHUnN0J3VnhF\n3CnlRnj3bxqUKHXCdYlMmh1C2WeoXOd2IffvUG+riwY9Fmyn8wQOoGSIizj9\nc+V+C2EgwnYDiU9a4/Chn84VM5ug8cW+/x6BsXrgsMSsBAgzSpoLdhoWRWo1\nVTo4\r\n=2dZo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "6f5029ae1a0fdab4dc25f6379a5ee303c2319070", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.14.6_1638987539324_0.21392657142337512", "host": "s3://npm-registry-packages"}}, "1.14.7": {"name": "follow-redirects", "version": "1.14.7", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.14.7", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "2004c02eb9436eee9a21446a6477debf17e81685", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.7.tgz", "fileCount": 7, "integrity": "sha512-+hbxoLbFMbRKDwohX8GkTataGqO6Jb7jGwpAlwgy2bIz25XtRm7KEzJM76R1WiNT5SwZkX4Y75SwBolkpmE7iQ==", "signatures": [{"sig": "MEUCIQCwg0RzrEgFwAI8iVUcb6Z3NhjrZTlXJ54zhhYpqoAZwwIgWTmx3Z3i7TgZot17KtvGMuJCQxxoutcgq6dwrSU6Iho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3GXCCRA9TVsSAnZWagAA30MP/1x31bX49Qe4PSWkU8Pe\nOO9jn8L5TTUy/zxgpESHbQNqu9iqjwB2fqV3eM+y4RIJQqo743xIqlLuTrEZ\nJ7jtZ2cYtfC7cikrkyk7FbjU5cNvJDz5DkAn1oAjtAGf0ONLIC6xTi7z8l7F\nSu84ijvggRIsKslUZq02CGV7IHA3nHPnLQlO8zpRDzxcRoFFvh+VNwjUxToK\nqZIGhiDxEzDkcP50O0ADaf6M+8Z7UjxLVT9snSj5HOfr5t5dSabZAYjnQGmR\nwJa3RFMe7y1AqTKWNmgGqG0Uc4JglzjQEGDjBgsJnsZwM8olpkMbOCpPMrzs\nHVUo6+2h8iQ7CWOh3CDVOkW7ZHrqb84h569T+fz+4EGVzvCW4U7/E2tqqSaf\necu5wfQSXo7XpTFctDhf8Cu+orxn3AV4PeCg6J3Tn9qwYs7DmF4aEr946Y1Z\n5D0TRA3GzbEsiq5hpJR16VOWb7emQRJSitU7guSlHWVhkPoOLpbaUhoMygWY\n/Q7wrFRHW6W2/HdvljH4cypfOCSODfD3OqCFTydAL7FlCzNhpdvOIXs40tIg\ndsUNVpTiUn8eXvHiWU4ksUk2km+7lGXnABKrTjS/pVWV4WtVP7Jno05AXEmJ\niCFiQp6NOI/2y+ZUUrp3NOxTBGv7bwlx0u+Af8AQkC0oOPZnuIylhw9X1LyB\nhjfc\r\n=hiDg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "2ede36d7c60d3acdcd324dcd99a9dbd52e4fb3a6", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.14.7_1641833922471_0.1774624366713229", "host": "s3://npm-registry-packages"}}, "1.14.8": {"name": "follow-redirects", "version": "1.14.8", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.14.8", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "016996fb9a11a100566398b1c6839337d7bfa8fc", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.8.tgz", "fileCount": 7, "integrity": "sha512-1x0S9UVJHsQprFcEC/qnNzBLcIxsjAV905f/UkQxbclCsoTWlacCNOpQa/anodLl2uaEKFhfWOvM2Qg77+15zA==", "signatures": [{"sig": "MEQCICm7jt/NC3sJ63hnib4jMMUJX6IodmlU4Di99maZQ84LAiAeLeSP8lnhx8xW89ujmWM2hIL9DV3ojiGEHViTeAC4dQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAk44CRA9TVsSAnZWagAAxpcP/3Ttt+U5wftsrHl9MtJX\nDYqhbF7UnKI0cRmJeWhyyMVoSvFwR8GwRcaZTdSDxJZuIKmBnHmzJX7ayHoU\ndTVvu54Z88c4GTBR2PieECkNmmlaELIYfdDWAgyh91gNG1CHpevxFZ6DkCP8\nzwr1LWs9o2PhWv1LePXOCKThSV8YyFKLpS63IZJyHVI/qR8XPJGHoE91/7Nh\n9B5siTKze9Msdy7+wFk9GDYqoYRcjNEFjwlWtZ8O9AcblDbQVnNq0BelJw77\n+vqXEXe6yNsXy7O8v/3P4ZbbSQxS5rV1yLdEA7v5w+wUDnQdGJhctM86+8gN\nzhinPw8791AeEVXCtVaKjR5G8/A0x2fOmx7fvPd3EY6Zuz/XQp447FPlsSYy\nJIiZlvP1X96R4TQH4J+Tb0lyMH0ETvXJ/lSMGLvHpbtlKZOAoi9aE/bNZhRw\nDHbusqIWvLmXdhgJDIRjUIOcDYJukInQcScWK6nOErEWSGCEAMRkpMQFgMTp\nFbdnoQPELxy6jGNjTqAFD/Swkp2m4/h3+DMwfMLGUmIcFqB6uX60S18wbrnb\n0m7LksyOmSw3gnHRp92W3yOR/uTD+HmDyV/J6tuAgjEd2DlxR99ql4m4Fk5d\nWQqo4dK9GN/498d7UX4HS4p6mS8Ub1svVAdkJhatoPnIQZtK5bvqfNRBzha+\no1b4\r\n=orvh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "3d81dc3237b4ffe8b722bb3d1c70a7866657166e", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.14.8_1644318263681_0.9673580401644828", "host": "s3://npm-registry-packages"}}, "1.14.9": {"name": "follow-redirects", "version": "1.14.9", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.14.9", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "dd4ea157de7bfaf9ea9b3fbd85aa16951f78d8d7", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.14.9.tgz", "fileCount": 7, "integrity": "sha512-MQDfihBQYMcyy5dhRDJUHcw7lb2Pv/TuE6xP1vyraLukNDHKbDxDNaOE3NbCAdKQApno+GPRyo1YAp89yCjK4w==", "signatures": [{"sig": "MEUCIHxQjp45G/ewq3vflbhwsIjbWYlAgMQtiQfsfMoRXZfVAiEA8MD6uDUhtDqXmUVpfFME1Q08CyRdDXrNFQmOLwvO1Gc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiD2aAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmos8RAAjKAru9f01w1FJ+RfbbMiSjHpEef9S5wHGg0Zm1ArKbXmwrpV\r\nrbRMEEpq2+KUrOzJT5fwz0mTsRGALL0YdjNoaz0raGt1nAyWzgdaioir0124\r\n1TFvsm5f3XedflXUi1vMRdbwR6kEus2VH2FMI7rpJ5/ofBJcexostFM6ANI+\r\nc9LAK1R8dptPNofi0bOTMe3jLxHrz6xwX9jKkMpz77F4uLilzwpNmAy0bv6m\r\n1+HF2Ix9Hh+1t/HhB24ThjXkbDfbtUEdEVjDLrfe7RFPiqKaizf1LEswQLGS\r\nEDI4JW7ecNCVShHy2VCgqOlXIS/VQvAoudMYlRife2nnU044qvqUvvgprMlo\r\nW6Db9J2vaK+oNbDupFK2zva/vKl/iyHg1V8zGdU2txdhJy8bArWf6g4xooEP\r\nA5ntptdon+0i9MeYWBdvqbWRUFxOdhsk8PGVqu6F6tiU+dqb1BxZGvgxPxgY\r\nHiM7MhBCdx7I7ksvcGQUvsQNwIz0QhuqUX72X2FYArauyJPx+0I0PAAP3+OL\r\nUrZHj06BZWI/+WWgfm5/5JrC79MSQzwZF2ud/Xf7MEPF54k1RyjXh9hfXN2X\r\nq3vEM/YORmp+/Wh5L78ZGa+EUDpSdybyXjYTwFJWSu0688wbDBDxC1OOYrAD\r\n+DHtNEdGxClGC67Sl+f/r5bayO3gn4Ylaio=\r\n=1Gmf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "13136e95bbe23cabbeaeb74bd0c933aa98dd9b96", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.14.9_1645176448431_0.25590024878711715", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "follow-redirects", "version": "1.15.0", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.15.0", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "06441868281c86d0dda4ad8bdaead2d02dca89d4", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.0.tgz", "fileCount": 7, "integrity": "sha512-aExlJShTV4qOUOL7yF1U5tvLCB0xQuudbf6toyYA0E/acBNw71mvjFTnLaRp50aQaYocMR0a/RMMBIHeZnGyjQ==", "signatures": [{"sig": "MEYCIQCekpxCiLcGcUQNMTj7C78QfVKXSNFv//kSQN2hq/tSVwIhAN0C9zSi+Hnvo0xNJyJ0JmyFkYTv4BJ+DojUcNXwmw0J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicZ7/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMNw/+MYS4c3GgbuLDVcvNHe951oMMjcz90vMhExGYJPaP5qiiMman\r\nAchseBuObgqZkjbk8lFVhBWETHD5JwhZjxXBpwErAZ05QlVVhiMGZSb/WcRX\r\nrIz1thF7bOM91hahexlncxZ9y0bYtefVJjJ69Gl7O0jAM/T4DvQALSq9oZVN\r\nuswwjX8JCavUIoNeSluj6eUbd6+BSQ/EsxKVGwdverxLxkznatX1kxEfjuOS\r\nHp+irWMZ35+S2zrM4D/dB/xnN+z/UacP9I50kA9Bv+Xvro5KO//uKHg7b7ka\r\nf+QPbVUC9fxWN5IBymkJ4Od7/CJLBUg8COa1CETNG9PI/9D2Uu0vb2KmAcqR\r\nkH99fYjJUYeMDvAmz89QVKpKB6pQSGzvNZXtDAtB4k9uy/68cScZvB35lboc\r\nupOfXKlnMzcaDbr0RNje/plOyE+2Zmq7eeO0kk0thc0aByKhrNh9xUSEjkZZ\r\nsNq1AeQSWGMb9X/YpQiKbV6Oeaxo8URlyCIYd9DgondssP/1nufCMqsN1fu2\r\ny/Z9wstLRSGB3PJG2B+gDh7FENz/zhdPGTzYshb1dbC0F8hET6u4KNWWLnT2\r\njgfmhjWlOmxqznsTuqCdineybfQhY/3tMhUVNequ8CEuXmv/7RXTxwmAZLsk\r\nqSK+9Axc9LH/FHiB3vNlZJ47qLkAzy3I1z4=\r\n=DwhV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "22e81fc37132941fb83939d1dc4c2282b5c69521", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.15.0_1651613439061_0.5798571521657423", "host": "s3://npm-registry-packages"}}, "1.15.1": {"name": "follow-redirects", "version": "1.15.1", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.15.1", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "0ca6a452306c9b276e4d3127483e29575e207ad5", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.1.tgz", "fileCount": 7, "integrity": "sha512-yLAMQs+k0b2m7cVxpS1VKJVvoz7SS9Td1zss3XRwXj+ZDH00RJgnuLx7E44wx02kQLrdM3aOOy+FpzS7+8OizA==", "signatures": [{"sig": "MEYCIQD8+D3ERHrgrBJZ6dMYXV97bsd+Swf1Idnepd0vozO5jAIhAOFsJSLhmlRtzs8ZpZ+1v8S8eXXqc4TlFAOQEjh7NU+V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikAwFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHUBAAhW5HKk+iXKh9Ag0VT6Ztnz5iPTQzpK6jzYcdoyeH9FUcO3al\r\n91AlwparL+5kaeEfWTtkERcv0PYjoF4hd1COiw22HJdTXFqh1mGt/3f/o2eM\r\nMt10Oe/14GnkwrruMPAHTV9yRJxE1YleCcp+81XPIe85EpDq1sf3+vhlop32\r\nTO5MRGTS5kLW8bVMaxeMHIW1Gv4fiJZR18A5TnZvbtRn0gUwzxtJWsh21spb\r\nd+VF+i2pu/Mj1lo/I+gd930IG9BLUkm1yC+bzQHqslF96KV4slK/a4V4l2RS\r\n3sr1HDXRa7DLAURmKpPK0Fdr24vcpe4mNnG9iX4G5TjNckKHubq1OQTTXyzL\r\n1aw0S3MlB9dF9az4RXCWfeceA0ePeeNm4eFTb+0UwFITsA/81Ecjb6CFQASs\r\nvOqD0eFQ1Xu2btIGyRrVWomX2a4J/X3ncQnWUh6KVL7izRHmqvwK8uCI5BwA\r\npquHZnaXDMVJPs8vpfTkqncGEumXvsTtpgSq+uJPvR340MDPeT19Ht5QSOAU\r\n0DhMfBZA4NX12n2OEmhK5oWgiUzaUsYDqVtcOcyUsNOAAGOYdCUxgVw+ddX5\r\no47wWwtfRARNwtuxB2ZNC8HVnIBXxpHU3ulspqR8NM9pKPWZ0lz7PQLkKsK2\r\n5FVRiu17P+85/uoHKNDw1UC9QcVe+mmdltQ=\r\n=FLmP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_from": ".", "_shasum": "0ca6a452306c9b276e4d3127483e29575e207ad5", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "62a551cbf7d4d3917c532d0b87ca77ef983f44c1", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "6.17.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.15.1_1653607429692_0.8252722442460141", "host": "s3://npm-registry-packages"}}, "1.15.2": {"name": "follow-redirects", "version": "1.15.2", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.15.2", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "b460864144ba63f2681096f274c4e57026da2c13", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.2.tgz", "fileCount": 7, "integrity": "sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==", "signatures": [{"sig": "MEQCIH4XAQwrMAPYIbhSXjaWTQfQPE6t+qFnTAZvgGB+EfiYAiBPrX3hxSBGIkcJGoWxvmNoID8sCT01g0Hsxqzd84dT7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIJvzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozIA/+Nzxm5iFxjz2GvCp4jWfJdfdqbtwLZo8aRyNWayKWhw6OX8Tx\r\n7J0OmhZfwdK7ObAD13Ujvx4GVYJ0LEN3QYTjI0z3U5ZfKzhewAaZQ6Mr/jqG\r\ngSQjlnzvWubDkvopKs7jWKvkVhajRgFvuMX3fbSJ8xaOhyjtqB+BOaCSG/R0\r\nr9lM6UzqtZAqMQ5BMdj/POt0/GejE6kS9UoPclN4qCVNm5hi+a3Hy0z5qy6E\r\nvfuguKx1lWET7YVG28yzmGO/8qSspxQ+Eeeq9dLBl8WUbXqf/L8ErM3o4ku0\r\nyGpGPr7dM61AifiBuuz89MrKsLRxcuzo2CEnowVqyRzjWjbNi3JhYEZagVAa\r\nlw3ycCvB2WRw8Ftqw7mqvcRY6XpAKtO2WcyuQfWX0EtFYC9+79Xfcna7Y/vc\r\nfoxJwHjaJ4Fdh9LNwzyiPPdAyhON3nLoUo+v9BtQmkhJR8H5jE2f7Ujlo925\r\nL8JvitdJYzSSP/+1pe23Tsq+72vKxa+/giwJnLrSVdZYvlrws5ydghp+GtXC\r\n6sN3kff06dKLCRFTy/Cq5L6HcgPOVhPkSOu2qxye3c4tjpR7c9sv3ixocIXz\r\nGEj5/3oFM1/HyhWPYe7L24jMABfOGMonJ+Ef0E693oO1pt3B11eHx5kpDDVZ\r\namgsj6hdNXc6Q+o87DcwPRYh++3k1BGIg1k=\r\n=Ht8q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "96552371eeec58567850d0288ba793274d1ca50b", "scripts": {"lint": "eslint *.js test", "test": "npm run lint && npm run mocha", "mocha": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "18.8.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.15.2_1663081459173_0.11989730084869654", "host": "s3://npm-registry-packages"}}, "1.15.3": {"name": "follow-redirects", "version": "1.15.3", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.15.3", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "fe2f3ef2690afce7e82ed0b44db08165b207123a", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.3.tgz", "fileCount": 7, "integrity": "sha512-1VzOtuEM8pC9SFU1E+8KfTjZyMztRsgEfwQl44z8A25uy13jSzTj6dyK2Df52iV0vgHCfBwLhDWevLn95w5v6Q==", "signatures": [{"sig": "MEYCIQC7xeXMrdBwLd8GuJHU1cCXApk4ogerPWuSw9yKPv2/XAIhAJbSnYkvR9moB3Mt7p/Vcko+mit8VEKutJoIgXx3Tpnv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28600}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "192dbe7ce671ecad813c074bffe3b3f5d3680fee", "scripts": {"lint": "eslint *.js test", "test": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "20.7.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.15.3_1695149507836_0.9807354401920754", "host": "s3://npm-registry-packages"}}, "1.15.4": {"name": "follow-redirects", "version": "1.15.4", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.15.4", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "cdc7d308bf6493126b17ea2191ea0ccf3e535adf", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.4.tgz", "fileCount": 7, "integrity": "sha512-Cr4D/5wlrb0z9dgERpUL3LrmPKVDsETIJhaCMeDfuFYcqa5bldGV6wBsAN6X/vxlXQtFBMrXdXxdL8CbDTGniw==", "signatures": [{"sig": "MEUCIHwQLswB0gd6USqvSbhJ52YhIzr0cx9Glpz4LAjUs3h2AiEAtY80IzJUF7RFohl5w6y7+Z01ElWXWWC1wAZx47QE1Kw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29352}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "65858205e59f1e23c9bf173348a7a7cbb8ac47f5", "scripts": {"lint": "eslint *.js test", "test": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "18.8.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.15.4_1703957284086_0.025894509091784146", "host": "s3://npm-registry-packages"}}, "1.15.5": {"name": "follow-redirects", "version": "1.15.5", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.15.5", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "54d4d6d062c0fa7d9d17feb008461550e3ba8020", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.5.tgz", "fileCount": 7, "integrity": "sha512-vSFWUON1B+yAw1VN4xMfxgn5fTUiaOzAJCKBwIIgT/+7CuGy9+r+5gITvP62j3RmaD5Ph65UaERdOSRGUzZtgw==", "signatures": [{"sig": "MEYCIQCjOhBgtA79t9mB36x4YR7ncxlc7CDm8Nh1cojfmn7BBAIhAJ5gQLSj/bwIx+4TnBQb2zE59vf3BUIWriSZ9P0yjZIA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29362}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "b1677ce00110ee50dc5da576751d39b281fc4944", "scripts": {"lint": "eslint *.js test", "test": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "18.8.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.15.5_1705048872586_0.35991654942305296", "host": "s3://npm-registry-packages"}}, "1.15.6": {"name": "follow-redirects", "version": "1.15.6", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.15.6", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "7f815c0cda4249c74ff09e95ef97c23b5fd0399b", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.6.tgz", "fileCount": 7, "integrity": "sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==", "signatures": [{"sig": "MEQCIEXl4L+RQsXN6PW8uitrbFGeacF8kztl5Kh/AUpRSXC6AiAkjYflH49mf1aND4ZQuzNKm1RGNz1c2sjh82Qf13laMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29373}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "35a517c5861d79dc8bff7db8626013d20b711b06", "scripts": {"lint": "eslint *.js test", "test": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "18.8.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.15.6_1710434278149_0.9378617732465675", "host": "s3://npm-registry-packages"}}, "1.15.7": {"name": "follow-redirects", "version": "1.15.7", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.15.7", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "87248a048fc4eeebfb2389d6aef79028da179690", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.7.tgz", "fileCount": 8, "integrity": "sha512-aAW9al/NMw4COcZnwgUaG8kAjBUXq/El+1R11e9RDHAIlxa1fb1b5SP0K6BbMoNgWdmJ/kXAwoTlVDlUN3OTDw==", "signatures": [{"sig": "MEMCIFD8Do03NJTEX+LCWxNbTKq4xGAbeDMl0tJx+pM4AKE7Ah9Dh8qCuEqD6K7Q96DeGOwhtBcpaSNUU9DfDl7ho4Ie", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30246}, "main": "index.js", "browser": "nope.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "760757f7b75cce429604492d91649cbbd473c8d4", "scripts": {"lint": "eslint *.js test", "test": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "deprecated": "Browser detection issues fixed in v1.15.9", "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "18.8.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.15.7_1725391700578_0.21589694048494112", "host": "s3://npm-registry-packages"}}, "1.15.8": {"name": "follow-redirects", "version": "1.15.8", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.15.8", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "ae67b97ae32e0a7b36066a5448938374ec18d13d", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.8.tgz", "fileCount": 8, "integrity": "sha512-xgrmBhBToVKay1q2Tao5LI26B83UhrB/vM1avwVSDzt8rx3rO6AizBAaF46EgksTVr+rFTQaqZZ9MVBfUe4nig==", "signatures": [{"sig": "MEUCIH5rT9snCnlsQ4UV61znYrcXlVnj9WypJtK9IgobluaoAiEA2zdoH92a7To9AjJiYYE/1UJtbOq+yDUfg24jFX7oV0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30023}, "main": "index.js", "browser": "nope.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "62558f0cd106195f4c17ece3ad255eb93487d37f", "scripts": {"lint": "eslint *.js test", "test": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "22.6.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.15.8_1725399862453_0.26606691502126534", "host": "s3://npm-registry-packages"}, "deprecated": "Browser detection issues fixed in v1.15.9"}, "1.15.9": {"name": "follow-redirects", "version": "1.15.9", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "follow-redirects@1.15.9", "maintainers": [{"name": "rubenverborgh", "email": "<EMAIL>"}, {"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "dist": {"shasum": "a604fa10e443bf98ca94228d9eebcc2e8a2c8ee1", "tarball": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "fileCount": 7, "integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==", "signatures": [{"sig": "MEUCIQD/Er/mWZNnUc1I6Y5vpycybunw0uETCSF7C6++MZcatQIgCqjsjDkdTUprVFrfkI6Mrxg3gI1ymc1T8bLU8F4f6r0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29918}, "main": "index.js", "engines": {"node": ">=4.0"}, "funding": [{"url": "https://github.com/sponsors/Ruben<PERSON>", "type": "individual"}], "gitHead": "e4e55c77b2d849280d105943f49f42e0c735d05d", "scripts": {"lint": "eslint *.js test", "test": "nyc mocha"}, "_npmUser": {"name": "rubenverborgh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "HTTP and HTTPS modules that follow redirects.", "directories": {}, "_nodeVersion": "22.6.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "lolex": "^3.1.0", "mocha": "^6.0.2", "eslint": "^5.16.0", "express": "^4.16.4", "concat-stream": "^2.0.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/follow-redirects_1.15.9_1725612993908_0.9585003512120172", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-11-15T22:23:37.885Z", "modified": "2024-09-06T08:57:40.841Z", "0.0.1": "2012-11-15T22:23:44.078Z", "0.0.2": "2012-11-21T00:14:20.978Z", "0.0.3": "2012-11-29T08:19:21.319Z", "0.0.4": "2015-07-01T14:43:59.128Z", "0.0.5": "2015-07-01T15:27:15.970Z", "0.0.6": "2015-07-04T13:26:09.166Z", "0.0.7": "2015-09-10T01:41:18.073Z", "0.1.0": "2016-04-10T19:31:39.368Z", "0.2.0": "2016-06-06T22:00:05.075Z", "0.3.0": "2016-10-20T15:49:08.939Z", "1.0.0": "2016-10-23T16:09:54.517Z", "1.1.0": "2016-11-06T16:44:38.542Z", "1.2.0": "2016-12-07T21:50:56.326Z", "1.2.1": "2016-12-18T12:52:48.546Z", "1.2.2": "2017-03-09T21:53:54.031Z", "1.2.3": "2017-03-10T16:51:22.410Z", "1.2.4": "2017-06-21T19:39:27.920Z", "1.2.5": "2017-10-05T17:04:14.010Z", "1.2.6": "2017-11-22T10:35:02.216Z", "1.3.0": "2018-01-05T18:57:24.272Z", "1.4.0": "2018-01-21T21:55:51.028Z", "1.4.1": "2018-01-24T00:42:40.382Z", "1.5.0": "2018-05-19T16:17:27.946Z", "1.5.1": "2018-07-05T20:44:53.010Z", "1.5.2": "2018-08-01T18:34:57.855Z", "1.5.3": "2018-08-13T11:15:32.833Z", "1.5.4": "2018-08-13T13:18:06.652Z", "1.5.5": "2018-08-13T18:02:14.290Z", "1.5.6": "2018-08-21T01:44:52.160Z", "1.5.7": "2018-08-22T18:02:41.148Z", "1.5.8": "2018-09-11T19:25:50.658Z", "1.5.9": "2018-10-09T16:01:45.434Z", "1.5.10": "2018-11-19T21:25:49.948Z", "1.6.0": "2018-12-25T22:27:34.053Z", "1.6.1": "2019-01-03T10:24:43.194Z", "1.7.0": "2019-02-13T21:40:42.432Z", "1.8.0": "2019-08-27T11:10:39.307Z", "1.8.1": "2019-08-27T17:37:53.351Z", "1.9.0": "2019-09-06T13:12:12.301Z", "1.9.1": "2020-01-25T23:54:34.866Z", "1.10.0": "2020-01-26T23:35:59.450Z", "1.11.0": "2020-03-29T10:54:23.756Z", "1.12.0": "2020-06-16T20:37:26.684Z", "1.12.1": "2020-06-18T22:37:14.802Z", "1.13.0": "2020-08-10T11:41:11.024Z", "1.13.1": "2020-12-13T16:04:55.785Z", "1.13.2": "2021-01-25T20:31:43.071Z", "1.13.3": "2021-02-27T14:41:14.164Z", "1.14.0": "2021-04-25T16:35:13.455Z", "1.14.1": "2021-05-09T10:13:49.823Z", "1.14.2": "2021-08-18T12:06:50.453Z", "1.14.3": "2021-09-02T14:06:35.708Z", "1.14.4": "2021-09-14T12:21:23.946Z", "1.14.5": "2021-10-30T18:29:32.810Z", "1.14.6": "2021-12-08T18:18:59.483Z", "1.14.7": "2022-01-10T16:58:42.832Z", "1.14.8": "2022-02-08T11:04:24.019Z", "1.14.9": "2022-02-18T09:27:28.602Z", "1.15.0": "2022-05-03T21:30:39.266Z", "1.15.1": "2022-05-26T23:23:49.868Z", "1.15.2": "2022-09-13T15:04:19.298Z", "1.15.3": "2023-09-19T18:51:48.020Z", "1.15.4": "2023-12-30T17:28:04.328Z", "1.15.5": "2024-01-12T08:41:12.752Z", "1.15.6": "2024-03-14T16:37:58.315Z", "1.15.7": "2024-09-03T19:28:20.798Z", "1.15.8": "2024-09-03T21:44:22.642Z", "1.15.9": "2024-09-06T08:56:34.070Z"}, "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "author": {"url": "https://ruben.verborgh.org/", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/follow-redirects/follow-redirects", "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "repository": {"url": "git+ssh://**************/follow-redirects/follow-redirects.git", "type": "git"}, "description": "HTTP and HTTPS modules that follow redirects.", "contributors": [{"url": "http://www.syskall.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "o<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubenverborgh", "email": "<EMAIL>"}], "readme": "## Follow Redirects\n\nDrop-in replacement for <PERSON><PERSON>'s `http` and `https` modules that automatically follows redirects.\n\n[![npm version](https://img.shields.io/npm/v/follow-redirects.svg)](https://www.npmjs.com/package/follow-redirects)\n[![Build Status](https://github.com/follow-redirects/follow-redirects/workflows/CI/badge.svg)](https://github.com/follow-redirects/follow-redirects/actions)\n[![Coverage Status](https://coveralls.io/repos/follow-redirects/follow-redirects/badge.svg?branch=master)](https://coveralls.io/r/follow-redirects/follow-redirects?branch=master)\n[![npm downloads](https://img.shields.io/npm/dm/follow-redirects.svg)](https://www.npmjs.com/package/follow-redirects)\n[![Sponsor on GitHub](https://img.shields.io/static/v1?label=Sponsor&message=%F0%9F%92%96&logo=GitHub)](https://github.com/sponsors/RubenVerborgh)\n\n`follow-redirects` provides [request](https://nodejs.org/api/http.html#http_http_request_options_callback) and [get](https://nodejs.org/api/http.html#http_http_get_options_callback)\n methods that behave identically to those found on the native [http](https://nodejs.org/api/http.html#http_http_request_options_callback) and [https](https://nodejs.org/api/https.html#https_https_request_options_callback)\n modules, with the exception that they will seamlessly follow redirects.\n\n```javascript\nconst { http, https } = require('follow-redirects');\n\nhttp.get('http://bit.ly/900913', response => {\n  response.on('data', chunk => {\n    console.log(chunk);\n  });\n}).on('error', err => {\n  console.error(err);\n});\n```\n\nYou can inspect the final redirected URL through the `responseUrl` property on the `response`.\nIf no redirection happened, `responseUrl` is the original request URL.\n\n```javascript\nconst request = https.request({\n  host: 'bitly.com',\n  path: '/UHfDGO',\n}, response => {\n  console.log(response.responseUrl);\n  // 'http://duckduckgo.com/robots.txt'\n});\nrequest.end();\n```\n\n## Options\n### Global options\nGlobal options are set directly on the `follow-redirects` module:\n\n```javascript\nconst followRedirects = require('follow-redirects');\nfollowRedirects.maxRedirects = 10;\nfollowRedirects.maxBodyLength = 20 * 1024 * 1024; // 20 MB\n```\n\nThe following global options are supported:\n\n- `maxRedirects` (default: `21`) – sets the maximum number of allowed redirects; if exceeded, an error will be emitted.\n\n- `maxBodyLength` (default: 10MB) – sets the maximum size of the request body; if exceeded, an error will be emitted.\n\n### Per-request options\nPer-request options are set by passing an `options` object:\n\n```javascript\nconst url = require('url');\nconst { http, https } = require('follow-redirects');\n\nconst options = url.parse('http://bit.ly/900913');\noptions.maxRedirects = 10;\noptions.beforeRedirect = (options, response, request) => {\n  // Use this to adjust the request options upon redirecting,\n  // to inspect the latest response headers,\n  // or to cancel the request by throwing an error\n\n  // response.headers = the redirect response headers\n  // response.statusCode = the redirect response code (eg. 301, 307, etc.)\n\n  // request.url = the requested URL that resulted in a redirect\n  // request.headers = the headers in the request that resulted in a redirect\n  // request.method = the method of the request that resulted in a redirect\n  if (options.hostname === \"example.com\") {\n    options.auth = \"user:password\";\n  }\n};\nhttp.request(options);\n```\n\nIn addition to the [standard HTTP](https://nodejs.org/api/http.html#http_http_request_options_callback) and [HTTPS options](https://nodejs.org/api/https.html#https_https_request_options_callback),\nthe following per-request options are supported:\n- `followRedirects` (default: `true`) – whether redirects should be followed.\n\n- `maxRedirects` (default: `21`) – sets the maximum number of allowed redirects; if exceeded, an error will be emitted.\n\n- `maxBodyLength` (default: 10MB) – sets the maximum size of the request body; if exceeded, an error will be emitted.\n\n- `beforeRedirect` (default: `undefined`) – optionally change the request `options` on redirects, or abort the request by throwing an error.\n\n- `agents` (default: `undefined`) – sets the `agent` option per protocol, since HTTP and HTTPS use different agents. Example value: `{ http: new http.Agent(), https: new https.Agent() }`\n\n- `trackRedirects` (default: `false`) – whether to store the redirected response details into the `redirects` array on the response object.\n\n\n### Advanced usage\nBy default, `follow-redirects` will use the Node.js default implementations\nof [`http`](https://nodejs.org/api/http.html)\nand [`https`](https://nodejs.org/api/https.html).\nTo enable features such as caching and/or intermediate request tracking,\nyou might instead want to wrap `follow-redirects` around custom protocol implementations:\n\n```javascript\nconst { http, https } = require('follow-redirects').wrap({\n  http: require('your-custom-http'),\n  https: require('your-custom-https'),\n});\n```\n\nSuch custom protocols only need an implementation of the `request` method.\n\n## Browser Usage\n\nDue to the way the browser works,\nthe `http` and `https` browser equivalents perform redirects by default.\n\nBy requiring `follow-redirects` this way:\n```javascript\nconst http = require('follow-redirects/http');\nconst https = require('follow-redirects/https');\n```\nyou can easily tell webpack and friends to replace\n`follow-redirect` by the built-in versions:\n\n```json\n{\n  \"follow-redirects/http\"  : \"http\",\n  \"follow-redirects/https\" : \"https\"\n}\n```\n\n## Contributing\n\nPull Requests are always welcome. Please [file an issue](https://github.com/follow-redirects/follow-redirects/issues)\n detailing your proposal before you invest your valuable time. Additional features and bug fixes should be accompanied\n by tests. You can run the test suite locally with a simple `npm test` command.\n\n## Debug Logging\n\n`follow-redirects` uses the excellent [debug](https://www.npmjs.com/package/debug) for logging. To turn on logging\n set the environment variable `DEBUG=follow-redirects` for debug output from just this module. When running the test\n suite it is sometimes advantageous to set `DEBUG=*` to see output from the express server as well.\n\n## Authors\n\n- [Ruben Verborgh](https://ruben.verborgh.org/)\n- [Olivier Lalonde](mailto:<EMAIL>)\n- [James Talmage](mailto:<EMAIL>)\n\n## License\n\n[MIT License](https://github.com/follow-redirects/follow-redirects/blob/master/LICENSE)\n", "readmeFilename": "README.md", "users": {"eklem": true, "g120hbq": true, "poginni": true, "alejcerro": true, "edwardxyt": true, "lababygirl": true, "shentengtu": true, "tarkeshwar": true, "antongorodezkiy": true}}