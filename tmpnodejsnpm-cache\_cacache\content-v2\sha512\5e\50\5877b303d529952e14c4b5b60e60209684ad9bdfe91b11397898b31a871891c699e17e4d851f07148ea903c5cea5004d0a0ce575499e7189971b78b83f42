{"_id": "color-name", "_rev": "18-a50b9768cf5cc733c22067d086ffefe1", "name": "color-name", "description": "A list of color names and its values", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.1": {"name": "color-name", "version": "0.0.1", "description": "A list of color names and it’s values", "main": "index.json", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:dfcreative/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "unlicensed", "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "homepage": "https://github.com/dfcreative/color-name", "_id": "color-name@0.0.1", "dist": {"shasum": "c824e087906bc0683062ab47272df7be6ec94659", "tarball": "https://registry.npmjs.org/color-name/-/color-name-0.0.1.tgz", "integrity": "sha512-h+3SQlhFZPTPTyLnkh24T8iwDqQ+dTeGkxxsf/bKypCiEGsw185T35HfiKVunj24vphahSdLWbTVvG9PFHVaxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuC02YIefm9IH7n1Z4xUhJuf5bPQ6s7Lyh9LV2risHoAIgfuyKQVuHuyiWk1psrQJcJBs8jFqbiWkMnktRNfq8J00="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "dfcreative", "email": "<EMAIL>"}, "maintainers": [{"name": "dfcreative", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "color-name", "version": "0.0.2", "description": "A list of color names and it’s values", "main": "index.json", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:dfcreative/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "unlicensed", "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "homepage": "https://github.com/dfcreative/color-name", "_id": "color-name@0.0.2", "dist": {"shasum": "767c4389d07d4bec162f5fb0b87a8c9b81a3c0af", "tarball": "https://registry.npmjs.org/color-name/-/color-name-0.0.2.tgz", "integrity": "sha512-ubJSEdDiEKySKubTMBG03Ulo4zDLculwX6xgkYYA5H6HEiMniEWqty0LUwyrKxz8v6s1+r0202HSsJsU7D+Gng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBsIX70G5+ywhz+3t744W+tqfTxhvSffFLy5MEd1XO4QIhAN9ZmOJCZMdSok032PD5xM8vIYWPC53K6XLHMGbFCAVy"}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "dfcreative", "email": "<EMAIL>"}, "maintainers": [{"name": "dfcreative", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "color-name", "version": "1.0.0", "description": "A list of color names and it’s values", "main": "index.json", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:dfcreative/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "unlicensed", "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "homepage": "https://github.com/dfcreative/color-name", "_id": "color-name@1.0.0", "dist": {"shasum": "ce3579a4ef43b672bee4f37e8876332b5a36e6b5", "tarball": "https://registry.npmjs.org/color-name/-/color-name-1.0.0.tgz", "integrity": "sha512-EGBM2T7+LfAKfv+ovjidZ+KPgItQSdjMdzNsvecNLLEjwKOG2XAfJKhJo5hUuX1+i/ofedvabKZwt+S46u3v7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGAC0Z6T+uOZw8eSou0CEOt84uKcsyXSSi/W+OxRGOCdAiA9rBcIS3hTTaMEznF7lUMVBR+wVv3u1x4qD1+jCnXzOw=="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "dfcreative", "email": "<EMAIL>"}, "maintainers": [{"name": "dfcreative", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "color-name", "version": "1.0.1", "description": "A list of color names and it’s values", "main": "index.json", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:dfcreative/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "Unlicense", "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "homepage": "https://github.com/dfcreative/color-name", "gitHead": "6d51c7376a86c96c9a645f7226e2cba1ffcf8c05", "_id": "color-name@1.0.1", "_shasum": "6b34b2b29b7716013972b0b9d5bedcfbb6718df8", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "dfcreative", "email": "<EMAIL>"}, "maintainers": [{"name": "dfcreative", "email": "<EMAIL>"}], "dist": {"shasum": "6b34b2b29b7716013972b0b9d5bedcfbb6718df8", "tarball": "https://registry.npmjs.org/color-name/-/color-name-1.0.1.tgz", "integrity": "sha512-ipj55CYipJcmYpiDwpDRZ91iMJOnsTW9wICiE1efyPWadY2FapkgfTvB8IJLxf5muHHrpKVVxhEnv03UIshOJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGjavfffqx2iZoyd8GzL2R+Bi2KsXIAK7vvd4BCZ2Sn4AiBkOV+UYLoJDofDkuITBSMq61ZzRQ85JG9NsNYezdazPQ=="}]}, "directories": {}}, "1.1.0": {"name": "color-name", "version": "1.1.0", "description": "A list of color names and it’s values", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/dfcreative/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "DY", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "homepage": "https://github.com/dfcreative/color-name", "gitHead": "0ccc911ab063f15b8d89f137bacef3955b99e838", "_id": "color-name@1.1.0", "_shasum": "a5481420307855e5bdbdbc26bc4b39d864828fcd", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "dfcreative", "email": "<EMAIL>"}, "maintainers": [{"name": "dfcreative", "email": "<EMAIL>"}], "dist": {"shasum": "a5481420307855e5bdbdbc26bc4b39d864828fcd", "tarball": "https://registry.npmjs.org/color-name/-/color-name-1.1.0.tgz", "integrity": "sha512-+cCpbUmNJ/7SCcD6RKVuIwXvd3oeSYTLDR8lJlk7ofhchGHp7EMit4q5AjCHLTc1Cyin4TIrWmhaukp0EORPkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAdK4Wc5m4JSRNIHyphLoIzKIbBhcQxYk7VuNoPhlpmnAiEAkDsR+EBvNbGN/T2iNtyaXpS4Jmc0BpAuxtqtW3AsM6g="}]}, "directories": {}}, "1.1.1": {"name": "color-name", "version": "1.1.1", "description": "A list of color names and it’s values", "main": "index.js", "scripts": {"test": "node test.js"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+ssh://**************/dfcreative/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "DY", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "homepage": "https://github.com/dfcreative/color-name", "gitHead": "b47de1bf75da523d6d6061a41f7f0c9bb7c213bf", "_id": "color-name@1.1.1", "_shasum": "4b1415304cf50028ea81643643bd82ea05803689", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "dfcreative", "email": "<EMAIL>"}, "maintainers": [{"name": "dfcreative", "email": "<EMAIL>"}], "dist": {"shasum": "4b1415304cf50028ea81643643bd82ea05803689", "tarball": "https://registry.npmjs.org/color-name/-/color-name-1.1.1.tgz", "integrity": "sha512-KueG6e1jnj4enh6KzhXxA51awXK/yB4z1gUNPuH81hLDdmvmiQU4EDL1mZPB4m9rxEJkZ+kqmd51Ml9rSz2B3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHPAypeXYwmXNlnPj/dE4AERIXSEJHoIEGxC0/JXmisBAiAObNO9Co2OZ8v8289fhsy7lrG7p8HYaapJqX8jOJeO9g=="}]}, "directories": {}}, "1.1.2": {"name": "color-name", "version": "1.1.2", "description": "A list of color names and its values", "main": "index.js", "scripts": {"test": "node test.js"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+ssh://**************/dfcreative/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "DY", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "homepage": "https://github.com/dfcreative/color-name", "gitHead": "db3f3979970c30380f30ae3209a144c855578ae1", "_id": "color-name@1.1.2", "_shasum": "5c8ab72b64bd2215d617ae9559ebb148475cf98d", "_from": ".", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.0", "_npmUser": {"name": "dfcreative", "email": "<EMAIL>"}, "maintainers": [{"name": "dfcreative", "email": "<EMAIL>"}], "dist": {"shasum": "5c8ab72b64bd2215d617ae9559ebb148475cf98d", "tarball": "https://registry.npmjs.org/color-name/-/color-name-1.1.2.tgz", "integrity": "sha512-UzOVwiu1XqofxLGE8CyCtJySssb2YtmDn/PoNJgvOrqGrH0w6h3MlfRAo1qHIZHzX/Ov1kXHKv/7OwsIX0Ot+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0GwOwCSjpYtwk21Qlufv7KwdT6ZfAzWhQ3cY8zfen5QIhAK2tNeZy0zlDCKRU8HEKs5PC+vFGRLn7P8FLDap5PjFY"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/color-name-1.1.2.tgz_1489460073624_0.09481105045415461"}, "directories": {}}, "1.1.3": {"name": "color-name", "version": "1.1.3", "description": "A list of color names and its values", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/dfcreative/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "DY", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/dfcreative/color-name/issues"}, "homepage": "https://github.com/dfcreative/color-name", "gitHead": "cb7d4629b00fe38564f741a0779f6ad84d8007a2", "_id": "color-name@1.1.3", "_shasum": "a7d0558bd89c42f795dd42328f740831ca53bc25", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.1.2", "_npmUser": {"name": "dfcreative", "email": "<EMAIL>"}, "dist": {"shasum": "a7d0558bd89c42f795dd42328f740831ca53bc25", "tarball": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzKQts2FJq//siNTEblzss3pfSXqBCbyYqpigBXVzO3AIhAKgONJYDUfAClb2tknEu48xoMUPwhUUWdnWDz0UCtNzM"}]}, "maintainers": [{"name": "dfcreative", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/color-name-1.1.3.tgz_1500157027207_0.5641644957941025"}, "directories": {}}, "1.1.4": {"name": "color-name", "version": "1.1.4", "description": "A list of color names and its values", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/colorjs/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "DY", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/colorjs/color-name/issues"}, "homepage": "https://github.com/colorjs/color-name", "gitHead": "4536ce5944f56659a2dfb2198eaf81b5ad5f2ad9", "_id": "color-name@1.1.4", "_npmVersion": "6.4.1", "_nodeVersion": "8.11.1", "_npmUser": {"name": "dfcreative", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "shasum": "c2a09a87acbde69543de6f63fa3995c826c536a2", "tarball": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "fileCount": 4, "unpackedSize": 6693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpMyZCRA9TVsSAnZWagAA8GsP/0AsmYCiGphh3CFKIPep\nvNTX/KZgee4XYpqtD9GtlSg2t7vq3RzzMqe0ZVaVtI6lHu6xbx1sjCSKveN2\nhhzpchB4yDFadju+d180Blrawa+JAuJW+mG5k346Iqt6d8zuPPrHhraR6fEY\ntHCVxhQiHpgyzoyMATZnII8fNc236SCgvDwV0vJsJS3xbJp21peIVgZAOZg+\nKNzcM6dH1yxgZb99THhHYI4za/+xkIm2oy8o9AKIJz8HcogqOG5MHaklc5R+\niZlIE5A9KpbUbLerhUatXUKu6CeosCbQcPcYsOfifXw5ifQunM86ySU6OgQF\nSADAeVO6k5ZBFvzaSjc7La7+MisZ2KPAKiegCLMxcqc5xVBCcjZb0F7vS59m\nHTAa/uHLgmv76wiNMz2zHR+1qYWt3/mQq5OItunccFZQ5jxw3JWzygY9C/Zs\ndUakmc6+pCScPuDzs6OruKqHPk4S0fv8HfH8wBGFzd0GvHtT8xKUmit5p6G7\nTIPJV+dVaz5iO8uhhtUpVUogs8Y9DyoRDBaz3Btq19B8sYBwacMlRZCHiwBQ\nYdpTe9Go7NBPUNYEUsiSiZxCT6GuIdCit6P6Dwx6AZ3+DdeTEB1DcF9UVycE\nwbc1jz10q0pTbW5REgtFZ5eDTVXUkepA3+P6sLwubq7yu8lsqO34tYp3rOop\nshOJ\r\n=Rq6j\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA/cRJF1USbKE8OvNZK7EOTIg9+Bg+KvqGyAcFM83/7VAiB4QKj7PJ06UPRuqXUutmL6e+n5XgOGZlpNzYmkOyPUSQ=="}]}, "maintainers": [{"name": "dfcreative", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/color-name_1.1.4_1537526936346_0.08590528311695667"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "color-name", "version": "2.0.0", "description": "A list of color names and its values", "main": "index.js", "type": "module", "engines": {"node": ">=12.20"}, "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+ssh://**************/colorjs/color-name.git"}, "keywords": ["color-name", "color", "color-keyword", "keyword"], "author": {"name": "DY", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/colorjs/color-name/issues"}, "homepage": "https://github.com/colorjs/color-name", "gitHead": "38ff46b4a0742bbc0c467ec1405a36af2fc4a0e6", "_id": "color-name@2.0.0", "_nodeVersion": "18.9.1", "_npmVersion": "8.19.1", "dist": {"integrity": "sha512-SbtvAMWvASO5TE2QP07jHBMXKafgdZz8Vrsrn96fiL+O92/FN/PLARzUW5sKt013fjAprK2d2iCn2hk2Xb5oow==", "shasum": "03ff6b1b5aec9bb3cf1ed82400c2790dfcd01d2d", "tarball": "https://registry.npmjs.org/color-name/-/color-name-2.0.0.tgz", "fileCount": 4, "unpackedSize": 6241, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHl3LBby2OhLdePUluYqfjsICIoBtE1u5tb3gpVzDfSaAiADXmvGvhQkoto5DEp+ZrT8rInmUX/zgE7cSifDjby1TQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjamiPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrhvg//YsH0qCdESS0SFvXd7l0+OGfvXBwxi8rm69Qn5zF4Ajq7rpl2\r\nuyZUt65jKjCGduPrZGDMyvzyXC8isWXZv8tp1fTk6rk5oH5NsC+4VogX/SWi\r\nfOezuBd7YtF9w4N94ADgw1/BvbhzCBBZBxDy5EdlW13deDAb7z6bT1eM5n1D\r\nOEAr6JN4n5v43lvJshDfHXD7Uexr/LXQRzIYGdwllU90eJBGZN0NW2PXZFB5\r\nboQgxo8qKMwMnshDOITbv1HqzdEkdc2X1l1l8cpjzMvrHibEN7hjhHdJYr9o\r\nOllxRqlFZT0iSUff13D7W6J3PJf7A8qOsT6lqb1R1MIDkpAeuIjF0V/AOTy4\r\nUxs2oiCyHP7ZXPAV1uJoioJjm4sXVqkyQWGKYHOQVQY003C2pIm2JvjkPIbm\r\ni3DL0mwbor5n7OkDI1jQOCmqcZ66jilR43tLpieSnoMngp8EtEU8D73oyz0p\r\nCO8qR4NMBDEw6ooAJSUhppNtzAEWjfN/t5gMSGlGV5gYNFKSYDdy4XzKhhVW\r\nC/2Db0OTYPcP5QxPgI3waBhoR1l0mENT3bknQ/BOwb5Jpq8yJEQbz3QAoP7/\r\n2UPSFmWvN89ijRSipr8W+JFQnwtabc/19/q3uxjqfdN9+DxQpG0pLFuj3nFM\r\nKx5B2LIFSwxKELu4vIy4bxH1u/e1KwGFfFM=\r\n=Gs/B\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "dfcreative", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/color-name_2.0.0_1667917967304_0.7363222639507283"}, "_hasShrinkwrap": false}}, "readme": "A JSON with color names and its values. Based on http://dev.w3.org/csswg/css-color/#named-colors.\n\n[![NPM](https://nodei.co/npm/color-name.png?mini=true)](https://nodei.co/npm/color-name/)\n\n\n```js\nimport colors from 'color-name';\ncolors.red //[255,0,0]\n```\n\n<a href=\"LICENSE\"><img src=\"https://upload.wikimedia.org/wikipedia/commons/0/0c/MIT_logo.svg\" width=\"120\"/></a>\n", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "moox", "email": "<EMAIL>"}], "time": {"modified": "2023-03-08T01:43:51.822Z", "created": "2014-11-23T16:18:30.217Z", "0.0.1": "2014-11-23T16:18:30.217Z", "0.0.2": "2014-11-23T16:21:53.606Z", "1.0.0": "2014-12-15T19:08:24.911Z", "1.0.1": "2015-09-21T02:51:08.141Z", "1.1.0": "2015-12-24T02:03:18.586Z", "1.1.1": "2015-12-24T02:05:36.986Z", "1.1.2": "2017-03-14T02:54:35.452Z", "1.1.3": "2017-07-15T22:17:08.140Z", "1.1.4": "2018-09-21T10:48:56.546Z", "2.0.0": "2022-11-08T14:32:47.478Z"}, "homepage": "https://github.com/colorjs/color-name", "keywords": ["color-name", "color", "color-keyword", "keyword"], "repository": {"type": "git", "url": "git+ssh://**************/colorjs/color-name.git"}, "author": {"name": "DY", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/colorjs/color-name/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"tg-z": true, "flumpus-dev": true}}