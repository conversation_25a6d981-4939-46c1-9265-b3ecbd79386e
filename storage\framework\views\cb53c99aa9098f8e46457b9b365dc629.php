<?php $__env->startSection('title', 'Quản lý dịch vụ khách hàng'); ?>
<?php $__env->startSection('page-title', 'Quản lý dịch vụ khách hàng'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Quản lý dịch vụ khách hàng</h5>
                        <small class="text-muted">Theo dõi và quản lý các dịch vụ đã gán cho khách hàng</small>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('admin.shared-accounts.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-users me-1"></i>
                            T<PERSON><PERSON> kho<PERSON>n dùng chung
                        </a>
                        <a href="<?php echo e(route('admin.customer-services.daily-report')); ?>" class="btn btn-info">
                            <i class="fas fa-chart-bar me-1"></i>
                            Báo cáo hàng ngày
                        </a>
                        <a href="<?php echo e(route('admin.customer-services.reminder-report')); ?>" class="btn btn-warning">
                            <i class="fas fa-bell me-1"></i>
                            Báo cáo nhắc nhở
                        </a>
                        <a href="<?php echo e(route('admin.customer-services.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Gán dịch vụ mới
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- Thông báo cấp bách -->
                <?php
                    $urgentServices = $customerServices->filter(function($service) {
                        return $service->getStatus() === 'expiring' && $service->getDaysRemaining() <= 1;
                    });
                    $criticalServices = $customerServices->filter(function($service) {
                        return $service->getStatus() === 'expiring' && $service->getDaysRemaining() <= 2;
                    });
                ?>

                <?php if($urgentServices->count() > 0): ?>
                    <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                        <h6 class="alert-heading mb-2">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            🚨 CẢNH BÁO: <?php echo e($urgentServices->count()); ?> dịch vụ sẽ hết hạn trong 24h!
                        </h6>
                        <p class="mb-0">
                            Cần liên hệ khách hàng ngay:
                            <?php $__currentLoopData = $urgentServices->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <strong><?php echo e($service->customer->name); ?></strong><?php echo e(!$loop->last ? ', ' : ''); ?>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($urgentServices->count() > 3): ?>
                                và <?php echo e($urgentServices->count() - 3); ?> khách hàng khác.
                            <?php endif; ?>
                        </p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php elseif($criticalServices->count() > 0): ?>
                    <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                        <h6 class="alert-heading mb-2">
                            <i class="fas fa-clock me-2"></i>
                            ⚠️ CHÚ Ý: <?php echo e($criticalServices->count()); ?> dịch vụ sẽ hết hạn trong 2 ngày!
                        </h6>
                        <p class="mb-0">
                            Nên liên hệ khách hàng sớm để gia hạn.
                        </p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Thống kê dịch vụ kích hoạt hôm nay -->
                <?php if(request('filter') === 'activated-today' && $todayStats): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-primary text-white">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-line me-2"></i>
                                        📊 Thống kê dịch vụ kích hoạt hôm nay (<?php echo e(now()->format('d/m/Y')); ?>)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h3 class="mb-1"><?php echo e($todayStats['total_services']); ?></h3>
                                                <small>Tổng dịch vụ</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h3 class="mb-1"><?php echo e($todayStats['unique_customers']); ?></h3>
                                                <small>Khách hàng</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h3 class="mb-1"><?php echo e(number_format($todayStats['revenue_estimate'])); ?>₫</h3>
                                                <small>Doanh thu ước tính</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6 class="mb-1">Top gói dịch vụ:</h6>
                                                <?php $__currentLoopData = $todayStats['top_packages']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $packageName => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <small class="d-block"><?php echo e($packageName); ?>: <?php echo e($count); ?></small>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Simple Filter -->
                <div class="row mb-4">
                    <div class="col-12">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <input type="text" 
                                       name="search" 
                                       class="form-control" 
                                       placeholder="Tìm khách hàng hoặc dịch vụ..."
                                       value="<?php echo e(request('search')); ?>">
                            </div>
                            <div class="col-md-3">
                                <select name="filter" class="form-select">
                                    <option value="">Tất cả trạng thái</option>
                                    <optgroup label="Trạng thái dịch vụ">
                                        <option value="active" <?php echo e(request('filter') === 'active' ? 'selected' : ''); ?>>
                                            Đang hoạt động
                                        </option>
                                        <option value="expiring" <?php echo e(request('filter') === 'expiring' ? 'selected' : ''); ?>>
                                            Sắp hết hạn
                                        </option>
                                        <option value="expired" <?php echo e(request('filter') === 'expired' ? 'selected' : ''); ?>>
                                            Đã hết hạn
                                        </option>
                                    </optgroup>
                                    <optgroup label="Nhắc nhở">
                                        <option value="expiring-not-reminded" <?php echo e(request('filter') === 'expiring-not-reminded' ? 'selected' : ''); ?>>
                                            Sắp hết hạn - Chưa nhắc
                                        </option>
                                        <option value="reminded" <?php echo e(request('filter') === 'reminded' ? 'selected' : ''); ?>>
                                            Đã được nhắc nhở
                                        </option>
                                    </optgroup>
                                    <optgroup label="Ngày kích hoạt">
                                        <option value="activated-today" <?php echo e(request('filter') === 'activated-today' ? 'selected' : ''); ?>>
                                            🎯 Kích hoạt hôm nay
                                        </option>
                                        <option value="activated-yesterday" <?php echo e(request('filter') === 'activated-yesterday' ? 'selected' : ''); ?>>
                                            Kích hoạt hôm qua
                                        </option>
                                        <option value="activated-this-week" <?php echo e(request('filter') === 'activated-this-week' ? 'selected' : ''); ?>>
                                            Kích hoạt tuần này
                                        </option>
                                        <option value="activated-this-month" <?php echo e(request('filter') === 'activated-this-month' ? 'selected' : ''); ?>>
                                            Kích hoạt tháng này
                                        </option>
                                    </optgroup>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="service_package_id" class="form-select">
                                    <option value="">Tất cả gói dịch vụ</option>
                                    <?php $__currentLoopData = $servicePackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($package->id); ?>" 
                                                <?php echo e(request('service_package_id') == $package->id ? 'selected' : ''); ?>>
                                            <?php echo e($package->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i>Lọc
                                </button>
                                <?php if(request()->hasAny(['search', 'filter', 'service_package_id'])): ?>
                                    <a href="<?php echo e(route('admin.customer-services.index')); ?>" 
                                       class="btn btn-secondary w-100 mt-1">
                                        <i class="fas fa-times me-1"></i>Xóa
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Results Info -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="text-muted">
                        Hiển thị <?php echo e($customerServices->firstItem() ?? 0); ?> - <?php echo e($customerServices->lastItem() ?? 0); ?> 
                        trong tổng số <strong><?php echo e($customerServices->total()); ?></strong> dịch vụ
                    </span>
                    <small class="text-muted">
                        Cập nhật lúc <?php echo e(now()->format('H:i')); ?>

                    </small>
                </div>

                <!-- Customer Services Table -->
                <?php if($customerServices->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Khách hàng</th>
                                    <th>Dịch vụ</th>
                                    <th>Email đăng nhập</th>
                                    <th>Kích hoạt</th>
                                    <th>Hết hạn</th>
                                    <th>Trạng thái</th>
                                    <th>Nhắc nhở</th>
                                    <th>Người nhập hàng</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $customerServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $status = $service->getStatus();
                                        $daysRemaining = $service->getDaysRemaining();
                                        $rowClass = '';
                                        
                                        if ($status === 'expiring') {
                                            if ($daysRemaining <= 1) {
                                                $rowClass = 'table-danger'; // Đỏ cho 0-1 ngày
                                            } elseif ($daysRemaining <= 2) {
                                                $rowClass = 'table-warning'; // Vàng cho 2 ngày
                                            }
                                        }
                                    ?>
                                    <tr class="<?php echo e($rowClass); ?>">
                                        <td>
                                            <div>
                                                <strong><?php echo e($service->customer->name); ?></strong>
                                                <br><small class="text-muted"><?php echo e($service->customer->customer_code); ?></small>
                                                <?php if($status === 'expiring' && $daysRemaining <= 1): ?>
                                                    <br><small class="text-danger fw-bold">🚨 CẤP BÁC!</small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo e($service->servicePackage->name); ?></strong>
                                                <br><small class="text-muted"><?php echo e($service->servicePackage->category->name ?? 'N/A'); ?></small>
                                            </div>
                                        </td>
                                        <td><?php echo e($service->login_email ?? 'Chưa có'); ?></td>
                                        <td><?php echo e($service->activated_at ? $service->activated_at->format('d/m/Y') : 'Chưa kích hoạt'); ?></td>
                                        <td>
                                            <?php if($service->expires_at): ?>
                                                <?php echo e($service->expires_at->format('d/m/Y')); ?>

                                                <?php if($status === 'expiring'): ?>
                                                    <br><small class="fw-bold <?php echo e($daysRemaining <= 1 ? 'text-danger' : ($daysRemaining <= 2 ? 'text-warning' : 'text-info')); ?>">
                                                        Còn <?php echo e($daysRemaining); ?> ngày
                                                    </small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                Không giới hạn
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($status === 'active'): ?>
                                                <span class="badge bg-success">Đang hoạt động</span>
                                            <?php elseif($status === 'expiring'): ?>
                                                <?php if($daysRemaining <= 1): ?>
                                                    <span class="badge bg-danger">🚨 SẮP HẾT HẠN</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">Sắp hết hạn</span>
                                                <?php endif; ?>
                                            <?php elseif($status === 'expired'): ?>
                                                <span class="badge bg-danger">Đã hết hạn</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Chưa kích hoạt</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($service->reminder_sent): ?>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-success me-2">
                                                        <i class="fas fa-check"></i> Đã nhắc
                                                    </span>
                                                    <small class="text-muted">
                                                        <?php echo e($service->reminder_count); ?>x<br>
                                                        <?php echo e($service->reminder_sent_at ? $service->reminder_sent_at->format('d/m H:i') : 'N/A'); ?>

                                                    </small>
                                                </div>
                                                <?php if($service->needsReminderAgain()): ?>
                                                    <div class="mt-1">
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="fas fa-clock"></i> Cần nhắc lại
                                                        </span>
                                                    </div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <?php if($status === 'expiring'): ?>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-exclamation-triangle"></i> Chưa nhắc
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="<?php echo e(route('admin.customer-services.show', $service)); ?>" 
                                                   class="btn btn-sm btn-outline-info"
                                                   title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.customer-services.edit', $service)); ?>" 
                                                   class="btn btn-sm btn-outline-primary"
                                                   title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                <?php if($service->getStatus() === 'expiring'): ?>
                                                    <?php if(!$service->reminder_sent || $service->needsReminderAgain()): ?>
                                                        <button class="btn btn-sm btn-outline-warning" 
                                                                onclick="markReminded(<?php echo e($service->id); ?>)"
                                                                title="Đánh dấu đã nhắc nhở">
                                                            <i class="fas fa-bell"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <?php if($service->reminder_sent): ?>
                                                        <button class="btn btn-sm btn-outline-secondary" 
                                                                onclick="resetReminder(<?php echo e($service->id); ?>)"
                                                                title="Reset trạng thái nhắc nhở">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                                
                                                <button class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete('<?php echo e($service->customer->name); ?> - <?php echo e($service->servicePackage->name); ?>', '<?php echo e(route('admin.customer-services.destroy', $service)); ?>')"
                                                        title="Xóa">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        <?php echo e($customerServices->withQueryString()->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Không tìm thấy dịch vụ nào</h5>
                        <?php if(request()->hasAny(['search', 'filter', 'service_package_id'])): ?>
                            <p class="text-muted">Thử thay đổi bộ lọc hoặc <a href="<?php echo e(route('admin.customer-services.index')); ?>">xóa bộ lọc</a></p>
                        <?php else: ?>
                            <p class="text-muted">Hãy <a href="<?php echo e(route('admin.customer-services.create')); ?>">gán dịch vụ đầu tiên</a></p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa dịch vụ này?</p>
                <p class="text-muted" id="serviceToDelete"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Xóa</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function confirmDelete(serviceName, deleteUrl) {
    document.getElementById('serviceToDelete').textContent = serviceName;
    document.getElementById('deleteForm').action = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function markReminded(serviceId) {
    const notes = prompt('Ghi chú về việc nhắc nhở (tùy chọn):');
    if (notes === null) return; // User cancelled
    
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    
    fetch(`/admin/customer-services/${serviceId}/mark-reminded`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({ notes: notes })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload(); // Reload to update UI
        } else {
            alert('Có lỗi xảy ra!');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra!');
    });
}

function resetReminder(serviceId) {
    if (!confirm('Bạn có chắc chắn muốn reset trạng thái nhắc nhở?')) {
        return;
    }
    
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    
    fetch(`/admin/customer-services/${serviceId}/reset-reminder`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload(); // Reload to update UI
        } else {
            alert('Có lỗi xảy ra!');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra!');
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\truycuuthongtin\resources\views/admin/customer-services/index.blade.php ENDPATH**/ ?>