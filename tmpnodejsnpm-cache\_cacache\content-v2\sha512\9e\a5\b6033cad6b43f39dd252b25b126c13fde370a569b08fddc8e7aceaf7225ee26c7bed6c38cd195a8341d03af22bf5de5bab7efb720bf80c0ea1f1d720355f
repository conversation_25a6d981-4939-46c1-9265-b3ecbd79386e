{"_id": "sugarss", "_rev": "29-85c5e52f223a63d815a62429f0a4e2c7", "name": "sugarss", "dist-tags": {"latest": "5.0.0"}, "versions": {"0.0.1": {"name": "sugarss", "version": "0.0.1", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@0.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "93155f94b0b99ef49daf5d646afeb8a178eb7ae1", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-0.0.1.tgz", "integrity": "sha512-b2GVhE0CTjpOeYMXLDAFhBkZXhD8sJJPAeezXe5/NSSAeifWxX7CAb1y0oR6IUIbZzsfKZd56RoCu3E4w+lBAw==", "signatures": [{"sig": "MEYCIQDSq0DmIm4t6a0iJwh0g5+d8AEbMCLvyzTkf1pTDDOo3wIhAITo4bH0pFLKE5sAMcx+ekHjxsG+ZFQl9HQP3UURNNgj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "93155f94b0b99ef49daf5d646afeb8a178eb7ae1", "gitHead": "8048452089706c9dc012db85cb9ea030579e2049", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepublish": "npm run build"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "5.7.1", "dependencies": {"postcss": "^5.0.19"}, "devDependencies": {"ava": "0.12.0", "eslint": "2.2.0", "babel-cli": "6.6.5", "babel-core": "6.6.5", "babel-eslint": "6.0.0-beta.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "eslint-config-postcss": "2.0.2", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-plugin-precompile-charcodes": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-0.0.1.tgz_1457425450443_0.07879688148386776", "host": "packages-13-west.internal.npmjs.com"}}, "0.1.0": {"name": "sugarss", "version": "0.1.0", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@0.1.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "10418201e8d38ed5e0f32fcf1f880dadaa08651d", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-0.1.0.tgz", "integrity": "sha512-o8+J4IcudFZCH9A6rijwOmkqiV+y35qguoDCwgI7hncuzlzmPK69Im+o8uJRQ0pkF+qEhPHWa3LlSMN6qmTwvg==", "signatures": [{"sig": "MEYCIQDonycoJpoqmyER3vlfgyK3AK3J4WjHPxaLfeAsEZrZ3gIhAJfcLFc99Yg2yFlVy5kVUencQrnWw99hU5TivniG7urn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "10418201e8d38ed5e0f32fcf1f880dadaa08651d", "gitHead": "97ad86ce7d2e117cfa9c75921ef2c0cf998070fd", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepublish": "npm run build"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "5.7.1", "dependencies": {"postcss": "^5.0.19"}, "devDependencies": {"ava": "0.12.0", "eslint": "2.2.0", "babel-cli": "6.6.5", "babel-core": "6.6.5", "babel-eslint": "6.0.0-beta.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "eslint-config-postcss": "2.0.2", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-plugin-precompile-charcodes": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-0.1.0.tgz_1457483769374_0.5579545076470822", "host": "packages-13-west.internal.npmjs.com"}}, "0.1.1": {"name": "sugarss", "version": "0.1.1", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@0.1.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "18901fe7be8d93629abdd915e23e2dedbe241ce9", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-0.1.1.tgz", "integrity": "sha512-nBe0hS6546TORWj9spuXMpTQidvj2pnPi/cpnITXnEycUMvi2WiJZfnxFwSi1o3cmUNIc7bO7zDJj/zO0TRbmw==", "signatures": [{"sig": "MEYCIQDwya20cNKG/BN6DIrlvIsa/p2X5HIVjhJoI0qESW/qTQIhAKxTXrBo57NBjI/jGDWst9zpC9+cTz7P/magCYTyUiTB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "18901fe7be8d93629abdd915e23e2dedbe241ce9", "gitHead": "b89292ebf74ed0098717cd435639da4bea2b2513", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepublish": "npm run build"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "5.8.0", "dependencies": {"postcss": "^5.0.19"}, "devDependencies": {"ava": "0.13.0", "eslint": "2.4.0", "babel-cli": "6.6.5", "babel-core": "6.7.2", "babel-eslint": "6.0.0-beta.6", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "eslint-config-postcss": "2.0.2", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-plugin-precompile-charcodes": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-0.1.1.tgz_1458059650433_0.43776392634026706", "host": "packages-13-west.internal.npmjs.com"}}, "0.1.2": {"name": "sugarss", "version": "0.1.2", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@0.1.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "d3c630ac59a9686354e0c5282cb2566fc64d48b3", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-0.1.2.tgz", "integrity": "sha512-JpIaQ+JBkDlaKZqngnGP5iK12C9J2hbIe/J6DaPFsdHX9cLUGEW8ba0f0KJ4PHBHiZ0LWynEnMbUZ96Sz4BjFA==", "signatures": [{"sig": "MEYCIQCAeC073RfZav+FYlM8Zo6aLQbWzXe3BWalCsYN78FkvQIhAL6koS8MVAkAEQg9aAh6rn5Ldv0uellp9RoHfooOPmyg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "d3c630ac59a9686354e0c5282cb2566fc64d48b3", "gitHead": "2871c8eedbe254fbfab87715166336df7f068765", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepublish": "npm run build"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "5.8.0", "dependencies": {"postcss": "^5.0.19"}, "devDependencies": {"ava": "0.13.0", "eslint": "2.4.0", "babel-cli": "6.6.5", "babel-core": "6.7.2", "babel-eslint": "6.0.0-beta.6", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "eslint-config-postcss": "2.0.2", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-plugin-precompile-charcodes": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-0.1.2.tgz_1458195375274_0.7997586966957897", "host": "packages-12-west.internal.npmjs.com"}}, "0.1.3": {"name": "sugarss", "version": "0.1.3", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@0.1.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "c3b0955c04346622064c8d674ce46ad40dcbea82", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-0.1.3.tgz", "integrity": "sha512-AR9X93aVe7WGzUNO+mbk+YAD4LpT4cCsuH0sPYzpvzXOo/kPSkvsEz0pJCsqFBsFlwo3B3KNVohxutkJ77xnXw==", "signatures": [{"sig": "MEQCIBEkPYgjPE7P9MGBTbLftx2SvMkkDqRzZaBRiOnoI40kAiBuz9rY2Cx3dUZCWozkOTv4OcmXQK61ONiWbbV8j/nMGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "c3b0955c04346622064c8d674ce46ad40dcbea82", "gitHead": "869fa4df28c9d438f9289c072f33f741e7be4473", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepublish": "npm run build"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"postcss": "^5.0.19"}, "devDependencies": {"ava": "0.14.0", "eslint": "2.8.0", "babel-cli": "6.7.7", "babel-core": "6.7.7", "babel-eslint": "6.0.3", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "eslint-config-postcss": "2.0.2", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-plugin-precompile-charcodes": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-0.1.3.tgz_1461397568542_0.04163599107414484", "host": "packages-16-east.internal.npmjs.com"}}, "0.1.4": {"name": "sugarss", "version": "0.1.4", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@0.1.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "0b1e5027232d305c14bcee90a46e2a3bc4817322", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-0.1.4.tgz", "integrity": "sha512-2Ykg208apZZaI4/xaxhAS7fzPGYB3VLI5MLyKuf2tjBUiDi6PjLJowEr8wIRLUs4ak3UJZSEHkBO2fNnDxxnJg==", "signatures": [{"sig": "MEUCIQCdWL5iEIqriWiDmHeMk5cJTG7WUuLGDbn1dt3FYm5pZgIgfBd/FacuQps6KeZKjhwCyz5RGFp60lBDSikGkpTFnpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "0b1e5027232d305c14bcee90a46e2a3bc4817322", "gitHead": "a869c18506168bea1429c9628d142b9011fbe986", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepublish": "npm run build"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"postcss": "^5.0.21"}, "devDependencies": {"ava": "0.15.1", "eslint": "2.11.0", "babel-cli": "6.9.0", "babel-core": "6.9.1", "babel-eslint": "6.0.4", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.7", "eslint-config-postcss": "2.0.2", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-0.1.4.tgz_1464626427902_0.720276698935777", "host": "packages-12-west.internal.npmjs.com"}}, "0.1.5": {"name": "sugarss", "version": "0.1.5", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@0.1.5", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "c0c25bc0941678504237e806aecd160a30a94eea", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-0.1.5.tgz", "integrity": "sha512-AJKFHsICop1t5mpjVbz8Y6lGwNyJUa4kw0lEURsrmZNGc1+OVQAYbRksIVco2CtTL2zdNM07h5oApgKwem1ACQ==", "signatures": [{"sig": "MEQCIGiikatOhZOsIGnyB3eGl/Hh27YUIXpK/my+Ii8LSRewAiAxcAtA0Sa2uVEkd0m3ZoUE4wHtFTJ8tPFs2aZEiwZOzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "c0c25bc0941678504237e806aecd160a30a94eea", "gitHead": "4dc5c18742af3988a0287009fcd2d5da1ba463d7", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepublish": "npm run build"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"postcss": "^5.1.0"}, "devDependencies": {"ava": "0.15.2", "eslint": "3.1.1", "babel-cli": "6.11.4", "babel-core": "6.11.4", "babel-eslint": "6.1.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.9", "eslint-config-postcss": "2.0.2", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-0.1.5.tgz_1469278585745_0.10302902827970684", "host": "packages-16-east.internal.npmjs.com"}}, "0.1.6": {"name": "sugarss", "version": "0.1.6", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@0.1.6", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "fe3ac0e1e07282aef1de84a80b72386ff4e7ea37", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-0.1.6.tgz", "integrity": "sha512-4Oz04ycq0JYd49G0t3TyRnQoFE9a822sWPHcVyhsDzzG67IC7AA1RJqQ5/7qlQxk2ZW4lt9bf/XvmU0UP3SJ9w==", "signatures": [{"sig": "MEQCIGO1fVJA18RUbTZS0OXQ1nK+E6s+3akgNRoh5wg6CpyuAiB8uJF+zT4JQn5KfCpCsamRoVI1VKv1IbqLjgjaNjzhPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "fe3ac0e1e07282aef1de84a80b72386ff4e7ea37", "gitHead": "399a098e0ec09079eeb8c4d30ca6c8abbc2f9f2c", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepublish": "npm run build", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "lint-staged": {"*.es6": "eslint", "test/*.js": "eslint"}, "_nodeVersion": "6.5.0", "dependencies": {"postcss": "^5.2.0"}, "devDependencies": {"ava": "0.16.0", "eslint": "3.4.0", "babel-cli": "6.14.0", "babel-core": "6.14.0", "pre-commit": "1.1.3", "lint-staged": "2.0.3", "babel-eslint": "6.1.2", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.10", "eslint-config-postcss": "2.0.2", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-0.1.6.tgz_1473304421438_0.9350021921563894", "host": "packages-12-west.internal.npmjs.com"}}, "0.2.0": {"name": "sugarss", "version": "0.2.0", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@0.2.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "ac34237563327c6ff897b64742bf6aec190ad39e", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-0.2.0.tgz", "integrity": "sha512-gmAdmKbPFALbfAMVqr8lmoIn3HCdiXZMHYpZGbdbAiflg+YGqAfBbTogBMo9EFo5AWUVgOWGXeFajKtP8e0Xxw==", "signatures": [{"sig": "MEYCIQC2I/36MPr1nccjUZhfkMpZ191/h/ZEV9GMXOBg1KpceAIhAI13X6fzzo3gx53nQ4XQuiLHnGhd15xlJVf1kQwLFhyv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "ac34237563327c6ff897b64742bf6aec190ad39e", "gitHead": "ded261e3d6cbb2ba779f8152efc9cfd69990c34f", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && ava && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepublish": "npm run build", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "lint-staged": {"*.es6": "eslint", "test/*.js": "eslint"}, "_nodeVersion": "6.7.0", "dependencies": {"postcss": "^5.2.4"}, "devDependencies": {"ava": "0.16.0", "eslint": "3.7.1", "babel-cli": "6.16.0", "babel-core": "6.17.0", "pre-commit": "1.1.3", "lint-staged": "3.0.3", "babel-eslint": "7.0.0", "babel-preset-es2015": "6.16.0", "babel-preset-stage-0": "6.16.0", "postcss-parser-tests": "5.0.10", "eslint-config-postcss": "2.0.2", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-0.2.0.tgz_1475852527678_0.7048247170168906", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.0": {"name": "sugarss", "version": "1.0.0", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@1.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "65e51b3958432fb70d5451a68bb33e32d0cf1ef7", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-1.0.0.tgz", "integrity": "sha512-d+VKMiTOsveF4swm/OriJoQ5zEE0XF+qvEd/84+3UTa9bH/SnrnrN9XBEZz8LtQWI4lLuC9SdxhoLvFDLFxKgA==", "signatures": [{"sig": "MEUCIQCkd/6O77+OK227c+I3DuennlN+8s3r97iNr1h0qEIMpQIgTwl7EO0ntvuazm1heGwGMcWyC16O28nk+0T/bKBKWUA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "babel": {"plugins": ["add-module-exports", "precompile-charcodes"], "presets": [["env", {"loose": true, "targets": {"node": 4, "browsers": "last 1 version"}}], "stage-0"]}, "_shasum": "65e51b3958432fb70d5451a68bb33e32d0cf1ef7", "gitHead": "92b0b207bf89fb610fae89bba0b859e5b0e4e8a7", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && jest && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepublish": "npm run build", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "lint-staged": {"*.es6": "eslint", "test/*.js": "eslint"}, "_nodeVersion": "7.10.0", "dependencies": {"postcss": "^6.0.0"}, "eslintConfig": {"env": {"jest": true}, "rules": {"complexity": "off", "key-spacing": ["error", {"align": "value"}]}, "parser": "babel-es<PERSON>", "extends": "eslint-config-postcss"}, "devDependencies": {"jest": "^20.0.0", "eslint": "^3.19.0", "babel-cli": "^6.24.1", "babel-core": "^6.24.1", "pre-commit": "^1.2.2", "lint-staged": "^3.4.1", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.4.0", "babel-preset-stage-0": "^6.24.1", "postcss-parser-tests": "^6.0.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-1.0.0.tgz_1494154209435_0.4844046556390822", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.1": {"name": "sugarss", "version": "1.0.1", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@1.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "be826d9003e0f247735f92365dc3fd7f1bae9e44", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-1.0.1.tgz", "integrity": "sha512-3qgLZytikQQEVn1/FrhY7B68gPUUGY3R1Q1vTiD5xT+Ti1DP/8iZuwFet9ONs5+bmL8pZoDQ6JrQHVgrNlK6mA==", "signatures": [{"sig": "MEQCIDH2ercLlxPIHmYf43idCgO9h1U0rp6N8SQ3DF8BZyjoAiA8UdsExZ1a40S+sb89DKonCGvq17hganzRnaO3ytS/sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "babel": {"plugins": ["add-module-exports", "precompile-charcodes"], "presets": [["env", {"loose": true, "targets": {"node": 4, "browsers": "last 1 version"}}], "stage-0"]}, "gitHead": "a5fcc84ed0bae3e257a0f1ce0a6c5360c699b634", "scripts": {"lint": "eslint *.es6 test/*.js", "test": "npm run build && jest && npm run lint", "build": "npm run clean && babel -s inline -d ./ *.es6", "clean": "rm *.js || echo 'Already cleaned'", "prepack": "npm run build", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "lint-staged": {"*.es6": "eslint", "test/*.js": "eslint"}, "_nodeVersion": "9.0.0", "dependencies": {"postcss": "^6.0.14"}, "eslintConfig": {"env": {"jest": true}, "rules": {"complexity": "off", "key-spacing": ["error", {"align": "value"}]}, "parser": "babel-es<PERSON>", "extends": "eslint-config-postcss"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.10.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "pre-commit": "^1.2.2", "lint-staged": "^4.3.0", "babel-eslint": "^8.0.1", "babel-preset-env": "^1.6.1", "babel-preset-stage-0": "^6.24.1", "postcss-parser-tests": "^6.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss-1.0.1.tgz_1509907406533_0.5031481094192713", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "sugarss", "version": "2.0.0", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@2.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "ddd76e0124b297d40bf3cca31c8b22ecb43bc61d", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-2.0.0.tgz", "fileCount": 12, "integrity": "sha512-WfxjozUk0UVA4jm+U1d736AUpzSrNsQcIbyOkoE364GrtWmIrFdk5lksEupgWMD4VaT/0kVx1dobpiDumSgmJQ==", "signatures": [{"sig": "MEYCIQD0qnj6FfaVerdLEGwI2ymWTqlUr4huwLbn4SenNcA+VwIhAPNci81ZxWRJUVbvkdmdt29cLl2Sr1UMPMSG/Bg295H7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbl5jCRA9TVsSAnZWagAAoEYP+wXbA5SDgotlLCMw4cts\n9bDmJHhib9Wh8svRiMYsmQ1HAQahhrlWN3BubEOM/NQjDkrc4ZPmaO3rqnzp\nAOSCnUwja8QFrVq6u32zes3wNyBsT6FCAF42l/+8CsVHDmLuDCTqWX1WhhK4\ns/ATgY3wZfeBRcyZMS9if1F+GfIwxJ8LoAcQB0A+/9B8WwovDQHzeAcQdF6a\nZnVXo8nQnZFgvisewgS/9Ng8u7pBeXIglZTo6r1nXEMbaM2qrGoJgyxk5h/U\nakQm0zT19idioforscfxnbPEIejbHN47DDh94Hj4lznDr0AI4PGDsCNmdbc1\n09MfwP2/BI5zzsPukWuJlokTkJMVifMD7aH+ufrIOx5mNH1UU+UNZVeEfa/C\nDjQvA+IFc28YnbnbGh0SfMyo/eDxnPx8e+9x4J6+jMtaUI4cbSxhDiLZGFDo\nBTc3H6QWFu9xsZq2WTf2UdfHUE5us63wfpqemnAgqjTXyEcyMSzNp65fYkkG\nNCCyeJRNByPNbmj8p0Xhm4AbfD0r+kVTJDmichUb3BkGZoflhdqODDZJpu5N\nt3NIlfC0aoPN+kpZgSCfmb3Af+/PdRFYLGpSVeunQS+X0wGC3ZCl2JBDNQtM\n5lfPZOh7YiTfzxTyW2juTNo6D0Y0bi907CDZhJ3n1UHimIq4M67Tbl7kZfOW\ngjGW\r\n=sa3z\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"postcss": "^7.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/sugarss_2.0.0_1533959779336_0.6646467809699204", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "sugarss", "version": "3.0.0", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@3.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "fb490f9d6f5f60e0a14209a7c8e611d2ddbde489", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-3.0.0.tgz", "fileCount": 13, "integrity": "sha512-9FkDUV3kQp7GHv56WHdPR11/OWTS6nf8OWnHrlkxPG9qQaOBlacB/Wb+zK3uBApUGwbwf7CFHQGlmCu3ljF1fQ==", "signatures": [{"sig": "MEYCIQCIWhPAng9Lg2mzY40o4mynt6D31Us0KW1g2O4R9PjjGQIhALmW/3JZB+uJVEr9MV0OUmCMSKMn/oFrRksWIx0epaqw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYPRUCRA9TVsSAnZWagAAYT4QAIDVAwQf424IJit9Er2D\nS8Wxvu9RP1d1ouLgMA7p/ZX4iJnGujOQN3FrQhZU68bQkYWTdK1CaYj7sDdp\nkiT+pzDTPc1ZEeplzRlcBFwtVM4GfpIAhWGouq3nRYfMIKHRqYhAVhvCeVOK\nQkuM509UQp1tkGnFbYT4dDz6oeej9p4DCxomMYIim1APFI4de689T1iFS/j+\nioGR/prseal0LrJYBvU5a1HvAaWxGUnJG19YGe+jcw4lD2OgKBbH3JQbzqQx\nj58g2u5ylBt/RY3Fb+Ce4PbPoiPTcSPDmlBQdbHSTPmIKaHjkGjmn1xHBb8l\nAdVFqN/qfYgDF/ASQy2yXGhTE6mW5uymMU1G64U+3vm++6pRioAbt2YC2XMH\nW6lfcxrBG6CAWAQkw8fhMnwn7UMJYGYl6OpZS55IQIKA5TYvmUOCEy9+Wfp6\nRxE/bwcpMNjLRav/VL4If0x5Sh/fo4gZgan2R/A930L2UBGhGBWAlCbsqsby\nlz9FxPe+ERwePc2/+QNegOhuoi0pj5ZXq0Uv6hIWnMU3ZJghGXmUBsw2htsO\nHLwwHv4A8jzXOVvs7EeF0Rsn3gAkF63s2/KdRMEsziNnaONN5tO4bz5bVIjr\nvKfNObQAeferRVmyt6JPnXE1WLQlU1mBbRGov0Ih4jBrY1x91ibb5XWRGcvP\nLt38\r\n=gzCU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./": "./"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "14.10.1", "dependencies": {"postcss": "^8.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/sugarss_3.0.0_1600189524211_0.08362728410000697", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "sugarss", "version": "3.0.1", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@3.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "1e4e315b3b321eec477ef9617c8964bcf3833b0c", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-3.0.1.tgz", "fileCount": 13, "integrity": "sha512-xW0tTjuJdd3VSsPH2dLgNDzESka1+Ul3GYVziyhX7GyXQboOARDaeEU++IjhOZPnoKoMENsU0tvtrCKr1sJwlw==", "signatures": [{"sig": "MEUCIE/TuBLlCV0CcPvuGbb6FE06ZXtKRYqnTVBA/IEXWuofAiEA7C1NoDHwcjpbDdrcoxHCkWB4KGIJ1RHZ5JUTI/57GgY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfb91QCRA9TVsSAnZWagAApIQP/AxraNFQbG1h52Uxa6Yh\n9J2LIq0u4Vyz9SnG+M66HtkLh6YJG5QCuW5leqSGAA/ZM5RNNdBDp/Gz7d5G\nQgKYjFMogSGs1PQTckATNmhKa7o7qApW6LGVxP2c+HsnKLWc0l/873F6/JyR\nb+vlDozY39Lipj5qq1LOAtDIe9P6cixNeIdvjC4i9/30XQVc3NG10KRZ5/on\nq1RCtZ8cwk4dCn+fybXcBmhbvHrBjSbsAJHWbfLhTocc2st6f83RTsVt0mw7\noARkt1jmaGBcuXaGhZkWLxWzAmEY7ws45JJoZgUNHhxwxB2N4LFov/5d4Uy5\nL6bQwqqXZ5M+dvg9DfmYuAoMeN7q4GmW+6Ik10KEVtchQ7rqRWmuurKGbb1T\n5NH3ASNY68iJdWJBKb2QbKJetJ+ux6B7u0cVKyu5fYfpNn4voCXyeqfOxSkA\n/huc6+dI4HuWayFofY11jnKUKM204V9kxyqtnF7LVa8Eek+erLzqmMmWQ4iR\nLq1S++F7/u4ZmRbyIUJzL9afyw4C3Sa+W5YwxCKLDVj3DZUfP/Q2kQYv+ymF\nDqvtQBlCG/ZZYlp7vPRKIh1rxQbyME3vpuRtv3JkQWc+ExOAOj/S8MIK2RhG\nbLJ+lUSp9dKly2ZOQ2oxT5efmYUE/xdH8zmE8WzYzaf7K48S+OyDY6CCjd8Q\nEw1A\r\n=FNgK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./": "./"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "14.12.0", "dependencies": {"postcss": "^8.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/sugarss_3.0.1_1601166671638_0.4259563227125567", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "sugarss", "version": "3.0.2", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@3.0.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "1665ef9ade10acc2ef04e014dead03c4bcc29b90", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-3.0.2.tgz", "fileCount": 13, "integrity": "sha512-ZkHjBT8KLsevaVl5gJPgvlBGhwpFrS7/ioGtHk7j3UGtzLM8AJnPgvrD7+ynpgmWXvR9NoMOc0mWfKjaKbcIEg==", "signatures": [{"sig": "MEUCIQCcVQ6v3k8tmv/otn5AgBTmAwR9IoBarBfbER/kfTzNfgIgd+7B1hnoOg/25Fq1DNPRxo2RiU8eoy2SmPvjY4QdGgU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpCTlCRA9TVsSAnZWagAA4ooP/ivmcb0xijCL/7QztSk+\nyBuhNKitmPkBMD5kpi/2FYiXGkqUPJgcLxdSZ+qwTZE62uvRhDyr5Dgayk7Q\np9eYF90HWKY5ceAv8f3mp/BdbMptr8sh+XlYV3GsnbkSc7JJevSL+7WR7tyt\npmL6zRVTECmpf0STGMg9wz38ejyJusuOfD0lBsTZ7eznw6l/osdRib86DM/6\nnyCxWy/hWNwYuFb74yoQJzBsOOxaQlNjxVD1s148o+b081ZchebCXH8i1CuG\n+6pvyP7JcoKHfbgkdy74DJ6YtlcMcbL6Yp/bIntvM0I/5wxk/GWPiHD7ezbH\nxd6WwWcXEXo/j2n6o9lUa7TIpkkjyt88p6cbj8NSeKMYYTaFJQqetHeNHWBu\nQyy2AfuTH8d73v93wDZ6A8RNirmwYfpVbVGl8ySf6/pFW3xrTgXH/WPJmB8W\nMtkFL/KQ/BR4tnLU+duOhLcC2vbZwARkUZSI6PB//cxMyHIaAaie3O8MH4yG\nSAk5wvYiVPgzcVBFyvxrZEq3/RTiWy3xbJDCarOZMpiVnSdBAM/M5/b9rogK\nw3cjHdzhNoMG+zMhyhQgPmT2eCGE/q+1uU4r/GWNGm66iKNuV0lLE3qp8D9e\nMKgVAAsuO8nTKp/nkAu3F3Q18oF8KcP/LjD0H6YZ50eQFff86DJUZvXx36vz\na0o3\r\n=yU1F\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./*": "./*"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"postcss": "^8.1.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/sugarss_3.0.2_1604592868507_0.6130340186248449", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "sugarss", "version": "3.0.3", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@3.0.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "bb2489961b98fbd15e4e35d6b9f4f2ee5547a6cb", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-3.0.3.tgz", "fileCount": 13, "integrity": "sha512-uxa2bbuc+w7ov7DyYIhF6bM0qZF3UkFT5/nE8AJgboiVnKsBDbwxs++dehEIe1JNhpMaGJc37wGQ2QrrWey2Sg==", "signatures": [{"sig": "MEUCIQDYisDR4zDpHSyDF9qbkCspxD9m1/cx67YfMPL+jDNRIQIgLxWKDuF70juLLKCBaM0XAyZ559gNQ3VYfAe5Bg7HjFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpCsOCRA9TVsSAnZWagAAht4P/i/4EXftjhNGmkUE1V9X\n8gtChSL1qfPQ/SymhservI7KeFqzIwv9oiQLETsZNc3KnUnVYm6ASmFXfqJX\nGz54YGiF+5dajPo4sZzseZRWI9ZESits2wleSc5X55/R7/u1ArweF0wQkQzb\nLgKJjd3uv/xZmb3TgxrnDdAQKtOvOKWX+OhaZwIC5fFFK90nXj65yQrl5U3T\n7mTLfdgLJwEThsheChIuurvLt/EFc11cDG/OtcU8Idt9EsvaRt56z47ocAQL\nkOIL3Myd7E1qCuPaTq/sEr2S3uEI2TDt3cuGojHsomJajk7ns5ucJaepnziX\nif4MU27EGeLXMLVN4y4uXNiX9Qdxkf77h0+AP7ENLt2ojrtHiqhyaFpz+AYu\nK5t+ZthoPha9wU4RxUoYB47Ynwi6qVCv81Df6B8kqubQwgMrBubDZXLQh918\nNWaj98uxvLj6PzKIPnk2RNEEXvlf3tnzwkzKQ+jTygstkIxa3hG+7fcPXHbI\nQjfjrSh7TvkHil8oCeRiL3lUO2Avi7b4QLoJCkThrMKspdJLfTuBfjACmdCS\nCACFywvWA4nSKTauey34D74cpGLN2YlCwYPyoPdV8378YDfSW8BzKA9EyBjN\nXDBcUOF4gofQPhxrxkkMhC8DTRkqlwHUznwOxvhvk8J35Wyl5WsDHbEulfDJ\neM+B\r\n=DpBB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./": "./"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"postcss": "^8.1.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/sugarss_3.0.3_1604594446355_0.17258071795762442", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "sugarss", "version": "4.0.0", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@4.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "f27cd3581a629f5945308bba2cff4c0ec3f48559", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-4.0.0.tgz", "fileCount": 31, "integrity": "sha512-UGRXeXalq9AnzfA+1cacM3lt29TtBUw21CqGPUNpgkilW6K19uAKZ8s0elCfIskFl8HtS3af23vmx3R95s5WXw==", "signatures": [{"sig": "MEUCIQCD3t+OyBSzSD4h2ERLUjYgkjQj3F10PZQKmm0lQl/TbAIgOk38wx9YcNdk6r0JtBoZXMCGqqiZ3DosgTXEYfwtjsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 356514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxu9nCRA9TVsSAnZWagAAgmEP/3H1XilQJ3Jo8DPTdtfi\nEK2RnWKgPdzhBuaUeixZk6biI+c/1zleRCJdp+Eb0FGotpvKf/i1SH/+xdJ3\nzpa5invMlUTwfjeX4g3irQTm0EHMAan7KMorhxyJT9DT5LjBQQ72uzVBo602\nkuPW0T432MSCByWzSqecEWdzohMiOvk9bhGXc4vHmSfUW1mCIEFyMV8U1t4l\njQ8R9+Q/CgKgq8WW8XjcU1AfKc8a8KctvvhYiT8S4ge18y/kKB7bY2J65szb\n/yVWw+02pUtAalvbvaMi0zFVB2DpO/mTnXOsukovIocgdgMOzslzHvaNtlFC\n8NZSQF1U1/Rm4gyGY7Jy6jLU3oEFU//F1n/WvLtnXbOst7Xn52RzgtqIFFD2\nYccP+nt8riOxlYJIu6XX+t2qzsv2mnO0LNNwp/lK/fLYanGfr3zigNbhSrIQ\nysScCRd+823mEHFyJXZxr3C9MCbl6sH6KGDz9QMQHT3RRvN8yEzIwObNwFhY\nrtDsplSHf5PRnA0hykvjZ6jRUFexfGA/n4GuBEoPiRo447e4Zi3rfCWQhMyL\nnrKIv/FtjzprStwYR/YX6dlNs3hQ3LnkPgVXtxiRpEeKJvLiZ8H4Jx+r57mw\nR7VmG5UsSayAV6K9K7LhnR/4nPi74NVWvFjhO+ZgSsD8jqRjV9wxR6LhW71Q\nHN+6\r\n=rWyi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./parse": "./parse.js", "./tokenize": "./tokenize.js", "./stringify": "./stringify.js"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "7.15.1", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "16.3.0", "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss_4.0.0_1623650151449_0.9584451023609482", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "sugarss", "version": "4.0.1", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "sugarss@4.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/postcss/sugarss#readme", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "dist": {"shasum": "128a783ed71ee0fc3b489ce1f7d5a89bc1e24383", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-4.0.1.tgz", "fileCount": 31, "integrity": "sha512-WCjS5NfuVJjkQzK10s8WOBY+hhDxxNt/N6ZaGwxFZ+wN3/lKKFSaaKUNecULcTTvE4urLcKaZFQD8vO0mOZujw==", "signatures": [{"sig": "MEYCIQDotwuigGZOFf9XtzN9pvEwzTMzoTo7TIUKGhKIkkF8eAIhAMr/JAIbWmuLtYOK7vEuGIgQL2cC9w++AYf/qWQ0WMEx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 356554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxvFjCRA9TVsSAnZWagAApq8P+gI5/eU7/iU4qQd3BHVW\ngpqO2jt2V856r0JxxsuiK8TZWEVeXqB/YLsLTLV/Bvl9IQW0XIVItsinFljy\nwUNnv6WWppw9vNO0oHo3OsHwdp+6g+mSUvfJgPash8L89k8HZDPIAerAiM8g\nkp7iEtJERK8nGfJt5Mi8J0oZs28DXCSpmu6pSmw6AWzUY3u5B0hDi36zHL/L\nVtIN1IBF+2Vqn7LuSBSIVIVh6fbNfXyaaglK8cvY4tOj1WfByDOHg3pLZkK1\nXQt8EsUkYVMdZrRNZnP7WpOTwHKu8oHnTNIbmGbU0M+O/R8W7y+7kAloK7kX\nhi8FRXDC9fRh+0hPMJcZJwiMYvv5bKEON4xRd3VKEcV6dnR6QuSsw3nSDEYP\nGNBwro21Re4eU601KCKDFZxjZLXpxDjg2LeZi4KKcc0M761SQpW5Be+SGHTW\nQCeQqFpy1AlizsWLkJQpWLvnxBy459ksT5ztki7pb/TPoPjKiTHW3DK54UoK\nhsOai0NEtVWzmh6uqT3no1cwJfrwGogvvEjB2a2eKLUvHmDGjwyDmDMR1XKn\nFCCkUF+Yy/QM/4T3htM5SJRPxli2n6hIFWUkY0MFWH292aBNyGJNsAc9Z4Ac\nFReR8HPzqnJT+5pePD3kpy0UtfBOfBAntpCAYE+VXvSHKaJWLyA5xF4BfSZ2\n1mco\r\n=zGaa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./parse": "./parse.js", "./tokenize": "./tokenize.js", "./stringify": "./stringify.js", "./package.json": "./package.json"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/postcss/sugarss.git", "type": "git"}, "_npmVersion": "7.15.1", "description": "Indent-based CSS syntax for PostCSS", "directories": {}, "_nodeVersion": "16.3.0", "_hasShrinkwrap": false, "peerDependencies": {"postcss": "^8.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/sugarss_4.0.1_1623650659682_0.45044453889318103", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "sugarss", "version": "5.0.0", "description": "Indent-based CSS syntax for PostCSS", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "engines": {"node": ">=18.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./parse": "./parse.js", "./stringify": "./stringify.js", "./tokenize": "./tokenize.js", "./package.json": "./package.json"}, "peerDependencies": {"postcss": "^8.3.3"}, "_id": "sugarss@5.0.0", "gitHead": "0b7a62c59d4c9e2e37a4043437dd13c97ab8ed9e", "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "homepage": "https://github.com/postcss/sugarss#readme", "_nodeVersion": "22.11.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-3//knMoF9btXcxHTbMRckIYjkEzSZ6pZjiaZ3wM6OIpUtQ06Uwqc0XgAr6jf+U74cLLTV/BEgmHWoeXPC+NhdQ==", "shasum": "a97ddc1b5a1598ba283a10b8d73da56a3848fe36", "tarball": "https://registry.npmjs.org/sugarss/-/sugarss-5.0.0.tgz", "fileCount": 13, "unpackedSize": 96559, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDFLQpFWrxfIu2drDx3MuHwCVpgCvPvNlK1QFstAMRqJAiEAxWVZ2x6st2BFpb8BNiAgnSVzwq71w3eMMJVKZtxqT4Y="}]}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sugarss_5.0.0_1732099078536_0.6054581045290888"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-03-08T08:24:12.388Z", "modified": "2024-11-20T10:37:58.878Z", "0.0.1": "2016-03-08T08:24:12.388Z", "0.1.0": "2016-03-09T00:36:12.200Z", "0.1.1": "2016-03-15T16:34:12.909Z", "0.1.2": "2016-03-17T06:16:17.826Z", "0.1.3": "2016-04-23T07:46:11.580Z", "0.1.4": "2016-05-30T16:40:30.399Z", "0.1.5": "2016-07-23T12:56:27.758Z", "0.1.6": "2016-09-08T03:13:41.695Z", "0.2.0": "2016-10-07T15:02:10.426Z", "1.0.0": "2017-05-07T10:50:11.416Z", "1.0.1": "2017-11-05T18:43:26.608Z", "2.0.0": "2018-08-11T03:56:19.490Z", "3.0.0": "2020-09-15T17:05:24.349Z", "3.0.1": "2020-09-27T00:31:11.781Z", "3.0.2": "2020-11-05T16:14:28.667Z", "3.0.3": "2020-11-05T16:40:46.495Z", "4.0.0": "2021-06-14T05:55:51.569Z", "4.0.1": "2021-06-14T06:04:19.874Z", "5.0.0": "2024-11-20T10:37:58.702Z"}, "bugs": {"url": "https://github.com/postcss/sugarss/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/postcss/sugarss#readme", "keywords": ["css", "postcss", "postcss-syntax", "syntax", "indent", "parser"], "repository": {"type": "git", "url": "git+https://github.com/postcss/sugarss.git"}, "description": "Indent-based CSS syntax for PostCSS", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "readme": "# SugarSS\n\n<img align=\"right\" width=\"120\" height=\"155\"\n     title=\"<PERSON><PERSON> logo by <PERSON>\"\n     src=\"http://postcss.github.io/sugarss/logo.svg\">\n\nIndent-based CSS syntax for [PostCSS].\n\n```sass\na\n  color: blue\n\n.multiline,\n.selector\n  box-shadow: 1px 0 9px rgba(0, 0, 0, .4),\n              1px 0 3px rgba(0, 0, 0, .6)\n\n// Mobile\n@media (max-width: 400px)\n  .body\n    padding: 0 10px\n```\n\nAs any PostCSS custom syntax, SugarSS has source map, [stylelint]\nand [postcss-sorting] support out-of-box.\n\nIt was designed to be used with [postcss-simple-vars] and [postcss-nested].\nBut you can use it with any PostCSS plugins\nor use it without any PostCSS plugins.\nWith [postcss-mixins] you can use `@mixin` syntax as in Sass.\n\n<a href=\"https://evilmartians.com/?utm_source=sugarss\">\n  <img src=\"https://evilmartians.com/badges/sponsored-by-evil-martians.svg\"\n       alt=\"Sponsored by Evil Martians\" width=\"236\" height=\"54\">\n</a>\n\n[postcss-mixins]:              https://github.com/postcss/postcss-mixins\n[postcss-nested]:              https://github.com/postcss/postcss-nested\n[postcss-simple-vars]:         https://github.com/postcss/postcss-simple-vars\n[postcss-sorting]:             https://github.com/hudochenkov/postcss-sorting\n[stylelint]:                   http://stylelint.io/\n[PostCSS]:                     https://github.com/postcss/postcss\n\n\n## Docs\nRead full docs **[here](https://github.com/postcss/sugarss#readme)**.\n", "readmeFilename": "README.md", "users": {"fpigeon": true, "ungurys": true, "jmsherry": true, "ridermansb": true, "spacerockzero": true}}