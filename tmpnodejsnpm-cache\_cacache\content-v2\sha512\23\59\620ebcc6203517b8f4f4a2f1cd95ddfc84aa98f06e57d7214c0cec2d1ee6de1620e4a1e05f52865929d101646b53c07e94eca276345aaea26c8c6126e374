{"_id": "laravel-vite-plugin", "_rev": "41-d97ea82ab9be3a6d870fcb5e31c9eb42", "name": "laravel-vite-plugin", "dist-tags": {"latest": "1.3.0"}, "versions": {"0.0.1": {"name": "laravel-vite-plugin", "version": "0.0.1", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.0.1", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "f7e0166fbec40610a6b013de3d1e627fa22abb08", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.0.1.tgz", "fileCount": 12, "integrity": "sha512-SY8zMqrAYabAhU/UPX1MNMEHov4VidET56qA4gDvky4q1xZHIEGTaiEBsRZbixySE9ncxAtMQUZwx7UQl8+dVQ==", "signatures": [{"sig": "MEQCIEzJFxqlNm/g7e066DMyhrJexApq6Z9NrhGIK7kve6eFAiBTnL8DMOCRlgeoAkhobpblRAXHVKlc7gtwwflV29mBiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihlwGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeyQ/8DJ41U9zV9vnu9mJGkuTbukPkeo55MztfUInigqaMFw2DFMVs\r\nbL7ui1ok+LAEyM+n5Kt9x/GqAiWUXt+3gWKdZn3POAQ0NXow3xb8iag0hzaB\r\natniCXIXw3z5rUwbLrZkF6YK77aeyPbwRfah+TLqrBdZyawUunRgrlzQ9dgw\r\nDk+jzYVqSPGWBxzFYBPvwlsAMCpM5gf/DSLgEittIrZdj+X685Pa15fEtr5K\r\n6ITKsER2ECHL+yCLMY7PXxpS5WWZKsLa4JrWn/weBxLxydDgXTOKJywyRkBz\r\nu1v93n1soFgdG9t+MQ50BwOShmYCM53B0R85WvmVg2TDEdDd5cftYSvEBqNC\r\nNpLK0ByeBhGpbK69UhQRLkMQnNG7q7MMXYRS6yuDIH5oJguQ3WowzDVPsGbR\r\ncQ/KgZou5c9nrGEdYWJAhl42nfRa2p53QbjqcWwnkBNaYChF67RQMWmw4HGD\r\nGtuH0Akzz4W6y8trVrHEv91NjUnEeLVM2J6fXGWO4cPzDe37D7tXfK1pCQEm\r\n5AZIZFoxOw4G2QyYeEXLlti0HLlKPpolDzgDlE083bufTSW6UVV0H1i2MGGG\r\nn94CqsH4YCrG4m1R35IhkVjTYxwiU3SLgy1Yjy6X4tTGG1fLnTZBveh295KF\r\nZYeNr5F6CqeAAjVe2kQl61AjH61tfoou/Cc=\r\n=zqgX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "4199fcd498f062ca8a640cf62f6493941feb9816", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "tsc"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.0.1_1652972550144_0.7393197320060376", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "laravel-vite-plugin", "version": "0.1.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.1.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "3aecc8df428bc4cbddf3944d9420fb0864c911a0", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.1.0.tgz", "fileCount": 29, "integrity": "sha512-2BU9ysFE5v+6g/3suI25hFdmYZlNVy2wSnAghzatL//i1+MUGeqjplQONN9xbx3W4M/aFwIcm0zrzAokDZoFZQ==", "signatures": [{"sig": "MEQCIDpXjw4AqmJlTLYAJdJ/kj/c+yGhbCfUo9qbQmX79SJLAiASuOVr/QS0jRJ3jblnSCUjvRp5u0sUV2M6Q5wWD+iheg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikXCJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5hxAAnGHqYvjzDxAZl1qLVQQNalmOXq7u3ZbGRlC0KSjsi2lHEpXG\r\nMqvn76bpvftM+l15n13ncoO9yHWDQknr6JLT0ivogPXD6Mh6rQ7QBRTXpds5\r\nfNdNkUYF0WRz9x7Fxw1a6SAC+bDFSoqtDM/WwheF/nwyO2FytTkxMm/iWh8O\r\nbTXJf68lb4rrEDWE6yH27OdQZteldzn36Yo8FrHqOAFhKTmpaYNG6qiI2MWX\r\nJLYQF+U/2hObkJ8RdAr1OjO7JDeTbaSyr4upZRIj1D2QfMh0OnqKqNA+mxBH\r\nGYsSwiPwi2k2sObJ8Vgk8EYF961IPXCl6l/uSf5tY5PkGSDbrswUSI0zjXVc\r\ng8oRz49o2ALOp/0jh/OVdq08YEnGwdgbE6ICZiD36D37cyA/m4ojTAm3RHx+\r\nGDz0vhJcRvMKCqpoLW/tvWs1EiTSuTR39n4H9GdGMurN7mwW4HTT6sUhy2i5\r\nin0oz5IuBU5ErIb6mL/TCt4Nfy5/SVr9/dK7lOQWTzW2JhkyHAifQiwR+fxi\r\n2YMW4QmfbLtvxJXwKaOAzWU1813sNojQ2f2NjK8v7ZDBpypt6fTaZ8y5mGP9\r\nbEUkBtPjqYv23s5dKZA8INHWLuQzARWAbZwnK27ndNyzkhoPZ4uUw5JjVAT6\r\nDkj1ZVAChkdNEnCMPthFrJpKvcz53ffhQ+g=\r\n=a5CY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "e3e356728daca33c2208ccc1a4198fbc008e4ab1", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.1.0_1653698697422_0.98934038792866", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "laravel-vite-plugin", "version": "0.1.1", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.1.1", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "840df496c8f03d92385fc88cb34bef561eb2e73b", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.1.1.tgz", "fileCount": 4, "integrity": "sha512-CPSrJgwf1PzMjE4Dg2JmjW+0TSmJNVG4uNLZcCHEWJpiFLKwxq3Os75fHVH7Yp2dJs5T0wVsNx7kKeCGJ+pyMw==", "signatures": [{"sig": "MEQCIAEJy6tUPUjvh/hItUYKM8PcMARLedy5lpc64/Xn+EfhAiB1xpXMvHWnA+fnW7yeD8o7j9W4wHjzL9fkSGNXWE5A9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11769, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3/VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxshAAmZXNwQSHl4kFEwfqO3asA1TktZ4ewEUoKzMZelLI5/Z69pSy\r\nBsVGAcL+UMCRmHIxbBqk5V62lld11Hc6cpLbYJx3lSUhJSV7KxCdzZ5Q7sOZ\r\nYhQRmH9kHQcustp8X6vR64nnwdD6Y7xzrYyC5Wrb5QAsBVvjCx8vZ7ON1EKO\r\nkX+P5fVW/0GwlZIyOYV2Ez7i6LfrgX1txToYDIUTuMSeyPD89Kq7rFe6+LJ3\r\nnaWqcy9Bfo18RAVjSkhQXiPolBTnXTonBVgxgke2S/u/xoTy49nl1irkq8br\r\nd03gCTaL3KR3jxY2A9lHxktBz0rEtH3IS++tCx/aPBNbyzHVaZZcjrkeGFlH\r\nPod6i+AbYwXpx055/UoeMBXv3oknJR6yhYPIKvxHFtzT1a7NpNwSH0YI+KT8\r\nxttiJOB78dRKCYoL5oD57DZPYNTsRKWSNhIgnx3ECN6XUPG+OtXoyiyETevu\r\nqDO0BNLwxQRXJm73i+zYOIGND88PPmusacI7/EzsT5EixjyD4MxY2fmDETps\r\nvWZfG68boMGPmfQAR2eVLfkJSqEFco9NM1Vz+2BRV2dA4ON4o0qAEL6SSXHO\r\n4ko1XM7O80eFtTzu434B64fv9Ikfn+jFeiveI4iR4uADXSYcL072dQqD7YOM\r\nZzT+zY/EbgRm1z/Omdq+fFVBnyW9EoPofpU=\r\n=lZv3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "d68fe2495d53bde1aba241dafb10a4156667f41e", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "ssr:serve": "vite build --ssr && node storage/ssr/ssr.js", "build-plugin": "rm -rf dist && tsc", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^2.9.9"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.1.1_1654095829181_0.5460765676094741", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "laravel-vite-plugin", "version": "0.1.2", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.1.2", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "6fc63b1f58c03c4e2ee5cca91e13e9f96072a68b", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.1.2.tgz", "fileCount": 7, "integrity": "sha512-6Znk7VqZ5Y3gLVa4XWF5+oKuFSxqa42JCBa1Xf/xEY6alVlA3oDRbGNLNZH9YqPCQ7taoYaeKrG1JNK3lasoug==", "signatures": [{"sig": "MEQCIDLEO9xGDlNqFkaB3xDB07qE4G15W1Ep7gxtY52uyWKIAiAmYyr5JRRFm4z514vAA230Gdv9bF/+9qOBgDONUmSfjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimCFCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5iw/8CJ0JnqqNyHPErD4AlSt0ObPpdzTZ4RTIQSPCFVRXdFsM2Y2m\r\nOEBHM5XZPz+hs4Fv+hDluIsB+3zcGbuRTpCkFg+TZQg2GjYOpSyGRFducWuz\r\nqVeOP0s03uilDSGaeCx8+nDiVI7ih2IeMHm0QopTDhJTL4vrzKPSPey5Ozaj\r\nUlQ0i+PdrOIcLIf0L99TyiAfKi316Lyj197q2sInwMzw/9eDGhxr/UzcePpJ\r\nJ3zksM+7n8b17TAtet91NUdclA1tL/ALcD94gLNY5p9UsywWn/YV8qwqhKBG\r\nAAZqM60YA/xA0e96EVfVxy6wHR4/4H6hit9St4WT0CTWyLFqtCANShuU/11s\r\npBb+G5HAtwrWv/00lrptxtg3zkJiK4+EFFcZ6QB/u7DevWoTrK3b1pD3Yplx\r\n07y7qcEqSaYIgXMVO+4Ruv9x9PE8YiK2vdjZJ0APFH/r3x/UlUYvC6SAVWpv\r\nXZW/DvZ3QHzU2qiM1PDToTx77uAcW1ko8rq99Ae6RHstHKcQ17oi1QMyH69L\r\ne6pn9vtfaOXv/rdo0n6eeEYgYqoRTKj7T1A0ugBwaiHiho+luZIOds7HUHCT\r\ncfzUeLuqroqZtCIoZv/AZmdj/b1CmM8z4PqvIg/tva/rmdOMqxgQ+uLjAbiR\r\nkKF2owWlC4uyLwPwDLo63JOdNSTmVc9x5dM=\r\n=EP11\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "6fe739caee596ba906e062ea61dce5c2e18d477f", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "ssr:serve": "vite build --ssr && node storage/ssr/ssr.js", "build-plugin": "rm -rf dist && tsc", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^2.9.9"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.1.2_1654137153833_0.6467233053492176", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "laravel-vite-plugin", "version": "0.1.3", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.1.3", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "d0353bccf243f2b9206c63e2adfcecb0500ac79e", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.1.3.tgz", "fileCount": 7, "integrity": "sha512-9iOMFtrQBRvs9T0WSfIPNtnInZoowbgDmHrONXj5vw1CvzKVr+/0BknnJE0rsJJcdcAJK5nSvlxR6urwcNVRxA==", "signatures": [{"sig": "MEUCIAs6xqZiBrxb642+GpAtQJgQTZoQGZsF0IzSLB7AzVyeAiEAwroI3DgCSJ3GL3vXdYJRe4G3a8kNbpjEFfZcwb4RaPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio4bFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNsA//dFzL8uppJ3sKPMXkdoYiN2k7xMo8QVWWYMjfLZfCSLF8QyO8\r\nf4BxXchwqGOKk2Fd9fjqnckBf+FCHEF+T3ajioxZfoZvh/U9MtdleFnsSSwI\r\nnFgl8TK/c2DH7WBAxMkXxHzpX7JyVFl1EiRtnkIxlSTf0dt36ay1FEZBV+/j\r\nKaAK5am59H8854wsIWze3tsa6Qvtr1hJQgnBtFHw6/oyW0QVnyKh0mUgWPB+\r\nBCh4FxHGx9K82BiglQ9+/NU4HULdmrRcH6yWawnWdh8mmoVAgXLh8qwXMpA5\r\n1+4eeBi1+FpkRi53gcXd5jUt5Yf6P6iIgL52bGJkOwkTxf8DZ60zVP73ESe0\r\n3gTNKsKb+pn039bQwAs9cCaS5ua1MvnrFTefwzmsAiTQEA9QzU1T76PNLODs\r\ne1z3E0tR+8uCQLnDm6Eymhc5GptdVXdAcJjkyWpnjd0ClsaOR2Lu9Glzdlhc\r\niVRzkLoRgP0QuwTM7hEP81OU+Jr/ifZ0+4zp1ri52H0EX/dCvTeU2Cz3BzDs\r\nES4gxChX7yQQ+E1I41LQkcbpP7hVqBTYJbGwaE8aylgpNIbikt+oEKn5PCys\r\nFvp5Lj+mbFz9O0w24zpXQElFSCIrjhOxJ9maECR8rqpTlLHeXgj93O3x0ajX\r\naoKepvuBFNnGKFSWyL0tm7Wxc9fLfyMcLkw=\r\n=V/K5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "d8860eb349b926a2dcaa8c65465c740e4d58d174", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "ssr:serve": "vite build --ssr && node storage/ssr/ssr.js", "build-plugin": "rm -rf dist && tsc", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^2.9.9"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.1.3_1654884037251_0.9850360911658509", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "laravel-vite-plugin", "version": "0.2.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.2.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "203dd67794091246372f43247aee1a497f1a7416", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.2.0.tgz", "fileCount": 7, "integrity": "sha512-nNGT8pXICjTPSVP0tmQNuG3btHbRsT8ATTWV+EhGEIJELUvgkg4SuGjNWvDqw+3xleupro1jM8/i8v1lbHZjgQ==", "signatures": [{"sig": "MEUCIHggh0aVvAoCQu6DLIl/A85NDkvoyO60laVigWdYK6uSAiEA/Atg3TYY+oN0/iz9CHApoXFnZsULkCwyJZ4R16zPqao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJip/GuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHmA/+Kdka6pgbtzWksuvnvfZpX9m2Qz17hte7z7Rj90modNF29cgo\r\nQaXZ/vl1Jrkb0VLFS8eoY19acUO1HpoyUtnp5LVNA753hoPHZXEc+Q0q1INV\r\nQo2uBXaBBmcXudBXvvlT92qiVATdIh4irrWHOMdkJYDZLoGltpM4Llkb6P0g\r\ndq25mRwV1O5sqgknp37LhK3z7buoGDMtvvMzGMa3vnitWdutNDc2Yp66/VLt\r\nSwDNjaU09VvMqjUGqXfPODiftd7fdXyiqqgzz7lDrSo3O4mEYF8Mnh/d5yG4\r\nxAmlW5BeRea/c7anSMtqciKDsZCCJff9AJoCcgCoJ9cY0h+j5YnCA/4Dub6x\r\naeLvWMVTD7gSYE95DnJ8Lkc/CCa7B236mDbPudZcwUVkfO1BG6mZOHG69EMi\r\nEIK5vTEBgKlNpj9zA+0O1mseyDH51GYHzTdIgVJtHgVQPpkEiyCVzP+O5JhK\r\nJ+VcGyZP8g29A0v+ENPjvUjTs852XRg0WJHgiOjhoS/z4rZR1TviCFptPHNA\r\nsn9z6P8C+Q0+M7oqWE3jBISf2RjjrRu8viTiGTim0QE6jV0SbIOrhALd8plE\r\nodEkPTxEv6LOJ1KC7W7c+AmXEnTKLBz1PJbOdBz54aImWN6g5sb5f6W6lCCn\r\nePzZk9VfB12UOg2hBMszufEoVzFV23F30/I=\r\n=F7tY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "3e4dbd2052edbc9aeec724aaf34dcb41f8b91783", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "ssr:serve": "vite build --ssr && node storage/ssr/ssr.js", "build-plugin": "rm -rf dist && tsc", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^2.9.9"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.2.0_1655173550613_0.13232321615019038", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "laravel-vite-plugin", "version": "0.2.1", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.2.1", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "cda3a3927ee66d32f7af066501568859dc441d4e", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.2.1.tgz", "fileCount": 7, "integrity": "sha512-f5q0hVCc5KN39qBqPVGA4C6TOvVdk3/MbQ/VSSC/V4W6VE/lsR7NsoxCPUnAkXOwuKRBFnxPPp4FekYH+tfuRQ==", "signatures": [{"sig": "MEUCIGlXlw8OC6/T0i+p9RyiQAaCeiZHNh0pePhHSXjV143VAiEAngETj8wX3+ClRplgSH2B/7mxcHVZj5J7HPoAKl+WE7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqpFSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrN9BAAgVUKlQCDwpFmLYGj76x1I/xKexcdcRyG/428mO6Es8ogLCvS\r\nHjlzVJmxk1G2FqLsWaDhSpwD012IjJ2l8F4YVn9Oyvfpq6q7K1g0qHm7fv7i\r\nQUaZevFXs5NEHzbhDCMrLmmrMKjpeUqCT/cjY4FZfSB7MxLj/9DZ8P9+A376\r\nDM0NbFbzOzJgiW6WNWEAvMFMc4N780TJFn0wEOWGKEYzuqsT5y1fmn4SWenn\r\n6RLYnfe9rY7lOFFY0DJ7YBlv8enREDV1XNVAIIkNmV8VVZ3YQaVrkczuxVPt\r\nu4AKLz5vKko1JbA0bgm4gtcF8TQNsXtQMtdyrK942K7XMJL3ifSymdg2WrAR\r\nsRDE9FRCYFn3haT34ttXfPA1wyugSRwTVLPUofYI3k6NN7hbtuibhMusIdH4\r\nly6pNNJd7sq9YbHG0q14eOxKg4ozcheYkNIBeWWG/UV7L9PTuZV283SXpN2y\r\nIOiAbE1iZndFGQh8KJoVBXtGB7J7QjFaNaozm6in6ub1ykGiOk93GwARuKwD\r\nJJ+rQlh6J1pYVjVzA2EbAklZ1VEqF3tCzIYIYz6jjHCkyeo6V8kXXBCcDXi1\r\nP2Z9wswNWIOORtUEwO/zIeUgU7+HLNq2U6WJeZwX3WsqRcyo417GElVn9HvM\r\nw4qvA7OdQYi6p4VqCah7EiMOcDPMJ3Hp88A=\r\n=AdEE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "c41269239bf8c2c890e63f0d93c60c28e5d9f177", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "ssr:serve": "vite build --ssr && node storage/ssr/ssr.js", "build-plugin": "rm -rf dist && tsc", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^2.9.9"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.2.1_1655345489841_0.15099946620010685", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "laravel-vite-plugin", "version": "0.2.2", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.2.2", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "c517c98b82fd0b6e269f0c8cf8038063c6501f45", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.2.2.tgz", "fileCount": 7, "integrity": "sha512-73R008Zj1qku9hdbHwf6V91IG9zKBpMJAhOEbr991y7c3saFjChtCapguFFqq5qvTD7zLJ2R2WifA1oBzH8bdw==", "signatures": [{"sig": "MEQCIAqdxLUwzVXfOmeCKOuEQS6X4t4wWRhCeRZHC958W86AAiBnGwqkfWqANrpd+O1vuJsOpK3XEAocM9g7sE1DYMRnDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirhnfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPmw/9Hj6MHpM23A32K6Ud0V/zQLaUgrnrO92/hQz1tLgWBVKG+/jG\r\nZyEoG8qNsFDxBr71BryiGWDMwA7IQh8j4jY9Dk0MXthSonbx4PtZmLazFhyN\r\nbUkux0GV+ht6z09PnzgfOBa2K+zf8ZcTwSD6Wn9Ooy9v5U0h1g6mnv02f8as\r\nX0ScKchmzYzKlVctS44VhzAegOnnpqrHQI0GZDwF39YyFwEU4iChk7ian3Hw\r\n88cQQKHQlDNf/5I4fHwwLkMqBUj1DNJ1byfeaYJ7eM5QswcyIACbRpFNBzKf\r\nI8PQbICRwZtiBFZ3lqatD1nh4J0u7VkabYYJffi9FtEt9cu9RuYstYWchA8g\r\nGnPGzHN5igpRPQm37StcfC4F0997FzqQNjgifCdOyjzg+iIkuTNcc4TetQ5G\r\nVyVGGqQE5Ke+S3cV0xvc65f+Igl9QMmwq8m4qPd2kzo1HkwA+/dU4Eaxpxcu\r\nepwNmBCYPGt63a0D7AhHfmoHLmXvD9dyiylZ8KIEQYgwU79xzUjldDo3p0qs\r\nMmDRhE9dJM+/wsNVQ4sT9RqiZSt4Bfjjvmw7pu0B4F8faOVzL/dDSjs+FSAQ\r\n2t1RejKlKERgzq2VQABstk8Tv6d3LlD+O3kHbKud6TPmdTZZJR3iOJAVWqVB\r\nQUEnnm+SjJxDHvVP+OMAR9G9I9mWopSCCUs=\r\n=q+Qh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "aaffee11c56770a7e9060eedb61ec626a9d8f562", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "ssr:serve": "vite build --ssr && node storage/ssr/ssr.js", "build-plugin": "rm -rf dist && tsc", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^2.9.9"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.2.2_1655577055109_0.7864735885846927", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "laravel-vite-plugin", "version": "0.2.3", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.2.3", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "bab2959bedb7c5e0bfb43d7bbd0661fe85645ef0", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.2.3.tgz", "fileCount": 7, "integrity": "sha512-VUy50TZFgOuc9+d6jBTjOUY3H2UEynu25h2ALWI0p+fl3yB88ZoxJUluamSD9SOE/4zQSI7ID8lFN/K4/Q1X1A==", "signatures": [{"sig": "MEQCICTWYPhR12gk+mqikTLUKsngrrS6ZjfadJpgF93XmVMiAiAdXDlElvQN9bmNaz1ylJoPXmgCt0brRRUUbYdknsO3kQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitfPcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqssw/8CVCNxyoFqljOitFrr3gO4tnTi8d91ddd8WZcqMnH4Zk4gnCn\r\n8ppt3AUua0P9cIBVcrZZJy3qu9f1pfilKwhbu5fWQRi1aO4ZwynFzNGHtqw6\r\n+dZdZjEsL6pOkSXmKrmhHAzC5qYoIH+bt1b9BfDtIml7hiW0e5Ol6z0Za3G/\r\nsF++xwPQnUkz4tIiCq1kI7+tQERmtJ10biYKUDJaqImAhffucz3G50oc9c6f\r\nP5Yhnl0S2B3QqJMT1KR22R7iGOcaA9faWfUZ1oL0d0hHhZtKBfNWRp9jBVwy\r\nCQSZCveDcrlGJWDh9w2hinR1c3WbduDxnlEA+q2ZC/QerxKg7lIbFzNhl3cC\r\nK8/m6OISa7jL0bOWPTjwPV/Q6/g/6DutsEmZLCl6MSjRSpaqVFFJteuWmKe8\r\n0wcodu+9UlsoAQlORiYDpzlchn5A9cZVvpOfvre83ZP5mzHEKS2gqtkCnmUh\r\nRPW/FDy+Y5NWYzvd62ydLI0OcapEZe16HBT6ZMaznHG2a42K98VkKPFXTaBi\r\naKfd9cl0UXEmTfLM2c8M2F/qP/tYZhVKh2LbEOZvgmw7Gs4jPe0nW90Hk591\r\nPGunA8V87JQQqFo0DZ94Eeza49oBXPrfiQEe+jvRzN0LmN2oiifh51USwWTw\r\nDM+XmyFhsghn8aLc3VTnVv7pmNaVHnOvs4E=\r\n=l6FP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "accb5bdad59444e551d31db45d3ee1bcfca38d59", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "ssr:serve": "vite build --ssr && node storage/ssr/ssr.js", "build-plugin": "rm -rf dist && tsc", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^2.9.9"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.2.3_1656091611777_0.49113372559670654", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "laravel-vite-plugin", "version": "0.2.4", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.2.4", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "66f4eeca83b236ca5dada7784bc56ff19730d84f", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.2.4.tgz", "fileCount": 7, "integrity": "sha512-tb5hZ3aZ/bjvQUAkAYSrq7b64ZyjTY/joegVzrMoV6xGZUGTHyglcNkaa2G7Wtu1j0hLIO64ETeryxWbScSfYA==", "signatures": [{"sig": "MEYCIQDq3/C+qKUYKQjl3TakqV+PZdA60Hd4l4Vj7li/xAA+SAIhAJo54QdhsKRV2jI46j7anm/sa3DF9TkW4UWEWWHI5PBd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivFbgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmph+g//REjr9ZqX9Cn+GccFhZlOhtTJMrqjczI/P7X2RQkCy4oGd/og\r\nXVGEZY/VDQUm+0EpkuaLl0wXALa5JykJMy8GVceFdf/UeAYOGyr4DeYEqj21\r\n25a0SHlhOoYDb9H0Z38DHjJpg5+Vb3/eFYxjItR9LSuvimYgEm1x5DwkWv/J\r\nXaqgDEq0VWzHX69DWZBwmxBS040VnN2ETmzwLnxHTQWkzdCuBGg2JqwokGfl\r\n+vqq4vDqne8nn4TKEkN31fs2x7wW3rUYaYyKEO3ezepnhDnMbMpMraQw3cQ6\r\n4xNkZV3OBEsZnt3hc66z7yY4zQC7Mwt5SevJ7pc5S1c57H+oZLH+ZPjpOL4a\r\n6wZ9eCMX7XiiFNBvCThpWY62wvpE+WGSFbI613CbniCm5shWsc0JVZFikr/O\r\nFCHkUniPiDL0PB3imGSxBybbBsi2SWfcGEwBifFZZt472Q8e1o8tZ7vECCTe\r\n0Q1Rsz03lAy8NasQmj3sniCiPnbREQX7G6bN8JBrtOh89jOkaWMZYvRvDt+U\r\nALd9jytru1hQ4ALISgIvndmWCvf+lChVexbLENA7jqh3wao2i7ffPvlmSMWQ\r\nYf0jNN/piEp1JKPROpf+4H9bgXihWqUp7yg+MptmobdkL8xTOgi8ZLVwWiXW\r\nR+qAWWgexGKL7nCgkTNMAu3xtCZ68tB3okI=\r\n=Xjm4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "fa1bf8a9eb1df5c1cfd33a9229d0bf904589e59c", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "ssr:serve": "vite build --ssr && node storage/ssr/ssr.js", "build-plugin": "rm -rf dist && tsc", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^2.9.9"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.2.4_1656510176376_0.6975202621345409", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "laravel-vite-plugin", "version": "0.3.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.3.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "3dbf0c3a3e08f9551eb564052f5238252a0e81d6", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.3.0.tgz", "fileCount": 7, "integrity": "sha512-lLtDt6XhGY7D094jubnNmyQnscVwv+w4ol24yMhglu6PHiICSzRv4v7gBUYYwFiE1coeBooNcJ0bFbdbcS3Izg==", "signatures": [{"sig": "MEUCIDjxiv+DI79To0iodGFurJyJ41lWE07Sw336kmO3sEuvAiEAnBM6tEIlwNsK3lgzZFLPQ6KGeYMInC7YQlacTgTLrpg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixGgmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoh3w/6A6B8XQ0eG5CK5+EasGd1SSL8JietcEa5FoLN4Nf0ngvhauIx\r\n9udseXaqOI3Jwamsh/8y2/4EN5Ggjv/mtU0GwCVcNe5Yhd+L1S4LaY3RPYrh\r\nTJCqSUSqS1HuDnzInWhzoSLRoLMOTKLtKM4FBYpDRT6ds6OqJDYijum+m4nU\r\nzwGIi7h8oipOYRaafRM7G4s7uj8HwX6hwDVykLNpECr6mFMAFWbaQZh934pb\r\nGEJnitL02okRYj1Bw+/2CDJFrJU9E1XNPUB93KziqTGRud8AMu5OOu1bse+l\r\necahC2aiI4VrrhQ9P42j7NSHWknNKg+jACfX+9E9lgSqATI/4tgOYq2qGbJ5\r\nAVkV/gqR1n0X1YHftf7x7sz3gROD8Na7SysMYAjS3SCb4jMvrr/TiDYzz5rM\r\n1uScnwvyqjFxF4Wdsq1O6nmUy3Xvt5gTanPGCMiGIBz+yNTBynkWI3MSCNBJ\r\n31u4GrL+/vwkpsviWX8AoRcxfDvKHmaU9lOUDfpNSzReebuOs0yVgcnTQDEt\r\nti+kF7964euRqPAs0lqsRnmt5DwVZF98/svytfxWVwaAwXlrq21VhNohZjNf\r\ned3RaO1Tsi9642AF6HJg3EG8OCWx6fArdSFK+1rRywOikMmNYBNCXf5rYliM\r\n0tFRiRhIO4EaseBRM3rLl48V9kdEma7JC8c=\r\n=9lTc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "f3908eb9038d26c5771da369d3c96693c5ef689d", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "ssr:serve": "vite build --ssr && node storage/ssr/ssr.js", "build-plugin": "rm -rf dist && tsc", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^2.9.9"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.3.0_1657038886720_0.07388205338020182", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "laravel-vite-plugin", "version": "0.4.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.4.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "7c0c342f9c51f648e66944dfa03ef69f5feb8af4", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.4.0.tgz", "fileCount": 8, "integrity": "sha512-ZAKCkRnMUnSt4+ZlZQEspcoBtUYRz48Duho9SMoUpwxyLsL7TswvNz35Vi3vOTz4B8Y6U88ZY9WwBiOHGyJByQ==", "signatures": [{"sig": "MEYCIQD+MilpBit4eS7bKN8eaUrtGLHX7JOg2dDSZRC4Cg9PLgIhAJKiGe+nc3it4+hchMt6Q+DXcYJOHkjaFskc3wAdgufW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizs4CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphnRAAjPVZI/W16oh2dblkfQp0FnfUQjK6lMlLw72xdz5hRr2uebNm\r\ndLxxN2fjJu5jcEh68q+KRTTaFEI427hZUWLCN731RCDoZU7K5ijW+JL/ncoQ\r\nRoOsvCfH7ANctX8XDlT4c2BJ7jJRlw6HqU/+4e+gLV5dCch3R7MVE4J4eutz\r\nghRajXnj1+CQ59xhmUgKcRPtp7iBAo4PU3l/TJTsJ+fOtf1AB9mfzdJaC8zN\r\nx3BlMy0f0Ar5MjRQs0IotElYa+xvKIfl4lxjl3sljzbb998PZPQDaa+o8V+s\r\n9GUubBfAmRmc6L6nnJXbrhXZsRwVEa68PgCsqVzNWoGEHpe5aveRPJO0wSyQ\r\nLLbZcIM6XooUivnnnWtiFfyw0c7NHuHwU04qUNcRk2m51CeM2E9nvZ0oEAVL\r\nlHKIhVt5V3JC2l7nR7UM+ERCBlUvwV3NkGqQmXnbKRJkuobIjH3maVBPPWL8\r\nVVGT6UxyBc1AeMCazJxKOfeKeHjVkX9RjGkodI+iL0Mzhh76JTjsogJCsyUB\r\n9veFsOYSLLQVzhYnaeB/0EeXzr3ChIgtkiLMINZTXqieF/nzR2jeUufqLQGv\r\nB0TcN9SSm/lZgIwNX3blhQimfJgZdSiy8aNcmoz9FQJZw4lFCbx5Ac0JlYuG\r\nCpGYC1U9ML7mIl0RcXHzNOSoeq92T8DldZs=\r\n=VnIk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "c74a227872806940bb3e039a74c3512537b7cae3", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^2.9.6", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^2.9.9"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.4.0_1657720321968_0.798818959534473", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "laravel-vite-plugin", "version": "0.5.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.5.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "08c215f9bfc1091e9c0d598ecaca5a581789ca1a", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.5.0.tgz", "fileCount": 8, "integrity": "sha512-SDH7uiJqFv3SvMypCLQOzU7hqbO2xmjXIVVD/h7jiyhYkxSQCz14G3Wdrbp6yJIY+szCQAmvhQG339iwohlfhg==", "signatures": [{"sig": "MEUCIBn3rtIckHOdZhOiyqmodkV/35cMUdmkZCcAD0Sol8E2AiEApVzK7j6pV1He6f63xbLDIHTt14fl10hMUeu4Vj5prIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1rmbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCphAAiUYKu1o9/G9DsnISzDrexnSZtm998CUwmfKtk47rwRn3mUNL\r\nfWkmUtCSlo2NvZn0gleKV1DrMZjU68xAIbIYoI5eGehvJ1T2WHqtIKhgbXgd\r\nkaNeZtzfNOczFoO75VWyB8eMMRh28RRAL2OzwcmulT/MuhBkBhMhOf3OgfTO\r\nBYyIMjyXTU0VXxaPCRXrD07lc7LPPQRKGe/LGuv08zXkJ7t6JAAOC3JxznkD\r\nkPcbipHVxBwLmXwtYxD8P12DoYuRyzX/hAdQmZzC3JhlVjF/IU9pWt6866KQ\r\n6/2cbLLWiNKDcRsnDDfZsV12xGYaKH1eQy6eNUuYjGyHCkKI3zq0+IAh0u9w\r\nkSrxpvHTPOSdYttZEzseZgY+N+Gs3WbjxPEBIjKnOOE86LpIXPeg/7lhSFMQ\r\ncovZbBBJ4ZmKKq9g1aQxBd6dh23Thj9FyinnyxajcXgFzPWf//mqJraEY9A4\r\nZa8dDQJ5g1hGlZIhR53FasO+eQ5BupK66Wz9QSDNgqlvzZ7drw+wtsCZVbi1\r\nsbDeIPGJUnlBuev05fuoOtPuEEizx4gEX14eXWHKeSL8sn2s5J+usYFHzRwZ\r\neHVZKif/M3G4GYeY5kLo00NBWrt+DrijqO1nhXKEYG7PFy2oqXBFppiPybYJ\r\n2lweiYsRX21+VJH1NONaP5Kt6ycBhG1Tb4Y=\r\n=SDdJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "a9cc6e90b6bd493769bf2c94818bca1c1bcb856b", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^3.0.0", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.5.0_1658239387516_0.17115412270714803", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "laravel-vite-plugin", "version": "0.5.1", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.5.1", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "91a9bc15f13cc7128d7df5be8bb0ab8dad337fdf", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.5.1.tgz", "fileCount": 8, "integrity": "sha512-ZU11NusQdONGjGcycXNdw/ZL436HXjL3RVbjmcrRhhhSH3TWouHLT+l013JkOVJlZj40wx+N+Ug0HPZ1JWpb5w==", "signatures": [{"sig": "MEQCIDho7esSXiH64muthJ/G4847mc/7g0d25JRwh6CtDL8gAiB/mJjZei4pCEdTSOPj+1TgmhBPUD4+fhIBToQ0wdHxvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2rAfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKpw//ZDJ+9B3EKvjIaTm01LuCekXdXZ6mQJ8xEz0/NLmz0drRtWhO\r\niL+DuBb+2yXXd0gNScy+nhCXljEA095m2a0FebWv/qRQvd9ncI7mz5OmMAyL\r\niS4u2/9rJlY866hrvby/+YzHpbsS8+75ikYQMrkOXcdJIxo+Q04ITJcRxlAF\r\nPcrMyPc+eM2+Ql2KLXRskwrPg8ve5wROIPTl4P/+A5b7mqtotScqnwVH+q2z\r\nhrw8l74xVmDrWouUhIkkvty7iZA0/uWO0RzqxmxEXRQVztze3yWrFUanuW1G\r\n8hZhV2kEMgNV/MIwpssQSb6H6e6MW6szmp8bx5qrLVO6tVsVMBYQyXDAQGOz\r\nLIePY6u3RnhAPxvUSmY3nVYMN9RkRSAAoo5+te5eZkZjBKdo80VfHJxKSoSC\r\nESnfytPjZXifBdqNvMetUIqHMLcxb/SjQGHkUclGvEpKhDdpkrcbzxH7jOuC\r\nJVAzqZkOmF5U3O4duggYAUjK34TIMnhDQZs0/pQoB46um2oazlzyoM2UBHcr\r\nOwBPjOmZTz74EDnkikrPrR/XYJajrpBUYo3m3ktX8YunkixqdOXZLPOHCSjH\r\nAISzBbH9SGYxyNRQNTSTrx1TQO3eTwhqdLBrx45Ahpcu9/ewfpZNrQ1w9Zo5\r\ndEhFik8qq/0fX8YYuO9RuEcCl7KTC430Bx0=\r\n=XAKV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "ccf5868fd1f019326c54d4fab606f96b87a5253a", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^3.0.0", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.5.1_1658499102945_0.4770014304230088", "host": "s3://npm-registry-packages"}}, "0.5.2": {"name": "laravel-vite-plugin", "version": "0.5.2", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.5.2", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "5b16ef189d8eab09ebd468f26d1b618b29f89c2b", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.5.2.tgz", "fileCount": 8, "integrity": "sha512-6ufssMvfF3IK/t1hRiYApTB6MHnpIAEZxte+V89aOt5T4CP8OVCPY5qvFBGwddW+yoVEOH5lnojSAD5YzM5T1w==", "signatures": [{"sig": "MEQCIEdglGJXotLdfxVMwh6QDVLUREa3WMeBe2XEWSpDc4j2AiB5B6j75+BpCyQesPkrGjKzArenee8bszBiZui3wx9t4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2rA2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq23Q/+LP9UlIgHe/TDw7fgRydsGMavaYhOcHEA5+RU/6/I9KcnugOr\r\nn8Oh13JUUNtGfMbnBSbjrXDgnHq0cu8D5C15x1Hl9Sk/40P37yxxpHR1pst9\r\nhIu1AUVqgiwAxwVCdFPO3cWdcgOGowY+6mIm6YoDAl2m8+wRBN9FydCWrDp/\r\nKj2X614zZ9NxDVi76BSAQipuO+VD8ZTOTS6U3ae3FMjkB/PnSrlQfioQcXvn\r\nrn2HzfT8Z1HgYZy0j/OWytcSZ+91lDuI08Ehd1Rc4XN6VwE6uH7mBmT6rFqv\r\nXYNBMLBQcGgogV+4aLi7l9CutoLji3ltnhjRigxLhEWsbQ/w73EBoPwvun/h\r\nYxNJ6y/e9IsJzkTuNkeX/eUbaFbsVpVwEyWNdmWl6IjYxpvdTUlwSj5xPGSb\r\nUCxoVpL5V5H1Y2ua9nPHeBTA+wdxOwxBvqNvCp8pYrNES02YkMO3lEtngUpj\r\nR71LUXEVClT+f9dIs/HvaOVsUlYpqYYaRrwDpKkral3Sysx0Ed4Gc4hp7CLj\r\nJ3dkaoPT+ODSN6Iwo/4EmlLHO7ja7/5LqZMmL/UyhtGfFNpRcoMl1cZ6v/4J\r\nb3zXeizshYbrGOaP+V8iD4oi2BWunUTtoO2DPHPbjMBIbC75Nyjf5x7au0C4\r\nVPi1aGjXJvqSKWWwNERPf3uH/Csu/SN6lLk=\r\n=38hD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "338c2453c470c26c027b3dd6e66db6c34b89abd8", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^3.0.0", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.5.2_1658499125798_0.5495236107482548", "host": "s3://npm-registry-packages"}}, "0.5.3": {"name": "laravel-vite-plugin", "version": "0.5.3", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.5.3", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "66f9689044db084f79da30550b0635d0f9089955", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.5.3.tgz", "fileCount": 8, "integrity": "sha512-7Sujs+ywIYQai7lfpn+KxkRCAz/RWVMO8VryheEyUCKBsEITRyLoml5kudMW/rWJCKhSVVSIF3KkYZ3pDmDZzg==", "signatures": [{"sig": "MEUCIQCGUpQtCDsGmmUX6/9yQCk1gR/nshozzufsfkPo7f52iwIgN6INooAjn+CJvU0fYUgFdcgam7rh2ZG9x95YNhzaTu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi68p8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpwcg//ZP6r9RkksukA6ocWqgfgCttzamfR7sRZCGThvMI8fVBcKfB1\r\nGRg5dGq5XE8MoL2C/Sa0pKp7bks/dbM7QwiGteRcJpYX8z0BPCvWyWGXWU9m\r\n8Aqk+9QM22HIfyFpvOUV6Mh/O/O7HOvqDg4MvXlw73BC2flp5W7cr+7bAsaM\r\nkphaGr2kowAwtvWQsHAE51mxxVg79pyNZPu6B3Z3YRz8RnwL19Kd78xLPT0E\r\nNqkqtktdSU2y2fPhHwyJE0NP0d0eEAWUSyco01+D/QV9PGsAdzTnnm8YqCi0\r\nIqYtXhTfG2VbZEsbNuOlUeWmrFT92r/yryMs1ZZWLA5NjEgfejdXhB2n0DkB\r\nFfDNgiqtmSzEDzYINYCb49F6AtFN3Fkw26yLLLCEHpl7H+g9+nzEtt4EB3ch\r\nqkNrTXN6cAHHCuN2dCPYYpdpIedNuMGO936/1dsgDf0p2pamQgc58sq7HBMk\r\n9XKwTs1+uimlSXzNIeFzhZjP3MQ4Kp6CMtCG6yAE6/FOB1ZNywcu/w2hjYEX\r\nTBdMwWsaXTEjCStXZzwhDAOovAreQQzHSDFeKhDfJr1nrqUZV6/zb82tP+tt\r\ntFiX44RKnQ2ieoHOZ22YUhWgdRpzT+q9LanBRVP8I0vaPKrle+c9Q+o/utmr\r\n0qq/mwyBWOnyXkCPlvcCVLGLwphG4USdabc=\r\n=WrJU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "dd5fccf12b9efbbe48f52fdd272daa6564a76f6e", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^3.0.0", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.5.3_1659619964521_0.1468979351783195", "host": "s3://npm-registry-packages"}}, "0.5.4": {"name": "laravel-vite-plugin", "version": "0.5.4", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.5.4", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "5bd998608b960fd4c175e9cb7a5677d80b12db3d", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.5.4.tgz", "fileCount": 8, "integrity": "sha512-LSLLul+YwY3egz0fzO4TPEGtUQAL4F2hrrt5wfQo3OzUfjVrqtRy0w1Zkw/g8CkfUyAZhr59oc/RxvPmpF9czg==", "signatures": [{"sig": "MEUCIQCozFJjFfubr5SbovpiXD7nwxZ2uIN+7QOSvujDfDQBBgIgBGdAklBI19DWaldpWt3P6A3DWz+kMomCzObk6BqRLyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+7NzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqH0w//bUv5DHFdeEhy25tta+BR+lMIgvic9bVsGF3rxYL9rO/mJ26L\r\nynXH8HXcagbpbnhrXcxVq0OHgHp0Y+Sypksb2648h1e9JTvBBY+Sy5yvl2WF\r\nsmYwm58g+4aeIyhm3bMlWLixKephmKutmR6FRXbs933E202518BYyC8zKVdB\r\nBnoX9j6zAcoal/zy0WyBf8PnTommrSICrpJ/O6NAJHauCCge6+YliLg5a65V\r\nxjzB5S8J85v/6Gbmb1pv2C5gWg9CG6CjBopoJqXbV8INA/Q2MTa50sopgIxV\r\nshi9IlH42rRQNmSYeobZj8I2G+6D1saNDYRk2obsC0bsW0VSRa1k16Pq27AP\r\nAf05kiXgydkDx+DKVr4J+UcHuF9TE9mzS01ywJjF1DbfqBonlyM4dQZuZzxk\r\nokXvS/1Sq1/M1qeULM7kIl4wq3uBWhVvEnxvG7izSHZesvfxkJkMXQnUDOFE\r\nqrAjgC4J9ydKfxauadGCLYnCVXWcmB/fA6d8Lbp+YWV34B3ln1IIIEolBYZ8\r\njMWIJ+PQabpAIUWSnguBsNU1vA4vLDqNoyuFwUmLZSylYfMibWyKc5nI9POG\r\naiUaNKgPE+LEjP4MxXGTnVr6tAVUWPBFhPgvur1gY+q+zShMzMdXi4FbefAN\r\nOF3PE2CHfGjZ/o9T0bY3QMkGT3oF93aPuO0=\r\n=UDKX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "112cbf98066fe5ceb3144c3557664b36f0f5e325", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^3.0.0", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.5.4_1660662643219_0.4040188233190041", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "laravel-vite-plugin", "version": "0.6.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.6.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "b3a73c5d4a5b7ea976d9e9c8db050c25fab6cecf", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.6.0.tgz", "fileCount": 8, "integrity": "sha512-Tns2IJlcEywi33C4vMVwI2BKCHokCwx//bb6KPSo4IbliojsYJbV6v+Cu0Xaw2sKnj7VDkKJC5uv9hw5vUkj7Q==", "signatures": [{"sig": "MEUCIErEyEvNONTwYSxemAflyfTWWkIIE7ZHGEV8KGY8LH2IAiEAqDPb3iJwIex8MLMtWMXL+BH3lAXpgNOpHqsN6i+1d78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB3WRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo85g/+LGvzJoFpvq5Aeau0//ABrcK0QL8IigKyH/lTc8FrWMSZY6tm\r\neM8VgOvDF0U49neY1jnfV9ScBPKIhMjG1EXv6xHNJ3EP/hnGeVlj+eMWdLT3\r\n3qlEU67ijdqZ8CJRCKhw0YfCKGHVWdwuiAuZH3W29d2F5PpGFjuOlvIcWHnt\r\n4HmbI0MRbFxjAm/+Wbo54ZEzWWbzO8IKmDMr2UYvpR0mGYMq+j+lODdhTlGy\r\nreSFZzKbN3N3EXXzIZcFSS1nlgYIge+2742o+pUPBPINxpJ/KjozhyfLSzxU\r\nD3CBccxYhpWcNkrbT34fmgtRWj2ZKXet+AXbfm3aeVVhzIQkUx6BIo8DXh9U\r\nWpfU/7fOGz7yj7Bix7MqmXSnBZ4S8cWhgGi6WzWI1WjqvLeH7dnTr5eW1BOR\r\n3ngW38502DpC0espqXOiV5Im1BjclWnVXIX8mIQNb/Fm30zf4FEwD5W91XQ9\r\nJ6GOZAvfzwUPkKi0IU89GKZDUA29zY2WruK8eGlTFVDPb5gmRUYnKiZC0IuJ\r\no3gRxxcbA5puQKyPmkNmiJ7LHsZDBM/gHceHLv7k1dteK5ipdsOecIp7JSY+\r\n9I8blrt28cJUx3eaJTZxQdGtrSiAVBI1idKFt+eCQywrCHKtyixbiWdPeWmM\r\n5Kv+5VoVhkzv3vNDgoLUa4mLAr/SKxcBPQw=\r\n=Hy3E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "e857f25edfc9862dd2749af88ff7983449449d8f", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^3.0.0", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.6.0_1661433233737_0.9840146267490639", "host": "s3://npm-registry-packages"}}, "0.6.1": {"name": "laravel-vite-plugin", "version": "0.6.1", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.6.1", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "d60b31584871a7206feebcf1dd6ab67ea494a670", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.6.1.tgz", "fileCount": 8, "integrity": "sha512-L8zt+bttm6+C0mo3an5J8wRW03SsjbTEouGb3bH2jj/XclFVAX/xEUkG9efhdRHjbEH5RY6cmdJ7bmf7zqjwIQ==", "signatures": [{"sig": "MEUCIE02QhlKPT8W7IBPGslhtleXTsTDRYWKl6qbzYMNYO7+AiEAq6DedbGTnIPaYVutOfnC7/eTqfUTRK0KZCLTft4cCAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKxEYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7pRAAne+cnICB4PZHfzvzGyrrF4XCawdStrh8sh+pAgQ875MTZGGD\r\nj7Kgpu4ZJ5Y8npbDmOrOMTEP0vzmqaOfZYQnhUxCj5CP8JSxALYV0UpN+SFs\r\nRGbw6b5pFg5MSaFYvuSu7vZMRiPUVLn6PF7Ky6kncBdCj2aPVmD4njnDl2lm\r\n1hNx8knk3rO1bdciwM2kLJFd94yt65dwolLZ7iVkwO+VXoHkiJDWNnjXe7YK\r\nK76v0lWTLLtNNW+kRfcxOfQErFdrhvYQ/XcPzB6BPAovuucpTgxe2N5PrBZP\r\nA5o3e0/pXA+bXbRUMzvKC0TyExAJh1DigsyM+Rbwc+qZICb0OedNoc0pBu9j\r\nwocyXEeYmK4Gm8txV+ql5ZXhfXlhMtGqjrXFaf1jxQUov5F4Vrp8n1gYbHqA\r\nCv67lfLPWHms/xow6tr0XLRLBsdUtkSf0P1pmOvbxbQh+ixSziuoIq4tiBeX\r\nUXWnct0vRJCf6FsuacWJ1bbOyQK3vSUZdtBkj4pkZZE51QYeEQqmxpF3kAus\r\n0OCBac5Me2Xwxag11C/8G5TCwByU/AO0nXR6/Sn+M9/bgXG8+AthIAaxdlVt\r\nze1w+sufaezcXeTDKOU1NFK0f0mfgjEqPk+J50U8hkRrhNL6guQKJK4D5X80\r\nn51Bl7sQbTotRtoNH28i9Pbp9yJgZQB48SU=\r\n=/lG2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "109d2ef907e439408c1475206d265e540bdfa1e2", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^3.0.0", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.6.1_1663766808705_0.65555105605666", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "laravel-vite-plugin", "version": "0.7.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.7.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "61403d3e3068fb62ea97d8d4feecf03db4a6a23f", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.7.0.tgz", "fileCount": 8, "integrity": "sha512-eYViSOGqqVEjQJXkXZFX7DpI5f8vGyAZ9pSoAeM5esJGsX5NtdZmJf2UfblODYcaknXd4POo/pkg0bTRt97X4A==", "signatures": [{"sig": "MEYCIQCKvdm7Et3dIIS8KZikyHgvXjw1mJG2IC1FCf1GO+vVIgIhAJeLaLFRWv9CVkbizJ78CMlbIKmVs0yXwYWYcnTKuSjK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWCerACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZfA//YSXUp9qMlvs6+lj0mPgCecwAceOHeQOR3fmjP4/G5DZlUpot\r\nfDokszgEZuEYE7fQcjyFhIB0jIgoTAfyjaKSqBYJ1moz8g/zFRqLVY/nMGm4\r\nYajRc38/gKTK+k6CWxV6eBRKONmqyBRaa6MupLyYprJtr4E1G13sJzwhf7tG\r\nzYz3L67VTmbfQzhY3B8bSMnt05XXK1k+Nc5xfqP1d9pC7fmLBPGQ9whBlkAp\r\nisIblAtAYLIPXtMG/1+C3l6CaTrK5dSVNj88xvgbTV9SzhH1nj1G5oZIZ/d+\r\nzIu4WsQZb0JPacLWVBQ81su0nX7J4MEt23cDMNaGMUEhP6XAvA5rB6LCLUui\r\ndCGZzbzVFdEZhnAuhJinNWsQjXN4qgQq53PFwQOrH5VWMUQFGohgKjGYtupr\r\nynHcAunktLlzfsD3jqBYww/IiC80GAfEC8yF0vXF+6QCVWNeJ4tc7DDH0rHY\r\nDUyCHQl0cyh7+sTIH+r7vgvHSz4OWbG+4O8eSuXfOle26ZjF6gltRyBnj0Tg\r\n2q0U/6nTwxBcVKPopQpRe9cvIX0hXZvrWub0jSXalwg/3nJK8oV2T5jgHlql\r\nxJkx37BvdbUcLS9lL9qYWJHWM0k16HHRiMod6WGu0P5WxKAgxCKS0sBejAqd\r\n9bSu63fOBsiq/WVcMKSJhXMOC76nNuTJIWI=\r\n=Q8MY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "81fe06fecc46c32b83dc2ceef027f371c470121e", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^3.0.0", "eslint": "^8.14.0", "vitest": "^0.12.4", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^17.0.31", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.7.0_1666721707527_0.3150615056440669", "host": "s3://npm-registry-packages"}}, "0.7.1": {"name": "laravel-vite-plugin", "version": "0.7.1", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.7.1", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "86293247a234ede0c75eca92191559bc854c5528", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.7.1.tgz", "fileCount": 8, "integrity": "sha512-nz2slMkGyabfEnXYsQnDJHCefQBdIJgeTkm2wgWEYbAaddwLzTSjmhT1diRm9TUYwIrNqlbuPNVD3VYy/kP9Wg==", "signatures": [{"sig": "MEYCIQDuVewqV06nbBsG2bgEE6i+za0Y67wibu5roVqSZicefQIhAKOp7irVJpiACHrghXp38qXFMP27BL+y/F/Z1C6QqWfa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJje4koACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpK3A//YnE2C7CYm0b4QIn/paxhVT6GVseMth48fBOU6CTGgXsL6IIz\r\ne+5bqqvgEyZF5Zl6yMLieVnmxV0ITW80VqMDWXtQlluOEdaVQypw6gfc/EBj\r\nhwegZiVx6lE5hR7dcI/+cEpB0bmL0bdDg1sf3FLZV6WELlDFCwGYK7BZyPbh\r\nlSnQDsJHPXXOnYn9971TRbLgFqcl64Pw22MQkYUNcGxPZ54ct/zV737uvozg\r\ncuP16m+QQ8DbX5sihnS3qZAhEFPbIMB9K/Tit8T+QUnsQQAQsHH/A0qt5ojz\r\n8kLj9c9ZjvRjWTFUPLUVcqfb+mCOebp23WkBHIcEGl0AFF13ji2aN6xsUjdV\r\n+5pQfCdzzdn5rELlVl7DZnNb2eyFLygyIN4pQclCVfQ43XLgQcs9wURn7l1E\r\nZEALaaV08WmCUwHE4IlrA0vtyhwppzYcw6RDBisZ8qnVnSspBZDtX6cSEcdA\r\nFYnB14AoQMVV4E56qE0KjKS4XEgzs12sZAY33Ngm4VOXDp6AG/Ucheh0etxL\r\nBriVfeoHf49ZL8v1GUN35WO35lp7lfLZyJ5tnHrlPfI3TOIZqQE9qHXdyvtk\r\nwBHGluljyZ/o2WrI2oL9GmcnNAYSLO716VeplIy1lJkp+3nEE04Qz6KLjfO+\r\n1z8ummxCgFZBDhLIJcPVTSUgX72P0s9fuVI=\r\n=5il/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "ca59e8c93f6da69f7402864d910b71864c0e30d3", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "18.12.0", "dependencies": {"vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^3.0.0", "eslint": "^8.14.0", "vitest": "^0.25.2", "picocolors": "^1.0.0", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.7.1_1669040423875_0.09064353749651466", "host": "s3://npm-registry-packages"}}, "0.7.2": {"name": "laravel-vite-plugin", "version": "0.7.2", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.7.2", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "b317f8345d9afc2344d51fa8054e4389984d9650", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.7.2.tgz", "fileCount": 8, "integrity": "sha512-MEAGEEOrDk46PnoUPLAhHxM1ATEdRYKyDtN8DT/PLTDnO/HY6JZpRZpeC+Q0fiYzbzg+6OH0nlhuSRKbBaCLcw==", "signatures": [{"sig": "MEYCIQDi75xbEdnHcg/EiWhmz9r/YBbvi/RFr4jdtlNqJtWJ6gIhAPxY46Kg/s2de1OxVLy0gYrCs7rQQ+dQ0bn4gdqitnNi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmzT3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+pg/+J27bpi1WIf0wF1Q2B+41d1Iss6i3UIwdnD4M2rR4QrLWTiXz\r\nLcVe+RZDvAyRtdTlVBqY+Wz0XsOjWE4YHNwH1e8Q4jtJthFTXIH2PlS0JDph\r\nAgcw0E9gSkHJrovidhCVU2Qqbj7ybrZ0ERCWJSMOnYCFQQfNzdrnJQOR+IVV\r\nvFzQSeoPQjWd5erRRZLySRAC3IgVtxPhsVEdPYjPWldDw8RHK3v8CELCBAnX\r\nRQRl6wF4q9UDApewNOgGW60DJThlMXQrpbviu4jhXnNG3dQe+wDaS2LeoZfA\r\ngRDDB3SauUGGv4v8wAeTTUrohSt2CStJ6SqlmAKRd608AI/ijD7TmFHsxzWw\r\nXgssVNI+hbit+DHQNtXavUGKMYQ4UCsTMJWyMkEvgwxwXEboKSZONS68d0CL\r\nyO7+m/kTaYb4McaE6RISPeJxjMvl+AQN6Z1uZZavGjfnCYMvg736rG6pktB2\r\nG+Bft0RzC/ZOX/PZhKRBRM84QXYDjY0Jz8azSNsdfRclNbqcT9Yj1fg97GGS\r\nyvuFgvUHXRR0dizcSlaAhbDVmQ1mpworNPO+pY5nv4QiAU74hwf0adfH6oDX\r\ntyVuZoOMK3vZzMwhqEw6OM0p1HYdq8yETt8Exsy76L8byCJOB4z1OR6hqKjE\r\n3mFVkXTkbkES5aFxVR7RKZuNHjNZzaS14EA=\r\n=Ieeq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "1fc11d47adcc9fcca3a4deef8b73b54d7156a7b7", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "18.12.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^4.0.0", "eslint": "^8.14.0", "vitest": "^0.25.2", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.7.2_1671116023532_0.16620913940968873", "host": "s3://npm-registry-packages"}}, "0.7.3": {"name": "laravel-vite-plugin", "version": "0.7.3", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.7.3", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "caaeaeaca1af0772607d2b813cf27720cc2030a1", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.7.3.tgz", "fileCount": 8, "integrity": "sha512-N6hH/JFA98oW1I0OZ2Gx1HF3/QzkBpUIkxqfKqxENQNLWLFx4+CWB7cxP+i4sGXp9LQUlLbH5Lq/eZoXtLAq8w==", "signatures": [{"sig": "MEUCIQDvp39QntvYZmM5U74oSW0AtQ+uacQpYfF8smF2CK2W+wIgTinKl4Xn/x8We/qsSEGxzlsVgucRmGDBAlttRElBBQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjofPXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIFRAAl60kRyBsk0ahjTDGoLCyOOIx5oBm0Chog32ObqDg43EgQWzb\r\nmFabdvKHEDxjhs8UJ1ggptx4dj56qZ2bc+5aGAYQ1BFBJlP1pn60qZtmOQ0J\r\nK0QN3sn/6BRHsaa2/LS/WTXEJNmMYnWn1pdLwXKKaaXt739YrMj00ZoPX4T/\r\ncY8LcsthAD5TAdAq+xyUFV7b/F9FQOCIrYMHVuGACTCBlWO8p/xj88HWujEp\r\nxebPPTIC6fTyNU5jtLmH9d0jfj+ILanKELJ8iIQ2JLjFn7/3V+DNUgBEerX1\r\nys+Rr3vOaGPsIEyLC1WikzLPsDyGT52Wi34FMYNwEnp5xFm9IRGYiEkTkEOZ\r\nW4nY+0MOpsiWpH9El0d9lNnB4oWiMtO6DMEuhqFwpy7txg2FzT/YJrQubsBZ\r\n/4V1oSgjMbRsM7b8IOAorkjyzLopysQVCKDyC1Z4YfeSy+Fs1LzlaGftrdKz\r\n4mI/yKkF13DsjL5JwiPcYyu1cwS+4y7nMt0HqG/iA5HMBL7gilrnkqBtFmj/\r\n3snji1gSPZ5TqwoRC8/x+SkE6AJdmH8KtJ4EOCnZInkaghNTb71pzNDb3Z8k\r\n+R8nn+/4O+Ihz0XDil6HcJwBtMu3Qa0++oVGZBMz3sn6C1D4M52xU4vogCk8\r\ncCHnuEZX9so1+BZ9cPr5jBLmogp4YXBycCs=\r\n=ACPb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "ea8aa280a4c0174515225637c279daf3be91823a", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "18.12.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^4.0.0", "eslint": "^8.14.0", "vitest": "^0.25.2", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.7.3_1671558103758_0.03251965054220607", "host": "s3://npm-registry-packages"}}, "0.7.4": {"name": "laravel-vite-plugin", "version": "0.7.4", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.7.4", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "563afc730191c134fa28c44311e26c9fb9ad68b0", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.7.4.tgz", "fileCount": 8, "integrity": "sha512-NlIuXbeuI+4NZzRpWNpGHRVTwuFWessvD7QoD+o2MlyAi7qyUS4J8r4/yTlu1dl9lxcR7iKoYUmHQqZDcrw2KA==", "signatures": [{"sig": "MEUCIQC6mrzaMQrkW1d2B4Mzn2KUHLWoxqvKzU4We5TZ2zKtxAIgGvPBjgOaeHK0qXnY+QQMk2qFuh2YtOM8J3GTTmOR9NA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4n6CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoJg//TgifpeHgbM6cfSjLOelRyDV09HA0MF2FPZsB8A9BcyHCUuLz\r\nchGIaLnx0TYflCagn4WJSINKShxIslqHHsjqJJ6J9Bikt0go+t02nQ+ZXUB1\r\nWlDywdPT+79+yUy4BpywPEh3x7o4faHoEF7XMcy2S2WDIEV9823apYz9NbYp\r\nxlVoFRgKKEDfJ5KyWslU1wJo3NErsKmp/SaAfgTWcnjH1WSxnpDcA3esfHUx\r\njpUvG5qsVLaDo2d0kFdFzHsBw6Cmpypum2PtPG8UuOAnW1PEpHJbZ206YDEq\r\n3R1xIRaCxewwZaRV7erPjmFMd6cFd/tabXN1EEGEd5IqjZ96LnRkQzWECab3\r\ncAY+mZmgmGHEV68NOyVimrhL7zMh/1DKilzyzZgjEog5fI1DGF8LiC1M5u86\r\nPWkhqGOdNuPWXgO2NJmj3kF8zBy4frrrsP/5QiZdD6PwDJRFuHL8C3UXG4v3\r\nZXzieLlb/HBBsUCSTmLFwcRo1Lg8Au8cQZ4CuoMpvoYqegd8EkxgWEy1LZ2Y\r\nEfQqUXEjNjP1U0phSkoCljLjcTDXgrP3sMoU3sx+JoQuYTJ8sWOl7XLMUzoe\r\nslrw/NrcpqvhtYH60nUdLBuvUyJhxUBI1tFBvT0BbsFV/Ys+yvoRAXfNOwuc\r\nFv3tpj8X9y/jgn8jzFZc/V6GeSgE7TyaMKg=\r\n=jk2V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=14"}, "gitHead": "6c80eab9ef3366bd49bb3bc5b01def015b265efe", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && tsc && cp src/dev-server-index.html dist/", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "18.12.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^4.0.0", "eslint": "^8.14.0", "vitest": "^0.25.2", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.7.4_1675787906382_0.03271954129305277", "host": "s3://npm-registry-packages"}}, "0.7.5": {"name": "laravel-vite-plugin", "version": "0.7.5", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.7.5", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "d657d3e28e000a52becc89798232026f8335f162", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.7.5.tgz", "fileCount": 9, "integrity": "sha512-BrrjFhbpJpQkG3a0DQ68x6Vk1glwQPF5A2HywY9w5te7Dp/AmEIj71f9/m/wS44/HFXTUVd2IUhD+zGjevgPfw==", "signatures": [{"sig": "MEUCIQCQtHD9b/HgYybVglfduzk77/SiiYaOZVaqnW/Nofk2LwIgfYL01LxTTHcrF3lTl/odlCv8lnYgcOFuoGtREk92KZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55378}, "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "import": "./inertia-helpers/index.js"}}, "gitHead": "312828b0413ebc95ea0e67dfa1ce6c584a55b712", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && npm run build-plugin-cjs && cp src/dev-server-index.html dist/", "build-plugin-cjs": "esbuild src/index.ts --platform=node --format=cjs --outfile=dist/index.cjs", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.mjs", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^4.0.0", "eslint": "^8.14.0", "vitest": "^0.25.2", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.7.5_1683643159191_0.08587359099171166", "host": "s3://npm-registry-packages"}}, "0.7.6": {"name": "laravel-vite-plugin", "version": "0.7.6", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.7.6", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "fb9622c739451758189ae8e9ece12bdd30087064", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.7.6.tgz", "fileCount": 9, "integrity": "sha512-WJ1WRqR/ZDbJD+qEEPM+biG0ZPSub0n3DywEi21+YO1jZA8amMoJGu76HiAFM16eWz5cBcCeRM/gseVXfGV4Mw==", "signatures": [{"sig": "MEQCICNb0MgkioI6eYL8Lly1JPrLWHoiKRkhweCSIqfqWfkpAiA/FqDSldy7DCYt+jzaqq4dtWtLAe/bqgI+86q2tCO9gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55925}, "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "import": "./inertia-helpers/index.js"}}, "gitHead": "1656e354dfc6fccfb2a8bb0df4c5262b6e6713c2", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && npm run build-plugin-cjs && cp src/dev-server-index.html dist/", "build-plugin-cjs": "esbuild src/index.ts --platform=node --format=cjs --outfile=dist/index.cjs --define:import.meta.url=import_meta_url --inject:./import.meta.url-polyfill.js", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.mjs", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^4.0.0", "eslint": "^8.14.0", "vitest": "^0.25.2", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.7.6_1683725700320_0.7782002520796245", "host": "s3://npm-registry-packages"}}, "0.7.7": {"name": "laravel-vite-plugin", "version": "0.7.7", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.7.7", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "1b3fdb96cccdbc363597a4b1cbc550ba741f315a", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.7.7.tgz", "fileCount": 9, "integrity": "sha512-/KsnyNUOMylBVLvGz1VlL1ukxyQeMQUz4zmnMflYe8fAWzLvjHDXnbh6fLgIrzAmevzNjYm/TUqmD5Io0+kqhg==", "signatures": [{"sig": "MEQCIHt8XmDfTmfgql4FYMmFEe0krXLZhTnJbhJj+hBGcyc1AiBq3vNkV599quC9yZqYu+2C4sfGvdWLqCcpzZ7QKDUC2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55975}, "types": "./dist/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "import": "./inertia-helpers/index.js"}}, "gitHead": "a72ae35f4f7a78d094ec8ce48010ad5c436456c9", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && npm run build-plugin-cjs && cp src/dev-server-index.html dist/", "build-plugin-cjs": "esbuild src/index.ts --platform=node --format=cjs --outfile=dist/index.cjs --define:import.meta.url=import_meta_url --inject:./import.meta.url-polyfill.js", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.mjs", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^4.0.0", "eslint": "^8.14.0", "vitest": "^0.25.2", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.7.7_1684252420345_0.7430330643622338", "host": "s3://npm-registry-packages"}}, "0.7.8": {"name": "laravel-vite-plugin", "version": "0.7.8", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.7.8", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "0fd0c577389ed5f4c5bb61cfbd9da5d18c9b58dc", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.7.8.tgz", "fileCount": 9, "integrity": "sha512-HWYqpQYHR3kEQ1LsHX7gHJoNNf0bz5z5mDaHBLzS+PGLCTmYqlU5/SZyeEgObV7z7bC/cnStYcY9H1DI1D5Udg==", "signatures": [{"sig": "MEQCID0LmSNnKFN+Kd8g+PAQUPGfhY/FS2i3/+bZ/SCI03BLAiAYta84MaW64hHZy30Rfnpqc5dlp8DHwODx5Ydxtt+gQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56025}, "types": "./dist/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./inertia-helpers": {"node": "./inertia-helpers/index.js", "types": "./inertia-helpers/index.d.ts", "import": "./inertia-helpers/index.js"}}, "gitHead": "93250166f8bb1a0734b2c23142ba77b9fdc1dc2e", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && npm run build-plugin-cjs && cp src/dev-server-index.html dist/", "build-plugin-cjs": "esbuild src/index.ts --platform=node --format=cjs --outfile=dist/index.cjs --define:import.meta.url=import_meta_url --inject:./import.meta.url-polyfill.js", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.mjs", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^4.0.0", "eslint": "^8.14.0", "vitest": "^0.25.2", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.7.8_1684935629347_0.2496674055742245", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "laravel-vite-plugin", "version": "0.8.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.8.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "9bad57e579309fa88eab69f4a48551f9576bdffb", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.8.0.tgz", "fileCount": 9, "integrity": "sha512-6VjLI+azBpeK6rWBiKcb/En5GnTdYpL0U4zS8gXYvb2/VSq4mlau5H3NWpSktUDBMM1b97LLgICx5zevi8IY0w==", "signatures": [{"sig": "MEUCICRkNEBq7udz85DvETWo0vB5lvMGnJweSkkmAOv+Vf9EAiEA6chkrnCllXhVoxymbd1r9JyK4mKJCgkqPBKSz0kkXrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57401}, "types": "./dist/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./inertia-helpers": {"node": "./inertia-helpers/index.js", "types": "./inertia-helpers/index.d.ts", "import": "./inertia-helpers/index.js"}}, "gitHead": "e44af37d4cf04732aca1d48092e6a0156aa83929", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && npm run build-plugin-cjs && cp src/dev-server-index.html dist/", "build-plugin-cjs": "esbuild src/index.ts --platform=node --format=cjs --outfile=dist/index.cjs --define:import.meta.url=import_meta_url --inject:./import.meta.url-polyfill.js", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.mjs", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^4.0.0", "eslint": "^8.14.0", "vitest": "^0.25.2", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.8.0_1691506393160_0.7993106049293377", "host": "s3://npm-registry-packages"}}, "0.8.1": {"name": "laravel-vite-plugin", "version": "0.8.1", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@0.8.1", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "dist": {"shasum": "5e077944946c02008993f7cece8c36144a25c433", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.8.1.tgz", "fileCount": 9, "integrity": "sha512-fxzUDjOA37kOsYq8dP+3oPIlw8/kJVXwu0hOXLun82R1LpV02shGeWGYKx2lbpKffL5I0sfPPjfqbYxuqBluAA==", "signatures": [{"sig": "MEUCIQCteDF6529NMHrIIdoM/mHN4gL3F/6FgYzrKl61ALbnJwIgDU/kvPv4fUKTCDqvIGR1uzkN1JTkC2XTadon5RkdNc0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57743}, "types": "./dist/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./inertia-helpers": {"node": "./inertia-helpers/index.js", "types": "./inertia-helpers/index.d.ts", "import": "./inertia-helpers/index.js"}}, "gitHead": "65d6eaa72dd0f7328b014489a4fb0429f866d9e5", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && npm run build-plugin-cjs && cp src/dev-server-index.html dist/", "build-plugin-cjs": "esbuild src/index.ts --platform=node --format=cjs --outfile=dist/index.cjs --define:import.meta.url=import_meta_url --inject:./import.meta.url-polyfill.js", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.mjs", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^4.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_0.8.1_1695747977254_0.7421576461972399", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "laravel-vite-plugin", "version": "1.0.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@1.0.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "bin": {"clean-orphaned-assets": "bin/clean.js"}, "dist": {"shasum": "4760909ee385ffcc554ed9b79ca318e3c827318f", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.0.0.tgz", "fileCount": 9, "integrity": "sha512-tuiir0LjqrgpYlOUP8plaEPwXAHcvCmB0HKD+noJrBew2yER1ahSz82u2lQMEWAJzBAFSEB4UytOxX+FQ1dOuA==", "signatures": [{"sig": "MEYCIQCyf6K678rlGG6ePzPKRL4a1A4wOlSpoi0psva2gGRjIAIhAMmsg/7CvI81rlzYFP8oHXwTpTyXIM7z8gePdYqFJ7xm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46004}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "gitHead": "0a753dee1ddb787a5c01e14d37a0c75abeeb45ff", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^5.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_1.0.0_1702997421974_0.12752410414800686", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "laravel-vite-plugin", "version": "1.0.1", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@1.0.1", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "bin": {"clean-orphaned-assets": "bin/clean.js"}, "dist": {"shasum": "b92d0c939ccd60879746b23282100131f753cec7", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.0.1.tgz", "fileCount": 9, "integrity": "sha512-laLEZUnSskIDZtLb2FNRdcjsRUhh1VOVvapbVGVTeaBPJTCF/b6gbPiX2dZdcH1RKoOE0an7L+2gnInk6K33Zw==", "signatures": [{"sig": "MEUCIGRRlEb57MUggaGnEgVM+6Oy2N+tQ553ogxtchpSFTwqAiEAi5sEBwwJbn6BQeojRgKGdAmqJrAP64fwdh8wtn+Fbd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45918}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "gitHead": "78e60b0eb46a4ed1682c2d214476dcdce59f383c", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^5.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_1.0.1_1703688796973_0.4286204507135569", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "laravel-vite-plugin", "version": "1.0.2", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@1.0.2", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "bin": {"clean-orphaned-assets": "bin/clean.js"}, "dist": {"shasum": "97575181a1f870532b39ab3b423b53f01385feba", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.0.2.tgz", "fileCount": 9, "integrity": "sha512-Mcclml10khYzBVxDwJro8wnVDwD4i7XOSEMACQNnarvTnHjrjXLLL+B/Snif2wYAyElsOqagJZ7VAinb/2vF5g==", "signatures": [{"sig": "MEUCIQC6nvgAxgYUZoJhK5HmVR6Whv3HHcn9hRyMydKneykDWwIgHDjdmD+dt95Lx3AhMG1HtDJ0KQIZSsBqo2lI3v4k1z4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45979}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "gitHead": "977afc5d24365207669d7e245585423a1ab4b935", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^5.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_1.0.2_1709087503569_0.4848484943431619", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "laravel-vite-plugin", "version": "1.0.3", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@1.0.3", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "bin": {"clean-orphaned-assets": "bin/clean.js"}, "dist": {"shasum": "76b33b11535cccd6612de178289e9c2ea4ede933", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-PIAOwL50f7GtC6rE7yfPf/35ho8Yd5GjVosfP/mEw1P9g2Pkz5TYkHpm2RdCYTqhohPd4WIsa1RutxJf0xIBEg==", "signatures": [{"sig": "MEUCIAT8ZhKv5aYze4K1q/xW23irs9JhEGC+7D4IgHP6AqU6AiEAiuX5GdX2IaK2fq42KNS6f9bsm85pelukS26+Jg8BZkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46370}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "gitHead": "b82e7b5df2c00e602128119205ece8d042766e60", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^5.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_1.0.3_1715864208146_0.1021128907537956", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "laravel-vite-plugin", "version": "1.0.4", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@1.0.4", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "bin": {"clean-orphaned-assets": "bin/clean.js"}, "dist": {"shasum": "b9a516ed212aae55c19369984dc5d2f09276560c", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.0.4.tgz", "fileCount": 9, "integrity": "sha512-dEj8Q/Fsn0kKbOQ55bl/NmyJL+dD6OxnVaM/nNByw5XV4b00ky6FzXKVuHLDr4BvSJKH1y6oaOcEG5wKpCZ5+A==", "signatures": [{"sig": "MEUCIQCDeaMaZZZkVy7m8UZCKjuMDdolDEGVxCQEuTJshxH49AIgQUUEZMO/t//zlBzOypEyrDavrkMr29ADKYtVscBeea4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46352}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "gitHead": "0cd9d80d8047408c9ad988a557fa286d47ce17aa", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^5.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_1.0.4_1715950915287_0.8759832611449903", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "laravel-vite-plugin", "version": "1.0.5", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@1.0.5", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "bin": {"clean-orphaned-assets": "bin/clean.js"}, "dist": {"shasum": "0d1881bf69d9d420a01f33db183adb82d9143362", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.0.5.tgz", "fileCount": 9, "integrity": "sha512-Zv+to82YLBknDCZ6g3iwOv9wZ7f6EWStb9pjSm7MGe9Mfoy5ynT2ssZbGsMr1udU6rDg9HOoYEVGw5Qf+p9zbw==", "signatures": [{"sig": "MEYCIQCoSPiATcMR0tKiuRefD8SWvjvmzhFjFwCNSTVg2zW8GQIhAPQMDZMNQK7wYpiBaUP4BXKcu1faPNhBOxtn0RwQ/XEg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46378}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "gitHead": "91491adffa1437fdf40b9e843804d2d9b2e54ef0", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^5.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_1.0.5_1720546337748_0.5560674161173538", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "laravel-vite-plugin", "version": "1.0.6", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@1.0.6", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "bin": {"clean-orphaned-assets": "bin/clean.js"}, "dist": {"shasum": "14c4f33574dda3ff4636ee73c83cd0c5f39e5d09", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.0.6.tgz", "fileCount": 9, "integrity": "sha512-B34OqmZc/rV1KvSjst8SsUm/LKHsuDusw8jiZCIhlnTHXbXnK89JUM9pTJuk6E/Vc/1DT2gX7qNfhipak1WS8w==", "signatures": [{"sig": "MEUCIQDpM8WtLd0ODKuKdcyyb3lOSU92gY2ETqjT1JhU+zLH4gIgXLicbnPP4mK3bMdv/jTZvFXJSuUfX18t4wZ6aIPJosc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46629}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "gitHead": "b331d990268c856be2228bc9296fa07e0029e41c", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^5.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_1.0.6_1731428850466_0.6088443095011489", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "laravel-vite-plugin", "version": "1.1.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@1.1.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "bin": {"clean-orphaned-assets": "bin/clean.js"}, "dist": {"shasum": "7f69edf78106bb27c6a6ca8574a2ee26354d6411", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.1.0.tgz", "fileCount": 9, "integrity": "sha512-jo0PQGvAxbhUwNPEBKwNV8cS5kEsrCHPKrUtIBAuPrWODNCKcM4a/7dq2Hq4vzHaXdE7qOoasFs6GNQZecfZqw==", "signatures": [{"sig": "MEQCIEJfo/qmtrjd9TW5WI9/IGq+N6eaOpxTvcnowbDuRM1hAiBCyJOsGlBohxRlRXmbSuLCtH1na46DIfon69i2Xjdveg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46640}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "gitHead": "aacee1d9a3bec54a59eecf6dce757d2fcda2e77f", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^6.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_1.1.0_1733154795511_0.9304583365421566", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "laravel-vite-plugin", "version": "1.1.1", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@1.1.1", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "bin": {"clean-orphaned-assets": "bin/clean.js"}, "dist": {"shasum": "e909a2df7cad7275dca3f7717f5e3075891045d4", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.1.1.tgz", "fileCount": 9, "integrity": "sha512-HMZXpoSs1OR+7Lw1+g4Iy/s3HF3Ldl8KxxYT2Ot8pEB4XB/QRuZeWgDYJdu552UN03YRSRNK84CLC9NzYRtncA==", "signatures": [{"sig": "MEUCIDHAw7okZ3wfQyyHMBwDnstTT9aU2iz8gIoLmFJzPw3uAiEAqjn9O5q+XdSZRNI9c6xD90FaE6L5W0dxh9iL9wVtCe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46650}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "gitHead": "b91e205586a0334aa7e35187cd46a7566e921f87", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^6.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_1.1.1_1733213740721_0.11959574551940566", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "laravel-vite-plugin", "version": "1.2.0", "keywords": ["laravel", "vite", "vite-plugin"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "laravel-vite-plugin@1.2.0", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "homepage": "https://github.com/laravel/vite-plugin", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "bin": {"clean-orphaned-assets": "bin/clean.js"}, "dist": {"shasum": "7a82e850fba9ca2359fa64f70e0647478ea5fde3", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.2.0.tgz", "fileCount": 9, "integrity": "sha512-R0pJ+IcTVeqEMoKz/B2Ij57QVq3sFTABiFmb06gAwFdivbOgsUtuhX6N2MGLEArajrS3U5JbberzwOe7uXHMHQ==", "signatures": [{"sig": "MEUCIEuUdpsetgEG0NwtehVxS/GY4DzorADkmeERlpdQCSCsAiEAol071k9KRTOXtpXI9Kr2fC+vcxe3ZaPGSD7wcE/EmJ4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46982}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "gitHead": "0201f3f08b40a050aa83d33d0d0318fa0970cc99", "scripts": {"lint": "eslint --ext .ts ./src ./tests", "test": "vitest run", "build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-plugin-types": "tsc --emitDeclarationOnly", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json"}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/laravel/vite-plugin.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Laravel plugin for Vite.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"vite": "^6.0.0", "eslint": "^8.14.0", "vitest": "^0.34.4", "esbuild": "0.16.10", "typescript": "^4.6.4", "@types/node": "^18.11.9", "@typescript-eslint/parser": "^5.21.0", "@typescript-eslint/eslint-plugin": "^5.21.0"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/laravel-vite-plugin_1.2.0_1737502058122_0.8877115540857896", "host": "s3://npm-registry-packages-npm-production"}}, "1.3.0": {"name": "laravel-vite-plugin", "version": "1.3.0", "description": "Laravel plugin for Vite.", "keywords": ["laravel", "vite", "vite-plugin"], "homepage": "https://github.com/laravel/vite-plugin", "repository": {"type": "git", "url": "git+https://github.com/laravel/vite-plugin.git"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>"}, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./inertia-helpers": {"types": "./inertia-helpers/index.d.ts", "default": "./inertia-helpers/index.js"}}, "types": "./dist/index.d.ts", "bin": {"clean-orphaned-assets": "bin/clean.js"}, "scripts": {"build": "npm run build-plugin && npm run build-inertia-helpers", "build-plugin": "rm -rf dist && npm run build-plugin-types && npm run build-plugin-esm && cp src/dev-server-index.html dist/", "build-plugin-types": "tsc --emitDeclarationOnly", "build-plugin-esm": "esbuild src/index.ts --platform=node --format=esm --outfile=dist/index.js", "build-inertia-helpers": "rm -rf inertia-helpers && tsc --project tsconfig.inertia-helpers.json", "lint": "eslint --ext .ts ./src ./tests", "test": "vitest run"}, "devDependencies": {"@types/node": "^18.11.9", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "esbuild": "0.16.10", "eslint": "^8.14.0", "typescript": "^4.6.4", "vite": "^6.0.0", "vitest": "^0.34.4"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "dependencies": {"picocolors": "^1.0.0", "vite-plugin-full-reload": "^1.1.0"}, "_id": "laravel-vite-plugin@1.3.0", "gitHead": "9d835fea5099f8bcba7b684769514d8eac835d43", "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-P5qyG56YbYxM8OuYmK2OkhcKe0AksNVJUjq9LUZ5tOekU9fBn9LujYyctI4t9XoLjuMvHJXXpCoPntY1oKltuA==", "shasum": "04a9b109281414b80f4355cd4cef94d98bd7dec4", "tarball": "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-1.3.0.tgz", "fileCount": 9, "unpackedSize": 46966, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDVxjMJ1co62VPfnO8oZFVWzEcf+Y2OfQe43Oo3AxxQvQIhAKap+tOB4NgJJ1StY+t9M8CXJ9ChhJlXYZ70Ri4iTy18"}]}, "_npmUser": {"name": "tayl<PERSON>twell", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/laravel-vite-plugin_1.3.0_1748960445384_0.7007985935706404"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-05-19T15:02:30.144Z", "modified": "2025-06-03T14:20:45.788Z", "0.0.1": "2022-05-19T15:02:30.320Z", "0.1.0": "2022-05-28T00:44:57.726Z", "0.1.1": "2022-06-01T15:03:49.432Z", "0.1.2": "2022-06-02T02:32:34.017Z", "0.1.3": "2022-06-10T18:00:37.408Z", "0.2.0": "2022-06-14T02:25:50.755Z", "0.2.1": "2022-06-16T02:11:30.016Z", "0.2.2": "2022-06-18T18:30:55.277Z", "0.2.3": "2022-06-24T17:26:51.990Z", "0.2.4": "2022-06-29T13:42:56.548Z", "0.3.0": "2022-07-05T16:34:46.879Z", "0.4.0": "2022-07-13T13:52:02.228Z", "0.5.0": "2022-07-19T14:03:07.713Z", "0.5.1": "2022-07-22T14:11:43.194Z", "0.5.2": "2022-07-22T14:12:05.998Z", "0.5.3": "2022-08-04T13:32:44.715Z", "0.5.4": "2022-08-16T15:10:43.355Z", "0.6.0": "2022-08-25T13:13:53.913Z", "0.6.1": "2022-09-21T13:26:48.851Z", "0.7.0": "2022-10-25T18:15:07.819Z", "0.7.1": "2022-11-21T14:20:24.011Z", "0.7.2": "2022-12-15T14:53:43.723Z", "0.7.3": "2022-12-20T17:41:43.938Z", "0.7.4": "2023-02-07T16:38:26.537Z", "0.7.5": "2023-05-09T14:39:19.384Z", "0.7.6": "2023-05-10T13:35:00.499Z", "0.7.7": "2023-05-16T15:53:40.523Z", "0.7.8": "2023-05-24T13:40:29.470Z", "0.8.0": "2023-08-08T14:53:13.314Z", "0.8.1": "2023-09-26T17:06:17.480Z", "1.0.0": "2023-12-19T14:50:22.184Z", "1.0.1": "2023-12-27T14:53:17.178Z", "1.0.2": "2024-02-28T02:31:43.779Z", "1.0.3": "2024-05-16T12:56:48.284Z", "1.0.4": "2024-05-17T13:01:55.435Z", "1.0.5": "2024-07-09T17:32:17.963Z", "1.0.6": "2024-11-12T16:27:30.653Z", "1.1.0": "2024-12-02T15:53:15.710Z", "1.1.1": "2024-12-03T08:15:40.888Z", "1.2.0": "2025-01-21T23:27:38.289Z", "1.3.0": "2025-06-03T14:20:45.568Z"}, "bugs": {"url": "https://github.com/laravel/vite-plugin/issues"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "homepage": "https://github.com/laravel/vite-plugin", "keywords": ["laravel", "vite", "vite-plugin"], "repository": {"type": "git", "url": "git+https://github.com/laravel/vite-plugin.git"}, "description": "Laravel plugin for Vite.", "maintainers": [{"name": "tayl<PERSON>twell", "email": "<EMAIL>"}], "readme": "# Laravel Vite Plugin\n\n<a href=\"https://github.com/laravel/vite-plugin/actions\"><img src=\"https://github.com/laravel/vite-plugin/workflows/tests/badge.svg\" alt=\"Build Status\"></a>\n<a href=\"https://www.npmjs.com/package/laravel-vite-plugin\"><img src=\"https://img.shields.io/npm/dt/laravel-vite-plugin\" alt=\"Total Downloads\"></a>\n<a href=\"https://www.npmjs.com/package/laravel-vite-plugin\"><img src=\"https://img.shields.io/npm/v/laravel-vite-plugin\" alt=\"Latest Stable Version\"></a>\n<a href=\"https://www.npmjs.com/package/laravel-vite-plugin\"><img src=\"https://img.shields.io/npm/l/laravel-vite-plugin\" alt=\"License\"></a>\n\n## Introduction\n\n[Vite](https://vitejs.dev) is a modern frontend build tool that provides an extremely fast development environment and bundles your code for production.\n\nThis plugin configures Vite for use with a Laravel backend server.\n\n## Official Documentation\n\nDocumentation for the Laravel Vite plugin can be found on the [Laravel website](https://laravel.com/docs/vite).\n\n## Contributing\n\nThank you for considering contributing to the Laravel Vite plugin! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).\n\n## Code of Conduct\n\nIn order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).\n\n## Security Vulnerabilities\n\nPlease review [our security policy](https://github.com/laravel/vite-plugin/security/policy) on how to report security vulnerabilities.\n\n## License\n\nThe Laravel Vite plugin is open-sourced software licensed under the [MIT license](LICENSE.md).\n", "readmeFilename": "README.md"}