{"_id": "lightningcss-linux-x64-musl", "_rev": "37-62a4444f3e7ab3397efe7cbeea14c5d7", "name": "lightningcss-linux-x64-musl", "dist-tags": {"latest": "1.30.1"}, "versions": {"1.14.0": {"name": "lightningcss-linux-x64-musl", "version": "1.14.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f354b94d96138850ccac86ee0ab16113aeeb10c2", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.14.0.tgz", "fileCount": 3, "integrity": "sha512-TI8tIaYv6SBpoBGatCG8gKQMxMCUcc+rvBSrqU7geHFBwzzyxOEHflENMclfwiYjTEM/HMdgJbWuHsIY3xvkuQ==", "signatures": [{"sig": "MEUCIQCw/6JbAEThKUGt6ngTg1Zn0NyK3X33YP+FqVihZaHJwAIgVV9FVjWxUuI4nCQ22zzlqVtzLZm+my3ZudeYowTKiU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3531107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGXtWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkNw//RVM85xQdtg1A9NFkrJUbEo9ZtneldZHtsbbReWqCZwVgDOOI\r\njOrT+z4b+tSBxWDwTb44M8kzC4IS9iZ/1BDM4ok1Sxf8dEsJcWPhOY3ehJfL\r\nJuz3xrZMHii936gbtT/qGPYzSVmHXu75aZgArtaZwF2urcqghBcsIz7Vn84Z\r\nQ4Wj0ACNgg/Auf+V31OJeHfS83Eox/9lmhWUGb5l7i8yfJs91VXwDuUk+Z7T\r\nefvr5bYy8tHl/dOANFl7zFSktaCTY2qWJT8irXAl5a8379xpRpcvK20XCB+U\r\nC9+a4MiKX0ST3x/DvAkemCWb39AjNBV9J6aM/ibw8l5vtMeJer5EsRMQrXAG\r\nmnO7FwNSgRVYzvAORJ3Wti6xTotoe6g/L4kP78utwhdHgnaDf4cQSGdHK3QP\r\nmmq3oosRhwor87/SpbTDlEzpULPv1UyGUM/ITnitAKtJVXlCeko5dtfugKpv\r\n0iXChq/qu6XE5ir8XY7+MwRR0xpdtBR4/IcqNXrLFNLoqcDnMCWPHlZSExip\r\nwqN1RzkUCKeaiyx9EuXs1osS3cnY3smhpIX9Ij5y3rKU9XJyDPiFMZKiOuxM\r\nzPlIXWdCPLWHRk534+fwkZ3vN3few++WV1T+nrOmWnjBI0E6NrPJL5/xu6Nw\r\nV/yWm+jn2iT63WnZqNNT2j9qGhFrlScjWig=\r\n=4AD+\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "2d17e0e1ed1482525985ef4d30809b4c06d76944", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.14.0_1662614358188_0.7937314536118991", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "lightningcss-linux-x64-musl", "version": "1.15.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "2a0b055bf4d472d1cccfbd072e7c418e974b4e5b", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.15.0.tgz", "fileCount": 3, "integrity": "sha512-9ylrbm6x3a6QYFdrHVl3nbVLgxSzh4IQT5PqjqVeaeP1XaC3tPu0bVoxkHai0o2rRFW+U0CjJI2+3VzZa/RaVA==", "signatures": [{"sig": "MEYCIQCpJzSjLO244rxxZfVvp/+mp0yxUTeoihIT4q21c4TO4wIhAOXT8eenPDq6Ox0YwvsGbZCs2OV38FZdPTEdV16HV7/s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3539346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIqchACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRSQ/8CjJTBVAiz8MPx0jqg/J3M8E9YGuSkn6QI7dT7nfzT5YpUbGO\r\nInr5QpABJptfPgRMTFN/d/2Qb2lPj9hKXDviz6nSupOcDjXTLHPL6HIThZ90\r\n1DZrcXb66Mq0IydZvl2adP+/5Z8//8OVRCoKJihzQdk5iI27iy1gpciZ0jHY\r\niXQu2djUxgEo9TXGqmr6GmAtiVAV+1VQe8oDUeAlRLINuWItyAjb7tBbTid2\r\nTykpaJ+xodi9icLNtwKe51i/ank13YCxlluAFJaEQa7cSwtNHMbxqEAZmL5k\r\nJs9jkc/eO004i5jzzAww4oOcrCIHPidkuKr+MttbSCAYKNk5UKIQwIsciZh0\r\nlLfJMppA6+Wn7svm3Qucgx0GoiCY0ip3WPep5zTZnggeFYCTMJw+QW+VIs7L\r\nFWEQu2m88c1/NdtwvPFFK6IV3cNPQm+T4C1lGK4xrZoMXjYblo4uwnpzo+k4\r\nlzaPwBYsUvE3YssFx1End5IzzV6CVTtKOJ87xcJyITtYAA+EgvE0OU1gEWLd\r\nnaT1C7K7IhTxXVCr3NMb3u8f1YmOJjVLL5CevOlGROc9dGK/kytPn5/IY0C7\r\nQ6c56r7gHC9GlbQfyRXNvlVM0Bx2VL94oXAldUuHWOblAhcCPRSoK/SnAxsO\r\nMHobf2sWqbDBCcMhlh4PO7CXFsfOWz1jDU0=\r\n=8vnJ\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "1a23835a0d72afc06ab5f98cbbee5fb03ae09074", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.15.0_1663215393368_0.43093982829757627", "host": "s3://npm-registry-packages"}}, "1.15.1": {"name": "lightningcss-linux-x64-musl", "version": "1.15.1", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.15.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ff4c88600e1f3839e7b68b1ab526788caa06db90", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.15.1.tgz", "fileCount": 3, "integrity": "sha512-n2cRPHxL57N+YMsBodF9HogieOicmlQVUCFM+RF0FOzayA8LdypacNWI3EDzbERWfkBAWtx2eLvV50DOJrkQdg==", "signatures": [{"sig": "MEUCIGz84lJrFcZREZKX7I5UyrRS6oA9t4g9x/gnzoKJBn8kAiEAwqyukQSPz8OED8I6Ddbe3px4YE0DyVPjjGwPwHIdOZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3539346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjI/JEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmro9Q/+M29nlPX0CQHR9kpe6JJ/KeqKTobdM3Y186x2xSbUjj8JLjfO\r\ns/UfihEY3K3MfC60pASALBd/4RqoxIjNTgq2mgGrIDXF1NiXG4CnZwcrQ7TZ\r\nk+bRzfLLaNYsDw/6flraBBNZE1g6LiNOvCh3kBJCQNeIfbRzrung10wvtmPp\r\nMTyGMtMbXj+WPqO+43myjGVxN1zD+cz126BD1PZPHiN0Wutmubpv6vaSAO2s\r\nlyEJu2Oyv7t25A9vKGVJeUnWZU1uQrIDDXoLGiZtTaeoELvU9ockR3GsQtsz\r\nd6WJ2gSaeupSUbfIVfL083W5Wc/apizlOh6ydEe9WhBZk7/1gYmL/vJ7qXRt\r\n2MyYqlATAVmbPWyEBYoiy0n5Q2NBJMpeShemcVIq2gxaQO+Y/XunTN1naUtt\r\nKaFzzVhVV4kKTMUen4D/TkxL/6W9Jw3MBL5iozUOTvibV3tnv20emUBREYyI\r\nUFo1uf4hW/4VDQJb+j3ghg6xzaPQUhEJHl9SaCo0izwdaQ+PVnYJkgG12es0\r\nOU2+BcuoYalxEH/XS3TIHEfRQSMgzQ9tP9fGisP4NcdIgaJzgy/pCXDymIUU\r\nKMs4xOdcDLe/C/sf4HGg4RdBitO2+WkQrhcS7Cwzix1cpPGcsmHzmHmxc6DH\r\nq/+7B5SdEAUl8DzMvzp8EsT4wtwe08kRy4A=\r\n=fgOp\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb66ece2f9b47bf4ef60848736dde697ec80a319", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.15.1_1663300164353_0.27312205791644684", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "lightningcss-linux-x64-musl", "version": "1.16.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "697d87448e0f6445b6fc3cf8529d89709f3bfacc", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.16.0.tgz", "fileCount": 3, "integrity": "sha512-HJsKeYxloEvg2WCQhtYPqzZUliLu9JBJNeI5y9cPQeDR/7ayGGLbVhJaotPtzJkElOFL/SaXsS+FRuH4w+yafg==", "signatures": [{"sig": "MEUCICH33NW4xPgfeuaDPRSoFnkwm9UvghiUpQtpf3Dw8FELAiEA9EtnVBrSQWVt1B651MFKYSuw/OSpYUo6+qJfDjaCZis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4501906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKTIkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDwg/8DFvYzLqtzdKqq6xW5BHbAY/djsDh6wuf1v9mSNGqY+zEGsld\r\nbz3h3TEUwszccBC5+rUrFDgBHXOrXxlCYUIVwB1dYqYQt0zW1/62cd7TVZaJ\r\nGDLLfVhISbuuC8FI2B6dmRzGwr3Tn9VMA1tZwICfn36QxAtPnv5pg4mJbO5B\r\nBLQ5NaphKs6ykidoAoDIjr0v+hz8o+/lpt8TulcLa+PVAckNN/mYj4BvYtdL\r\nSdHHSmdhPAIh6phNvU02zORXAvRzbpRwuy7NIe+lWl1M6GrjA4OyeycC5hLy\r\nMFQdlXdz61bHlN027hGawlwtAagYsYHHWGQU4TihfFdrGxjM3gyuZv7WJ/Ae\r\naHxq91Zoc1UGvn/9UcehyVk9YhztPKhiIZ825vpUoYBJeB4KjJ0Amt1woBRy\r\nKydrxbIoVi/eX9TKQ89lw03fmcLRPGkdn8QYMhF3yLVI91/nyitWrMODylSH\r\n0x990ywAOfyWR9k6Gsc8mdqIPACD2ZOvg8PPoKsW0a8SWbd0KL9aRBoz+GbR\r\nxDrHBqB8sD3kfaZZiYbZ15W0TRSs2p4TmvbJHnS05SU3Ky+0N0tiaF4GaQjh\r\nOP+zMW3Xw/sOoIGPl3pQfD454rvlB1bWbk+FPectQrngQP1+JgNS8mHCcrPL\r\nlWOEb8tt3dsW+F8yyxBS8Qr3ufw4OTmIjyI=\r\n=9bE3\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "200120e24aa31449703707db14ffef1bf40a7fea", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.16.0_1663644195946_0.49889662777952304", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "lightningcss-linux-x64-musl", "version": "1.16.1", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.16.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "20b51081679dd6b7271ce11df825dc536a0c617c", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.16.1.tgz", "fileCount": 3, "integrity": "sha512-VZf76GxW+8mk238tpw0u9R66gBi/m0YB0TvD54oeGiOqvTZ/mabkBkbsuXTSWcKYj8DSrLW+A42qu+6PLRsIgA==", "signatures": [{"sig": "MEUCIBgSMuRM7g75d7mBx6/ighNgcf8heh8VGkD+jUVfcxyMAiEAxkuMftRk393dAKG9n5pkPtRWjU+mit+tMbiMNYGQThs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4415842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZ/agACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4jg//ZQK0OPfTI3pEYpOp/0lAg9coR+VB0k7yGxDtluL7cDH0ozND\r\nZjat/tuykAbuO1ao+blPwKYPOApTlvuLXHyuIhc0slLyY0AgATeDPbXxgiw8\r\nzmA1KpUW0mbFTU66YmB/oyZsDYn4KrVj5FtZQOspKyamtsiGajSlcdHg9or/\r\nFquiCiZ4T7rIwgJjz+Ru8P3ozcs2WnD68i6VCQo5/crVidXDlNEpUclh545O\r\ndrXvQX/wb4uWdEW+s6cGM2SjmvyHGqXGw6MXCTNG8nZHZVr5NS8/IQm+Hbil\r\nUFdByfrL10BN7O95rD7fWojbDLHU5ukRTxx6N4We049yIbIC67tO956Hw9Gq\r\nwN0D35tmdMrQfgofU2JWPlane1fZREqH7DM+9KimlcIDdXj0A9nqoZazwV5k\r\nGWuaX1UFpxpTFx/nUYL2IHtOc0Hvoykukc96NZ1aUkqy4rdg5OWbrH0QilO/\r\nXyrR/mUo/l6vR4Ic/CqtN5YfA4Z1XvY0QqCN1bKIbCeMONLkzyQSC7x9x0UW\r\nlisDS6ovDxr4I6Et28tI1x0RdU/tFub/3xHUjoJKk1UC4vcVU8K4fzX2uMMT\r\nnJbPPcEy+qX24vGvJx9nXr1NDYZZwRsMIZOGYt1d7Afu9EaAJ1coc/QmOR+z\r\nAgNaHJYj65SOAzvS6kk9RlsgguBBg1VLfx0=\r\n=pWyI\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "87e808fd771374ae705886351ee62c4fac3d3630", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.16.1_1667757728453_0.8323081351658879", "host": "s3://npm-registry-packages"}}, "1.17.0": {"name": "lightningcss-linux-x64-musl", "version": "1.17.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "08607fb1827c719fa3a28d98aeeec92ccb4d64dc", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.17.0.tgz", "fileCount": 3, "integrity": "sha512-F4mZ9uPMD35wegJvyVOvfSuX+Xuu6N0kqNsw5o0cX0SVLEbYXh5xhXXgr/V1aS4VPGqqMR+HoSJV5FJ/g3DvEA==", "signatures": [{"sig": "MEUCICg7vbeRxwrjWI3F+a3pOC5jQ7uxWHhj6ApJm5CXrR2dAiEAoBFib/Hs7XQ8NRXDdTcDcIp50+b3fhYtldRQZz+Er/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5656930, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhje8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+DQ/6AoX3uhSj1ZtvAbwawh273mwljxWkGVukYy6z6LLMFEGeJQN3\r\nOzl0HVkfZgIPg1gA28MjJ01SKRR3P3TMS4ZgbWkynyHxMF/mQJxxFttJS2Ih\r\nFkMAX8JFNY9MQ5TIzUW7sfipLN+11eB6QGYpLaK3GeWPIr+sFtqWQhc36yZF\r\nU759pFLCZRyRcPfkNGIfGMqfSYeInhHLTRCZy28Wmhu9OSKwFtbDclg3qozX\r\nzC+BwEfN0jBL5hLPZYz3i6apdd9dgxzUv/rSGgY2VOvni87oUmiB9nqbl1mm\r\nmgpj3HPQy8Vi4F5PAVxBT4zKFygXKo0gaMKdp6hbR5aJ/zutvagmitn1LTBC\r\nggFSdMem4FedQ9O2tZVKgNe+j53I+XWOTWWTULwv0ytjaUHS5fgJeX9OOhMW\r\noDlMhcFc+qpS5x2I9O5glf+Lvdzuu/LFgKYGShixyJe2rb7weQuVM/2R7CDb\r\ne+gEdCFPHFo7+GwB/YsVueetVv++nBQqgLUEc999xssqXoocPPK0lrPqOqe8\r\nRex1J/AR83a0NmiOAwQDCII3R53RF648aPJ0jzcCK1x68YCDhmihz+hnu50I\r\nX1DhuTukDZ9JhnHS1j1aty2v6h6jttKjOPAhYd5digJT7PIpsIWteLDLGtG6\r\n/KKLGEgGdHlynTdr12ebFvvmDgnat7qnf6s=\r\n=XFR4\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d4853b204cc80b051d29be324e2d16f621e76336", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.17.0_1669740476365_0.3909136434653664", "host": "s3://npm-registry-packages"}}, "1.17.1": {"name": "lightningcss-linux-x64-musl", "version": "1.17.1", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.17.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "78765c58c111af43e7d311afa4713348ce9b2766", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.17.1.tgz", "fileCount": 3, "integrity": "sha512-ydwGgV3Usba5P53RAOqCA9MsRsbb8jFIEVhf7/BXFjpKNoIQyijVTXhwIgQr/oGwUNOHfgQ3F8ruiUjX/p2YKw==", "signatures": [{"sig": "MEQCIGbRCwAOfob7+tM2A0f4ARhOF+9pxeyVJZa5GfjHdZ4AAiA8zcLXUYHI73uojXRDUg5kHMGfueCBavkueXMsXfYt/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5661026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh5NGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5OA//dsBMB1qXzX+bJoOK+dMGZ34k16GjkeKhS6Zv8yGYnRgsWguz\r\nCUmFSs72nOn7rAl+ekCuMDqK/dZqZgktH2jaF0R1wG+4A8BL3UpAonpuIMzX\r\nEjygA6HW8xYISEe2Isw37IHhPpLghFj0DtSujJjBngs8toxltkSNofEyIOE+\r\nOjRZ/xDpQ8B4XSl7CpmKn+Ifc7dvqR2RuI1TmLG4wgxc64+Qsuou976om+AD\r\nqe/MiFz+vT3KsyJr4Frwzxpi5B0xCW/qRTewOJjVC9CHEIx7PmXlZQUXcfdb\r\nHTvqI5jmIh1Gi8jEZoRPTXTr8unSgIX3OW05H0y6YYyXc0hhMStBqccpFkBH\r\n7v8l6MLSMf5kOKCXnUB3k5E9YxX5wzj46wk9784RcGz+QS9QH8Gvb5oSnFQv\r\ngdXcPWfJgBbXFHHiHwR+Xu3iw6V7TyLyZEWD1FLY9jVTjIc/jHERqaoPz7iH\r\n/EvyRSAyIvexUJweKOSV1V3dx4o0RIPIFObnHPB1HdeOpoJ30tm1XCq1xq0o\r\nybV4kLycQePPzE4lNYxd1NZk9dQkYexKpHDf/G92ig8L9sEq9/qlAIXTJewb\r\nBR+b0wer/1IUY97FIB5AhgvdJRID+pK3ySKwKsNjtNlrZGmtdUFXYyR1DtK/\r\n0udNmfMWeRnn9rFCWVXBNytxG6zL+EgaLK4=\r\n=JU57\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "27cb9c2a382fd8d9702de8def9dd18db54280c43", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.17.1_1669829446371_0.1063916618776426", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "lightningcss-linux-x64-musl", "version": "1.18.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.18.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "4d84de26b8185aa42450e0f4c83bbfb5a36ae750", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.18.0.tgz", "fileCount": 3, "integrity": "sha512-na+BGtVU6fpZvOHKhnlA0XHeibkT3/46nj6vLluG3kzdJYoBKU6dIl7DSOk++8jv4ybZyFJ0aOFMMSc8g2h58A==", "signatures": [{"sig": "MEUCICceSDnePqNGfHaU+9kU3c8hwsZ3fkKSPapDbzpRtIbpAiEAo7xVDnbHxDQgpgrCZzdV8t4mrnzkhOKWhy8zSu2gyEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9617095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtbCvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrNhAAkQNSnDZxg7vj5lBbhpAoExUgGJMm4tWvbtc+COBcx3hY<PERSON>lo\r\nsDXoUcif7tVcgtgUuyReLBCoAVRwOw5yEzCkYEzPDvQS1gLFcLgzh5xA6Oa7\r\nSvX8xJ2f3Qs7yHhc7Kfix2Yt46rLhxeT8RAI44TMrDBgCzvHpys7jyC483xM\r\niB5hp/0fE17FjCflAoHIk4ZU5pi/3EbWlJX4hgaw37kodwJnkIug50+dVgJt\r\nQ22dkDhC1w/jN4HGu7XfQXBwEDTBJh3lceYOZz7AjT4Jnr3fVVq6sexe13ZX\r\nUGTZC7DcnTKWZHt9Bti3m0nL4zxRXwIG+k7/19oVsd2+ADIh5TJSs3vzFdNf\r\nRzc6EP8PXVOEKau1UBdqt86k2nXkmSlCiDKmIqksYbxAHtY7tLo8I8+40rEY\r\nIj6Usw44QpFFTQaZM0KfvS3Y4hlKnjiPzqctrtGc2COkqTHqMMdpRi1ABs43\r\nerbvgR1jJc1Muwf5oyRcqr64BhhyLpc7U6imgMZ4pch8/U2tUy/K4ktFjyLO\r\nFPCNJ9q805OAbo0QXeIIwsfd66+M3YrWHskaTSCKyXI3M/5xkVCOnARmbbkC\r\nvJ8N2iWPZO9LFSCtnMPgjYBgEOIn/8NvYJVER4SsqQI8T/dnnH2BccHrC9ex\r\n2mON6tpUIyfLm/3Ur3x9cbe3Sd+mvLe4RjM=\r\n=Z4Mr\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "6fb77292c9ef4320fa85a1e26fec3fbdbae3cf8a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.18.0_1672851631049_0.8508008832273286", "host": "s3://npm-registry-packages"}}, "1.19.0": {"name": "lightningcss-linux-x64-musl", "version": "1.19.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.19.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e36a5df8193ae961d22974635e4c100a1823bb8c", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.19.0.tgz", "fileCount": 4, "integrity": "sha512-SJoM8CLPt6ECCgSuWe+g0qo8dqQYVcPiW2s19dxkmSI5+Uu1GIRzyKA0b7QqmEXolA+oSJhQqCmJpzjY4CuZAg==", "signatures": [{"sig": "MEUCIQDNNRbi+gwLnQpVoc/QbRBFXq2i8FtteQ/XsW3cfywnMQIgGY0LGWcq0F3zCdF+Jk2aMKOmZIn9eimLySv0UERHwNs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9026575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6l5aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYMQ/7BHI5lup6B2iTkQxONxNjOqzSCq6ZU+wGMkFsihS4kjpgZaCt\r\nOVYZybe9cwNBpoUYt0Im55+y3eRt2pE1+zmRvLgMzSRoxWiGTt0e0+lPQE1S\r\nHX5f4ATacGD7u/tNhI76yeeHJfQkrAS36A2oaxfKr11H/z3I5Eh01E/myhIK\r\nFUuZ2cEIoJ3Lwito48UO2vpi7Q078M+/Inq1h1JBSNuT3rIrJRHuSMyLOUI3\r\n82NspL8YMHonXjZfwzmL5oWZRszAyhWGH3y+3LX90wsva+h/QUZr/VAY8GQS\r\n286+rYH4u9z+esltC4OGlTi8iHyCuum5ie0zTUbXtGF2FAZzEOJyTrNJkbJ3\r\nUk63kbUQNfHeGRcu+dmYjI9TeBrqWtwP+coC4gSayDCmjjCz4l8KXN6DfH9A\r\nJ8LorfseeNJlZhXQC1eGZZ2LhwiQZRDy2/tX9r1qvKHaoxb7GEwiYIXMSkDb\r\nUM+NdiLpr9Whdet2OCVQ/N99ZqrupQnLAClTLaLjf3WgkvkC7n7SuuPtYUb+\r\neO1WgLSxsFLwCJ9DHPmbJU2POK/YJz3xIHyZ23W4KpoX+Ag+HeAQmDVrJPl2\r\nyQlA1YxQP7tVG2f6NU1gb6nVP5guJp8sXUSncGbRTwZ9qnvC6UNHXD+XeOnf\r\nqq3JwyOwTAoFgOaxm5RRvD+9FIg4k+GTY1A=\r\n=xL76\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "05c23f1269321d4b072f6264a5c9cb6edd8a02bb", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.19.0_1676303962347_0.036518640907791555", "host": "s3://npm-registry-packages"}}, "1.20.0": {"name": "lightningcss-linux-x64-musl", "version": "1.20.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.20.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "aa21957fc51c363b4436e973911d01af4bed3c35", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.20.0.tgz", "fileCount": 4, "integrity": "sha512-EmpJ+VkPZ8RACiB4m+l8TmapmE1W2UvJKDHE+ML/3Ihr9tRKUs3CibfnQTFZC8aSsrxgXagDAN+PgCDDhIyriA==", "signatures": [{"sig": "MEUCIQDi47piSKICeuXGNWU31gBwwHBd/YqzXbJB4XhtJdS2ZgIgWx0/aOh9sF+0z7EAGYRFw6h4PadipehctM0XL2FfCzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9296919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQGRQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmraQw/+N0KC5XXqWtdKcirnrE2k6UjnaOuI+T/IYDZcDITQj/ZIJo9P\r\nGHpCJGbSvHI0rbgk+Kx1L0/BUUCNPKopIZbieCJciG8+Vti3hXwIZVPILcV2\r\nIHvqxWAsGjS0yvi3VfTxF2NnxHKpLi/Ohm6kMuBLuT2838FMvCOfS9OsG7xX\r\nxNPikO7w6ly51Ig+f9JdJ4HN9ZmLA/eUzIODS/cAOfs46ZawuQhcUAa+ChQ5\r\n4pmwbvsZ3a9hSvOEduHGNp3TIOtdZg53F3hdUb47g+KtSnGwajnrw9MWNpFW\r\nmuWa25DIX0TjbOHvgIUIlgMtbOVYbn87AEQO0Gy+0hWbhfJo/hN9FTpnwqo9\r\nk6i+Soa1FOxhGizZVr6tUIvK1ZwSuF/oABUCGsAQSAQ19o9fYK5GYBfc6BAs\r\n/qcl32NTwxjolu53H4ekNV4UkPrfMXj8rxZDxOJlncCMwAQD9v2O2E1oEQs1\r\nagxsECWRa4NYzAdw8K86C0GfU92n7gqSfR0KgO+sNMTJ01ZY9kam2itf/nY2\r\nuwbLUweuAEIfgoMny88mqqQLXWIaYFrji9ts8MUR4rG+Z6xTtBCnMhbEqjkz\r\n12eSPKTfrPd2SdDug1Ofk534HnXjP3lPiLDuHRq+LG42V0H5MrTKzXeJTVIL\r\ntQvtr5j9jjPLDxvP6l3jCyEcdJ0GovYFZkE=\r\n=hxPA\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4028d4d9026750b31b813ecb4ca5387dba8dd396", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.15.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.20.0_1681941584368_0.018983243716567344", "host": "s3://npm-registry-packages"}}, "1.21.0": {"name": "lightningcss-linux-x64-musl", "version": "1.21.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.21.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d1f7f43e9744959a2ba2996488989d7b9cb06f85", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.21.0.tgz", "fileCount": 4, "integrity": "sha512-S51OT7TRfS5x8aN/8frv/JSXCGm+11VuhM4WCiTqDPjhHUDWd8nwiN/7s5juiwrlrpOxb5UKq21EKDrISoGQpw==", "signatures": [{"sig": "MEUCIGz5dM/2ISNwtxckZ1anxJyNrYrHI6s08spcoWFjgsH2AiEAmGOotGxXWLKIEfCyPruEBDfHyoEt5/NWEkdoCLBq7tc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9600023}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "11f4179f81041e14a04536d3bb0618f06de4e454", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.21.0_1686117504468_0.5877014592563283", "host": "s3://npm-registry-packages"}}, "1.21.1": {"name": "lightningcss-linux-x64-musl", "version": "1.21.1", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.21.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6ed62f9e0f2dc9361edaa722836bdf495e28e2de", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.21.1.tgz", "fileCount": 4, "integrity": "sha512-eA2ygIg/IbjglRq/QRCDTgnR8mtmXJ65t/1C1QUUvvexWfr0iiTKJj3iozgUKZmupfomrPIhF3Qya0el9PqjUA==", "signatures": [{"sig": "MEYCIQDv5QuBKwxmtJmcDcws/4riZjXXA9LJWSz+7harJOK8owIhAJkAokRi+dBI2I2Fhx4vvZweQm2vvTll/b5LDwrL8vdu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9604119}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a36c105af3bb83370f64e28a8cf98b7dea52a6e0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.21.1_1687660744284_0.8104473384333695", "host": "s3://npm-registry-packages"}}, "1.21.2": {"name": "lightningcss-linux-x64-musl", "version": "1.21.2", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.21.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "b2df1dffd62e02e78ee80019ef3357aad501951b", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.21.2.tgz", "fileCount": 4, "integrity": "sha512-1w+oiZY3H1V/5FxpvtEUXHBZFuAPzUFoFolpDKNDphr9h9xPo3xc2eg3zuz96ia+tb2QmBB5S3QZrQ/dGGc8GA==", "signatures": [{"sig": "MEUCIQDbkwVTAqBGp8zt0MiqXzQgAkfu4rURpnUuLlscyXeRTAIgX+xLsaM0nJfcMBhjV+lpEjUc59vZkHw/7+cAA9CygI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9616407}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "e4963a58a813e02b47a7dd0320defcf0c4182512", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.21.2_1688266105475_0.9214448393456678", "host": "s3://npm-registry-packages"}}, "1.21.3": {"name": "lightningcss-linux-x64-musl", "version": "1.21.3", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.21.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6d5291323d036df5020c89e0fcec285684b1cbf8", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.21.3.tgz", "fileCount": 4, "integrity": "sha512-9xQ8B6en2lHqR3Iu7W4PdV7914zBQ18Z+LxDveUzfqCiSCN79Y+4JPpNVYJ9DipUERjWKz0k7wQeEUOXpPy8qA==", "signatures": [{"sig": "MEQCIGIHSKL+rGIPSPstkudrwZevTLLp+zC8zA8bJfNnfLnDAiBdtWK//kuCAiiUj63mI4ZRrrNgl87nVxWVriwrMV9CNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9616407}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "83bab71e80781b4d2b128f5938099b2f0c8239bd", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.21.3_1688397358563_0.2608709022983071", "host": "s3://npm-registry-packages"}}, "1.21.4": {"name": "lightningcss-linux-x64-musl", "version": "1.21.4", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.21.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ef6697f3e29a1c3278417f1d141bf96ce72b2844", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.21.4.tgz", "fileCount": 4, "integrity": "sha512-R/fT1c8g8xrrCeU1fSErUb31OBMR38KcEDmrwCmxzsSntJ7o9YkZdbVx1FWRqBy8A8tNHm3MyQqZzU/owTJszw==", "signatures": [{"sig": "MEYCIQCDOfTO1fdgGY4i2zluPDXIJq7BreL081T2bpczhQ/ZCgIhAL/3I6kUj+GXU2OLQQxDncSU6EIjbjaqySOG9ehQwTjh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9616407}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "bd517fde245dcce75a4b2eb21a25b147d54eb64a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.21.4_1688441882440_0.9719523781328396", "host": "s3://npm-registry-packages"}}, "1.21.5": {"name": "lightningcss-linux-x64-musl", "version": "1.21.5", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.21.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "cde2e9e1958cc7782264622983a2433312526b55", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.21.5.tgz", "fileCount": 4, "integrity": "sha512-Ib8b6IQ/OR/VrPU6YBgy4T3QnuHY7DUa95O+nz+cwrTkMSN6fuHcTcIaz4t8TJ6HI5pl3uxUOZjmtls2pyQWow==", "signatures": [{"sig": "MEYCIQDdZY8zA+QMvBu7BSjsUcJLwcCQ85+h8c01vSfRv+fVdAIhAPs2OKaz5M4nX8tLXBLG2JU9hH8Ffj263i6A8CoWqbq1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9616407}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d44a73c3c000ccebbe8355b7cf9272236e573b4d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.21.5_1688531424758_0.3888110757611858", "host": "s3://npm-registry-packages"}}, "1.21.6": {"name": "lightningcss-linux-x64-musl", "version": "1.21.6", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.21.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "bfca2e3a7e5c0c81e40d222601c127529260ba48", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.21.6.tgz", "fileCount": 4, "integrity": "sha512-m5VrLGfAuJ2axk6bgPzOx5XBVxOGw5DTGySIoN7QSlFauFAwcGN/5iZbM6Gil0F7pY1Xy2iuouaMxs620zvlLQ==", "signatures": [{"sig": "MEQCICIVnZHCMoxd3jeNyTvW6lah/DzNZnkSoEFoSCqbUVGvAiBHIs+lymkjlSRrxVkSO/GIUEVx7WBXxOp+BM3/qGg5LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9624599}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f485eb51d13e677a674cb10e170c71138446b71d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.21.6_1692511459347_0.20514959083625328", "host": "s3://npm-registry-packages"}}, "1.21.7": {"name": "lightningcss-linux-x64-musl", "version": "1.21.7", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.21.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "c721e301c3d8dd436de14ae98f16bcd8585ca537", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.21.7.tgz", "fileCount": 4, "integrity": "sha512-A+9dXpxld3p4Cd6fxev2eqEvaauYtrgNpXV3t7ioCJy30Oj9nYiNGwiGusM+4MJVcEpUPGUGiuAqY4sWilRDwA==", "signatures": [{"sig": "MEQCIE2CM1mX+yyyGTHsyEfpcElzZsOtjHqdHjza+RfKuwqKAiADdfv6fTIAdsLO3sgccOAHlGyI2IgUbyVIUryyePoTyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9624599}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "393013928888d47ec7684d52ed79f758d371bd7b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.21.7_1692555057476_0.7626471020580969", "host": "s3://npm-registry-packages"}}, "1.21.8": {"name": "lightningcss-linux-x64-musl", "version": "1.21.8", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.21.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "19787f71eeabdcec34af6e74509a2902548d45f9", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.21.8.tgz", "fileCount": 4, "integrity": "sha512-TYi+KNtBVK0+FZvxTX/d5XJb+tw3Jq+2Rr9hW359wp1afsi1Vkg+uVGgbn+m2dipa5XwpCseQq81ylMlXuyfPw==", "signatures": [{"sig": "MEQCIEYEhxlG9wRi9az7VDC2uZsDYZhEF7Y22mCRoS5ls8T+AiAmcfeggzD+hcj1RpKFbRW7s8GglLZYE5Kv1gN2fBAGhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9624599}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81cb0daee30dad924b14af7fbc739ddfe31d7f9e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.21.8_1694407641806_0.8434105261444715", "host": "s3://npm-registry-packages"}}, "1.22.0": {"name": "lightningcss-linux-x64-musl", "version": "1.22.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.22.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "1d34f5bf428b0d2d4550627e653231d33fda90f9", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.22.0.tgz", "fileCount": 4, "integrity": "sha512-t5f90X+iQUtIyR56oXIHMBUyQFX/zwmPt72E6Dane3P8KNGlkijTg2I75XVQS860gNoEFzV7Mm5ArRRA7u5CAQ==", "signatures": [{"sig": "MEQCIBpkB0r+vNcbUu4T9EvXe+CPV138KDda2nYYKoYHkN3BAiA37+QMvhDTVwqig/HH1peK0wHnzFcZCNaVfibBUYskCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9624599}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "7ff93ca5c69ba9df415e1e2319d275e2cec249d7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.22.0_1694990950252_0.4689213177997096", "host": "s3://npm-registry-packages"}}, "1.22.1": {"name": "lightningcss-linux-x64-musl", "version": "1.22.1", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.22.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "e713e56798f8a50df3e3f285ef102191a01ef951", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.22.1.tgz", "fileCount": 4, "integrity": "sha512-ZgO4C7Rd6Hv/5MnyY2KxOYmIlzk4rplVolDt3NbkNR8DndnyX0Q5IR4acJWNTBICQ21j3zySzKbcJaiJpk/4YA==", "signatures": [{"sig": "MEUCIBFxhHXPxfQ+xQv/c3dAP2xgPTGtyNSskcW+1NPvdCgQAiEAjk19W+fLPI/gvRaSPI/KHvcP8cqvfc1sn+F4gP0gVxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9636887}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4994306f8f8d98a2b37433fced50efdacbbab901", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.18.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.22.1_1699395822403_0.34127847929868316", "host": "s3://npm-registry-packages"}}, "1.23.0": {"name": "lightningcss-linux-x64-musl", "version": "1.23.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.23.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "ad65b5a944f10d966cc10070bf20f81ddadd4240", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.23.0.tgz", "fileCount": 4, "integrity": "sha512-G9Ri3qpmF4qef2CV/80dADHKXRAQeQXpQTLx7AiQrBYQHqBjB75oxqj06FCIe5g4hNCqLPnM9fsO4CyiT1sFSQ==", "signatures": [{"sig": "MEUCIQCi2dAhgyqmpll8R1mPo4px/NauUoDZ2wXd/bLXZmb96wIgVhKX5wzsUwHkcM7JCiWkY/E+iY3eA54+DT5cEnfemPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9681943}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b47f496a86075adcb6719aee3a8867e749a880b9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.23.0_1705276081209_0.47973666538907267", "host": "s3://npm-registry-packages"}}, "1.24.0": {"name": "lightningcss-linux-x64-musl", "version": "1.24.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.24.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "26edd10bcbd95833c602d3c4b71b628b26603d62", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.24.0.tgz", "fileCount": 4, "integrity": "sha512-HI+rNnvaLz0o36z6Ki0gyG5igVGrJmzczxA5fznr6eFTj3cHORoR/j2q8ivMzNFR4UKJDkTWUH5LMhacwOHWBA==", "signatures": [{"sig": "MEYCIQCxCVpRzn0z7aa0QpqC3SQEu+59HN40640FHR5c+nNJGwIhAJmHGYoWnmnsf3UnG1LEEDd2v+6PUsLiTEnMs5LwAj+Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9768047}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "30b9d79b3d277dfb90d90d78cd9182ac755ad6cf", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.24.0_1708648899842_0.792963212313025", "host": "s3://npm-registry-packages"}}, "1.24.1": {"name": "lightningcss-linux-x64-musl", "version": "1.24.1", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.24.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6ed1714737e4af2249ed10f431bc8137bd6cc4c7", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.24.1.tgz", "fileCount": 4, "integrity": "sha512-HLfzVik3RToot6pQ2Rgc3JhfZkGi01hFetHt40HrUMoeKitLoqUUT5owM6yTZPTytTUW9ukLBJ1pc3XNMSvlLw==", "signatures": [{"sig": "MEUCIQC41pETCVwmjbMEXQawien+2LNFZ/AmjGub6qaOl+VRKAIgLQy2H5kY/LdQk9q8eulZWiWLVeLMkmFP6ExcehQFElI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9489151}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "baa1a2b7fa52eeb3827f8edcc2e14de33cd69ad0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.24.1_1710476591647_0.9367697055116657", "host": "s3://npm-registry-packages"}}, "1.25.0": {"name": "lightningcss-linux-x64-musl", "version": "1.25.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.25.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "1029ba0585ed5405e5518b8c3acadb867f313b61", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.25.0.tgz", "fileCount": 4, "integrity": "sha512-7ssY6HwCvmPDohqtXuZG2Mh9q32LbVBhiF/SS/VMj2jUcXcsBilUEviq/zFDzhZMxl5f1lXi5/+mCuSGrMir1A==", "signatures": [{"sig": "MEQCIG6nJYjaaEXpQD81Uh6jFNAZy402YhOqzpDUfy7oKsTPAiBPCUvCSdrps3WTYmMk+Alvsj/xdv6+swqY/2Lpl3XbUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9644799}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81d21b9f5201c6eb8365fbb4fa81cbd947c3474f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.25.0_1715973765125_0.645910598397849", "host": "s3://npm-registry-packages"}}, "1.25.1": {"name": "lightningcss-linux-x64-musl", "version": "1.25.1", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.25.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "6f92021bae952645fe297bea10467c3cccba0138", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.25.1.tgz", "fileCount": 4, "integrity": "sha512-TdcNqFsAENEEFr8fJWg0Y4fZ/nwuqTRsIr7W7t2wmDUlA8eSXVepeeONYcb+gtTj1RaXn/WgNLB45SFkz+XBZA==", "signatures": [{"sig": "MEUCICbfCvpEUFATV+LOB4mrzBrIXjT+IgSqWPlhDsAdtybiAiEAtGSVAHnLpEBmEt8lHZpwtjDEdLBKNrDpcWxmAM7fKHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9652991}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "fa6c015317e5cca29fa8a09b12d09ac53dcfbc77", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.25.1_1716617188237_0.8113967904833541", "host": "s3://npm-registry-packages"}}, "1.26.0": {"name": "lightningcss-linux-x64-musl", "version": "1.26.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.26.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "831cf950d824e88d6e00f3fc2f33ef6f50850004", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.26.0.tgz", "fileCount": 4, "integrity": "sha512-yX3Rk9m00JGCUzuUhFEojY+jf/6zHs3XU8S8Vk+FRbnr4St7cjyMXdNjuA2LjiT8e7j8xHRCH8hyZ4H/btRE4A==", "signatures": [{"sig": "MEYCIQC1Lu6YpkvBHTqjp98JvkyCvCBcToBoc1eUnskSUBGW1AIhAK0+vfvAExbiEeP5fMu9cBrO9Zuoj7aWpqN+nD0WT31b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9534207}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "0bcd896e81a8a2c5d847f626a37e2cffea79e2a0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.26.0_1722958379161_0.2990552523995502", "host": "s3://npm-registry-packages"}}, "1.27.0": {"name": "lightningcss-linux-x64-musl", "version": "1.27.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.27.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "247958daf622a030a6dc2285afa16b7184bdf21e", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.27.0.tgz", "fileCount": 4, "integrity": "sha512-QKjTxXm8A9s6v9Tg3Fk0gscCQA1t/HMoF7Woy1u68wCk5kS4fR+q3vXa1p3++REW784cRAtkYKrPy6JKibrEZA==", "signatures": [{"sig": "MEYCIQDc/GqFPhP2aw/fvFTiwS3ud2M5yRQqMqG1Dw0X1oxxdQIhAJkkaGxB2Iy5q5WIWAGwXWkE+q2A1Jqye3nA49nVNoSB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9538303}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb49015cf887ae720b80a2856ccbdf61bf940ef1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.27.0_1726023649777_0.26567657332780215", "host": "s3://npm-registry-packages"}}, "1.28.0": {"name": "lightningcss-linux-x64-musl", "version": "1.28.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.28.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "2a3e2218afc4101e08167cd8856590613841cc66", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.28.0.tgz", "fileCount": 4, "integrity": "sha512-dpRsBXlrGVoWbJXnIt4Z7M75EnffNpFNO9gEwMTDrZvob2/a04TBrhEe4wnPxajIbrY1InVlg7Qb7ohevDpBGg==", "signatures": [{"sig": "MEUCIQCR9GGgDTE3AsZNxG9bKxuAl5a/L2QFI9RAF/txVd9SNgIgNdnq+CaoYpV2HuCDq8mrmFqrcuZwXBQtB0Tu5JmQDoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9554724}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "8a67583105757e4a25378d65d243b87a345b2c2d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.28.0_1730668671053_0.011242681606860483", "host": "s3://npm-registry-packages"}}, "1.28.1": {"name": "lightningcss-linux-x64-musl", "version": "1.28.1", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.28.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "f1a9d0cafc1eb7ec72ef4f2a3a81b5631060c461", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.28.1.tgz", "fileCount": 4, "integrity": "sha512-IHCu9tVGP+x5BCpA2rF3D04DBokcBza/a8AuHQU+1AiMKubuMegPwcL7RatBgK4ztFHeYnnD5NdhwhRfYMAtNA==", "signatures": [{"sig": "MEUCIHX4puqh4RFwo4xGPQr+FdXw/aiaPZ1N9ixgPK0zqU7IAiEAyWdACCrR7tw3xk0W2PuUf9GJaMtZ/VFST9Ts+vnk2q4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9550628}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a3390fd4140ca87f5035595d22bc9357cf72177e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.28.1_1730674707654_0.07753319651709512", "host": "s3://npm-registry-packages"}}, "1.28.2": {"name": "lightningcss-linux-x64-musl", "version": "1.28.2", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.28.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "13ce6db4c491ebbb93099d6427746ab7bff3774f", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.28.2.tgz", "fileCount": 4, "integrity": "sha512-alb/j1NMrgQmSFyzTbN1/pvMPM+gdDw7YBuQ5VSgcFDypN3Ah0BzC2dTZbzwzaMdUVDszX6zH5MzjfVN1oGuww==", "signatures": [{"sig": "MEUCIQDciO6yGEx6TD6eaGAnowAAvVs97Ulp9O0Mf1WnM7GNNQIgSQkoBzLC1gdMHoAgyNKDAgjH8VfozuYxdHsa37m0BD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9554724}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "9b2e8bbe732d7c101272ddab03ac21b88bf55c4a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.5", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.28.2_1732512298636_0.621186390912218", "host": "s3://npm-registry-packages"}}, "1.29.0": {"name": "lightningcss-linux-x64-musl", "version": "1.29.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.29.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "5abe2483fb85762d6758e6a999dc62d1d5d76ac9", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.29.0.tgz", "fileCount": 4, "integrity": "sha512-1TLtr1/3Xqf/ZjWmYilSQSUOOO1gSOZmMTYHMalX0O6dLY5v0kO1muFWrueC1QEb46/7tiI2ko59a9m1S2pXqQ==", "signatures": [{"sig": "MEYCIQDNVLAeM7+LmvqP7IhBnPJ8mTq/mD8ey5H44GSGXziLlQIhALAHEKsu+nvdq9iPoKcqIB0egEYQZiZLvv96oUEJWPYW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9153300}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "222fa9f7ad8b4004afd7a30c2f99cab3c8621191", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.29.0_1736401669939_0.8274739110159786", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.1": {"name": "lightningcss-linux-x64-musl", "version": "1.29.1", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.29.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "fb4f80895ba7dfa8048ee32e9716a1684fefd6b2", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.29.1.tgz", "fileCount": 4, "integrity": "sha512-L0Tx0DtaNUTzXv0lbGCLB/c/qEADanHbu4QdcNOXLIe1i8i22rZRpbT3gpWYsCh9aSL9zFujY/WmEXIatWvXbw==", "signatures": [{"sig": "MEUCIHOOFxFewSnQAiEgI1Dyu1J4BEuVIwqgNOeFZwshemnfAiEAkkF7SvnB2b87rIF1paKopZ1dh5FFMd20gXlH9G2c/ME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9153300}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b10f9baf8878411bf2b09dfe8d64ba09ef7a4eac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.29.1_1736445765529_0.9481263365954566", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.2": {"name": "lightningcss-linux-x64-musl", "version": "1.29.2", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.29.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "2fd164554340831bce50285b57101817850dd258", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.29.2.tgz", "fileCount": 4, "integrity": "sha512-rMpz2yawkgGT8RULc5S4WiZopVMOFWjiItBT7aSfDX4NQav6M44rhn5hjtkKzB+wMTRlLLqxkeYEtQ3dd9696w==", "signatures": [{"sig": "MEYCIQD3TdeVeskfWba4eiNAdvemRySLXpZ89ihALseltIOs7QIhAMnMQvxlbbtU1okUqy1dyGqERLXCAY3wSJtjPdLdcXRk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9157359}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f2303df29448a55c5f4f284f747eb53760769fe4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.29.2_1741242125463_0.4357489586099248", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.3": {"name": "lightningcss-linux-x64-musl", "version": "1.29.3", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.29.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "d546ba32ad49ad30754abb9b4213f125f212a8bb", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.29.3.tgz", "fileCount": 4, "integrity": "sha512-3pVZhIzW09nzi10usAXfIGTTSTYQ141dk88vGFNCgawIzayiIzZQxEcxVtIkdvlEq2YuFsL9Wcj/h61JHHzuFQ==", "signatures": [{"sig": "MEUCIQDQAOUvtEd0oOtWmMgxqcDFR0Ze0qfs0JemUTbbmlvFvAIgDS6A3iNdxnb29krFZoISfmSNycLj9NUEbJftz6cbksg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9177839}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "80eb8617c5f8f5519ed85bd3eb6d8953f4d32493", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.29.3_1741974601549_0.9573147143314524", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.0": {"name": "lightningcss-linux-x64-musl", "version": "1.30.0", "license": "MPL-2.0", "_id": "lightningcss-linux-x64-musl@1.30.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["x64"], "dist": {"shasum": "7ab47aca4c339f701a6e6461cb15684707b8d41b", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.30.0.tgz", "fileCount": 4, "integrity": "sha512-e/nHeX5SAEcfAzyLob5H1Jhm8uHLKwpOIHzcURKnXTMFdBqIDOsETMhmcB5AGDqsr6Q5D9u0QVswDdRo+btSgg==", "signatures": [{"sig": "MEUCIQDTsanMjMbrkkqFrGK1Yn5/+SdkQiVUWyxvZ4S62Gg18gIgaCfhm+fTifR/wQZFjGhM+TA05PbUIgJ8lo3qgw1l26A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9235183}, "libc": ["musl"], "main": "lightningcss.linux-x64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "474c67c046b4a54217166057296fa4ca28764858", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-x64-musl_1.30.0_1746945558368_0.9542461641593458", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.1": {"name": "lightningcss-linux-x64-musl", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.linux-x64-musl.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "resolutions": {"lightningcss": "link:."}, "os": ["linux"], "cpu": ["x64"], "libc": ["musl"], "_id": "lightningcss-linux-x64-musl@1.30.1", "gitHead": "f2dc67c4d3fe92f26693c02366db1e60cae0db27", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-rRomAK7eIkL+tHY0YPxbc5Dra2gXlI63HL+v1Pdi1a3sC+tJTcFrHX+E86sulgAXeI7rSzDYhPSeHHjqFhqfeQ==", "shasum": "66dca2b159fd819ea832c44895d07e5b31d75f26", "tarball": "https://registry.npmjs.org/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.30.1.tgz", "fileCount": 4, "unpackedSize": 9235183, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDbW6J3oE4hpXC0GpHVLwsxHdp9WoWgkNOKCXPCfLx5OAIgbPyOU9KuIKQDijwiWcPvPo9VE4threNAfAiHWFCvOLg="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss-linux-x64-musl_1.30.1_1747193931297_0.9248362235084531"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-09-08T05:19:18.112Z", "modified": "2025-05-14T03:38:51.761Z", "1.14.0": "2022-09-08T05:19:18.480Z", "1.15.0": "2022-09-15T04:16:33.709Z", "1.15.1": "2022-09-16T03:49:24.612Z", "1.16.0": "2022-09-20T03:23:16.223Z", "1.16.1": "2022-11-06T18:02:08.718Z", "1.17.0": "2022-11-29T16:47:56.627Z", "1.17.1": "2022-11-30T17:30:46.751Z", "1.18.0": "2023-01-04T17:00:31.343Z", "1.19.0": "2023-02-13T15:59:22.646Z", "1.20.0": "2023-04-19T21:59:44.656Z", "1.21.0": "2023-06-07T05:58:24.708Z", "1.21.1": "2023-06-25T02:39:04.610Z", "1.21.2": "2023-07-02T02:48:25.748Z", "1.21.3": "2023-07-03T15:15:58.924Z", "1.21.4": "2023-07-04T03:38:02.815Z", "1.21.5": "2023-07-05T04:30:24.959Z", "1.21.6": "2023-08-20T06:04:19.612Z", "1.21.7": "2023-08-20T18:10:57.814Z", "1.21.8": "2023-09-11T04:47:22.126Z", "1.22.0": "2023-09-17T22:49:10.563Z", "1.22.1": "2023-11-07T22:23:42.797Z", "1.23.0": "2024-01-14T23:48:01.450Z", "1.24.0": "2024-02-23T00:41:40.203Z", "1.24.1": "2024-03-15T04:23:11.930Z", "1.25.0": "2024-05-17T19:22:45.414Z", "1.25.1": "2024-05-25T06:06:28.493Z", "1.26.0": "2024-08-06T15:32:59.415Z", "1.27.0": "2024-09-11T03:00:50.141Z", "1.28.0": "2024-11-03T21:17:51.351Z", "1.28.1": "2024-11-03T22:58:27.966Z", "1.28.2": "2024-11-25T05:24:58.910Z", "1.29.0": "2025-01-09T05:47:50.141Z", "1.29.1": "2025-01-09T18:02:45.828Z", "1.29.2": "2025-03-06T06:22:05.728Z", "1.29.3": "2025-03-14T17:50:01.875Z", "1.30.0": "2025-05-11T06:39:18.639Z", "1.30.1": "2025-05-14T03:38:51.591Z"}, "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "license": "MPL-2.0", "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "description": "A CSS parser, transformer, and minifier written in Rust", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "This is the x86_64-unknown-linux-musl build of lightningcss. See https://github.com/parcel-bundler/lightningcss for details.", "readmeFilename": "README.md"}