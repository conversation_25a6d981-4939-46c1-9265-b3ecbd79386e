{"_id": "vite-plugin-full-reload", "_rev": "15-c3f5f0fdd1dcedabeaf3edc6a1d69165", "name": "vite-plugin-full-reload", "dist-tags": {"latest": "1.2.0"}, "versions": {"0.1.0": {"name": "vite-plugin-full-reload", "version": "0.1.0", "keywords": ["vite", "full", "reload"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "_id": "vite-plugin-full-reload@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "dist": {"shasum": "a966df73baa4471294a9177a4372e5356a4f262e", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-XlVCVvtmAWTGFC0OTVDh1B0/Ka8vtStct/komfjNugP9ZpFKznHLPBT63WKTg+yzxhVPjbkyREmNIIPgJIeJXg==", "signatures": [{"sig": "MEQCIH7HEqpXjy73jiZcmT9Kd51z6IMB1v0P42SSpmUlKk70AiBYhZYBcj4F4gxUkzI0bVADVkBlD2vjEnXzONbp6DPCuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUUT+CRA9TVsSAnZWagAA8a8P/jWksSuzYlpRni6PiKrC\nvUzHAsxsiYAz0uxdbG20lbcrpabAkcXlO4AAA7/ZtO3YkLK3tB2ryh5kZTo3\nfRKmWLzXGrgKvXNUep8dMtXyWUAYHtWPbfo4uNJqftspOFfLN5KVxjipfW6h\nA5kN+NeWb1jOKn7S4c/83BOxyu3HkW3n+k2CfnJ5JtJWfqPlJpFJDWZ64SLS\nBFef9lyAUrptN7lU057o8krw0B2dFU98u/jnTVCpldkaJBnE88d4JHhDHJx4\nVIwQpC9EdZGnJROETa03J6S+OTqe89sgweIvN8vTjuf7Vq2TKhxBisBWdxBR\n0zWBsL+DqT5v2k7mKEFDnM0MfY/28hj34VrYAOUT5HdGAFraJpWnpZgdYvHu\nbwe4G9E62D+DexBmB5tdf8Ls7ICVLVahjqdwawILHLPu37I+KBAiMkoVG1iZ\nabf6ACcumsA7GCTGxGVp7cFzNhP7aVfZnnp6jBC6k+F3xxwdb6QL1BjJrESs\n3IgGMHBrOLgwzT/hd7vRNcTy395DX8HBgGLpTo/YMKK3qh5Doa46pTt+I2nM\nUoYwlsW2+pO+tR9yypwee2OVvMog57EhNksCBKylXwPlJ091X9w77tsShR1+\nCFtb0wPXkAQyx+2ddhyhQYvI6AKlRfv5Y9z70Qe4h4DDRXuwumXWUeuUIbAa\nBjT3\r\n=WUE9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.mjs", "gitHead": "5e72833a55ce52d8fff50442fca5ae60e9963c90", "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm", "clean": "rm -rf ./dist", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prerelease": "npm run build", "postinstall": "husky install", "postpublish": "PACKAGE_VERSION=$(cat package.json | grep \\\"version\\\" | head -1 | awk -F: '{ print $2 }' | sed 's/[\",]//g' | tr -d '[[:space:]]') && git tag v$PACKAGE_VERSION && git push --tags"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git", "type": "git"}, "_npmVersion": "7.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "15.2.1", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^4.8.3", "vite": "^2.1.1", "chalk": "^4.1", "husky": "^5.1.3", "eslint": "^7.22.0", "rollup": "^2.41.4", "chokidar": "^3.5", "typescript": "^4.2.2", "@types/node": "^14.14.35", "lint-staged": "^10.5.4", "@mussi/eslint-config": "^0.5.0", "conventional-changelog-cli": "^2.1.1"}, "peerDependencies": {"chalk": "^4.1", "chokidar": "^3.5"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_0.1.0_1615938813386_0.24749497083662209", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "vite-plugin-full-reload", "version": "0.1.1", "keywords": ["vite", "full", "reload"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "_id": "vite-plugin-full-reload@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "dist": {"shasum": "e1105153dcd8ff78b4e86f93405cea887db8b027", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-0.1.1.tgz", "fileCount": 7, "integrity": "sha512-z+F4ZZzkFOfAMgKBtvmWiLOvj/oKC74oDNonG9+9hjK8gSMNN/tX4u5290sa2bu1nDFWDIXkTJIMiFtvkblyeg==", "signatures": [{"sig": "MEYCIQCPlo3EnGF9A2e/oDYojq6nGwLXwz7kZj5IBNE035jWzwIhANH2UIJKnI+kdf40VdDMfYaagjUdk0gWczSu5pHuYpgm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUUrfCRA9TVsSAnZWagAAOB4P/3Hww4YXgr8fTjGrES6d\nSHdKBEhPL4BNyx3Qm7u8fjJj0Ah3X2O542E2+PoS3NXAbaLGuKJtbaSxsRT7\nnMbx2Fo04oCes9nLTsf2lFsoG6ahSQkzFuMa2TEetMNf10GuByp4xzK1A/IO\nwLaun3sSRsVsgMrW11UhvCUAoIVDqmL2O46aOKa3abTwBLApBwAeZETbeoDu\nZI11LHlKH4Gscwur7+yaffTXF+k6uJzF8+CqtwPXCn5+k5iVBY2oYf6pWkBM\nHqutO+wy2/G0IqcfV3iuMUg8SuWnu/AOnHDZUz56Vyw3LV6Jh8G243Q3gwF2\nmNnBlUP+fLBEGGR1DSAE9FMf0xe6lynFijRL+NeBIU0gNnh7vHL9iUBJDJ7O\nVDiw+tGmajgdYq4Qet5+CEQe/Cz18akZ941MxbF7qqpxIm07Q80+bUQz16pe\ns0BjPBvFDE6uqZZ50/Ti1DnnNXi2orcL5frCA5YdBjv8mNC/Rf06doh/NS4S\nqmoCZeKt3pgkS/+TyUFz9l+UWYLL1SrK8BDpe9XX224sCLd2iGwvE4QW6Le8\nzd0CTAaK4kSTKNpgBvatsaEPoiAzWwuJ9j05sMBu2BGLfK+sr6x4rvTONFMZ\n1aD9H53i9/1njHQG46EIxso8OSxX4LudkatOcYVnFcCCc/W4HVCAA1J9FUXO\nIjen\r\n=Hj9B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.mjs", "gitHead": "6808d548a8cb8ba8d768c58058a015da3c878350", "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm", "clean": "rm -rf ./dist", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "postpublish": "PACKAGE_VERSION=$(cat package.json | grep \\\"version\\\" | head -1 | awk -F: '{ print $2 }' | sed 's/[\",]//g' | tr -d '[[:space:]]') && git tag v$PACKAGE_VERSION && git push --tags && pinst --enable", "_postinstall": "husky install", "prepublishOnly": "pinst --disable && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git", "type": "git"}, "_npmVersion": "7.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "15.2.1", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^4.8.3", "vite": "^2.1.1", "chalk": "^4.1", "husky": "^5.1.3", "pinst": "^2.1.6", "eslint": "^7.22.0", "rollup": "^2.41.4", "chokidar": "^3.5", "typescript": "^4.2.2", "@types/node": "^14.14.35", "lint-staged": "^10.5.4", "@mussi/eslint-config": "^0.5.0", "conventional-changelog-cli": "^2.1.1"}, "peerDependencies": {"chalk": "^4.1", "chokidar": "^3.5"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_0.1.1_1615940319278_0.5948701514106842", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "vite-plugin-full-reload", "version": "0.1.2", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "_id": "vite-plugin-full-reload@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "dist": {"shasum": "f2b565f3eb8017d2248424a07479557c595587a5", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-0.1.2.tgz", "fileCount": 7, "integrity": "sha512-XMEa0iw8wEScdVN5SgtVw3C2IDoDoDXmeDjO3zc3MYhxtKigk/0OEakfCIEtHyGhNsldcFRiAE5ca9PXS9SAOw==", "signatures": [{"sig": "MEQCIF6iejwlW2tInho6kIEKvScUFkt1wAOz8IKUMWwzOcLRAiAwOQRUyjceMKN2BewgRNL9h9YnT8pqV6todOFoNQVmRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7769, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgU5oGCRA9TVsSAnZWagAAJEQQAKToL48meVeVWD4lMZbP\nSy4d1abNat7bDWe1QHNUh3BlKKjJGImP0x6cVMgMTj6lWxKiU43tNhxnY847\ndxYM2sqfxwpI6He0skIirqemlQTCSpEDSH+7lWuMqC/BvXmraZ6BkBbATJ5M\n0VGzsQlY4KqJ2TonyhkBVSPMl0aVsgWakYbzg7jhpNs+yOC0BU/2CX4HMR+S\ncnunW/gi1/EvjO1VKkQLOt4m7S6yP01yIQmjs0l2hmtg8guxaDKT1W5FPvEu\nvL5wAiQ3QRCu4brTDs9BziRYUcDTL4eBP3HQKkgA2fsJjW4xIewSZ7sIoju3\nDHASaUk7b9bAL8AAYbBkh73lV9qoLDZCMWULCH0vssIMKgKYnWLZ9xzAzYqd\nzDBg1yrPS+iz08PvptJlBEkbGKHYNoTxFscCU2KganTNLcztFWWIvgVmOJUp\nfxzHu10HK4mpHLVCD/n91NqsPeL0kAh1nWAhpPRvmjjRHC4zC/hLJTdP+fUn\nnrb8C9yAokCXe8waroRc12O2SK9+EbF1zqLgJsR/vQaAQYx1o5VCMDOtgSeW\nrTOF7mNMhIhrhEnFjqFb5aDJjtsjY8tM3jZXOnNqBcCBQIGioR79mISdyGPD\njJwS6PFoBIhQz/5TKla9g23VZcYILfLJVnHgTB0/zaLkejN2MfohfxrTC+ex\nHHmd\r\n=EOXO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.mjs", "gitHead": "dcd36e79c02bb5ba9ee5eedb760aa56e242adbc6", "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm", "clean": "rm -rf ./dist", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "postpublish": "PACKAGE_VERSION=$(cat package.json | grep \\\"version\\\" | head -1 | awk -F: '{ print $2 }' | sed 's/[\",]//g' | tr -d '[[:space:]]') && git tag v$PACKAGE_VERSION && git push --tags && pinst --enable", "_postinstall": "husky install", "prepublishOnly": "pinst --disable && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git", "type": "git"}, "_npmVersion": "7.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "15.2.1", "_hasShrinkwrap": false, "devDependencies": {"tsup": "^4.8.3", "vite": "^2.1.1", "chalk": "^4.1", "husky": "^5.1.3", "pinst": "^2.1.6", "eslint": "^7.22.0", "rollup": "^2.41.4", "chokidar": "^3.5", "typescript": "^4.2.2", "@types/node": "^14.14.35", "lint-staged": "^10.5.4", "@mussi/eslint-config": "^0.5.0", "conventional-changelog-cli": "^2.1.1"}, "peerDependencies": {"chalk": "^4.1", "chokidar": "^3.5"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_0.1.2_1616091654054_0.45302047475774954", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "vite-plugin-full-reload", "version": "0.1.3", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "_id": "vite-plugin-full-reload@0.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "dist": {"shasum": "c807e6b8499165402934c181c4b23aa7c3223258", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-0.1.3.tgz", "fileCount": 7, "integrity": "sha512-MdGt7FAIXYAeuW1HDW87nbqMwsO9p8CZ2ynoRnzG6wW7zm752c3RQMgmOiTacnl/iwcfAy+uJvUCRwQgrGuRyA==", "signatures": [{"sig": "MEUCIGkVLNJXTOYZBEJhhOm5Oz90onlZM6Sbr3pDrNrnoeO7AiEAj+8JLylgPAW9nrgwsuhaRwdnY/+pntsVb7NWJ+qUwdg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgU51vCRA9TVsSAnZWagAAAo4P/RXXtWwg56hMTYUfXcxP\n3F0udzwAh9o+U1VtFBqI1b1Paq0LIEApTDoSrjZPri/SgndyGxIMX1q/SRck\njVj0INoLPI0rXj9kJA2WbHA5p4zol4xQYH6Z1FKdyjROar8HLYp0EWqbEVXr\ndV9xtkNtHOdjVk4pwM/q8TQXdouwbfgTeZPHEQbgwSzY8MYJqgxBFFJntohu\nn7V1hakLCmR5/11lCYqfhfzN4vg0LpAT1KYi+Jdx9E16ol8b92PyF6ZviuNy\ndwd6lP/EPghT54W//CcMx91Pny1QATI+ULB7ywnPuqsqbrlEaWfe9c3hCb0n\nSCsmIy6Jho0sQi8RTENf/wLh2eYjoq4pEBr2SftyupG6uXYfiLRDCI4XWfUe\nQpUQoq+e+I7VIO5zr9tJaq3ZW29fgp9hGgv3gvG80+l1pFG622gmEaqZJvNk\nd74W4hc2XjkeExY8dVKuXOfZ9HAzKiZgorMq7R+/4X5ST4Sh6IyVJLHMdfBP\n+7wq4yT6buRBlEuiG4hi12yt/MtRxZweW4s1X6H7BaeTEM6/QO0cQy/wZVY5\n+16fFt08QEhSMJ6DVESczmhF3r4txrrVdecBI6cr6nhHoac37lzd8vo0hTPa\nE1AobtVkgoXesLw4st4/xzncFqEJD31UGR1PmqaoDBJDtPHEoIosvwg28j0R\n8MR+\r\n=yCUD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.mjs", "gitHead": "3f2b2568640e1e2dce6f87a28b09ecc62e099dea", "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm", "clean": "rm -rf ./dist", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "postpublish": "PACKAGE_VERSION=$(cat package.json | grep \\\"version\\\" | head -1 | awk -F: '{ print $2 }' | sed 's/[\",]//g' | tr -d '[[:space:]]') && git tag v$PACKAGE_VERSION && git push --tags && pinst --enable", "_postinstall": "husky install", "prepublishOnly": "pinst --disable && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git", "type": "git"}, "_npmVersion": "7.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "15.2.1", "dependencies": {"chalk": "^4.1", "chokidar": "^3.5"}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^4.8.3", "vite": "^2.1.1", "husky": "^5.1.3", "pinst": "^2.1.6", "eslint": "^7.22.0", "rollup": "^2.41.4", "typescript": "^4.2.2", "@types/node": "^14.14.35", "lint-staged": "^10.5.4", "@mussi/eslint-config": "^0.5.0", "conventional-changelog-cli": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_0.1.3_1616092527280_0.8967220689052198", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "vite-plugin-full-reload", "version": "0.2.0", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "_id": "vite-plugin-full-reload@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "dist": {"shasum": "7c79961479f9f052513f70b83f39dd6a0dcaedda", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-0.2.0.tgz", "fileCount": 7, "integrity": "sha512-4DJ2ICgwSneTNjdYfau+N2gcuxrMBzjdGSsjpw+kJiSzryxnbBYKKhoz1Qbnm/d2XpPH3f3Gp6GZAZtubA0npg==", "signatures": [{"sig": "MEUCIFNZieyb6doqQy70B9zoeo6rZ7ZgOMdNAlnPdkxkMAvrAiEA6in17hEk+sloGYlA00JpHfjkFBDl8vZq09lx/YCCUVw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVf/jCRA9TVsSAnZWagAAEIsP/2uMg5jqQkLrNrjq8ybU\n6QHo4DQVm8bu4O93YJ+LrIzN++CAGCRK/OIHNcIyGKd61Mwx/+uOS+kt7iEZ\n5jPA0eynjxXiUOKpEoSxiia35AHFJ7+mDLts5rDkeFPq5PIFGpwDKH2fqfAZ\n9iSyawAuh83/IDOAXmJYncqT1Ma+FzcmKC2iD44AlywSKv57CgQ0AeiKmnpv\nDpJRZbDWQAJbuh9voexXAMuxWzlWwVv0yAJq5gsLRrshqotgSmbaiJnM3tya\nK2WwFy4ksiptcJxZQVc4URN9Ft/nWhWa+CWE06LawodcFx9Rcu2uOWQACm5Z\niMCH3gHIrpc1AvATMDiOBN7ABcl1tKt07aSanuQeuxIj0BqXWaEptGSc+ZOZ\nC6538yPGvlFyO1FJo6Dg9gaTTJt19DVoZ7/Mvl3CnBfGz2u8UvKDR9xAlFZU\nwdBLUHICmoSr6KL9u1XAzzxQ+UOkQ1KYN5QCoOEWUJJPKIDFirlf0a/1yL1/\nFjmKf5cKUqRCLlJZUTbfq8/Ceo3DVUEbq7W+qamQcAIzIImxoP5/xAAMkE4/\nBcW/5q5Zxeo7swbgUqHQIK4CXy88/LFOKhCqp59fCfZpHWSg70C7g0Xqe/kE\nvVUMSHfe6Bwn5RMAWzUX1M4ejP7eE3o4wCKD8sDHlS2Kaloh5aOz4MQjwG+h\nNS0L\r\n=6G7e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.mjs", "gitHead": "875a69e533d768cc2794fb19dfe018779931e80a", "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm", "clean": "rm -rf ./dist", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "postpublish": "PACKAGE_VERSION=$(cat package.json | grep \\\"version\\\" | head -1 | awk -F: '{ print $2 }' | sed 's/[\",]//g' | tr -d '[[:space:]]') && git tag v$PACKAGE_VERSION && git push --tags && pinst --enable", "_postinstall": "husky install", "prepublishOnly": "pinst --disable && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git", "type": "git"}, "_npmVersion": "7.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "15.2.1", "dependencies": {"chalk": "^4.1", "picomatch": "^2.2.2"}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^4.8.3", "vite": "^2.1.1", "husky": "^5.1.3", "pinst": "^2.1.6", "eslint": "^7.22.0", "rollup": "^2.41.4", "typescript": "^4.2.2", "@types/node": "^14.14.35", "lint-staged": "^10.5.4", "@types/picomatch": "^2.2.1", "@mussi/eslint-config": "^0.5.0", "conventional-changelog-cli": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_0.2.0_1616248803124_0.9755495413663351", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "vite-plugin-full-reload", "version": "0.2.1", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "_id": "vite-plugin-full-reload@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "dist": {"shasum": "19e2586d066e46dafadbffa831ce2cc0f2605ed7", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-0.2.1.tgz", "fileCount": 7, "integrity": "sha512-xlp1+89B8YorpO8YrLcNJhYRbRS5X9OR7Q/gzgXyhdundOlu4gaLv6NPRlHh8XCYoBU8gfsGZW1Z6ZpzDXUd9w==", "signatures": [{"sig": "MEQCIFsZTnetcXlmofcgZ+DhP6XQ5zlfvMYDrIl4bhkO2xilAiAvVzMPbxhZP3UAByrfnMmjxk6RXf54Om3ITcp8aeAVYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVkLWCRA9TVsSAnZWagAAAkEP/0y1rI8z2iK5AtY81c0t\nBSVXq0uDJOjEaRzCxZG4USdv3qJJhuL/neYZHY8BDkYJwSnAOYYoLLKtYtHN\nwiJtPUaGiPypf+9f29gpREXB1nFxG3UTxrMaaPqKeAFO9V3Uze0tZHrxDRUB\n8w9QwIUUk+BlRtMElaa7MO2o0yead91ch0cwDJ5Tu0rglXM1GLq6DZAVf43k\nmYbFoOL+bea7BMKvlsVWmQpmIOJhDq5iUIHPQnUbe4Keox83I/FR0vfYDbzc\nzqQZv5kcW0JJyGtNjRmnil30LC69c8bBQSH12cibBPHgINfA3ou/D6LJ9SAY\nwEIYVIRe1fEN9x5QFWMnPJ2wlWr9rbrQ+zvrHGilVmXG0G2XRb+MQCgu7y29\ny6U28PLQ9VxiT3pnZohLX9fJ7ebcyHlK1e2vhK4sxH/lARuXfhumM7nDlK2p\n/n6PFnUYeta6Qmmjjjv1xDoVV5TUHbOEEmhJ9N4STzX8H4AuU+z7AJDCeH62\n5BjZSDCBMnVnrM+Uu5fFCQqMCihOdV0tH64nIVjPOOSED298UreIp4Vm1WL1\nOG+a3MqzzkDk4f9+KA1g2ST6m/2/RLADgBdy17aECM/j9pyxGCIsoJQ+tbw6\nh9C+NoV9EW1we76t0DdFPwSOw/p1NJISuYovvj9To1OSEsuUZ7ZlwwEFukMl\n0QG1\r\n=eV8k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.mjs", "gitHead": "b1266d80b6bee1c64bdcd85d883ceee71e6ba12d", "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm", "clean": "rm -rf ./dist", "release": "node scripts/release", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "postpublish": "PACKAGE_VERSION=$(cat package.json | grep \\\"version\\\" | head -1 | awk -F: '{ print $2 }' | sed 's/[\",]//g' | tr -d '[[:space:]]') && git tag v$PACKAGE_VERSION && git push --tags && pinst --enable", "_postinstall": "husky install", "prepublishOnly": "pinst --disable && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git", "type": "git"}, "_npmVersion": "7.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "15.2.1", "dependencies": {"chalk": "^4.1", "picomatch": "^2.2.2"}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^4.8.3", "vite": "^2.1.1", "husky": "^5.1.3", "pinst": "^2.1.6", "eslint": "^7.22.0", "rollup": "^2.41.4", "typescript": "^4.2.2", "@types/node": "^14.14.35", "lint-staged": "^10.5.4", "@types/picomatch": "^2.2.1", "@mussi/eslint-config": "^0.5.0", "conventional-changelog-cli": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_0.2.1_1616265941514_0.7913792429748017", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "vite-plugin-full-reload", "version": "0.2.2", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "_id": "vite-plugin-full-reload@0.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "dist": {"shasum": "7c5373590fa2b1940cb400810c3b22b7d84ece43", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-0.2.2.tgz", "fileCount": 7, "integrity": "sha512-5O0b31tatFPQbhywsvfHs2fLzm1mULNTzI9UEXWRJuwxiBkg7YD1+2wqdj/RKKQk84AImsJb1YFVUr0bnEbkfQ==", "signatures": [{"sig": "MEQCIDPbcnDvtTIdTB0SmZM7pMwSZ4hRZC1NkQoc+1R5QVNYAiAbA5RDisJt9zy6k66b7NPMRV9ubgmqoh98Jxa53F5G5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkqAGCRA9TVsSAnZWagAAznEQAI7lhP0hyAYPcchvZsFy\n7n4o5O8TnWErMrQ1T8SJqOpCvj3/zCmi0pKu2uoisJYGUAO+u7Exbb4Y75YZ\n9IxRyvgcDq25xxBw1oGcukaIkyqslw+V7v5tITSibxPDmPel4FqnBjUGTRtl\n5K2ZO04kraG1v99xyym7S09q6P4/qmnL8LU2XPuo6f/DhqnNnEzhXKpJMCoH\ngosIW8MXGeMv5RqGuwQCytX0qMWAT007xq38g9+Ls0WSxmnT3YmxNZRAKnkC\ncFVaATgULBVJ40X5AQ50o0jxsy6cZgBRgTHDoe3XkGeV6XPfMAuJvqwnHnqB\nNOvsN9USrt5ZGfDw9NovADCT2oY/59nZTFce+07JtsezTU/3G/KzenwMK05z\nqER/YS6betaLLX/ThWyqm+ckePZ/xEylNWzcAiQKXw80Tnfn2jgBc40H5VBJ\nlu4ypMZRAdWpPbGcQKjjWEaS/tJYMBtLC+TB2BOpzfPgPg2QH1SJQ/NK0EIa\n9pww9RFpq59eZ9CkrJ/Z5LMWOx/bKchttUZBXZvAJTli8hozBLxwXY4E25+p\n0H3H/2ECcLWxFVyYfccR5DE+5pbI/xTVz6fwINqrjz/DLVyK5KF2mBQpt11u\nnEt6JplOYWjLPo8yFuCLBiw/3AUpDnCoFL0Ggl3oIiTNErAp0GN2qqe4yrbY\nI8wr\r\n=zIYM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.mjs", "gitHead": "1a4bf3338676524a3a7235e34c1e621ad76510df", "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm", "clean": "rm -rf ./dist", "release": "node scripts/release", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "postpublish": "PACKAGE_VERSION=$(cat package.json | grep \\\"version\\\" | head -1 | awk -F: '{ print $2 }' | sed 's/[\",]//g' | tr -d '[[:space:]]') && git tag v$PACKAGE_VERSION && git push --tags && pinst --enable", "_postinstall": "husky install", "prepublishOnly": "pinst --disable && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git", "type": "git"}, "_npmVersion": "7.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "15.2.1", "dependencies": {"chalk": "^4.1", "picomatch": "^2.2.2"}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^4.8.3", "vite": "^2.2.3", "husky": "^5.1.3", "pinst": "^2.1.6", "eslint": "^7.22.0", "rollup": "^2.41.4", "typescript": "^4.2.2", "@types/node": "^14.14.35", "lint-staged": "^10.5.4", "@types/picomatch": "^2.2.1", "@mussi/eslint-config": "^0.5.0", "conventional-changelog-cli": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_0.2.2_1620221957477_0.5365920487672198", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "vite-plugin-full-reload", "version": "1.0.0", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "_id": "vite-plugin-full-reload@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "dist": {"shasum": "230b6b61e7ff4fc66bfe7e24d0dfe9ad2b6d750d", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-GUhQ4wSm+H6yAlz7d2JwLu+BEcsFqZu1SUH0CNG+uyP4ALXGofUjHi+eGdLX19bYhMuOUWW3EwMt68jFkBsMew==", "signatures": [{"sig": "MEQCICAljL0+ek7e61y3k+XnAa9xMrne8t4bcXiF/gcoE7PHAiBwzMtkeXAZJKuQ6Ckzz4OJRPN9412T54q5a8PpwWGa8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1PLcCRA9TVsSAnZWagAADV8P/1+jRIGlAhKkBcORYqvu\niquB1fuKhmTbfPR7UGBid5HY8SzOl8VT80rG1ZnZvsXmZHIDMGiED44V7H3t\nqt2JXcPPv9rbNWyKLrJPBI3WsQUUiWlD5SqVG4JXKMr1tOPV8B4vdPuxIdMB\n0EqurulIYz/ma/WmDRSEal5eJa42Q5UfP49Y3FF/zoVASEp1hyBWtfxKhzxa\nBTw104X4a8SfpQFlUh6jXkSGrgwLVYAkG/P2WtMAM9fU+mn2H5hy9VOc8niM\n/SfCTWsCWT17HncT4sJjvuKjmNAAAzAbl87rxeIuo2GHjPh0D5sYYVZXHC3r\nuJPBJ/wFZVh8qRcNh4hmS3JzlKkqu8TGLNSExVrHc+AHT/UHc+GhZ8VdtMZ2\nGPnbsI/7EWbTd7tBFETdECGgfNm/u6exRTg8eKjCeSbMHQmkN2E31OwNts+Q\nTMbilYM+RYowiim/lYSYYUptInlgNYyb8Vwmd+O0D4v4kXjDXBZJUVPTcmo/\nuoYOn9OxVf3xGPewnCs/qRwtxPF5z/VaWKz7LopiJCNlX/ua2t+HQk/gAVdz\nukAA3Ofq/fvLAqN2QtIfb7lsAy+mNogtp5RfkjoXzT9/8pg37FWgxkNnO0Y/\nJqlWmqCFIPo1d9lODD26BPsSVNDAncxe6lgVBz+tvnrik4ynRTQpQyqlPwPF\nLRCw\r\n=Wttu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "gitHead": "736c603cf6fb0297241b08a81f485c6c6f447778", "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm --clean", "clean": "rm -rf ./dist", "release": "node scripts/release.cjs", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "_postinstall": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "16.5.0", "dependencies": {"picomatch": "^2.3.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.10", "vite": "^2.7.10", "husky": "^5.2.0", "pinst": "^2.1.6", "eslint": "^7.32.0", "rollup": "^2.63.0", "semver": "^7.3.5", "enquirer": "^2.3.6", "minimist": "^1.2.5", "typescript": "^4.5.4", "@types/node": "^14.18.5", "cross-spawn": "^7.0.3", "lint-staged": "^10.5.4", "@types/picomatch": "^2.3.0", "@types/cross-spawn": "^6.0.2", "@mussi/eslint-config": "^0.5.1", "conventional-changelog-cli": "^2.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_1.0.0_1641345756273_0.25966234046504", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "vite-plugin-full-reload", "version": "1.0.1", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": "<PERSON><PERSON><PERSON><PERSON> <maximomus<PERSON><EMAIL>>", "license": "MIT", "_id": "vite-plugin-full-reload@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": "https://github.com/ElMassimo/vite-plugin-full-reload/issues", "dist": {"shasum": "9cd8bfebf923a2b6177a6e9b1fa516302bff8c8d", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-G/IXcDjuhfDAK4zEzWTM/kL0DiAVv0+Dox+zGZeJyUWhwtg34Q9XtJkNXveku+mTSKE20TMp3TF8D7bFEhmvfA==", "signatures": [{"sig": "MEUCIQD9udvluQUuvTKGm6saH8MCsRfHWFPYx8Nhhr1b1N2hJgIgP2ovezxT6j8HhTGA+Sgrk9ZW+FvX8GH+R7+NprtKVkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10529, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwtESACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMxxAAjJMUjDlULvybVWIBwYtr7x0JcO7Y5Nc6gSHxNBTx/EYYlcg9\r\nUpUJOomNvf0/arObouaPgchQvxPc6ZORqA+LXOAJKDZntLxrdHo6MGeWcRdd\r\nUGLCgJ6Lp4VSbXURSUgpSEQMOYhrv7mzdna4vhCMqq14jLaXnZLpgeJroeAb\r\nMVV4HzNvNficeHFvLt0+aEqq7yT7I1oi5/sx92X9/ryvuegdpNcIRIKs9Nf4\r\nN98xSd9UfLXmBqYMf8Iw+mv6pdeqUvxCQZ2WvwmOzdsQggDdS54ExdUQPaCU\r\nRutAfWfqdX6vcyKFTNyXMoo9vbxpSiUNP4ri8B0n9pynxypODkHQy0VOilqo\r\nXTKkEqNU8tWGr40hXbkoEOqHlBmbghEffXdEsimMJVFvf6OAOk+HsKE0pePg\r\noZGoXi5pyaxmkeN3JrLbAQHY5rI4z2RvXiMQbyGeiPIhPMj6JWIn3iL8vvI+\r\nUB1recyNw2JJT3TIsDkICoyzsdrIx7ZS2vRGq44BGQy4uWkNvk++KZQryWO0\r\nuqAKQzTRk1tC02A5vhv1bxHYbN/FHJNr025MrP/dkRno4kXq9BpccX0v4q5g\r\nRIJmsgq/KtOjdU6lUkp8yxz20Pja4cZGHtj/9xkaT5rZAmR/HF7aj5NDd2fF\r\nCwyhwORCQARFfRWPAL4ggaVnYUA7bdEAUOo=\r\n=DPvJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "_from": "file:vite-plugin-full-reload-1.0.1.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm --clean", "clean": "rm -rf ./dist", "release": "node scripts/release.cjs", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "_postinstall": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "_resolved": "/private/var/folders/j6/gyvfxjy50jqgy9lth08nd_f40000gn/T/2aa2dbf27e246746027eb623e51b0bd4/vite-plugin-full-reload-1.0.1.tgz", "_integrity": "sha512-G/IXcDjuhfDAK4zEzWTM/kL0DiAVv0+Dox+zGZeJyUWhwtg34Q9XtJkNXveku+mTSKE20TMp3TF8D7bFEhmvfA==", "repository": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload", "type": "git"}, "_npmVersion": "8.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "16.13.1", "dependencies": {"picomatch": "^2.3.1", "picocolors": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^5.11.10", "vite": "^2.7.10", "husky": "^5.2.0", "pinst": "^2.1.6", "eslint": "^7.32.0", "rollup": "^2.63.0", "semver": "^7.3.5", "enquirer": "^2.3.6", "minimist": "^1.2.5", "typescript": "^4.5.4", "@types/node": "^14.18.5", "cross-spawn": "^7.0.3", "lint-staged": "^10.5.4", "@types/picomatch": "^2.3.0", "@types/cross-spawn": "^6.0.2", "@mussi/eslint-config": "^0.5.1", "conventional-changelog-cli": "^2.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_1.0.1_1656934674145_0.5781739966392738", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "vite-plugin-full-reload", "version": "1.0.2", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": "<PERSON><PERSON><PERSON><PERSON> <maximomus<PERSON><EMAIL>>", "license": "MIT", "_id": "vite-plugin-full-reload@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": "https://github.com/ElMassimo/vite-plugin-full-reload/issues", "dist": {"shasum": "49f7c22ba243ac298c0e8648d0dea762e99f6f18", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.0.2.tgz", "fileCount": 6, "integrity": "sha512-zciz+dtflPmEPbhl1USLw5oh20MW665MFneiHeGSaNMVxyxaYjUSZWeuiBq1m5vnHvHdcqTI5A+Chd0JzOm9Sg==", "signatures": [{"sig": "MEQCICttLSAAG473vKiIw8Xj5omEJry8L4W/ZeG2B5NMbjhDAiBuhE9AWcsyzQAaWZQUBc20AwmgNWkzg43NHtDGOgQXNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2X3DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyMw//WOehS+zi1hGX+lllGw+z8MUSoaw5hRuZnZ3oBhqlwZLtmw8m\r\nessq926ajTPM1WjqX5jvPeNwYHCmS0ghxLYT/KQR9xuHmqHXlC1EzeDAGqB8\r\ndqTTyYKbuIATWJOb2wO4tl/chtVLmXkLabLZrx/1t/Ycy4OLUrfjihb1drrW\r\n3Lg7fxNvNyVqBTm/d5vPN+Zei6YvcGei5zAbGp4RnTdbhcPAPudZ+S4y17wW\r\n/8HLCHNsceVPHDzfJCw016bhlUve04WGJJJc3WWm4Yjlm3kJVJWbuwgUzEPw\r\n1LhwJ5SOVStMmogr3jjndDU3PltJB1Bj/MG3o2mRhS7Y//iZ6bZNd090WAEv\r\ntdHXUdT/QjXdDEQScOEX/mLuuLTzuqaGMDNEFVLppiwDunffwxzXqWWxRxL7\r\ncqj4ui006wet325jWWmHRECcczvinVUvUks0B1s+gDrdqJSB3nCk8jVfiijR\r\n6jTwsjz3G8P7REW+nlJhlawD1rU0E7EJUCnq2PQEb60gh7YtvX+klVMgzFyT\r\nlHXpXJ9/sXNsxBCNH8pR35HQYAGkJPRpCpZ6lqLsgXvyQ8ee//a3cJahX9LP\r\nSc9tfRulzDQ7LsT2iW0F4ydNhOgflo4TuJ9lWhVv4txHmb7GWbQCK+Vv3mBa\r\nyzGB41k+7FgamuyG7BnlnJS0oM+afXGK3po=\r\n=AM5Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "_from": "file:vite-plugin-full-reload-1.0.2.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm --clean", "clean": "rm -rf ./dist", "release": "node scripts/release.cjs", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "_postinstall": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "_resolved": "/private/var/folders/j6/gyvfxjy50jqgy9lth08nd_f40000gn/T/19eba3adc50e9eef349a07fdee731423/vite-plugin-full-reload-1.0.2.tgz", "_integrity": "sha512-zciz+dtflPmEPbhl1USLw5oh20MW665MFneiHeGSaNMVxyxaYjUSZWeuiBq1m5vnHvHdcqTI5A+Chd0JzOm9Sg==", "repository": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload", "type": "git"}, "_npmVersion": "8.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "16.13.1", "dependencies": {"picomatch": "^2.3.1", "picocolors": "^1.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/space-before-function-paren": ["warn", "always"]}, "extends": ["@antfu/eslint-config"]}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^6.1.3", "vite": "^3", "husky": "^5.2.0", "pinst": "^2.1.6", "eslint": "^8.20.0", "rollup": "^2.77.0", "semver": "^7.3.7", "enquirer": "^2.3.6", "minimist": "^1.2.6", "typescript": "^4.7.4", "@types/node": "^14.18.22", "cross-spawn": "^7.0.3", "lint-staged": "^10.5.4", "@types/picomatch": "^2.3.0", "@types/cross-spawn": "^6.0.2", "@antfu/eslint-config": "*", "conventional-changelog-cli": "^2.2.2"}, "peerDependencies": {"vite": "2"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_1.0.2_1658420675528_0.4312497462075431", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "vite-plugin-full-reload", "version": "1.0.3", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": "<PERSON><PERSON><PERSON><PERSON> <maximomus<PERSON><EMAIL>>", "license": "MIT", "_id": "vite-plugin-full-reload@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": "https://github.com/ElMassimo/vite-plugin-full-reload/issues", "dist": {"shasum": "67997c0243b783c3b7e97159543acdf5c0224bc0", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.0.3.tgz", "fileCount": 6, "integrity": "sha512-owIZz/tGXFyUzIDzbu4KBpxrpd08BO+tuWqZABCug5C2laNTwOS4mcDMsMKnXT7B7WR8oRd+BI6a2kVDtsan3w==", "signatures": [{"sig": "MEQCIHaDg/CuiluZDXLgpb0KJa33aP64y9KGahCP/aVHzkaEAiBx3vNhcYAHPgdPgr2nO0Z4GMpKG4nPAwzkc4QtC5Ny5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2bM/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmondw/8Da8ZGUxI5+aRlNWCDDQJCCk8UPva5g8oU4KydcnYZbVV4VjF\r\ne+iAF7jG9Iu6oDFt5WbDOKxoxv/LkA6vpXc86RuF9MzM4EmlNUBnDoRUAc+r\r\ns7YjBAU5xMqlAwV6Rvo3BvnGARz7FtPbUE3OIohXQZdo54K1wF9ZuYyqWEiS\r\nUDHddukArVZnxm33QxGWBpxdyZOAoteU7fz23aOZGLVmOcda7R97RinxxFaU\r\nHRNlwuhw7OLOR8i8Fcic3ON/crLechIMm3ZQ1+usgY99EkmTh0qn7wfKE7A6\r\nbNPHKIZ6DKCH/v9PuOf09VTE0/XTLaeCXCQC1UPXBp2+8V0hUIa5UMKu5+xf\r\nVu0CaRNblKd/7L00YYkeWo85LzkwzYsXevsQpDPc8pLLVdwKn0HFqMp1/mn2\r\nkAg6kE0UdO3pF4OVwmOieX9t5i3D6ig8fI8sYiatAy+t1BQAEBNgI9+3qDK4\r\n6LH3deFMC1Ye7YpgKY/aZi45u34PRf4hiAvfc/3hlA4haPW6uoKZlE4QVV1i\r\nYy7sGSbFgzmu9kENElicyik2DWG/uL4kPWmT0XC2KwtzhXyO8TRdtIsC1RYR\r\nBjpdXa2HPnlBB/WoB4dXTKnB4/C/omHujLaRQAexfdh/4bcJYtP4IShMgmsK\r\nJDdem2M78Vo1k9OyUhQhRTpNGmPwrR/2Oc0=\r\n=HDNL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "_from": "file:vite-plugin-full-reload-1.0.3.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"dev": "npm run build -- --watch", "lint": "lint-staged", "build": "tsup src/index.ts --dts --format cjs,esm --clean", "clean": "rm -rf ./dist", "release": "node scripts/release.cjs", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "_postinstall": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "_resolved": "/private/var/folders/j6/gyvfxjy50jqgy9lth08nd_f40000gn/T/651a6edade2539c060eb02ede4334735/vite-plugin-full-reload-1.0.3.tgz", "_integrity": "sha512-owIZz/tGXFyUzIDzbu4KBpxrpd08BO+tuWqZABCug5C2laNTwOS4mcDMsMKnXT7B7WR8oRd+BI6a2kVDtsan3w==", "repository": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload", "type": "git"}, "_npmVersion": "8.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "16.13.1", "dependencies": {"picomatch": "^2.3.1", "picocolors": "^1.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/space-before-function-paren": ["warn", "always"]}, "extends": ["@antfu/eslint-config"]}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^6.1.3", "vite": "^3", "husky": "^5.2.0", "pinst": "^2.1.6", "eslint": "^8.20.0", "rollup": "^2.77.0", "semver": "^7.3.7", "enquirer": "^2.3.6", "minimist": "^1.2.6", "typescript": "^4.7.4", "@types/node": "^14.18.22", "cross-spawn": "^7.0.3", "lint-staged": "^10.5.4", "@types/picomatch": "^2.3.0", "@types/cross-spawn": "^6.0.2", "@antfu/eslint-config": "*", "conventional-changelog-cli": "^2.2.2"}, "peerDependencies": {"vite": "^2 || ^3"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_1.0.3_1658434366826_0.9233277598529794", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "vite-plugin-full-reload", "version": "1.0.4", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": "<PERSON><PERSON><PERSON><PERSON> <maximomus<PERSON><EMAIL>>", "license": "MIT", "_id": "vite-plugin-full-reload@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": "https://github.com/ElMassimo/vite-plugin-full-reload/issues", "dist": {"shasum": "3fecd446f9accd5af01eb0328f6d161dca7cfc45", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.0.4.tgz", "fileCount": 6, "integrity": "sha512-9WejQII6zJ++m/YE173Zvl2jq4cqa404KNrVT+JDzDnqaGRq5UvOvA48fnsSWPIMXFV7S0dq5+sZqcSB+tKBgA==", "signatures": [{"sig": "MEUCIGFFVl9+mXzvjhSmvW14I8CaYa3uBYCHhpmD9FF5qe94AiEA97ZlH17tS757rJyKeWpwqQeW4yPVIsVs7jTRKME8ATI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8nGsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpi/g//bNApq8lC2DVK+ak45mmlrH0ezYJA5kKezA6GYB/fBTYmj4yK\r\nhM/C5etRLtOtXoEpXFWnPz7mq4nd8i/UoJKYpc5/0/Pv4wjjkVgszctZFIJg\r\ngtC08ENJiEMQFAmsL90qibpveEmYWQ8gbpjmKgYw5+TruAo9qRfYpvwLtk1X\r\nIIAXdvTGDqt92GH2G+6e5gYZaCZYk3orNhcclxvPCpJoymISz6YNz0J+1IE3\r\ny3WoGVoEIER5FOFoeRaxf4xKJmrz+NYqv6CHLog/zgL0vVZWyFKXhBdWDeVY\r\nZfmNwktTQPB5EF4hTXieubVC7UF0L/BhlBHqFz5ExgCyEJZ0D8x7WK6hAyU9\r\nu2l/iGWN99pEiFQ5U/AhVJ/YK0wXQQ7bfLHl29LnuZZpB2FM7/APblNvEp3o\r\nWBPCjF2G2iryKNY5KKLe03KidkRwE7hwzOgExmEjJ+EiPOGQNca5qWoveW7+\r\nLmUr2glq5YQH8WQ7YAL3QZLrk8nNg+8umeb3+CvxUgwkBJGqbmEIPxG3rJvU\r\nGusjo1leQsbA+b2fsDpDF7NBQYMgVxD1xtrktQrklvyAlS8nyT5WVgkkijKe\r\n5rc+0RAL37H1iFvKQbX/X7jrM9Y3VggQd2Xcbin3qt9FAw0mSJRsAKisgI5o\r\nTeAHGIUzWIi7OeC9yxKRDH//k0GeBjcRcgY=\r\n=wp2R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "_from": "file:vite-plugin-full-reload-1.0.4.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"dev": "npm run build -- --watch", "lint": "eslint . --ext .ts,.js,.vue", "test": "vitest", "build": "tsup src/index.ts --dts --format cjs,esm --clean", "clean": "rm -rf ./dist", "release": "node scripts/release.cjs", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "_postinstall": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "_resolved": "/private/var/folders/j6/gyvfxjy50jqgy9lth08nd_f40000gn/T/571634b0075ee2936aba2de35bd7ad07/vite-plugin-full-reload-1.0.4.tgz", "_integrity": "sha512-9WejQII6zJ++m/YE173Zvl2jq4cqa404KNrVT+JDzDnqaGRq5UvOvA48fnsSWPIMXFV7S0dq5+sZqcSB+tKBgA==", "repository": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload", "type": "git"}, "_npmVersion": "8.1.2", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "16.13.1", "dependencies": {"picomatch": "^2.3.1", "picocolors": "^1.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/space-before-function-paren": ["warn", "always"]}, "extends": ["@antfu/eslint-config"]}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^6.1.3", "vite": "^3", "husky": "^5.2.0", "pinst": "^2.1.6", "eslint": "^8.20.0", "rollup": "^2.77.0", "semver": "^7.3.7", "vitest": "^0.21.1", "enquirer": "^2.3.6", "minimist": "^1.2.6", "typescript": "^4.7.4", "@types/node": "^14.18.22", "cross-spawn": "^7.0.3", "lint-staged": "^10.5.4", "@types/picomatch": "^2.3.0", "@types/cross-spawn": "^6.0.2", "@antfu/eslint-config": "*", "conventional-changelog-cli": "^2.2.2"}, "peerDependencies": {"vite": "^2 || ^3"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_1.0.4_1660055980311_0.6024261979095238", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "vite-plugin-full-reload", "version": "1.0.5", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "_id": "vite-plugin-full-reload@1.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "dist": {"shasum": "6cddfa94e51909843bc7156ab728dbac972b8560", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.0.5.tgz", "fileCount": 6, "integrity": "sha512-kVZFDFWr0DxiHn6MuDVTQf7gnWIdETGlZh0hvTiMXzRN80vgF4PKbONSq8U1d0WtHsKaFODTQgJeakLacoPZEQ==", "signatures": [{"sig": "MEQCIDM8Bvy2uGTfRCXqYuzbc7IcP+Y3gjliOAj77/1GbFi9AiAArsn/BrFASnnYcWVwfHxfArp02fDYpW3/af0w8LmVnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjk1q5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNFQ/8DjYqrCHfiP4ISIPxdGmWXc2iUhAAlPML+7wDQwLdrTPpNk/D\r\ns983lefPTbaET0/kGwkfBMmMJxmlLtZY1MaqlVrgA/GgMrWsv5DtXSQ+W24y\r\nKn+15XSMjWfZ6lYE9mOmliyQDOtIH9DgtbMDHOzV0Dm+POCmv8N3PyVuOhcA\r\nbQSPW8nStZGd8iowWeD5SjNz235BO41OBZJjalwm0uZQJcRaPlQYoZOM0Vpa\r\njoH8kJ84TP995X4PkCsubnpqSGZwryURnAJbNYx9kAOYFqi0hJcpdGVkhnNO\r\nZRqZWGFCl+kYhgBBSNd0agV8cnCeU7XQgjUvuiyuK9woHtaMgk6MaXKxn6Ts\r\nv8N1ZOmYfzkXI6CvFbPICbtWIy1xakld/P5j4E5K8V3BXYv+rxrdm/+JURyn\r\nVZ/SrDCE/ZbFEBeZjDEUYFiNuIkHBcahjwL7BXRB/DYyB/rXFLB0DpnJ5/SN\r\nwVBznWUZ8sp/JU3BoUvEPfq70ZmtBrFw8yR5HQPDYotQkxxcJ/wf8doo8SPw\r\nLVaiiB7qq3co5FR8sdbpydS/XATcr8YsP2WDinmb66+EVeD52NJ4cZh0voI2\r\nJEyXEwwoVjuO8hYXUqpZzbf9mK+25J4pOPciHEFcm9myRjr8Csw/mXshiGMh\r\nW2ce+CXkAnpqP9pV3EoqIA9+Nm30/9xRbTo=\r\n=zjcX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "_from": "file:vite-plugin-full-reload-1.0.5.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"dev": "npm run build -- --watch", "lint": "eslint . --ext .ts,.js,.vue", "test": "vitest", "build": "tsup src/index.ts --dts --format cjs,esm --clean", "clean": "rm -rf ./dist", "release": "node scripts/release.cjs", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "_postinstall": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "_resolved": "/private/var/folders/j6/gyvfxjy50jqgy9lth08nd_f40000gn/T/79b8d9b173b6320df24c2c76e73f0602/vite-plugin-full-reload-1.0.5.tgz", "_integrity": "sha512-kVZFDFWr0DxiHn6MuDVTQf7gnWIdETGlZh0hvTiMXzRN80vgF4PKbONSq8U1d0WtHsKaFODTQgJeakLacoPZEQ==", "repository": {"url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git", "type": "git"}, "_npmVersion": "9.1.1", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "16.16.0", "dependencies": {"picomatch": "^2.3.1", "picocolors": "^1.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/space-before-function-paren": ["warn", "always"]}, "extends": ["@antfu/eslint-config"]}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^6.1.3", "vite": "^4.0.0", "husky": "^5.2.0", "pinst": "^2.1.6", "eslint": "^8.20.0", "rollup": "^2.77.0", "semver": "^7.3.7", "vitest": "^0.21.1", "enquirer": "^2.3.6", "minimist": "^1.2.6", "typescript": "^4.7.4", "@types/node": "^14.18.22", "cross-spawn": "^7.0.3", "lint-staged": "^10.5.4", "@types/picomatch": "^2.3.0", "@types/cross-spawn": "^6.0.2", "@antfu/eslint-config": "*", "conventional-changelog-cli": "^2.2.2"}, "peerDependencies": {"vite": "^2 || ^3 || ^4"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_1.0.5_1670601401453_0.9303300620618924", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "vite-plugin-full-reload", "version": "1.1.0", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "_id": "vite-plugin-full-reload@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "dist": {"shasum": "ca6fa32631024a459ea9e5613dd4c0ff0f3b7995", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-3c<PERSON>bNDzX6DdfhD9E7kf6w2mNunFpD7drxyNgHLw+XwIYAgb+Xt16SEXo0Up4VH+TMf3n+DSVJZtW2POBGcBYAA==", "signatures": [{"sig": "MEUCIQCiftGzT31av7Jt4fYo/hqBWdV6CD9BOcFOTcv8iI/q/wIgDnPp0xrL2SuKv4bqVISRDWkkMm7w9O8pbcKQyRhdrAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12577}, "main": "./dist/index.cjs", "type": "module", "_from": "file:vite-plugin-full-reload-1.1.0.tgz", "types": "./dist/index.d.ts", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"dev": "npm run build -- --watch", "lint": "eslint . --ext .ts,.js,.vue", "test": "vitest", "build": "tsup src/index.ts --dts --format cjs,esm --clean", "clean": "rm -rf ./dist", "release": "node scripts/release.cjs", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "_postinstall": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "_resolved": "/private/var/folders/j6/gyvfxjy50jqgy9lth08nd_f40000gn/T/48426aa1b3f65e78e7feee81ee427854/vite-plugin-full-reload-1.1.0.tgz", "_integrity": "sha512-3c<PERSON>bNDzX6DdfhD9E7kf6w2mNunFpD7drxyNgHLw+XwIYAgb+Xt16SEXo0Up4VH+TMf3n+DSVJZtW2POBGcBYAA==", "repository": {"url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Reload the page when files are modified", "directories": {}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "_nodeVersion": "20.8.0", "dependencies": {"picomatch": "^2.3.1", "picocolors": "^1.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/space-before-function-paren": ["warn", "always"]}, "extends": ["@antfu/eslint-config"]}, "_hasShrinkwrap": false, "devDependencies": {"tsup": "^7.2", "vite": "^5.0.0", "husky": "^5.2.0", "pinst": "^2.1.6", "eslint": "^8.20.0", "rollup": "^4.2", "semver": "^7.3.7", "vitest": "^0.34", "enquirer": "^2.3.6", "minimist": "^1.2.6", "typescript": "^4.7.4", "@types/node": "^18", "cross-spawn": "^7.0.3", "lint-staged": "^10.5.4", "@types/picomatch": "^2.3.0", "@types/cross-spawn": "^6.0.2", "@antfu/eslint-config": "*", "conventional-changelog-cli": "^2.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/vite-plugin-full-reload_1.1.0_1700163225188_0.8371549262673024", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "vite-plugin-full-reload", "description": "Reload the page when files are modified", "version": "1.2.0", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git"}, "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "dependencies": {"picocolors": "^1.0.0", "picomatch": "^2.3.1"}, "devDependencies": {"@antfu/eslint-config": "*", "@types/cross-spawn": "^6.0.2", "@types/node": "^18", "@types/picomatch": "^2.3.0", "conventional-changelog-cli": "^2.2.2", "cross-spawn": "^7.0.3", "enquirer": "^2.3.6", "eslint": "^8.20.0", "husky": "^5.2.0", "lint-staged": "^10.5.4", "minimist": "^1.2.6", "pinst": "^2.1.6", "rollup": "^4.2", "semver": "^7.3.7", "tsup": "^7.2", "typescript": "^4.7.4", "vite": "^5.0.0", "vitest": "^0.34"}, "lint-staged": {"*.{js,ts,tsx,jsx,vue}": ["eslint --fix"]}, "eslintConfig": {"extends": ["@antfu/eslint-config"], "rules": {"@typescript-eslint/space-before-function-paren": ["warn", "always"]}}, "scripts": {"clean": "rm -rf ./dist", "dev": "npm run build -- --watch", "build": "tsup src/index.ts --dts --format cjs,esm --clean", "lint": "eslint . --ext .ts,.js,.vue", "_postinstall": "husky install", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "release": "node scripts/release.cjs", "test": "vitest"}, "_id": "vite-plugin-full-reload@1.2.0", "_integrity": "sha512-kz18NW79x0IHbxRSHm0jttP4zoO9P9gXh+n6UTwlNKnviTTEpOlum6oS9SmecrTtSr+muHEn5TUuC75UovQzcA==", "_resolved": "/private/var/folders/j6/gyvfxjy50jqgy9lth08nd_f40000gn/T/5607697cc885f4f32e12f94b4f756127/vite-plugin-full-reload-1.2.0.tgz", "_from": "file:vite-plugin-full-reload-1.2.0.tgz", "_nodeVersion": "20.8.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-kz18NW79x0IHbxRSHm0jttP4zoO9P9gXh+n6UTwlNKnviTTEpOlum6oS9SmecrTtSr+muHEn5TUuC75UovQzcA==", "shasum": "bc4bfdc842abb4d24309ca802be8b955fce1c0c6", "tarball": "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.2.0.tgz", "fileCount": 8, "unpackedSize": 12577, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDjbaXzvc9ERtJDp0ElT+wxpD1qpxxM4I/Oi5366DQYAAiEAjl6LBPunb8tPp1c6w84LmTmlmWFuKd9AeG75LyAun9I="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/vite-plugin-full-reload_1.2.0_1720480348334_0.39795881131546307"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-03-16T23:53:33.386Z", "modified": "2024-07-08T23:12:28.696Z", "0.1.0": "2021-03-16T23:53:33.513Z", "0.1.1": "2021-03-17T00:18:39.424Z", "0.1.2": "2021-03-18T18:20:54.191Z", "0.1.3": "2021-03-18T18:35:27.420Z", "0.2.0": "2021-03-20T14:00:03.407Z", "0.2.1": "2021-03-20T18:45:41.652Z", "0.2.2": "2021-05-05T13:39:17.602Z", "1.0.0": "2022-01-05T01:22:36.440Z", "1.0.1": "2022-07-04T11:37:54.322Z", "1.0.2": "2022-07-21T16:24:35.685Z", "1.0.3": "2022-07-21T20:12:47.001Z", "1.0.4": "2022-08-09T14:39:40.427Z", "1.0.5": "2022-12-09T15:56:41.619Z", "1.1.0": "2023-11-16T19:33:45.395Z", "1.2.0": "2024-07-08T23:12:28.530Z"}, "bugs": {"url": "https://github.com/ElMassimo/vite-plugin-full-reload/issues"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ElMassimo/vite-plugin-full-reload", "keywords": ["vite", "plugin", "vite-plugin", "vite<PERSON><PERSON>", "full", "reload"], "repository": {"type": "git", "url": "git+https://github.com/ElMassimo/vite-plugin-full-reload.git"}, "description": "Reload the page when files are modified", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "maximomus<PERSON><EMAIL>"}], "readme": "<h2 align='center'><samp>vite-plugin-full-reload</samp></h2>\n\n<p align='center'>Automatically reload the page when files are modified</p>\n\n<p align='center'>\n  <a href='https://www.npmjs.com/package/vite-plugin-full-reload'>\n    <img src='https://img.shields.io/npm/v/vite-plugin-full-reload?color=222&style=flat-square'>\n  </a>\n  <a href='https://github.com/ElMassimo/vite-plugin-full-reload/blob/main/LICENSE.txt'>\n    <img src='https://img.shields.io/badge/license-MIT-blue.svg'>\n  </a>\n</p>\n\n<br>\n\n[vite-plugin-full-reload]: https://github.com/ElMassimo/vite-plugin-full-reload\n[vite-plugin-live-reload]: https://github.com/arnoson/vite-plugin-live-reload\n[Vite Ruby]: https://github.com/ElMassimo/vite_ruby\n[JS From Routes]: https://github.com/ElMassimo/js_from_routes\n[picomatch]: https://github.com/micromatch/picomatch#globbing-features\n\n## Why? 🤔\n\nWhen using _[Vite Ruby]_, I wanted to see changes to server-rendered layouts and templates without having to manually reload the page.\n\nAlso, in _[JS From Routes]_ path helpers are generated when Rails reload is triggered.\n\nTriggering a page reload when `config/routes.rb` is modified makes the DX very smooth.\n\n## Installation 💿\n\nInstall the package as a development dependency:\n\n```bash\nnpm i -D vite-plugin-full-reload # yarn add -D vite-plugin-full-reload\n```\n\n## Usage 🚀\n\nAdd it to your plugins in `vite.config.ts`\n\n```ts\nimport { defineConfig } from 'vite'\nimport FullReload from 'vite-plugin-full-reload'\n\nexport default defineConfig({\n  plugins: [\n    FullReload(['config/routes.rb', 'app/views/**/*'])\n  ],\n})\n```\n\nThis is useful to trigger a page refresh for files that are not being imported, such as server-rendered templates.\n\nTo see which file globbing options are available, check [picomatch].\n\n## Configuration ⚙️\n\nThe following options can be provided:\n\n- <kbd>root</kbd>\n  \n  Files will be resolved against this directory.\n\n  __Default:__ `process.cwd()`\n\n  ``` js\n  FullReload('config/routes.rb', { root: __dirname }),\n  ``` \n\n- <kbd>delay</kbd>\n\n  How many milliseconds to wait before reloading the page after a file change.\n  It can be used to offset slow template compilation in Rails.\n\n  __Default:__ `0`\n  \n  ```js\n  FullReload('app/views/**/*', { delay: 100 })\n  ``` \n\n- <kbd>always</kbd>\n\n  Whether to refresh the page even if the modified HTML file is not currently being displayed.\n\n  __Default:__ `true`\n  \n  ```js\n  FullReload('app/views/**/*', { always: false })\n  ``` \n\n## Acknowledgements\n\n- <kbd>[vite-plugin-live-reload]</kbd>\n\n  This is a nice plugin, I found it right before publishing this one.\n\n  I've made [two](https://github.com/arnoson/vite-plugin-live-reload/pull/3) [PRs](https://github.com/arnoson/vite-plugin-live-reload/pull/5) that were needed to support these use cases.\n\n  At this point in time they are very similar, except this library doesn't create another `chokidar` watcher.\n\n## License\n\nThis library is available as open source under the terms of the [MIT License](https://opensource.org/licenses/MIT).\n", "readmeFilename": "README.md"}