{"_id": "asynckit", "_rev": "6-1439bf9993161717edf41eaf56ef3db5", "name": "asynckit", "description": "Minimal async jobs utility library, with streams support", "dist-tags": {"latest": "0.4.0"}, "versions": {"0.1.0": {"name": "asynckit", "version": "0.1.0", "description": "Minimal async jobs utility library", "main": "index.js", "scripts": {"clean": "rimraf coverage", "lint": "eslint *.js test/*.js", "test": "istanbul cover --reporter=json tape -- 'test/test-*.js' | tap-spec", "browser": "browserify -t browserify-istanbul test/test-*.js | obake --coverage | tap-spec", "report": "istanbul report", "size": "browserify index.js | size-table asynckit", "debug": "tape test/*.js | tap-spec"}, "pre-commit": ["clean", "lint", "test", "browser", "report", "size"], "repository": {"type": "git", "url": "git+https://github.com/alexindigo/asynckit.git"}, "keywords": ["async", "jobs", "parallel", "serial", "iterator", "array", "object"], "author": {"name": "Alex Indigo", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/alexindigo/asynckit/issues"}, "homepage": "https://github.com/alexindigo/asynckit#readme", "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.9", "eslint": "^2.9.0", "istanbul": "^0.4.3", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.7", "pre-commit": "^1.1.3", "reamde": "^1.1.0", "rimraf": "^2.5.2", "size-table": "^0.2.0", "tap-spec": "^4.1.1", "tape": "^4.5.1"}, "gitHead": "4e07ce5c2cf1ef940ee2d2ef7e8428ef6a2def9d", "_id": "asynckit@0.1.0", "_shasum": "328b62aa8bb99bff494e25379a19861e01572a4e", "_from": ".", "_npmVersion": "2.15.6", "_nodeVersion": "0.12.11", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "328b62aa8bb99bff494e25379a19861e01572a4e", "tarball": "https://registry.npmjs.org/asynckit/-/asynckit-0.1.0.tgz", "integrity": "sha512-H2szEj/NE/S1CHUTalFXa0rROTQ75dcLSxt4fg1zP8hzvumVdU1myGjwwonhhYxNlm0IKGDX1DC1rbZDOTgyzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCp6DthqVastRAajzoCK+Ym4i+3teC3ounrbNN4YUKvRgIhAPPRWUoB5NvmCYlVYzEyI8qhp1rK6GdpF52OTsjphKdP"}]}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/asynckit-0.1.0.tgz_1463613943057_0.8168901917524636"}}, "0.2.0": {"name": "asynckit", "version": "0.2.0", "description": "Minimal async jobs utility library", "main": "index.js", "scripts": {"clean": "rimraf coverage", "lint": "eslint *.js test/*.js", "test": "istanbul cover --reporter=json tape -- 'test/test-*.js' | tap-spec", "win-test": "tape test/test-*.js", "browser": "browserify -t browserify-istanbul test/test-*.js | obake --coverage | tap-spec", "report": "istanbul report", "size": "browserify index.js | size-table asynckit", "debug": "tape test/test-*.js | tap-spec"}, "pre-commit": ["clean", "lint", "test", "browser", "report", "size"], "repository": {"type": "git", "url": "git+https://github.com/alexindigo/asynckit.git"}, "keywords": ["async", "jobs", "parallel", "serial", "iterator", "array", "object"], "author": {"name": "Alex Indigo", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/alexindigo/asynckit/issues"}, "homepage": "https://github.com/alexindigo/asynckit#readme", "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.9", "eslint": "^2.9.0", "istanbul": "^0.4.3", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.7", "pre-commit": "^1.1.3", "reamde": "^1.1.0", "rimraf": "^2.5.2", "size-table": "^0.2.0", "tap-spec": "^4.1.1", "tape": "^4.5.1"}, "dependencies": {}, "gitHead": "9e318400f05566fd7ae5968b6f2052926853dd0a", "_id": "asynckit@0.2.0", "_shasum": "449693b00700b64babfb12f81be4c9919e4deb4e", "_from": ".", "_npmVersion": "2.15.6", "_nodeVersion": "0.12.11", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "449693b00700b64babfb12f81be4c9919e4deb4e", "tarball": "https://registry.npmjs.org/asynckit/-/asynckit-0.2.0.tgz", "integrity": "sha512-0M7mjypF9Y4iT1EE+Z2Ysk+yT2HaU6e5lH4Ii72gQYarafrQW7oV5DFAB61rxVmkjTv0Rc85lZiYDFCjBBOY8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE8IiW1uJP61sWZZVn8CizEvJXBeAFT7n8l2VUTs98rvAiEAzOJs3uwelYUMBXl+FUD+Z8iYH2fVSb1OGHXAXh64t7U="}]}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/asynckit-0.2.0.tgz_1463645848762_0.41517290310002863"}}, "0.3.0": {"name": "asynckit", "version": "0.3.0", "description": "Minimal async jobs utility library", "main": "index.js", "scripts": {"clean": "rimraf coverage", "lint": "eslint *.js lib/*.js test/*.js", "test": "istanbul cover --reporter=json tape -- 'test/test-*.js' | tap-spec", "win-test": "tape test/test-*.js", "browser": "browserify -t browserify-istanbul test/lib/browserify_adjustment.js test/test-*.js | obake --coverage | tap-spec", "report": "istanbul report", "size": "browserify index.js | size-table asynckit", "debug": "tape test/test-*.js | tap-spec"}, "pre-commit": ["clean", "lint", "test", "browser", "report", "size"], "repository": {"type": "git", "url": "git+https://github.com/alexindigo/asynckit.git"}, "keywords": ["async", "jobs", "parallel", "serial", "iterator", "array", "object"], "author": {"name": "Alex Indigo", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/alexindigo/asynckit/issues"}, "homepage": "https://github.com/alexindigo/asynckit#readme", "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.9", "eslint": "^2.9.0", "istanbul": "^0.4.3", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.7", "pre-commit": "^1.1.3", "reamde": "^1.1.0", "rimraf": "^2.5.2", "size-table": "^0.2.0", "tap-spec": "^4.1.1", "tape": "^4.5.1"}, "dependencies": {}, "gitHead": "b92a500fee672934c19bdb008974b46d494b8b4b", "_id": "asynckit@0.3.0", "_shasum": "bde42eec27d7888fef99f7427e63c2eb624c2903", "_from": ".", "_npmVersion": "2.15.6", "_nodeVersion": "0.12.11", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "bde42eec27d7888fef99f7427e63c2eb624c2903", "tarball": "https://registry.npmjs.org/asynckit/-/asynckit-0.3.0.tgz", "integrity": "sha512-qCxikyv0Ny1dG4uriCSpP0fev60kCazWJQyM7UyZ1njF5XuvE92konkM8YA+/y5ONBy1Di4VD3MX0uG8UQvKhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICaanWgtDwvKuU+qCKTR3Ail+/YRI/xW+VztiVKplyh2AiEA/9429K304tv3FY+ZtH7ALX7nN+cskSla24AMZMS0WTk="}]}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/asynckit-0.3.0.tgz_1464241837301_0.37562970467843115"}}, "0.4.0": {"name": "asynckit", "version": "0.4.0", "description": "Minimal async jobs utility library, with streams support", "main": "index.js", "scripts": {"clean": "rimraf coverage", "lint": "eslint *.js lib/*.js test/*.js", "test": "istanbul cover --reporter=json tape -- 'test/test-*.js' | tap-spec", "win-test": "tape test/test-*.js", "browser": "browserify -t browserify-istanbul test/lib/browserify_adjustment.js test/test-*.js | obake --coverage | tap-spec", "report": "istanbul report", "size": "browserify index.js | size-table asynckit", "debug": "tape test/test-*.js"}, "pre-commit": ["clean", "lint", "test", "browser", "report", "size"], "repository": {"type": "git", "url": "git+https://github.com/alexindigo/asynckit.git"}, "keywords": ["async", "jobs", "parallel", "serial", "iterator", "array", "object", "stream", "destroy", "terminate", "abort"], "author": {"name": "Alex Indigo", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/alexindigo/asynckit/issues"}, "homepage": "https://github.com/alexindigo/asynckit#readme", "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.9", "eslint": "^2.9.0", "istanbul": "^0.4.3", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.7", "pre-commit": "^1.1.3", "reamde": "^1.1.0", "rimraf": "^2.5.2", "size-table": "^0.2.0", "tap-spec": "^4.1.1", "tape": "^4.5.1"}, "dependencies": {}, "gitHead": "583a75ed4fe41761b66416bb6e703ebb1f8963bf", "_id": "asynckit@0.4.0", "_shasum": "c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79", "_from": ".", "_npmVersion": "2.15.6", "_nodeVersion": "0.12.11", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79", "tarball": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBbge6e7VDP+ilAARDSShG7w73q75+pQRdohpUXBDv2CAiB+LKgQl5A0S/jAElUxESEQ0eOsIiqsMLQ+vLPlsFj23A=="}]}, "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/asynckit-0.4.0.tgz_1465928940169_0.8008207362145185"}}}, "readme": "# asynckit [![NPM Module](https://img.shields.io/npm/v/asynckit.svg?style=flat)](https://www.npmjs.com/package/asynckit)\n\nMinimal async jobs utility library, with streams support.\n\n[![PhantomJS Build](https://img.shields.io/travis/alexindigo/asynckit/v0.4.0.svg?label=browser&style=flat)](https://travis-ci.org/alexindigo/asynckit)\n[![Linux Build](https://img.shields.io/travis/alexindigo/asynckit/v0.4.0.svg?label=linux:0.12-6.x&style=flat)](https://travis-ci.org/alexindigo/asynckit)\n[![Windows Build](https://img.shields.io/appveyor/ci/alexindigo/asynckit/v0.4.0.svg?label=windows:0.12-6.x&style=flat)](https://ci.appveyor.com/project/alexindigo/asynckit)\n\n[![Coverage Status](https://img.shields.io/coveralls/alexindigo/asynckit/v0.4.0.svg?label=code+coverage&style=flat)](https://coveralls.io/github/alexindigo/asynckit?branch=master)\n[![Dependency Status](https://img.shields.io/david/alexindigo/asynckit/v0.4.0.svg?style=flat)](https://david-dm.org/alexindigo/asynckit)\n[![bitHound Overall Score](https://www.bithound.io/github/alexindigo/asynckit/badges/score.svg)](https://www.bithound.io/github/alexindigo/asynckit)\n\n<!-- [![Readme](https://img.shields.io/badge/readme-tested-brightgreen.svg?style=flat)](https://www.npmjs.com/package/reamde) -->\n\nAsyncKit provides harness for `parallel` and `serial` iterators over list of items represented by arrays or objects.\nOptionally it accepts abort function (should be synchronously return by iterator for each item), and terminates left over jobs upon an error event. For specific iteration order built-in (`ascending` and `descending`) and custom sort helpers also supported, via `asynckit.serialOrdered` method.\n\nIt ensures async operations to keep behavior more stable and prevent `Maximum call stack size exceeded` errors, from sync iterators.\n\n| compression        |     size |\n| :----------------- | -------: |\n| asynckit.js        | 12.34 kB |\n| asynckit.min.js    |  4.11 kB |\n| asynckit.min.js.gz |  1.47 kB |\n\n\n## Install\n\n```sh\n$ npm install --save asynckit\n```\n\n## Examples\n\n### Parallel Jobs\n\nRuns iterator over provided array in parallel. Stores output in the `result` array,\non the matching positions. In unlikely event of an error from one of the jobs,\nwill terminate rest of the active jobs (if abort function is provided)\nand return error along with salvaged data to the main callback function.\n\n#### Input Array\n\n```javascript\nvar parallel = require('asynckit').parallel\n  , assert   = require('assert')\n  ;\n\nvar source         = [ 1, 1, 4, 16, 64, 32, 8, 2 ]\n  , expectedResult = [ 2, 2, 8, 32, 128, 64, 16, 4 ]\n  , expectedTarget = [ 1, 1, 2, 4, 8, 16, 32, 64 ]\n  , target         = []\n  ;\n\nparallel(source, asyncJob, function(err, result)\n{\n  assert.deepEqual(result, expectedResult);\n  assert.deepEqual(target, expectedTarget);\n});\n\n// async job accepts one element from the array\n// and a callback function\nfunction asyncJob(item, cb)\n{\n  // different delays (in ms) per item\n  var delay = item * 25;\n\n  // pretend different jobs take different time to finish\n  // and not in consequential order\n  var timeoutId = setTimeout(function() {\n    target.push(item);\n    cb(null, item * 2);\n  }, delay);\n\n  // allow to cancel \"leftover\" jobs upon error\n  // return function, invoking of which will abort this job\n  return clearTimeout.bind(null, timeoutId);\n}\n```\n\nMore examples could be found in [test/test-parallel-array.js](test/test-parallel-array.js).\n\n#### Input Object\n\nAlso it supports named jobs, listed via object.\n\n```javascript\nvar parallel = require('asynckit/parallel')\n  , assert   = require('assert')\n  ;\n\nvar source         = { first: 1, one: 1, four: 4, sixteen: 16, sixtyFour: 64, thirtyTwo: 32, eight: 8, two: 2 }\n  , expectedResult = { first: 2, one: 2, four: 8, sixteen: 32, sixtyFour: 128, thirtyTwo: 64, eight: 16, two: 4 }\n  , expectedTarget = [ 1, 1, 2, 4, 8, 16, 32, 64 ]\n  , expectedKeys   = [ 'first', 'one', 'two', 'four', 'eight', 'sixteen', 'thirtyTwo', 'sixtyFour' ]\n  , target         = []\n  , keys           = []\n  ;\n\nparallel(source, asyncJob, function(err, result)\n{\n  assert.deepEqual(result, expectedResult);\n  assert.deepEqual(target, expectedTarget);\n  assert.deepEqual(keys, expectedKeys);\n});\n\n// supports full value, key, callback (shortcut) interface\nfunction asyncJob(item, key, cb)\n{\n  // different delays (in ms) per item\n  var delay = item * 25;\n\n  // pretend different jobs take different time to finish\n  // and not in consequential order\n  var timeoutId = setTimeout(function() {\n    keys.push(key);\n    target.push(item);\n    cb(null, item * 2);\n  }, delay);\n\n  // allow to cancel \"leftover\" jobs upon error\n  // return function, invoking of which will abort this job\n  return clearTimeout.bind(null, timeoutId);\n}\n```\n\nMore examples could be found in [test/test-parallel-object.js](test/test-parallel-object.js).\n\n### Serial Jobs\n\nRuns iterator over provided array sequentially. Stores output in the `result` array,\non the matching positions. In unlikely event of an error from one of the jobs,\nwill not proceed to the rest of the items in the list\nand return error along with salvaged data to the main callback function.\n\n#### Input Array\n\n```javascript\nvar serial = require('asynckit/serial')\n  , assert = require('assert')\n  ;\n\nvar source         = [ 1, 1, 4, 16, 64, 32, 8, 2 ]\n  , expectedResult = [ 2, 2, 8, 32, 128, 64, 16, 4 ]\n  , expectedTarget = [ 0, 1, 2, 3, 4, 5, 6, 7 ]\n  , target         = []\n  ;\n\nserial(source, asyncJob, function(err, result)\n{\n  assert.deepEqual(result, expectedResult);\n  assert.deepEqual(target, expectedTarget);\n});\n\n// extended interface (item, key, callback)\n// also supported for arrays\nfunction asyncJob(item, key, cb)\n{\n  target.push(key);\n\n  // it will be automatically made async\n  // even it iterator \"returns\" in the same event loop\n  cb(null, item * 2);\n}\n```\n\nMore examples could be found in [test/test-serial-array.js](test/test-serial-array.js).\n\n#### Input Object\n\nAlso it supports named jobs, listed via object.\n\n```javascript\nvar serial = require('asynckit').serial\n  , assert = require('assert')\n  ;\n\nvar source         = [ 1, 1, 4, 16, 64, 32, 8, 2 ]\n  , expectedResult = [ 2, 2, 8, 32, 128, 64, 16, 4 ]\n  , expectedTarget = [ 0, 1, 2, 3, 4, 5, 6, 7 ]\n  , target         = []\n  ;\n\nvar source         = { first: 1, one: 1, four: 4, sixteen: 16, sixtyFour: 64, thirtyTwo: 32, eight: 8, two: 2 }\n  , expectedResult = { first: 2, one: 2, four: 8, sixteen: 32, sixtyFour: 128, thirtyTwo: 64, eight: 16, two: 4 }\n  , expectedTarget = [ 1, 1, 4, 16, 64, 32, 8, 2 ]\n  , target         = []\n  ;\n\n\nserial(source, asyncJob, function(err, result)\n{\n  assert.deepEqual(result, expectedResult);\n  assert.deepEqual(target, expectedTarget);\n});\n\n// shortcut interface (item, callback)\n// works for object as well as for the arrays\nfunction asyncJob(item, cb)\n{\n  target.push(item);\n\n  // it will be automatically made async\n  // even it iterator \"returns\" in the same event loop\n  cb(null, item * 2);\n}\n```\n\nMore examples could be found in [test/test-serial-object.js](test/test-serial-object.js).\n\n_Note: Since _object_ is an _unordered_ collection of properties,\nit may produce unexpected results with sequential iterations.\nWhenever order of the jobs' execution is important please use `serialOrdered` method._\n\n### Ordered Serial Iterations\n\nTBD\n\nFor example [compare-property](compare-property) package.\n\n### Streaming interface\n\nTBD\n\n## Want to Know More?\n\nMore examples can be found in [test folder](test/).\n\nOr open an [issue](https://github.com/alexindigo/asynckit/issues) with questions and/or suggestions.\n\n## License\n\nAsyncKit is licensed under the MIT license.\n", "maintainers": [{"name": "alexindigo", "email": "<EMAIL>"}], "time": {"modified": "2022-06-13T03:39:04.302Z", "created": "2016-05-18T23:25:43.646Z", "0.1.0": "2016-05-18T23:25:43.646Z", "0.2.0": "2016-05-19T08:17:31.695Z", "0.3.0": "2016-05-26T05:50:39.597Z", "0.4.0": "2016-06-14T18:29:05.418Z"}, "homepage": "https://github.com/alexindigo/asynckit#readme", "keywords": ["async", "jobs", "parallel", "serial", "iterator", "array", "object", "stream", "destroy", "terminate", "abort"], "repository": {"type": "git", "url": "git+https://github.com/alexindigo/asynckit.git"}, "author": {"name": "Alex Indigo", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/alexindigo/asynckit/issues"}, "license": "MIT", "readmeFilename": "README.md"}