{"_id": "lightningcss-linux-arm64-musl", "_rev": "37-746090f3e7117a087bb628091022c434", "name": "lightningcss-linux-arm64-musl", "dist-tags": {"latest": "1.30.1"}, "versions": {"1.14.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.14.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9d5fbbdddffbf1583462644a3e4934d38702e472", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.14.0.tgz", "fileCount": 3, "integrity": "sha512-8FSXmV7yPqk+1vErgdEO6OvDXDT/MkJMF+jSaUuL5wEvCC5yiJsaktNEJvn5EHsFbODi9DNvUt71sjeFCJma1w==", "signatures": [{"sig": "MEYCIQC2HQPDj/TBkfh4gOYYeADjOGkbB7Jp8kVdi+S+/nQlyQIhAPjfv2qi3OVgk3QyGOsRBsm+1USasV4D6tqAqtCINcP8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3014924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGXtPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEARAApBYAbPqO1kqwXGp4lHVC0UmC6KHPKy1oljVTslM5Bj+NsK3g\r\nI7Beo6KPcjohdMtPA004nDDBy50Kl7Pk+P2i0y7Xb8x8lacRBuWsVW1P/Qtw\r\ne0NUoKoocZU4AyhEgZN9WdRsiwJBhLKRs1TPk9BUR1L/O6rQ/qyO7/+C85aT\r\nn9EUOd9nrFPexTPycdrii1aAbYBVztQX23NHfVc4PeSkMg+EpcntOkUoY9GH\r\nwnH3FdtkJYPrKyA8GgXdIUoKQ0nuIAMiCkxYGiIk3FMnZlvv3oQmlsQ5mQTc\r\n37kHBxXcUYD3i38a3VlCkhz+aJiNsPfsHmL8mcmTp6eo5YIXhiP2YS6uappk\r\nSw2ylshUZPZ1h/5znWylrEO5Sbd1sp1qX2SMhQkYAPqfMO+QnhlRTeM1XW3z\r\n2JYbK5KfbPKwLmfiWpapSzJlfBzSxt+m9ixZOctLLF0NRM/iTDmKJ6AiNe9l\r\n4TslAea4a1mx91zhINKbZvU9uognkGZ09XYACK1L/2htwWd903ppOBFE7tOf\r\n8e2fWeBS+ZlevvjYarah9lQvFk21bHRVcA2E9fQOzr7M4/Y1me3pgVxwo3a4\r\nl0AWLQFjw9O8QwBCqkFY4ZFUKrAC4yHy1DJnKWMH4R03yDK0glmHzyLtqFMm\r\nk04QFidWYi7pABYbCjNEzt5943+tnUZthgY=\r\n=etPL\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "2d17e0e1ed1482525985ef4d30809b4c06d76944", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.14.0_1662614351160_0.952020954891309", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.15.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6628d7cb156e1efd5c8232b383f7d16704f92bd1", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.15.0.tgz", "fileCount": 3, "integrity": "sha512-xbSvBeoacWXL8l3CzGWSEfQ6ro3L7xwkQobtYOJi5Whlw1F5pLm3pTmxDcRlEClY3Vf6kVQmP0LCoNyfWtmfWQ==", "signatures": [{"sig": "MEUCIQCsNW1G4ZDksPvNR7t3gsWTtJA7JWkByCJTcn4U+Ji+AgIgKSulQ2RSA9JGTgHee2AvGUXX4WQdeo5x9/D+xhMhE2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3023163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIqcdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqT2A//WCFoIDNEVhxV8eOs7t8BucuSZmGKjwISxwjC0B4VjVFq/hpn\r\nN1aoYt79CkFfpBY9eX9eKCbtfdvnE76XsIXID2vuZSVz1dJ0vY4tnHTJk5UW\r\ncMp60tlmHfuDQ3vi11LOcyo5op7RbWuufMiFAwzXU1twRY6vEvditzOef8AN\r\nB0olzQtIlrr9b1g4p/MnrW+ruh/YW9/lliM664bVMkJnzi+1GpGP1P2sGYPz\r\n7IURACxfqi3en0NUyVQsWEv5EJhCvKObakLxzxpX3tAFXDdRbgSxWeSNW6SM\r\n+svqqms6VPGlcXPs9tiK1tZU4+sxBgrlUAM0Ww93ymOlaLexSZALB5ikPExX\r\nZhrhV5A30QcxmrzLV5gqAjMmmZF7dZtQHwHARrPwqRIDSMUFReUc7k3VJIYS\r\ngg5QHANb1WJTPQkeEF/BYu/WF5W0Jb+BnnCaN9LHE11kMo67h2pmyjCcyKvl\r\n0cMWAFr4m+5cSeWXxYz/g8gOsRsBnI5grqyrM4iiRhYGmb7pTmDp7bnzJtX7\r\nPZPEnfDrK7Jd0wyUaIgxEspLmYsw0tsxXZDa4ZQ6Hvq2hbGbJ5+/PBAwZbCh\r\nzOQ91U4ZTeYP2MKJQ475Io5FLFl+5ZBiGZfTAt79Kcx1+hdVtt82x6BD05fV\r\nHBvbn1Ir0hYpuAL0SiuVIW8Cn+z1+vyC/6Y=\r\n=0mFz\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "1a23835a0d72afc06ab5f98cbbee5fb03ae09074", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.15.0_1663215389253_0.19801136565188315", "host": "s3://npm-registry-packages"}}, "1.15.1": {"name": "lightningcss-linux-arm64-musl", "version": "1.15.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.15.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "13bb83b88cc9663377596f26311faa8c664aa4eb", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.15.1.tgz", "fileCount": 3, "integrity": "sha512-cGhslFivvMLSIsesZbEaUurXRJMGUftHukiY5DjWtXkA2iIqqV7TyK3j6ZJPy76hlRKsjU/WO8CrabAvr6kmsw==", "signatures": [{"sig": "MEUCIE8UDZhix2/eqmT2xtU7vkTxYH2n2g4EDemn/mL/vDr8AiEAu0ORsLAbbDYJOuuNfP9hXjui+G/uAJ7oTk61lm1ezLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3023163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjI/JAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgFRAAjFlshIhGZR3trU2Z8PbaaYGtC9/Kix0CexnpUwu/TyZLD0JS\r\nGPpMuyVM30jtLpy6Qovr7/OHQ1DxeK5XDb/wWKGeIbS0CmXYiL80wsGNRZVg\r\nZa+FGQlNHUpcLuUaOnjsmAruCwPYjrlojyt3rwWY6HO6iDP7V3F+RB/deYL0\r\nnJlv5UVmaK6H8rJmfYkKeO+qVuy60irH9BBGayR/W65mvhHm6WDxRDIEqt0Y\r\nZC8whufhOP1/B+C1u9TRSe+LAqmAkFaf909GBVOyHSpBK0nHWSILKu11f4Q0\r\no28S3Vc2FujJJF98usSfDTYD/F3tpNbFxmGTjMj5sJ7y0MUUfSWcgV4cajXt\r\nO1J+mlqgBP6IoTdZN31M/VLsoRAZc7eX7NUwMw0sEeM2LKIYW7BQXqEtWR0d\r\nLeuhVcBzrYPePwZqY6aAJBncs9XLSS9iZQMdvQNeWI/UF0S5CgdJUx2tvOTm\r\naoPcjgqS5QV0Ji224QDnClXqysU8oToEC1yLdiD+y0OQYLx1Madye/PzYdqS\r\nKIXZMIcfFJ5OaCMeZNxCNsyMD6CAUlWRNDb1bzFz9vwH0kUY8Ve6bBqPog0F\r\nDQnbJbZupaL4Oabg8L6TLfQSN4f4XfgmMgIbFw+kVS/vo8RRJ9G3M18qfauX\r\n7CCF7aRUPkeHu7W5SkRXm7uowiHe76YnzS4=\r\n=m5aA\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb66ece2f9b47bf4ef60848736dde697ec80a319", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.15.1_1663300160060_0.6918163347566428", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.16.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d06936e0570fb51d58cc18ef2fb8858aeecd1979", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.16.0.tgz", "fileCount": 3, "integrity": "sha512-1QXWStnTEo4RFQf0mfGhRyNUeEHilCZ0NA97XgwKwrYr/M7sYKU/1HWY00dPxFJ6GITR2pfJGo9xi3ScSSBxbA==", "signatures": [{"sig": "MEYCIQDL7+AfasRliTcT2Xb8FdEDuFN4xX/R7VSO8VRiyLpZKgIhAK0AMKhz6n/LJ3wg6kDeqz7YQZTruPCdr4GF2YKzmsfF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3772731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKTIfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7vQ/8CYhO9BhBWSQQIzwNNhPZUsd3udj+7FpS/p2ANIBH/ZlzkmGY\r\n4jctTmygWFOFLAgN/VEx5CPNqs+Tv2MhE/p/7PjIbqx87CGBUpkkpUNGn87/\r\n/X7bGiDFGcbudpbQ1acCELQfqx+5cisCK0qvJj4i6dQ37/1ayJNzn8YC8HLN\r\nSO5DMSOTftSVdNdGJXGri0PyoxvOpymov98e80zrQ/w4t6yIOwgfmnYmtXO+\r\nAN54vsL5cv8DMPD7jHjY55tUH1sCp8CZVG5CxDL/fL1Yd45mTglZ4HOrLirA\r\nTkzy0ou6ztwN2rOWMJys5CYwoNwkZifTJsbtoL0SkNe6TN2prXDJKU0uyoU5\r\nQsif2sStUgjHgTft4jjdZYBU+RtyO/1UW8iCkPpAyVOGF9hZs9oIW70Uv/08\r\nVHsPjvkouoH8rdEXXP4u37ymG6SJc0/uRG5oVrg4eD8PXb1aLdzp+8cc/5t0\r\nf0WtjaiV8oYFegpNdPNkzwl3nNWWx7zMKhKbCDxQSpx3MoGXWt0sXA2N0W9y\r\nBjZuSjzFxTSNg7tICJg5Vp2fDbHp5JfXa+IV7+fxLn2Y4pLpzaK1VPRLY+nQ\r\nkTLpEtX1tYMdCfouFloUuLUjDn1AeUGay5ej0oNyYVgafXL7O3Orrc9rMFtV\r\nioeKuEhWgQLu7Cx1j4uNjfL/At3xId8DqoE=\r\n=bSTo\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "200120e24aa31449703707db14ffef1bf40a7fea", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.16.0_1663644191250_0.710400377160934", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "lightningcss-linux-arm64-musl", "version": "1.16.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.16.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d13a01ed19a72c99b4ef9c5b9d8ee0dcdc4bf3ed", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.16.1.tgz", "fileCount": 3, "integrity": "sha512-VUPQ4dmB9yDQxpJF8/imtwNcbIPzlL6ArLHSUInOGxipDk1lOAklhUjbKUvlL3HVlDwD3WHCxggAY01WpFcjiA==", "signatures": [{"sig": "MEUCIFEjZHrSBtdmRSU5ZpbxBVqZpWWO0ohz0Nw0QCkfQDRzAiEA7hOrp40/ImCeop5twGop/e3JvYb7DF5taUEBEvRsI9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3690763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZ/acACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeBQ/+IQ8HD5VX8TQ1/ewUYD08Fd57LrjIy7JMbiNOtJoEwXdP+Sz1\r\nzGSf5CSNGrktK6V0avTTZOKBZVtJArrpIGthxjIM+dl44NTnjqg8Vfj/dQrL\r\ndop4HxE7BAfm+vgJLsIUtxgSCbnty9m3YFMdBdJQmuhjhavOo1eTbyuux62N\r\nSuRE56O9T0/CKDPXmZcM2uTguBe+k5EBvaa13gP1/BBkdCIw5MtKiA+5Zx4+\r\nCOk1fKjoXlho37zDk0aGi0RmTwY10m9jlU32EwXahVwMNw62kJLJg9CDT0ha\r\nCtwdoBFVBl/yj4nIj4bkcj4bPp8YdZX4lFonALlcY9eBMpKp1kHNl2JYOQ5j\r\nzyuhx2wXPFjEnw8TrPqA/WbsxTO6hEy7C3D8ch6Gvv31zF5eRVt2WSCMUWpr\r\nKAsxMrQzZAHCt0UCVbna1syBk8TihyuCFYpZ7yzGY/WqU03Os89UdCjRrLrc\r\ncbSdu1/Dj8otruWeQHBI2j8NMDTvgpx9jvSQoZs7hrnzu0bCjn7zayk+hsRW\r\nrsdWGbKqxOaJv/6QolanZy0UZaP1wG+IGGwVCRcGmgxn5rdqr1P02UqalbTy\r\n7+5VJoZzllfmkX4Rv8oFAeHgovUiWposN2E85710yrNvaISPDbVAb7wF4oHE\r\nlqUKVXnYraiQHiexZkxUkxM1gd7HYy1D7jc=\r\n=Htrr\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "87e808fd771374ae705886351ee62c4fac3d3630", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.16.1_1667757724322_0.34695634248246376", "host": "s3://npm-registry-packages"}}, "1.17.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.17.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "cd88f0c0d994c13d5eb95126a95165b9cc30cc90", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.17.0.tgz", "fileCount": 3, "integrity": "sha512-HtXlBG7mKDBGqqxKzyFb4m7BeJhO2jc+KGwcNz/3BJcQLGDbUyInVHC2kwIDtlVQi5Ugn7Hg+Q/kSOyYdnuQ3A==", "signatures": [{"sig": "MEYCIQCavvfoWO9R9TT2fTWT3XG34Ig+lG2D/r0lZHOlwtBsZwIhALV1lId1AYOmfzH2Hk/Q6CKGdn+Efu590ZVcgOdbWeX+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4661515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhje1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5ng/+NbVv8kCeQmB7O3LU33v4TLpNHedSCWU4+xgAwhWedZZUYek7\r\ncvxL1AreDj9Ctpqf6JuGMbd2YZkLWynPeTGdt6soqI5Bp8RqQGC8m9O6Sly6\r\njg2pDRlMI2QXnjedEspV3QEsiAZRFlJEFq/nH6FO2uIcnbqEkRv4NYuHcG51\r\nElJ1b4FzyLFTece8EAnT0N+P787/MjcsFGV45G+3NZIt/wGpG6qd88+WrMXd\r\nNgfbgN60R6PKPuAiSmyT45WFLGrd4jlGKpeHzaTyHowNnP7dx1SKXUoGm49j\r\n9vansvIIGDyakg/lzErdZ9e0ESpYFnGoPlhJxperxdOZxMS8b7Q3QEL2RYwj\r\nX6OSZs1H/j3h0e6VsqqRO1/atUQtcKYzwT4NYba3mg0/PmFt7immKhnDShAf\r\nIBIZGcoYmbLFwtppTWN5tx3Qyld/SZZS/FJfDXlq5GBxNdA3ARmwgl+8YQ2g\r\nH+Cby/gPvtWMxY1oHLKwggySyEj8EXMZh+/yYWNdw8/b2XYNq5v3n1MoiXXd\r\nPgAp1pykoDcHktjDLa9SBidhcRDC4Bl3bGrHzcu+fEEIndo2q3ix4EveMcp2\r\n8YiOZXB7MioHveOZZtL1sVUvbqtRKaGKZc5ucI/inLrJkgZPqfYQVGgkKRYK\r\nYp3NkijXcvf3RwpE5E0H3P0/vD3gBi8uhPA=\r\n=NYEm\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d4853b204cc80b051d29be324e2d16f621e76336", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.17.0_1669740468876_0.8311999793919878", "host": "s3://npm-registry-packages"}}, "1.17.1": {"name": "lightningcss-linux-arm64-musl", "version": "1.17.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.17.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "14e46b8d2f50e83a710c62432e447bd9f0c328a5", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.17.1.tgz", "fileCount": 3, "integrity": "sha512-/IgE7lYWFHCCQFTMIwtt+fXLcVOha8rcrNze1JYGPWNorO6NBc6MJo5u5cwn5qMMSz9fZCCDIlBBU4mGwjQszQ==", "signatures": [{"sig": "MEUCIQDM+MziHx2ykBDDysAzE+ytsbr8fye31w9BO8HQrQY8IAIgO9ztsYGyVxbEJ8uh5Iv0HvV2cPZ64XMa5myTsNoU9NA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4665611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh5M+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAWA//VuTdviMs+fBDif39ankDSth6NP6V/lr0w7FFcbU341Fbht4o\r\nY7Wwn6ggisG/JZVj56tLGSlKJUKc2Qm0yQiVif0rU8Fi1cwTV6HNE4kDVPX3\r\n8Mn96cB1ZOtGhzb7c+bxyAUl0Lrk/FpQvH+SJAYMEylV/QA5kaidygkoFBFT\r\nyiT0yjDxxKrcpGpCOpPUIsIRxpfAZGM8emSfPSIPrLrMSO0e//gRC8FX2lbl\r\npzXnZDfwESGfhmHN3Iask7lKnu619p5FWabuG9YN9al26aHpQIruV4CW9pr6\r\nEDfny+uJQP94mCfZlJ7dKFWADb0gT67Z5fg05dvzzmdYBnhbZZ9NJvEcJubb\r\nSL7r+N3QNvAEZj7EzexUypMufcQL+nmNxAagfp9X9eCzxrJSMYdfou43uFYO\r\nzDD9f/gE1HSB4XSM7a8MCpKxXSuN4VrwuKod8+Rv9Fej4gG3eAsDVwpmynWA\r\nLtep4Gkup42DCzRkgCwXYS01uqQVZbGxTSHnK+T/29unzy3wPUP3PKghvIHT\r\ns8ZLdriJBSmPaELRrP79kEWg1DRzoKcz2z7n14YiKN6YsD9Y9EFlPu+/KgRd\r\nMg5HRYKmgA7yFCQcASvAcznxpB3yer5cxr7kQSIX8C1eqkiTytnOh+ao/rqo\r\nYhQ2H+MR8+yPzkC1tYc6tOIChkbFU5DPrAQ=\r\n=4ir7\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "27cb9c2a382fd8d9702de8def9dd18db54280c43", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.17.1_1669829438544_0.9346069332900007", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.18.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.18.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "88393c101cf236ea0cdc97fddd66b82db964d835", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.18.0.tgz", "fileCount": 3, "integrity": "sha512-2FWHa8iUhShnZnqhn2wfIcK5adJat9hAAaX7etNsoXJymlliDIOFuBQEsba2KBAZSM4QqfQtvRdR7m8i0I7ybQ==", "signatures": [{"sig": "MEQCIBBj46hstJcwYKvgfBvYFErafFsQ7ODaeEyeECzTQgQKAiAeSPNz5Cg/wAz/0YbYD/0fr4CTfzNwAgR22j+XkmGE+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7786160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtbCnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAqg//Rf6LtEJqleqxXg4A2iLBuVy38i4dNZwv/4G7/D1sujSURhqy\r\nrGT4oOCoFOz43kC3DQs/jydDAkfVq+Tz2s6M7NqtdrCWj+IgzWLUMcO6WWxG\r\n2/352PT69fnTQtR0pgGeSmIQS6ycMWZQjcyX7CBhDqlzRK0z6+cvXTaOG4K5\r\nJ1YFsMYUOGoU36MbxwVmOVmRgReqMM6ckQ6igQK+J4zE1gp2LMQfh51zJkH8\r\nGld4eNHRPGn1igqPnEPyA/k8minB5AlsKKD3EhdigmMlc85J++vDgMHIYHCV\r\nbCvHfIJ+0tBSGIXu6/gXBEaLUaHpaxmPTQGRkX/TxigYts+hwH5HWv0TDDtI\r\nXmufKPHTzJq8J9T28YoOsTY06HKB6gvFSJx7JdliyUDMm3F7vlnaz/QiCbmA\r\nsAd9Wid1n1bf5IBx7sUpUZnqk7XGPOhyfmWmsA7bs2XORHMlh6hijOHxxIHx\r\nP20Dt8RFFbn4afbbyMl44k/eaE1iH4CIwZPC5+Hl10aT8RB2ZPTxS7gsjgpY\r\n/LOTTEhW/PFWoeOFrc11CI2bNUg9b8UybZVucr/osQwJXUSNHv9RXzQTEgj8\r\nbrHMqVA4cv+E4UyFEVKx5+cx2CkoUi/4ysr9kCWs+oVUT48CkUJsMLYf5y3T\r\nieuJI9RvjLJmZZspdSn1IBgMrxYZLiukxrU=\r\n=WeD/\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "6fb77292c9ef4320fa85a1e26fec3fbdbae3cf8a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.18.0_1672851622751_0.8148462392357141", "host": "s3://npm-registry-packages"}}, "1.19.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.19.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.19.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "85ea987da868524eac6db94f8e1eaa23d0b688a3", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.19.0.tgz", "fileCount": 4, "integrity": "sha512-vSCKO7SDnZaFN9zEloKSZM5/kC5gbzUjoJQ43BvUpyTFUX7ACs/mDfl2Eq6fdz2+uWhUh7vf92c4EaaP4udEtA==", "signatures": [{"sig": "MEUCIDdlUbqqf+CzsG1nDnBGHqu/jJ5SrUkJ45XEJ3N5wp/ZAiEAsxk3AyCBVGnYqelPyCLjL3koSezgPhPKRBlYQh7JbIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7375864, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6l5UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqV3A/+IbvfZp4sMxkpXBmlloJwsSX8QXwG8gErOrdKLrJm5bhAApdm\r\nqImQl5826S90pKnhMOd6JqY7ZpBJ+yPiaTNGUBAyPcHv+5ClaGpdaOykMaP2\r\nLIIrXh7oM2rU0GcqAVomgz5h6/RqD2KHhDPxIYOm6L/HmsDjBG8l6/gjc2Zc\r\nV4/AwPpLwMmyQR0XfoVyPp/5J4yOQ5a3CXiX2qCHtO50oWdB+pNplK2m0wMH\r\nFQeRdYUZrVhatR35VFgo83erKckKAXhrvTozik5cLBuzDQiLtrbCsaOogEtp\r\nEuG7gn43diGeuCuG/XShjGMShweOmxsHDoGLWSizcnySMVtaoZgunGXQ90ss\r\nVH1sefC77toiw9alaJHK+NbXBjhWN3fgQ/0aNnXXsume986ZFslfZHXzmrdt\r\nQPzYCYxJYMXodQpymBlZCYkWNJ5ctHVW1YmuvJxGG27xmulFcPBslQjpXN7o\r\n74bJervXxuoXcUzDT5gcum7vJ+OIJnzUGnzDfUq4IdLOJWqZlUb+bAQeZAIx\r\nye1yPDe8F34AuWM7BijOJ0J+AK5L9MmxIGiZlQsiwg/xa93kkhX2ofBmfFwV\r\nGFzDYHUyaGicu/n61vZe7BY37PYdyGAc2B6+aGsy94xdp7hITkBZ4ryNhLkM\r\ngnuMJaotUd6AQvb/xqFEkKYsgzPagxca7Mc=\r\n=GM7q\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "05c23f1269321d4b072f6264a5c9cb6edd8a02bb", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.19.0_1676303956048_0.9822181171553734", "host": "s3://npm-registry-packages"}}, "1.20.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.20.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.20.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8d812309c4e70398cee79fcdc452548ca521a6eb", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.20.0.tgz", "fileCount": 4, "integrity": "sha512-Po7XpucM1kZnkiyd2BNwTExSDcZ8jm8uB9u+Sq44qjpkf5f75jreQwn3DQm9I1t5C6tB9HGt30HExMju9umJBQ==", "signatures": [{"sig": "MEQCIBSHepGlWPmoyZW0gvH3gB5Nm1WnWqpmZfAnUlV/s07ZAiBFgQpp/koh1Xlghi+GwyCIbwgo8DA0BgWnIQ+dmW2BcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7609344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQGRHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpwiw/8D+IaiWgVZaHxp/f5xLCZXmS/HD9/GcXkRXbYPoADdveQHonQ\r\nyZBhwffr27kV7OHdTsxBKowLNaXKRFMlO2TO6DYjM+vXzeO5iyINEFmSgVgT\r\n7Tv6VDpfNC/vsSdkwsqu/OCIkXeDdZ4tY1uZZgDtzGb3yADDrMcxHwif788y\r\nInbkeCDRZtCmKHthnFJGE5WdNKBMCMnCqMOFPWVn4hTFkOBLSG2NUEbFMciJ\r\nybAUO9wSRfVLacMu3H3Cvruhc/zO+79t52GUXitykqRXE+Df4NhpjFrl3oDn\r\nbWC7uh02dT6p16RgcuWa5fkGXjR7lQ6q9a2W3b79u14c93u5N5+GpsdT8AZh\r\nsoutPmF32M+TX1lDDeNdc97PCw8iHn17KhjCqldi1b6K/yssvjz1ryV+Sh/6\r\nvBDXx4sRZR+kmFr6E43kcu7l9USsjzel46365TeMElQCzhOt8wahnnuSJueV\r\nLLH4QB29+fnMyKfctS/5QMNUzwOOeDrcTQxWhbiaGt6lPTrvulxLV8KqT4DY\r\nl+O7Muef3GLdekx9jbIhIAquq3KgGZiXh2KLq7kCd+07ISAjnAQvMqZwXCnm\r\n2Qq0m8bS8sy3AS/9GeW2Qw5Qautq410P7Pc42eW7OKjH/mKxqX/g1mm1JwjW\r\nuhLx64Hl6bg0HtjxrQAlXr4F2F2ztf43Bc0=\r\n=YaI7\r\n-----END PGP SIGNATURE-----\r\n"}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4028d4d9026750b31b813ecb4ca5387dba8dd396", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.15.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.20.0_1681941574912_0.09854701061104687", "host": "s3://npm-registry-packages"}}, "1.21.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.21.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.21.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d5963868d6c20f2ea697f3ee19a34f38737d4ff5", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.21.0.tgz", "fileCount": 4, "integrity": "sha512-4Zx51DbR41neTFMs28CI9cZpX/mF5Urc6pChTio5nZhrz6FC1pRGiwxNJ+G15a/YPvRmPmvQd3Mz1N4WEgbj2A==", "signatures": [{"sig": "MEQCIAmcWNIkTofCJeM3TrUwSXrSAJQNrpxnAyk+9ytVpnlSAiAnSG+l4xvZML/pyT6goQWtfcL+RKUyYA1h/9HKf/kFvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7851008}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "11f4179f81041e14a04536d3bb0618f06de4e454", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.21.0_1686117498088_0.9298961347301291", "host": "s3://npm-registry-packages"}}, "1.21.1": {"name": "lightningcss-linux-arm64-musl", "version": "1.21.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.21.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b59848d392542210e1caa7a1abff16c7d0adb1cc", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.21.1.tgz", "fileCount": 4, "integrity": "sha512-vGaVLju7Zhus/sl5Oz/1YbV7L/Mr/bfjHbThj/DJcFggZPj1wfSeWc6gAAISqK3bIAUMVlcUEm2UnIDGj0tsOQ==", "signatures": [{"sig": "MEYCIQCiU0WI9CiL0xnJtsUDCtLq7xV6iJThuPhiUlhVYJF1+AIhAKjuSxpWCLpvcxN0kZQ0AJgbVHMR4FwV2LfUnz1QjA7Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7851008}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a36c105af3bb83370f64e28a8cf98b7dea52a6e0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.21.1_1687660735948_0.6368853550170119", "host": "s3://npm-registry-packages"}}, "1.21.2": {"name": "lightningcss-linux-arm64-musl", "version": "1.21.2", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.21.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b369927b01299af67f89654388cfdc378bd1a377", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.21.2.tgz", "fileCount": 4, "integrity": "sha512-dTIaIbOgo/mYzkEi2ESI9+ciZz7un8G0AxiM8E8lrkelm3lgTnSO/4fWaEfx+u9LsVM7dgAN5cfiDBQCr2koYA==", "signatures": [{"sig": "MEYCIQCk8OhOpAu17cvZl3btLvdryg6DZdYnNfZU+nXbm3Px+AIhALpkTrTFWIqUpLBOtTt5MePsOsKEaKfzcoW0DR01yako", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7855104}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "e4963a58a813e02b47a7dd0320defcf0c4182512", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.21.2_1688266098512_0.8039700257194264", "host": "s3://npm-registry-packages"}}, "1.21.3": {"name": "lightningcss-linux-arm64-musl", "version": "1.21.3", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.21.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9cd07bad2a95a10d29bb999c16aea3a39baa7821", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.21.3.tgz", "fileCount": 4, "integrity": "sha512-UIfZrGUHWEdjDXa/lGjG7YaPsS7OURfqazblwvrQIODXKg8K32qMk0lzcvPFSBae8upRt4NE5DFKypbENI4xng==", "signatures": [{"sig": "MEUCIFVy7sTWMIgkf128eE2gHN5OcEZDGTIrgoM7g0JDDl/3AiEAoW17ZO7HSy8A1z1ANNO5o4HU2NMRglJfJveYzx7nLTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7855104}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "83bab71e80781b4d2b128f5938099b2f0c8239bd", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.21.3_1688397351345_0.06256298619707445", "host": "s3://npm-registry-packages"}}, "1.21.4": {"name": "lightningcss-linux-arm64-musl", "version": "1.21.4", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.21.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a3dc50ce586bcfaa9499335fe17317bb1823d69b", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.21.4.tgz", "fileCount": 4, "integrity": "sha512-qfGAOBXUatU1rSpGep3FvsgHqRY5vsjEPXylYuDVhdc/VoWFshTEC8W1pfgHBwGvl9S8HmHGHFPZATuidUHxKw==", "signatures": [{"sig": "MEUCIHhPdAKsscRkeDHXDqu+5jtdFV8Xdqv3ANJWwveZq19EAiEAuOvtI2ZdVfRTCukZLUHciu/qWM92rM42s6lu+xOtMgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7855104}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "bd517fde245dcce75a4b2eb21a25b147d54eb64a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.21.4_1688441873599_0.002768439887855134", "host": "s3://npm-registry-packages"}}, "1.21.5": {"name": "lightningcss-linux-arm64-musl", "version": "1.21.5", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.21.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6dd43b3436432efdadb58949fb3821d4206044e9", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.21.5.tgz", "fileCount": 4, "integrity": "sha512-bc0GytQO5Mn9QM6szaZ+31fQHNdidgpM1sSCwzPItz8hg3wOvKl8039rU0veMJV3ZgC9z0ypNRceLrSHeRHmXw==", "signatures": [{"sig": "MEYCIQCJlvOVR26lmq/wEKYMf6LULlAhmGZ4amHMdznHxhFmZwIhAPCtrwvZ1lecJ6GnweHdUjDRXcf5lr9XG7ld7WkRryo7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7855104}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d44a73c3c000ccebbe8355b7cf9272236e573b4d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.21.5_1688531417480_0.12933293683377878", "host": "s3://npm-registry-packages"}}, "1.21.6": {"name": "lightningcss-linux-arm64-musl", "version": "1.21.6", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.21.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "aa869182c8669426cd638df0e6a141b39101d486", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.21.6.tgz", "fileCount": 4, "integrity": "sha512-Yfy7yi+VCkaw711aJJR4spH1ou/sTBCxy8AIAVby7Nb/cqSeD4PelgDoiOQ18tKskzQC3LD3NMbJH+1Wl3DJ9A==", "signatures": [{"sig": "MEUCIQD02c/VeMJ/tQYruV6huJwyHPWM1IsstvI9Tkv1WLudjQIgJF4cyoIqRoG4ipGpc7U46oS3dgPE7XEGKoEhYlRn8Vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7863296}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f485eb51d13e677a674cb10e170c71138446b71d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.21.6_1692511451630_0.9058313289492657", "host": "s3://npm-registry-packages"}}, "1.21.7": {"name": "lightningcss-linux-arm64-musl", "version": "1.21.7", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.21.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "160e07457051df02c4b580dc8d13e5a5f7fe7404", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.21.7.tgz", "fileCount": 4, "integrity": "sha512-pfOipKvA/0X1OjRaZt3870vnV9UGBSjayIqHh0fGx/+aRz3O0MVFHE/60P2UWXpM3YGJEw/hMWtNkrFwqOge8A==", "signatures": [{"sig": "MEQCIFFkj137Qzk23fK91I3kvzadhWkbpVTAnZOApGZkAIyRAiBmLrLC6PZBPqNbTwgArf6em0+NGLSEPfDOSpQrkRCd7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7863296}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "393013928888d47ec7684d52ed79f758d371bd7b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.21.7_1692555050444_0.6963761428728898", "host": "s3://npm-registry-packages"}}, "1.21.8": {"name": "lightningcss-linux-arm64-musl", "version": "1.21.8", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.21.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "98c74b70d99e08efb3cc6dacd0c57d516a15c2e7", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.21.8.tgz", "fileCount": 4, "integrity": "sha512-01gWShXrgoIb8urzShpn1RWtZuaSyKSzF2hfO+flzlTPoACqcO3rgcu/3af4Cw54e8vKzL5hPRo4kROmgaOMLg==", "signatures": [{"sig": "MEUCIGh123OiR1W7C/9WFTm6I1DX6mv59M5f8hINYCBiGOtVAiEAvlXhE/WU1oY9TfZoPSAiD4fcYNyqPqaPKONm9topHrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7863296}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81cb0daee30dad924b14af7fbc739ddfe31d7f9e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.21.8_1694407632984_0.6329736279743208", "host": "s3://npm-registry-packages"}}, "1.22.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.22.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.22.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8d863a5470ee50369f13974325f2a3326b5f77df", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.22.0.tgz", "fileCount": 4, "integrity": "sha512-RRraNgP8hnBPhInTTUdlFm+z16C/ghbxBG51Sw00hd7HUyKmEUKRozyc5od+/N6pOrX/bIh5vIbtMXIxsos0lg==", "signatures": [{"sig": "MEUCIQCsG/Du8LTABK7L3RuI11wI+Q8h7f+L78NPvqUB46/IMAIgUWRcmQ+2qsrNSJd/5/jVyQj/s4ta896DibmwdVMEAR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7863296}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "7ff93ca5c69ba9df415e1e2319d275e2cec249d7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.22.0_1694990943025_0.514064578504625", "host": "s3://npm-registry-packages"}}, "1.22.1": {"name": "lightningcss-linux-arm64-musl", "version": "1.22.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.22.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "cff86acaa98a0245add5a333098befc894802137", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.22.1.tgz", "fileCount": 4, "integrity": "sha512-MCV6RuRpzXbunvzwY644iz8cw4oQxvW7oer9xPkdadYqlEyiJJ6wl7FyJOH7Q6ZYH4yjGAUCvxDBxPbnDu9ZVg==", "signatures": [{"sig": "MEUCIQDZsrRmViXV8yd5+OVfSiTw7H0khrWSMTSDEFLcvfuz6AIgPVYNrHF7DYSJ1r5vawGkGU2/XgVXioV+7xqVbXHGsvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7863296}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4994306f8f8d98a2b37433fced50efdacbbab901", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.18.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.22.1_1699395814681_0.2463358164105578", "host": "s3://npm-registry-packages"}}, "1.23.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.23.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.23.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3212a10dff37c70808113fbcf7cbd1b63c6cbc6f", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.23.0.tgz", "fileCount": 4, "integrity": "sha512-cU00LGb6GUXCwof6ACgSMKo3q7XYbsyTj0WsKHLi1nw7pV0NCq8nFTn6ZRBYLoKiV8t+jWl0Hv8KkgymmK5L5g==", "signatures": [{"sig": "MEQCIG0x/c2Hrup636/OtQL/h9lMNcIZ+GkzDmXRk2+ITXQrAiB7FKMCU3E29n+CXMNK2QjBWDsPEfG6GbGKBwztDpIkKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7900160}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b47f496a86075adcb6719aee3a8867e749a880b9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.23.0_1705276076397_0.4129499073208198", "host": "s3://npm-registry-packages"}}, "1.24.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.24.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.24.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "be696ac45e7d211accc812750bfe6d61ef37b480", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.24.0.tgz", "fileCount": 4, "integrity": "sha512-5wn4d9tFwa5bS1ao9mLexYVJdh3nn09HNIipsII6ZF7z9ZA5J4dOEhMgKoeCl891axTGTUYd8Kxn+Hn3XUSYRQ==", "signatures": [{"sig": "MEUCIAkF82GjLOCrJrOBevjNw4tRUt8SmYv7avaZdJXw+EuuAiEAlZjVeBd6izPZ9ur4erGgDCvw2V7+fQ3VPZpUhc0tePk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7990360}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "30b9d79b3d277dfb90d90d78cd9182ac755ad6cf", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.24.0_1708648893347_0.06283955765754956", "host": "s3://npm-registry-packages"}}, "1.24.1": {"name": "lightningcss-linux-arm64-musl", "version": "1.24.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.24.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "644abd32c09c87228bfb5dda21e8d3f75da6f731", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.24.1.tgz", "fileCount": 4, "integrity": "sha512-JCgH/SrNrhqsguUA0uJUM1PvN5+dVuzPIlXcoWDHSv2OU/BWlj2dUYr3XNzEw748SmNZPfl2NjQrAdzaPOn1lA==", "signatures": [{"sig": "MEUCIEfbwDGeuzC05fSzfVEPT4sl2rrJ20znMU94hGcmiJL+AiEA34xZyt9lue0HWj+hi6Han0C+vu3936BnFK1P/vjyyjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7817960}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "baa1a2b7fa52eeb3827f8edcc2e14de33cd69ad0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.24.1_1710476584629_0.2691698913870535", "host": "s3://npm-registry-packages"}}, "1.25.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.25.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.25.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "582659a22f6014a74e4deaeeccfc055bccfa6d90", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.25.0.tgz", "fileCount": 4, "integrity": "sha512-4kw3ZnGQzxD8KkaB4doqfi32hP5h3o04OlrdfZ7T9VLTbUxeh3YZUKcJmhINV2rdMOOmVODqaRw1kuvvF16Q+Q==", "signatures": [{"sig": "MEUCIQDvRg0osFzeephFMAFjV8F4BFYgo65NVAzlScGeyl5JhwIgYWcI7LoXHs6nR3SMh2ruy4q6+Nrf79dfH7Izbbc/KZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7953128}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81d21b9f5201c6eb8365fbb4fa81cbd947c3474f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.25.0_1715973759279_0.3400102453466174", "host": "s3://npm-registry-packages"}}, "1.25.1": {"name": "lightningcss-linux-arm64-musl", "version": "1.25.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.25.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f45d7c832bb9c73a13dfc59c8de4692f7e47040a", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.25.1.tgz", "fileCount": 4, "integrity": "sha512-IhxVFJoTW8wq6yLvxdPvyHv4NjzcpN1B7gjxrY3uaykQNXPHNIpChLB52+wfH+yS58zm1PL4LemUp8u9Cfp6Bw==", "signatures": [{"sig": "MEQCIAL/YzMVeTIgrctpKlIzWoS00+wPkqHo4zAPNVK9OJyoAiBikAwvNp/36y9sFm9p/fTkZKFCnvJejX+mtnhN5vHYug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7957224}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "fa6c015317e5cca29fa8a09b12d09ac53dcfbc77", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.25.1_1716617182713_0.20183328392362943", "host": "s3://npm-registry-packages"}}, "1.26.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.26.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.26.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "df309b1acafbded280171db44eb615f2d26e2cab", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.26.0.tgz", "fileCount": 4, "integrity": "sha512-XxoEL++tTkyuvu+wq/QS8bwyTXZv2y5XYCMcWL45b8XwkiS8eEEEej9BkMGSRwxa5J4K+LDeIhLrS23CpQyfig==", "signatures": [{"sig": "MEUCIGucBNoiS9Xkx1pPXQ5A64LCMUwNNdpcRWE4IYvF4x3+AiEAsM8qEPcfVb+4p7ZNGgv4cGlPiSNcaGxly+X5zcrrUgs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7846632}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "0bcd896e81a8a2c5d847f626a37e2cffea79e2a0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.26.0_1722958371059_0.32230659974489484", "host": "s3://npm-registry-packages"}}, "1.27.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.27.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.27.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6682ff6b9165acef9a6796bd9127a8e1247bb0ed", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.27.0.tgz", "fileCount": 4, "integrity": "sha512-rCGBm2ax7kQ9pBSeITfCW9XSVF69VX+fm5DIpvDZQl4NnQoMQyRwhZQm9pd59m8leZ1IesRqWk2v/DntMo26lg==", "signatures": [{"sig": "MEUCIQClou2OidmI6QLODjKlFmJxNftYVb7nI0Jn+sGEpCwkfQIgNH+8BQfOE0Ha8paaXZBXrfKrpG6Vxe4z9AreoXk733w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7854824}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb49015cf887ae720b80a2856ccbdf61bf940ef1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.27.0_1726023644311_0.8927549274692979", "host": "s3://npm-registry-packages"}}, "1.28.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.28.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.28.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "82fc2d59c9088f42cf88765f582d1045922acac1", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.28.0.tgz", "fileCount": 4, "integrity": "sha512-Ma7dyJV+TvZdQLUgC3wm2AvVkmC0ElCXMS2Fgc+abqgf6cBJOad12emFKiSGJdAxtHlVLv5+Bn5wT0nbwTAWRw==", "signatures": [{"sig": "MEQCIEOcxoTurh9EL14Zxd44aLD9vg9thgsf8flY9A1+lndBAiBL2a0+AUflsS3W22kz36VVFPyjKO8rDO7yKXMDt/eaEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7858957}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "8a67583105757e4a25378d65d243b87a345b2c2d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.28.0_1730668664070_0.39540159279147735", "host": "s3://npm-registry-packages"}}, "1.28.1": {"name": "lightningcss-linux-arm64-musl", "version": "1.28.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.28.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9d2561d8a5ecfb3f1f18651da0acc592e837ea3a", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.28.1.tgz", "fileCount": 4, "integrity": "sha512-dnMHeXEmCUzHHZjaDpQBYuBKcN9nPC3nPFKl70bcj5Bkn5EmkcgEqm5p035LKOgvAwk1XwLpQCML6pXmCwz0NQ==", "signatures": [{"sig": "MEQCIGVy3Ikq7atWTFlm4n6C71UeC+/QXWbv18DiMEn8H+YtAiBpZx75CwfMoXZBjZHsRUUErnWdHtaOK6R2ZaliH9XUhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7863053}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a3390fd4140ca87f5035595d22bc9357cf72177e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.28.1_1730674701711_0.030392186840827895", "host": "s3://npm-registry-packages"}}, "1.28.2": {"name": "lightningcss-linux-arm64-musl", "version": "1.28.2", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.28.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4d9bc20cf6de28c4d0c586d81c577891555ad831", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.28.2.tgz", "fileCount": 4, "integrity": "sha512-1SPG1ZTNnphWvAv8RVOymlZ8BDtAg69Hbo7n4QxARvkFVCJAt0cgjAw1Fox0WEhf4PwnyoOBaVH0Z5YNgzt4dA==", "signatures": [{"sig": "MEYCIQD26u80W68fSA5JTm7kM3C0M/JeoqZZU6e4INPAoHuHOQIhAKZsi+FRZ9OiN0AyiB7hXb5kBjjxqtK3UPx5zLc60uua", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7871245}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "9b2e8bbe732d7c101272ddab03ac21b88bf55c4a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.5", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.28.2_1732512292039_0.3689635041762125", "host": "s3://npm-registry-packages"}}, "1.29.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.29.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.29.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "fbe9277426b0e36290510cb1a1296ac5b89d1bef", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.29.0.tgz", "fileCount": 4, "integrity": "sha512-87G89xyMaf+J2mKFjGvMcd8/pHXloN0bLIBcenPXyvFJWgZsDhNtn7eyCGZN9783DjUTdnNw5x7LE2JHIVEnrQ==", "signatures": [{"sig": "MEUCIFLegBcLXDIqbKMxXx7UrH+zNpoJtzuzChxlwycGzsAmAiEAyAw++Ph5A43lpYgYJ3S3DC5A36324I57J4ioOmAw1So=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7711477}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "222fa9f7ad8b4004afd7a30c2f99cab3c8621191", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.29.0_1736401663985_0.759910306661896", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.1": {"name": "lightningcss-linux-arm64-musl", "version": "1.29.1", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.29.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "389efccf80088dce2bb00e28bd7d1cfe36a71669", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.29.1.tgz", "fileCount": 4, "integrity": "sha512-UKMFrG4rL/uHNgelBsDwJcBqVpzNJbzsKkbI3Ja5fg00sgQnHw/VrzUTEc4jhZ+AN2BvQYz/tkHu4vt1kLuJyw==", "signatures": [{"sig": "MEUCIQCbR0XVIRU4cTO8KYP32+F1ebcKzad2Y9JrrIdSkvzxZgIgUP8U79N4dP00CdDB+vbSC0mdRhiLf5F9BfSU1cwo3+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7711477}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b10f9baf8878411bf2b09dfe8d64ba09ef7a4eac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.29.1_1736445759327_0.5995545439842815", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.2": {"name": "lightningcss-linux-arm64-musl", "version": "1.29.2", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.29.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a95a18d5a909831c092e0a8d2de4b9ac1a8db151", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.29.2.tgz", "fileCount": 4, "integrity": "sha512-Q64eM1bPlOOUgxFmoPUefqzY1yV3ctFPE6d/Vt7WzLW4rKTv7MyYNky+FWxRpLkNASTnKQUaiMJ87zNODIrrKQ==", "signatures": [{"sig": "MEQCIDQxY9MST/FosO5xfgu9D+XYagJbZ3+7cFEYK+Ve0sEgAiAcEDQwv0QUeisBXina9k9NclrSLL7gH7dcz1yCOyprJQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7715536}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f2303df29448a55c5f4f284f747eb53760769fe4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.29.2_1741242119438_0.7088731824331411", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.3": {"name": "lightningcss-linux-arm64-musl", "version": "1.29.3", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.29.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "038e2088b6594d27244f7e00463b448b5edc2139", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.29.3.tgz", "fileCount": 4, "integrity": "sha512-dxakOk66pf7KLS7VRYFO7B8WOJLecE5OPL2YOk52eriFd/yeyxt2Km5H0BjLfElokIaR+qWi33gB8MQLrdAY3A==", "signatures": [{"sig": "MEQCIEAcZn+pkcyy5qJ49iMBC1/KtCcISZF0cvN70znssxorAiB8lD4Q/havUtDadgwhG5+wkx2TkJfU/DLt2zHO+h7kZA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7719632}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "80eb8617c5f8f5519ed85bd3eb6d8953f4d32493", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.29.3_1741974595291_0.24490582394426008", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.0": {"name": "lightningcss-linux-arm64-musl", "version": "1.30.0", "license": "MPL-2.0", "_id": "lightningcss-linux-arm64-musl@1.30.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "7a043124e2446df54b0800ebee99a6f2e728892c", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.30.0.tgz", "fileCount": 4, "integrity": "sha512-RNZNW/AyKax8wWR4xMKoyAb40dqhzOtnAw4knjbyxJUUEL0wzBEXO3k44AS3UFRjxTyd/s46adVQXxE/vOaSgg==", "signatures": [{"sig": "MEQCIBn7TBdd7Q0uFbCqd6Pxn0JZRp3iACim4VHxaKdXe5cPAiB+t4RJBGl373ZKIVmlQvsJt78RipZnAlINP6SU/VdcdA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7764688}, "libc": ["musl"], "main": "lightningcss.linux-arm64-musl.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "474c67c046b4a54217166057296fa4ca28764858", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-linux-arm64-musl_1.30.0_1746945552809_0.3476278160149604", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.1": {"name": "lightningcss-linux-arm64-musl", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.linux-arm64-musl.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "resolutions": {"lightningcss": "link:."}, "os": ["linux"], "cpu": ["arm64"], "libc": ["musl"], "_id": "lightningcss-linux-arm64-musl@1.30.1", "gitHead": "f2dc67c4d3fe92f26693c02366db1e60cae0db27", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-jmUQVx4331m6LIX+0wUhBbmMX7TCfjF5FoOH6SD1CttzuYlGNVpA7QnrmLxrsub43ClTINfGSYyHe2HWeLl5CQ==", "shasum": "f2e4b53f42892feeef8f620cbb889f7c064a7dfe", "tarball": "https://registry.npmjs.org/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.30.1.tgz", "fileCount": 4, "unpackedSize": 7764688, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCID459OxjwF4dAL/CqdhvHQfM4BCf1rzWwv/LobASZZH+AiBbqneYxS459HEbvrLlwoTsYTKHuEZ4p8V12liM7wALog=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss-linux-arm64-musl_1.30.1_1747193923998_0.7811927102372198"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-09-08T05:19:11.087Z", "modified": "2025-05-14T03:38:44.511Z", "1.14.0": "2022-09-08T05:19:11.427Z", "1.15.0": "2022-09-15T04:16:29.506Z", "1.15.1": "2022-09-16T03:49:20.252Z", "1.16.0": "2022-09-20T03:23:11.489Z", "1.16.1": "2022-11-06T18:02:04.580Z", "1.17.0": "2022-11-29T16:47:49.173Z", "1.17.1": "2022-11-30T17:30:38.821Z", "1.18.0": "2023-01-04T17:00:23.068Z", "1.19.0": "2023-02-13T15:59:16.316Z", "1.20.0": "2023-04-19T21:59:35.167Z", "1.21.0": "2023-06-07T05:58:18.348Z", "1.21.1": "2023-06-25T02:38:56.194Z", "1.21.2": "2023-07-02T02:48:18.811Z", "1.21.3": "2023-07-03T15:15:51.621Z", "1.21.4": "2023-07-04T03:37:53.876Z", "1.21.5": "2023-07-05T04:30:17.796Z", "1.21.6": "2023-08-20T06:04:11.870Z", "1.21.7": "2023-08-20T18:10:50.699Z", "1.21.8": "2023-09-11T04:47:13.231Z", "1.22.0": "2023-09-17T22:49:03.335Z", "1.22.1": "2023-11-07T22:23:35.054Z", "1.23.0": "2024-01-14T23:47:56.650Z", "1.24.0": "2024-02-23T00:41:33.644Z", "1.24.1": "2024-03-15T04:23:04.890Z", "1.25.0": "2024-05-17T19:22:39.612Z", "1.25.1": "2024-05-25T06:06:22.907Z", "1.26.0": "2024-08-06T15:32:51.411Z", "1.27.0": "2024-09-11T03:00:44.638Z", "1.28.0": "2024-11-03T21:17:44.465Z", "1.28.1": "2024-11-03T22:58:22.032Z", "1.28.2": "2024-11-25T05:24:52.298Z", "1.29.0": "2025-01-09T05:47:44.446Z", "1.29.1": "2025-01-09T18:02:39.764Z", "1.29.2": "2025-03-06T06:21:59.683Z", "1.29.3": "2025-03-14T17:49:55.602Z", "1.30.0": "2025-05-11T06:39:13.103Z", "1.30.1": "2025-05-14T03:38:44.355Z"}, "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "license": "MPL-2.0", "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "description": "A CSS parser, transformer, and minifier written in Rust", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "This is the aarch64-unknown-linux-musl build of lightningcss. See https://github.com/parcel-bundler/lightningcss for details.", "readmeFilename": "README.md"}