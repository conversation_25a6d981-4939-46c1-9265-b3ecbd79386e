{"_id": "tree-kill", "_rev": "33-3a5792bff476d02909fc4682562486fd", "name": "tree-kill", "description": "kill trees of processes", "dist-tags": {"latest": "1.2.2"}, "versions": {"0.0.1": {"name": "tree-kill", "version": "0.0.1", "description": "kill trees of processes", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "license": "MIT", "_npmUser": {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "tree-kill@0.0.1", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.12", "_nodeVersion": "v0.6.14", "_defaultsLoaded": true, "dist": {"shasum": "f485007aa3be7270df3106b040b95a264b773c21", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-0.0.1.tgz", "integrity": "sha512-c/bqwz9mFEsfEdgnbmflsZCHv+H5DbFaEVBGFEru2/FWB4T2rWDmobCLF+c60pqKwQf6oR7LXyFm8TqWeMMmqw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmZSVrl1Xo56PYcGQ1UVrmeIrYUiCXI5cr/ffW/FrfpAIhAKbThsf9tyRI6lsmdMlV2nTqmTv/MUOU9OOgdVT1eAqe"}]}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "tree-kill", "version": "0.0.2", "description": "kill trees of processes", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "license": "MIT", "_id": "tree-kill@0.0.2", "dist": {"shasum": "f5482376fc7239e86dc3790a195302bc0b9499bb", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-0.0.2.tgz", "integrity": "sha512-khEmrwhc6WviIpCDztNlu0G6cS3mPwIdo68kB32bqloFRK7BFs7Szd+i9vnfl76rL/AgqlYCOfkgO0gOmDiZyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBpgFlmj9omZNslGmJVvcl8yIrWmiOBUzZdHWtjAAcBGAiBx05SXUE1PuaCNcC+7onmAag9cUdFVKXtsnckxOeIX6g=="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"name": "tree-kill", "version": "0.0.3", "description": "kill trees of processes", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "dependencies": {"once": "1.1.1"}, "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "license": "MIT", "_id": "tree-kill@0.0.3", "dist": {"shasum": "ac2bc2ddb409b86c687498c0a49ccf431a802677", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-0.0.3.tgz", "integrity": "sha512-WFjIxNmIiTToaTYP+M2FEguRkF8BRCDaVdjG/BgPZ4a665xFDt+brTkUBNv7zwo8cUACdFdWQRZKyYeWgMyMQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSyKNkWamqVR3PmroRjGNUQ+VObEeoD/tQRIBKjbZ/HwIhALiiTnI0cuMdy13f/8Tk8jmortn3ybmI2niHjFDgX9HU"}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"name": "tree-kill", "version": "0.0.4", "description": "kill trees of processes", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "dependencies": {"once": "1.1.1"}, "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}], "license": "MIT", "_id": "tree-kill@0.0.4", "dist": {"shasum": "1945532fae3ebea78b1c83150952198ba2c66be5", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-0.0.4.tgz", "integrity": "sha512-+TLw98FON8SU4wzHNx+C8TZcYvmoerIJcqXqgPDKOftNPu7V4bZCWCRCkQFZFH06tiquyu+14VeefR6iBXnCVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1dFdE8dWl6hUkidWT7DY48dUAJLpPuHmHKubPRYciOgIgDpctiOP9StvaI9cl/VHi+k848DCY+7n3SDMCM1oucUE="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"name": "tree-kill", "version": "0.0.5", "description": "kill trees of processes", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "dependencies": {"once": "1.1.1"}, "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}], "license": "MIT", "_id": "tree-kill@0.0.5", "dist": {"shasum": "443a0f4a4a8d4b48e1a9aa097fba6d0dad50b3d0", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-0.0.5.tgz", "integrity": "sha512-pFQmJ/HR6Dvn46uul8dNXXn0bDiQYaxgoJBrKun+LhaJZAL4KnbznTWOeWHwDMTQp8jM4WoC6OMM8A7X7zGNAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDobVid3iVqBDKjyr590pQF1s1hUWpVbQixaCjdgdxr4QIhAOLUT4lnfISYQa2h263FBOJh9XvCQHwNNIyYKP5CT7lU"}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.6": {"name": "tree-kill", "version": "0.0.6", "description": "kill trees of processes", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "dependencies": {"once": "1.1.1"}, "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}], "license": "MIT", "_id": "tree-kill@0.0.6", "dist": {"shasum": "8f56c254fa50ce55750898ab3fb5a4d2ac01472f", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-0.0.6.tgz", "integrity": "sha512-zZN7Xu88PvtgKCZOPJDAbSWpnS8NDM9NRN8Uqu7YOD1MRe4wMKHhe7U7TLPisMm7ycad2dYb44zGk4D7/XFEEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwx1jHYg5azPaXNFA/zkSnsrZY02Z/sRkVDdJmSs7dOwIhAKM17XE/3hWAce38jtEHZXc7qE+yZ/Pd7FoskBkgkHu9"}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "tree-kill", "version": "0.1.0", "description": "kill trees of processes", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "dependencies": {"once": "1.1.1"}, "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://wmhilton.com/"}], "license": "MIT", "gitHead": "04062d27c5911c0f187c247917d8d5916ce55da2", "bugs": {"url": "https://github.com/pkrumins/node-tree-kill/issues"}, "_id": "tree-kill@0.1.0", "scripts": {}, "_shasum": "c5be4edcf626789975af27af44d53430beb834f1", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "0.12.2", "_npmUser": {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c5be4edcf626789975af27af44d53430beb834f1", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-0.1.0.tgz", "integrity": "sha512-bZRR10HN9EM3I6N4QR9ZTAXW8cuZztWEhY9U20F2cXePNT/dneHLbZYtKJ8zuJvBmfIzzrZjkVglONAiXPtVuQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBoKb2oThpvIdeEVATMQxK6UBM3aPhP/UKHke+zYzNwHAiEA0KLSyUA+dWhEQC9bZk4bqARNM9zVN7gO4CTIBmpwR2c="}]}, "directories": {}}, "0.1.1": {"name": "tree-kill", "version": "0.1.1", "description": "kill trees of processes", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "dependencies": {"once": "1.1.1"}, "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://wmhilton.com/"}], "license": "MIT", "gitHead": "eabd2d14ceffebc64e12768d2d667d5fcc713bd4", "bugs": {"url": "https://github.com/pkrumins/node-tree-kill/issues"}, "_id": "tree-kill@0.1.1", "scripts": {}, "_shasum": "53906cdefcefc69ebcb55d4007e0171e6dc3882a", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "0.12.2", "_npmUser": {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "53906cdefcefc69ebcb55d4007e0171e6dc3882a", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-0.1.1.tgz", "integrity": "sha512-RJi8ICVVY5Arlmd5+hA06LaL/95YDupmJBxpDjvSyj2CdgTvOYmcJDgJDu0CA30fcDaYrxbd8Axp6ZEH0u+Rlg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIElgSHXjt7ky+CCh9AC9k0+j22nChBh6fnv4StvSI5/zAiACtveKBQSJs1vhEOMPyK4JDzb5Wun4MEhi8OS5WWHZFA=="}]}, "directories": {}}, "1.0.0": {"name": "tree-kill", "version": "1.0.0", "description": "kill trees of processes", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://wmhilton.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://ultcombo.js.org/"}], "license": "MIT", "devDependencies": {"mocha": "^2.2.5"}, "gitHead": "96ed23e2dc95482c733dfbf2773b4bf253c9b567", "bugs": {"url": "https://github.com/pkrumins/node-tree-kill/issues"}, "_id": "tree-kill@1.0.0", "scripts": {}, "_shasum": "6ef2cff673c85cfc88cb2cd3bbb1f3cc82a963ab", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "0.12.2", "_npmUser": {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6ef2cff673c85cfc88cb2cd3bbb1f3cc82a963ab", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.0.0.tgz", "integrity": "sha512-MMhtLlhMSWzysG+C8CTBZyJJmzHcLDQeKi0qge17uGTAj/8vRMz7lhDVuGmHxMPBH24rnB+SYJ3cvJqpoLBUUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6k7Uxco2JbHwc7/3UsEY2ZSkT9TNVVec+mYtaN5vZfAIgVpC/85471bz04uyDMldCvLJZrmnRnF3ut8IpA5C9VC0="}]}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "tree-kill", "version": "1.1.0", "description": "kill trees of processes", "main": "index.js", "bin": {"tree-kill": "cli.js"}, "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://wmhilton.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://ultcombo.js.org/"}], "license": "MIT", "devDependencies": {"mocha": "^2.2.5"}, "gitHead": "d9c7ed9b82089751cefdfd9e68a32a79a520628e", "bugs": {"url": "https://github.com/pkrumins/node-tree-kill/issues"}, "_id": "tree-kill@1.1.0", "scripts": {}, "_shasum": "c963dcf03722892ec59cba569e940b71954d1729", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c963dcf03722892ec59cba569e940b71954d1729", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.1.0.tgz", "integrity": "sha512-GMrxLv+KCy2p8UOWVfMALOz0xbv0Ii8HUybWomnWZlpm4gHnOMmg0jrPgkZ0LA6NVzLlHaLAVlajQtK+mN2veA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFDfl4rFk2VbTpG7ErOj5Ai1TWyaa3ThHXP5CF0AMY5AIhAKU3usuqgArdZrgqMAtLdWW2W+WcGtKawOnvP9KA5KWF"}]}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tree-kill-1.1.0.tgz_1463156361304_0.06612400314770639"}, "directories": {}}, "1.2.0": {"name": "tree-kill", "version": "1.2.0", "description": "kill trees of processes", "main": "index.js", "types": "index.d.ts", "bin": {"tree-kill": "cli.js"}, "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://wmhilton.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://ultcombo.js.org/"}], "license": "MIT", "devDependencies": {"mocha": "^2.2.5"}, "gitHead": "92ead1111360eb54432e9f4f65f49bfb5ee6cca2", "bugs": {"url": "https://github.com/pkrumins/node-tree-kill/issues"}, "_id": "tree-kill@1.2.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DlX6dR0lOIRDFxI0mjL9IYg6OTncLm/Zt+JiBhE5OlFcAR8yc9S7FFXU9so0oda47frdM/JFsk7UjNt9vscKcg==", "shasum": "5846786237b4239014f05db156b643212d4c6f36", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrYkWpKo056AQzuSIc6c8bDEBC4yrbiPmkQiJbvHsiUwIgUYWK+wqLmEs2bUugLIptebW5zVvQlCXrwd3xBgOouCQ="}]}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tree-kill-1.2.0.tgz_1505860955114_0.22414045105688274"}, "directories": {}}, "1.2.1": {"name": "tree-kill", "version": "1.2.1", "description": "kill trees of processes", "main": "index.js", "types": "index.d.ts", "bin": {"tree-kill": "cli.js"}, "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://wmhilton.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://ultcombo.js.org/"}], "license": "MIT", "devDependencies": {"mocha": "^2.2.5"}, "gitHead": "d408f04316d8656cf520f35799518d5838d53955", "bugs": {"url": "https://github.com/pkrumins/node-tree-kill/issues"}, "_id": "tree-kill@1.2.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4hjqbObwlh2dLyW4tcz0Ymw0ggoaVDMveUB9w8kFSQScdRLo0gxO9J7WFcUBo+W3C1TLdFIEwNOWebgZZ0RH9Q==", "shasum": "5398f374e2f292b9dcc7b2e71e30a5c3bb6c743a", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.1.tgz", "fileCount": 6, "unpackedSize": 7363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3/yRCRA9TVsSAnZWagAAhI8P/33jrtBy8HKlLklDpsZD\np1Sj3jk/B9159h7GgG9trKVupt/xbwEbmLl1Nwnlxs1kV9xpIw7csfBpbPN+\nleYuWJ8qDCwYNA3KfOKQLiyPstTJDlN7iSufG7brBZJDqowWeDiPBHmFRVj8\nCjLkju6JcrvE2/lDFhlqLVxaC9GUpku+9Je7eiGkPC0CF+gnvgjJ6UVPtBsi\nscuFKJ77vXtcSgvEaVlUeqDvs+wzc2QMWruZ2yzdYXS5p5Db8Ggnugf9ha9D\nFDUAV68kd/KllKCwlizKAVutsQULoW+M1ZrqDf5hn9w6EIAWIr5rA3r+hsrB\nE003sWI5uhQKR0n/mco8NF69x/0SDoTqO9qZuOpH76NkCe6tRxNSJtrxA9f6\nZvV1Lb6vc72fA9dRvJTw4iwPzyxNf3XPXrlFIEiqrsPTKKqL4O4YY2I48dpC\nPYEdyhm9i9y47RPPJikXPaoteCyXhvPydFl5GxQZuD5neXDh/7P6DHHx4C9f\nQevpf1RRVbuilEYkr04JY13EnOE7kJx8+INi8C2lVx0ufvRykK9RIpQoE3sX\n1hu4mjdnvbg8xx51CkEPDL5VN7H03MtWHzpxF+/M+1Mc3rhyryvX0odqO/TH\nXK/igN00OLp1WFh9E55oW1PNEqjARz5gGLFRtlDBjBLeqJQp6rxQ0ESDK431\nDqyP\r\n=XdRT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwAGVL7ZkqN7UyZb8obnjJC/zgfDwQi3FVtqdBpjTLvAIhAN4ZnkxAkc3YqJ4Jl2DlGL4KKrjFlSHQfHeL0lWLWjYn"}]}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tree-kill_1.2.1_1541405840790_0.1693121613732902"}, "_hasShrinkwrap": false}, "1.2.2": {"name": "tree-kill", "version": "1.2.2", "description": "kill trees of processes", "main": "index.js", "types": "index.d.ts", "bin": {"tree-kill": "cli.js"}, "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://wmhilton.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://ultcombo.js.org/"}], "license": "MIT", "devDependencies": {"mocha": "^2.2.5"}, "gitHead": "cb478381547107f5c53362668533f634beff7e6e", "bugs": {"url": "https://github.com/pkrumins/node-tree-kill/issues"}, "_id": "tree-kill@1.2.2", "_nodeVersion": "10.15.3", "_npmVersion": "6.11.2", "dist": {"integrity": "sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==", "shasum": "4ca09a9092c88b73a7cdc5e8a01b507b0790a0cc", "tarball": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz", "fileCount": 6, "unpackedSize": 7816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8TauCRA9TVsSAnZWagAA/n4P/RjmpJW5C+IepzIu9Xcq\nhxIZCfV3r55NBDxFRJY0Ut+zw3oM57QbINDsXY52FunE6MQxevqQ/DA2lZ4i\nWnTNw+JCrjJBOzyb4CnFl+yxwxH+UqUVbNP+UnphKgohhprwMqxwP0oZhoca\nG+4FyeubP/Z/+J3aUiovYl6FcjF8+zxdOSVJrruhHCwdFh2e31gQAmZnBJ0o\n+A9VkWBe0Q4mYvFgzyey9pFxOZa4ycV3rJfxKrClMHSZ+SXDVpAHPUflJ1tH\nkzAQ5Ur6vKlhuA6t0vSZZNnWdQW6VZF9DambVIcZAenyT0xzd/vyJN55hnCX\n5VKr+iaI+iv/XW6FHDgrNvlkUB13Pqa7h9iN49Lxi+WojOFGaX48Iv4DofnM\n9CopDp1Ax2hY+cd/BjmI0gDdHz+Axe8dpewwIzox7dJF8fHuiQVC3vo1mNJj\nvOV0zbdNSeDySxS0c+TDH6aEv1jLcPgbhG5dBQGyCUWRSugH/RzMATxVuzml\ngNqDrJnF9jxU8pFT7M0aOKblHOn3mc0jQBDR4bRdLfJlYToI0dpv8aifYGII\nR5I51wMa+iB2ijkac4Srw+me0IWjz/+HM2kSBk/7QXPVVHPzaUdxlxy3sgJ3\n+/cqCRD0cz6Eh/Fz/sumWb2cgdeXeJYYMMYYaOC/3Vqsg7E+tAziSJ4zOOTs\nnGN4\r\n=T7m4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3HY7p9w7pQEgghXK6wF0f5owS/TQ7hz+ZpuKSaebBngIgBEE3MVCkMnJWFnYPL8tQo60woR4K1jHq9TLVtzB39zE="}]}, "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tree-kill_1.2.2_1576089261752_0.05821041701212937"}, "_hasShrinkwrap": false}}, "readme": "Tree Kill\n=========\n\nKill all processes in the process tree, including the root process.\n\nExamples\n=======\n\nKill all the descendent processes of the process with pid `1`, including the process with pid `1` itself:\n```js\nvar kill = require('tree-kill');\nkill(1);\n```\n\nSend a signal other than SIGTERM.:\n```js\nvar kill = require('tree-kill');\nkill(1, 'SIG<PERSON><PERSON><PERSON>');\n```\n\nRun a callback when done killing the processes. Passes an error argument if there was an error.\n```js\nvar kill = require('tree-kill');\nkill(1, 'SIGKILL', function(err) {\n    // Do things\n});\n```\n\nYou can also install tree-kill globally and use it as a command:\n```sh\ntree-kill 1          # sends SIGTERM to process 1 and its descendents\ntree-kill 1 SIGTERM  # same\ntree-kill 1 SIGKILL  # sends KIL<PERSON> instead of TERMINATE\n```\n\nMethods\n=======\n\n## require('tree-kill')(pid, [signal], [callback]);\n\nSends signal `signal` to all children processes of the process with pid `pid`, including `pid`. Signal defaults to `SIGTERM`.\n\nFor Linux, this uses `ps -o pid --no-headers --ppid PID` to find the parent pids of `PID`.\n\nFor Darwin/OSX, this uses `pgrep -P PID` to find the parent pids of `PID`.\n\nFor Windows, this uses `'taskkill /pid PID /T /F'` to kill the process tree. Note that on Windows, sending the different kinds of POSIX signals is not possible.\n\nInstall\n=======\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install tree-kill\n```\n\nLicense\n=======\n\nMIT\n\nChangelog\n=========\n\n\n## [1.2.2] - 2019-12-11\n### Changed\n- security fix: sanitize `pid` parameter to fix arbitrary code execution vulnerability\n\n## [1.2.1] - 2018-11-05\n### Changed\n- added missing LICENSE file\n- updated TypeScript definitions\n\n## [1.2.0] - 2017-09-19\n### Added\n- TypeScript definitions\n### Changed\n- `kill(pid, callback)` works. Before you had to use `kill(pid, signal, callback)`\n\n## [1.1.0] - 2016-05-13\n### Added\n- A `tree-kill` CLI\n\n## [1.0.0] - 2015-09-17\n### Added\n- optional callback\n- Darwin support\n", "maintainers": [{"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "wm<PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-27T20:50:10.934Z", "created": "2013-04-11T00:35:36.827Z", "0.0.1": "2013-04-11T00:35:39.464Z", "0.0.2": "2013-04-11T13:15:16.271Z", "0.0.3": "2013-04-11T20:53:27.415Z", "0.0.4": "2013-07-24T18:42:02.150Z", "0.0.5": "2013-07-24T18:49:45.715Z", "0.0.6": "2013-12-11T16:56:03.507Z", "0.1.0": "2015-05-09T18:10:13.001Z", "0.1.1": "2015-05-15T20:46:25.318Z", "1.0.0": "2015-09-30T13:21:12.921Z", "1.1.0": "2016-05-13T16:19:23.886Z", "1.2.0": "2017-09-19T22:42:36.007Z", "1.2.1": "2018-11-05T08:17:20.920Z", "1.2.2": "2019-12-11T18:34:21.876Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net"}, "repository": {"type": "git", "url": "git://github.com/pkrumins/node-tree-kill.git"}, "homepage": "https://github.com/pkrumins/node-tree-kill", "keywords": ["tree", "trees", "process", "processes", "kill", "signal"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twolfson.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://wmhilton.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://ultcombo.js.org/"}], "license": "MIT", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/pkrumins/node-tree-kill/issues"}, "users": {"uniquevn": true, "mapaiva": true, "moimikey": true, "geekwen": true, "visual.io": true, "davidrapin": true, "jens1101": true}}