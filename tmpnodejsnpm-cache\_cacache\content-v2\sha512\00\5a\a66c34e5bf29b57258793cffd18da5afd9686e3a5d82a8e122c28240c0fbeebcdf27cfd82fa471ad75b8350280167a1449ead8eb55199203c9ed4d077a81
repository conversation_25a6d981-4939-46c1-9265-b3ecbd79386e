{"_id": "@types/estree", "_rev": "813-452004a1b4010ecd5385cf8979e7eaa9", "name": "@types/estree", "dist-tags": {"ts2.0": "0.0.39", "ts2.7": "0.0.39", "ts2.4": "0.0.39", "ts2.1": "0.0.39", "ts2.2": "0.0.39", "ts2.6": "0.0.39", "ts2.3": "0.0.39", "ts2.5": "0.0.39", "ts2.9": "0.0.44", "ts2.8": "0.0.44", "ts3.1": "0.0.45", "ts3.0": "0.0.45", "ts3.2": "0.0.45", "ts3.3": "0.0.46", "ts3.4": "0.0.46", "ts3.5": "0.0.47", "ts3.6": "0.0.50", "ts3.7": "0.0.50", "ts3.9": "0.0.51", "ts3.8": "0.0.51", "ts4.2": "1.0.0", "ts4.0": "1.0.0", "ts4.1": "1.0.0", "ts4.3": "1.0.1", "ts4.4": "1.0.1", "ts4.7": "1.0.5", "ts4.6": "1.0.5", "ts4.5": "1.0.5", "ts4.9": "1.0.6", "ts4.8": "1.0.6", "ts5.0": "1.0.7", "ts5.3": "1.0.8", "latest": "1.0.8", "ts5.2": "1.0.8", "ts5.1": "1.0.8", "ts5.5": "1.0.8", "ts5.7": "1.0.8", "ts5.9": "1.0.8", "ts5.6": "1.0.8", "ts5.4": "1.0.8", "ts5.8": "1.0.8"}, "versions": {"0.0.15-alpha": {"name": "@types/estree", "version": "0.0.15-alpha", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.15-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "761dfc8c0890903b21d91b58161cded5e540679b", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.15-alpha.tgz", "integrity": "sha512-V624MhmYe2FummvnWeTAD5rA3J/DxdTxgKuToQz6fadiXGXrtTMAlA7B5Rq7qvrV2X/X/nqRtK2XxLvgZlR0FA==", "signatures": [{"sig": "MEYCIQDjecXs36mMbBfDpPdcmq2LKKKDwx0bOlAneygT0DnOhwIhAIq3K4EVQegKgX8dyMLXtTud88d1SHSVEXYFZS99vqY3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "761dfc8c0890903b21d91b58161cded5e540679b", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "_npmVersion": "3.8.2", "description": "Type definitions for ESTree AST specification from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.15-alpha.tgz_1463460720683_0.39466594462282956", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.16-alpha": {"name": "@types/estree", "version": "0.0.16-alpha", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.16-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "9ff8a4c956152f99edbb1f26bb7d3d4aa4067e0c", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.16-alpha.tgz", "integrity": "sha512-EqfeX1NVQGSuZnlv0NtfJZF3cXr2jnzYCLwzCQctcm75pqvfVv6ViJeXJMW+D/8WqB3CaMPyG23pkRIC0y4dXg==", "signatures": [{"sig": "MEUCIQDL+Wd9dgBUFJzBeLImhFIsr+1Z9PKY1Dqc4mE273MsvgIgZka6HSdmzhP9gPhuEGgkd98h76K1ppnRp5l63b7dT6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "9ff8a4c956152f99edbb1f26bb7d3d4aa4067e0c", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "_npmVersion": "3.8.2", "description": "Type definitions for ESTree AST specification from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.16-alpha.tgz_1463690847061_0.8162690196186304", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.21-alpha": {"name": "@types/estree", "version": "0.0.21-alpha", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.21-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "d29bc3d27a0ea11ff63b708369ab6daa3d632e97", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.21-alpha.tgz", "integrity": "sha512-qPw3fWdbKqslKUi+Hllhm/5ZCT+OIdUg7Pj6DiR0nHI3AB5Ec8HEACiniH1CO178rwc8sR8RIZL8VO5x225tMg==", "signatures": [{"sig": "MEYCIQCxY+08BCecvFkABcDhjbu0IeDbh01UwyoO1RrXZdePrgIhAJQYP5byD1JhkBZExhUMzl+rbAx+mTBKbZVw8pKC3hmz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "d29bc3d27a0ea11ff63b708369ab6daa3d632e97", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "_npmVersion": "3.8.2", "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.21-alpha.tgz_1463772732461_0.908426753943786", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.22-alpha": {"name": "@types/estree", "version": "0.0.22-alpha", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "f1c6b6c8ee3f0987cac4a868fa6780560f9cab13", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.22-alpha.tgz", "integrity": "sha512-R+uO6zmsihLpFGZ0Am1tK86JEe60vTRwumiQbug97fdBbtShANM9r45m86FBM+O1Jfl3jgEuAQJMjX/TXoQHuA==", "signatures": [{"sig": "MEUCIG2hDJv7fsNC+8FbXYlrJ0bpND5LWAUtdTRvtChIpJB+AiEAy18OcMnKt8G7xK9BN4vvjMr+dvcpeYr/oZSBrhR9a7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "f1c6b6c8ee3f0987cac4a868fa6780560f9cab13", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "_npmVersion": "3.8.2", "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.22-alpha.tgz_1464151830723_0.909670507768169", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.23-alpha": {"name": "@types/estree", "version": "0.0.23-alpha", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ea4e3d6126ccabb79c8761869f9719288256a5d5", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.23-alpha.tgz", "integrity": "sha512-i4McUtW4iWw7Ev8rmY4sNEfgM4NJfmQc1VbyRdyPh1f1NKU6Yq7BtqmmVJvYwEzQO5NOdhFeNln1suOarprWKg==", "signatures": [{"sig": "MEQCICkh05JKIMRu5YaHZWgbpj4Ew4SvXckpskX4ur+gzHQ1AiBZPIjMgp7bvV7QNu8Lb1Byi3vtbcxnFqEhfLGy/dCJqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "ea4e3d6126ccabb79c8761869f9719288256a5d5", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.23-alpha.tgz_1467400946034_0.5142802761401981", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.24-alpha": {"name": "@types/estree", "version": "0.0.24-alpha", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "2382179bf7ef5b9a8f8016cd3178885e2399cc61", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.24-alpha.tgz", "integrity": "sha512-vti14jDakf+nblO9mqqg7AS5Il5tGwllVV32IKc+0uF/CFt+FWQQviKA5XUbyIVYoluPtlET4caKypqbKpUUxQ==", "signatures": [{"sig": "MEUCIQDjveOKSTvgvIbG9ctmqqiok/DZ5kVV349eexBVzJVf4QIgFuXQpF4I9UnI7YimjmaOflbOVnRcBNbL+VsiOYl2pEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "2382179bf7ef5b9a8f8016cd3178885e2399cc61", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.24-alpha.tgz_1467412757429_0.07738191401585937", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.25-alpha": {"name": "@types/estree", "version": "0.0.25-alpha", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "e8f67b7b57cc8271247a6d20e57deb79927d3013", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.25-alpha.tgz", "integrity": "sha512-n/5vW4MJrveGdDs30wzoNBhS5yxCBkdipH5GVILyIstUZf8rmJ2jpwVGxuaBCr4htOQ9YCSKTb16YOC13EMLBQ==", "signatures": [{"sig": "MEUCIQDNoaFM1PZouFhKm38GwLdU4B61AKHFXl/KGopL3ipXRgIgOyu1FsE+BZX7Jki88Z+NMvpun1oCxfmxvsMDHFXA6ZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "e8f67b7b57cc8271247a6d20e57deb79927d3013", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.25-alpha.tgz_1467425977058_0.7091638254933059", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.26-alpha": {"name": "@types/estree", "version": "0.0.26-alpha", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "c8d3a6e02dc3ea9ddbf589aa6dfec0ff6c6a5f43", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.26-alpha.tgz", "integrity": "sha512-/cNM8P7s4DBKj1zFZAXYzPsEs4hx6igx1t1OFioK9yCnyp4ONdkO0f6wV1YD+Oo4RVHmemRA9emR0Q0NAo0dcw==", "signatures": [{"sig": "MEUCIQCWO6RzlGvosWUMLdqDJ7QWT7CSNuWHKRjm8gFqmPuOcAIgdQRorsMnzysrkl4ph1TWsXcrzyLu9+5V1D23oOPHO6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "c8d3a6e02dc3ea9ddbf589aa6dfec0ff6c6a5f43", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.26-alpha.tgz_1467591168263_0.5292988654691726", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.27-alpha": {"name": "@types/estree", "version": "0.0.27-alpha", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.27-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "678e762832d2958e2ce3323ce0a457ee72d1bf3b", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.27-alpha.tgz", "integrity": "sha512-uiJOCpG0ylIvYK6hFQb/ZxnQkoROgZZI6FIllnT1YsW2QyD6KAomFDynR3Nw6tVBl2qjZpGVxMPAPMhTWLgrxQ==", "signatures": [{"sig": "MEQCIF85FpNK45BakG6Voe1ETkCclasnV8bEo2/4F3UGt/BIAiAWUVBYRVZ0Bi7MDd58jwP3+24VaRFBCj5Nr5nXKrh3pQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "678e762832d2958e2ce3323ce0a457ee72d1bf3b", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.27-alpha.tgz_1468008355410_0.703524440061301", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.28": {"name": "@types/estree", "version": "0.0.28", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.28", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "6452954b458abf5441c45143032ec38f2517cef9", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.28.tgz", "integrity": "sha512-Up6m8YJUB6ItVpcpD+u1s11z/aF8DGKA4Pc59OVFd4m8J5TFkgpgdb9nuQYlHj+hB+4bZE50iW+hZWsOEQav3Q==", "signatures": [{"sig": "MEQCIBz6C0zHHJPdBrQZDLqroWf5Ctz1J7oFoa9we/UtbM6DAiBrlLX94+vBCSppBPEeKL88D0NQ4TmeeBq0OyDr6W618g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "6452954b458abf5441c45143032ec38f2517cef9", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.28.tgz_1468506699454_0.260924271075055", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.29": {"name": "@types/estree", "version": "0.0.29", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "https://github.com/RReverser"}, "license": "MIT", "_id": "@types/estree@0.0.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "34cbd292fa590b8bfb79f1a6e00684724957cb4b", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.29.tgz", "integrity": "sha512-MjU+uZBTM9Uzi2HLyucoyMSI9eFmhPcqzXZdlRwmkCZLkKs19GhsZTqli5u1hG7fMLOAo/PT2EBNsAXBYiuDZw==", "signatures": [{"sig": "MEQCIEM3sNyZYN81KCifg+UpeQW6lH/FcnWTCekh+UMaWOFDAiB1YuIUIYgjRZsBSX6LRAS36JejCjCw9Pm6EvaKVMgsnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\estree", "_shasum": "34cbd292fa590b8bfb79f1a6e00684724957cb4b", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\estree", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.29.tgz_1468933838838_0.5043976446613669", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.30": {"name": "@types/estree", "version": "0.0.30", "author": "RReverser <https://github.com/RReverser>", "license": "MIT", "_id": "@types/estree@0.0.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "f074723fddcafdce0a1ed187ac72c095df4d62ff", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.30.tgz", "integrity": "sha512-OJMRW2nEDz9NkzV6hcRbmlZn8Hi+CIlQSfC0Q0GG8DU/xMOmKS30ypkX06VVwPx+/oN1XsfQsXt3nUBiq+rblA==", "signatures": [{"sig": "MEUCIQCRWNShvzi53I+rB7C5d5r5llHEpF/cEuko/G6013bDnwIgEL/wo9QPf5lysQl3dmYUQ3RoOUDU6ekX9EoWyhNEMkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.30.tgz_1470153121497_0.024318387964740396", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.31": {"name": "@types/estree", "version": "0.0.31", "author": "RReverser <https://github.com/RReverser>", "license": "MIT", "_id": "@types/estree@0.0.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "f3a812fa5eb8c2a643034d701dceaf21e73cffdb", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.31.tgz", "integrity": "sha512-vV7gDxZ/ondG4fHHHXUWqXdoRg37hmcGwyWKocwsov/Ew79mD3fP2RrVqHpVLuHuliEzg3W89bf27wc7GI/SIw==", "signatures": [{"sig": "MEUCIQDFV6bRjlMc0MgeoG3PLBGwCccpaDE7YlNAjKuwht2MOAIgHS8yUdbwiD4DJ7fNURA/rL8Upnecuo02HC4VpImVr6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.31.tgz_1471620283165_0.6256591433193535", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.32": {"name": "@types/estree", "version": "0.0.32", "author": "RReverser <https://github.com/RReverser>", "license": "MIT", "_id": "@types/estree@0.0.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "7aa486bd26ee336b5255ffc93fdf054e06f5b11b", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.32.tgz", "integrity": "sha512-gjP25umUAc8u1jRzUQuRKMTpmomOHCFDsQfPlXyApO7JQILiZMVufsfX0aczOjCeWO63Yo1U+y6Cbvvz7Bv8Ng==", "signatures": [{"sig": "MEYCIQCd90DFPNn7xL1aBwGxvh4+pUpC3COG5NkWXqielUvm1QIhAK2uSIlQkmTnHMCpgx+8HEdRq1Xx15D05/pQygbBS+SI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.32.tgz_1472150481760_0.8328709425404668", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.33": {"name": "@types/estree", "version": "0.0.33", "author": "RReverser <https://github.com/RReverser>", "license": "MIT", "_id": "@types/estree@0.0.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "49e39ae088bf70b10f23351387f38be00fd27063", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.33.tgz", "integrity": "sha512-0rYbqswh5qIOCSlS5EtaFNF0AhJFpRswKdHnVZfqWqW40EZQO/oNQn+NCfufHt6KkF8yeBcqVtfkIxQqUjVBxQ==", "signatures": [{"sig": "MEQCIAKDdeLIthjVb7F+3LEKh7VUMHMTJeYEPIIwwL93Gx3MAiBtY03VJl/tUwt/BZDkOw5+QvidWhpzazS7eGbLMWIQqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.33.tgz_1474303578697_0.19232818158343434", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "f29d17a6b651c3791ec7343490560cd6b45c68d9d5fecd2cc0061b4dc41fa847"}, "0.0.34": {"name": "@types/estree", "version": "0.0.34", "author": "RReverser <https://github.com/RReverser>", "license": "MIT", "_id": "@types/estree@0.0.34", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "aa7af69a3a91922171ee411b3c9d8f6beb4af321", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.34.tgz", "integrity": "sha512-QvmYt9JM6RFZdoKj/k9kJnptA2JQDjDcZifnxlvSRAqE72yXXxttcG2yQHs+8btp7TCI1k09K2GDKg5T71Abag==", "signatures": [{"sig": "MEUCIATQruDTsKSNqNwPCelTz3/2IpNUdlM1vGW0SmgnFhfqAiEA0DTT5m8gIaxWT4cExrLRrM9d3rt3lq1k3YLMlwN69g8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.34.tgz_1474406139519_0.9271249345038086", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "e13a11e00708c3be1468097059019c50e9ddf8312391e8b6e1f837d895ee6801"}, "0.0.35": {"name": "@types/estree", "version": "0.0.35", "license": "MIT", "_id": "@types/estree@0.0.35", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "8999974b34028686a8d61a719e61c138d3755107", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.35.tgz", "integrity": "sha512-YHw6WdL0pkFzHpQE2kQumwfQbRq05S6frrtL/T84uinoH1XfgwgyooCyFVzkPpCbDds13zXHfYmrqnhQoPkJVA==", "signatures": [{"sig": "MEQCICLcQ3R66ULaFij0jge3Syxd5VWPENJ2TH2nZ+S+K49BAiBH+3oOzgEPPZsNp/OcC9mQirxmxDpIXoRcwz3dSu59lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.35.tgz_1491939372137_0.49075658340007067", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "6c1401b5240814ed80f3c08a08f631fb37d320a4f62e93f0096798ba42cb2693"}, "0.0.36": {"name": "@types/estree", "version": "0.0.36", "license": "MIT", "_id": "@types/estree@0.0.36", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "dc601017bb2e1ac3ad8c438757b542aff515f548", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.36.tgz", "integrity": "sha512-l1Kpg9ZLSPrSFL66nKbRfxeKM8FTVCmDYeDor3kwpUi7485lnyR+BkaR+o3ptK8acd7XpGDuQ46RwSVD7kyJ+w==", "signatures": [{"sig": "MEYCIQCVYMHWc2/PNt1KMlzRDSC/m63GHBZdEv17ZBIcaNh44AIhAI2me4PDeShggPFyOn2knCOZMG7H9ZDPL/sXPbwQ1xQu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.36.tgz_1500750270693_0.9286409511696547", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5c7ac9ae7b67bcee409b5e8ca572bd64b1336caf472e50190327daf5f024b091"}, "0.0.37": {"name": "@types/estree", "version": "0.0.37", "license": "MIT", "_id": "@types/estree@0.0.37", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "48949c1516d46139c1e521195f9e12993b69d751", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.37.tgz", "integrity": "sha512-1IT6vNpmU9w18P3v6mN9idv18z5KPVTi4t7+rU9VLnkxo0LCam8IXy/eSVzOaQ1Wpabra2cN3A8K/SliPK/Suw==", "signatures": [{"sig": "MEUCIF+6zLyGKHrA7biITUpgGv5dlqt66Z2ri4LqKRVp2KQ3AiEAx2LuAZFecl7vJhLk+2VsR4+x15YU8SvDgaoWsVlRRQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.37.tgz_1500919312746_0.4375996091403067", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "27a92f458ca416ba37deb65c8d26dc1bc5a2c2f36e269efa1ce3c54dffc7786e"}, "0.0.38": {"name": "@types/estree", "version": "0.0.38", "license": "MIT", "_id": "@types/estree@0.0.38", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "c1be40aa933723c608820a99a373a16d215a1ca2", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.38.tgz", "integrity": "sha512-F/v7t1LwS4vnXuPooJQGBRKRGIoxWUTmA4VHfqjOccFsNDThD5bfUNpITive6s352O7o384wcpEaDV8rHCehDA==", "signatures": [{"sig": "MEQCIAdRWEAjScQKx3Y3U6oxm1JJ5tLt1zUaNvqKPCaHmF0RAiAj6eCBrGUANPuiT/MyfChRQEfgZUXbOE0xX0/wg4LCnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/estree-0.0.38.tgz_1507890758429_0.2064026182051748", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "19879f4eaa24d649d1de20d95bf747d91637d037470d47442d96dad57ec9a424"}, "0.0.39": {"name": "@types/estree", "version": "0.0.39", "license": "MIT", "_id": "@types/estree@0.0.39", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "e177e699ee1b8c22d23174caaa7422644389509f", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.39.tgz", "fileCount": 4, "integrity": "sha512-EYNwp3bU+98cpU4lAWYYL7Zz+2gryWH1qbdDTidVd6hkiR6weksdbMadyXKXNPEkQFhXM+hVO9ZygomHXp+AIw==", "signatures": [{"sig": "MEUCIH+9wYHbkV0xL6CyRL91AmZT2huKwMPaHPMZuzFVyY+cAiEAkjjnKNrsOUgDPx5YTnmOSJP9ONT47D1X8rsGwCRDMgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1lelCRA9TVsSAnZWagAAROAP/ik+oem56TqD4mXTPevS\nZy8SasiUvaMJci0GIt+HYBbd927gC3oQK1fz/hRiXiHrcT5csRPndu0N27xR\nybDBKpsMoggNXjx3toIgMaspvLCPZ6f2eOiVpqmerhCH8OKnOkuN9TWQiqA/\n6M0snfJMT70Wg/oJqM79oakX9vb0CTMIhxfqe5Vu5hNpPjbmw6cD3EoSFUSV\nmgc6tc2mq4cfZRSVUsUM2yzornuD3ziSkVb8cDQXDTAcPFGqWmnAr2UaltYl\nC0p/yszaedmxuq4pCmGKqNHNBrexT8w3MJLtZGEgDySf43MsyCcAPx5u6nXl\nF4XaBAZvqxKcp0a07Ok+NMvKqOgwqdER08LpcWTsqqPmtxGHPTjMa4kCGSE1\n/zJ5s8Or6BLNnnB2ow2urS/yOxl+KRt3MLXSwtRy7ztIKiVKUiYn1iZu5m2c\nPX3hKSfrKn8xbJrnUb3SJrp36J0tFTN4eTSVVkN0OR0qdDdBsaLMU22dQqup\nnwAN6B4v515PsARIWFJe7hIt8pspBjlSa6TPj63jLv/n9hlaREb/ydYeIcPU\nONf2xOLA6wx21+MbjK2itkwJ5ghFlPlT5WfbPyqt6Q7dbI7uil2Kr4kwfUvB\n2eTS6ZYPila233OCG2ZiAWOCc0IVPodnPc6Qi5d+tvcu+sfa/NNspgnRy62D\nPED0\r\n=KEdC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.39_1523996579757_0.06106125561434195", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "427ba878ebb5570e15aab870f708720d146a1c4b272e4a9d9990db4d1d033170"}, "0.0.40": {"name": "@types/estree", "version": "0.0.40", "license": "MIT", "_id": "@types/estree@0.0.40", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "0e6cb9b9bbd098031fa19e4b4e8131bc70e5de13", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.40.tgz", "fileCount": 5, "integrity": "sha512-p3KZgMto/JyxosKGmnLDJ/dG5wf+qTRMUjHJcspC2oQKa4jP7mz+tv0ND56lLBu3ojHlhzY33Ol+khLyNmilkA==", "signatures": [{"sig": "MEUCIQCw1B9bbMGFO1xL1djW+KneJ58OsXRYBygf9yh2aWAdCAIgO5FAaC/rz+B77AkShlShCCr6mFu08AP312Zj5Ew2BnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22056, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3bQ1CRA9TVsSAnZWagAAWOcQAKD7eAIxjt/xjGyjA3pX\nZ68zQwf0+4j9dvFAhg0fFMIWMfst/eFJ5DRSzOZFbtx6Uw86r5byAyLLGCQT\nII6HVNKce0lvZjLZQak0uoKd3jEUhbOYcZqA+zUlEPTTqG56Lac0WywXkAIP\nT0gzsly51SO3P2gWd+D85g4G6QEajPm8ZS9L9vQ+qeOu0TANQNSo5bn0sdDX\nL/e3ALs2Za50wlcT7gSxP+MXcjrMHsNRpMdaCooHbY1xPNbGnEHCaHBIDN5U\ngnX+JHdcUz+/W2Xg6LaiKXEjgOXLIuQHwUq0TNAyfw8EBUXTAzHn5tk4D2np\nt7XsCOeNdr06QeUW/imk1ytGoc/hTw+v8L3CjFjjDDN3SU30BGgBCNdWUdRM\nEL50PYeOhKkKmyJwUrLel63uKNh4bwdBMdpcmbsVggVCFcLZeobMfmA9s/x5\nce3se3iKTRVObBDTGUzfm5SRi/HnWFSoAU/n0rEcCWV35vwdq4caVSsPmBbf\nliXnCBuU6oyHDwAvH/mSPGy0ZR29D2c5nTlctpnrEAYmqPgvz/U4Np3/PNGp\nn9ZYUdgb9cT0/9S5beIprUajXmf7koTbG41ubIMw+OLUR2CsD3BAu7294Hqo\n4WcgsGaAOitz4PZNdUAi67fwCV7pZB+0mtwxjxolbc2uIuFPyb93V8J10jFy\nUPxz\r\n=7JVM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.40_1574810677614_0.22768039272209029", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d2f945f0562d9138ca0101749678cb992bd5b9fc0f1476d4cdfa661bcf0c2576"}, "0.0.41": {"name": "@types/estree", "version": "0.0.41", "license": "MIT", "_id": "@types/estree@0.0.41", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "fd90754150b57432b72bf560530500597ff04421", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.41.tgz", "fileCount": 5, "integrity": "sha512-rIAmXyJlqw4KEBO7+u9gxZZSQHaCNnIzYrnNmYVpgfJhxTqO0brCX0SYpqUTkVI5mwwUwzmtspLBGBKroMeynA==", "signatures": [{"sig": "MEQCIBJsdozGA5g1wYaO6QoN4fFx69uZUSoVvw0Z6fKB2GfIAiAtwk/SjwqHLrVPM3uOb+uZpL3yTSQy9CwyHvfCMnuErg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeASBDCRA9TVsSAnZWagAA4WcP/0qCZZtyLZ0sSBQd26uc\n4gdHyyROwExCPqwpqv9ffachBkmnvyck5kJJq1rkAoI8DW2Ddpdj5j9VgtlZ\nCBdf7Jf2J6mmS/64bxJDsyjt7yVINC9BUYm42bI/3zmq/DRZlsvsGGBFV+mb\nyVrjKp/uEyIgN/EgaSRKS+5HWh6t7z3VZ7i75IMAdPsA0/Nei5gUaZ00zk7F\nyjSsHoXrmU+detpfrngFGy6LQlB/V5fA6rQUvim/SF3CixS2RQ1UGrGmdOfo\nE655RMzTQvpe04/uaiCyyN3JoG/P9HtDA/eLN8/p71EqnoL3x59XlS17Q7+i\n3CqQa+S611AQu7PkjIPgOwQU0OIwCmJjI5vImBWwQ3TsaSLIOEsozfhS5uN8\nNXGzrLv/QhYfCzaBJkDe4DFMAwm/qFZA2QHQ9rZFB6T8vMrTRo+ZQL87zALa\n4Iek8cIeAEoM/hvMzx7VV6wPXqlRidi6muLiwDh+yvk4cOZAYX+GnE6EMRY3\nmys4gm7mgpiJFLnN0dwnnYedB3X9Vf+Tbejz0Vr8OsSIA37oX5agX/YJyYg4\nHaa6TrFhkIfZ2P17NZuan5ZuAfEjIjXhgTQEDuruRZWuYU6KnRw11TjMx2be\n9EYpiCDcnelNIItm9UvTOflujeDsXhHNtHxgwc8YcLCP5xqWjOV3fjaaLbS+\nuykC\r\n=UmES\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.41_1577132098643_0.08096609314502223", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f63e47a2772a75a2ef1db4244cc9d4cc95c2ac7fa3a610968c14434a32070244"}, "0.0.42": {"name": "@types/estree", "version": "0.0.42", "license": "MIT", "_id": "@types/estree@0.0.42", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "8d0c1f480339efedb3e46070e22dd63e0430dd11", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.42.tgz", "fileCount": 5, "integrity": "sha512-K1DPVvnBCPxzD+G51/cxVIoc2X8uUVl1zpJeE6iKcgHMj4+tbat5Xu4TjV7v2QSDbIeAfLi2hIk+u2+s0MlpUQ==", "signatures": [{"sig": "MEQCIAuE5jYa0nkSOuUs6T134DjKR2oyO/1zsaM5O6ketkONAiAX3RmpsdyOJPPDJL0fRVb3yh3Dyh7tKnQY+mSR1M8ZsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFl0XCRA9TVsSAnZWagAABmMP/ArIpjD+4LIPXO3aKCyo\nZBiVVdpoIF+WojN3aBwGdGx+/tZFwU/LHqAOvAv50X/81ef5/JruEmzfM2Or\nboENK+VixfF+sTRMcza6CxYOA9U3i7zcZymvyM3vog+JOgkHY993PrDinKeh\n4BlfTJr9eRZWPFir4giEb1BFWlkFaZncX2QlJ572yHv2iknddmud/jHT/5RO\n/3HMlVPASMuZQnw3tghz8aenC5dSIstdqDncq6B3xzNXtdJiS4nuQQkYOG2j\niG+hwuKinCssuFD995TRRgE5X8PTN+w629xluuHXn2Wp/xFErY0aQdf69jP6\n5etFFfBMIdxtxiz0HK6K/bVyXrOGNgZowgvUuyLQUkVEv9HmitRTLVSdeFna\nA2/zHDKP6wfNeNL44CtHDjvq3ZvjUWLECXte7QFDkDLmv+1VmfSjLXsy/1LN\n4Fcquwsqp+Ob0moe6cTumOptJdizj0hD5LDvD8S2KPH89+wItOrjYbpOUCSN\nSMOfOUucNVGrSWezWRon/sKX/YAiekR2eG8/uPWglz4NQD1nSYhFmjqOIMUg\nBnqZ5AQZx9ALINJbpcKB+v0HOZ6jO/E0SSmvQDAl2M21K4HTQkGlFOrbAC7j\n9b2ak0ceqJYvAZ+2sVbSVdCStaelfMucqZMhEB/9kiGcJx5xtjoUuVsxW3rK\n/Xc4\r\n=P7wC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.42_1578523926780_0.33996786440952276", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7a9c3307dac590dae10b639e005b86dcdbab8d118bf1ce74022b1c4f1a0d9913"}, "0.0.43": {"name": "@types/estree", "version": "0.0.43", "license": "MIT", "_id": "@types/estree@0.0.43", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "b73abf9f5db97540cd69815fd65c5b1198526c4e", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.43.tgz", "fileCount": 5, "integrity": "sha512-WfOySUnBpyKXbkC9QuZguwOGhGnugDXT2f2P6X8EIis7qlnd5NI1Nr4kRi357NtguxezyizIcaFlQe0wx23XnA==", "signatures": [{"sig": "MEQCIBWv/F1QgLnr0UKowmJjgqBI78qhCB8rR0akhMp9ITRDAiBQtHeLPOzUWV4AzBhTaLklVg2qKVM93Xa61+QPlfOZ5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecoeeCRA9TVsSAnZWagAA0nUP/j4DT9omNOmnGMjm0w9/\nACBddERn0c0K24cMyga6N18TNUce2hZLFdM07EFaiKq8/8EKPYv+VKX/U8UD\nmYFjnayf3I0Rh9bzuofJV7ZmfSENWKAw+Pup2Eq0AUVsBPbkxnPAbKCB9lQ5\nOhMZs4N9U8aLK0Y1iyqnpDpwnAz+6Fbq0LVqE9VAgCIgW6uxSDK/YhHH/oxn\nB4EVixI3Ni/BnwH9AMIhMGh5m8EWo19pvrEShgEGTYwN2FqtYgFGuSdyJo9D\nJ9HXNK7IGERZN46yLBHn6ZsZ5dEtg40mJ6lCH4isDSOeg9jzmEcYMxdyikXA\nZgzYfENCQ//lniKgR22c+xEsC3yBw/xmfT2wtZZOOnDUkI6sJV4T3k6+Aa1e\nX7GtH6pXX+kHsZamUZTdpHIj1r1V2B0E5Fx0tT5MnRtTRpTHR06dZuEfVDuK\n1EymJPAmtS9WtDusWYOZQXI81/odLdLcbXY8Z/BKWj5iLfpLBy+W//BwFbEZ\nW+dv8aK2VBf0XppcO0m5Z0z5Ay5ItdwyUYOkg3Cyugx5/23xgslSo/R7gQsZ\nzb05q9WmOkGEEVhXvbtC7IUDSMU6pm9/M7S+BCz3vDojm6hZmnCPy7BGfTyB\nF3jqeirKJHVGLwGqzIcWAwqJP4cP5SEyFJ1CxopLAFjCP5P/MJK+V/MzaJMQ\nvgPj\r\n=73qQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.43_1584564126149_0.05648611810767501", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5bc1cecb66e4af2dcd64bbda30525b86ef58f24b8a9c780fd4c47baaea3a665c"}, "0.0.44": {"name": "@types/estree", "version": "0.0.44", "license": "MIT", "_id": "@types/estree@0.0.44", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "980cc5a29a3ef3bea6ff1f7d021047d7ea575e21", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.44.tgz", "fileCount": 5, "integrity": "sha512-iaIVzr+w2ZJ5HkidlZ3EJM8VTZb2MJLCjw3V+505yVts0gRC4UMvjw0d1HPtGqI/HQC/KdsYtayfzl+AXY2R8g==", "signatures": [{"sig": "MEYCIQC3eY2HEWPNoglJwYB9S/nW8Yq3fXOH8uaJsSg1r5EjaQIhAPPRcax283dB4DHP+at0EDNwh5Ix3RI1jjVJUw9FVKR/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedQYzCRA9TVsSAnZWagAA1ksP/0bXwCkqYItgrN0all5p\n9fBGAhLikTuovW75hukp+wXpVW3FbBPSKdUa+tlq3gPIEDKoml8NQDghjpqb\nK4ZwQ3sSb++MPiPwDYaQxPqH6xj9A6ArkmHuIp+QkhreQQsm0o+j3bbosV5m\nXQiy+SpAEvpYEJZH6JMVGw+fU0erE3BfafXtUq2DaUjztRmzU2AMtwVSBheb\n4YiGI3cAXEV/Ghp0nTFT3UW7WzgrYcGaKsQZzMQatAIiK2KpZrNRALtxDOVw\nWwSRDmsXTJHZy6jcuTUL1B8fo/mHo/ymFZifoTnYoLr0bV+SAkCgkZHUZ0fV\n0jCqPSdKdQpfqWvdoKtY1msoJNLZZI/Za8XLzJ7IFhqArPBVbEYpMxxGHXBG\n5NyUBNoAWIdBaiuDacLHvEXxYWb79sDKgi63Ej0zpBdvZJYlqIk/5pFE2VLn\nvL9nDToEF21GJTFCE2DBFYfh8rPxu/ERmKtTQ4uZJjmn7aJqIAdydC8ptE4G\nbuBoSRbErHQ/IrPr424IVHgS3/emYSmG3VO4GkJCtHyb7me0QbtSmf+zsH9o\n85qE6ioGOb45JSFT9JFw3bXD0XCntEVFOgWOFJKn9ceutUhePg6aizZb+roO\nGEr2dN93jY6u6f+00Dn/tbi0bQhCtBVanmijDi8JKgookiohCYT2AHiq1wZH\nDp+V\r\n=8N6j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.44_1584727603339_0.41396815893376426", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8a49a4a0054b726ee6edd5edb54211937dde427a52a5cec19a9fc512871a7b3b"}, "0.0.45": {"name": "@types/estree", "version": "0.0.45", "license": "MIT", "_id": "@types/estree@0.0.45", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "e9387572998e5ecdac221950dab3e8c3b16af884", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.45.tgz", "fileCount": 5, "integrity": "sha512-jnqIUKDUqJbDIUxm0Uj7bnlMnRm1T/eZ9N+AVMqhPgzrba2GhGG5o/jCTwmdPK709nEZsGoMzXEDUjcXHa3W0g==", "signatures": [{"sig": "MEQCIC2PSOuN/5zERDGZCO1UAArbfsyduGG/woqlIVniaH3jAiAA6GSyGmpVwtN7U46oBgpN1Gz2q82R2fc52umJ4YUaiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22582, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8lrkCRA9TVsSAnZWagAA9n0P+gL9Gf0X2e/fq/RDZkFH\nFIZArjkavQo8aNCOwxV27e3x7XP0iWa9xekBSWNs/DKorV1YQrwpzmqWiM6a\nilZ8qGiYXse88J3XhJdPBo81QXv2KNj7fhk2Dg2jN+JmE470RKYLk89S8V9n\nxgGnUpQpG2EeR8KuNVpyq4drtNrCZC4W+jUvdNgfIPCJxIs69cdQbFj9Yt9e\nufO0tl3/MqnX/UE5FSD5rgm/sDPWxUaNEmK8VS2ccaGtG2z3Xkp2QygNP0QI\nT3zHERJ/uemiLA1INqIZDXi04zco6LZE4RsKs15f31BuD8gdtRHBGmCkbMPc\naP1u0RSXJK0LD1QRpeFERcQTVLlzdsZct0Uzwjp6F6jCZWoLeanXN+vvwM4Y\nLpTzW5i4x6XJ2HadZQSaDHnyQF+quUE8d4/MSZuTAgWKzDInzPVwRd4mAh23\n7bZxE45SZpkMjCoUQr9D+CCnSx1OZp3yVprkYeov1m8RNzG2q4u5+1oNs6Ii\n6MhOcQlxYJoEP3HfNemI6o9/UID6fcocxFGEnH9IiyLSDLO9DqrwJTU7qfvW\nD6vG/Qptigiv6acDLGxJQfqgdhPIYbUz8dKQjy8mHqN9VZmhC68OLPvblJJL\nOhu94b7JaHB2+N64OBEgffVQZ0+EaOQeFnBK09MkykK7hMQGxeADIiR/FVA3\nvtSk\r\n=YV7r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.45_1592941283590_0.46785700203512803", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b35f62c8f3816627a64bca3fa39197accb260b4275d5e4cffba0b6d5af44fbed"}, "0.0.46": {"name": "@types/estree", "version": "0.0.46", "license": "MIT", "_id": "@types/estree@0.0.46", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "0fb6bfbbeabd7a30880504993369c4bf1deab1fe", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.46.tgz", "fileCount": 5, "integrity": "sha512-laIjwTQaD+5DukBZaygQ79K1Z0jb1bPEMRrkXSLjtCcZm+abyp5YbrqpSLzD42FwWW6gK/aS4NYpJ804nG2brg==", "signatures": [{"sig": "MEYCIQDRMmWTetAkgr3Dt6ycnllPhCVgksfu7s6NdwPWqdxQ5wIhAOOcW91lCm39G2bVu/3TIEkklIxQaTAhaY57gmr4oYD4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/M3nCRA9TVsSAnZWagAAQTIQAIWkhO0HWDQnovd+TMCc\nJzyYKDKEQKKx9y2glS0enmO48E7dzTEuDSyseGG0vcfYqa7O+J56X//7X5Jh\nB+OyQQJwNb2rUvueiL9145ineBh0VmxgYfW+urFiOJwE2lKRsn755v89VpWG\n56FFfJt/jR2KpVfvm3o21r2CcxCpr4+0xTKhweVRZ4ewE2MAw0u6VxO8tbW7\niFMmx650ti0zgqkrJ1VjUkDgJE0HEPsDoy6n5+9M6E7TbnI4qGnjH6kc/k06\nLGf65PszA2BHSdymnfZMpqiPCoLlrlK/nGJB53Q+WziCzSNKcQA6etOiFBcq\nt9Y04XpwOJ5fOeHigrgP778qg6Nxta8dEO4CEXF/NdD9P59DxDYL0yu5xmHA\n3FaDNNLyatwXfAQzjAz30qaNwCJvDViVwG4qix5ElwNR2Y3DjL0rtMzO7vPG\ny3V97gVeO2cKWxLJI7xb7R6QIttmO+BbkYp3Pa1onRO7c7u6xeyTMSzvRCD5\nw44dmBfcE3RT8fH4tycm4LQTdzTmKXnp47OVzrljA63VTREHwHhlA/DP/2un\nbxGtU+1HvbHY6xkrE1chHDKYS10jrD+5yEUb256EinbrD2LGIDIkXHveTIf1\n7JYRIVeh2g5zRd5xgDYQMg+tXE+sG2QpiTMDoyqVvyQjy/2JgyYcwj+e+M6g\nF1vb\r\n=9+dG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.46_1610403303390_0.380563509148347", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "591b2ee0824345153647f52ddf178b1b5890729b3e806ab046df52628c383720"}, "0.0.47": {"name": "@types/estree", "version": "0.0.47", "license": "MIT", "_id": "@types/estree@0.0.47", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "d7a51db20f0650efec24cd04994f523d93172ed4", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.47.tgz", "fileCount": 5, "integrity": "sha512-c5ciR06jK8u9BstrmJyO97m+klJrrhCf9u3rLu3DEAJBirxRqSCvDQoYKmxuYwQI5SZChAWu+tq9oVlGRuzPAg==", "signatures": [{"sig": "MEYCIQD5js20t2dQwEABNcqvkJ9tRrbe0DnjwoHqRGTrOsFA7QIhAO9M7yuV4qxUpGg0xDlUziqN3HCHSutq9rnU8o6W8nAF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXDxQCRA9TVsSAnZWagAA+SsQAKECdGp+v4ivupUPYg5H\n106TPSR9B4iAflk/h9cRuJKVKIA+UWxNnHFC+ABSBz3ejfQr4svPC09YC48r\nbCdgzCE3wV1oSVfYVztXF9+7EVgZerHoqdYKfCHvFERUEQrSUPgepD+ygRFf\nO2KWCG6fw0lmgFbb1BDYvYEQsqcqxhDxDAMx0HEnYXo7IlKlwoyc2n0EmZqL\nawNgXN0BzSEcwAmdfiHbgDoagT/1tYtimtWHIHLwkjk1pkYHiqN6R0qcPUTD\nwzcWDSWwAkKyZJ1hjkQ+UjpZnTGfFdT9yry7+MPw8GzUBzOn8TCPBz2rt0GZ\ntSzKJPh4WiSG75V/qTvxDs1WvBZSgbDhG8c/lYhpvF6GS/KMswqhfcWZYWuN\n+jzeHSFEP+Do9TCLwEhuN+Fl9bf/84Lixz+l5TWBZWUlWo4x9qlnrJ22bev0\n7fuqJKQ5cVcoq1DUxtZsLQkiZm5cIJ4eXnOzPIoIKyOrLbWvegHtEVLu/EWb\ndckR3eBILq9oHKR8tfzaIwIQ0VXc3jZP62nfbVZMSVLnJNEbjR3lKr0k8Why\nPONX9DpUesGD6oZ0RjLLryDXgVhscwP7Iok9hLav4xhyt/ewAwNJMI18Kthr\nT/h8UqhQ5GbgKV0iNOgcGb3SOsozpqw5fTdPsBzaEe8tDlzV5s7vetumdJmN\nYSlG\r\n=zujQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.47_1616657487765_0.5669362080423166", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "172e2abc1fcf2432ca068a590e27f625f36dceab49893851d3bc7269fa02966b"}, "0.0.48": {"name": "@types/estree", "version": "0.0.48", "license": "MIT", "_id": "@types/estree@0.0.48", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "18dc8091b285df90db2f25aa7d906cfc394b7f74", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.48.tgz", "fileCount": 5, "integrity": "sha512-LfZwXoGUDo0C3me81HXgkBg5CTQYb6xzEl+fNmbO4JdRiSKQ8A0GD1OBBvKAIsbCUgoyAty7m99GqqMQe784ew==", "signatures": [{"sig": "MEQCIE7FW08yIFO98o7S8+iicZ1S52rvyG4xq2N7p9uMoZifAiAdfJ4g9a3vC+f1mWkt5gjc89TMoOSj3CT5kaga/60vVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtxfQCRA9TVsSAnZWagAAjmoQAKGSl+8bL1RfdkzwSP1+\nLwiOimdm8eDNGd6+2nyyNsyu/VJ8Dnfb2MRw4O/GxCDBG8rJEXQyPuvR/ofP\nkSuVM8UP4xQZOjsxkdLSg01yMb13Veyb04x6CkWmH9NcPYmIVpr5aJCQd2mO\nQArRFjuHaHP0NYCenoiGczx1Grcqd2UQBLWXemuGmqXfLIPJ4ouM2iI9PbCr\nzFVJG9BnbrzOq3bWRmO0BmUu19/j2IGp+I1yHng0Re2eOv0LUF9YvuGlGiIw\nixabEeZBJxWoHUG+84u+qPE2PSU5wsR1ZYP74RWfu2p3oOjVUwunIfE4oSRk\nfHfclCrSk1VaKJs0BolGRVLlcMSd4TpgQKDevscZKtJ9BVmyt+E7egpYXeye\nFgI4rjwDaRxphVJJic5CDoRt7NXGuBj8/6OSgytCxm5salVl2IY6LCbbMRHO\nCV/S6pvgTylKyl9iAeA5zMKWS21M3ziCikXJBG1sWGBQ663Jm7yeZ7UvP8rS\nzxjxIa8lLBbK5qgZ3sMuyvaLZAJDOjUUC6IyLQTbZ6maykVhLKWucP552bI2\nRZF2ONoZ6L6cH2lJxve5SIxHSoGdpUmJlj+Me3S2B8treBxc8sNR5HbRqA6O\nFstyRVJmq6XcucfnUnkM8Qu5ShDWu4v0EypKI2WV9/lj/gpvpqJBdHpKrb3i\nlMTi\r\n=iLse\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.48_1622611920051_0.5400415116505266", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "474f09de11b819537a7e8519b411dcbd4a51eb978402550a42a6cca2fe689bde"}, "0.0.49": {"name": "@types/estree", "version": "0.0.49", "license": "MIT", "_id": "@types/estree@0.0.49", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "3facb98ebcd4114a4ecef74e0de2175b56fd4464", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.49.tgz", "fileCount": 5, "integrity": "sha512-K1AFuMe8a+pXmfHTtnwBvqoEylNKVeaiKYkjmcEAdytMQVJ/i9Fu7sc13GxgXdO49gkE7Hy8SyJonUZUn+eVaw==", "signatures": [{"sig": "MEYCIQDbZYAsY7grL4Q7pcc21NZJd69nRkwUuZ3JUeIcN8PPMwIhAO2rxHvpToQzmdAmPvD1Ks97Aq88q8gp1tUfEfyNb/1Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg3myMCRA9TVsSAnZWagAA3rIP/AqTy1cV2O4/fZlSEmwG\nkzBfKflv53ZGgY7mpY9ANI+bfVo06hKOQAfwoTNt8FXZ3ymqAkkdDNcVzdUQ\nksj1n9Tj0v8KYQBIDiLOZZ85itBlua5zlN6yvBUQH4lMIQaoENf+c1JdEvt+\nf5L8aTzP7ijg0nUYeXelE0oeWOONNSZ5IV1bAC0MM+FNggulrRI7P8unNQYM\nEAH159/9HD+K4CBFNG2MUwBKdq4jpknFFz84vl1UtFji+oPuK7IpIBWGiG3F\nlvrgxhSjw+IKsa1YdM0+Y0V8CitZH4ebz9nxJ/2XDIG3LsUwnT0mwqQ21CUo\nZ2TKafGc8S7nlmPFHWK8rKKKRAjlXTVgO7gedZK1aQH3U58/gAimVSP3m5R5\nF23jGRQwC5fxAiuUGnAdLRNE9B3wqchtBMqIZTX2keaIDG7cGGtd+51Aw9T8\nLhxCYCZunoJ0bV1lObhEXYLAxPB5bMnv3Yiyc2mZnqYBeAqvMod+j25o+o0S\nn3QMexG/iZmGzWoOEu+m/t49YawBYmhP//fk0+rCI1SkrLnVnSKwlgSzHAUG\n1pSh5jvrjxpsoXLYsny+vezQAOgi2DTgufGRH+cGQc6tuxxZiYC3vY5vBY61\neg+mG7U6D8Agn3In3+rqohgGiX2DPJIFMfdPCbFmOeSXJrXNdFavOkN3eQ+z\njwxP\r\n=gRwv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.49_1625189516078_0.7110936987986687", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0a455b36fbd9e3f4bb9627b688c4dc2354b664c4ca558ca6de6d024fe2210168"}, "0.0.50": {"name": "@types/estree", "version": "0.0.50", "license": "MIT", "_id": "@types/estree@0.0.50", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "1e0caa9364d3fccd2931c3ed96fdbeaa5d4cca83", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.50.tgz", "fileCount": 5, "integrity": "sha512-C6N5s2ZFtuZRj54k2/zyRhNDjJwwcViAM3Nbm8zjBpbqAdZ00mr0CFxvSKeO8Y/e03WVFLpQMdHYVfUd6SB+Hw==", "signatures": [{"sig": "MEYCIQDJZHWWR/iEaUA10mCpFAWaL2OqruDujwyx9kSyxklBaQIhANp4Q6vRdlp5D62MuUM7retAzQW0oV2c4uYhQrOrIilt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5KtNCRA9TVsSAnZWagAAq0YP/0ooaqCnMB1FQGyur5LD\nVkObpjVT2rEPV2x7/fZnxOFQkCBsiZ2YttIzzULnPYzWiLN0NdnihPDe5rYU\n1KN5VD2tFldMRraQRZtirs57Tw7uV85P48819W5a8OvV2Redxvzu1kbtMWs9\nuDf/Lk9Fa1Z7o7jjaX3OmpIc8Xc/Yf1g+yW/IDSqt0HzEScZvz2CfFAJNI7T\nv1mVEBhy4ata+xSyDRiIgsrdhU+jnBfjbUnUV/rVFnN73UB6Vro0KHZLou2b\nH2og3+EmBAc6o1CKh77/+DDD3aKJFjSBQX9b5EAJ2drTq0wFZH2jX+d8rgtG\nTPqRIZUlL+HPnQVAZpkEGfjhmerz6W2PAZAkvqlzO0Eijn0QFnmETyPxJzic\n3OiaG0QWrrPqRoIO1vqsXvlbtuw6b6tL2b0uoE4EsYKr+4K7agJiCrmu2Z9Q\nZaMjRveoATOG4rB6qjtRx5piPHP9RLfmwDvahvSfipYPMakjI2YhYs3JXrna\nELetlcRgwbvGeecc+75sMVfr2hqD15NBnHlAnZ1+uKjI55H8A+g6ZdPl6THo\n0lZDodl3S04zlBBu/UJEV0exThM2C3kqp8aqHVyy5d8gqPqE2KFrD0u4EbSw\neNiZkFIH9sM52xghQYCrms6fAolCWyGSNrB97FT2CfhHKPhrk8vmbmhyz/C+\n9M9x\r\n=U+/i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.50_1625598797034_0.974434419729703", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "17c3bbf309fdb8f5ab9aed3719a85b192528e1cc459e4ce8336f68936d64b34d"}, "0.0.51": {"name": "@types/estree", "version": "0.0.51", "license": "MIT", "_id": "@types/estree@0.0.51", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "cfd70924a25a3fd32b218e5e420e6897e1ac4f40", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.51.tgz", "fileCount": 5, "integrity": "sha512-CuPgU6f3eT/XgKKPqKd/gLZV1Xmvf1a2R5POBOGQa6uv82xpls89HU5zKeVoyR8XzHd1RGNOlQlvUe3CFkjWNQ==", "signatures": [{"sig": "MEQCIBF0f0CIniEbAgzvRBDt9ZmF4sMVS5zxy1cQd25m+pCSAiBZqf69X6cJS3z93yCm4YkTtM/sHxYsE8TC/Jxsz2kpsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/7i1CRA9TVsSAnZWagAAd6YP/i/5Ba1nZU8xgmNb3HJr\ntD51xHZtvtz3BH21+MNeTq23c0Iy248T7DJuu3/ybwt/6rvHgJcvDFTltnh4\nWyviIB0FY7ePe//W6y6YdeuKrjGT9aBC4jnIM72JKk7L3sev3Hl+DxiZ0KII\nzAq5d5s9QAAaMrTN6J/WwCdEVn5jnRgDPDKQoPU3RBfNOerEjPraio1vjGmR\n14PL/noggZgafIP3ex9O0+qyPkCFz1eRm84qyuz7Tuaq12CcXk+JST+Q3wyO\nn8i5U9lxvPx82cojEf4+NhKMz7HJI77wyl3QWdD4iRrBdUZ4IAf2NTtL73nM\noUm1lJNNAgXbQ5jGVvfLw1k5KBEPvbyqmNc9LCbndKdGBfSMz48yyzv0BD35\nXnz6oACy1lxhjcdqCb6V6Tz7YZF7dnI7jCOpqkvk0UenzgegDqYv1ulnVec6\njR87fqS5gR7YwZLbktBGhmaL1BxJJqCxcXi1ZBbXF8jpA7RtHKIsPeDnPsdS\nEl511XA2csGXcJDjGyguVjek53ppvccvRvfHQIb2uZZBKKeY2osTqze1gjM7\nStq/r6ndSL5F6b+tjVJTszOehJfhIHUvCRIHTC4WddDHQ3d/xHPfBPsZk9PG\nxzrPgI1/YU4NLtTysf7T6b87tCahrtTjs8WLvp0QZfsGP9CyzRM2csbt9Mht\ndYM2\r\n=TXma\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.51_1644148917321_0.7972041649319637", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "95cbccd2dde8b8492f61e0e8049641ae05129705d9ba742955b711cfb71f3621"}, "0.0.52": {"name": "@types/estree", "version": "0.0.52", "license": "MIT", "_id": "@types/estree@0.0.52", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "7f1f57ad5b741f3d5b210d3b1f145640d89bf8fe", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-0.0.52.tgz", "fileCount": 6, "integrity": "sha512-BZWrtCU0bMVAIliIV+HJO1f1PR41M7NKjfxrFJwwhKI1KwhwOxYw1SXg9ao+CIMt774nFuGiG6eU+udtbEI9oQ==", "signatures": [{"sig": "MEYCIQCkHYMm49RI1X+FiKAVuHf/r4B3yMmfy69FAEUF3jHYDAIhAMiyXMRtWKrOD2kVDCJl2XwRS+uMxh6cKady/zYUvc/x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitt0IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqn+g//WzTa4t2UmiK6pTJycCnmxXahyu+HMjwIK+poMlP+3C/6TenF\r\nJJ5dBacFiiwwQNXRU3mO5j3KorW7zVJbPWUVuXAvy0ejiyll0QCI3i/c9kvi\r\nVK1amE9I697AywdwBS+SPOnxyNjvhP8s3vzs4EGC7Bqhk8px4TIxFmb/Dc2f\r\nbi7/bqqosxVzEz/moy0moyBITgcvNLOa8j3s8GKMY3xlH0v9A+dxH9sj11gL\r\nle7NUlz4kWt2YPNH4aMpP8+gRRDvJOGAiNN6LZ6nzj3zOnVfbDTY4dH/g4RJ\r\nH8EdO38oAdYOs+oJt0EW0GGqFQ2b+XJozMRvr0iQMEOknKNhKgutnqjkCKmp\r\nZ+h8EywUO/mzvZPbJNPmtmCFriq5chRsZ5bYbXtTJXeFqTNj+gr6vMD3h+HS\r\n3xhMruUith/X2blquGXGz+Wj4uyYrmvSlFm8zcp+CCh22ruxAvskNFhrdXU3\r\nuWOnr+8PHBGrwXkfOvOorIOpByMX0TVIBd68njB/L6FPC5583fkp9cWcCGVR\r\ndZ1f6/L8+pL3gHxfrQMg/fECaKF4YKjzTVtJRrAju1esPZXNe0qAAWwCOzVP\r\njqbcUv24wU+L8I5XLoE+HCKeZnT0QyqPH0Xfkxm248h53tzby1Ef9HsygY8F\r\nwEur2zzefY6abJTIOB6h74Z/6GiNBNDVF1E=\r\n=U40N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for ESTree AST specification", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/estree_0.0.52_1656151304011_0.15730951131297544", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b0676d90e8ad8d4e2e79cad828f7376d13fcbceea7d6df658e0910ff66daa200"}, "1.0.0": {"name": "@types/estree", "version": "1.0.0", "license": "MIT", "_id": "@types/estree@1.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "5fb2e536c1ae9bf35366eed879e827fa59ca41c2", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-WulqXMDUTYAXCjZnk6JtIHPigp55cVtDgDrO2gHRwhyJto21+1zbVCtOYB2L1F9w4qCQ0rOGWBnBe0FNTiEJIQ==", "signatures": [{"sig": "MEQCICzyd8XEmKX5hzrVNMbpbzQcrcJPXSObUNHrct3lEOT4AiA7CoF8MjPyeBuKQoCjUQV8HTYOSQwWgYMV1szzEhf6+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizdpYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCKg//ZnZm6fk4uQSw2xOffkcqSM1Z7RI67Up8m6a9W1g7pz1xnvyz\r\n1VBkxvi0g3nzTiCSiuLgvlYGLFBza9zgsVwJl0xpgr3EiQP083LKLWr4Hnqy\r\nCxgReOJZjYd0B6akBOWQglFx+YWaPqbdI0aIzBQIBF3wcuuyy621/8KQGeiz\r\nuBB8tc7gVBK/HIKrUxQDk+TdyZJv8ydjOyIwxkfDlDoBdNYpJiLBvM2PTkiL\r\ng7a0pzSJXRKw7iDuK2i+bWrP4XSlMDd19le80KcysLrX5BAUshq0PSVPXDZX\r\nR4mqB0TbAOmlTmpBySs33ZgNIb6wUyz8li6oXZm6nVTWUtF13ia7Pdecwi0M\r\noBHaI/yNgYfft8ieArti3ppWfSubKMvFRoq9kH46RvLTUCwq5utPD7sD7O6v\r\np5V1mxrW39GvCISXbnASIiFh9J1oqFrrGZKSZHqYoywxRtsDD2Lpq73rEgGd\r\n/lgBj+pKNjDquAkN+VNDbfziD01vw0Itld2SBbH4s7Jlm9cnv7OAUDFLWdGD\r\n17t8RXUghH+oDRHvhIHUQyBzayg6ftwcdIJucVu+pMLjzMgJs8W1NcnfLDLm\r\nzx2kILfkkryLAGswqXH1nC5Qh+QrEmGbMVnnoMrxjzO7yZz8C5I8SwmBIb/3\r\ngBfiunx2NPtb+jbNRLKJMt8yBKKzVSdccm0=\r\n=i7N1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for estree", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/estree_1.0.0_1657657944605_0.2009675111383169", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a2ea4a390167d173308db373b92a5dc4b94a7e8263cc0de049320c577bb30fc1"}, "1.0.1": {"name": "@types/estree", "version": "1.0.1", "license": "MIT", "_id": "@types/estree@1.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "aa22750962f3bf0e79d753d3cc067f010c95f194", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-LG4opVs2ANWZ1TJoKc937iMmNstM/d0ae1vNbnBvBhqCSezgVUOzcLCqbI5elV8Vy6WKwKjaqR+zO9VKirBBCA==", "signatures": [{"sig": "MEQCIHulIeDyj7eXj9+dF0TIlArkd3AjYjwhuPo3HY+aWLvpAiAJFzhWdYbvzYxXz4+eq5UTafAfPYFtYP6mUa01RdO9nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkP3X4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1rg//WZ08zwx1wQJgjleyGePaCwAwSlmUOyyF73Z5mgvLT7Bc+o8y\r\nysPSughPY3O1JGE2O/ttftlqvtSKCwuFSsdDCF40LZ3WydSY8rRl0L9okdPF\r\nZInjdGAHnutZO4K7zAE/XvUl7id7B0UWcHQ0ifnRuA41engqT/wkUDoutZyb\r\naYCTdJ77toJ0auQtI1Wa/4oV9iozvlnWSXZ6fgGEWaR4KIRdZ6tiFand63/j\r\n2Sbf3Rm/PxO/ji1/h94PsZUuffK4n9QDIr4YLm6FzvDu3p+eeTU8LCYSNwwm\r\nbRdRwuIlsaNFftDN5r2gdOk/r7uWvSF4LabEnYPDnVFp7V8lcm7bMg478dV+\r\nEUhAjhRXFu7VylPDC2D2C7gjrmvTrZ4BqmFOVRx6sCbDD7MQJZGKMlVv7I/M\r\nfSRE1TRkxo+LzQ66qVoYGj36ti1UIhGjLHo+qazgXsBDXrDZwEdUmMDp550W\r\nSjJKT99MmCpd+u+C4TXuKqhXw7fQBKcUyY69cwNlAhQlY7o7c//UOsOEisqy\r\nlKeuh43lRx8PKPxFtmy1S5Weq/weOm1GuRbpzbqdAgudWQzo/KtAUCeTdWEo\r\nGdye6p0NEEHSMpW5U1nxeNAM63I9YEFrRsgx6Pb3xHs4B7BNtksBBTP4Mo2z\r\n8DLiOlrhfY5GMx89UF16NPW4uuZYgKrBDuw=\r\n=NbbQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for estree", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/estree_1.0.1_1681880568290_0.8714915971902573", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6bb5253923dc858fe2d49a5555adfc2902dcbdb5536fa2b595339f0b498c29cf"}, "1.0.2": {"name": "@types/estree", "version": "1.0.2", "license": "MIT", "_id": "@types/estree@1.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "ff02bc3dc8317cd668dfec247b750ba1f1d62453", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-1.0.2.tgz", "fileCount": 6, "integrity": "sha512-VeiPZ9MMwXjO32/Xu7+OwflfmeoRwkE/qzndw42gGtgJwZopBnzy2gD//NN1+go1mADzkDcqf/KnFRSjTJ8xJA==", "signatures": [{"sig": "MEQCIDT+Nu0K5kh1BLIWSLRooIH4lAvwODObaLK3Loij9A86AiAQJbq6r6fL5zGZGATsrUuDWcbwcCPmSPOrdIb/CQPPbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25703}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for estree", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/estree_1.0.2_1695489811123_0.1300390086768457", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "00c2c4d477c1ec5ae01786562bcd553116a4ecb2d8c6d0482a9186c7ba63f10f"}, "1.0.3": {"name": "@types/estree", "version": "1.0.3", "license": "MIT", "_id": "@types/estree@1.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "2be19e759a3dd18c79f9f436bd7363556c1a73dd", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-1.0.3.tgz", "fileCount": 6, "integrity": "sha512-CS2rOaoQ/eAgAfcTfq6amKG7bsN+EMcgGY4FAFQdvSj2y1ixvOZTUA9mOtCai7E1SYu283XNw7urKK30nP3wkQ==", "signatures": [{"sig": "MEUCIQCYVV5ga6usuvwtzZ9wWuO8MJlB3l+5x17ynN5abfOcVgIgEhAwOAhI+m/89oeZQXdCNP1MrW5M7QeHNUWjblbGbMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25475}, "main": "", "types": "index.d.ts", "nonNpm": true, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for estree", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/estree_1.0.3_1697594389353_0.028534173201061552", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "36e79e20f43a2a1e16682303808bd4752e77b5acbfb04f2accc676715ef8d7ad"}, "1.0.4": {"name": "@types/estree", "version": "1.0.4", "license": "MIT", "_id": "@types/estree@1.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "d9748f5742171b26218516cf1828b8eafaf8a9fa", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-1.0.4.tgz", "fileCount": 6, "integrity": "sha512-2JwWnHK9H+wUZNorf2Zr6ves96WHoWDJIftkcxPKsS7Djta6Zu519LarhRNljPXkpsZR2ZMwNCPeW7omW07BJw==", "signatures": [{"sig": "MEUCICh11UbpJKemTnzooAzUO+KwQgiAJlq8ufN3YuC+sPMWAiEA2wMKZRttvUR4ydKeOL2yLJT5GrpAtpBBVaEN4bB4iM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25736}, "main": "", "types": "index.d.ts", "nonNpm": true, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for estree", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/estree_1.0.4_1698704229223_0.7166445817633738", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d9dbde8e10a5e616b8a102568562e7a5f694f5c4e65dbfe3bcdf608938e918d1"}, "1.0.5": {"name": "@types/estree", "version": "1.0.5", "license": "MIT", "_id": "@types/estree@1.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "a6ce3e556e00fd9895dd872dd172ad0d4bd687f4", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-1.0.5.tgz", "fileCount": 6, "integrity": "sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==", "signatures": [{"sig": "MEQCIFJQgOiFznLMXFq05bz/eZDk+sG9a96aCuKJGxQ5eO1aAiAcYIADN6TePbtusX5C8Gmj5EvuVUUXomBXvN6dWAamRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25736}, "main": "", "types": "index.d.ts", "nonNpm": true, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for estree", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/estree_1.0.5_1699323835370_0.46760862446623763", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6f0eeaffe488ce594e73f8df619c677d752a279b51edbc744e4aebb20db4b3a7"}, "1.0.6": {"name": "@types/estree", "version": "1.0.6", "license": "MIT", "_id": "@types/estree@1.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "628effeeae2064a1b4e79f78e81d87b7e5fc7b50", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz", "fileCount": 6, "integrity": "sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==", "signatures": [{"sig": "MEQCIAujiwrAiiZr1+7OZlavUx4rTv02vDfwm6Fb5/kdDokjAiBl11QUZShAi6ESlJHEVZ+D3KKreQDRQLWZo1dMV1D00w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25834}, "main": "", "types": "index.d.ts", "nonNpm": true, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for estree", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/estree_1.0.6_1726652220894_0.58556**********", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0310b41994a6f8d7530af6c53d47d8b227f32925e43718507fdb1178e05006b1"}, "1.0.7": {"name": "@types/estree", "version": "1.0.7", "license": "MIT", "_id": "@types/estree@1.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "4158d3105276773d5b7695cd4834b1722e4f37a8", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz", "fileCount": 6, "integrity": "sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==", "signatures": [{"sig": "MEYCIQD3xhoGBX2cJb2Zi+1J8azqscXEQYFxetPjwbpIQMvT4QIhANrwk5fcgqXTjmcB8tb+8X3sLZbVrkGdVxBQEutLxzmQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26147}, "main": "", "types": "index.d.ts", "nonNpm": true, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for estree", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/estree_1.0.7_1742801651201_0.7252383563334608", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "1ab11f4e78319f80655b4ca20a073da0dc035be5f3290fb0bfa1e08a055d0c7d"}, "1.0.8": {"name": "@types/estree", "version": "1.0.8", "license": "MIT", "_id": "@types/estree@1.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "dist": {"shasum": "958b91c991b1867ced318bedea0e215ee050726e", "tarball": "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz", "fileCount": 6, "integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==", "signatures": [{"sig": "MEUCIFpqrUlgdlY9VKuYfAN0YrwKdsye4K7oz9thbvxlUryaAiEAiHaVlRKMQQI7nKErk9ciLTEcUVEpwkZUTRwgODNwarg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26173}, "main": "", "types": "index.d.ts", "nonNpm": true, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for estree", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/estree_1.0.8_1749168274521_0.2562279118336148", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "7a167b6e4a4d9f6e9a2cb9fd3fc45c885f89cbdeb44b3e5961bb057a45c082fd"}}, "time": {"created": "2016-05-17T04:52:04.146Z", "modified": "2025-06-09T04:08:09.756Z", "0.0.15-alpha": "2016-05-17T04:52:04.146Z", "0.0.16-alpha": "2016-05-19T20:47:31.974Z", "0.0.21-alpha": "2016-05-20T19:32:12.886Z", "0.0.22-alpha": "2016-05-25T04:50:31.211Z", "0.0.23-alpha": "2016-07-01T19:22:26.546Z", "0.0.24-alpha": "2016-07-01T22:39:20.999Z", "0.0.25-alpha": "2016-07-02T02:19:37.651Z", "0.0.26-alpha": "2016-07-04T00:12:48.719Z", "0.0.27-alpha": "2016-07-08T20:05:58.123Z", "0.0.28": "2016-07-14T14:31:42.967Z", "0.0.29": "2016-07-19T13:10:40.779Z", "0.0.30": "2016-08-02T15:52:02.520Z", "0.0.31": "2016-08-19T15:24:45.055Z", "0.0.32": "2016-08-25T18:41:22.323Z", "0.0.33": "2016-09-19T16:46:20.684Z", "0.0.34": "2016-09-20T21:15:42.984Z", "0.0.35": "2017-04-11T19:36:12.390Z", "0.0.36": "2017-07-22T19:04:30.809Z", "0.0.37": "2017-07-24T18:01:52.918Z", "0.0.38": "2017-10-13T10:32:38.497Z", "0.0.39": "2018-04-17T20:22:59.882Z", "0.0.40": "2019-11-26T23:24:37.700Z", "0.0.41": "2019-12-23T20:14:58.798Z", "0.0.42": "2020-01-08T22:52:06.886Z", "0.0.43": "2020-03-18T20:42:06.294Z", "0.0.44": "2020-03-20T18:06:43.516Z", "0.0.45": "2020-06-23T19:41:23.720Z", "0.0.46": "2021-01-11T22:15:03.533Z", "0.0.47": "2021-03-25T07:31:28.028Z", "0.0.48": "2021-06-02T05:32:00.220Z", "0.0.49": "2021-07-02T01:31:56.207Z", "0.0.50": "2021-07-06T19:13:17.225Z", "0.0.51": "2022-02-06T12:01:57.547Z", "0.0.52": "2022-06-25T10:01:44.230Z", "1.0.0": "2022-07-12T20:32:24.817Z", "1.0.1": "2023-04-19T05:02:48.457Z", "1.0.2": "2023-09-23T17:23:31.298Z", "1.0.3": "2023-10-18T01:59:49.609Z", "1.0.4": "2023-10-30T22:17:09.470Z", "1.0.5": "2023-11-07T02:23:55.578Z", "1.0.6": "2024-09-18T09:37:01.060Z", "1.0.7": "2025-03-24T07:34:11.427Z", "1.0.8": "2025-06-06T00:04:34.692Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/estree"}, "description": "TypeScript definitions for estree", "contributors": [{"url": "https://github.com/RReverser", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": "", "users": {}}