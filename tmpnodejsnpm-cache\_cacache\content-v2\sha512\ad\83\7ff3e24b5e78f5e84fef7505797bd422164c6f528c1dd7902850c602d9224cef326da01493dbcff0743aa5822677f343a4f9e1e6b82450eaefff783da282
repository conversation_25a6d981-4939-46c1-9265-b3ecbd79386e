{"_id": "tslib", "_rev": "93-c4f1441b5b964dae9161282b077ebe4c", "name": "tslib", "dist-tags": {"latest": "2.8.1"}, "versions": {"0.0.1-security": {"name": "tslib", "version": "0.0.1-security", "keywords": [], "author": "", "license": "ISC", "_id": "tslib@0.0.1-security", "maintainers": [{"name": "kmanion", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/security-holder#readme", "bugs": {"url": "https://github.com/npm/security-holder/issues"}, "dist": {"shasum": "24b9ec60c77c2770dd727f6eca34d562ec5c252e", "tarball": "https://registry.npmjs.org/tslib/-/tslib-0.0.1-security.tgz", "integrity": "sha512-MGQLCd48q7p3lze9vAW0lVdweX8ygY7oqEoNCILzO+B3Ks1lzUQncn6o0UUpl7Sh6rEs0x7NNhbkjzVTsNtPhg==", "signatures": [{"sig": "MEUCIAfis4fvc1KKf8keN2lqtmpFWkZoB8HsGtCoQHpXm2TzAiEAvnR7bSn/jyyeAwbXPTuBJYVlHjyI+EKamURRIpy9O+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "24b9ec60c77c2770dd727f6eca34d562ec5c252e", "gitHead": "9ef10011153183d323ed420df5bb17eaf08ab730", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "kmanion", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/security-holder.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "This package name is not currently in use, but was formerly occupied by another package. To avoid malicious use, npm is hanging on to the package name, but loosely, and we'll probably give it to you if you want it.", "directories": {}, "_nodeVersion": "5.10.1", "_npmOperationalInternal": {"tmp": "tmp/tslib-0.0.1-security.tgz_1466117777096_0.3211661959066987", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.0": {"name": "tslib", "version": "1.0.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "_id": "tslib@1.0.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "6998194cc814ec190383692a0a82bfefaf2eb0b5", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.0.0.tgz", "integrity": "sha512-L0ArT0jBH9PUOH5Aka4QjM/5S2jqC0CN61Y2b3AMeJb+tQ/JrajupVSwM9MEkzPODElCWBNak7sh1W86G5cfFg==", "signatures": [{"sig": "MEUCIDhQzmD101ycI1AqWrb1JxMrR77m1LKc8brZ3YS8tw21AiEA8xS/7SNrjKO3tWpM+f7h6KbczOQ3jfT1bNuO8XeT9cA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "_shasum": "6998194cc814ec190383692a0a82bfefaf2eb0b5", "gitHead": "fa4c8cc77570f662be17aee747797edc7988884c", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/Microsoft/tslib/blob/master/LICENSE.txt", "type": "Apache License 2.0"}], "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Runtime library for TypeScript helper functions", "directories": {}, "_nodeVersion": "5.11.1", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.0.0.tgz_1467074765430_0.27921289042569697", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0": {"name": "tslib", "version": "1.1.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "_id": "tslib@1.1.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "df06b30e178cc8e93d631899153a88f0d369b052", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.1.0.tgz", "integrity": "sha512-dqH0ktT4kiaMs4MddpBf/0rwbL7MQingazV4AL/qitP7rrSiPmNYgdGnMdUggDeLHnEcdAnVTa6wPOST5x3TAA==", "signatures": [{"sig": "MEUCIDZI1lX/KwRaaeezooJ1naHUaCh9fajdVuRhRImzDnqOAiEAviGK7aAtuZDz2RvaEEipLa8bho0rON0O0uSsyef2VlE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "_shasum": "df06b30e178cc8e93d631899153a88f0d369b052", "gitHead": "53f2f165fdcdea32d1199d6a96759fe962b7a922", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/Microsoft/tslib/blob/master/LICENSE.txt", "type": "Apache License 2.0"}], "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Runtime library for TypeScript helper functions", "directories": {}, "_nodeVersion": "6.3.0", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.1.0.tgz_1479755930401_0.8607384378556162", "host": "packages-18-east.internal.npmjs.com"}}, "1.2.0": {"name": "tslib", "version": "1.2.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "_id": "tslib@1.2.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "074fb171034594bdba4c4dbb197c26e4a0b88383", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.2.0.tgz", "integrity": "sha512-Q0LMqOcejiawNBNxE+WB+6gth6akOCQEIw4Kbg2UHJIfigyh+oe50ynBHc2m3d2Ge5ZQt0beoISratodGs0pMA==", "signatures": [{"sig": "MEUCIQCnTEP+5WtzZIKpY22ykVnAyhO/I8wdU0ztzEf/AwA+iAIgVEIic46M0uTAzMFTf7Q1fJ2jmVlRFZ93b+5DKsyjyu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "module": "tslib.es6.js", "_shasum": "074fb171034594bdba4c4dbb197c26e4a0b88383", "gitHead": "ee3bc0d49706d885b66028010f54aa79577f6c81", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/Microsoft/tslib/blob/master/LICENSE.txt", "type": "Apache License 2.0"}], "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "5.11.1", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.2.0.tgz_1480468810722_0.7613356069196016", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.0": {"name": "tslib", "version": "1.3.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "_id": "tslib@1.3.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "fcc488f05abdfc34cbfcd26bba6cfdb02fe726eb", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.3.0.tgz", "integrity": "sha512-LsucxpaWAMYIegVDPOlds3AkXN6yJn8eIshLCm5pPMCimEcRRQb0s1h4bsdO4p4tTK2+ONr97+XFSvsGCdc3Zw==", "signatures": [{"sig": "MEUCIDeMiKxDY5artDciYXgKphcrRsAaYl1A/anC3f3hyunpAiEAtlPbIPVv5srb1nJIQSeFgOT7R59y6BtrZD6Rdmm28aI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "module": "tslib.es6.js", "_shasum": "fcc488f05abdfc34cbfcd26bba6cfdb02fe726eb", "gitHead": "72389df410573470f21a1db3c3359fe889ffe7d0", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/Microsoft/tslib/blob/master/LICENSE.txt", "type": "Apache License 2.0"}], "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "6.3.0", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.3.0.tgz_1482341966387_0.4456295589916408", "host": "packages-18-east.internal.npmjs.com"}}, "1.4.0": {"name": "tslib", "version": "1.4.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "_id": "tslib@1.4.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "6bb6b97fe5f3d71bfbcf10e22ee1e29b6e63cc19", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.4.0.tgz", "integrity": "sha512-o/68URESl2pv3AbrmqBclrc5WM5J1lDeXsZ1Ap2JZfxHVpOrMIbP/e66QvCEObP5pjKsY255taa0yF5uYdXXXQ==", "signatures": [{"sig": "MEYCIQDxB4VjHHyaVlijmzFI2OI5YdIC5vvXERNRezF+w/ZcTAIhAOTLru00OFxoPW/RLnCBx0+knkO+OVAs9wVyq9Zx0WK6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "module": "tslib.es6.js", "_shasum": "6bb6b97fe5f3d71bfbcf10e22ee1e29b6e63cc19", "gitHead": "e633121b3f22d4cdcc2a9a88c9a13bec509db69d", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/Microsoft/tslib/blob/master/LICENSE.txt", "type": "Apache License 2.0"}], "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "7.2.1", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.4.0.tgz_1482381969978_0.5694500505924225", "host": "packages-12-west.internal.npmjs.com"}}, "1.5.0": {"name": "tslib", "version": "1.5.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "_id": "tslib@1.5.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "3bb50f871e5fdf9a4555a9ff237b730860048fea", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.5.0.tgz", "integrity": "sha512-97kp7YA+MzS/3JIPU0mGeELSs8lACJOAq4SLBH+SCnd+FD3y6YYLe1xGOsF3VnqdeKJV3fTQ0QrHR5+fsu+IdA==", "signatures": [{"sig": "MEUCIBvmokW39o9s/6DddohJd3cdWbSNhschukaNNVpRfEwNAiEA1c069obPldhlcGISafXVqZeMAlxvQneNnIw0vP5v/i4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "module": "tslib.es6.js", "_shasum": "3bb50f871e5fdf9a4555a9ff237b730860048fea", "gitHead": "d1e950a04d8b5c749fd53baad667d8aaef5fd4b9", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/Microsoft/tslib/blob/master/LICENSE.txt", "type": "Apache License 2.0"}], "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "7.2.1", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.5.0.tgz_1483639649866_0.3902803307864815", "host": "packages-18-east.internal.npmjs.com"}}, "1.6.0": {"name": "tslib", "version": "1.6.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "_id": "tslib@1.6.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "cf36c93e02ae86a20fc131eae511162b869a6652", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.6.0.tgz", "integrity": "sha512-ML0WHCzxVSSS6BZKOU89PX2eLxEB2SWh8zdwTuvDwCe11iJoG9h4a8EnGlFt11oxocvPaoGOwjunT0ul3SebEw==", "signatures": [{"sig": "MEQCIH7h8mzoyLhw+Tl/dCNSN0rjfyIAfAxKznq/le3N25r2AiAimL5uE7cFRyYQNGCqS77NnxMaWQ4EU2i19lpZ6omGlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "module": "tslib.es6.js", "_shasum": "cf36c93e02ae86a20fc131eae511162b869a6652", "gitHead": "6a883a325b50a9969fe7738ac7461a03cd570363", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/Microsoft/tslib/blob/master/LICENSE.txt", "type": "Apache License 2.0"}], "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "6.9.5", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.6.0.tgz_1487723078652_0.47501660767011344", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.1": {"name": "tslib", "version": "1.6.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.6.1", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "b77b09abc5fa4935e157d838b80e36dad47152c4", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.6.1.tgz", "integrity": "sha512-x35hQMk5wOH6iQtn2NfO0c8qLGMqxbTumt7uGMH0CObP1yAWl/CAQ8pGoKpdTSK+Q4ty87knPvQgJhuhVg6H7A==", "signatures": [{"sig": "MEUCIQCaTDxGdV6Y8q1zp1zjjjpjvB0WNL3vsY1YVhHH1ujMQQIgPPnRCAm8foa987cZJ3qh6tky9Xhf+VCWMPP3gPD1wY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "module": "tslib.es6.js", "_shasum": "b77b09abc5fa4935e157d838b80e36dad47152c4", "gitHead": "b687af3e87d37965064577b49be87f70376f7cb4", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "6.10.2", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.6.1.tgz_1492640651319_0.26942968158982694", "host": "packages-18-east.internal.npmjs.com"}}, "1.7.0": {"name": "tslib", "version": "1.7.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.7.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "6e8366695f72961252b35167b0dd4fbeeafba491", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.7.0.tgz", "integrity": "sha512-w53V7tqqsLjhlMYrjZQOdT38bslIXQ1xEl1j0AY+myeEzZ2EtNdw0o2C21Wpgnfb2j8nNzun33zUuRzGeoW1ng==", "signatures": [{"sig": "MEQCIAynkjVNHa7LV3UGsc4SlTW0RKwXB/2I0HfESEmA6t4iAiAkY+NUNklxpBxB038macaZp46YceAeqANzcIGjuInnpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "module": "tslib.es6.js", "_shasum": "6e8366695f72961252b35167b0dd4fbeeafba491", "gitHead": "0cadbd082a3d57bec86bd5da792b131c3816edd8", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "6.10.2", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.7.0.tgz_1493934784345_0.395422940608114", "host": "packages-12-west.internal.npmjs.com"}}, "1.7.1": {"name": "tslib", "version": "1.7.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.7.1", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "bc8004164691923a79fe8378bbeb3da2017538ec", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.7.1.tgz", "integrity": "sha512-V1/w7gNW+Ke6nqyi5es1uv9Ncz4OJf4cDO7kaOCGDz5iPXMXNKkmKWn6aMmCFgU0d0O2+/nMA1PDMrMGJkPtdg==", "signatures": [{"sig": "MEUCIE+EsLZJ4SyK793JQQsAnFMRThYHohfvMQ94fud2B4N7AiEAprtyAUpFvhS1X3u3bR2mfPeBpsrvfJpQQfJGDkpTb+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "module": "tslib.es6.js", "_shasum": "bc8004164691923a79fe8378bbeb3da2017538ec", "gitHead": "363babce6a17743cd2247b97a008b7b25fc9322a", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "7.2.1", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.7.1.tgz_1494888108332_0.903959889896214", "host": "packages-18-east.internal.npmjs.com"}}, "1.8.0": {"name": "tslib", "version": "1.8.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.8.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "dc604ebad64bcbf696d613da6c954aa0e7ea1eb6", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.8.0.tgz", "integrity": "sha512-ymKWWZJST0/CkgduC2qkzjMOWr4bouhuURNXCn/inEX0L57BnRG6FhX76o7FOnsjHazCjfU2LKeSrlS2sIKQJg==", "signatures": [{"sig": "MEUCIQDl7pF2fuj082JPOOwDvMZTBIWV1JyJwb2uDjNaedFudgIgdRVh1V5P1qRH0SEprmpoaU4yWD03uqhRG0W3zIqbk58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "b780e46e4293f0d0793c6c65f4c77feb88e897c6", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "8.6.0", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.8.0.tgz_1507696492379_0.2634648806415498", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "tslib", "version": "1.8.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.8.1", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "6946af2d1d651a7b1863b531d6e5afa41aa44eac", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.8.1.tgz", "integrity": "sha512-13/fEL8ro991mjA1w35XI/FSHF4dI2L+zNxkJJnGUcjFG4Ht+3ztBusOmg/DFiOks5NKJX3wXSG+q/nKUmhe5g==", "signatures": [{"sig": "MEUCIG7MRGBUfqlUSfmfo+ahQcg6ojnFCH/bbseHWamgfaEdAiEAnQmGzONMelDchYm22f/Iqu6hUR/vHjf4Wnky8+y0PR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "_from": ".", "module": "tslib.es6.js", "_shasum": "6946af2d1d651a7b1863b531d6e5afa41aa44eac", "gitHead": "b1ed5e1b940756d18609833caf9b781e9bc3984f", "scripts": {}, "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "7.2.1", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.8.1.tgz_1512602816537_0.9111927668564022", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "tslib", "version": "1.9.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.9.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "e37a86fda8cbbaf23a057f473c9f4dc64e5fc2e8", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.9.0.tgz", "integrity": "sha512-f/qGG2tUkrISBlQZEjEqoZ3B2+npJjIf04H1wuAv9iA8i04Icp+61KRXxFdha22670NJopsZCIjhC3SnjPRKrQ==", "signatures": [{"sig": "MEUCIQDttC6cKHbfXHjWuYE04M/2UhIqkPnQ7lGTIFVYu4Bg7wIgMCjQMuehBgcRgjbMm1f7CYgrSurzsRuvee4lqF/plD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "3d0f4d4823605e6e82cf250b509d9a685c42ad41", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "9.2.0", "_npmOperationalInternal": {"tmp": "tmp/tslib-1.9.0.tgz_1516311863696_0.40897737606428564", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "tslib", "version": "1.9.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.9.1", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "a5d1f0532a49221c87755cfcc89ca37197242ba7", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.9.1.tgz", "fileCount": 12, "integrity": "sha512-avfPS28HmGLLc2o4elcc2EIq2FcH++Yo5YxpBZi9Yw93BCTGFthI4HPE4Rpep6vSYQaK8e69PelM44tPj+RaQg==", "signatures": [{"sig": "MEQCIDCphaZpaTl5yPLQr19gyVCXc4I4JZH0GpiWx+7Jdv1sAiAd/OUzA3rol4YAXSW9djukaEt7eMZuq+Ejq3M3uUAYCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58108, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+dJlCRA9TVsSAnZWagAAc04P/2055iQwodzc2/wjF925\nGQEwSSuK3fseLB9zAY+/Ik0Ttv8d70p7v3AYp7L8M8/bvrZnQcmqmLxUuUJf\n3nG+j7mfizoPK6HBJ7h6LVFs5IKpHHKapxsmg+dUj9WL/xgaou4OKTPeupBl\nld/Ke8Y2bI2c7l6RsMz7aMj/KbnTSlXXMg5xTOCHHieSq20FrLRhZ23+Uoby\nNPzVuJAclBGKw7z5d9X6rL5tkYYA8lvnFwYdInz4QFvNl1tJk5WidFZ7LwUe\nvwbGsMuJ6tiqSfzqbaiQ22WSSzqzfxpFUx4dxUf3cS6NeKjNjGMi2y/U7Cid\n+PQyRWsU2GojdjptwoOie5CHHtYFXecQh8/WPYiHoL386iCH5rgX8nWXvl03\nfq89jw4/VXUfvXjMrDTekQDOakrEjkqwhbV+fnalCkdrguNQ7qBweTQ2Qetn\n6MENIqo8CecjFymZT/ggJFFiRpmi8gelV3dzC+imJJgYXh7v1icSuL3AKsF+\nVPTs/QEzGtPIWnej+NWKG1yu3X+SQgSs28Hf6hDcd3Hy0MMVuVosWDD4WG2J\n+yjEB36xLET9Z/ni5Xy4/MMNp6TynpA38/a5qA2Df66QxNWLKs8BY1NhIDv5\nIbHsA8j8s9wG2lY4Cisjii21fZP2bHNNWibmOSxB+Jgu/dFsXl4WR/L+UWw/\nRHVh\r\n=RMXV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "2c3d5d6be3594f3a202cfb7593f0b2d1b87c76ad", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "9.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.9.1_1526321764351_0.034802452325894295", "host": "s3://npm-registry-packages"}}, "1.9.2": {"name": "tslib", "version": "1.9.2", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.9.2", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "8be0cc9a1f6dc7727c38deb16c2ebd1a2892988e", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.9.2.tgz", "fileCount": 12, "integrity": "sha512-AVP5Xol3WivEr7hnssHDsaM+lVrVXWUvd1cfXTRkTj80b//6g2wIFEH6hZG0muGZRnHGrfttpdzRk3YlBkWjKw==", "signatures": [{"sig": "MEUCIEPqyl9Vrmmps4zuAzSlH7dUv634Ho7EQ+ezeVofcrAqAiEA+Xp7Jh2KzeCLgySkmccN4RAFD9x+bA6UsQHXYQDdFNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58208, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDedzCRA9TVsSAnZWagAAsfQP/09FFQBPzm/nvPzty2rY\n+7GveoqLDO8sv1bbAJwjR/WleMPfuIsNWu1ORxzp9AKathfCl0bewO6Vv2AZ\nYdCZ2/kM2lvOF89Goj9iB/PJKReXcsNWguOdt/8d80b7z6PGxsvjDuolN00C\nn2X3zktK9MNrMng2p9CBrXicgLG6JH3RiVpWyWHppKTmazYSVz8mryYer6jV\noAAMUfdGC+n/QZtHVRzO6aBCHfJkT/wFglyH3ejZqYmGvyqese2wZ0DN0I8e\nxPs/lVNbrBXtaxSMyxOgG5YVnmhfgyLIColYapGoAc+AbFpdPARW6OjhqkyC\nVrRce2ZWOJd1xs5CXk92weHG/FW/pZzsTQvZCyiuT80uE3Q036fhQNjyHWyS\n/GV1XmbRYmOK5Qcj/mD1cSoSNom3qxG4xo8H1LHDtlIYO78apHEdU73apv6x\nySwkkMRWkMWnUc2Mwvm4IoDMX+Qw9160m/glJZfJdKBkQMfABR7emgh9k3zu\nZrMLLb6mw+KSl96iDvybTNlcFnjpc4iNWmZiB5lSXnop41LHt/pKjMQOXZzY\nmV4w/7UVSB0S++jMFC1HzlU3umnzTY+tCAQINBt8iyQm0astvyGlUlI9bGXw\nWJfbzPlSjyjzwrEctffsg34s7l+l4BtgAFmMHuI9ModEWk/Eh207BLe2licn\nmCsn\r\n=5/3y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "a921045169515012bd51cb6153c1eea692229c86", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "9.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.9.2_1527637873859_0.7466322394870168", "host": "s3://npm-registry-packages"}}, "1.9.3": {"name": "tslib", "version": "1.9.3", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.9.3", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "d7e4dd79245d85428c4d7e4822a79917954ca286", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.9.3.tgz", "fileCount": 12, "integrity": "sha512-4krF8scpejhaOgqzBEcGM7yDIEfi0/8+8zDRZhNZZ2kjmHJ4hv3zCbQWxoJGz1iw5U0Jl0nma13xzHXcncMavQ==", "signatures": [{"sig": "MEYCIQCIJ8pmikvGeN6irHvCtHjPzSFL8OsGlzu++g11EcJUtgIhALNTvZ8Dme0oXLKyQF+sXz3T/ra7gDolroKLjEPpvOwm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLKj/CRA9TVsSAnZWagAAMbAP/0g8SAYSi20BDDGjSuw4\noNwKH5PeM4GkxOyEvI+dvrhx7I9TuH+dbBmXf/776MflvSeiZ+nzh/q7EP8o\noFTAvh/jg5o8tKbpMSZaQwMG1Zni18qt8dpb9hY39kN+/3UEPbguMEqcIJsD\nNlg8vc/QnlcvV8L1XxTr7d8x/2+lXhEJygG6+JIhDJvl58SzoCuJKvyDFRAU\nMhQ2avaorYsyh9yR5C/9shY/I/TJk0yhTDmfdLj+M/6Fc4u6XO0uSv5CJaG4\nOoB3xgg/dVJR9eNPyCDjyIaLvTH+VPjZOwwpcy1MmIJpztCa4XcZ1TBtha4a\nsOGRrik8OmawEorbOmFAeXxyhUTtTWqBq2EuaiCzBZl6niRZLY/6jWKExXPM\ngmBvKwqm2M7Vnas37Ab9Ob/Acwaq39oKs4gubutex1xJw7H2uAPNe1XlWIVV\njw61DmK278xzQXNQvCHS7HQp/sG0ma8tFz3XT8S4QM9jLdsLHjjwYUvCnv9p\nlnbdOL/fARL4ZkzzBW8RHo44o8kdDhHO/t7VqOn9hSb/s95tqJIwhWGw/XMk\nudBtWcGEAfg7mt1EDsSkwCNNhPESC54O/reBW04FrXRnWaBa8xm/XLxQmIC/\ns8yJhstov1IiIHXGx/TwSKPCHHlfPGYQrlnjgiYrIJqV6Jxti267xw+kRluD\neT4g\r\n=O9KF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "9dd9aa322c893e5e0b3f1609b1126314ccf37bbb", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "9.4.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.9.3_1529653503252_0.6289690602918909", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "tslib", "version": "1.10.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.10.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "http://typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "c3c19f95973fb0a62973fb09d90d961ee43e5c8a", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.10.0.tgz", "fileCount": 9, "integrity": "sha512-qOebF53frne81cf0S9B41ByenJ3/IuH8yJKngAX35CmiZySA0khhkovshKK+jGCaMnVomla7gVlIcc3EvKPbTQ==", "signatures": [{"sig": "MEUCIQCqRi7yaGX+pNrBpSaksGomKLY5FPzBNCsukTybmCggXwIgB3WZQnWGFy6ZB7HqzrUE3kTc/FGvv3KTCycRv55+mC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/tnbCRA9TVsSAnZWagAAbx4P/2qh+Bkug5hngdttGPKu\nvOo3zVChx4lFykr1SBqE20AIlN+QI1NhVjlWgfhcsPJFXTuNRZR33ZsZ0ItY\nFz5St8fQw293LDW8iWS/m8eIF1UmrI0h6Udtk4zgpJPt0Y40EWjATbWPpkGB\nZpdTAjcEDJnDJANdR+hK1B4Ln+0ZLKVx+Ar6oRIk/JCdzSTHWXmZIyP6gGGi\nA7ppCZqPLs/QhB1OQNVR1lpMTTw0FX5dgTTufrfb4Srl1/yOi2epzzNA9eu4\nLN1RKiyelXmFsgQawoZoo+cSAHGuPQpv1K3dJ6F25jpIgBJHGkAHgEmydtAb\nlNWUSXokcHgL2t/DT8gICCF5Z9bmdpNljiCC/B4bq1SFpuFgKQIbWrhNCFy2\n5rRQgHFK848W8ZfsZVhdKUktd/7T4D/tFBT9TEcLHgY/1yqmOQAPL4AtUqSy\nXe5rhLMze33nl7eh1mVPQpWQlrtzGq+0GRgkwfQVTRRnStXMWIqRM3FgXrqO\ni/yy+HVrfTE71sqaZGgul8vfbDo6m70n0ca2Cpo02UJjC7oVzDH82aHwgOTf\nvWoYgU5zewKcu/Jxi5vhn3TYlkb9UQyNIur/VmMl3NZ1dceQnaqdv1Z+1UX6\nMfmhF7LL1HllVVO2u8ZqiKNoVppAHt4aJuRNcor/uth/Y2dHLTI/eWG0/9WD\nA9bw\r\n=A20l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "e1aae12c74c57200f72a7f9cfb53321e0c43b616", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "_nodeVersion": "11.15.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.10.0_1560205786771_0.9231548154121492", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "tslib", "version": "1.11.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.11.0", "maintainers": [{"name": "typescript", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "f1f3528301621a53220d58373ae510ff747a66bc", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.11.0.tgz", "fileCount": 10, "integrity": "sha512-BmndXUtiTn/VDDrJzQE7Mm22Ix3PxgLltW9bSNLoeCY31gnG2OPx0QqJnuc9oMIKioYrz487i6K9o4Pdn0j+Kg==", "signatures": [{"sig": "MEYCIQDRirK+glW7Nwl1KtwfHHan3FkG91Fb4RlUZjutgh8j1AIhAL5ygpdtKk5h9B2kGmp4pyE6djHcS+F/piXH7BVonGwK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTvksCRA9TVsSAnZWagAAZzwQAJ92ZNqhoMLLNyxo1Q6J\nLJdrFiMVLDrKMWmyPz90tYq//9lHUpgn1qzoKNs5paoYEBDHltndy6+dXG1U\n7BsKg/GKDstgR2ZM025I7pdRsBt9nvrpMwNItU6PaQ8bnVAZKhYLm7kHGWW3\nqDA7/aardQNGRHCVpHbXUnmz2W/T0B1bRxDSKgMceIZ1V8KOGNFH+lRQ2g26\nJ7ZFPB9D7VqUOe48JtuLXGmwq0FEA+XD1H6iUQ3vOpUCTl2K8cJFg4K/oVZe\np4VB59TeUg8VqxPe+ODYnPwATY9acp1JYOVQwIOyy6HL9ReyHpWchW2KTBCP\nhTUkqsaPld/Hqv1fTGAHmZ33a5zJZHOZ/aUr/W6NcaQA7HAu+4JTVbBFwMZl\nQ7T+FCQ7O1fwQ7HZ4BcDq0Bg269+TXDlz+8nIpU1HZGJ5Kbbtbex6IPUBQe1\nsXN4ZcH6FWem41r4Yc8KB6ntusf6qPUlVwUez/jxnWAxM+IjdhNEMAcMCjCX\nd+Z7FhS2BKACGnc910ld1Gbr8JEgDib2dSR0om3/SF056RAL+9OgMsRmV7iH\nCYMzVjWB/cdgglXVL+NZsxT7lgpxSUNUXpNRneQdHAXf3ZfhA2xhW+w4JYFy\nZ+3+w6bm2HukLN91tE3NerLwOS+rYvloGgDhD7KjIyLoXEnIf6iFe51sLiYL\nmLDe\r\n=r4E+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "19d1e0a336c8826551956310ecc7dd9fa06511ad", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.11.0_1582233900236_0.17706924280555403", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "tslib", "version": "1.11.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "Apache-2.0", "_id": "tslib@1.11.1", "maintainers": [{"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "eb15d128827fbee2841549e171f45ed338ac7e35", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.11.1.tgz", "fileCount": 9, "integrity": "sha512-aZW88SY8kQbU7gpV19lN24LtXh/yD4ZZg6qieAJDDg+YBsJcSmLGK9QpnUjAKVG/xefmvJGd1WUmfpT/g6AJGA==", "signatures": [{"sig": "MEQCICd8O1fRIeMdnPoKsyNmI0/CneLNgApVEyxbjV0wzxe+AiA9TRTLzk5gMSfNUz09rQXRrznMzAfxubdRzdYcunn9+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWBlHCRA9TVsSAnZWagAAUs8P/0iuCfG3FBBkBKnELE07\n8P91jap3siRIuB6fHXxCxlRjmL5QhKOhgpyN5K5AtkLWbszu5Vzgyvaa1w+u\n2nmRJHxsxMse9QqpknFrUOI1hfjQ4lbe2+xCVw+WGp1psa4GL86vvBjdAATa\n8TkaST7X8lYT7bevEj9Aa31Zt/XbIbQgT3GufbEieutwIjnYlbmdU4o5wQ3b\nMypdTQR9WT6QxhMMp2cJK83nGs8gq2qJsz5lexQ/HpN4yzbeaqvBjs2Luyfb\n0LcV1zTrpPjryDlY8m+d+YR73Or8XUtXF/EPgePt3KVRufrI722JTaG/V4Dy\nXTgYFsrIlIxjOivCXpBIZxOtQ/RvWqvzY1rm1q9mYjyjVsggqctzpJaGb2Jd\nIbsV9+NiT/VnhTvRDrncX3Cskja6if1Yl0fSh+ejNSEWwE1MVFMKUVQlD9zN\nu7GxbVrrfy87yiXOkPVvPcEyciWMd6po8E61YG5mKPH0tqGMz8FSZ90axgVI\n1n9jH8KDsA/3d1NwycNKQIdyaVeG3ompHTemKRpI2tuu5SxEK6aYbeA4ZNU9\nEoGBq7AwXc4vfDjjEmkbr3mdS2iJqbEeoMYcrrK/2VLUKAAQDEGRVXeZ9LIj\nzMG9/RyAHdLsNIBz1pna8Eou7wI79O6E/uPfOgIGbtISYRX/0dKuaemYRRFm\nUfMR\r\n=3G+p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "c1f87f79190d61e1e4ca24af03894771cdf1aef9", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.11.1_1582831943365_0.2501874944980056", "host": "s3://npm-registry-packages"}}, "1.11.2": {"name": "tslib", "version": "1.11.2", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@1.11.2", "maintainers": [{"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "9c79d83272c9a7aaf166f73915c9667ecdde3cc9", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.11.2.tgz", "fileCount": 9, "integrity": "sha512-tTSkux6IGPnUGUd1XAZHcpu85MOkIl5zX49pO+jfsie3eP0B6pyhOlLXm3cAC6T7s+euSDDUUV+Acop5WmtkVg==", "signatures": [{"sig": "MEQCIETx7Td8gn9UBxNXAnDk4vZDW6oA/qbX8aDjSlnacM0MAiBk59rWvp9huVzcUqiand8RM7135h+9+9bMUf9d2Yy6nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesfVTCRA9TVsSAnZWagAATHkP/j8QxfRxcsDPnlfY+jMG\nUPUdVsqAtu5mUQ2FjUrJM+RMjYv1c6uWGreeThcu3mIRBJTexWruYtHsEexr\nBOSPknV18SKXMvLfVfquyLtA1eLKG7VUVNQ6Vtn6L2VFmVNI/8m0YWLlMT2m\nAAedVx0IlwhPsQlJXYW6Q+3npbtqosujqY0idwDgTJKiyPeQivtqlW7FBbWI\nzx5IE7Vp6poS+qKcum6/cl1cRYRaTxrxQbOBCpd3fi1eE7jGqzAiGktNJHeG\nzrVmR/oTukDbU8u2IWXk2sPmSXaKAm36lWnuapTWrn8X17SXVVGlOxanpvT1\nxNFuVt+qt7GhWY43g72nfxhbcRmJhmfOiJP/hrD2JZ1GcBeHOtopzLgEfncb\n5EzlqcXiRYkAlQ69JFe56jk5GLr2YNuJ64CDkHo8FpJKYsrZwPA/oCKpyPaS\na+gAXvZVcjO1w9n0gCMiER2COakFwPrk0a54uksLT+T1IXqD7jZ04/s3Rt7s\nj9pNnPSX1I8Nq1v2IRZvW1WnThQBLsq0HaiCcN0Of8nNe6eTyvaqoF1NXO/H\ndfXWSF95rDytqVTHbhYzMaF9kLtL/nz2k1L1sG/jTce9ir9MOcznCO35owDc\nQsmnoxoqomGVcQtcGzwqSJL56F0mI6uBkjFcqlIISiTAb1IZfHkkBejSPehB\nVfto\r\n=aGAO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "3d93421d7be470d614f527ff207d47559c859510", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "12.16.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.11.2_1588720978820_0.06876526908743164", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "tslib", "version": "1.12.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@1.12.0", "maintainers": [{"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "d1fc9cacd06a1456c62f2902b361573e83d66473", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.12.0.tgz", "fileCount": 9, "integrity": "sha512-5rxCQkP0kytf4H1T4xz1imjxaUUPMvc5aWp0rJ/VMIN7ClRiH1FwFvBt8wOeMasp/epeUnmSW6CixSIePtiLqA==", "signatures": [{"sig": "MEUCIQCJkwoqBRZK7wKZiQqluLgSJt2JKH8JtDsyRFpHtUGxmwIgQ3o1VAEd/0hoodZrobmc6gRRfDhJb0rSNOcqPgShQ9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuuX/CRA9TVsSAnZWagAAOhMP/3nXRBheGfDW9QqWJE5r\nTpEiFUKJbzMEHiu6jEIJtQ6dPVT50YlfrhYlBkPgr7UCTnhrdfAqbnaTbG3r\nN0QuZhiEr7ZIXkmXoQTtXjWuZGVo4ywTn4bhIlPm+qlczIOxZVw89+5EFCOk\nYtqeiEy4GX78/MQJ2Rrr/I9IWVZqt//jBEqy6NVLMguN0S5uF13mSIQu3qyL\nhQm9RjiSEBIuFJxDxOB1ivqRKPTGPmELqL5TVE8UwJyyQkH6XCGG4yVPbtXf\nAg1hTtVjuTPFcaBkD0o7uqcbVfxLp5JEMjJ4t+14fBNu2u8FuypgbIlTXZVS\nzVahrgj2zFxH8oD0FP4lIuJeujW1hhZvY1JlrvdNIMZK4bT0iUJ5xpDp/unb\n+ReXwD8bxCoGXaXEBVkCw3NkKVQ6R3Wmvz6Ia1TNyXtA8n8NgAGG3Xc+R8nv\n3hjtvED59EL+LhcfEaVYiHrpwJI5ly0qCpljBU+48pip62YyXZVIxtLD5Vd5\nse4zuyGzsGZTX9HxrtoDKdqn6xD0PNSzM2juzAPuvFwOkHpEUHxZGg+S3hkG\ngBabzoEyfsJdnPfHJf5LhjNkLvyA0FJPRKna8rp7NGlmhhQXA5a7Ag30FB9+\nWO6QdsT85Eq/Yj6n2/My4C4Uxx8JWEuBXxR7/0XX4Rd56aUiLeiEX2T7yI7g\niJg9\r\n=DL+7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "3a2181005919fe65dc68b3407937eea60e04e753", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "deprecated": "TSLib 1.12.0 accidentally contained a breaking change and is not compatible with the output produced by TypeScript versions 3.8 or earlier - use tslib 1.13.0 for TS <=3.8, or TSLib 2.0.0 for TS >=3.9", "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "12.16.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.12.0_1589306878749_0.7705174932204684", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "tslib", "version": "1.13.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@1.13.0", "maintainers": [{"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "c881e13cc7015894ed914862d276436fa9a47043", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.13.0.tgz", "fileCount": 9, "integrity": "sha512-i/6DQjL8Xf3be4K/E6Wgpekn5Qasl1usyw++dAA35Ue5orEn65VIxOA+YvNNl9HV3qv70T7CNwjODHZrLwvd1Q==", "signatures": [{"sig": "MEQCIDteAuIQTgEksCnkEbYpAxRFvu47qTrYLdowPsMxNxmwAiA4+jWHJ1jhDQ8u6M3zfxsvEcbkBFH9Bx08P5KpJUse+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevHsDCRA9TVsSAnZWagAAukUP/2swkL6F1lc/Wit/U28y\npChpPLWk+LC8OoLfTuOQ+6p9VEnjAqgx4HaKXAT8HkBbL9eFz3InxU2PrYDl\npIY4jUTxXU4bPwajFGOyvZOdAkxg9YPNNyu2EJFxsp4XUB/5ujm/8oCCkzyQ\nK4Puv7Le9+INFZDtIKvaB/6h46OcQ8+hr6WSnoY8Les02dc0yyEG+rLjmVqC\n05YW6rAulZ37POBwmOc8oidKZWTgBALE9NPrLVYKUGfnT0Bs4COnDaErHCEX\nHC7NtZChO1lmhoDJP/6i50rXoiWz02LVm9IWTpefpzgS7/Wx61vGLuVftHLc\nlDp4ftafWysf6x48Z+xma+Moh08a+UIN3VbgWqTUY1FcUUJEvpA7iMs9bx4e\nRy6h0QJogddIUZr+2vaPce8XHcB1oDonr8hVCTcyUTL1oC+3fEw8RJRUKgC1\nLb26Eo4JOhgXDQlubQ9gx24QQMCl116kLZAQUp4j5lzDIwZrMvRCCe0HgAcc\na4BVT86/xvEy/CEKenMZsV7z1ks+I6OP0pY3SwmJ5dcaYsz2bv5K4UKNf8nO\niPHCwSPPSuog8+vhnayctN5l7/SgLJI1x5hqgWIT7/audDy8e4pCH5qb372J\nvXqGiMmcdt4tYbtYCsZh03Mi8nYw7ap4OYn2JliZcGAFBiiuqKkUSCfw7bcM\nDTRf\r\n=TghQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "b7e9c51bf7fb01865e8eec136fc30098c6144bb8", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "12.16.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.13.0_1589410563138_0.2783540343901998", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "tslib", "version": "2.0.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.0.0", "maintainers": [{"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "18d13fc2dce04051e20f074cc8387fd8089ce4f3", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.0.0.tgz", "fileCount": 9, "integrity": "sha512-lTqkx847PI7xEDYJntxZH89L2/aXInsyF2luSafe/+0fHOMjlBNXdH6th7f70qxLDhul7KZK0zC8V5ZIyHl0/g==", "signatures": [{"sig": "MEUCIFZXucaqFDvPhTG88wyXgsXy6eeYkDqTbt3moRY6hxu/AiEA8lOgrxF9jFB05Y8i2Rwq0Dk85/tLp1qXl/byrAOUuXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevH3+CRA9TVsSAnZWagAALXsP/38jJZqlScB81cw5aaiF\ntEWxNoGXTwlQTesCZuCivHhMur8SpjyNGJtYAHgwY8ZjXT90jnZJ/nI9j+5b\n+uqqz1BS7qmiM/sxT/eBld/0IHQM4fOYKyoqOIx8pqaaNZt4C2EFrtiEr6BW\nTydPdwPX24qvzaN4vd7kN0icq56p6MLFdoZB1QGc6+bdK8rAxVyfd/2sQOFO\n9LL5YEd3RWu/kOuWvVrdrGSd5fUzagO1FGwoo38LaeajgzJXtT/4Cxarckae\ncM+sNYN9jZiBLXEjZOkSUdq+A4JWozF+KFbF66gGTTx5BZCNaGmvKgw7I4bx\n2hOa0g9GM2JtkEaIq6A8S5tAdEKruFrJ7VFGPEcSonkL89SfWTq8HhKIxnNR\nEQmDtyVcocGcGxIDyRxSRv9rEzumhUvgGT5buXWmxUrkaBnA1p4YpYbggNQm\nYcdJaltuuZilIJ6t6dI/PeqRLEvWpOCvKS/rRYupbppUd8gsej8FeH7kZFXB\nbUMh8+kscmYMC6dHv4rYlUeG6w0Z+S8AYd9rVq43fRvtSYw8EK4KlPkuNfcK\nboVQwSSISN8zxB7rPNSZHYcAfO1aZ9wxqBXKKDjli5Md0SDmXB1AbmTrAocJ\nE+v+ApAiLd4XjY6q1a4LanaJRQxng7QT467TRsxSa8DMSyBP8Xc5K17P+ER3\nLU4X\r\n=+5z1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "2d120146f6873a0732753a49dc39a211bbd466da", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "12.16.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.0.0_1589411325544_0.5510561469423085", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "tslib", "version": "2.0.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.0.1", "maintainers": [{"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "410eb0d113e5b6356490eec749603725b021b43e", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.0.1.tgz", "fileCount": 9, "integrity": "sha512-SgIkNheinmEBgx1IUNirK0TUD4X9yjjBRTqqjggWCU3pUEqIk3/Uwl3yRixYKT6WjQuGiwDv4NomL3wqRCj+CQ==", "signatures": [{"sig": "MEQCIFuBKOL2zixnh/ASD8EmUupcXmJBT7F3yuSV5n3NiHqQAiAG1vglUXT/DAtZm9CmduLAdL42FsMBpJYb5cicnT4XuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLIWlCRA9TVsSAnZWagAAD+oP/R5epFC8axJ/hUEwS903\nhhl+aUChoXbi1Wq95lJ5jQBJHdRi3FfRB6B8S7FdF7Xt/tlZ4Cshfocw4Iup\n4Mk9/Gx4wzJGonVb7gWExM96Z7a+iM4edjytVZJKS6ch8UbULHIvYPTsNBVm\nZI2jGada0uDQyrL55m0/THn+k9hIVJK/bkoFZ84E3GsF9TIpNibncj13fYf7\ncHsLYxQCfEWt43i5KYngYLTey9e8JYCbGmaGhjrNB791ZywGwppsPWH9DcZE\n0QIn6n1YfGOIq1qtZYpY3+Yw12eqJ+GEyNviSut+5EGc8MFVGT9g8WxIQlbg\nQIAH5T32qhY2SRhS89Z7IueKMaxFsS2e1oWkz6HN0p1oLDXahvHg5fc9+Irl\n6tbYuUZkuZSVf/DiizzhJfgXLg82k3LdJL7/WD67kMOk9gDKhmNtB/AsR4+e\nciX1wzs/Sl94NnQDCHbcjvh/2cWt9M1h42YRDUkla0P2A+HfXvW0PMER2C03\nFlY0IFHx/I5QjTwcb7lAMuT8ieDKX3gEkPs4yhQ1ecBQb4bFO36DGe4sEEsL\nrTx3SuccFtKudSR9fKfs66B+efDBocpKkUEUOT2masR8zYe29Qv44Y7JSXfi\nkFjycxIuOLv1Wv5vmts8NPNJ0bZismx4oWufb7wfl22ExNgyA0GTcm+CaQm9\nP9il\r\n=OBxr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "gitHead": "a64b24a1fb618c892809c38861aa55b4c161e632", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "12.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.0.1_1596753316653_0.32792054818011573", "host": "s3://npm-registry-packages"}}, "1.14.0": {"name": "tslib", "version": "1.14.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@1.14.0", "maintainers": [{"name": "typescript-deploys", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "typescript-bot", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "d624983f3e2c5e0b55307c3dd6c86acd737622c6", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.14.0.tgz", "fileCount": 13, "integrity": "sha512-+Zw5lu0D9tvBMjGP8LpvMb0u2WW2QV3y+D8mO6J+cNzCYIN4sVy43Bf9vl92nqFahutN0I8zHa7cc4vihIshnw==", "signatures": [{"sig": "MEYCIQDxW/c+hQCUDErC2xWBIeGVrmbrSN5iNIWukV8aoyA/aAIhAJVlGpsXDWqBfG78v4Tvt4KEBCGc67xu9a9t1W9LS+H+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffI7zCRA9TVsSAnZWagAAh2wP/1y9PuNFCyR1SMEuZpSn\n+JOnJnjQtZ3dPoeWJL07LLJg0VX2cYjIgSZI5H0P04uI57BQYRvL43FCSXlg\nMD4PYsV5LGmmdgoagrvbvVXq84hHYNlgvpm83fpD/8V9IYjWs32+nJcpCew8\nT6QWm0dpIKpE8itUVTW9qxQd2/9dD57NmUwdYrJ1lzD9Jt5Y0h6mq+cFFCri\n5E1DYPRG4LWZtIWeFtm6dbMTJZK33Cx6jjZ6Hv5lONKRFumf5Lcyb+jzk/oZ\nkLmtQgWIlvTGMZ1dTYOUFR7qKHk1gEdIEgD7Ni8ESDWuoMNC8uerM7h73gh8\nrknCntN+6DbYFekToYdWaZHH/iGSjXvoFyKmrJHLkUwCUs6uBSxLNknCRAGs\n/I8Yef1DugIs7T9XBCGTDnYYDHmroPJLq9mpOA52wW+yvep4L4OmOvfAcs/P\nnh0F7ttsZMLWXP4Ntx7gAlQR57LIwtRSeKpJDqQW/D7EuC9NHMvJQJXZ86MM\na5UdiKX0vHya9LP9OxKhjRXy+1sTDL7FH75UzJhOpH1dkT08SchdgwOlGmiW\nvUMTJXKKFbbttr/cQbQMBFGHO4uzjQNBY0HQ/JfrmMflXmqhXx7DydruL2sm\nLsD5GX3lj0S88E1bSyJ/1QbiFrCcvH4t2YyRf6PmfzYj7qirFO2U5ClJSrJ7\nZiw5\r\n=5jYB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "default": "./tslib.js"}, "./": "./"}, "gitHead": "abb109f2949936a708844026c66e293fe0a76268", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.14.0_1601998579086_0.4729021367497501", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "tslib", "version": "2.0.2", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.0.2", "maintainers": [{"name": "typescript-deploys", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "typescript-bot", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "462295631185db44b21b1ea3615b63cd1c038242", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.0.2.tgz", "fileCount": 11, "integrity": "sha512-wAH28hcEKwna96/UacuWaVspVLkg4x1aDM9JlzqaQTOFczCktkVAb5fmXChgandR1EraDPs2w8P+ozM+oafwxg==", "signatures": [{"sig": "MEUCID6WlaldU8Zx5sH9jl+vK/5KQrzsRvFYsVMiLpsV29SfAiEAvEH3rR9hiMIbXKr/wb+ZKE4SRngOhspztW5rt+O227g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffI+kCRA9TVsSAnZWagAAuEQP/2SGGaTDhXidzcfZiGZg\nq9F8OG1dM/dtsRjIHJtKxN4UuPE7qPCBT1uCMfe5hJEL1CWqiccL5Z+95fNL\nob1V60pl5z3jcllvLWwWqSiEB8TV+vM2iSiLz2a81GYG9dYuzQCZfhs5xU8N\nvWJ+OWxmz8wZAeh6CgS6HvRI7F5O9Rv58uD+qVg2rATgqLz6b6SPRR2oA3EZ\nIPLMs52e6L8MtCqleLmmAGp8LkYK4w3O2V8Vz3N66mZo8KNR/ATVRItL9GNv\nKgaptC9RWXOhtiLwro4PGclxVUZUWNh7O+D2kSuamYqe7ebA/nQ3kv7u1rIZ\nOdkuhKaC+QoJeifd0w8yl1stfFCy51sfbW2zUb5mrM0pDARK2L4WhEAs33RM\n0KumJvNqQe5FO5JL1vvY7RVnnGxiI+yg1ge61F4f7QZkEDA4SKc55I6WpLZx\nk4ovJFxFofleQCCCyY8wl1H30H97pm+9+xYGVbozvmRV5KYxZrpotdP0fvJz\nSGobBlElGQXiMD34FiXWSdMSa/j6EmaICYRMtbog2NJ0s7WRUwMDS6yQhOM7\nRcGYMKSTijvMBbmGw7KTBAAbrNAVBH2tovpAm7jfl5N1B5vF0ml3Zyr8Cjhx\nQiHS0GXe7d+q+VxX+/wqC/QmDzUV9oVvyzaq4kXyKOn1xWk+6/g8dNsdehMF\n7Ef8\r\n=jdSg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "default": "./tslib.js"}, "./": "./"}, "gitHead": "4dc5f8280a00c3ffeb959f47d876f9a98c1dba68", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.0.2_1601998755770_0.17564797146786404", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "tslib", "version": "1.14.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@1.14.1", "maintainers": [{"name": "typescript-deploys", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "typescript-bot", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "cf2d38bdc34a134bcaf1091c41f6619e2f672d00", "tarball": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "fileCount": 13, "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "signatures": [{"sig": "MEQCICJ+rIeTgN1CLrZXY920+5lKQULE2UQXujVPhSHnG5PyAiBFabCCIBlAeViOH29jrzGZzYODwmRHtu1LGxe5/T69Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgPOTCRA9TVsSAnZWagAAS3kP/0b5NPdsXvP1WMO/MpQT\nIooH5pZBih1FwG+OJvxqZAuVZPbU8NHYu4V2B8JMgVfhCKq6x86PdZEDJYDI\nKD1k3C/E3V+YOB7RDfVCrlwBPfYAGYT6Ov4ZZrBSw3Bpcc3OR0GffrsYvgiC\nB7chTtqWNc7Od5tuJWNvodewWZF50IONTYOvSwf2JfS1PofEI4VUGj57q/5y\nD63p3WBx4rvJ9Klb77YNeoOXER7kxlUH90BA6eDtPHD/ICD21QXp5I/lyNJD\n+EK9Al7qijwc5sqz4H28uHXs0w6f47pc3cYg5XePFK84TuP4adEgGGp3/TGG\nMT2aQ7KGOwQpQhVWXLBIrGo+3cXEQzSw7oUmv15lQxS46YqGZB/EkY7rwpy4\nmb4ZDCcnfG8bKXVLsbQ9rMcFYAZIf+rD4sIMaA6A7CHTej8pFv/AFAKn2Ef2\nqYl1K5qeVK744YCdQ5DIWBDz4SbWS2yY4cb3PIaas8CITEy1/qNAIaddirZV\nnYX9RSkTAn1bRm09LopWoakCYb/yFZV6JiML9drQU7WWMoFDS1q+9iSq3o2o\nwHsuH0CMzF9QveuveeQKZ+r2QCQQw+HkNfWt4p9EjZNRaeL01sRznoI/LVpu\n3IfTE5kMENTtqo8WNev9E0GA2XnBPmjaKUGWAUi/0TAU2MPdmAClzLw9y0Jy\n+Dts\r\n=g9K8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "module": "./tslib.es6.js", "default": "./tslib.js"}, "./": "./"}, "gitHead": "7d33f3607d635dbaaaba006a4648346a81422f3a", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_1.14.1_1602286482361_0.12401493654195783", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "tslib", "version": "2.0.3", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.0.3", "maintainers": [{"name": "typescript-deploys", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "typescript-bot", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "8e0741ac45fc0c226e58a17bfc3e64b9bc6ca61c", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.0.3.tgz", "fileCount": 11, "integrity": "sha512-uZtkfKblCEQtZKBF6EBXVZeQNl82yqtDQdv+eck8u7tdPxjLu2/lp5/uPW+um2tpuxINHWy3GhiccY7QgEaVHQ==", "signatures": [{"sig": "MEQCIH6Iv33ot0gVo4BJHL4mgD5Ix4xA5417KXiX4ix/anm2AiBUoXRZLB9vbhTwWfvcpiLnpIMK/z9aMLPz5YjCEw0JMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgPPPCRA9TVsSAnZWagAAlIEQAKGrvmQRjE+79HgmPKP4\nZWYTMEpY2JEpq/b75vVoAZzfopr5d0JXrlpb2w+iuUsVsAlf0IVyrxq8TW0R\ngbTAK9/T4yuMnWg2J45GXYNGiYwyuVtsTHuw/ltC80L1rt+awozwFriegUt8\nqWphTJbJiPcMBwhPL9iKFHNoIUoi+wnaIS/ucdnfMj8QU2NTiiR2KIupG7be\nzPAc4R/Gfdl+vPcIJnxaVzRfex/M3uxwsivo1xGDHdqfmcgIOd1e+NkWLNCx\nxX9w1xyXivLXI7n/B+E5b8MFZjBENTzL8p6R9/hHiAcFm9FibNcAAvPi3Xvg\n0ZJsbbJSdMkfDQgedT+ya2OaWlZvq6uxQb9Ban3Tk0YqLWFo7D9X9DTliVnH\ngLR8vuzh0dbeUcuvFd3I/Ywfcs5vegzjxkDDUsMyNBu1sLWzK+iHpP0O0vLY\n8IfZGdL0kbeM5dRCZGRcEPWf9sDrteoi1NOGhMu8ggR9ZGqsWWxBqkZSNHAF\n3lesr/stEgeyJ4urEluBCrqjTQC9v9McPuU9nLmXOfHy7Wl9R8MsxHCsWpfp\nRyHcFZnyvQ8DlujveMkQUEVvXYMXYxMDeKIF840QSTZwv9+29e5c51rsnsGZ\nbDAHZYiK7qvgJ+HOdAuv+Kb7jsd+W0rbHaJA1MxtPf6jXKJa9AIzq6nahIZz\nLlxQ\r\n=+Ir4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "module": "./tslib.es6.js", "default": "./tslib.js"}, "./": "./"}, "gitHead": "76bedfddc2cad38aa11060a32f56e9484c279c98", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.0.3_1602286542804_0.2490824255311792", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "tslib", "version": "2.1.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.1.0", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "da60860f1c2ecaa5703ab7d39bc05b6bf988b97a", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.1.0.tgz", "fileCount": 11, "integrity": "sha512-hcVC3wYEziELGGmEEXue7D75zbwIIVUMWAVbHItGPx0ziyXxrOMQx4rQEVEV45Ut/1IotuEvwqPopzIOkDMf0A==", "signatures": [{"sig": "MEQCIAUiV5EH6R0yTk5rAOGEKylSm2ICcST/JRV1rBZ2jN5GAiBgOKHvjCrWXVHOLbkv6EgHBMMMUSMuh/pAdBTdvrnJww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9PYtCRA9TVsSAnZWagAAnqcP/3Mun2N3x2aEZGq6rx+T\ntkpx9e6JbQ/l9SukB+gV9bzYO48cLeNNf8twbDWdqwNjZqAXZ4dT3fZ/80o1\nI248llQaEMC0fGSFkII0SCptzWxc9rC1zOgC0jhPZmRbjYYFf+Z7WEQVKKgl\ntpHm5GahJIgzQf/ARaAYnxisGP0emNsOfx2jIqLUXXXjGUhpI7sdS92xs6Qt\niHOa8PidtHkeNOFLVMoWFYIH0MuuaDkrZGiByMTyhJbp/3LOZqSMP4+hgJd6\n5bPD5pUU/VD66L42br0dpzbr7peyEew9Kd/ghlZXmd2YQILNnULl3XIbm/eS\nVh+qWs6/Cfl5WdMQwltAtrg6L2MTZvcTP4aKwqZTs57l3ZDkqST6MR73EOUR\noh/LTYQGFCs1bi0IlcsZ/SMvr84Us7zeajGH7ayUx85ex0huPZGdCiR7reOM\njr2lgNK2VT/2Mrhm8RQQJpw4uyf0++Gom8MZf6m5WiZ6zye3pJ36nEmc+QAp\nW7u2mwQQ5dcSiw1ojvX9iv6ueLZZy3CeMBObGl+i4UKgV/fEkjMlk8SaE9AK\noKDixJ6o/MC2ZTIoxv43yTZdzv9gS9WiOttPorXq3LdpxsSOaBXze3maBwDR\nLyZftSHxckQB4B9KAou3BV0f8p4NeuYpw1hav8MxkrnBRfdKcQ+IXlR5M1yv\nENba\r\n=ByXL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "module": "./tslib.es6.js", "default": "./tslib.js"}, "./": "./"}, "gitHead": "54a056a7e501b5e1978fe1cb5b743bb0f69a2a74", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.1.0_1609889325216_0.41964274895110987", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "tslib", "version": "2.2.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.2.0", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "fb2c475977e35e241311ede2693cee1ec6698f5c", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.2.0.tgz", "fileCount": 11, "integrity": "sha512-gS9GVHRU+RGn5KQM2rllAlR3dU6m7AcpJKdtH8gFvQiC4Otgk98XnmMU+nZenHt/+VhnBPWwgrJsyrdcw6i23w==", "signatures": [{"sig": "MEUCIBFcJcdghQaJBzmrCsZC8YJlXDrBIKVvl75W680Zme5/AiEAomhq5Ihoa7u+epO/pnqkLh4ZEeDc7W6IkV2hMvy9WFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJga1UiCRA9TVsSAnZWagAAvi8QAKUBT7hCrlN+CI47eDyV\ntcV/r/FifcMpNpwDI1IkOB7fl3KQRoUqILZGQvk99JUxteu3FIXh4+HPU4it\nEvcntOoskRT8x0Pt+C0DYXOIgsWOYK47OLmV7C4nlRybMdN2fBcJq0oPvTDz\nUlXc4ThdDKv8Wt4KpFTZ/B2iVKEt+T60V7L9PcQOOcfFQprZs0jW6UHDlLzY\nH+rDCj6xrbOlrmjldH57MayHW4gJySbmGXUmxKme4N2r+YmaTBjzf2FOm3nz\ni+IoiBFrfm+wlTZQaMc6gQcT18C0zGCVJqKd5+q1Ii4TK2PP0pt/LYCgA/HU\nOaNqg1oyGw6/5sx88/Jz9I/99LiESi0qEZJfPIslnOSDCtRPfvre3XTkpi6+\ny+gH+1m/Go1V3gcO86VdCn7IDcWAT5gLQOtFQx4zcdj57jnTKLn6fAhRA/ET\n8+KMoilMDlmXBeO09cIBYVqoAs84pYnv9sZgbojBoYertUOy/oos0moYsBgC\n5Un0R1m+33fphcEQaViJXjVzY2nAUjamANZPpVVb9SuBPD7SMsQ0104Jcpec\n0L5dAMU2pEZuvroHAsM6rX3ir4Kko1uEyVrjmF0x5+fQa0OKi8FO7J1vHkUS\nWBWzZ6ZGaNUCqx62ur/eCvgcCyDZtRbYwDYgWdzdXD1jqas8kZ9PUQy6tfrc\nwvBZ\r\n=6KHJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "module": "./tslib.es6.js", "default": "./tslib.js"}, "./": "./"}, "gitHead": "f7eea49789d7902f96802d37e674e75590f7eb66", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.2.0_1617646881540_0.11385737855017597", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "tslib", "version": "2.3.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.3.0", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "803b8cdab3e12ba581a4ca41c8839bbb0dacb09e", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.3.0.tgz", "fileCount": 11, "integrity": "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==", "signatures": [{"sig": "MEUCIAwYfY8tQGIVmMmc8jjW4x3W277ugXj1fiN23adXMNcCAiEA5hxwEaVDLKIWvwBTD7/uTBX5SOCtEwPBhTM4U/QP920=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgw+SsCRA9TVsSAnZWagAApJkP/0LHz77zNfnldjTcMiXm\neID995K3RRmhhRIGod1PcH2Wjpwhq8F9AbZfiB5ezOG+3oNSbjYepmVQmCTu\nGxuQrc0w8d7NgelSc6sB2+wVypFi6opYJUr5kXjs6qFCZdwdXWzNfWLfJlSf\nCVmO8sM9H+w1vnTW+y1WcM04CjqJRnPkMofveX4BBgVop88xVBoCquS/NlbB\n6SjMKG64YS5FhujFAzejvv+Bz3J8p71B2YARG8Z4+/tFCPPxQyIPRwWcPOgV\nFemj4VIaDaL11jAwqZ1rN8ZfMjlRke4S0zuANH45wwtEugE5Ng9cBMcl1lyC\nW+KeVrqnPzWqfPj+YN+/8MoAp9NdnG6irMwsBn9OMaEwXQUPrqdAJfrFK5MV\ns/85Ra5V+BksCQJIZI7CUgkL4l5TxnPg86v6Yl+LfXC8835bTNVj6iif4cYv\n7hprvAeBffWp3vTGrZ6/XLSKGQd39UMI/aAAgsD09Nn3T9/f68nlcSCebS4F\nF2C1MuUAeuLNlb0M750Z7qD5XQqFgpacwR2Ln2ExPeOKCWKgdiYHdEMArKT8\nyLcAI6rtpu4RYjKQPT2rzRNDEN5FzGDi+Dst4BWVM7oL/aI90+9/Dhzy+fwo\nZk88Gz1EnfaCYEjsD/Y7Ky6WQkt7F+mt8qvSJfKQQ76M/7L2/OiiFVN8X+At\ned+C\r\n=mKhC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "module": "./tslib.es6.js", "default": "./tslib.js"}, "./": "./"}, "gitHead": "0b9301459c223140c9a01b4215cbbc1639a396ca", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.3.0_1623450796708_0.6857621088451058", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "tslib", "version": "2.3.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.3.1", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "orta", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "e8a335add5ceae51aa261d32a490158ef042ef01", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.3.1.tgz", "fileCount": 11, "integrity": "sha512-77EbyPPpMz+FRFRuAFlWMtmgUWGe9UOG2Z25NqCwiIjRhOf5iKGuzSe5P2w1laq+FkRy4p+PCuVkJSGkzTEKVw==", "signatures": [{"sig": "MEYCIQDrITnBj9PdDAV+/YyXA4Tpr8Pcz+Jh4S9z5AXCMbx4jQIhANIXZLGdClVeC3np962Uy928ZAGkkH5+LqEdFTzMoJpH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFFVmCRA9TVsSAnZWagAA1mAP/R8TZNh1prc+YAuXHn+L\njQKNSkdMMeWtkcm+iPLEaSVbulYbIjm+Pe1Juu27/BX4meiOGz1cvJZ1q6tq\n9yQyCIOUs60BQijcHJ89yl+EAlqeaUy1WG/EbXUt/hLtghLDdscaWmM4O68i\ns+AuQDigbbTqN8B2jKkAqCltfW8yyN1piZ0GXs7B42etbEvruKZLbuH1FGr+\n3CSpbW66hJX4lz3kvuP0f8c+RN1sWoJSOj+jgb9hiS4ETByQ3vzGHNYU2fmm\nvYjNBTDbvIHK1A0Scsk/zcj2Rf3IXBSbI1/7BxokT48Tw1VeZQi5Et+AB4is\nJupLGmWc3xqjM6kJqJKeJ5m1u3F+35G0j2pATNp5urbzSUXN2YtVPxXAgxVf\nzU0PX4jiELciFpf+Wl+v4DQwYJbiZNWZDCahVx8+yFKGCDNmyfybvfAI9QPZ\nT6jLSEpEGCPF4kYAfaSDIG4LqiGab6GwMAea1PVLKkJxaR7irFr/4TfA082p\n7quziOh+eKiO4It9oibSVgnXKioXsB76GJqq8yP2fooLoyyCnz79hmHnFa7i\nBxdFLCJ7k2NJRi9oZc/ULOCpTMZbZKs+v9eZsPwjZK6MV5Y87gjRtKjDp5vU\nJNQ2BTMMMHUuSQ+f2p0aE2ew5/nlA1bSnJYn1RmQC4VFKClGUm1HMLcTLRi5\nqZfx\r\n=1vlT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "module": "./tslib.es6.js", "default": "./tslib.js"}, "./": "./"}, "gitHead": "251802eeddb5556f507595c624ee7792154ce9fc", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.17.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.3.1_1628722534510_0.25378985711037405", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "tslib", "version": "2.4.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.4.0", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "7cecaa7f073ce680a05847aa77be941098f36dc3", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz", "fileCount": 11, "integrity": "sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==", "signatures": [{"sig": "MEYCIQCUy/LXSOfGknkp001DaY4gdM3U1+2hsYq52Hrfty+m8AIhAKBVhZxPrBZlAvRwkagdvAEBg9fgFQr4w4TDWSrabaXJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYfXdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorjA/9GvqDJZ9uZ+k8aucbZzr1CCIFkWEbfj/u3ElfoRdPG2RvV7Mh\r\nFa3PJDnP16k1l/Yg0ySd24kBop6y+222Y3dv/Suj80Q0pjZTZVgkK80Wc1vb\r\niitH6nbkYDUL3ufGU7VSHxKr04tQA7iReKaNBnnI4REI0POBiByN6HYOW8Hm\r\nnAU1XjVw7/1X1+0KhereWyI04LfdYCouLtPCw7Aa70Ggzy1iUmIJJtP8c273\r\nYbgIG2yVEf1MJcPkFy3bnbwy8V+hRPHWl0pa07ytRn0XLAjL1B/NDQ+hx/vp\r\ncmUR3/b0/pe1mUfqj3Wms12Lkjm3pM7XI6nEDFxTcR+Qc6Chv+tFjduiQKZr\r\nZDgYbGvlP+X9MG6upVU66ppWHj15wk0HvdM0/zKRrYc1CribxWe+9oK4jrJD\r\nCMejgUj00ODT9MPGse3WI8J8+Ly0ncM28t71z1kz7XkhKPcxvfurbdqmCnmK\r\nUt740eHan4u7GnM66JI+5Unpf0COHD5D/pHYnKZZs5uHYezt7g2LhH+Yd9Rf\r\nrnDKyW/fVsxmZ6VTNM8GdVTGxT4Q3tX95db+zysnOpbDHrMqVfcxPbTRqvus\r\nPPzhx8g7UghqkRl+R7tkSBwsf+5tAkQtZPpQP19DKg8512XRx2N0fy9MsfFC\r\nF2o7Ygi0oZrlP8OYvata3kanhFwJb9wVX9A=\r\n=BXdU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "module": "./tslib.es6.js", "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "5b7da89bf0bdeb1ba102345fa4a759745f3494f5", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.4.0_1650587100791_0.9564760331658868", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "tslib", "version": "2.4.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.4.1", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "0d0bfbaac2880b91e22df0768e55be9753a5b17e", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "fileCount": 12, "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "signatures": [{"sig": "MEUCIB8EvxJelnQdNMf7xo5QRM9svuaMMA4FILo/r6QQkJ9GAiEAgkauFwvHCtAmjLWibk2U9kLYLV9Kh+fZM+T34qMKIdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYBNhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8/BAAoasHoOOtsEQtGQOCMzM54+rdsTTx8pJfWfoK5FPY2rgu0hsN\r\nwFz8CcNJ5/eXAKxrdvcdAnN7qwEJrYB2bu5UXEF7XJ1U9XEGYqsqO61w97KQ\r\nQhusICW3q6FK+wdHf2iOKJnHnwTvSdOkqJtzRxgEUSdmcsCxI/lGvXejUpwJ\r\n67mzMTDp38rEIEOSw3G8pUCJbIB02AIcYU1AZU2VkMRTan6lLB95gFAl1Fhm\r\niCmSjscRP7ZsfrXpW+AfXeUuq4GavF+iBOLMJD6YsUGcTVMNihTrQCVojZCl\r\nrwqFtHOqkOIKK2tW/q0mLB/KbhMvAG1Xh57arORiczc5JJAyZSTBScQlTRjO\r\nBt4++zDZJasBPbhewqflRvljEOldOkkXWbob3u6DmShjqOJlQQC3KknqvPOM\r\nBPkEe13BL+PDM6SNlHwAEyfWx/OGIjbRfjSeVukLsKDUFjH0Z54dUi+Nry3j\r\nfXvZQOuFO0/FchKkp3bfsJxNyE8dc90mVAseTrEplqBkU5iIG1lsRWAD7Bk4\r\nMnLGeCq+HjixorIFibYkmMA1QrjnH133P5VPh+VrSfNiWKWkjMRgrbAlyIx/\r\nF9EIytuHdAoUKPRVnTP6/jCRUCDWffHsVJ/OfiOsNsMalz7mgmOmen9j2pYJ\r\nBNvuigmNudvHLxp+tw0qlLx5+ycN03CoFTQ=\r\n=9jRT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "module": "./tslib.es6.js", "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "8acd4b30d375be8c43144b9ee0b3c98599e7a9a1", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.4.1_1667240801715_0.982447184411114", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "tslib", "version": "2.5.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.5.0", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "42bfed86f5787aeb41d031866c8f402429e0fddf", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "fileCount": 12, "integrity": "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==", "signatures": [{"sig": "MEUCIQCP+rql64rhYPeVRqKO126YbX5RGkgNqFjsjs/SsFliYgIgYynuVxAIeoYoSxL0uDIzdB9bxjkWi5Ptw7bpEzmWREA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0uUuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMTg//e+DATEAVebIfRUs2A+nH8eciYhN6YjgySjCiKcBKRT87zt98\r\nOxIlDlQi8XTh3o4mArWzfIEXUlqx1FrKa992g7pu2uHAhfC1Yqx8x+tkta2T\r\nTGN83GRA/+T5ovakqVQ6tbAs2n8OITBTtYW1WrsvtXtmTadtVxTLe/hNk2Yd\r\n7puqH5XDrJJJodAL0xwawldAj97zQxCzl7F3BmxZwGwH33aXuczTmxsqqZuv\r\n9X6SYrqQPKRXll4kjDOSudPfRf4flDF67BKIILIgqG+BpyaCW/9w16LTgK+4\r\nn47pd2jpDcuBaF6vI99rqTa/S5pQE9284pvNUPljQgm5Q4FTpHZcF4AnfO4M\r\nSzRRFZzGaIbCpbJHl8usXCOBq6vfUVKM0l+2eveZJyWu3GMHKd2j/TAVAnLZ\r\nPBpYJ8I6eaDR0xyLNlOpn2lFRKzv/Z8ocs4x7r7yVetj7Zq9E1qAJtCN2Qy2\r\nfZUfZcJ+oWcHI+Db8kvfPeBo5scSuPMvC0F4oIfnb5udD91dRWqeTnP/grRT\r\nIawZVcHK4ThpMOK9o+riWvpBk5MNansTkvT+ZJDCwjMNqBln5KFh7rOiJQ/x\r\nAgIiUWXsrn1dHK7t0iVujdp9M8DgoN/7q0MBhPIu8vWbjBFRRO5E7uFf2r7J\r\n3EVN4M0Omi757/8kxi3ot4S4dXiX3BaqFAo=\r\n=M97L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": "./modules/index.js", "module": "./tslib.es6.js", "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "e388a23287bdea2c4f411d3ddedd1c80002f76a7", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.21.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.5.0_1674765614419_0.14295187448701974", "host": "s3://npm-registry-packages"}}, "2.5.1": {"name": "tslib", "version": "2.5.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.5.1", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "f2ad78c367857d54e49a0ef9def68737e1a67b21", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.5.1.tgz", "fileCount": 13, "integrity": "sha512-KaI6gPil5m9vF7DKaoXxx1ia9fxS4qG5YveErRRVknPDXXriu5M8h48YRjB6h5ZUOKuAKlSJYb0GaDe8I39fRw==", "signatures": [{"sig": "MEUCIQCWNzkPwdMbczsq8xV1P3Vym19QQ9vqB2Zcwl0ZyUhAQQIgS7YQMLghO0uAgRniWOFyUjxke05ddxdgGKWAWrdC80A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60922}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": {"node": "./modules/index.js", "default": {"types": "./tslib.d.ts", "default": "./tslib.es6.js"}}, "module": {"types": "./tslib.d.ts", "default": "./tslib.es6.js"}, "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "91fcbd0c62f644e3527d99126bf7751cdbc372dc", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.5.1_1684363994341_0.6753643195736925", "host": "s3://npm-registry-packages"}}, "2.5.2": {"name": "tslib", "version": "2.5.2", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.5.2", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "1b6f07185c881557b0ffa84b111a0106989e8338", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.5.2.tgz", "fileCount": 13, "integrity": "sha512-5svOrSA2w3iGFDs1HibEVBGbDrAY82bFQ3HZ3ixB+88nsbsWQoKqDRb5UBYAUPEzbBn6dAp5gRNXglySbx1MlA==", "signatures": [{"sig": "MEUCIGCi9UBMfp2rHexEaWFXNJCkd55tHkzzsBXZ/teyZFTgAiEAnodWu85JPBjKqIkzqT7+dlRXa+6KzTMGobAT5atYUJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61569}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": {"node": "./modules/index.js", "default": {"types": "./tslib.d.ts", "default": "./tslib.es6.js"}}, "module": {"types": "./tslib.d.ts", "default": "./tslib.es6.js"}, "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "e623061dc031172d9e5075bdba120f4c61bd3eeb", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.5.2_1684447154867_0.7332346210496714", "host": "s3://npm-registry-packages"}}, "2.5.3": {"name": "tslib", "version": "2.5.3", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.5.3", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "24944ba2d990940e6e982c4bea147aba80209913", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.5.3.tgz", "fileCount": 14, "integrity": "sha512-mSxlJJwl3BMEQCUNnxXBU9jP4JBktcEGhURcPR6VQVlnP0FdDEsIaz0C35dXNGLyRfrATNofF0F5p2KPxQgB+w==", "signatures": [{"sig": "MEQCIBu2yXgARO6LTwPkAaxF4zWrxDzOuoqAIli0ZCZE6tPcAiBpL5+0JZ1G57vEyy/Tfln0DsqQLge39ON+ZaaFriaunQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76139}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": {"node": "./modules/index.js", "default": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}}, "module": {"types": "./tslib/modules/index.d.ts", "default": "./tslib.es6.mjs"}, "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "cc5ff034c859a04008e9de1393cb54c755939c1c", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.5.3_1685740928575_0.47360468743384665", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "tslib", "version": "2.6.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.6.0", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "b295854684dbda164e181d259a22cd779dcd7bc3", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.6.0.tgz", "fileCount": 14, "integrity": "sha512-7At1WUettjcSRHXCyYtTselblcHl9PJFFVKiCAy/bY97+BPZXSQ2wbq0P9s8tK2G7dFQfNnlJnPAiArVBVBsfA==", "signatures": [{"sig": "MEYCIQDbol43DZu9bkyfAHxRk8XoMcv48hH4usNLj+KOFJJE+wIhAN14BOiwMhPXTH0XPgr6Fi1HbvHRkw1fv6xerJy9VsWH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83488}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": {"node": "./modules/index.js", "default": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}}, "module": {"types": "./tslib/modules/index.d.ts", "default": "./tslib.es6.mjs"}, "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "7c7f3ae0f82a32c42922537c63363e8b39494e67", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.6.0_1687820861115_0.7587059769639812", "host": "s3://npm-registry-packages"}}, "2.6.1": {"name": "tslib", "version": "2.6.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.6.1", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "fd8c9a0ff42590b25703c0acb3de3d3f4ede0410", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.6.1.tgz", "fileCount": 14, "integrity": "sha512-t0hLfiEKfMUoqhG+U1oid7Pva4bbDPHYfJNiB7BiIjRkj1pyC++4N3huJfqY6aRH6VTB0rvtzQwjM4K6qpfOig==", "signatures": [{"sig": "MEQCIEo6AO8kpBczNi3Y1BYvHXDZhV43q5uk9vcZBr7csmzqAiAqz0rORDr8r4Y+6F9e1ys6Cs7og8uNSInFPdbzd/QDCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83986}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": {"node": "./modules/index.js", "default": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}}, "module": {"types": "./tslib/modules/index.d.ts", "default": "./tslib.es6.mjs"}, "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "41c120df275e4954bc74a09c8f6e517af9f622f3", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.6.1_1690230601584_0.26144405305077556", "host": "s3://npm-registry-packages"}}, "2.6.2": {"name": "tslib", "version": "2.6.2", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.6.2", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "703ac29425e7b37cd6fd456e92404d46d1f3e4ae", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "fileCount": 14, "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==", "signatures": [{"sig": "MEQCIC/X2syglPBKLW43+WYAuvHn+biBsI0IqC9r6BK1wsyZAiAkJ4sAd4AgIFoDusfaUZwlfbHLgM3mBMBQbFMo7Vb7DA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83980}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": {"node": "./modules/index.js", "default": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}}, "module": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}, "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "49ac8dc364f9d053674f9f67ac78d426e451eaa2", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.6.2_1692380518599_0.3403847500295838", "host": "s3://npm-registry-packages"}}, "2.6.3": {"name": "tslib", "version": "2.6.3", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.6.3", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "0438f810ad7a9edcde7a241c3d80db693c8cbfe0", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.6.3.tgz", "fileCount": 14, "integrity": "sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==", "signatures": [{"sig": "MEYCIQDf0y3wv/Z9LgMrbtqKwuny/yI0tIIWGuMFxETcshlg+wIhAPnBiPS5ytr6kPHGTvZ4J6MkZGsTub5NsB+SZUa384dy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84876}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": {"node": "./modules/index.js", "default": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}}, "module": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}, "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "a280d4b4e099238613d98d4f762c5f7ef896cdb1", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.6.3_1717532719595_0.2807978856469904", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "tslib", "version": "2.7.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.7.0", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "d9b40c5c40ab59e8738f297df3087bf1a2690c01", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.7.0.tgz", "fileCount": 14, "integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==", "signatures": [{"sig": "MEYCIQD4h8NGRa8U36jhJ9oHwlZsJbFUEzwHsYu/+OAFOD1m/gIhAO3WiokPXuinAVKkvs8KWNw/scqdKs6piwRy27vfv2ah", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86155}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": {"node": "./modules/index.js", "default": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}}, "module": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}, "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "6abc075910605cc5c6c74d8566190ee865c9e672", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.7.0_1724442787391_0.6968800392720169", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "tslib", "version": "2.8.0", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.8.0", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "d124c86c3c05a40a91e6fdea4021bd31d377971b", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.8.0.tgz", "fileCount": 14, "integrity": "sha512-jWVzBLplnCmoaTr13V9dYbiQ99wvZRd0vNWaDRg+aVYRcjDF3nDksxFDE/+fkXnKhpnUUkmx5pK/v8mCtLVqZA==", "signatures": [{"sig": "MEUCIQDJJ2W2WitSkFFRx+bhrHXy9UMpQ+Jy4VZNPMp7CWglngIgInEkU7SzccSggXU/JjdUeNVxjb1/kGaWpK67Z1/chgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89090}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": {"node": "./modules/index.js", "default": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}}, "module": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}, "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "7c11588c364da4267328cfd32ed20de57494db67", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.8.0_1729014769704_0.88437145361806", "host": "s3://npm-registry-packages"}}, "2.8.1": {"name": "tslib", "version": "2.8.1", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "author": {"name": "Microsoft Corp."}, "license": "0BSD", "_id": "tslib@2.8.1", "maintainers": [{"name": "typescript-bot", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sanders_n", "email": "<EMAIL>"}, {"name": "and<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "minestarks", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sheetalkamat", "email": "<EMAIL>"}, {"name": "typescript-deploys", "email": "<EMAIL>"}], "homepage": "https://www.typescriptlang.org/", "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "dist": {"shasum": "612efe4ed235d567e8aba5f2a5fab70280ade83f", "tarball": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "fileCount": 14, "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "signatures": [{"sig": "MEQCIGwCQX5/87cZTV61H/mDacqOyTtSjyRcavAmrLhEda86AiB2MqmcgfsJi/8SXZR4Ju49WkLgmhXWB8ZPMpdPSZCGTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90359}, "main": "tslib.js", "module": "tslib.es6.js", "exports": {".": {"import": {"node": "./modules/index.js", "default": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}}, "module": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}, "default": "./tslib.js"}, "./": "./", "./*": "./*"}, "gitHead": "d72d6f70b36286bc3f94a3dda1e64dcb568b1370", "typings": "tslib.d.ts", "_npmUser": {"name": "typescript-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Runtime library for TypeScript helper functions", "directories": {}, "jsnext:main": "tslib.es6.js", "sideEffects": false, "_nodeVersion": "22.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/tslib_2.8.1_1730414568373_0.8447792725557923", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-12-30T09:21:20.138Z", "modified": "2025-05-22T16:57:30.422Z", "0.0.1": "2014-12-30T09:21:22.769Z", "0.0.2": "2014-12-30T11:02:46.195Z", "0.0.1-security": "2016-06-16T22:56:19.306Z", "1.0.0": "2016-06-28T00:46:05.835Z", "1.1.0": "2016-11-21T19:18:50.947Z", "1.2.0": "2016-11-30T01:20:12.477Z", "1.3.0": "2016-12-21T17:39:27.117Z", "1.4.0": "2016-12-22T04:46:12.098Z", "1.5.0": "2017-01-05T18:07:30.457Z", "1.6.0": "2017-02-22T00:24:40.508Z", "1.6.1": "2017-04-19T22:24:12.069Z", "1.7.0": "2017-05-04T21:53:04.607Z", "1.7.1": "2017-05-15T22:41:50.599Z", "1.8.0": "2017-10-11T04:34:53.500Z", "1.8.1": "2017-12-06T23:26:57.437Z", "1.9.0": "2018-01-18T21:44:23.777Z", "1.9.1": "2018-05-14T18:16:04.424Z", "1.9.2": "2018-05-29T23:51:13.946Z", "1.9.3": "2018-06-22T07:45:03.346Z", "1.10.0": "2019-06-10T22:29:46.948Z", "1.11.0": "2020-02-20T21:25:00.328Z", "1.11.1": "2020-02-27T19:32:23.640Z", "1.11.2": "2020-05-05T23:22:58.990Z", "1.12.0": "2020-05-12T18:07:58.852Z", "1.13.0": "2020-05-13T22:56:03.236Z", "2.0.0": "2020-05-13T23:08:45.637Z", "2.0.1": "2020-08-06T22:35:16.803Z", "1.14.0": "2020-10-06T15:36:19.182Z", "2.0.2": "2020-10-06T15:39:15.861Z", "1.14.1": "2020-10-09T23:34:42.516Z", "2.0.3": "2020-10-09T23:35:42.926Z", "2.1.0": "2021-01-05T23:28:45.399Z", "2.2.0": "2021-04-05T18:21:21.670Z", "2.3.0": "2021-06-11T22:33:16.856Z", "2.3.1": "2021-08-11T22:55:34.653Z", "2.4.0": "2022-04-22T00:25:01.196Z", "2.4.1": "2022-10-31T18:26:41.901Z", "2.5.0": "2023-01-26T20:40:14.611Z", "2.5.1": "2023-05-17T22:53:14.585Z", "2.5.2": "2023-05-18T21:59:15.029Z", "2.5.3": "2023-06-02T21:22:08.810Z", "2.6.0": "2023-06-26T23:07:41.304Z", "2.6.1": "2023-07-24T20:30:01.894Z", "2.6.2": "2023-08-18T17:41:58.755Z", "2.6.3": "2024-06-04T20:25:19.808Z", "2.7.0": "2024-08-23T19:53:07.533Z", "2.8.0": "2024-10-15T17:52:49.926Z", "2.8.1": "2024-10-31T22:42:48.624Z"}, "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "author": {"name": "Microsoft Corp."}, "license": "0BSD", "homepage": "https://www.typescriptlang.org/", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "repository": {"url": "git+https://github.com/Microsoft/tslib.git", "type": "git"}, "description": "Runtime library for TypeScript helper functions", "maintainers": [{"email": "<EMAIL>", "name": "typescript-bot"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sanders_n"}, {"email": "<EMAIL>", "name": "and<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sheetalkamat"}, {"email": "<EMAIL>", "name": "typescript-deploys"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "readme": "# tslib\r\n\r\nThis is a runtime library for [TypeScript](https://www.typescriptlang.org/) that contains all of the TypeScript helper functions.\r\n\r\nThis library is primarily used by the `--importHelpers` flag in TypeScript.\r\nWhen using `--importHelpers`, a module that uses helper functions like `__extends` and `__assign` in the following emitted file:\r\n\r\n```ts\r\nvar __assign = (this && this.__assign) || Object.assign || function(t) {\r\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n        s = arguments[i];\r\n        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n            t[p] = s[p];\r\n    }\r\n    return t;\r\n};\r\nexports.x = {};\r\nexports.y = __assign({}, exports.x);\r\n\r\n```\r\n\r\nwill instead be emitted as something like the following:\r\n\r\n```ts\r\nvar tslib_1 = require(\"tslib\");\r\nexports.x = {};\r\nexports.y = tslib_1.__assign({}, exports.x);\r\n```\r\n\r\nBecause this can avoid duplicate declarations of things like `__extends`, `__assign`, etc., this means delivering users smaller files on average, as well as less runtime overhead.\r\nFor optimized bundles with TypeScript, you should absolutely consider using `tslib` and `--importHelpers`.\r\n\r\n# Installing\r\n\r\nFor the latest stable version, run:\r\n\r\n## npm\r\n\r\n```sh\r\n# TypeScript 3.9.2 or later\r\nnpm install tslib\r\n\r\n# TypeScript 3.8.4 or earlier\r\nnpm install tslib@^1\r\n\r\n# TypeScript 2.3.2 or earlier\r\nnpm install tslib@1.6.1\r\n```\r\n\r\n## yarn\r\n\r\n```sh\r\n# TypeScript 3.9.2 or later\r\nyarn add tslib\r\n\r\n# TypeScript 3.8.4 or earlier\r\nyarn add tslib@^1\r\n\r\n# TypeScript 2.3.2 or earlier\r\nyarn add tslib@1.6.1\r\n```\r\n\r\n## bower\r\n\r\n```sh\r\n# TypeScript 3.9.2 or later\r\nbower install tslib\r\n\r\n# TypeScript 3.8.4 or earlier\r\nbower install tslib@^1\r\n\r\n# TypeScript 2.3.2 or earlier\r\nbower install tslib@1.6.1\r\n```\r\n\r\n## JSPM\r\n\r\n```sh\r\n# TypeScript 3.9.2 or later\r\njspm install tslib\r\n\r\n# TypeScript 3.8.4 or earlier\r\njspm install tslib@^1\r\n\r\n# TypeScript 2.3.2 or earlier\r\njspm install tslib@1.6.1\r\n```\r\n\r\n# Usage\r\n\r\nSet the `importHelpers` compiler option on the command line:\r\n\r\n```\r\ntsc --importHelpers file.ts\r\n```\r\n\r\nor in your tsconfig.json:\r\n\r\n```json\r\n{\r\n    \"compilerOptions\": {\r\n        \"importHelpers\": true\r\n    }\r\n}\r\n```\r\n\r\n#### For bower and JSPM users\r\n\r\nYou will need to add a `paths` mapping for `tslib`, e.g. For Bower users:\r\n\r\n```json\r\n{\r\n    \"compilerOptions\": {\r\n        \"module\": \"amd\",\r\n        \"importHelpers\": true,\r\n        \"baseUrl\": \"./\",\r\n        \"paths\": {\r\n            \"tslib\" : [\"bower_components/tslib/tslib.d.ts\"]\r\n        }\r\n    }\r\n}\r\n```\r\n\r\nFor JSPM users:\r\n\r\n```json\r\n{\r\n    \"compilerOptions\": {\r\n        \"module\": \"system\",\r\n        \"importHelpers\": true,\r\n        \"baseUrl\": \"./\",\r\n        \"paths\": {\r\n            \"tslib\" : [\"jspm_packages/npm/tslib@2.x.y/tslib.d.ts\"]\r\n        }\r\n    }\r\n}\r\n```\r\n\r\n## Deployment\r\n\r\n- Choose your new version number\r\n- Set it in `package.json` and `bower.json`\r\n- Create a tag: `git tag [version]`\r\n- Push the tag: `git push --tags`\r\n- Create a [release in GitHub](https://github.com/microsoft/tslib/releases)\r\n- Run the [publish to npm](https://github.com/microsoft/tslib/actions?query=workflow%3A%22Publish+to+NPM%22) workflow\r\n\r\nDone.\r\n\r\n# Contribute\r\n\r\nThere are many ways to [contribute](https://github.com/Microsoft/TypeScript/blob/master/CONTRIBUTING.md) to TypeScript.\r\n\r\n* [Submit bugs](https://github.com/Microsoft/TypeScript/issues) and help us verify fixes as they are checked in.\r\n* Review the [source code changes](https://github.com/Microsoft/TypeScript/pulls).\r\n* Engage with other TypeScript users and developers on [StackOverflow](http://stackoverflow.com/questions/tagged/typescript).\r\n* Join the [#typescript](http://twitter.com/#!/search/realtime/%23typescript) discussion on Twitter.\r\n* [Contribute bug fixes](https://github.com/Microsoft/TypeScript/blob/master/CONTRIBUTING.md).\r\n\r\n# Documentation\r\n\r\n* [Quick tutorial](http://www.typescriptlang.org/Tutorial)\r\n* [Programming handbook](http://www.typescriptlang.org/Handbook)\r\n* [Homepage](http://www.typescriptlang.org/)\r\n", "readmeFilename": "README.md", "users": {"andr": true, "vivekrp": true, "noemandn": true, "vinbhatt": true, "zhangaz1": true, "mataco817": true, "mgthomas99": true, "monolithed": true, "flumpus-dev": true, "sergeyshandar": true}}