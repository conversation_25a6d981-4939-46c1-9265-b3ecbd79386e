{"_id": "@tailwindcss/oxide-wasm32-wasi", "_rev": "142-984106ce2fb8512a19f9ad509f39c71c", "name": "@tailwindcss/oxide-wasm32-wasi", "dist-tags": {"latest": "4.1.11", "insiders": "0.0.0-insiders.2941a7b"}, "versions": {"0.0.0-insiders.d801d8d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d801d8d", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.d801d8d", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "2aeecb5c453f6a96300f0da2864b42a1803dfda6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d801d8d.tgz", "fileCount": 113, "integrity": "sha512-y67m0DRzTwKTC98UWowLd1EXz727/NcKYGISg7h68E3RuCC/cUv2T8SzWXDwWVZ4nUyYpxcVLM5hc+Yp7DP5Fw==", "signatures": [{"sig": "MEQCIAat4iGD2rLHc2/s2p/CuXMV7YsJhb+yRLRI790v0FawAiBCsQ0EpJdVlrMSAi3PwcI6noeR/zdrC0Fok9LKGDv8xQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d801d8d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178501}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d801d8d.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/89a95ae151f2d0132a75f30f974c97b3/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d801d8d.tgz", "_integrity": "sha512-y67m0DRzTwKTC98UWowLd1EXz727/NcKYGISg7h68E3RuCC/cUv2T8SzWXDwWVZ4nUyYpxcVLM5hc+Yp7DP5Fw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.d801d8d_1744388480712_0.7858119041697222", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.bbd916a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.bbd916a", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.bbd916a", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "210b44f493608a6e4ce560bca120e900d05da9df", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.bbd916a.tgz", "fileCount": 113, "integrity": "sha512-Ipoo7m2IaNvJSWZy57V6nYBseJtS7vQ+vk5RTakrqBoWgyBrs8alveAIauNs1sQpfZi/zuHEo/MgyGubyEkPww==", "signatures": [{"sig": "MEYCIQCFbAt2DDa0WWsS2sH6I+HlEm8dA1gzhwwihT/+mqkwjgIhAJi0piiIei29qSwzbBa+WPWc9hV7ot24TH+cDzGyOdxt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.bbd916a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.bbd916a.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/a3590b4b47f4dc89a7d13e7e0901239d/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.bbd916a.tgz", "_integrity": "sha512-Ipoo7m2IaNvJSWZy57V6nYBseJtS7vQ+vk5RTakrqBoWgyBrs8alveAIauNs1sQpfZi/zuHEo/MgyGubyEkPww==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.bbd916a_1744457762797_0.6142970250343103", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.cf2591c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.cf2591c", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.cf2591c", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "29445303153eae0906ed13c0e790a9cfd3d7287d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.cf2591c.tgz", "fileCount": 113, "integrity": "sha512-xKfemF07S6hipav91Uc6mlIrx5GiVujF9zIwgEj4ojVCJnoDrpQ+GkOGIjgHw1VMuU509SMsODZO6hpTSDlsfQ==", "signatures": [{"sig": "MEQCIHNYxxH338ObDnSqcVnGD/POTLjvtm+CLOK5GoJopjLhAiBZIgcDaNDDJdAJ30ZxDy7Jj1Klrsw8+WL/zG+VG8VcFg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.cf2591c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.cf2591c.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/b8c96aae096c81083f5301b0e7c30f02/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.cf2591c.tgz", "_integrity": "sha512-xKfemF07S6hipav91Uc6mlIrx5GiVujF9zIwgEj4ojVCJnoDrpQ+GkOGIjgHw1VMuU509SMsODZO6hpTSDlsfQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.cf2591c_1744622391415_0.6232390119881812", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.aa836d3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.aa836d3", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.aa836d3", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "4e5bda6f3e50ea45d46d1f312baaba6cc387fd7e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.aa836d3.tgz", "fileCount": 113, "integrity": "sha512-0TwxdBm8gi/5CBtl4TyeauSyaiGuGnLx5vxNqEJWifCHPyCzDf8omQQC5GSCFLaqTGQISduH0e+xoQG81TJcug==", "signatures": [{"sig": "MEUCIQC/zDc2VKOqT+xnRWzc60kwr85am/B/ba8EXTZgN3+S2AIgYxxtz24Tspp+Mj617IQk2l4SpU7IeSaQRKLRFYGeq1k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.aa836d3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.aa836d3.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/13980717064b35bbf983514fa64c9c89/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.aa836d3.tgz", "_integrity": "sha512-0TwxdBm8gi/5CBtl4TyeauSyaiGuGnLx5vxNqEJWifCHPyCzDf8omQQC5GSCFLaqTGQISduH0e+xoQG81TJcug==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.aa836d3_1744645213254_0.8927496789359901", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.4": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.4", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@4.1.4", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "2c6b1aba1f086c3337625cdb3372c3955832768c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.4.tgz", "fileCount": 113, "integrity": "sha512-2TLe9ir+9esCf6Wm+lLWTMbgklIjiF0pbmDnwmhR9MksVOq+e8aP3TSsXySnBDDvTTVd/vKu1aNttEGj3P6l8Q==", "signatures": [{"sig": "MEYCIQDxtwm5m8nxcBhZ/n9wDNDlwya+HBqUuaTCgsmVH+sqJwIhALY5MB+OhauJdroWkz77y6ew/nbo9XTFzycvPNMzwv/y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178622}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-4.1.4.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/76ba2806c793675c683a05c3fb16fc9d/tailwindcss-oxide-wasm32-wasi-4.1.4.tgz", "_integrity": "sha512-2TLe9ir+9esCf6Wm+lLWTMbgklIjiF0pbmDnwmhR9MksVOq+e8aP3TSsXySnBDDvTTVd/vKu1aNttEGj3P6l8Q==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_4.1.4_1744650063140_0.8966696529346125", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.25539e3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.25539e3", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.25539e3", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "630c18da465298b49d48afe76ee2376785fa2d70", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.25539e3.tgz", "fileCount": 113, "integrity": "sha512-GID1lai/h2kvx3CzD4nbVI7E/CfnosstjscOtotib3uVVuUXd3+5IIToRKEXZBnKa+PwLDO55B+03xgzMY9Jxg==", "signatures": [{"sig": "MEYCIQDCGdh2f6m6drPCOATO0gWQ3DhmZX+AXLqQhWnwezjz4AIhANzc77ISmd/XzhY4HkXLHixbpd2OjqxLW+IBobFJaNBj", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.25539e3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.25539e3.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/cf497278d170cb27a1b673b9063e8b75/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.25539e3.tgz", "_integrity": "sha512-GID1lai/h2kvx3CzD4nbVI7E/CfnosstjscOtotib3uVVuUXd3+5IIToRKEXZBnKa+PwLDO55B+03xgzMY9Jxg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.25539e3_1744650868259_0.8432809270122017", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.adcf1de": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.adcf1de", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.adcf1de", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "0121d48ecde468302f35d48dac48199c86462bd6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.adcf1de.tgz", "fileCount": 113, "integrity": "sha512-+3KyS22HMgktwea4CS/WOTfE4X37UEQaDKXcZw4BReAENwFqbtpufxGBGCZYTQjjkbwLD0rnIELDL49/IJ9srg==", "signatures": [{"sig": "MEUCIQDVqM6XelEjFyvDX3qll83VQx4vcKtd98h2QkT/A7p01AIgZuNgMZ4XD6GkY1/jZ7PW+QU2aIlg8EiGJOAfvB8jVbs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.adcf1de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.adcf1de.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/0a71104d854883136d91f71a0a562502/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.adcf1de.tgz", "_integrity": "sha512-+3KyS22HMgktwea4CS/WOTfE4X37UEQaDKXcZw4BReAENwFqbtpufxGBGCZYTQjjkbwLD0rnIELDL49/IJ9srg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.adcf1de_1744711856442_0.8215927706305264", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.8feb6a7": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8feb6a7", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.8feb6a7", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "e427f594c003d4764eddd0911db3ea350b30ecd7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8feb6a7.tgz", "fileCount": 113, "integrity": "sha512-cLTOqLfttlTCxeFTFyCW8MhaJRs3p9bnzUZBKfJANPvfrCr70ldDfy766ES5l9kTs0aVHLHFx4hyHvcGoIHbQw==", "signatures": [{"sig": "MEQCIC6WjIjdnSWkaZGMbmb/F6Egtl/PwPJ+KcdPmoVIVcWHAiArut6pYrg5fLK0qokWEckLE59Ig9uMl+jI9YkM2fFeuw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8feb6a7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8feb6a7.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/45dc4baa478d82c28d78ff65b293f8c9/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8feb6a7.tgz", "_integrity": "sha512-cLTOqLfttlTCxeFTFyCW8MhaJRs3p9bnzUZBKfJANPvfrCr70ldDfy766ES5l9kTs0aVHLHFx4hyHvcGoIHbQw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.8feb6a7_1744966461170_0.12450326102000275", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.fc4afc2": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.fc4afc2", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.fc4afc2", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "1f101c7798866c51986213b981597bfa3fe142c3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.fc4afc2.tgz", "fileCount": 113, "integrity": "sha512-ChhQq9Nuj8aTHU60OLXZcHWOMQbVYYHeU2UEJl0Q9US3t41P2FpSGLpZ/k1uveFJHWKjzrIMHc2culCDwSiO7g==", "signatures": [{"sig": "MEQCIBGffq0iNWPZABZwVKzQAFmXylOM6Tjd4WscdOT50zRuAiA+Lx1Pow6IwIawHgk66Lo/sBPtktb/vb6Dxu9mbr3V+g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.fc4afc2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.fc4afc2.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/cb8525d71320ed840b515a19a8551773/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.fc4afc2.tgz", "_integrity": "sha512-ChhQq9Nuj8aTHU60OLXZcHWOMQbVYYHeU2UEJl0Q9US3t41P2FpSGLpZ/k1uveFJHWKjzrIMHc2culCDwSiO7g==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.fc4afc2_1745312583154_0.09193945527027148", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.650558d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.650558d", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.650558d", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "b25dfe24bf673a85e30ee80ed3674adb22ac6c16", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.650558d.tgz", "fileCount": 113, "integrity": "sha512-gBJQaGwsEMMBGmWVEvfpXSgvBDRyL5+yi66JxljoaWqIiNYmUMxQk+uD/kqVoPPp7nQztLRI7XKW22xgFrOCQg==", "signatures": [{"sig": "MEUCIQCni8jZo/Ls/lWcHhwf/cpohudJM+qpSjOt1wFbGnC5zgIgUtuKyS3nFg2hko3G3LGi81OvJw57uoch54zoOj0266o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.650558d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.650558d.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/34c932cc9f008f9698a6412471832fd0/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.650558d.tgz", "_integrity": "sha512-gBJQaGwsEMMBGmWVEvfpXSgvBDRyL5+yi66JxljoaWqIiNYmUMxQk+uD/kqVoPPp7nQztLRI7XKW22xgFrOCQg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.650558d_1745329396784_0.7289243234549285", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ee0d752": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ee0d752", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ee0d752", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "5a1de1e1d08bbdd3867555b7c8fe44ec7b8003e7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ee0d752.tgz", "fileCount": 113, "integrity": "sha512-FTv3uDMu8XSayiuGga4F4gJGQyiixxHBIwuO7lrFIqValshuyXWlz0hUUKhO3Sa0oCJvJ+GyisozomHCAXkViQ==", "signatures": [{"sig": "MEQCIApEQ+0mrLHOdt/plxOOga34bOMCGuRR1MGkQhsoBWwPAiAzE9mIOn0IJp1YgsSVdyYw2nR/+Iqs8jdUtlEvIbX6HA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ee0d752", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ee0d752.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/9c98f3afd679e746e01720b19d6233ff/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ee0d752.tgz", "_integrity": "sha512-FTv3uDMu8XSayiuGga4F4gJGQyiixxHBIwuO7lrFIqValshuyXWlz0hUUKhO3Sa0oCJvJ+GyisozomHCAXkViQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ee0d752_1745329410728_0.7984166741835359", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.8bf06ab": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8bf06ab", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.8bf06ab", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "e48a9ea1c2354e30e0836a0c795b46293c2d2bf2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8bf06ab.tgz", "fileCount": 113, "integrity": "sha512-r/+9goIOLeroN71ksddwwCGL3B8AKnDv35saWnjoUA1dv/ZX+PtLhxSt1Z7/GVAw26eyKNAqH9J9BH9VmS3Pfw==", "signatures": [{"sig": "MEUCIDe99d4q9pkf4nY5Q69J4wnqckvVjUPPfFAeUZSz2JRVAiEAqh5BiawT3TUjC4+p4dIxEn2L3zRhFKcZki0Bhy0YHyA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8bf06ab", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8bf06ab.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/71a46ae9730723f6ef10996789177edd/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8bf06ab.tgz", "_integrity": "sha512-r/+9goIOLeroN71ksddwwCGL3B8AKnDv35saWnjoUA1dv/ZX+PtLhxSt1Z7/GVAw26eyKNAqH9J9BH9VmS3Pfw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.8bf06ab_1745331045462_0.16295396901378956", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.25ec6a3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.25ec6a3", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.25ec6a3", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "fa6764ea39553ed3d8586ab8a7b86d3181c6e3aa", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.25ec6a3.tgz", "fileCount": 113, "integrity": "sha512-Vbu1Pq/eypu9ebcoTxcOzxEPqWpb2DrK9lEo738o22IFjvEdPvmP4FDqRfwKBS+I1xrO3um1XhYTZaDf53bCzw==", "signatures": [{"sig": "MEQCIEhQ4CUEwMNi8PeN4lGz8YxOwFs04VrlKkYH9iz8Dnj4AiAgjUiBQ5gMCHNunNZmEEWZo3mFDBA5AOkiICMZ0zjGZA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.25ec6a3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.25ec6a3.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/86f134dd387353aa34dfd18e9986d616/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.25ec6a3.tgz", "_integrity": "sha512-Vbu1Pq/eypu9ebcoTxcOzxEPqWpb2DrK9lEo738o22IFjvEdPvmP4FDqRfwKBS+I1xrO3um1XhYTZaDf53bCzw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.25ec6a3_1745334253063_0.1754633402960728", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.8e826b1": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8e826b1", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.8e826b1", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "b2f4f597e72306fd16b26e42d6d266e2032b1359", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8e826b1.tgz", "fileCount": 113, "integrity": "sha512-pHEKrz2V/dsdxO9JwMhaCA+MuW4d9bwcQujhhjT2HlGWD6K/zkto1kukj5mgi8Y37HQUbrCHbklh+DjhUdqiHw==", "signatures": [{"sig": "MEUCIGAmixBckiSX5qLeiEjQAlu2pO6Sgspm0wWl6D1Q6/ZtAiEAzrVp+G+dpjnFJuCd0T9LtEzPnWu51lYZ1ge5Up1dECc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8e826b1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8e826b1.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/0e321d211817dccb9b9ebb4fb91a40c7/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8e826b1.tgz", "_integrity": "sha512-pHEKrz2V/dsdxO9JwMhaCA+MuW4d9bwcQujhhjT2HlGWD6K/zkto1kukj5mgi8Y37HQUbrCHbklh+DjhUdqiHw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.8e826b1_1745335200227_0.15940258232159676", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.a7f4a4d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.a7f4a4d", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.a7f4a4d", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "9f868856593251f731df227c20deb62ae633217c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.a7f4a4d.tgz", "fileCount": 113, "integrity": "sha512-H4eCfXXD/N4TV1J3Ru1xv3xn+uo20jd05wA6YCzoldhhOMK+brAQxsZmFdAg0QJOr0KtbrWXgBYjeYkWGWTNFw==", "signatures": [{"sig": "MEUCIQCh3mhKB2oMS/Ta/X8GWIciwv0L1q2RBEFjgSuSJrj3dgIgB0PbPKbfvtJUV9vtNhfQuLSNavTcLOKGUwO1pjq0gUY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.a7f4a4d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.a7f4a4d.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qn/7t0vq3ts721cmgt0tgrtgzl80000gn/T/b5f992866d2f4b51d400255bee76aa0a/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.a7f4a4d.tgz", "_integrity": "sha512-H4eCfXXD/N4TV1J3Ru1xv3xn+uo20jd05wA6YCzoldhhOMK+brAQxsZmFdAg0QJOr0KtbrWXgBYjeYkWGWTNFw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.a7f4a4d_1745414622571_0.46563985304342714", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.46758f7": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.46758f7", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.46758f7", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "c77a2fd1621b11ae98c63097762ae15aeac746ef", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.46758f7.tgz", "fileCount": 113, "integrity": "sha512-REsq6nkGh0yDJLShpf+gvT3BQoY8moBjogK1n21qLKAptfY0AIHaYikL7wvkquCuuRajK4UwVQkLfyCRx0BxTw==", "signatures": [{"sig": "MEUCIA7cdUgDdAuj/1FSsQ34iEmkwCl9+YbUmx5Y2t3H+tldAiEA1q9HMKqu6ejnPMzUSNisk/ks6RPZrh1RFPxMUBFuB7g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.46758f7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.46758f7.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/9fa2f62a87373c0e1d94c4ab790e5bd4/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.46758f7.tgz", "_integrity": "sha512-REsq6nkGh0yDJLShpf+gvT3BQoY8moBjogK1n21qLKAptfY0AIHaYikL7wvkquCuuRajK4UwVQkLfyCRx0BxTw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.46758f7_1745486895535_0.9101952445303434", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.2bf2b4d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.2bf2b4d", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.2bf2b4d", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "863af99d88f22ef0cef3c0013661ab8ed8ef7c39", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.2bf2b4d.tgz", "fileCount": 113, "integrity": "sha512-7IntJwcIOaKWvynh7xPuS56abNJispsWFOAcidszfQs4mf2iY/zsd5zcV8zNM3h0HJm5D3bYmkiPtkaXQ8gjLw==", "signatures": [{"sig": "MEUCICd5iSTBUBfgNnpzBsINQcjUIyAKYCFinWQZFlne4AXaAiEA3SVCvpn93zYiM7eJb16rT22IsJSl/LlUCq/QuLcL+qE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.2bf2b4d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2bf2b4d.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/32d68944e59fe454b8e5f9323e419c08/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2bf2b4d.tgz", "_integrity": "sha512-7IntJwcIOaKWvynh7xPuS56abNJispsWFOAcidszfQs4mf2iY/zsd5zcV8zNM3h0HJm5D3bYmkiPtkaXQ8gjLw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.2bf2b4d_1745487014449_0.7965636261117275", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.d780a55": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d780a55", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.d780a55", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "f6990f3872b0203808558500f6e3d6bed40f81c8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d780a55.tgz", "fileCount": 113, "integrity": "sha512-P4PmFt5sOwwFTeTLBadKUJBIr8aXwVd8cVdUQLwwKSVQoFli8umhUcss2GYfJJilTaUensR3SX18ikXNVwKhXw==", "signatures": [{"sig": "MEYCIQDhClPIQ2JRoQVQhS2vE/AyLvNjrBZvanKjQgfYH6ATXAIhAMANUmsiUWhmXD5tLr5BpVpDZxUxuwErm6R6JJApnI5T", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d780a55", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d780a55.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/e4d82e86482756b6e8a1d6808d1f630d/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d780a55.tgz", "_integrity": "sha512-P4PmFt5sOwwFTeTLBadKUJBIr8aXwVd8cVdUQLwwKSVQoFli8umhUcss2GYfJJilTaUensR3SX18ikXNVwKhXw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.d780a55_1745575322233_0.6309499227653319", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.231cddd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.231cddd", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.231cddd", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "e6e0bffd63859e139817bb6cb60691a9a4aee2ec", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.231cddd.tgz", "fileCount": 113, "integrity": "sha512-3qn9oKNyc+3USkCK40nqZPlkSt5WW2Do6K6/2jek9uCyTvTv6iz+K0OBjxHolSiZeA7B2Rg5DqNN6RuPo2Oezg==", "signatures": [{"sig": "MEUCIQCvv5D98V9PG76zsB4EJcnX9GyYCKzC5XsRbBCPbwnpYAIgQkpwxAob68sPx6FpJSlNyiUTvrWCHknwZ2F1nOGi88k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.231cddd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.231cddd.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/4490b5bb95c46be28059aae9c9e9fb44/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.231cddd.tgz", "_integrity": "sha512-3qn9oKNyc+3USkCK40nqZPlkSt5WW2Do6K6/2jek9uCyTvTv6iz+K0OBjxHolSiZeA7B2Rg5DqNN6RuPo2Oezg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.231cddd_1745575987457_0.12681581382592588", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.52000a3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.52000a3", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.52000a3", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "02d14b85ab09a093d4132e4a046b472429101a05", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.52000a3.tgz", "fileCount": 113, "integrity": "sha512-bwCmqnIach25mnOSc7tQsdZU1jCFmAP3RH9ejDp9QI8q6CoZ+LKcJUEsg7ifekPVAhH85kRsMQCYZ2IB+I/mvg==", "signatures": [{"sig": "MEUCIQDmNKtqMb3hCaj0gzV4VEn/oqhtUdO2aq3xHmdUcP0HNAIgXoQYhZeI30CBFBo0B9vZWQDWsg2o5D6g411DJSMpvo8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.52000a3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.52000a3.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/71bc4be4f3bbe6b91b178416cc6ca394/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.52000a3.tgz", "_integrity": "sha512-bwCmqnIach25mnOSc7tQsdZU1jCFmAP3RH9ejDp9QI8q6CoZ+LKcJUEsg7ifekPVAhH85kRsMQCYZ2IB+I/mvg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.52000a3_1745576082797_0.6715550009487672", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.62ca1ec": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.62ca1ec", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.62ca1ec", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "640301c8d2d18880a1f7e4f0bc69db1029b44890", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.62ca1ec.tgz", "fileCount": 113, "integrity": "sha512-hL9JNnZoRMHMNP3kZNzfMBHNKfMxiE6jW1Ay9fCmS1hnagYzarTozL1x0dN2VrNIvlO1oGxLKSh7FM/JlvUUmw==", "signatures": [{"sig": "MEUCIQCQydQsnUrTj8dcoSDpwTNW6AB4GFdamtJ9nlvJflIKtAIgS+69YQbOK4RQraz1+Qg1Sili4BHooLQBoGsJCbYNbR0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.62ca1ec", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.62ca1ec.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/475ce1d51af3993005e62dbf902b187b/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.62ca1ec.tgz", "_integrity": "sha512-hL9JNnZoRMHMNP3kZNzfMBHNKfMxiE6jW1Ay9fCmS1hnagYzarTozL1x0dN2VrNIvlO1oGxLKSh7FM/JlvUUmw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.62ca1ec_1745579379404_0.49031765288524154", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ba10379": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ba10379", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ba10379", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "32d67a24aba9d83a1054dbc778bc6673c2a26b26", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ba10379.tgz", "fileCount": 113, "integrity": "sha512-hI4HVulZYBYCHCxHUoYk15ZQYYumf23XYmKqkxG3mP1ioKxxlXdof+7BT0Ig7//rzWpcwRx919AgPAWJ8EkHZQ==", "signatures": [{"sig": "MEUCIQDgCwYDNHPyLc328V8Y4DQYyjRCz4zKRkg6MfRvLPuh2QIgTm9bDy5+nqAzyrQi5OfBfSBymBkieZzKBzLhB0v+utg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ba10379", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ba10379.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/be7e73e4e9fc50f8a34ac5f312c26c3f/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ba10379.tgz", "_integrity": "sha512-hI4HVulZYBYCHCxHUoYk15ZQYYumf23XYmKqkxG3mP1ioKxxlXdof+7BT0Ig7//rzWpcwRx919AgPAWJ8EkHZQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ba10379_1745695453314_0.015787604204413164", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.af1d4aa": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.af1d4aa", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.af1d4aa", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "38ad7049a3529ab21645d399563c257c28d3e8ff", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.af1d4aa.tgz", "fileCount": 113, "integrity": "sha512-in0hWKK0KWZnGeKO9sf7IyC+yJkduUQlANtbRKaJ1MVwCirPx7P2lVE29PKX3IXNC4bZiXyR3+OlGUN4KSm9sg==", "signatures": [{"sig": "MEYCIQCv8DQ7yTPAngfcX/y0vapCgOjJxzo7XmHUTSOL54R/uAIhAIdlumEMwJwq2PlkFwOs8a5TY3OiILMzhZ2Zlr66RcaR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.af1d4aa", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.af1d4aa.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/2668d2670595ebdadb4867f4619c66e7/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.af1d4aa.tgz", "_integrity": "sha512-in0hWKK0KWZnGeKO9sf7IyC+yJkduUQlANtbRKaJ1MVwCirPx7P2lVE29PKX3IXNC4bZiXyR3+OlGUN4KSm9sg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.af1d4aa_1745831068356_0.7999257001450282", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.3a1b27e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.3a1b27e", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.3a1b27e", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "e170bb109c55c0db662649b4bf9d5910c6882662", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.3a1b27e.tgz", "fileCount": 113, "integrity": "sha512-Em/AIbskR5gUPLo3viDvLWlL6Vl42mQKcpHwr7AuuqsVLLGdtTGaFiOph4sls3LC0m9gDtNAdWSRLwq9TbKA1g==", "signatures": [{"sig": "MEUCID5DouLOTW7smUFgKiYec0kaCxaDWvOS9JPdEShMYKd9AiEA5mgMoSFCsKsQixbjz22ocvJjDUnPfl01rg3QR5zVX0k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.3a1b27e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.3a1b27e.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/3e0cd904d4626970ff9870702f1cdf97/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.3a1b27e.tgz", "_integrity": "sha512-Em/AIbskR5gUPLo3viDvLWlL6Vl42mQKcpHwr7AuuqsVLLGdtTGaFiOph4sls3LC0m9gDtNAdWSRLwq9TbKA1g==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.3a1b27e_1745861558978_0.693005617707553", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.d2daf59": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d2daf59", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.d2daf59", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "23db094e32d134ccdaa2415eaa3ccdf538b1e7cb", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d2daf59.tgz", "fileCount": 113, "integrity": "sha512-2XMix28KYTpOKH4uj9NS3vRqUpSzvjntkSP/XIAzC6nYbPIHyG0DRxynB78eYXfY5m8GpOhxGF9kPedDayEKkQ==", "signatures": [{"sig": "MEUCIQC877edUdahtHhMBWthEMdS9LdXRQmR94x/uwZeEqrBxgIgWl4EqaltdnOcmMlzr7jUBCK7vcThDXKvKF3qGju5OEY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d2daf59", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d2daf59.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/da67d61340df8c8c73edbf996031ee03/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d2daf59.tgz", "_integrity": "sha512-2XMix28KYTpOKH4uj9NS3vRqUpSzvjntkSP/XIAzC6nYbPIHyG0DRxynB78eYXfY5m8GpOhxGF9kPedDayEKkQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.d2daf59_1745861911614_0.7664355168884407", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.9fec4ef": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.9fec4ef", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.9fec4ef", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "edc380d248247c055a51622276974b4928902460", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.9fec4ef.tgz", "fileCount": 113, "integrity": "sha512-alu8K9/VcKZop4zlgvGc8NE14zkh/DOzyEqEOggXO2PZwDcyIv1qZ4xilkFxvMO8nAON0Wdg91tTE5OA5Ys+rw==", "signatures": [{"sig": "MEQCIEY74hKufRbdFwcLd2rdxdlDIo9iGn96sDdrn1ybHuSfAiA7zbSvsvIW/QwYOnGYUR6p9vgs/242LA0l0RqPLtzilg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.9fec4ef", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.9fec4ef.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/7520c5e049878d28868f3b34fe938da2/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.9fec4ef.tgz", "_integrity": "sha512-alu8K9/VcKZop4zlgvGc8NE14zkh/DOzyEqEOggXO2PZwDcyIv1qZ4xilkFxvMO8nAON0Wdg91tTE5OA5Ys+rw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.9fec4ef_1745943092218_0.273814909152132", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.d3846a4": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d3846a4", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.d3846a4", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "2287b4434e4f819bc66753543b87ae480044444c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d3846a4.tgz", "fileCount": 113, "integrity": "sha512-sUjaPx536iViaVaKC9Trd4oJl1FnBS/iNx8T4MbBIRHpOSY+pYX2UT2vsozv320aSGaHB9ph50ICeJq/7fxqPA==", "signatures": [{"sig": "MEUCIQDoobTJFp3gr55yubngGAAQUqrgvAcLuthgaaeVfqF/jAIgROEO9zAE5WWookXLS5Tar7D5yqrxoPZg2Tr1mN7eKZQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d3846a4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d3846a4.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/8f77070237d52ad6af04129b40a001e1/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d3846a4.tgz", "_integrity": "sha512-sUjaPx536iViaVaKC9Trd4oJl1FnBS/iNx8T4MbBIRHpOSY+pYX2UT2vsozv320aSGaHB9ph50ICeJq/7fxqPA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.d3846a4_1745943961052_0.8751452152310644", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.dbc8023": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.dbc8023", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.dbc8023", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "54a8f0b779b55a4a1e3a699911d88c0d4798db2e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.dbc8023.tgz", "fileCount": 113, "integrity": "sha512-20FI3qwgCTLx5HhPi9KrFPqHmQTArnE3rN/p1byPH7CrHjdJtLe0/+0fZ5g0yc4QRDVrwnaZT2BwHgIQeg3KdA==", "signatures": [{"sig": "MEUCIQDae+bXuaGD7tBWQwURv2qedgLrJaLKpbc/s8RemGK0owIgVFDeHjkUZaoSXU6OpAPBwbUz/3IlDT6DOic+7+J9Ybo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.dbc8023", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.dbc8023.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/45252fe382d6644a8567d47cabbe2068/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.dbc8023.tgz", "_integrity": "sha512-20FI3qwgCTLx5HhPi9KrFPqHmQTArnE3rN/p1byPH7CrHjdJtLe0/+0fZ5g0yc4QRDVrwnaZT2BwHgIQeg3KdA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.dbc8023_1745946960661_0.8368818273661733", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ab4eb18": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ab4eb18", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ab4eb18", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "a0821b4b4ae20dc60bf53766feae34445f180668", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ab4eb18.tgz", "fileCount": 113, "integrity": "sha512-BIqWiRabjk3Bxmxn5bF3OI0RIvY8x6sy8+CPHvs4+ynPBYEHETtGqNwAdVbtr1iFU8lVTv9Qe38eQarjBO9y+w==", "signatures": [{"sig": "MEYCIQDPAaVM0Ak/iCzU2r7mrFUz1uWBpbypMoT1QzchlE4V3gIhAO3JmvAMiOQlfOIhmtC/ZzsCRp8y5gIYJMGVP8KBV5xf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ab4eb18", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ab4eb18.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/b76bc0010a39ad61859dcb162c7362ed/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ab4eb18.tgz", "_integrity": "sha512-BIqWiRabjk3Bxmxn5bF3OI0RIvY8x6sy8+CPHvs4+ynPBYEHETtGqNwAdVbtr1iFU8lVTv9Qe38eQarjBO9y+w==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ab4eb18_1746007132360_0.30567385937782854", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.a636933": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.a636933", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.a636933", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "c03442c204feb314462cd25ac3c84afce8a78396", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.a636933.tgz", "fileCount": 113, "integrity": "sha512-ai7F6EL/7okENb87vbQlCCwiIo6/r97vWDlcz/Mq6E3CnJqcnX3px2ggIg4pOsU58XdWLabNLbr44b8UecrDqQ==", "signatures": [{"sig": "MEYCIQD/YSn8nInO2itgampJh69DOjnIMKtD3X8+cMaGlOrxpwIhAJq9QpOtvXmJydHKtKZ6d837QYewI/oI0ULTxxzzZXVF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.a636933", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.a636933.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/e9eb0a7b8081d2b9723a8b8ef2d01d58/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.a636933.tgz", "_integrity": "sha512-ai7F6EL/7okENb87vbQlCCwiIo6/r97vWDlcz/Mq6E3CnJqcnX3px2ggIg4pOsU58XdWLabNLbr44b8UecrDqQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.a636933_1746016874416_0.8059195795487883", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.45cd32e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.45cd32e", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.45cd32e", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "a08def93fa497ad5772fa6f89dc432a3f7673dc5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.45cd32e.tgz", "fileCount": 113, "integrity": "sha512-Iqq31V1BAaZhJsTrqbwNd4PpCICtmnkGWYcI5pNbdsogfoa1rWz+EJU/jLKPH71pGXGEHE+DWaIqIUtW3a0dQg==", "signatures": [{"sig": "MEYCIQDWYtkj7+EZrLhOD8WFfP17lJUz2xHFiqbhpNcrhyUiWAIhAJNj3loi78+cc8cXiTMfnubTAkRsZArLsemJm1QErVvZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.45cd32e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.45cd32e.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/4d69c9096ce095cf394a80c655e315ad/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.45cd32e.tgz", "_integrity": "sha512-Iqq31V1BAaZhJsTrqbwNd4PpCICtmnkGWYcI5pNbdsogfoa1rWz+EJU/jLKPH71pGXGEHE+DWaIqIUtW3a0dQg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.45cd32e_1746025566362_0.46235126305498553", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.5": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.5", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@4.1.5", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "6bceca7bd7b387936b8fe292be3ab3c305da1699", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.5.tgz", "fileCount": 113, "integrity": "sha512-hwALf2K9FHuiXTPqmo1KeOb83fTRNbe9r/Ixv9ZNQ/R24yw8Ge1HOWDDgTdtzntIaIUJG5dfXCf4g9AD4RiyhQ==", "signatures": [{"sig": "MEYCIQCt/Xqy1UYZs06n1Oyivni/qy5+kURTvS0nAwQCKvxhBAIhANRRulaw8GEx3yQR1MyB4yqJar+t+w+vRg7VhpFpLQMv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180340}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-4.1.5.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7g/cnfrzbd555q9lmpsr1byngs80000gn/T/621eeff030c347e5d9884a9def5ed9c7/tailwindcss-oxide-wasm32-wasi-4.1.5.tgz", "_integrity": "sha512-hwALf2K9FHuiXTPqmo1KeOb83fTRNbe9r/Ixv9ZNQ/R24yw8Ge1HOWDDgTdtzntIaIUJG5dfXCf4g9AD4RiyhQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_4.1.5_1746028386665_0.2164690064903616", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.4e42756": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4e42756", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.4e42756", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "cf62dcb85b435248a2de77540562df5e14b4710f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4e42756.tgz", "fileCount": 113, "integrity": "sha512-Ki1mY+4s3A/s1jfR1S0S2oSD3/CR0eOnuo0W7bohbpnDEHc8oeXudntnQFdcQDPBA0YyYthJ7bDJIMdL/Sf2OA==", "signatures": [{"sig": "MEUCIQCVSLe3N7RXXKpNxNjNmo77lSQUKQJIPL12Vc7oQ7RgAwIgH4s76866KdHus1IJr2pkUnC2t4HrJXx8MJnJA96JxiA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4e42756", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4e42756.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/8c733454b6def88f72cf4d5bde723a5e/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4e42756.tgz", "_integrity": "sha512-Ki1mY+4s3A/s1jfR1S0S2oSD3/CR0eOnuo0W7bohbpnDEHc8oeXudntnQFdcQDPBA0YyYthJ7bDJIMdL/Sf2OA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.4e42756_1746221178704_0.3749231304764069", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.c095071": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.c095071", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.c095071", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "89f43b42a02f39c737e36dfde0c6d03bd9be78b5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.c095071.tgz", "fileCount": 113, "integrity": "sha512-wIVbNEIPX0wWo1K+k9gbDKWdShbgvGbsOGnkVO57oP8aR0aQok5YOvQw/vWZsYhgCQR2y4T4ty95nGgIN6D+xg==", "signatures": [{"sig": "MEYCIQD2T7L8HF5vmV8uolPr31qhz0C2SvkNgh3rTWZubU2gwAIhAKMvkIujR/QFctpbRZ0YTrKp5RIrfWRVBe2FGz6RWBjK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.c095071", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.c095071.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/c095d167485f82073445823fba9465d0/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.c095071.tgz", "_integrity": "sha512-wIVbNEIPX0wWo1K+k9gbDKWdShbgvGbsOGnkVO57oP8aR0aQok5YOvQw/vWZsYhgCQR2y4T4ty95nGgIN6D+xg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.c095071_1746226576397_0.7670782454030882", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.dd5ec49": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.dd5ec49", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.dd5ec49", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "e26e9eaf899c01881f5543b0a5becbf1298a98d3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.dd5ec49.tgz", "fileCount": 113, "integrity": "sha512-L76yd/i9RHUBRTM+KmFqmYoa6mQbtqgUZfoaWEJgERCu7SuiPPAbh77x/UoQTsVkQnhZ4f31mF6o7XEoVbuc0w==", "signatures": [{"sig": "MEYCIQCM7a9gcAMK+IfEt+RfcZBltqlsFo982ZufUEueXTEgYAIhAJk0xbr24lVT1Y1U8tyat77xO6b+yzY0Fn1gtWojkeE6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.dd5ec49", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.dd5ec49.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/74935e5d6f6da2751169a581be2fd0cd/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.dd5ec49.tgz", "_integrity": "sha512-L76yd/i9RHUBRTM+KmFqmYoa6mQbtqgUZfoaWEJgERCu7SuiPPAbh77x/UoQTsVkQnhZ4f31mF6o7XEoVbuc0w==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.dd5ec49_1746227490399_0.8882230986795905", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.e00d092": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.e00d092", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.e00d092", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "fb0fabe86890f780c19c4cb22c9b01e640d5e20a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.e00d092.tgz", "fileCount": 113, "integrity": "sha512-rKX6NgT49vd1/MJIG1uKPwrBskYniEE/mBFGWMNPzAaPoWGTNaOqS65C+9a4cGdwk5HEJspVXvxlqwOWU44mMg==", "signatures": [{"sig": "MEQCIAoC1+8n4vlee5IZCwkW6+lR6KcYLzX3TN5D3zXYjjfJAiAe7gbE0su9StxaJEcw9AKB98QL/M1ujR5Lq9waZLD8RA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.e00d092", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.e00d092.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/24bc39f8a340e6c28bf288c987e5caa9/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.e00d092.tgz", "_integrity": "sha512-rKX6NgT49vd1/MJIG1uKPwrBskYniEE/mBFGWMNPzAaPoWGTNaOqS65C+9a4cGdwk5HEJspVXvxlqwOWU44mMg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.e00d092_1746380203026_0.9573468549754109", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.473f024": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.473f024", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.473f024", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "55ab31f662cee23f8b805439a527daec4e671c99", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.473f024.tgz", "fileCount": 113, "integrity": "sha512-lUCgGRa8e9ckXpUU9W00pGkyHZVSzw0eITHFwz3nIyL5jEvjOHLoYdXvVWBdQz2grQ9qly38wdVyKAKOty+cOA==", "signatures": [{"sig": "MEYCIQC4LJ9KD9iv9JlcZGn7ebjJedSYj3RDBxvl01ziIX9zsQIhAOyfdruE2Nc6FMk9hxTKbBNCHfY7/BYiz8NsuoGB+z/l", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.473f024", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.473f024.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/08df7f0d203978d087b60036a391e045/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.473f024.tgz", "_integrity": "sha512-lUCgGRa8e9ckXpUU9W00pGkyHZVSzw0eITHFwz3nIyL5jEvjOHLoYdXvVWBdQz2grQ9qly38wdVyKAKOty+cOA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.473f024_1746441509129_0.013275704339378969", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.d38554d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d38554d", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.d38554d", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "56336625c2a8cc2791201b00da333dd45f2791ad", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d38554d.tgz", "fileCount": 113, "integrity": "sha512-isO1tvEE6/FMFy/7dmrqeYULwonEBoC2OuTInuhNzcBQtf/r8ufsSa/xpTlhcKHHSamlUpRasw4V0GJsIB0gBw==", "signatures": [{"sig": "MEUCIQD/Eil1gOvPu49wG4pwHguBMD5FphmsWSfzpJfXQg4UEwIgbMdhcUSdrZn5mbRH2pax/Jc9ITeE1iqHOBqdM9scSnc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d38554d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d38554d.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/effa552dfa69751d5faa693cd410b6ff/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d38554d.tgz", "_integrity": "sha512-isO1tvEE6/FMFy/7dmrqeYULwonEBoC2OuTInuhNzcBQtf/r8ufsSa/xpTlhcKHHSamlUpRasw4V0GJsIB0gBw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.d38554d_1746455865996_0.2965363945608026", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.6a1df6a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.6a1df6a", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.6a1df6a", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "1b1d26bef1dc2e2911c2e32cceb81353cd0e7a2a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.6a1df6a.tgz", "fileCount": 113, "integrity": "sha512-Nwctrd9WGeONfh+SrxX1MZBhkLKSXhK14kEktnOeZ1UYMNt3eFjf3sERP+2bmfBkdsQI+yU6xpLsXj+i3qI9iQ==", "signatures": [{"sig": "MEUCIQDZ01XE4SOUY9QPRKhjI6//epvx7lFo50kXDMEcf3g84AIgMfcce/f9GohK+gkVpjEfYgd+fG3B/ec0PH8aulw2uN0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.6a1df6a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.6a1df6a.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/766f4ae9d2c34f6b972f83143534979a/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.6a1df6a.tgz", "_integrity": "sha512-Nwctrd9WGeONfh+SrxX1MZBhkLKSXhK14kEktnOeZ1UYMNt3eFjf3sERP+2bmfBkdsQI+yU6xpLsXj+i3qI9iQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.6a1df6a_1746459462638_0.9005815371106691", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.5c5ae04": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.5c5ae04", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.5c5ae04", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "43f2104aaf95bb274b038350e72289a098440234", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.5c5ae04.tgz", "fileCount": 113, "integrity": "sha512-hYyQL6kmVoieyPzkXsShT64Vwi1Buu3zvCZKM0GLllbtkn8V5QGP/M+zR1UW6wWi+xN5EDjWWJUmlJHNSNQcRQ==", "signatures": [{"sig": "MEUCIQCXw8V2ZQpDV1yr+vOJbwJl+2biRm2v2SRnRMi71NUNlQIgSNtxBz+IgamYsAqNowV40ND8zmTDYRKqx8eLwzzYNiA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.5c5ae04", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.5c5ae04.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/23bd0e9d7c95c4a4aade301e218ce4aa/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.5c5ae04.tgz", "_integrity": "sha512-hYyQL6kmVoieyPzkXsShT64Vwi1Buu3zvCZKM0GLllbtkn8V5QGP/M+zR1UW6wWi+xN5EDjWWJUmlJHNSNQcRQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.5c5ae04_1746460036771_0.2541224635806407", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ed45952": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ed45952", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ed45952", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "ad2f6110adbefeafb141e1a13e6a8e2b6332c576", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ed45952.tgz", "fileCount": 113, "integrity": "sha512-RoORXcuJYgrxYCZAglZQLXM17PIzbAai7kLVaz402rhLH3pRvuIBZhZMdyebdeE32zW4NogfMKfy2aYttJipzg==", "signatures": [{"sig": "MEUCIQCcKPAIsN73d9AwmwOs8wBcPyXXdNPab6pJza2TTfuEhQIgfKTULX9qUzSi96MSBgrwVnzqxbDMp92btSrGDdLC4go=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ed45952", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ed45952.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/4d41e3d4d876e213756a4f7c116b02d3/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ed45952.tgz", "_integrity": "sha512-RoORXcuJYgrxYCZAglZQLXM17PIzbAai7kLVaz402rhLH3pRvuIBZhZMdyebdeE32zW4NogfMKfy2aYttJipzg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ed45952_1746529395724_0.5820698191954958", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.4f8539c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4f8539c", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.4f8539c", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "4873f2ee851cb50352c37f5fac46abf7fbe3acea", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4f8539c.tgz", "fileCount": 113, "integrity": "sha512-8p37VY5cLeKaJ1eoHeb4cNvWjCcMgRCrjCtKfKxSR9byw0qdWoaYs0i1YljQM72izjDL/PkoxpUWC/ofp9hZ/A==", "signatures": [{"sig": "MEYCIQDtdWij0MaJFw5CPpZsyca2nqoB1YyNPFF1y9OSrDKi6gIhAM41AziOel06oDsJEVIn4y24jzOCSu/+eih4CUsKBW+b", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4f8539c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4f8539c.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/2209f4aeeead04aa9c150e7cbbfa9f5a/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4f8539c.tgz", "_integrity": "sha512-8p37VY5cLeKaJ1eoHeb4cNvWjCcMgRCrjCtKfKxSR9byw0qdWoaYs0i1YljQM72izjDL/PkoxpUWC/ofp9hZ/A==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.4f8539c_1746538846161_0.45036076883757503", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.449dfcf": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.449dfcf", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.449dfcf", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "e8d03722c534d7b249cecff1aacdb0c981e744f0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.449dfcf.tgz", "fileCount": 113, "integrity": "sha512-r9tK6guqFfGtQL2azrEAclMEXG+oXCYNjNzjhOXabcp2xQ7caXDrRbupUeAjEUuDdTTqPRjyE7cMUOz8LxqJvQ==", "signatures": [{"sig": "MEYCIQCsijolaOgDPP4Uh+oEGah66X1zKQqE7qYYZ2BEdVuoPgIhAOpxTnhKaTpuuKC+iJwAvz0m/K8MTI6PXRFFjdVCs8hU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.449dfcf", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.449dfcf.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/6a3a7ff5e29ad920d643b8ea26065768/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.449dfcf.tgz", "_integrity": "sha512-r9tK6guqFfGtQL2azrEAclMEXG+oXCYNjNzjhOXabcp2xQ7caXDrRbupUeAjEUuDdTTqPRjyE7cMUOz8LxqJvQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.449dfcf_1746555032689_0.5174907472264056", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.d8c4df8": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d8c4df8", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.d8c4df8", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "3fbb65b975b43c2275b925324aae616a589ee08c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d8c4df8.tgz", "fileCount": 113, "integrity": "sha512-PTFGK7tb05kjG+Xv+BWZpYbwQNhLcGFbhYyfa9l2OJhwbxDVbj3szmvsnDzmHliRZTnlc/rz1lfwM2fwg35phA==", "signatures": [{"sig": "MEYCIQD3QlJdW77iJdKwTe1BnD2CYLup8pNx3snvQpoRXAhULgIhAPJ+T0AuqhxwxPev3JmL9uHKpVh+ZfdqkdXcGgcpzxy1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d8c4df8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036359}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d8c4df8.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/hn/k7g0_sh57112t0xtjxcjcm5r0000gn/T/50b80d8e7cc26fa32c84a8bd777f2207/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d8c4df8.tgz", "_integrity": "sha512-PTFGK7tb05kjG+Xv+BWZpYbwQNhLcGFbhYyfa9l2OJhwbxDVbj3szmvsnDzmHliRZTnlc/rz1lfwM2fwg35phA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.d8c4df8_1746628151856_0.9089036349860005", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.179e5dd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.179e5dd", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.179e5dd", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "d10cea8728f6373c1c2d950c4fb13a1b3a3b6098", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.179e5dd.tgz", "fileCount": 113, "integrity": "sha512-WaL64zmnIinc0sxA8mHWFRSKYvAqR7rZWCN4dU3762C7GfwiKyeuc5z0Dacc8bfKdTxRvqTj3sv/7c/HdM08fQ==", "signatures": [{"sig": "MEUCIQDttsvjcFCIXzbzofkMVVTVYeE8vEPkqCAdgCV3D65AIgIgdEZieMI3fn5PWnmCKKG3P3WraGLr+6C3t1VIhnCoc8g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.179e5dd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.179e5dd.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/7cdc330dae8265fb0adb057d5c602f5c/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.179e5dd.tgz", "_integrity": "sha512-WaL64zmnIinc0sxA8mHWFRSKYvAqR7rZWCN4dU3762C7GfwiKyeuc5z0Dacc8bfKdTxRvqTj3sv/7c/HdM08fQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.179e5dd_1746696910546_0.9459842299877295", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.17ca56d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.17ca56d", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.17ca56d", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "d6609614db4d8f74e3ca4261e0a4cdbbb475aa96", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.17ca56d.tgz", "fileCount": 113, "integrity": "sha512-EoyJueIE/5DVJZIBe2SmaJr2MFUz66AFQoYliQ954L2wGhTLGFhB26j1uNR+a29g0IJrxJL85wFSbh7DbeOGVw==", "signatures": [{"sig": "MEQCIFXbMWpwnbmn1eVBlKlBGGlZmHZvBU7XRfaZGxCqR04IAiB8UkA3n+6tYNVRPGwEJjyyM9lGhZcT6nX2A501vRMgFw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.17ca56d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.17ca56d.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/24891985746f2ee6d566c4e834cb49be/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.17ca56d.tgz", "_integrity": "sha512-EoyJueIE/5DVJZIBe2SmaJr2MFUz66AFQoYliQ954L2wGhTLGFhB26j1uNR+a29g0IJrxJL85wFSbh7DbeOGVw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.17ca56d_1746722270947_0.4003824015468589", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.62706dc": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.62706dc", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.62706dc", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "f56d608610e082fe19dee57e261b6e18a4145436", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.62706dc.tgz", "fileCount": 113, "integrity": "sha512-9hAfCXBd0hl+hoRp0V5Ejpq/i2cug45fOCvx04buwcb5ZZ5nJEmRFMFBQCzKXWp76c8fDxGhq5nnp9N3BNdcUA==", "signatures": [{"sig": "MEUCIHywMuXCDD/xC9um7cugYsHYQL36O5YRolfI7MPjC5cpAiEA7JvyZmm1WShzFN8JB3TGOd/qR1ySlfVdM4ujEoVtVyE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.62706dc", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.62706dc.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/7532438c5e2d9d372e0f7d2deb3b1764/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.62706dc.tgz", "_integrity": "sha512-9hAfCXBd0hl+hoRp0V5Ejpq/i2cug45fOCvx04buwcb5ZZ5nJEmRFMFBQCzKXWp76c8fDxGhq5nnp9N3BNdcUA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.62706dc_1746723284411_0.9216846571706261", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.56b22bb": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.56b22bb", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.56b22bb", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "4280a050f89a9030a068d53a7ed88d8e707f36d7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.56b22bb.tgz", "fileCount": 113, "integrity": "sha512-J9W4nnb4iSLtPijEzOLv6+GjtUZQSYpPtbzS5ScZexu9zd2I7yZz+i0juB3Mf0WWJIIXR2EvPIRl9YXhabB6sg==", "signatures": [{"sig": "MEQCIHfAq4gltsvLde3h7aPj8eNz7phblHYoi8kQzU6/TrVcAiBcVWGBBDooaL7XRPedNEurk2GqMswxWuIcn/FqlpOB3A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.56b22bb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.56b22bb.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/4b723cfcab23cffa31517eb4d2d28c00/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.56b22bb.tgz", "_integrity": "sha512-J9W4nnb4iSLtPijEzOLv6+GjtUZQSYpPtbzS5ScZexu9zd2I7yZz+i0juB3Mf0WWJIIXR2EvPIRl9YXhabB6sg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.56b22bb_1746736674368_0.9783803001230311", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ff9f183": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ff9f183", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ff9f183", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "42899c8a01cbeafde1690194bdb6342987c7d464", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ff9f183.tgz", "fileCount": 113, "integrity": "sha512-yp0oSssrKqIV6RXHrtqNYDZ0eklCX4xQJytspG2pU48qSa+vjsIGZ+tMr+sbZkL5+LNCXMzcmPqy5LyKzrKw/Q==", "signatures": [{"sig": "MEYCIQDrqiUz3ENz8enewM4sGzMtPnyQ6Y2yh5cHt7aNsSkPLQIhAPkviLISXR5d2HsU9kYtPUNm9oUGu3KGHxYYoNQyS4HX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ff9f183", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ff9f183.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/93a76489504da50542e138c54731a377/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ff9f183.tgz", "_integrity": "sha512-yp0oSssrKqIV6RXHrtqNYDZ0eklCX4xQJytspG2pU48qSa+vjsIGZ+tMr+sbZkL5+LNCXMzcmPqy5LyKzrKw/Q==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ff9f183_1746738265952_0.4011179033607828", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ae57d26": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ae57d26", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ae57d26", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "1e0b461251cac03f6787d0b64579006bdee03c79", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ae57d26.tgz", "fileCount": 113, "integrity": "sha512-MbNTiC9dbGq42phlta4SzqkBIM3/rel7XC1P70N7trJMbXRY4+mOpHxmXgGDo7/yjSma8JvvKI5ZEOwbGcpNUw==", "signatures": [{"sig": "MEUCIQD+P4zRULMm1u8VH+z++uQ87mpArczfZZxj+Qq0UmKXYwIgZlrCuWHx6JTY9qrmYYWm3VYqJxHUCvXrz9c+M1XRqDw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ae57d26", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ae57d26.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/e3839dabcf884076260815f2c7b6881a/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ae57d26.tgz", "_integrity": "sha512-MbNTiC9dbGq42phlta4SzqkBIM3/rel7XC1P70N7trJMbXRY4+mOpHxmXgGDo7/yjSma8JvvKI5ZEOwbGcpNUw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ae57d26_1746786125378_0.45953841024394726", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.2f6679a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.2f6679a", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.2f6679a", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "9e19d89e5692035cce97bd0e0db0acf65516cee6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.2f6679a.tgz", "fileCount": 113, "integrity": "sha512-6YQmusTj9GCGnZVa2s6SXBPgp3/3Fxi9jRXjAdvMVBWT0+m6x+gTlzYlnqbwuPr2YBH/2NRjjrQKhb2B3lh2pA==", "signatures": [{"sig": "MEYCIQCGZRfnOCVlP8UpJX5fm3p176DTV+yMt05Bz8HB0d7qHQIhAI/GRSJSuwjeKzAdD/HpcmJCI1b3CBdsk6SN3jVQbIfI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.2f6679a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034695}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2f6679a.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/7d46da9e2c2ec178845826e0934ba362/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2f6679a.tgz", "_integrity": "sha512-6YQmusTj9GCGnZVa2s6SXBPgp3/3Fxi9jRXjAdvMVBWT0+m6x+gTlzYlnqbwuPr2YBH/2NRjjrQKhb2B3lh2pA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.2f6679a_1746791976962_0.17661259954055808", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.47bb007": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.47bb007", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.47bb007", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "21eb6844834d7bfa03dd344939b9ad435df2b6e3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.47bb007.tgz", "fileCount": 113, "integrity": "sha512-J<PERSON>+PN+KN2IfbEBonaOniCsK5TTubBvPGN2g/HQg/aQE87urALfj+UTn9eJIYPLNBuvnClCnYBaeoA4WxHk6iwg==", "signatures": [{"sig": "MEQCICg0Z/ZykJtp/xxSLuvAYJoJel5Kw1AIF8/1TdBcEnF+AiBw/1jTY4aO9++0lXkf0jmu4/2q3hxaQpFpU6hnrJvaZg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.47bb007", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034695}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.47bb007.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/dd422f7f4bfe51e73305c53aed651fab/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.47bb007.tgz", "_integrity": "sha512-J<PERSON>+PN+KN2IfbEBonaOniCsK5TTubBvPGN2g/HQg/aQE87urALfj+UTn9eJIYPLNBuvnClCnYBaeoA4WxHk6iwg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.47bb007_1746795843212_0.14962689145015506", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.2d13998": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.2d13998", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.2d13998", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "8290985142640fa173eaabfb2bb61a31717f9905", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.2d13998.tgz", "fileCount": 113, "integrity": "sha512-MgGdYRFb9sHzqrkwgc/Y9ZmsNTXtHB1YTr6Wrm870OXu+DapJVpw5hqAZSzocgrCkDa32ywOSFSZHBCUV+CsCg==", "signatures": [{"sig": "MEUCIBQSUGwaxGhDoDJO5i9w80k+4GqZwl9recj3nsvM4A5kAiEA0qiPhp6AB1iNHjPXF8wARTE1PRsBTt71z19h4ymQ8QQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.2d13998", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034695}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2d13998.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/3df596d0468e8d5cb3212bafac8fe0c7/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2d13998.tgz", "_integrity": "sha512-MgGdYRFb9sHzqrkwgc/Y9ZmsNTXtHB1YTr6Wrm870OXu+DapJVpw5hqAZSzocgrCkDa32ywOSFSZHBCUV+CsCg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.2d13998_1746798503452_0.20153283184582382", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.6": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.6", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@4.1.6", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "7e45eb7aafec0406477a05403689198a9f062b4d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.6.tgz", "fileCount": 113, "integrity": "sha512-qAp4ooTYrBQ5pk5jgg54/U1rCJ/9FLYOkkQ/nTE+bVMseMfB6O7J8zb19YTpWuu4UdfRf5zzOrNKfl6T64MNrQ==", "signatures": [{"sig": "MEUCIGs4GyiraFFnhe4vpeggtcPo6Nk/40YVOngvhBcm38aNAiEAtshhiD+unauZm/DYLu+qUDhcGxiS/9A7UkiaGC1wxCA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034678}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-4.1.6.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/4095f19ae23a2895d284336d6b97ff73/tailwindcss-oxide-wasm32-wasi-4.1.6.tgz", "_integrity": "sha512-qAp4ooTYrBQ5pk5jgg54/U1rCJ/9FLYOkkQ/nTE+bVMseMfB6O7J8zb19YTpWuu4UdfRf5zzOrNKfl6T64MNrQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_4.1.6_1746799009082_0.15526695568053284", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.737994b": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.737994b", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.737994b", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "7a7f7953198145d34ab2a7f5764e77e4d2494af3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.737994b.tgz", "fileCount": 113, "integrity": "sha512-l+8vHmA0kVndbxmDCVR8ofC82L2yR6nuqPB66dKa3dfjFBbqzt1rpGlJxIr1nuEkYkZrmf+uLBc0iMzH/mnxFA==", "signatures": [{"sig": "MEUCICqgCmjIgJ6vW3WxFUGpVi1BFo7xKYHKc4EQYJ2MAH0wAiEA6OlQnkJP1aJLmVcHwd+rYJYXTLD8O1Emc4DbvM0fvX8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.737994b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.737994b.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/a37dfd6074af2562bfa29312dded27a8/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.737994b.tgz", "_integrity": "sha512-l+8vHmA0kVndbxmDCVR8ofC82L2yR6nuqPB66dKa3dfjFBbqzt1rpGlJxIr1nuEkYkZrmf+uLBc0iMzH/mnxFA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.737994b_1746808650956_0.8074018363917339", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.3386049": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.3386049", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.3386049", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "8d6c3feee092039053a67d5c1c20a9b684febcc5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.3386049.tgz", "fileCount": 113, "integrity": "sha512-dik/E+9qnzEWaxlmbwq/Xbn0munVluIfRydNp84rKtgZWY6QMfCBLoLg/ELMpFVhtrIT8XyXXgVsuazeRu+DQw==", "signatures": [{"sig": "MEYCIQD+4aA9OIsQhCc7PBCyxM5EixoJJ+hvTXaP9AgCFsNwmgIhAJkK8MZrV3pHCHG8DS5toYwWrVMRMPar52+Rkd8+wfcv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.3386049", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.3386049.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/44be1e42d382175702dab62dae559d3a/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.3386049.tgz", "_integrity": "sha512-dik/E+9qnzEWaxlmbwq/Xbn0munVluIfRydNp84rKtgZWY6QMfCBLoLg/ELMpFVhtrIT8XyXXgVsuazeRu+DQw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.3386049_1746821701451_0.02603924637136279", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.f0986ce": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f0986ce", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.f0986ce", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "530334f679e5ac5576c8e53f686a13d1cb3aab07", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f0986ce.tgz", "fileCount": 113, "integrity": "sha512-uIVGSGi/yod8CDDwOjxGTvIy453tIfhZV28Z+9y1LF1xdRoZWhT7jH5LU6lzQ02dlvqQC2OFbQgV/jPCL6SZzA==", "signatures": [{"sig": "MEYCIQCI5pEeiWnhP2B1pmU9qtvoAgWFLkOOVLXWjukOF8u6XgIhAO+ROMCFWrpd0dnIAV1S3gdyzpTET6V/3Bo+rSfWzWH9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f0986ce", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f0986ce.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/d327acfa7678fe8b169dcfd17e5ab9a0/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f0986ce.tgz", "_integrity": "sha512-uIVGSGi/yod8CDDwOjxGTvIy453tIfhZV28Z+9y1LF1xdRoZWhT7jH5LU6lzQ02dlvqQC2OFbQgV/jPCL6SZzA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.f0986ce_1746822103828_0.9833381373681269", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.0d975f5": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.0d975f5", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.0d975f5", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "9a25f3817916117f5739a5f1796c0c22775adb10", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.0d975f5.tgz", "fileCount": 113, "integrity": "sha512-pfSaNt54ZTMf7NEJuzp1CtQIk9rTsxcAzgSDHmuooiZNG81PjgiJf6yQuE0XZ+Xvc7mflpj8mwCG3SyyaqNZkw==", "signatures": [{"sig": "MEUCIHFhYNWu5a6959vCrnJvxfiRg5jOgrMi7FLbu29c41XhAiEAzy/FbbfqifR+GvKHEuioExHgDYMZos5Rw8g+7I5Nv1Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.0d975f5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.0d975f5.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/71ccf7022ec4e9570416d81e25fcc0ac/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.0d975f5.tgz", "_integrity": "sha512-pfSaNt54ZTMf7NEJuzp1CtQIk9rTsxcAzgSDHmuooiZNG81PjgiJf6yQuE0XZ+Xvc7mflpj8mwCG3SyyaqNZkw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.0d975f5_1746876546199_0.6735499729864125", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.19e2b29": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.19e2b29", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.19e2b29", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "25d4212f080771ae7cde7c06b58111b95c4ea974", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.19e2b29.tgz", "fileCount": 113, "integrity": "sha512-/IFTar/5FWv00/Nr6a9uu4RN9XPP8rsVAPlXhua3wMDTrmJ2PSDRFLgTSIeO9ttcC0RTUu6vGQhHepAAGNpOWA==", "signatures": [{"sig": "MEYCIQDtbgymO1kQgzgBmN6GsoUIdsxQan6ZU/PSBi2P0H4UvwIhAKTIQeXKx2GgsYEtPVXlDM5XU4canObPdHKB1ZjWN78U", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.19e2b29", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.19e2b29.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/cd3afeef6ef2d977feb1c19544f7d7f9/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.19e2b29.tgz", "_integrity": "sha512-/IFTar/5FWv00/Nr6a9uu4RN9XPP8rsVAPlXhua3wMDTrmJ2PSDRFLgTSIeO9ttcC0RTUu6vGQhHepAAGNpOWA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.19e2b29_1747041048410_0.7433690008834366", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.5688f0a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.5688f0a", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.5688f0a", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "8913156ee11e7a0938fa793579b5307e54a5273c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.5688f0a.tgz", "fileCount": 113, "integrity": "sha512-PyoW60ZO6sxEDJDdmXzMPS7K2oa7noSOy80+S82ui+BcGx3+eARpPQI2nhffYieIfw628PIrOjNnF0NZ671smg==", "signatures": [{"sig": "MEUCIQDAXMZnJq/xm8zjruGYHCO1Qwg0t7tzC1hdGSCtaO89CgIgMRlM8Ie6WlD2KA+X2m5EX0BqyM7xjpesZry3uaYMogA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.5688f0a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.5688f0a.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/e26ad44b246be7e1a0792d52c3233221/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.5688f0a.tgz", "_integrity": "sha512-PyoW60ZO6sxEDJDdmXzMPS7K2oa7noSOy80+S82ui+BcGx3+eARpPQI2nhffYieIfw628PIrOjNnF0NZ671smg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.5688f0a_1747048663561_0.6833345076045392", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ba944ca": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ba944ca", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ba944ca", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "155a9fbf5fe1444980a55dfc1d4162dd16c248a2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ba944ca.tgz", "fileCount": 113, "integrity": "sha512-LxOoUXfmL/J1dcCGe3WD6s+TzXov3XrQiaRGDlekpBB4HOVCIBamYOulxpD40q/Y0GwTKV0e+kjezlXa5WoqdA==", "signatures": [{"sig": "MEQCIF9uLYK/DREA4g/GJ36rGhh8pf7ZGPsjW8OylOtGQmZsAiBKRyCRjmqC6H67rO7sE53UuL/HhrLXaZBuiSSkaX5jKA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ba944ca", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ba944ca.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/9814b9755fac459fc6a28671fa08efb2/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ba944ca.tgz", "_integrity": "sha512-LxOoUXfmL/J1dcCGe3WD6s+TzXov3XrQiaRGDlekpBB4HOVCIBamYOulxpD40q/Y0GwTKV0e+kjezlXa5WoqdA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ba944ca_1747056385411_0.5539089265276094", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.4fba87b": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4fba87b", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.4fba87b", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "6c71ff670f32f67ffabdec0c113e8070a4677f56", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4fba87b.tgz", "fileCount": 113, "integrity": "sha512-g/yiO9tOP4TjVqIaCg7roMef4T+gucT1YLLX6hDqHJBXfsRiAstvWXnTIC9E+2kIxW1aJUVB8pTqXyf8aekOXQ==", "signatures": [{"sig": "MEYCIQDIonEFqF2VwIXdrOufPW/BUd7V2lmwtg4VEDuhJOYWMgIhAK4b99GwnZZEAEI7EWX+yxNfqAZJqVFViR+qZ9CqYqdI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4fba87b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4fba87b.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/43c9ccf71f60277c2a7012a0b6435bd5/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4fba87b.tgz", "_integrity": "sha512-g/yiO9tOP4TjVqIaCg7roMef4T+gucT1YLLX6hDqHJBXfsRiAstvWXnTIC9E+2kIxW1aJUVB8pTqXyf8aekOXQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.4fba87b_1747058363839_0.42545959342365514", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ef2e6c7": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ef2e6c7", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ef2e6c7", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "8f1cf4ead7fe9e11283cf2f846c5c6cd9802b393", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ef2e6c7.tgz", "fileCount": 113, "integrity": "sha512-8s8aHB4eYnBErtPGKxBl5R8ZAT2k0nYi+QGy7QtG1L9nuKJX3CwMsizwvlhUuP5OUPkErGL+2tHQ2sk3Rkpqbg==", "signatures": [{"sig": "MEYCIQCIn4r48hxRItS0vWketVltibDg5XsqoR/W2MtkjsUQQgIhALGPqyvUEv5BdUevWJ5Q9phXB8pcETkUo+8vji6ldUhw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ef2e6c7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ef2e6c7.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/37026827dd71d9a9f8cf0e0a927e01c5/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ef2e6c7.tgz", "_integrity": "sha512-8s8aHB4eYnBErtPGKxBl5R8ZAT2k0nYi+QGy7QtG1L9nuKJX3CwMsizwvlhUuP5OUPkErGL+2tHQ2sk3Rkpqbg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ef2e6c7_1747139351434_0.7589781917204912", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.498f9ff": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.498f9ff", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.498f9ff", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "b699b46aaad627924cffbb6a5fffdbd7bcc41c57", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.498f9ff.tgz", "fileCount": 113, "integrity": "sha512-scCTbn0Ykc9+rKstDOdrTha5xn9KY3cW+LJU63LS7JvKAjDVhZIJddQ/N8LLjw0OgGCZ3jpnYNWT51ACHoFScQ==", "signatures": [{"sig": "MEQCIATv7x66P2qXhxP2GId5ZXXCoXlIAY60CGZxo1Q5+p+bAiAzeuEpURxFFhes6qhJlD8itkOUzUfIsGeHGtW0xfZ6fw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.498f9ff", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.498f9ff.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/1b03c3feb1fd3fa5688dbb6b2985e085/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.498f9ff.tgz", "_integrity": "sha512-scCTbn0Ykc9+rKstDOdrTha5xn9KY3cW+LJU63LS7JvKAjDVhZIJddQ/N8LLjw0OgGCZ3jpnYNWT51ACHoFScQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.498f9ff_1747151360771_0.6373772536606064", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.4db711d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4db711d", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.4db711d", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "a19adb3931df7690dc6f415ce482d2ff6a8e4c07", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4db711d.tgz", "fileCount": 113, "integrity": "sha512-QoXgcAfUY78wMzjRIzTJ+huwuHYZXTJmRTeFXc8kpcd+lA1Rzig/Kvco/xRh+RBERzd2XVVBLD5Bv1Z3ZMikJQ==", "signatures": [{"sig": "MEUCIQDk8mEAwvWQtnf9t7NSWYLhAJCwdrmZv9OQ4EL2xJ7k2wIgedkGh3LY2HLdUBYEriK0eLmFHKBd3A8pXUtTCS4BW/M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4db711d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4db711d.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/2cebc9d09a66154d587a24f9f608fa64/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4db711d.tgz", "_integrity": "sha512-QoXgcAfUY78wMzjRIzTJ+huwuHYZXTJmRTeFXc8kpcd+lA1Rzig/Kvco/xRh+RBERzd2XVVBLD5Bv1Z3ZMikJQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.4db711d_1747216161200_0.1228639747078153", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.e57a2f5": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.e57a2f5", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.e57a2f5", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "44d346bd89756f469606f6089e3d3f5759baf172", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.e57a2f5.tgz", "fileCount": 113, "integrity": "sha512-UzdvpC/EOrpgsUX6QNXIdORzPrGOeOSbef08QMyofbeFkJjy9GslbHkhTqaqdfIwYvO+ItTAaxKwLYJMmJZxcQ==", "signatures": [{"sig": "MEYCIQDW65a7B18G97ATkPS8n4jq0Ok0teC1LAsmhkjoT6S3dAIhAN2iGZzEus5U1UCFTqB0fBd1/QZjtk0AJWyGxoOlyfoC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.e57a2f5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.e57a2f5.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/e981ec66867ac765bb8d8ba018ac3954/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.e57a2f5.tgz", "_integrity": "sha512-UzdvpC/EOrpgsUX6QNXIdORzPrGOeOSbef08QMyofbeFkJjy9GslbHkhTqaqdfIwYvO+ItTAaxKwLYJMmJZxcQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.e57a2f5_1747227699868_0.6718791398457227", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.f3157cd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f3157cd", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.f3157cd", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "726ba8f25da713c04ef2e7cf9251cfb1b5a654be", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f3157cd.tgz", "fileCount": 113, "integrity": "sha512-AanOvCPVd8jHMNYm3vsBDU289b1TjfSgH2f62+a9vv10/ldAvtAqeTv6g0OKjMKq9QQakhiodVvR7AR/+M6TvA==", "signatures": [{"sig": "MEYCIQDJExQXmyBo8ymhjN45yZJIF2vkqAl5RFNCvlkxFxhVLAIhAKPCRGWaqHQatlaNCi+9TlNQDOuLtgJYjvdlBcEniX0d", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f3157cd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f3157cd.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/2fad5908cafceca8c3cb1a86644f5c4b/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f3157cd.tgz", "_integrity": "sha512-AanOvCPVd8jHMNYm3vsBDU289b1TjfSgH2f62+a9vv10/ldAvtAqeTv6g0OKjMKq9QQakhiodVvR7AR/+M6TvA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.f3157cd_1747307330739_0.6031908150390477", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.6fb98d2": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.6fb98d2", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.6fb98d2", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "19afa61e2a6ccf29f19284260462056f946730cc", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.6fb98d2.tgz", "fileCount": 113, "integrity": "sha512-WVRqoA0NseiNUHcaZKBsBzFz3Ty9T89MWqr2zYR8FiSd+5Sj4K5W1sHEmmp+xZJ+n8Nho42PA7T7AG/ZOFdqQg==", "signatures": [{"sig": "MEYCIQDHmujMXNXW/FYJxiKLZo0l2BGvVtD32Dwa+TSEA/owxwIhAMPcrBzAA78iGqnQhKeVucwqTsFX7fjelaVYmZJAW4LT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.6fb98d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.6fb98d2.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/f396b67b357db13046cbbdeb4dc980d2/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.6fb98d2.tgz", "_integrity": "sha512-WVRqoA0NseiNUHcaZKBsBzFz3Ty9T89MWqr2zYR8FiSd+5Sj4K5W1sHEmmp+xZJ+n8Nho42PA7T7AG/ZOFdqQg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.6fb98d2_1747308722323_0.7242128230341276", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.1ada8e0": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.1ada8e0", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.1ada8e0", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "067ba2d6fbbb0032025ea816953e665bdf0cf51c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.1ada8e0.tgz", "fileCount": 113, "integrity": "sha512-ANJIax3WiwIOEpciK4aHphscEFIWrM/IPnC/C4lAcam5ijS9gxo/cDTI+E9qNTWzNvlia4mRiaCKPVzSQ7uVvQ==", "signatures": [{"sig": "MEUCIEz309qzB+SgjJhYoUse7uoQRTEext4Zp1pQUeW45r52AiEAozLO7aalZVd9E5dPxx6RVEKBv3bpRvAzbfHvxryYFZQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.1ada8e0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.1ada8e0.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/9ba5f52a6b0b03abad685ddea6d06d00/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.1ada8e0.tgz", "_integrity": "sha512-ANJIax3WiwIOEpciK4aHphscEFIWrM/IPnC/C4lAcam5ijS9gxo/cDTI+E9qNTWzNvlia4mRiaCKPVzSQ7uVvQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.1ada8e0_1747308757549_0.7711705111125726", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.bf591fe": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.bf591fe", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.bf591fe", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "54cccaa3e4d1e7f13013884716e4842870a7323a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.bf591fe.tgz", "fileCount": 113, "integrity": "sha512-q8jvr2/6FfxNWzkTNet/F1AYSYCCNvegHg6y4SbREBkZF99+NsX6j/e8a7gj6D33iTsiSzCZBvlLXwfApnpWlQ==", "signatures": [{"sig": "MEUCIQDCsLyHqDTHDeahr0HKtDTgJRP/oWFH2/m3jriebFd6qgIgCk/d5gPZYTZwUB0nesNxNzRy3CpMxX/RAnRqRjk+mQo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.bf591fe", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.bf591fe.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/840873cca9437e8ccc962f18329a7891/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.bf591fe.tgz", "_integrity": "sha512-q8jvr2/6FfxNWzkTNet/F1AYSYCCNvegHg6y4SbREBkZF99+NsX6j/e8a7gj6D33iTsiSzCZBvlLXwfApnpWlQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.bf591fe_1747314022737_0.5368881245967021", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.74e084a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.74e084a", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.74e084a", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "09ee6096a4ca6555e87db66dbcb9396f6dd54123", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.74e084a.tgz", "fileCount": 113, "integrity": "sha512-CnaLgiNJEZnhlzjJgp7QwcZs6DtWNvxt/TcOYWYyiy1i+OI6PjglIdV/fTHrcU79UlNBnQnLU0arS/uonWVpyw==", "signatures": [{"sig": "MEUCIQDHqXDVln6HmssoX5iSO71JyLCpJD7t5rEQo68TZkQ1ZQIgI6Max8PZkuCtLgdA4wWWMxcbjPwJwk1clQX7fKfOwFk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.74e084a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.74e084a.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/acf6edd2deb929037282a9c56c773c2e/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.74e084a.tgz", "_integrity": "sha512-CnaLgiNJEZnhlzjJgp7QwcZs6DtWNvxt/TcOYWYyiy1i+OI6PjglIdV/fTHrcU79UlNBnQnLU0arS/uonWVpyw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.74e084a_1747316386986_0.009459686022111224", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.7": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.7", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@4.1.7", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "79b41b0c8da6e2f1dc5b722fe43528aa324222d5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.7.tgz", "fileCount": 113, "integrity": "sha512-ANaSKt74ZRzE2TvJmUcbFQ8zS201cIPxUDm5qez5rLEwWkie2SkGtA4P+GPTj+u8N6JbPrC8MtY8RmJA35Oo+A==", "signatures": [{"sig": "MEUCIHuRadZMDQUffwOiyZrsNIt9+WHYfNI1NCiNRVg8ADPoAiEAue4VdVoMa28UFjdx+86boUWmBPOGyeE5bfEm2jhntx0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034763}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-4.1.7.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/ffe659cd5b4427ce8c34ce7aa6ad4402/tailwindcss-oxide-wasm32-wasi-4.1.7.tgz", "_integrity": "sha512-ANaSKt74ZRzE2TvJmUcbFQ8zS201cIPxUDm5qez5rLEwWkie2SkGtA4P+GPTj+u8N6JbPrC8MtY8RmJA35Oo+A==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_4.1.7_1747318264215_0.42615628038212705", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.d69604e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d69604e", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.d69604e", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "4b8ee583911feb35875be4e089de37969f0bea38", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d69604e.tgz", "fileCount": 113, "integrity": "sha512-gGph1uSVhsUCylxsYpuAEZ0h6xsBgbZVMaJMma3LCbWosgz8x7yRvCP3kysNn84+Cjp337KyDkAMyqO3CcNpZg==", "signatures": [{"sig": "MEUCIQCwiZBZeChasGy5mpSlCgNvBWfQaXEULH8rgFlcRFEFBQIgE4SSatC9DquZ2lT4eqMaRapOObKaTddEGTNuh3NgB6c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d69604e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d69604e.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/9bbcf6b792b5f3c094c577c2281a4bf4/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d69604e.tgz", "_integrity": "sha512-gGph1uSVhsUCylxsYpuAEZ0h6xsBgbZVMaJMma3LCbWosgz8x7yRvCP3kysNn84+Cjp337KyDkAMyqO3CcNpZg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.d69604e_1747326135402_0.8239973759332091", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.c7d368b": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.c7d368b", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.c7d368b", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "4cbe60822ae9da6b93d2101232b34ab3d78ee692", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.c7d368b.tgz", "fileCount": 113, "integrity": "sha512-yV9JuFdTCWL6xyeSxFIGUpUo+lfosmQOHoi1/Ah1NsULenVBe+IlXc5iCiuzjPb4dX/34CIhWi8ZeU/Z+9FtCg==", "signatures": [{"sig": "MEYCIQD+PO8Nn3j+FJREpsdJAjytynw8N1bZhWFRYht9hyrAZwIhAJYtXsOeT+8utirxairRm6nADppqV7b8UhsrHxCCPtNI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.c7d368b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.c7d368b.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/c3b0ead8aace74215f1e5b967ca96e2c/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.c7d368b.tgz", "_integrity": "sha512-yV9JuFdTCWL6xyeSxFIGUpUo+lfosmQOHoi1/Ah1NsULenVBe+IlXc5iCiuzjPb4dX/34CIhWi8ZeU/Z+9FtCg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.c7d368b_1747393298556_0.7368585249186765", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.71fb9cd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.71fb9cd", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.71fb9cd", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "db0e86a3c18295540fa4ee7c29d4b5af6a95ffe4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.71fb9cd.tgz", "fileCount": 113, "integrity": "sha512-zMdH074kOxEqTKLPTQ6pRM9A+Uh2FwnTg8dJ4aPpve6TCKDIFNvHL9+P9g1KOQ5C6TOeAlvSX7YrTxGgGRrHMA==", "signatures": [{"sig": "MEUCIQCoYhQi90q8cm4B3yGgSlV+FIdNDprPVg0M5We6LJ5G8gIgSLjovDgZC8OZ+85bP7Ayd4y+atMf1oj0s27V7TAjiZ4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.71fb9cd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.71fb9cd.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/56230e78c701a9095eb23712b22167d7/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.71fb9cd.tgz", "_integrity": "sha512-zMdH074kOxEqTKLPTQ6pRM9A+Uh2FwnTg8dJ4aPpve6TCKDIFNvHL9+P9g1KOQ5C6TOeAlvSX7YrTxGgGRrHMA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.71fb9cd_1747652122745_0.8975895802856975", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.a42251c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.a42251c", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.a42251c", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "14817d4635d45546edbb89aafcc776108e159c80", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.a42251c.tgz", "fileCount": 113, "integrity": "sha512-r24+kZOEgQwZrm7uChh0AREe4kaVdp+IOJr1EgZkCBDPAZOX+/29/U/Y8UiksnBPjIX4rN+ZgiADwRD1SdnKzw==", "signatures": [{"sig": "MEUCIQC2dX2ujZpVxvRcRDRF3QaoIWwVO+dverlDJdGIy0pibwIgRb+FOGaRdU4PYXHwPkBlqcAqKBMEIQX10wDoR0aQUQs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.a42251c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.a42251c.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/1e7ac534fd53147d4bb1e307074dffb3/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.a42251c.tgz", "_integrity": "sha512-r24+kZOEgQwZrm7uChh0AREe4kaVdp+IOJr1EgZkCBDPAZOX+/29/U/Y8UiksnBPjIX4rN+ZgiADwRD1SdnKzw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.a42251c_1747666231861_0.33376093818145525", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.9df5ba7": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.9df5ba7", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.9df5ba7", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "00e1bab62db66a3d2e516daa10152cb2aed6fa38", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.9df5ba7.tgz", "fileCount": 113, "integrity": "sha512-AbEu/uLxs8RHyobDS5Ar5zwCsSCP5Szi1mEwqzFXJCPahLOAPRoAPE4uRvrrc3/IZcuY787KNCNstYWNJpAjrw==", "signatures": [{"sig": "MEUCIQCdZzh5Sv32IrZqomKhHO50R9A7QT7KEllz7gY3WXSFLAIgfLs28CfhSJKsJMHUCZJHesDSQjjQzahPMRByOEs43NA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.9df5ba7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.9df5ba7.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/110cc64f383a3b26faa858c5e92ddc4b/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.9df5ba7.tgz", "_integrity": "sha512-AbEu/uLxs8RHyobDS5Ar5zwCsSCP5Szi1mEwqzFXJCPahLOAPRoAPE4uRvrrc3/IZcuY787KNCNstYWNJpAjrw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.9df5ba7_1747668969975_0.36193633177322004", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.7013ca3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.7013ca3", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.7013ca3", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "bfb71458f2a5ea6da3057db52384500dd8571148", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.7013ca3.tgz", "fileCount": 113, "integrity": "sha512-7nhaNMKvyzA1camV2PQciQ0G8YYq4oNs39o0Vh0RMxE4HCmhLrPHvS7Ur0qqqTpMF0JCDR6t27xg5D61OJFVFQ==", "signatures": [{"sig": "MEUCIQD2dbWofzoj5r5H2DSfhmJfTxGXg+gEcW4rQxHZz/wDpgIgFeZU8sVt3RwTocUekIh+PA3m4j8yV4HV+gJb99lHyv4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.7013ca3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.7013ca3.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/6f432cfec1f0e1d51eb196456c5e1a6d/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.7013ca3.tgz", "_integrity": "sha512-7nhaNMKvyzA1camV2PQciQ0G8YYq4oNs39o0Vh0RMxE4HCmhLrPHvS7Ur0qqqTpMF0JCDR6t27xg5D61OJFVFQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.7013ca3_1747682909244_0.5889481843533131", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.24ed64e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.24ed64e", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.24ed64e", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "ecf364cb819674c01b3570b06df4c0e83e512404", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.24ed64e.tgz", "fileCount": 113, "integrity": "sha512-iBjA+FWihQYObp8I8RHpOHqgTLul9ym9nFohEnstq2CO5OqJB4ssYHBxb+KVHK+uI5hGeR6/A/YyFx358NHkHA==", "signatures": [{"sig": "MEUCIAZhD9B/yWtAThkwFWlbJRPcw+23CuFW6ZukQbFbuhQGAiEAo1daCQmSDlq4gVgBiEsrXepPC8IHcr9rmwEw60iOGg0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.24ed64e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.24ed64e.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/a37de30e328339d9aafaafa763f68976/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.24ed64e.tgz", "_integrity": "sha512-iBjA+FWihQYObp8I8RHpOHqgTLul9ym9nFohEnstq2CO5OqJB4ssYHBxb+KVHK+uI5hGeR6/A/YyFx358NHkHA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.24ed64e_1747685808057_0.6989481941335702", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ed3cecd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ed3cecd", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ed3cecd", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "aec16464c3e557e0c923acdcd55f5dab081c14d6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ed3cecd.tgz", "fileCount": 113, "integrity": "sha512-wNYaj97gM3R66iriYEj25UzxognEUTIAHc4nZo05j7tcJTNTZFfq1+d6pNAekneQZMSQmZ7W/wE8CxtTg3k5wQ==", "signatures": [{"sig": "MEUCIGD0TfdW2J3ZEPjwxudyNVQ9FxLr6ZaOpJabUqOxaTiEAiEAyHZphTbY5soVGpV70yNMutDrfFyTQqhXcoBiQ9cRdYE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ed3cecd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034824}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ed3cecd.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/bbb314f3da76afdc3b9a1f586ed86061/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ed3cecd.tgz", "_integrity": "sha512-wNYaj97gM3R66iriYEj25UzxognEUTIAHc4nZo05j7tcJTNTZFfq1+d6pNAekneQZMSQmZ7W/wE8CxtTg3k5wQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ed3cecd_1747995874387_0.05754736486639733", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.9cb3899": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.9cb3899", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.9cb3899", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "bcb454c58b4c1b555d535a033087f173e8144eeb", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.9cb3899.tgz", "fileCount": 113, "integrity": "sha512-LEYXncYqpKgJ5vHe5GlxVeE95VOOzfqblh22/aPK89kOBVn9z8HybVYqf5LLVEiY0SpEY6/JefZebHbYtDYD3w==", "signatures": [{"sig": "MEQCIHlNK3eWgQqMvBcQ9D7X/hwWQ7dvnS/jbN9KgqRP/IuKAiAsmg4CIU0e0mSMNBy8QOJZONRzpcmNudSZbYYMFy/5nA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.9cb3899", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034824}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.9cb3899.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/79913698bdf725a4c4115a3cbca4f297/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.9cb3899.tgz", "_integrity": "sha512-LEYXncYqpKgJ5vHe5GlxVeE95VOOzfqblh22/aPK89kOBVn9z8HybVYqf5LLVEiY0SpEY6/JefZebHbYtDYD3w==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.9cb3899_1748014160785_0.8293105057945029", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.884f02c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.884f02c", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.884f02c", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "909f17ba4ac0d0a0ac5f6de07222f1482cbb1fe9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.884f02c.tgz", "fileCount": 113, "integrity": "sha512-wYzk3B1rW4gCDerq9RI1fuDptflewcX1nGebpOP/OsU48EayVMXY7xtxbCFOKrSAu8ERqVmlB4NL6UN7GkOWog==", "signatures": [{"sig": "MEYCIQCLdtuB/i12EL1X+A35hxPUA1DV4T9591xGoeKyIyf7xAIhANTEPgBnh9gyl+O7goz6Ae3rmrj9hzds+DI08yQgwTBt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.884f02c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034824}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.884f02c.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/0aa727220b1cba69d9a6406c4b0282fe/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.884f02c.tgz", "_integrity": "sha512-wYzk3B1rW4gCDerq9RI1fuDptflewcX1nGebpOP/OsU48EayVMXY7xtxbCFOKrSAu8ERqVmlB4NL6UN7GkOWog==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.884f02c_1748030846832_0.7366361658505096", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.8fcc633": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8fcc633", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.8fcc633", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "c599685ffd78ef3b1c1eca094f0978906411f1e7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8fcc633.tgz", "fileCount": 113, "integrity": "sha512-1jxWt8Qki7CCHYTNc5fPAJNNQd5naTE+tlCbOSPpPfSf8bVQ8gMdtpwpo32yHuR0OaPbZencumWJ2ZA7o0/UKg==", "signatures": [{"sig": "MEYCIQCM8HXVDVSNzckJUXZDMt51BiGdeuU3U0mA+3eQzgZD0QIhAJVbk32HF5gZW98JcsQEl2VVN843sGNsX0PXz0iVa4N1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8fcc633", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035082}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8fcc633.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/b454ddac2b5350fff7e82606d4e1b402/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8fcc633.tgz", "_integrity": "sha512-1jxWt8Qki7CCHYTNc5fPAJNNQd5naTE+tlCbOSPpPfSf8bVQ8gMdtpwpo32yHuR0OaPbZencumWJ2ZA7o0/UKg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.8fcc633_1748037979211_0.31045471650331913", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.5d4e8f0": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.5d4e8f0", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.5d4e8f0", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "7d39e0575c355011f7f228c52ec7aa6c7a306390", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.5d4e8f0.tgz", "fileCount": 113, "integrity": "sha512-D1jcl6kHQeoItUQeMH0vlFghzDp4zgWsVGBljKJB+DQVHVcVx6hrmtatLMUbDWto/1Srlcc+oKDLGp9rTWC6ew==", "signatures": [{"sig": "MEYCIQDNEkib3kFAW82Gf6l8ZXRfKRsIT6DMI1A8K4j7dxtoRQIhAOXBNSUyaTD9uYtnwdkyPOGeZFAuRuVYz2gCnamTpWJK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.5d4e8f0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035082}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.5d4e8f0.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/e08f647e57754525a55b6a379b61b759/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.5d4e8f0.tgz", "_integrity": "sha512-D1jcl6kHQeoItUQeMH0vlFghzDp4zgWsVGBljKJB+DQVHVcVx6hrmtatLMUbDWto/1Srlcc+oKDLGp9rTWC6ew==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.5d4e8f0_1748037998032_0.4387853545064355", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.5131237": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.5131237", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.5131237", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "3a5b795d6be50145b54a92c55858d9c43ac7ec6f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.5131237.tgz", "fileCount": 113, "integrity": "sha512-84lpWLY18DcNP8uVYNsLCCwNynjj/lO20LmZnebBDCUBbnRuUH43V1e1jmfKGsu1qqfw0h7DkOh/8PURD1HHBw==", "signatures": [{"sig": "MEYCIQD9zJLt0g6dudbpLLjL9Uj1iFYZneU4PHHfizUQs4CDuAIhAOXO7A1sFDbT7cYDyLQ2Mhidy1vicsaYxp8NRBEbh1Xd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.5131237", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035082}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.5131237.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/5e15c20b590b6a25139b362fd5de6acb/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.5131237.tgz", "_integrity": "sha512-84lpWLY18DcNP8uVYNsLCCwNynjj/lO20LmZnebBDCUBbnRuUH43V1e1jmfKGsu1qqfw0h7DkOh/8PURD1HHBw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.5131237_1748270531214_0.052271076771376324", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.1d4c263": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.1d4c263", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.1d4c263", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "8f351ebf08d867624b9012ea155798181599b277", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.1d4c263.tgz", "fileCount": 113, "integrity": "sha512-JxZmx0ls3ga6l5KXRPZdCpqaDoYmVmYto898zM98zus2Kc3NEu2t9YUuWeCw1cP8rFmJXAZkLGO9tyV60ZqSmQ==", "signatures": [{"sig": "MEQCIDuGGsSRlztaYlEMF3GRiOjwobyFJEwBoBiD0AqrrhIrAiAFRcjlgY3hO+BCjlM3tBKzbO0q3c9TSJr48Tvqb/N0sg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.1d4c263", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.1d4c263.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/347ff3cec9cb0ee52dc50a8aef7e9cc5/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.1d4c263.tgz", "_integrity": "sha512-JxZmx0ls3ga6l5KXRPZdCpqaDoYmVmYto898zM98zus2Kc3NEu2t9YUuWeCw1cP8rFmJXAZkLGO9tyV60ZqSmQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.1d4c263_1748278823719_0.23233176136434852", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.a37f6ba": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.a37f6ba", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.a37f6ba", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "d3731fe1285a3046c042b5422ac0be8e00e0dbc9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.a37f6ba.tgz", "fileCount": 113, "integrity": "sha512-9mk/2bjlhbY5b6je+iPMditGSKVxEU/+DnPaSlcX5lJlO616bxWA0IK9HAJrLNCj2VX3PMI+JSfUO1xRTETfdw==", "signatures": [{"sig": "MEQCH1efDlQKyHqi8enKCTnfSEkMAhw5U/GgSQQb0Dof2TMCIQDV+0N5y+/u7jdzfXzDph42blGVS8mj+6ZfeK1H6SUsNw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.a37f6ba", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.a37f6ba.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/66f25bde7bc68765f111895d2bf0bf9b/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.a37f6ba.tgz", "_integrity": "sha512-9mk/2bjlhbY5b6je+iPMditGSKVxEU/+DnPaSlcX5lJlO616bxWA0IK9HAJrLNCj2VX3PMI+JSfUO1xRTETfdw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.a37f6ba_1748337669010_0.6680871830555595", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.58a6ad0": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.58a6ad0", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.58a6ad0", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "df48b9907f4dcc59a3ed75a450a40724f04f7f33", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.58a6ad0.tgz", "fileCount": 113, "integrity": "sha512-ZBGo0j1XLBHAeRqHIRKmZNYtzWDBxixmWX61ixqDjVCtDfDcox60og347M5zCWAiZQGGVxJpgJhYMt3yzHZPNw==", "signatures": [{"sig": "MEQCIF2SJ9NSWSKqXlygiSMrXqdcS4oTmsOOX/Ot55vS0crAAiAEgYavvfVHdMTJCx07i/fG/3zs3rGf0yDrE1ZyKm76cQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.58a6ad0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.58a6ad0.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/0aac74fbb543cff0746f0efd38f228bd/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.58a6ad0.tgz", "_integrity": "sha512-ZBGo0j1XLBHAeRqHIRKmZNYtzWDBxixmWX61ixqDjVCtDfDcox60og347M5zCWAiZQGGVxJpgJhYMt3yzHZPNw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.58a6ad0_1748366143686_0.3364820144355978", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.4bfacb3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4bfacb3", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.4bfacb3", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "0422647b29896fa408db27268a59cf3493329d0e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4bfacb3.tgz", "fileCount": 113, "integrity": "sha512-BImlpUa3ijrdkEn6fwehx/LaKXxP32faQ3jiq3573K6f8WaScMxYKO2qMEIxsDXDLDKxSzTRFce2TgHqdGR0Sw==", "signatures": [{"sig": "MEQCIHE/XGR6qM7xsPDs9scgRLqx/Q1aMJ3JfbJJub96cVuyAiA+Gb9P35GBZkUEjO8pefG0RP0qw4LPeKTh5HFH0Dn8Nw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4bfacb3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4bfacb3.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/0c08b256d7764aa13f1ee747beca5f06/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4bfacb3.tgz", "_integrity": "sha512-BImlpUa3ijrdkEn6fwehx/LaKXxP32faQ3jiq3573K6f8WaScMxYKO2qMEIxsDXDLDKxSzTRFce2TgHqdGR0Sw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.4bfacb3_1748370460936_0.3405277886826843", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.193eb84": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.193eb84", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.193eb84", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "e664e456b8441976884e12667f53af19fb0a592c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.193eb84.tgz", "fileCount": 113, "integrity": "sha512-xbVZtYz8bSGPBG8HwQTdsNKjYO2ozGqz/6yQzn9tbdHnGg7mUhZvP54vVxuMi1LcfIxaWVcqQlvsCt1Z30VLnQ==", "signatures": [{"sig": "MEUCIAhx+kmx8x9ShbPUiZXIo/8Jvx91Xvh4trN4XpUQv0ItAiEAiPRzW/k895AJvSdD0dLrohmWd5+1VDOrwiJoon9wb/4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.193eb84", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.193eb84.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/367fed45b49a087f6e118cdb2944c812/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.193eb84.tgz", "_integrity": "sha512-xbVZtYz8bSGPBG8HwQTdsNKjYO2ozGqz/6yQzn9tbdHnGg7mUhZvP54vVxuMi1LcfIxaWVcqQlvsCt1Z30VLnQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.193eb84_1748445063244_0.819542495449703", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.8": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.8", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@4.1.8", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "c5e19fffe67f25cabf12a357bba4e87128151ea0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.8.tgz", "fileCount": 113, "integrity": "sha512-CXBPVFkpDjM67sS1psWohZ6g/2/cd+cq56vPxK4JeawelxwK4YECgl9Y9TjkE2qfF+9/s1tHHJqrC4SS6cVvSg==", "signatures": [{"sig": "MEQCIHamyZoRF3JfoApxl8RoJ42PaANmpLhj7HJuU6WHU1JHAiBm2h4yfjKld23XS7mGLonVFwexXjsNsS4V37pkm6I7+Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035159}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-4.1.8.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/667c8b45975c968532ede66e9c27cf09/tailwindcss-oxide-wasm32-wasi-4.1.8.tgz", "_integrity": "sha512-CXBPVFkpDjM67sS1psWohZ6g/2/cd+cq56vPxK4JeawelxwK4YECgl9Y9TjkE2qfF+9/s1tHHJqrC4SS6cVvSg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_4.1.8_1748446203739_0.5524529092916044", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.3c629de": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.3c629de", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.3c629de", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "ddda2dd0795cf5d775bdf720fae9192ada287af8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.3c629de.tgz", "fileCount": 113, "integrity": "sha512-7yGopE+5eVGNv/qOZTHVT3ONp8ZETG7Y4bpU76iJm3iIMDEjZqqwsrNa0CBXkNY9+as5310TBSJ2wRl9ZV4+yQ==", "signatures": [{"sig": "MEUCIQCQn70rX627OVcAzR2Jh/vvkJnUHgPTxiZkiMS9rsuAvAIgF+jOfdi2AU/xapAei9TlBIaqQKHfokDs2i3or6k1W/8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.3c629de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.3c629de.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/39a07dd2967de1967a7334369d046e78/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.3c629de.tgz", "_integrity": "sha512-7yGopE+5eVGNv/qOZTHVT3ONp8ZETG7Y4bpU76iJm3iIMDEjZqqwsrNa0CBXkNY9+as5310TBSJ2wRl9ZV4+yQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.3c629de_1748540815353_0.533965752690239", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.31c0a21": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.31c0a21", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.31c0a21", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "0c84edcb8103910195d65819feb471f62b27ac45", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.31c0a21.tgz", "fileCount": 113, "integrity": "sha512-iRuHHG6Blg/0pBQTIUjUQNPVP8saTUxUkxR7ZMSTWi6582xu6Wgu6RMCuPKYQuGmriI3zkY+/AlEyceUT07O1A==", "signatures": [{"sig": "MEQCIDkjwIZqsBHaJRTb15lJE9Gg1rmCf7woW3wwd7U70wVAAiBFBZatoFNUK8trSI4TDA2070PyW1i6sZvypvq2X6XQ7A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.31c0a21", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.31c0a21.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/tp/bfmjfn9s0hd59bm9z80j3mgm0000gn/T/7830d9691256d7c720c77d1decb7e8ff/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.31c0a21.tgz", "_integrity": "sha512-iRuHHG6Blg/0pBQTIUjUQNPVP8saTUxUkxR7ZMSTWi6582xu6Wgu6RMCuPKYQuGmriI3zkY+/AlEyceUT07O1A==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.31c0a21_1748612554789_0.8101283775087091", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.54c86d4": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.54c86d4", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.54c86d4", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "956b061c1e6156e6b01438a358541678f2da0e2e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.54c86d4.tgz", "fileCount": 113, "integrity": "sha512-oXfgjbnBX8X80WBpKwyx3gDMUPUo466ec+OvtTVusqwtWgRNsw8Xc3/gs+DC/2aj6S9Zyy7kKqeyGJkhRCrLhA==", "signatures": [{"sig": "MEUCIA84zOfFDYcQ2bySNPpTMTKLS5lEH1Wxf9xrNq5QJcKFAiEAgPXbgeXMIAYLcmqq0aJM5qYcNsfeBmgtELz+95uvPj0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.54c86d4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.54c86d4.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/tp/bfmjfn9s0hd59bm9z80j3mgm0000gn/T/22d3cd90dd6b031760c033c083b59a6d/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.54c86d4.tgz", "_integrity": "sha512-oXfgjbnBX8X80WBpKwyx3gDMUPUo466ec+OvtTVusqwtWgRNsw8Xc3/gs+DC/2aj6S9Zyy7kKqeyGJkhRCrLhA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.54c86d4_1748614616580_0.3472453376174993", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.191195a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.191195a", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.191195a", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "d8ec7d41d58db0f0feff02b145a03501113597a9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.191195a.tgz", "fileCount": 113, "integrity": "sha512-rz5LxlwBbwh1cw+znjYfc6UvqU9J1mZA2qRFPcH2QImjYbPY90CsWMcpgO/KDxC+ljsAyQqrGmX6dxjm3b6V6w==", "signatures": [{"sig": "MEYCIQDAC6KXbHJvuZhZJhCGLKYp7y8/OEyn5apSmwrRdtYe4QIhAJ4fYcn7JuBNaGmsnTI9ONf1J3aCCKprxeyURMQ5yZ97", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.191195a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.191195a.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/tp/bfmjfn9s0hd59bm9z80j3mgm0000gn/T/271688c0db6c65382dd554a65b3a6fc6/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.191195a.tgz", "_integrity": "sha512-rz5LxlwBbwh1cw+znjYfc6UvqU9J1mZA2qRFPcH2QImjYbPY90CsWMcpgO/KDxC+ljsAyQqrGmX6dxjm3b6V6w==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.191195a_1748872113313_0.22382839636366003", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.b3fde17": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.b3fde17", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.b3fde17", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "a26eca0b11f494c91571e7cf98257b43fd3c08a0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.b3fde17.tgz", "fileCount": 113, "integrity": "sha512-fqJjDu+Bv2Eg3eVRAEr0zhBM5oBFvmndiKnCRthfZmevJ8pPK/9Jqd9gk7lJyAZOWJRy6yk7ITXcqGbpMdvgDQ==", "signatures": [{"sig": "MEYCIQCvG+Qh9SMcpPqt1IuihtUED5ap210TiUiykbYVQGju1QIhAPASjzqESrIJhhtsBO1GpqKGB4g9Zxqau6Lt++8UvbdH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.b3fde17", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.b3fde17.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/tp/bfmjfn9s0hd59bm9z80j3mgm0000gn/T/b4a4d9540b9724c911fa01bd5e3a8336/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.b3fde17.tgz", "_integrity": "sha512-fqJjDu+Bv2Eg3eVRAEr0zhBM5oBFvmndiKnCRthfZmevJ8pPK/9Jqd9gk7lJyAZOWJRy6yk7ITXcqGbpMdvgDQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.b3fde17_1748941425868_0.013722275377313853", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.f425720": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f425720", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.f425720", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "8a478a28d8281c522f24e6610418e48be46cf45b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f425720.tgz", "fileCount": 113, "integrity": "sha512-sebVh9NYbX3XyiazAbIRESUwKx9+YsU8NZ2W9mZoIoDooHmHbPNMZcQiciXNFNV7gYA7ciYKZeNQUNKpkGXi6Q==", "signatures": [{"sig": "MEUCIQCWLF6z4WGRCTTgUkz8G++CIXIpMGmysx+0MaprWsQw9AIgdW/N3pxDT0x9Sc3f4sYBG3XXO+qVY42A77YELc+dt3Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f425720", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f425720.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/w5/_8wgjw3j5cg6mgrth3s2kg9m0000gn/T/daf7629ab34a2021e5dd8efc9c992fef/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f425720.tgz", "_integrity": "sha512-sebVh9NYbX3XyiazAbIRESUwKx9+YsU8NZ2W9mZoIoDooHmHbPNMZcQiciXNFNV7gYA7ciYKZeNQUNKpkGXi6Q==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.f425720_1748975782861_0.04081641289895033", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.0c1c0c4": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.0c1c0c4", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.0c1c0c4", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "3472eed92c85e5150f4c437fa7e118d4d9126611", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.0c1c0c4.tgz", "fileCount": 113, "integrity": "sha512-T59xrRUEoKQntMM/Z9sQLcG9cz+koY3iwwHHV0ZebjFG5YRosKQ0YjU/p8eamYFsPmq/TY/AhUqdMUywBcJKYw==", "signatures": [{"sig": "MEQCIEcPuAsDR/VE+JNB8frdsQ+3fYia6YShaUEyGhSEXMFfAiATF7mJKBIIPFNNS902lau5FN6EuOKipBePT+prTfQk1A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.0c1c0c4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.0c1c0c4.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/tp/bfmjfn9s0hd59bm9z80j3mgm0000gn/T/7607f7aa82ce25bdeb01ec22de163297/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.0c1c0c4.tgz", "_integrity": "sha512-T59xrRUEoKQntMM/Z9sQLcG9cz+koY3iwwHHV0ZebjFG5YRosKQ0YjU/p8eamYFsPmq/TY/AhUqdMUywBcJKYw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.0c1c0c4_1749116371617_0.17260567840260133", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.8bfbac5": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8bfbac5", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.8bfbac5", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "29ff6e241b37688f00b66f655e28c22a07b34d4a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8bfbac5.tgz", "fileCount": 113, "integrity": "sha512-l7FaSLhlb6k6Tuc7RCHKwhKM6yP/Qspvhja/EgssaQbAYBVrxLyV6bGE2ov/ZTCaRZPgGx4qzN4gPsKUAVRxVA==", "signatures": [{"sig": "MEUCIQD4zkQDuuP5Po+VOBZcb+LBackhrSmig62WINYkLPHnWAIgbXZrA9zrZET37IJIYmie+ee29AsyoKlWKP1+PrSx6Qs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8bfbac5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8bfbac5.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/tp/bfmjfn9s0hd59bm9z80j3mgm0000gn/T/9b52df9f1c04877cdd521be30368921a/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8bfbac5.tgz", "_integrity": "sha512-l7FaSLhlb6k6Tuc7RCHKwhKM6yP/Qspvhja/EgssaQbAYBVrxLyV6bGE2ov/ZTCaRZPgGx4qzN4gPsKUAVRxVA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.8bfbac5_1749130337238_0.8398846216708729", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.288ab3e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.288ab3e", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.288ab3e", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "3573c5a0aec0c8269d750edcd475ef5f71e14c1a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.288ab3e.tgz", "fileCount": 113, "integrity": "sha512-oIffCPEPChYoy9fKTWdDxZ0V3zzpP7aPWtNDqr+5qALTfhi2XXUDHUPGzMUSeSllXZFkvg34wFgiQ1P/YoxgLQ==", "signatures": [{"sig": "MEUCIQCooqJfYdsvA1YsoMRtU+6cxNGj+2Qqd93KBonq2e8vVgIgcn4RGoSjOz0lGdhUiYTs5/DdWTs6yCI8c6cZG6iT73s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.288ab3e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.288ab3e.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/tp/bfmjfn9s0hd59bm9z80j3mgm0000gn/T/a23e1d17adf2025d5af472351c5f0d4e/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.288ab3e.tgz", "_integrity": "sha512-oIffCPEPChYoy9fKTWdDxZ0V3zzpP7aPWtNDqr+5qALTfhi2XXUDHUPGzMUSeSllXZFkvg34wFgiQ1P/YoxgLQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.288ab3e_1749131023891_0.27170524976145805", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.63f6a6c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.63f6a6c", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.63f6a6c", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "544912804208cf90c0c513977a56dad9b0388247", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.63f6a6c.tgz", "fileCount": 113, "integrity": "sha512-XR5gyQlTkeTw7ifcQmXYpsa3wKONILMXEU2HGnoezucqDHQ+PzPI7xnIBQtRJubP6CQwvh7KBuzYHK5ozPGl1A==", "signatures": [{"sig": "MEYCIQDRNinCUDlpMSZXscYFrJ7W4j+24fJERIkqLV0fuE2iPwIhANlL2zJVAeOJedxC1Q9JU5jmwpYQyqXdTeFJTXERJGj8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.63f6a6c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.63f6a6c.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/0c146be2570c93a759c6b136659a14f1/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.63f6a6c.tgz", "_integrity": "sha512-XR5gyQlTkeTw7ifcQmXYpsa3wKONILMXEU2HGnoezucqDHQ+PzPI7xnIBQtRJubP6CQwvh7KBuzYHK5ozPGl1A==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.63f6a6c_1749145183830_0.8920629117623762", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.21ece6c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.21ece6c", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.21ece6c", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "743de002b891160450f2a083ad0478916cb70a6f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.21ece6c.tgz", "fileCount": 113, "integrity": "sha512-vXmYX0axCYp7hDUVcY7M2+wm+hiyiotM3FSH1ZB5L4wCcLOa9llwsFRfGTW7f97rF5H9MUBidDEH1vpnrXX0/Q==", "signatures": [{"sig": "MEUCIAkudoSlSipTUM3PvYcUQV1ABigqxa3hpnfS4qxJU2vFAiEAzQb2TPsijgWBScjbQYEgWvFuSZdahPUFwL6mHarTw/k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.21ece6c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.21ece6c.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/tp/bfmjfn9s0hd59bm9z80j3mgm0000gn/T/06640ff355a80b67f58f1affac331b26/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.21ece6c.tgz", "_integrity": "sha512-vXmYX0axCYp7hDUVcY7M2+wm+hiyiotM3FSH1ZB5L4wCcLOa9llwsFRfGTW7f97rF5H9MUBidDEH1vpnrXX0/Q==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.21ece6c_1749157965751_0.6041307080927456", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ada85b1": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ada85b1", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ada85b1", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "ec9ca8d8950eedc571246643d976571ee9c99d5c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ada85b1.tgz", "fileCount": 113, "integrity": "sha512-3ubDeZEwRH0a7yfD4WAwJa6h+1Qo0dHckZgkm3llF/nW0hZrO9cJQcLlQOu0JL0CqoV6W4yjJuiNHg3gphbqDw==", "signatures": [{"sig": "MEQCIF09EHA68+99h1bFQdQPGQDgNvWwJ070ov5n5GNnGu5eAiBmasWgWXQfAY7C+U+fmFQRiuAhJfmfjbcen73AC4JGZw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ada85b1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ada85b1.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/1f713bba9dc901ffead071092aca9005/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ada85b1.tgz", "_integrity": "sha512-3ubDeZEwRH0a7yfD4WAwJa6h+1Qo0dHckZgkm3llF/nW0hZrO9cJQcLlQOu0JL0CqoV6W4yjJuiNHg3gphbqDw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ada85b1_1749226976835_0.7248553786469496", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.f0f42f6": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f0f42f6", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.f0f42f6", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "e5514284ba72e8c9dccc42fc2d07eb88d8c8db3e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f0f42f6.tgz", "fileCount": 113, "integrity": "sha512-uCosOPFkmUz3o4mZSzMTg+HLDj+8E2c6rconNpgWRRWan5kcWOlEVoQXPSCcF5H0LSAsNny54lEhQI1u0sZ8MA==", "signatures": [{"sig": "MEQCIH8uxvmdfCCSxnyCQYyLjUw6F19IeONZjm5hhlDZDyBBAiAUFrEGPgZhao9otzFVkiulYa1Zh3kh82n5c2w0tnUjPA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f0f42f6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f0f42f6.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/dfafc428c1bad35ab3d5f9b7bdceca48/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f0f42f6.tgz", "_integrity": "sha512-uCosOPFkmUz3o4mZSzMTg+HLDj+8E2c6rconNpgWRRWan5kcWOlEVoQXPSCcF5H0LSAsNny54lEhQI1u0sZ8MA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.f0f42f6_1749239514544_0.07647777671669886", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.fd95af4": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.fd95af4", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.fd95af4", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "269c2b1e302f26cf57e8336a3b95808bfc513f03", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.fd95af4.tgz", "fileCount": 113, "integrity": "sha512-THoH81CUOrMikP/WVMFDFyYxTTcxFtMOMeAD9KCujp/zvnCw8dZq3hCk5GwVju1Rpl3FQoQky/GIAfH7tsB68Q==", "signatures": [{"sig": "MEUCIQCo1KgdXMORSmuOiB2vT3XFWwYuITzV1WFh88RsldIkYgIgOlsxWTD689eiMy8AZj2wrZAypcKQTM1o45+GAOYL5Sw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.fd95af4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.fd95af4.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/0ff5401f91392f2efb215c0785d449b6/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.fd95af4.tgz", "_integrity": "sha512-THoH81CUOrMikP/WVMFDFyYxTTcxFtMOMeAD9KCujp/zvnCw8dZq3hCk5GwVju1Rpl3FQoQky/GIAfH7tsB68Q==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.fd95af4_1749475895667_0.14040486819786802", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.bea843c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.bea843c", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.bea843c", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "46c6fb19931ffad02e1a76d4ec6b998f3c6f7e24", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.bea843c.tgz", "fileCount": 113, "integrity": "sha512-bfVngAguq1QtdCfygaC3lx0quDuGtPOrmkKDK1lVvSgc2LkCGatVd2DqQcwS/hRakM4FLISWIX03H8Zk0/vzbA==", "signatures": [{"sig": "MEYCIQC1trcR/7SnQNYXsS80R/D6675VaNgGDwrrB6wfgmgMGwIhAMncdEgeWiqsD67R/C1nbuhF1QwNjeDJ2Mv4yOPXu0kf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.bea843c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.bea843c.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/3493b42c5cc9fdd1618b8a1db9b6f14c/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.bea843c.tgz", "_integrity": "sha512-bfVngAguq1QtdCfygaC3lx0quDuGtPOrmkKDK1lVvSgc2LkCGatVd2DqQcwS/hRakM4FLISWIX03H8Zk0/vzbA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.bea843c_1749478384016_0.44782509791628544", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.da08956": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.da08956", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.da08956", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "3a5823d492d532949ce87e217c29efdb2e55d669", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.da08956.tgz", "fileCount": 113, "integrity": "sha512-EB+wh7fVqYvHXdtQi8NJW6eOdsoiWxhPrL3xoLvb1AwV8U6ST6UoocmJBdqGyJcsQ+Wfch3rqDu3oboDQF1h+w==", "signatures": [{"sig": "MEUCIBFKMjI0v18ho3S3RNGxTAHGRhb8jsehqF0PdPFFL+D0AiEAgX5Cp2b1YuXvQ6suNfiWILH3ziDqnKvjkHITuDn0ddE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.da08956", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.da08956.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/bf4fdb77a19f915d9896ee92a1906205/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.da08956.tgz", "_integrity": "sha512-EB+wh7fVqYvHXdtQi8NJW6eOdsoiWxhPrL3xoLvb1AwV8U6ST6UoocmJBdqGyJcsQ+Wfch3rqDu3oboDQF1h+w==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.da08956_1749554146281_0.9728090348046177", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.aa817fb": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.aa817fb", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.aa817fb", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "67f15168890b10858b56a2648a616d43441ef164", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.aa817fb.tgz", "fileCount": 113, "integrity": "sha512-QwYBiHoNhP5s+OXEVCu+z16PUAnJn11ztKIQ6xnTOEWMxXT5u7jSyOYk9lN570a+CpKs5z2LPS+5kiR/1pBbMw==", "signatures": [{"sig": "MEYCIQD/FvQ8J4uCK1mBKBj/k8VjGY3+9+jEQAD0cKYV04ZevQIhAK74FtD9w1dFOVOSKn4TMuMvJIYwxYRZfX/4a+4aW5BT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.aa817fb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.aa817fb.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/f95678f36405ad05bdd11fb03d53c585/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.aa817fb.tgz", "_integrity": "sha512-QwYBiHoNhP5s+OXEVCu+z16PUAnJn11ztKIQ6xnTOEWMxXT5u7jSyOYk9lN570a+CpKs5z2LPS+5kiR/1pBbMw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.aa817fb_1749640414729_0.2339440716787271", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.b88371a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.b88371a", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.b88371a", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "57dd0ae952e6d631b47a62ee2ed695c102782d53", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.b88371a.tgz", "fileCount": 113, "integrity": "sha512-OXSe1b2pluHs3O3lINs24RzLmRzKXh3P7Nt7vvC59TMXbHO615PyBESRVwtsKJUO4IPPTryloZ/4I6/3xm8jQQ==", "signatures": [{"sig": "MEUCIBXi/2yHkmyzDvDeyE4fSjyWk3rodfHoMi9jkmz9kiXvAiEAhKyBpvcXW62R/v3bhnoKv25pl83+cuTP763Ej1kayAU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.b88371a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.b88371a.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/ae427479e258d099e247a28c4700ded0/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.b88371a.tgz", "_integrity": "sha512-OXSe1b2pluHs3O3lINs24RzLmRzKXh3P7Nt7vvC59TMXbHO615PyBESRVwtsKJUO4IPPTryloZ/4I6/3xm8jQQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.b88371a_1749650790577_0.5618726855063538", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.9": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.9", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@4.1.9", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "99033b8f8196884fe947d15ca0541f612fc4deaa", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.9.tgz", "fileCount": 113, "integrity": "sha512-G93Yuf3xrpTxDUCSh685d1dvOkqOB0Gy+Bchv9Zy3k+lNw/9SEgsHit50xdvp1/p9yRH2TeDHJeDLUiV4mlTkA==", "signatures": [{"sig": "MEUCIFVgmbRf1cf1Mpr+7iypX99MeTpN7G9rgNDnxrql6xdRAiEAxzJOHDoB8HQDQPAAwsM/7KcL4NTl1AWGAiYbFeeP7Sw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035159}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-4.1.9.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/c70278e77a160e8d8da7017e543eb20a/tailwindcss-oxide-wasm32-wasi-4.1.9.tgz", "_integrity": "sha512-G93Yuf3xrpTxDUCSh685d1dvOkqOB0Gy+Bchv9Zy3k+lNw/9SEgsHit50xdvp1/p9yRH2TeDHJeDLUiV4mlTkA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_4.1.9_1749652478223_0.05949441142081513", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.427649e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.427649e", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.427649e", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "58949fe99d561d2eb89a0ea8ba0eb564d94436ae", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.427649e.tgz", "fileCount": 113, "integrity": "sha512-YNZgSOT/OvrW2//K/8Ved7YrZ3yj7pYwfArxFoAVuZV3yVcnwC+deYhHR92mS6JWeTOlj4ldc3KVTQq5FPhbfw==", "signatures": [{"sig": "MEUCIC/OGjn52zB8XInIt1HC+XWr7ugBNp1mnWRqVERdfWXUAiEAq/5LLptzWyidEEtwt9ZD4X2Zc6z3udZzwOsDO5v75uM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.427649e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.427649e.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/d6200d5288f0136ca2705fa11763b010/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.427649e.tgz", "_integrity": "sha512-YNZgSOT/OvrW2//K/8Ved7YrZ3yj7pYwfArxFoAVuZV3yVcnwC+deYhHR92mS6JWeTOlj4ldc3KVTQq5FPhbfw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.427649e_1749665134871_0.5759959619057371", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ddb0bef": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ddb0bef", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ddb0bef", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "59299c7d55871ddb92625851a1362a889fff4d40", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ddb0bef.tgz", "fileCount": 113, "integrity": "sha512-VVFWJTJzN+g42TJ5cDYyiGOF+W1xpvS9AMZnPapKSovNXWq5BbtQv7WK+BWvzMPEE/45gppQ2+thYZnnYMyGPg==", "signatures": [{"sig": "MEUCIQDdo3acX+Adq3nTZ1Z7HSYHNzLKDSNUUEhNQjU+yD5QrAIgHUW95+2T09P1Hk7DE3io/yV5LUa52W65DVticjtU1V0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ddb0bef", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ddb0bef.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/b892c92b28b7bb343687076e15223462/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ddb0bef.tgz", "_integrity": "sha512-VVFWJTJzN+g42TJ5cDYyiGOF+W1xpvS9AMZnPapKSovNXWq5BbtQv7WK+BWvzMPEE/45gppQ2+thYZnnYMyGPg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ddb0bef_1749673559672_0.7377506986175413", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.d06bbb8": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d06bbb8", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.d06bbb8", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "a3fd339c945fd7af66126fe7110357db5328c487", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d06bbb8.tgz", "fileCount": 113, "integrity": "sha512-QL7U8xe0WuomWcQcga3K+eZ12hiFhXx/SYwtgf+jR3lKT5iB9Nj+qzjFdQs8tB5XjXFZAy2z648uqu5OfnvIUg==", "signatures": [{"sig": "MEUCIC9ELJBMP2hFZqG1HHJrT2ZVzxJ4PPLPBaxMUtACNsXpAiEAk/lpfXuWIpmQASDy3avrZc+OVx9WMXDxqb92oPDNAQw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d06bbb8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d06bbb8.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/c52226f4766b38736c41f1877f5589d8/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d06bbb8.tgz", "_integrity": "sha512-QL7U8xe0WuomWcQcga3K+eZ12hiFhXx/SYwtgf+jR3lKT5iB9Nj+qzjFdQs8tB5XjXFZAy2z648uqu5OfnvIUg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.d06bbb8_1749674077692_0.20553737250547632", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.10": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.10", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@4.1.10", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "6e749424db4f6e076371a66da7c4daf1fcd4f9df", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.10.tgz", "fileCount": 113, "integrity": "sha512-d6ekQpopFQJAcIK2i7ZzWOYGZ+A6NzzvQ3ozBvWFdeyqfOZdYHU66g5yr+/HC4ipP1ZgWsqa80+ISNILk+ae/Q==", "signatures": [{"sig": "MEQCIBGdUfscytbQT/vNeBCqq5L2HAuNOpOtv+rjcfzA6yQsAiBEu9ITMTHkCyYdju21To2DpfevrRxyfekaUVSomYEG6A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035160}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-4.1.10.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/27cabce31cce9be3023a5f596da56bb7/tailwindcss-oxide-wasm32-wasi-4.1.10.tgz", "_integrity": "sha512-d6ekQpopFQJAcIK2i7ZzWOYGZ+A6NzzvQ3ozBvWFdeyqfOZdYHU66g5yr+/HC4ipP1ZgWsqa80+ISNILk+ae/Q==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_4.1.10_1749674799080_0.9040968920007362", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.2ebaff2": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.2ebaff2", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.2ebaff2", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "82613478fe79b1c3be69f042ecaca7d6795321cf", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.2ebaff2.tgz", "fileCount": 113, "integrity": "sha512-/uuZS2F8gcMOSVSrNwz8F9o9YbYtwLTEKPF0xLQ0s+nrd2Bbz90yw2sikLJozDXHDCYnxLvo9qov2wXPbMXygQ==", "signatures": [{"sig": "MEUCIAVzuQIgwAFck66rFtvuz5IZDhvwKC2ki6ySeyhvO7eYAiEA9ruS2XPzjthqYf7rJLxCdobVbekcBSyMVGkv27m5M/k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.2ebaff2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2ebaff2.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/da4cae6ef57ce8630c70176eff365a00/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2ebaff2.tgz", "_integrity": "sha512-/uuZS2F8gcMOSVSrNwz8F9o9YbYtwLTEKPF0xLQ0s+nrd2Bbz90yw2sikLJozDXHDCYnxLvo9qov2wXPbMXygQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.2ebaff2_1749676553224_0.3907780049840186", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.7f97179": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.7f97179", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.7f97179", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "7b5c76f0381ce7413c500fc031bff626852812f2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.7f97179.tgz", "fileCount": 113, "integrity": "sha512-wvhLQ/UCoD5NlvrmfkNLGs4rhTbhb4rN/01nGikv2/EWAKivFklsuuKB5vSLAI4jAQVvlwP9EYvTKeaW0xpm9Q==", "signatures": [{"sig": "MEQCIDtJ1Eg/WqPkPC8jmv94rmOGS3hw/dFrbrVvuHV+aHLQAiBreOsg8B/vKCy0yPXrmKq4I38FDPLVlwvsanq3oGrM0g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.7f97179", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.7f97179.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/8d34d5eb586f848c7fce72970e5400fb/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.7f97179.tgz", "_integrity": "sha512-wvhLQ/UCoD5NlvrmfkNLGs4rhTbhb4rN/01nGikv2/EWAKivFklsuuKB5vSLAI4jAQVvlwP9EYvTKeaW0xpm9Q==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.7f97179_1749677494756_0.9440382157812803", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.bab16ae": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.bab16ae", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.bab16ae", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "9ed003496e30c7d7e9e23ed6a6892ac9cf3845b5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.bab16ae.tgz", "fileCount": 113, "integrity": "sha512-asA7qxIAMiF1aaVx9vs/rfJVDos17Urmstz2S1SSiUxA8FwO22zJutVvesBXGdHTd1ICbgf8K8YUjX0PYjx1EA==", "signatures": [{"sig": "MEYCIQD5IQ4yClpRKdz5j/PDzznt1Z6F4p1r0ElaT2zSE0TPbAIhAOYDCEbxjyBxoqWyD4ZCvJciETEkafvADhBdqf4aN7SZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.bab16ae", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13031509}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.bab16ae.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/9e8f7bcc41585a89c3953a572daa9a3f/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.bab16ae.tgz", "_integrity": "sha512-asA7qxIAMiF1aaVx9vs/rfJVDos17Urmstz2S1SSiUxA8FwO22zJutVvesBXGdHTd1ICbgf8K8YUjX0PYjx1EA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.bab16ae_1750084600300_0.4433441318076283", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.4453496": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4453496", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.4453496", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "2e4f58de8cce704d8c5df8f9989487cd9d455dbc", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4453496.tgz", "fileCount": 113, "integrity": "sha512-o+ZRC5uF3ViCbbQ4vcrAxZCJ3kON3QU8pcvuzMNkezx841Efdz8AjTJfILLf/QpFFwXgKPXiTGtufuzavxiFgw==", "signatures": [{"sig": "MEQCIB8NCL+RpWESHIv6BYhLFMt//4lNMP0xOAInzYL0V20aAiAzgXhbjzJBgiATWwn6UY8hVsq+H77N9g0nGyYmXj5Uyg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4453496", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13031509}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4453496.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/886dc7d8821fb1fc9e5bc7f8b98b8f9e/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.4453496.tgz", "_integrity": "sha512-o+ZRC5uF3ViCbbQ4vcrAxZCJ3kON3QU8pcvuzMNkezx841Efdz8AjTJfILLf/QpFFwXgKPXiTGtufuzavxiFgw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.4453496_1750172123514_0.02958858491954075", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.63b5d7b": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.63b5d7b", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.63b5d7b", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "cc9a9355f591cb5122b35fb3747d6292d4b09526", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.63b5d7b.tgz", "fileCount": 113, "integrity": "sha512-QNBjqWfPujkGZycWvkdENl8XEEfWQ/xDkoS4J+8iRvfwZWxozMCAXtuC5eZ0hCSazDE0Fhwo7L5lf1J5/B8E0g==", "signatures": [{"sig": "MEUCIH59wT+aHaB7bZ4Wbba3SBYY+90bkM19/Gjy8/5G1PlMAiEA+ra6VHHks+Wk70SkQBCtxHENYdCsKs15H6mKT3Th6EY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.63b5d7b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13031509}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.63b5d7b.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/a6c2e38149db27e47fbfb7dbb7e30f21/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.63b5d7b.tgz", "_integrity": "sha512-QNBjqWfPujkGZycWvkdENl8XEEfWQ/xDkoS4J+8iRvfwZWxozMCAXtuC5eZ0hCSazDE0Fhwo7L5lf1J5/B8E0g==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.63b5d7b_1750186627655_0.045520194933664326", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.75cbfc2": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.75cbfc2", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.75cbfc2", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "c8b1523bec1ac2d5b31363a20ea439a876541a09", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.75cbfc2.tgz", "fileCount": 113, "integrity": "sha512-kYDmrjAJdHArRZTVlXyl8DY3wr7e0XyLeEL3MVYFTlJtcuXBkA4AMxZvO4j+SJlpBffA0nyK0Jm3vh6cllm7Ow==", "signatures": [{"sig": "MEYCIQCCzM+AKd6CJU32bQxPoENzpytKj2V689M8AsJeIUEmygIhAJtAVQlOF/D+YuuiAleKy6XXJRrr4th6dmrj7KEu20UW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.75cbfc2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.75cbfc2.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/ea2b7400712774eb9274ea769af2bc69/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.75cbfc2.tgz", "_integrity": "sha512-kYDmrjAJdHArRZTVlXyl8DY3wr7e0XyLeEL3MVYFTlJtcuXBkA4AMxZvO4j+SJlpBffA0nyK0Jm3vh6cllm7Ow==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.75cbfc2_1750265738966_0.9629812515091152", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.f4a7eea": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f4a7eea", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.f4a7eea", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "841484c1a8155f128a692e429882771057157741", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f4a7eea.tgz", "fileCount": 113, "integrity": "sha512-VVXeOEIsu9T/jIDaOpYjB/E96beDAnD4lG4JNWL4cc0SD7lJrW9cwaQYDg/cOOzLuLbmb4p3A1FN+HltxY0YJg==", "signatures": [{"sig": "MEUCIQDq5h/pD8X3EOEjGEndIVm+ztCrVLoYU5sGrWjr6UPcswIgdsZwRJB77itwMP+LQ/3o+j6KzPCFlltNqpDrUgTr/Kg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f4a7eea", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f4a7eea.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/d5411dfc44316cdc5ceea707ca3217f0/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f4a7eea.tgz", "_integrity": "sha512-VVXeOEIsu9T/jIDaOpYjB/E96beDAnD4lG4JNWL4cc0SD7lJrW9cwaQYDg/cOOzLuLbmb4p3A1FN+HltxY0YJg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.f4a7eea_1750274824053_0.7064126596724509", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.c5a997c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.c5a997c", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.c5a997c", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "f6167ce25e9bd8609b6e19e2cd2b819abc1a99ff", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.c5a997c.tgz", "fileCount": 113, "integrity": "sha512-Tg3nHU11riqnUCrm3ThCgOZ6USZgh29hAIgo/aOyR8ZkDw4OfLLWAreVXk0BPLZkbgD3e6pGafYmcAqvoS7lVA==", "signatures": [{"sig": "MEYCIQDCij1HPoGx4uRw78jKRhvQDoL5WI/n1q3gmYuIDeM3bQIhAKdNuGDkgROUBnyBJtmsnnXzhNB3Y/wql3Peb17505RQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.c5a997c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.c5a997c.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/70fe9ee07a8d128e8a4976cbc99d674f/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.c5a997c.tgz", "_integrity": "sha512-Tg3nHU11riqnUCrm3ThCgOZ6USZgh29hAIgo/aOyR8ZkDw4OfLLWAreVXk0BPLZkbgD3e6pGafYmcAqvoS7lVA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.c5a997c_1750359676833_0.6675190028787432", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.1a35fbc": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.1a35fbc", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.1a35fbc", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "af31e3f2df4f160ec12cb39059545790c1871371", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.1a35fbc.tgz", "fileCount": 113, "integrity": "sha512-Gx8k4v39vTf/cAofJdqTjZ9a1Z7miGs7TfwiCsIY+FCkKwwiyxcjLeXzXqMXGYPXL/GY1z/v6pGaM5TMjr87BQ==", "signatures": [{"sig": "MEYCIQCbE/UEqOKRbV8E5umcqYlm06WyFeXXJHWUIGdFhaiRMwIhALfE5tXQd2Mj+7L+n7GfTVCpnORNz8691eR0QnT5jmTj", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.1a35fbc", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.1a35fbc.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/7197aba7e0354ba12fb18a03426f57dc/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.1a35fbc.tgz", "_integrity": "sha512-Gx8k4v39vTf/cAofJdqTjZ9a1Z7miGs7TfwiCsIY+FCkKwwiyxcjLeXzXqMXGYPXL/GY1z/v6pGaM5TMjr87BQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.1a35fbc_1750415566838_0.30198149210118763", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.d788657": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d788657", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.d788657", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "2fd39c4321286f1907a8c08e105f9923743827d8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d788657.tgz", "fileCount": 113, "integrity": "sha512-zkWnIPZL6ac1eArcbPCyS+qKGekA8oA+NneIb8C6WvH40w1mTkopUuZE4Ne8m2FlJaDhSq3COOj6kW6mq5x6QA==", "signatures": [{"sig": "MEUCIQDjPDLOyMuxkR43nchrhc/BBYUACQMw+X1G7Z9bPx/ulQIgMee/x2lwJn6VLMrkHjGAWZMNKE4PDvAsITa1XwZ6Tz0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d788657", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d788657.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/52a1f1b6d48b0aceb16eaf305a237276/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d788657.tgz", "_integrity": "sha512-zkWnIPZL6ac1eArcbPCyS+qKGekA8oA+NneIb8C6WvH40w1mTkopUuZE4Ne8m2FlJaDhSq3COOj6kW6mq5x6QA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.d788657_1750443152186_0.186903499023104", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.f0e2f5b": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f0e2f5b", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.f0e2f5b", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "84d8935511ba3552007fcf8138464ca6a82c333d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f0e2f5b.tgz", "fileCount": 113, "integrity": "sha512-EcCtbMsXHJreYpl8g+gIsw4kHenIyYV+GH+RG4b9B0ZHIa49rN9vizkB4OiDpq2LEmx8JCAgcOP6ggSkw6fztw==", "signatures": [{"sig": "MEUCIQCnEsz5psyL13z3b2MP232HKorAY1jqaOtHmcGZeiyzpQIgPhKZ3n8VL2ZR+VBZSj64cs/JGAqIer+Vn9UOnIO4CHE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f0e2f5b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f0e2f5b.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/c2182d05462c4c8521348551db7845b4/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f0e2f5b.tgz", "_integrity": "sha512-EcCtbMsXHJreYpl8g+gIsw4kHenIyYV+GH+RG4b9B0ZHIa49rN9vizkB4OiDpq2LEmx8JCAgcOP6ggSkw6fztw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.f0e2f5b_1750535248288_0.8829735492124022", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.767a60a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.767a60a", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.767a60a", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "bcd7e41a58b0c474aff5a88ed675f9ee9ef03687", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.767a60a.tgz", "fileCount": 113, "integrity": "sha512-fgVKgfQvaPgOjgTNISWmnc7QVetKohJ4QMtk58YObLNGYgWTSIpVYr9JTTZjZxVAOdoCTjhw8v/Es2Ej0G4gKg==", "signatures": [{"sig": "MEUCIQCYkbM8kB9R0sTZpfE/yA9NENPA8wubVNQTB6QnqVEokQIgNz0Hq67OVFUMZWjONjaE0Ti1tanMIziU+gl5ngtR72g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.767a60a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.767a60a.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/e79ca111514f191a9e7691a340494050/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.767a60a.tgz", "_integrity": "sha512-fgVKgfQvaPgOjgTNISWmnc7QVetKohJ4QMtk58YObLNGYgWTSIpVYr9JTTZjZxVAOdoCTjhw8v/Es2Ej0G4gKg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.767a60a_1750774065911_0.5222517303700525", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.b9007dd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.b9007dd", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.b9007dd", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "c199f83c32bf5a8566fcb7dc47e300dc3cee3d22", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.b9007dd.tgz", "fileCount": 113, "integrity": "sha512-pdFZHKj7IH35lUAP8nqPo0Y3OEIRB+v9Yk5rEnIawLcXGgnHVrQ+0leS7nHaQY6RQ1VE/urjI9siiE1gSSe/1Q==", "signatures": [{"sig": "MEYCIQDomk4ZvszxZH6fLP8k69F0aKL73xFcB1UpJ2BEbP91lgIhAL5N0mKCB29/OaOvJhqh15IJ94jYLinx9SB8uMN/o/Xl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.b9007dd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.b9007dd.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/c6af43fde15c83cdda01340f5c6ee597/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.b9007dd.tgz", "_integrity": "sha512-pdFZHKj7IH35lUAP8nqPo0Y3OEIRB+v9Yk5rEnIawLcXGgnHVrQ+0leS7nHaQY6RQ1VE/urjI9siiE1gSSe/1Q==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.b9007dd_1750774308827_0.9633497043928025", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.aa85931": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.aa85931", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.aa85931", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "c67f4224387dbf233a25a93f79e082a945d031db", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.aa85931.tgz", "fileCount": 113, "integrity": "sha512-4yAAV15UJPQALEAI9EEvOOxvl7IYcLN4jMWWzOjtMGBboMYFSkCkqcJ0JG59M1ulKbQWBsqaFD3ryMcyR5JBoQ==", "signatures": [{"sig": "MEQCIB/TxYbQYuTOVaV7qhy+d9QWvorOkNahgCs0P7qCxUJYAiA59cGHXLq8uLWl2rsv6gKh/OMsj5ClkzZvdv5Q/NQYdA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.aa85931", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.aa85931.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/f81fb29bdb210018faa2c8e9f9e66db6/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.aa85931.tgz", "_integrity": "sha512-4yAAV15UJPQALEAI9EEvOOxvl7IYcLN4jMWWzOjtMGBboMYFSkCkqcJ0JG59M1ulKbQWBsqaFD3ryMcyR5JBoQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.aa85931_1750783159649_0.04987529738778251", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.6ad26de": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.6ad26de", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.6ad26de", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "3dc1c335d18fd637a480d02ef4b0b5ac964201b4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.6ad26de.tgz", "fileCount": 113, "integrity": "sha512-1lEfwpjha8smbhjb678RYNKx7noA5CXybYase5EO6IQDbNtsR+0AiGm4uzHoTi8gw0ZLu4FJl+GMvhYoQbErvA==", "signatures": [{"sig": "MEUCIFtQXSCbRVPpalIyJOs/u7xfprwGE84TlTtp/0TjakUJAiEAw7BjA/N9DzlrCh4DY0EQ2wyTZTrJfw4WKWwosA+0qvs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.6ad26de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.6ad26de.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/38fa39e3677619775fb05be5cc13fdad/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.6ad26de.tgz", "_integrity": "sha512-1lEfwpjha8smbhjb678RYNKx7noA5CXybYase5EO6IQDbNtsR+0AiGm4uzHoTi8gw0ZLu4FJl+GMvhYoQbErvA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.6ad26de_1750930554696_0.35205645949197684", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.b24457a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.b24457a", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.b24457a", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "2f640282269644edb2570101178b17fe001cf29c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.b24457a.tgz", "fileCount": 113, "integrity": "sha512-GBAmOpejLui06vfVdOTPfVntc7aCZUhc9RQOhpvrb4nc6ohaRD+XIm+zSD+fA3M8Xsr9Fsc3438+sD+et9JGOQ==", "signatures": [{"sig": "MEQCIDmQd2XiM7fKUqG3HMlet3xpT0FSe/4Jg4EZZyNS76MZAiBZ4m4Ie1kW/NwxwCBauR2dLE/2uL5Zr5iqLAPQ7z10FA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.b24457a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.b24457a.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/2da4199a3d10ee2399d51bc5f4d6d7fc/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.b24457a.tgz", "_integrity": "sha512-GBAmOpejLui06vfVdOTPfVntc7aCZUhc9RQOhpvrb4nc6ohaRD+XIm+zSD+fA3M8Xsr9Fsc3438+sD+et9JGOQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.b24457a_1750935124014_0.8531848542715297", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.11": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.11", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@4.1.11", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "a1762f4939c6ebaa824696fda2fd7db1b85fbed2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.11.tgz", "fileCount": 113, "integrity": "sha512-Xo1+/GU0JEN/C/dvcammKHzeM6NqKovG+6921MR6oadee5XPBaKOumrJCXvopJ/Qb5TH7LX/UAywbqrP4lax0g==", "signatures": [{"sig": "MEYCIQCeEQYMkmILbyOUhVRISDKPTpnZjik+6Vyz2og05jMSrgIhAOCEXjJOa7a1PH5zCi5u3kX5ao5dhyDhnc4qy2H3gGWY", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030622}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-4.1.11.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/8dc09935689c660f5856f195ae48a6f5/tailwindcss-oxide-wasm32-wasi-4.1.11.tgz", "_integrity": "sha512-Xo1+/GU0JEN/C/dvcammKHzeM6NqKovG+6921MR6oadee5XPBaKOumrJCXvopJ/Qb5TH7LX/UAywbqrP4lax0g==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_4.1.11_1750935571998_0.21764462679573726", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.b716d10": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.b716d10", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.b716d10", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "c2fb793eab92fbd871441fda613da0b371fde438", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.b716d10.tgz", "fileCount": 113, "integrity": "sha512-X/103SXy3EUK03541Z1C8YAx9U6tKcCPk4we5nCqHIitK5vqDZ922ue4HF+1egcIfgykzrmbdj3pWTmudoPAXQ==", "signatures": [{"sig": "MEYCIQDUDasvGhgA9lSER6OkwIUyPvrdoEFCT7JrVs357dJJRQIhAOlHjlkF9OZEEVz7JXYEulF4vEfYYjCSMMfQs96csm35", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.b716d10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.b716d10.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/0c70c14d5c28015c7ccdc5281a58f54d/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.b716d10.tgz", "_integrity": "sha512-X/103SXy3EUK03541Z1C8YAx9U6tKcCPk4we5nCqHIitK5vqDZ922ue4HF+1egcIfgykzrmbdj3pWTmudoPAXQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.b716d10_1750967563189_0.7511971874038608", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.2030e94": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.2030e94", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.2030e94", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "ad0612eaca4a973f0f2c5dc23de584990224bb79", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.2030e94.tgz", "fileCount": 113, "integrity": "sha512-8GCOJEi5gDM1ea34vOyV27m9TZP+9HiLQeT1IJw4ewxUeGVecvorP8qqkQTOkZyNAPlamPvaAzedLMBvxvMUrw==", "signatures": [{"sig": "MEUCIQCwLi8vMZc0YO5IbbVQwF8kD04DIS4O4gQW500R2o6yAAIgPwGxjB6KjtsEfcqu5q46b23UeYKQK3tU28HCsFh+ATA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.2030e94", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2030e94.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/9bf0a01e2274f4db6348992b6a1ed217/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2030e94.tgz", "_integrity": "sha512-8GCOJEi5gDM1ea34vOyV27m9TZP+9HiLQeT1IJw4ewxUeGVecvorP8qqkQTOkZyNAPlamPvaAzedLMBvxvMUrw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.2030e94_1751040219118_0.7699278187862792", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.ca7caae": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ca7caae", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.ca7caae", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "4cedcb1e3f6c44b4c51589801c5f8ff232c54cf0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ca7caae.tgz", "fileCount": 113, "integrity": "sha512-eDkfq1MQOdJhr2iWh6PeDtPBfuasF5yLzqorfSWEG7mTUb0fsAAcCDj8vpB7LI9//gL2Fb8jREK9Nth8X78EIA==", "signatures": [{"sig": "MEQCIE5RGye/NlkMNSPN/yTzBztrckVXPKvFUiKQXe0KJ5A4AiAFWb93FyG2BvzpyoO0Y7j0olNf9nQcuiA+4Tr/SfdQ3g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ca7caae", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ca7caae.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/ca08910c1e526750fc569661df1909cd/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.ca7caae.tgz", "_integrity": "sha512-eDkfq1MQOdJhr2iWh6PeDtPBfuasF5yLzqorfSWEG7mTUb0fsAAcCDj8vpB7LI9//gL2Fb8jREK9Nth8X78EIA==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.ca7caae_1751290826141_0.9116836728097912", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.7946db0": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.7946db0", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.7946db0", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "fb362b2599b1a23da307d3c42e16beefd3b672d0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.7946db0.tgz", "fileCount": 113, "integrity": "sha512-P+zFYB3TytPSNdyFegKksjSeRkk+iUVYSMko7NG+ldVBxNrC7deXV1Az4EEp8HYaitfKgIshy7DdFS02Jr9nVg==", "signatures": [{"sig": "MEUCIQD8V1LNYa1TIHKrx3sPo3XoJxKnXp6ob8R+6thwe5kK+QIgP8Nh6ZBdRJkeHWHagynGX1xJFM+7c3Ek/NvSU24g5Cs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.7946db0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.7946db0.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/0306534c250a6a4810d8780512516215/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.7946db0.tgz", "_integrity": "sha512-P+zFYB3TytPSNdyFegKksjSeRkk+iUVYSMko7NG+ldVBxNrC7deXV1Az4EEp8HYaitfKgIshy7DdFS02Jr9nVg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.7946db0_1751291367717_0.5073122293450034", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.05b65d5": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.05b65d5", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.05b65d5", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "d3304433b4e14203a7c4d030d6d19f649ebd4650", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.05b65d5.tgz", "fileCount": 113, "integrity": "sha512-vc9WMcL0gGFvVEH0shvAAKocAhQ4qpSPmEGl0G1/o0IhU/uB6lPefEHVoaiZbguaFbLiUdRZHD224KsfsH4erw==", "signatures": [{"sig": "MEUCIQCvR0luqpCiKd2ihomunX0WiAAEyOeZF6h6rCmEChlcdwIgScSQSt45ZRguS14Tx6ePMznfiBTGtC6S+pnI2Qxbm7I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.05b65d5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.05b65d5.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/c393513bd433d934322bf3e81ef938e4/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.05b65d5.tgz", "_integrity": "sha512-vc9WMcL0gGFvVEH0shvAAKocAhQ4qpSPmEGl0G1/o0IhU/uB6lPefEHVoaiZbguaFbLiUdRZHD224KsfsH4erw==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.05b65d5_1751301448374_0.19202398144682853", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.f307c31": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f307c31", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.f307c31", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "fdac383dd6aa440dcb71e21b6e289d26145cba1f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f307c31.tgz", "fileCount": 113, "integrity": "sha512-tuBttI/nkuzD9ScXUjOw/ConI1coCf9WjklJ86jjuSt/lSLsi+E0bUzI/8CI9KYeQ+87s5fz7Y3TSMbD1iHaTQ==", "signatures": [{"sig": "MEQCIGDAzQEotnger/nsVwbNcTyf2k1kC4rIbra879cBQLmbAiBbc0rl4BdN9BzAfrt/TOk9wNCiqoy8Wg2tARTrYWRChQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f307c31", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f307c31.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/11a22c56ca50d8fc107d985899d6459b/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.f307c31.tgz", "_integrity": "sha512-tuBttI/nkuzD9ScXUjOw/ConI1coCf9WjklJ86jjuSt/lSLsi+E0bUzI/8CI9KYeQ+87s5fz7Y3TSMbD1iHaTQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.f307c31_1751376703222_0.6254319290105437", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.3cab801": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.3cab801", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.3cab801", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "910c36831352e97740d6272f51c8e420299b46e4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.3cab801.tgz", "fileCount": 113, "integrity": "sha512-dPzHpbjIH5tszA7ytlNJjc4S61TmohCo9tkRNwZ3XKjL+vMaCXkhhdINP0IZnxYJo4TM2YUmFAIcfSsy7t1U9A==", "signatures": [{"sig": "MEYCIQDUwUmA/NIEGd4skhMcGEU0PADztDNNegyzjI1w85VApgIhAI2gybKC+R9jMEJVxUFxRx4Trvq/L/Jd6P0ZXrdO7LUg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.3cab801", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030696}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.3cab801.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/c59d142922a654a8a2b4939b654ca0b9/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.3cab801.tgz", "_integrity": "sha512-dPzHpbjIH5tszA7ytlNJjc4S61TmohCo9tkRNwZ3XKjL+vMaCXkhhdINP0IZnxYJo4TM2YUmFAIcfSsy7t1U9A==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.3", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.3cab801_1751477478979_0.9704335454875512", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.8c80330": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8c80330", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.8c80330", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "c2adfac1b79d8e39d2d122c833a5c42d8b9c236c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8c80330.tgz", "fileCount": 113, "integrity": "sha512-0fHDI0juG+tPckxSWfP73cgiW7i1oacApuul1iSvJFx7ARtflHW1762wVbZrqA/pvPE/mGNy1x6Qk30ZE0yEkg==", "signatures": [{"sig": "MEUCIBTUmncETwa+i+Cgu/klEqSrlUA4evjHODYnUgF1tVzlAiEAiQfI9qmwWzdd8La3nhA9j1v3l5V0mBoTd8CrWf05+8Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8c80330", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036475}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8c80330.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/3c8dacd7b5fd54d95146a9aacd694a08/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.8c80330.tgz", "_integrity": "sha512-0fHDI0juG+tPckxSWfP73cgiW7i1oacApuul1iSvJFx7ARtflHW1762wVbZrqA/pvPE/mGNy1x6Qk30ZE0yEkg==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.3", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.8c80330_1751486967428_0.44996585415463697", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.d2fdddf": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d2fdddf", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.d2fdddf", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "68165c20299b7e81f76f1236f1248ece5078d7d4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d2fdddf.tgz", "fileCount": 113, "integrity": "sha512-drWt7JMH4TSGZWuKS6TKQH/vULhzNxNyudAfQwbwcNQZOo+m6CEd5JhudX0csg2HTlE54bnZqQDsPtJeY0hm9Q==", "signatures": [{"sig": "MEUCIGNFQTEZIBf77oSLfvwA1woqWAnDRVbEuazJEUYkeXx/AiEAjCrM2OoFJoMIpJiSRX4L5YXyz77R+Pe/QGJ39i1UEoM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d2fdddf", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036475}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d2fdddf.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/e4187c86b18eeb01613e3d03edf42e43/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.d2fdddf.tgz", "_integrity": "sha512-drWt7JMH4TSGZWuKS6TKQH/vULhzNxNyudAfQwbwcNQZOo+m6CEd5JhudX0csg2HTlE54bnZqQDsPtJeY0hm9Q==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.3", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.d2fdddf_1751489340274_0.13952009897840512", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.9169d73": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.9169d73", "license": "MIT", "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.9169d73", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "cpu": ["wasm32"], "dist": {"shasum": "934688d1309678981a95945a9b542fb894d470ba", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.9169d73.tgz", "fileCount": 113, "integrity": "sha512-9AIGvHVfdXyyO9qrtmWcg6L8PHWfYBC9zYC3W+GRPNoXijlHSeHrWVkQI6hqpmF61FqyoVPUUSDa4kaLLi9ZuQ==", "signatures": [{"sig": "MEQCIDh8sdf9t4C4Gkvmm8LX2Z8jszsB66VadfWeSD4s9i7kAiB5AciAWxwiGTRXRQw+aLQF9zD+TQZ5zv4hm4zObvsjLQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.9169d73", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036475}, "main": "tailwindcss-oxide.wasi.cjs", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.9169d73.tgz", "browser": "tailwindcss-oxide.wasi-browser.js", "engines": {"node": ">=14.0.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/173dbb76514a810fece478fa4b48ff88/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.9169d73.tgz", "_integrity": "sha512-9AIGvHVfdXyyO9qrtmWcg6L8PHWfYBC9zYC3W+GRPNoXijlHSeHrWVkQI6hqpmF61FqyoVPUUSDa4kaLLi9ZuQ==", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "_npmVersion": "10.8.2", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "directories": {}, "_nodeVersion": "20.19.3", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "publishConfig": {"access": "public", "provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "_npmOperationalInternal": {"tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.9169d73_1751489943335_0.24009936717551517", "host": "s3://npm-registry-packages-npm-production"}}, "0.0.0-insiders.2941a7b": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.2941a7b", "cpu": ["wasm32"], "main": "tailwindcss-oxide.wasi.cjs", "license": "MIT", "engines": {"node": ">=14.0.0"}, "publishConfig": {"provenance": true, "access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/tailwindlabs/tailwindcss.git", "directory": "crates/node"}, "browser": "tailwindcss-oxide.wasi-browser.js", "dependencies": {"@napi-rs/wasm-runtime": "^0.2.11", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "tslib": "^2.8.0"}, "_id": "@tailwindcss/oxide-wasm32-wasi@0.0.0-insiders.2941a7b", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "readmeFilename": "README.md", "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "_integrity": "sha512-E0yxexE88JbY7IMbDI6DOup+DOO2WDtrWnxZ4fvlhaG6/7vV167YxEDNtaUXxFoJFvxJCpSpUzp6kdhPFfNbYQ==", "_resolved": "/private/var/folders/y6/nj790rtn62lfktb1sh__79hc0000gn/T/0923c174c4f998cc0cc5b74b9fa17fca/tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2941a7b.tgz", "_from": "file:tailwindcss-oxide-wasm32-wasi-0.0.0-insiders.2941a7b.tgz", "_nodeVersion": "20.19.3", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-E0yxexE88JbY7IMbDI6DOup+DOO2WDtrWnxZ4fvlhaG6/7vV167YxEDNtaUXxFoJFvxJCpSpUzp6kdhPFfNbYQ==", "shasum": "bcff8d4c9a4b842b2c2663667d5fb4da11356682", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.2941a7b.tgz", "fileCount": 113, "unpackedSize": 13036475, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.2941a7b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIH808bx5LFMyeMFtjdXDa4YA9P7N0BZIkFSTF/lgW98/AiEAqoTSDMmmWq8JsXy/XZDXGH0gJEjnOAbybPsFlsRcYeA="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/oxide-wasm32-wasi_0.0.0-insiders.2941a7b_1751556511065_0.42388592512654677"}, "_hasShrinkwrap": false}}, "time": {"created": "2025-04-11T16:21:20.657Z", "modified": "2025-07-03T15:28:31.752Z", "0.0.0-insiders.d801d8d": "2025-04-11T16:21:20.919Z", "0.0.0-insiders.bbd916a": "2025-04-12T11:36:03.023Z", "0.0.0-insiders.cf2591c": "2025-04-14T09:19:51.659Z", "0.0.0-insiders.aa836d3": "2025-04-14T15:40:13.506Z", "4.1.4": "2025-04-14T17:01:03.320Z", "0.0.0-insiders.25539e3": "2025-04-14T17:14:28.527Z", "0.0.0-insiders.adcf1de": "2025-04-15T10:10:56.685Z", "0.0.0-insiders.8feb6a7": "2025-04-18T08:54:21.432Z", "0.0.0-insiders.fc4afc2": "2025-04-22T09:03:03.387Z", "0.0.0-insiders.650558d": "2025-04-22T13:43:17.007Z", "0.0.0-insiders.ee0d752": "2025-04-22T13:43:30.949Z", "0.0.0-insiders.8bf06ab": "2025-04-22T14:10:45.817Z", "0.0.0-insiders.25ec6a3": "2025-04-22T15:04:13.353Z", "0.0.0-insiders.8e826b1": "2025-04-22T15:20:00.503Z", "0.0.0-insiders.a7f4a4d": "2025-04-23T13:23:42.905Z", "0.0.0-insiders.46758f7": "2025-04-24T09:28:15.782Z", "0.0.0-insiders.2bf2b4d": "2025-04-24T09:30:14.722Z", "0.0.0-insiders.d780a55": "2025-04-25T10:02:02.424Z", "0.0.0-insiders.231cddd": "2025-04-25T10:13:07.715Z", "0.0.0-insiders.52000a3": "2025-04-25T10:14:43.004Z", "0.0.0-insiders.62ca1ec": "2025-04-25T11:09:39.634Z", "0.0.0-insiders.ba10379": "2025-04-26T19:24:13.583Z", "0.0.0-insiders.af1d4aa": "2025-04-28T09:04:28.624Z", "0.0.0-insiders.3a1b27e": "2025-04-28T17:32:39.262Z", "0.0.0-insiders.d2daf59": "2025-04-28T17:38:32.018Z", "0.0.0-insiders.9fec4ef": "2025-04-29T16:11:32.487Z", "0.0.0-insiders.d3846a4": "2025-04-29T16:26:01.273Z", "0.0.0-insiders.dbc8023": "2025-04-29T17:16:00.882Z", "0.0.0-insiders.ab4eb18": "2025-04-30T09:58:52.633Z", "0.0.0-insiders.a636933": "2025-04-30T12:41:14.674Z", "0.0.0-insiders.45cd32e": "2025-04-30T15:06:06.625Z", "4.1.5": "2025-04-30T15:53:06.917Z", "0.0.0-insiders.4e42756": "2025-05-02T21:26:19.019Z", "0.0.0-insiders.c095071": "2025-05-02T22:56:16.672Z", "0.0.0-insiders.dd5ec49": "2025-05-02T23:11:30.778Z", "0.0.0-insiders.e00d092": "2025-05-04T17:36:43.265Z", "0.0.0-insiders.473f024": "2025-05-05T10:38:29.359Z", "0.0.0-insiders.d38554d": "2025-05-05T14:37:46.244Z", "0.0.0-insiders.6a1df6a": "2025-05-05T15:37:42.966Z", "0.0.0-insiders.5c5ae04": "2025-05-05T15:47:17.080Z", "0.0.0-insiders.ed45952": "2025-05-06T11:03:15.933Z", "0.0.0-insiders.4f8539c": "2025-05-06T13:40:46.462Z", "0.0.0-insiders.449dfcf": "2025-05-06T18:10:33.006Z", "0.0.0-insiders.d8c4df8": "2025-05-07T14:29:12.151Z", "0.0.0-insiders.179e5dd": "2025-05-08T09:35:10.878Z", "0.0.0-insiders.17ca56d": "2025-05-08T16:37:51.217Z", "0.0.0-insiders.62706dc": "2025-05-08T16:54:44.641Z", "0.0.0-insiders.56b22bb": "2025-05-08T20:37:54.611Z", "0.0.0-insiders.ff9f183": "2025-05-08T21:04:26.294Z", "0.0.0-insiders.ae57d26": "2025-05-09T10:22:05.619Z", "0.0.0-insiders.2f6679a": "2025-05-09T11:59:37.212Z", "0.0.0-insiders.47bb007": "2025-05-09T13:04:03.631Z", "0.0.0-insiders.2d13998": "2025-05-09T13:48:23.760Z", "4.1.6": "2025-05-09T13:56:49.328Z", "0.0.0-insiders.737994b": "2025-05-09T16:37:31.248Z", "0.0.0-insiders.3386049": "2025-05-09T20:15:01.886Z", "0.0.0-insiders.f0986ce": "2025-05-09T20:21:44.110Z", "0.0.0-insiders.0d975f5": "2025-05-10T11:29:06.414Z", "0.0.0-insiders.19e2b29": "2025-05-12T09:10:48.662Z", "0.0.0-insiders.5688f0a": "2025-05-12T11:17:43.854Z", "0.0.0-insiders.ba944ca": "2025-05-12T13:26:25.637Z", "0.0.0-insiders.4fba87b": "2025-05-12T13:59:24.132Z", "0.0.0-insiders.ef2e6c7": "2025-05-13T12:29:11.711Z", "0.0.0-insiders.498f9ff": "2025-05-13T15:49:21.041Z", "0.0.0-insiders.4db711d": "2025-05-14T09:49:21.480Z", "0.0.0-insiders.e57a2f5": "2025-05-14T13:01:40.116Z", "0.0.0-insiders.f3157cd": "2025-05-15T11:08:50.923Z", "0.0.0-insiders.6fb98d2": "2025-05-15T11:32:02.549Z", "0.0.0-insiders.1ada8e0": "2025-05-15T11:32:37.765Z", "0.0.0-insiders.bf591fe": "2025-05-15T13:00:23.021Z", "0.0.0-insiders.74e084a": "2025-05-15T13:39:47.205Z", "4.1.7": "2025-05-15T14:11:04.444Z", "0.0.0-insiders.d69604e": "2025-05-15T16:22:15.632Z", "0.0.0-insiders.c7d368b": "2025-05-16T11:01:38.798Z", "0.0.0-insiders.71fb9cd": "2025-05-19T10:55:22.995Z", "0.0.0-insiders.a42251c": "2025-05-19T14:50:32.097Z", "0.0.0-insiders.9df5ba7": "2025-05-19T15:36:10.228Z", "0.0.0-insiders.7013ca3": "2025-05-19T19:28:29.532Z", "0.0.0-insiders.24ed64e": "2025-05-19T20:16:48.346Z", "0.0.0-insiders.ed3cecd": "2025-05-23T10:24:34.610Z", "0.0.0-insiders.9cb3899": "2025-05-23T15:29:21.006Z", "0.0.0-insiders.884f02c": "2025-05-23T20:07:27.142Z", "0.0.0-insiders.8fcc633": "2025-05-23T22:06:19.485Z", "0.0.0-insiders.5d4e8f0": "2025-05-23T22:06:38.279Z", "0.0.0-insiders.5131237": "2025-05-26T14:42:11.420Z", "0.0.0-insiders.1d4c263": "2025-05-26T17:00:23.953Z", "0.0.0-insiders.a37f6ba": "2025-05-27T09:21:09.282Z", "0.0.0-insiders.58a6ad0": "2025-05-27T17:15:44.081Z", "0.0.0-insiders.4bfacb3": "2025-05-27T18:27:41.267Z", "0.0.0-insiders.193eb84": "2025-05-28T15:11:03.430Z", "4.1.8": "2025-05-28T15:30:03.959Z", "0.0.0-insiders.3c629de": "2025-05-29T17:46:55.641Z", "0.0.0-insiders.31c0a21": "2025-05-30T13:42:35.014Z", "0.0.0-insiders.54c86d4": "2025-05-30T14:16:56.855Z", "0.0.0-insiders.191195a": "2025-06-02T13:48:33.574Z", "0.0.0-insiders.b3fde17": "2025-06-03T09:03:46.075Z", "0.0.0-insiders.f425720": "2025-06-03T18:36:23.202Z", "0.0.0-insiders.0c1c0c4": "2025-06-05T09:39:31.877Z", "0.0.0-insiders.8bfbac5": "2025-06-05T13:32:17.473Z", "0.0.0-insiders.288ab3e": "2025-06-05T13:43:44.101Z", "0.0.0-insiders.63f6a6c": "2025-06-05T17:39:44.041Z", "0.0.0-insiders.21ece6c": "2025-06-05T21:12:46.019Z", "0.0.0-insiders.ada85b1": "2025-06-06T16:22:57.090Z", "0.0.0-insiders.f0f42f6": "2025-06-06T19:51:54.852Z", "0.0.0-insiders.fd95af4": "2025-06-09T13:31:35.886Z", "0.0.0-insiders.bea843c": "2025-06-09T14:13:04.252Z", "0.0.0-insiders.da08956": "2025-06-10T11:15:46.628Z", "0.0.0-insiders.aa817fb": "2025-06-11T11:13:34.990Z", "0.0.0-insiders.b88371a": "2025-06-11T14:06:30.787Z", "4.1.9": "2025-06-11T14:34:38.446Z", "0.0.0-insiders.427649e": "2025-06-11T18:05:35.172Z", "0.0.0-insiders.ddb0bef": "2025-06-11T20:25:59.973Z", "0.0.0-insiders.d06bbb8": "2025-06-11T20:34:37.945Z", "4.1.10": "2025-06-11T20:46:39.365Z", "0.0.0-insiders.2ebaff2": "2025-06-11T21:15:53.483Z", "0.0.0-insiders.7f97179": "2025-06-11T21:31:34.974Z", "0.0.0-insiders.bab16ae": "2025-06-16T14:36:40.636Z", "0.0.0-insiders.4453496": "2025-06-17T14:55:23.848Z", "0.0.0-insiders.63b5d7b": "2025-06-17T18:57:07.902Z", "0.0.0-insiders.75cbfc2": "2025-06-18T16:55:39.218Z", "0.0.0-insiders.f4a7eea": "2025-06-18T19:27:04.312Z", "0.0.0-insiders.c5a997c": "2025-06-19T19:01:17.032Z", "0.0.0-insiders.1a35fbc": "2025-06-20T10:32:47.043Z", "0.0.0-insiders.d788657": "2025-06-20T18:12:32.429Z", "0.0.0-insiders.f0e2f5b": "2025-06-21T19:47:28.564Z", "0.0.0-insiders.767a60a": "2025-06-24T14:07:46.107Z", "0.0.0-insiders.b9007dd": "2025-06-24T14:11:49.033Z", "0.0.0-insiders.aa85931": "2025-06-24T16:39:19.963Z", "0.0.0-insiders.6ad26de": "2025-06-26T09:35:54.949Z", "0.0.0-insiders.b24457a": "2025-06-26T10:52:04.263Z", "4.1.11": "2025-06-26T10:59:32.233Z", "0.0.0-insiders.b716d10": "2025-06-26T19:52:43.392Z", "0.0.0-insiders.2030e94": "2025-06-27T16:03:39.344Z", "0.0.0-insiders.ca7caae": "2025-06-30T13:40:26.445Z", "0.0.0-insiders.7946db0": "2025-06-30T13:49:27.980Z", "0.0.0-insiders.05b65d5": "2025-06-30T16:37:28.576Z", "0.0.0-insiders.f307c31": "2025-07-01T13:31:43.446Z", "0.0.0-insiders.3cab801": "2025-07-02T17:31:19.192Z", "0.0.0-insiders.8c80330": "2025-07-02T20:09:27.627Z", "0.0.0-insiders.d2fdddf": "2025-07-02T20:49:00.470Z", "0.0.0-insiders.9169d73": "2025-07-02T20:59:03.527Z", "0.0.0-insiders.2941a7b": "2025-07-03T15:28:31.310Z"}, "bugs": {"url": "https://github.com/tailwindlabs/tailwindcss/issues"}, "license": "MIT", "homepage": "https://github.com/tailwindlabs/tailwindcss#readme", "repository": {"url": "git+https://github.com/tailwindlabs/tailwindcss.git", "type": "git", "directory": "crates/node"}, "description": "This is the **wasm32-wasip1-threads** binary for `@tailwindcss/oxide`", "maintainers": [{"name": "reinink", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "malfaitrobin", "email": "<EMAIL>"}, {"name": "thecrypticace", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}