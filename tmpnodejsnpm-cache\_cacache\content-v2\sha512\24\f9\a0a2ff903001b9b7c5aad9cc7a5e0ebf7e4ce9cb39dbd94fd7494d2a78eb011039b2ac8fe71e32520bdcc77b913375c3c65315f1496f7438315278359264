{"_id": "chalk", "_rev": "1223-351d11b0b26c9602eec315628b296b71", "name": "chalk", "dist-tags": {"next": "3.0.0-beta.2", "latest": "5.4.1"}, "versions": {"0.1.0": {"name": "chalk", "version": "0.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chalk@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "dist": {"shasum": "69afbee2ffab5e0db239450767a6125cbea50fa2", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.1.0.tgz", "integrity": "sha512-E1+My+HBCBHA6fBUZlbPnrOMrGKnc3QAXGEvCk/lpEG/ZKowZFg01dXt6RCYJMvTWYgxHWTyZQ6qkCrVPKJ2YQ==", "signatures": [{"sig": "MEQCICcizJ0CViMVIGAdOi/w9z8s5d3Zn23t9fMlbuGpzGENAiB4IAPyJ4DSwN/KaA6WIkJE7cqo3iZiTLD1ClLbpbCEiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "chalk", "_from": ".", "files": ["chalk.js"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/chalk.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "Terminal string styling done right", "directories": {}, "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.1.0"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.1.1": {"name": "chalk", "version": "0.1.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chalk@0.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "dist": {"shasum": "fe6d90ae2c270424720c87ed92d36490b7d36ea0", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.1.1.tgz", "integrity": "sha512-NJbznmWlxmS5Co0rrLJYO0U3QW6IzWw2EuojeOFn4e8nD1CYR5Ie60CEEmHrF8DXtfd83pdF0xYWVCXbRysrDQ==", "signatures": [{"sig": "MEYCIQDyRcBiBgR63thSUFHx+SV7hi4FtDRy5WNaeEfCEr6c6wIhAJbSSa3GwYNrT0RVe6AVJcvtXcjsOVg7yTdLVbxlzfK7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "chalk", "_from": ".", "files": ["chalk.js"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/chalk.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "Terminal string styling done right", "directories": {}, "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.1.0"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.2.0": {"name": "chalk", "version": "0.2.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chalk@0.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "dist": {"shasum": "47270e80edce0e219911af65479d17db525ff5db", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.2.0.tgz", "integrity": "sha512-CHq4xplBE+jhsJKGmh8AegFpEsC84kQNPMeL2mjrD5ojPc1LqNV1q5opCBU7BcRxWbpX+S8s+q4LFaqjP1rZmg==", "signatures": [{"sig": "MEUCIQDfcM6X1OG63W5oEByNa/SCZJWAU3mNjL4zY35I5yHbGwIgNPIUWRFZl6a3ByWBLR4cXyn2WHnYru0PY39fougSJzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "chalk", "_from": ".", "files": ["chalk.js"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/chalk.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "Terminal string styling done right", "directories": {}, "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.2.0"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.2.1": {"name": "chalk", "version": "0.2.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chalk@0.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "dist": {"shasum": "7613e1575145b21386483f7f485aa5ffa8cbd10c", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.2.1.tgz", "integrity": "sha512-nmVapomwGksziCuynboy7I+dtW4ytIdqXPlrfY/ySx8l8EqFRGHyA04q6NMNpOri8XliGUGwXyfScVl48zFHbw==", "signatures": [{"sig": "MEQCIBda89bi2LZLymi7dToSY9rMAFRCXyg6fqyF15yoQO0tAiAT1ZyRicgVCJku8vgl5CUqx27uRUEnRK1GZ2kowTHh4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "chalk", "_from": ".", "files": ["chalk.js"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/chalk.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Terminal string styling done right", "directories": {}, "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.2.0"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.3.0": {"name": "chalk", "version": "0.3.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chalk@0.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "dist": {"shasum": "1c98437737f1199ebcc1d4c48fd41b9f9c8e8f23", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.3.0.tgz", "integrity": "sha512-OcfgS16PHpCu2Q4TNMtk0aZNx8PyeNiiB+6AgGH91fhT9hJ3v6pIIJ3lxlaOEDHlTm8t3wDe6bDGamvtIokQTg==", "signatures": [{"sig": "MEYCIQD/TMCMMPNIkr1dvG6kRR/CWZZJtqKGoMdimY4IY6MEsgIhAJ1HbZkkcygfBmN1N4nqpYpBdAbRlLQOQoHfhOmBxYT1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "chalk", "_from": ".", "files": ["chalk.js"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/chalk.git", "type": "git"}, "_npmVersion": "1.3.10", "description": "Terminal string styling done right", "directories": {}, "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.2.0"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.4.0": {"name": "chalk", "version": "0.4.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chalk@0.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "dist": {"shasum": "5199a3ddcd0c1efe23bc08c1b027b06176e0c64f", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.4.0.tgz", "integrity": "sha512-sQfYDlfv2DGVtjdoQqxS0cEZDroyG8h6TamA6rvxwlrU5BaSLDx9xhatBYl2pxZ7gmpNaPFVwBtdGdu5rQ+tYQ==", "signatures": [{"sig": "MEQCIBe296G5Ckfk2TKZTU3bGX1WzY2zO0oXe5yCT2EecEajAiABeRfWovOVYu9t02fFT3Pnrbreb2qwOUoA6c16yVmoUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/chalk", "type": "git"}, "_npmVersion": "1.3.17", "description": "Terminal string styling done right. Created because the `colors` module does some really horrible things.", "directories": {}, "dependencies": {"has-color": "~0.1.0", "strip-ansi": "~0.1.0", "ansi-styles": "~1.0.0"}, "devDependencies": {"mocha": "~1.x"}}, "0.5.0": {"name": "chalk", "version": "0.5.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@0.5.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "dist": {"shasum": "375dfccbc21c0a60a8b61bc5b78f3dc2a55c212f", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.5.0.tgz", "integrity": "sha512-rTCcbF0wrwC+kKzA/3SpBc6PrcOx/+PRQVtS3PEDw5tGzqycpB48dRS8ByxFDd8Ij5E1RtafZ34R1X9VLI/vUQ==", "signatures": [{"sig": "MEQCIEGqQOniqI9HsAKeDEvFr1KDB3AZGGcdwYu4q0ylIHtUAiA65kQmXrb1YvhBtaJjhFQBrt7C9MIZfOOBOrg8SOziBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "375dfccbc21c0a60a8b61bc5b78f3dc2a55c212f", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/chalk", "type": "git"}, "_npmVersion": "1.4.9", "description": "Terminal string styling done right. Created because the `colors` module does some really horrible things.", "directories": {}, "dependencies": {"has-ansi": "^0.1.0", "strip-ansi": "^0.3.0", "ansi-styles": "^1.1.0", "supports-color": "^0.2.0", "escape-string-regexp": "^1.0.0"}, "devDependencies": {"mocha": "*", "matcha": "^0.5.0"}}, "0.5.1": {"name": "chalk", "version": "0.5.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@0.5.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "dist": {"shasum": "663b3a648b68b55d04690d49167aa837858f2174", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.5.1.tgz", "integrity": "sha512-bIKA54hP8iZhyDT81TOsJiQvR1gW+ZYSXFaZUAvoD4wCHdbHY2actmpTE4x344ZlFqHbvoxKOaESULTZN2gstg==", "signatures": [{"sig": "MEUCIHm4uVj8/eT3R7g1Q6tzTAMZ2pdqmG9OumNXatGp4ykfAiEAqiq/PLZqnD8UaVvvHh3ElJAhsquDcnDKTEjLo6bZyNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "663b3a648b68b55d04690d49167aa837858f2174", "engines": {"node": ">=0.10.0"}, "gitHead": "994758f01293f1fdcf63282e9917cb9f2cfbdaac", "scripts": {"test": "mocha", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/chalk", "type": "git"}, "_npmVersion": "1.4.14", "description": "Terminal string styling done right. Created because the `colors` module does some really horrible things.", "directories": {}, "dependencies": {"has-ansi": "^0.1.0", "strip-ansi": "^0.3.0", "ansi-styles": "^1.1.0", "supports-color": "^0.2.0", "escape-string-regexp": "^1.0.0"}, "devDependencies": {"mocha": "*", "matcha": "^0.5.0"}}, "1.0.0": {"name": "chalk", "version": "1.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/chalk", "bugs": {"url": "https://github.com/sindresorhus/chalk/issues"}, "dist": {"shasum": "b3cf4ed0ff5397c99c75b8f679db2f52831f96dc", "tarball": "https://registry.npmjs.org/chalk/-/chalk-1.0.0.tgz", "integrity": "sha512-1TE3hpADga5iWinlcCpyhC7fTl9uQumLD8i2jJoJeVg7UbveY5jj7F6uCq8w0hQpSeLhaPn5QFe8e56toMVP1A==", "signatures": [{"sig": "MEQCIA2fHIK4HaMM5vT8+vgIZDSYl2yR1wBMDpQnyK2mEp73AiBv9R+hCSU6L2lKmjPk37XVGjJaJPMWSPFUT6HNBGXJJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "b3cf4ed0ff5397c99c75b8f679db2f52831f96dc", "engines": {"node": ">=0.10.0"}, "gitHead": "8864d3563313ed15574a38dd5c9d5966080c46ce", "scripts": {"test": "mocha", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/chalk", "type": "git"}, "_npmVersion": "2.5.1", "description": "Terminal string styling done right. Much color.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"has-ansi": "^1.0.3", "strip-ansi": "^2.0.1", "ansi-styles": "^2.0.1", "supports-color": "^1.3.0", "escape-string-regexp": "^1.0.2"}, "devDependencies": {"mocha": "*", "matcha": "^0.6.0"}}, "1.1.0": {"name": "chalk", "version": "1.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "dist": {"shasum": "09b453cec497a75520e4a60ae48214a8700e0921", "tarball": "https://registry.npmjs.org/chalk/-/chalk-1.1.0.tgz", "integrity": "sha512-pn7bzDYUIrL0KRp/KK5B+sej6uYtzQ5hYOdLU+L3MVWHCgoYi4aUYdh2/R2rsdURIoOK/ptZi5FDtLdjvKYQ7g==", "signatures": [{"sig": "MEYCIQDvFUAscqbO0W1o8ynaLsS3H/qRFyNIcBpeciwTE0L2eAIhAJLC4kNijQLiP53FxwjKtIk/yb2Mz5bSJkvQcbmO38Lh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "09b453cec497a75520e4a60ae48214a8700e0921", "engines": {"node": ">=0.10.0"}, "gitHead": "e9bb6e6000b1c5d4508afabfdc85dd70f582f515", "scripts": {"test": "mocha", "bench": "matcha benchmark.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/chalk/chalk", "type": "git"}, "_npmVersion": "2.10.1", "description": "Terminal string styling done right. Much color.", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "ansi-styles": "^2.1.0", "supports-color": "^2.0.0", "escape-string-regexp": "^1.0.2"}, "devDependencies": {"nyc": "^3.0.0", "mocha": "*", "matcha": "^0.6.0", "semver": "^4.3.3", "coveralls": "^2.11.2", "resolve-from": "^1.0.0", "require-uncached": "^1.0.2"}}, "1.1.1": {"name": "chalk", "version": "1.1.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@1.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "509afb67066e7499f7eb3535c77445772ae2d019", "tarball": "https://registry.npmjs.org/chalk/-/chalk-1.1.1.tgz", "integrity": "sha512-W10W+QfIxJlTm3VRtg8eafwUBkDfUPFvRvPv4jCD9vF4+HzlAyXJ7P3Y5yw/r+gJ1TzFEU6oFqMgp1dIVpYr0A==", "signatures": [{"sig": "MEUCIQDJnOp0QV+THZBj97NAJIJ/7FOfH1ApR+V17cduIWef4QIgKx6kVG0zABWS4/A8EyL/AV5PuxB8aCmy9tNSXcFiXa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "509afb67066e7499f7eb3535c77445772ae2d019", "engines": {"node": ">=0.10.0"}, "gitHead": "8b554e254e89c85c1fd04dcc444beeb15824e1a5", "scripts": {"test": "xo && mocha", "bench": "matcha benchmark.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "2.13.5", "description": "Terminal string styling done right. Much color.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "ansi-styles": "^2.1.0", "supports-color": "^2.0.0", "escape-string-regexp": "^1.0.2"}, "devDependencies": {"xo": "*", "nyc": "^3.0.0", "mocha": "*", "matcha": "^0.6.0", "semver": "^4.3.3", "coveralls": "^2.11.2", "resolve-from": "^1.0.0", "require-uncached": "^1.0.2"}}, "1.1.2": {"name": "chalk", "version": "1.1.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@1.1.2", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "53e9f9e7742f7edf23065c29c0219175a7869155", "tarball": "https://registry.npmjs.org/chalk/-/chalk-1.1.2.tgz", "integrity": "sha512-QBKX51aavmpKcCkgrJXhjS5b3rCgH2Wn99BYqUV2H1FjTP7Mm4KTcskSxuKrfhQKt69mBn9jH4Kb2xnchvEaOw==", "signatures": [{"sig": "MEYCIQDR73JAj03sZJRAoJDQVm+RirI4Iry8ZuCvjsoI7cTA4wIhAOjIHdQPS2bXbYZucSmdnq17cnoYJ4fHFrpbk9slQEst", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "53e9f9e7742f7edf23065c29c0219175a7869155", "engines": {"node": ">=0.10.0"}, "gitHead": "a838948dcbf2674dd28adfbb78e791900ae741e9", "scripts": {"test": "xo && mocha", "bench": "matcha benchmark.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "chalk@1.1.2 introduces breaking changes. Please use 1.1.3 or above.", "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Terminal string styling done right. Much color.", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"ansi-styles": "^2.2.1", "supports-color": "^3.1.2", "escape-string-regexp": "^1.0.2"}, "devDependencies": {"xo": "*", "nyc": "^5.2.0", "mocha": "*", "matcha": "^0.6.0", "semver": "^5.1.0", "coveralls": "^2.11.2", "resolve-from": "^2.0.0", "require-uncached": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/chalk-1.1.2.tgz_1459207923607_0.6091341155115515", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.3": {"name": "chalk", "version": "1.1.3", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@1.1.3", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "a8115c55e4a702fe4d150abd3872822a7e09fc98", "tarball": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==", "signatures": [{"sig": "MEYCIQCYydQMZbUiHhEF1lG6Vvl8dFiZehECOS8naCRKiBaDWAIhAMB+3sTOs5gMFmQyiUE6HzXaIsahGhReBUr4OYaI+iCX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "a8115c55e4a702fe4d150abd3872822a7e09fc98", "engines": {"node": ">=0.10.0"}, "gitHead": "0d8d8c204eb87a4038219131ad4d8369c9f59d24", "scripts": {"test": "xo && mocha", "bench": "matcha benchmark.js", "coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Terminal string styling done right. Much color.", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "ansi-styles": "^2.2.1", "supports-color": "^2.0.0", "escape-string-regexp": "^1.0.2"}, "devDependencies": {"xo": "*", "nyc": "^3.0.0", "mocha": "*", "matcha": "^0.6.0", "semver": "^4.3.3", "coveralls": "^2.11.2", "resolve-from": "^1.0.0", "require-uncached": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/chalk-1.1.3.tgz_1459210604109_0.3892582862172276", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "chalk", "version": "2.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.0.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "c25c5b823fedff921aa5d83da3ecb5392e84e533", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.0.0.tgz", "integrity": "sha512-7jy/5E6bVCRhLlvznnsbVPjsARuVC9HDkBjUKVaOmUrhsp6P3ExUUcW09htM7/qieRH+D2lHVpNbuYh7GjVJ0g==", "signatures": [{"sig": "MEQCIHTAVf6u8jvZCfxvAN3anX5/E4q4xlYfkCEkZENhrWYbAiBLQg/CjJ1n1peDolmlJB8V892hSsvTW1L1zOl0qA1IHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "templates.js"], "engines": {"node": ">=4"}, "gitHead": "3fca6150e23439e783409f5c8f948f767c2ddc5a", "scripts": {"test": "xo && nyc mocha", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Terminal string styling done right. Much color", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "nyc": "^11.0.2", "mocha": "*", "matcha": "^0.7.0", "coveralls": "^2.11.2", "import-fresh": "^2.0.0", "resolve-from": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk-2.0.0.tgz_1498780161964_0.21432337583974004", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "chalk", "version": "2.0.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.0.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "dbec49436d2ae15f536114e76d14656cdbc0f44d", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.0.1.tgz", "integrity": "sha512-Mp+FX<PERSON>+FrwY/XYV45b2YD3E8i3HwnEAoFcM0qlZzq/RZ9RwWitt2Y/c7cqRAz70U7hfekqx6qNYthuKFO6K0g==", "signatures": [{"sig": "MEQCICfJZUefrwq1SvzsgOv2Q/7HhKkcNRqOTzI/P/PsGm7dAiAVHrhAfBt0v3Bjqycj4E95pSguOX6OdYQeIElUAHjbvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "templates.js"], "engines": {"node": ">=4"}, "gitHead": "5827081719944a2f903b52a88baeec1ec8581f82", "scripts": {"test": "xo && nyc mocha", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Please upgrade to Chalk 2.1.0 - template literals in this version (2.0.1) are quite buggy.", "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Terminal string styling done right. Much color", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "nyc": "^11.0.2", "mocha": "*", "matcha": "^0.7.0", "coveralls": "^2.11.2", "import-fresh": "^2.0.0", "resolve-from": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk-2.0.1.tgz_1498793206623_0.8611406192649156", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "chalk", "version": "2.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.1.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "ac5becf14fa21b99c6c92ca7a7d7cfd5b17e743e", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.1.0.tgz", "integrity": "sha512-LUHGS/dge4ujbXMJrnihYMcL4AoOweGnw9Tp3kQuqy1Kx5c1qKjqvMJZ6nVJPMWJtKCTN72ZogH3oeSO9g9rXQ==", "signatures": [{"sig": "MEQCIAR2pBNunhcCtIpoVtR8CzUr4fkHHazxHsUmYiEOl3SyAiAj1UgZ9m1qQjHPwS0lWc7+x71FyiJ9BnT8LKzU8hZpaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "templates.js"], "engines": {"node": ">=4"}, "gitHead": "38f641a222d7ee0e607e4e5209d3931d2af1e409", "scripts": {"test": "xo && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.7.0", "matcha": "^0.7.0", "coveralls": "^2.11.2", "import-fresh": "^2.0.0", "resolve-from": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk-2.1.0.tgz_1502078203099_0.6595528507605195", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "chalk", "version": "2.2.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.2.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "477b3bf2f9b8fd5ca9e429747e37f724ee7af240", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.2.0.tgz", "integrity": "sha512-0BMM/2hG3ZaoPfR6F+h/oWpZtsh3b/s62TjSM6MGCJWEbJDN1acqCXvyhhZsDSVFklpebUoQ5O1kKC7lOzrn9g==", "signatures": [{"sig": "MEYCIQCJ26ChsXMgEr8DGfcdGhwoECmCJIQc8C/WtfNNghWXpwIhALxt4sAYIddaWoA+laUzI/bv5TtudGNeOLEa+lB3iTKA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "templates.js", "types/index.d.ts"], "types": "types/index.d.ts", "engines": {"node": ">=4"}, "gitHead": "d86db88e778fa856f4d6f5f68c588750ca06b822", "scripts": {"test": "xo && tsc --project types && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.8.0", "matcha": "^0.7.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk-2.2.0.tgz_1508296541817_0.8590951061341912", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "chalk", "version": "2.2.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.2.2", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "4403f5cf18f35c05f51fbdf152bf588f956cf7cb", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.2.2.tgz", "integrity": "sha512-LvixLAQ4MYhbf7hgL4o5PeK32gJKvVzDRiSNIApDofQvyhl8adgG2lJVXn4+ekQoK7HL9RF8lqxwerpe0x2pCw==", "signatures": [{"sig": "MEYCIQDfa8X3hU20LXJII/tFJYOtrI+If9MXC6lVP2kVo2TeagIhAJTH6/ofItVCqYxnT7cTqmAxRF8XTpQFWlvUr/dLjfi+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "templates.js", "types/index.d.ts"], "types": "types/index.d.ts", "engines": {"node": ">=4"}, "gitHead": "e1177ec3628f6d0d37489c1e1accd2c389a376a8", "scripts": {"test": "xo && tsc --project types && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.8.0", "matcha": "^0.7.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk-2.2.2.tgz_1508815246099_0.3707860491704196", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "chalk", "version": "2.3.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.3.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "b5ea48efc9c1793dccc9b4767c93914d3f2d52ba", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.3.0.tgz", "integrity": "sha512-Az5zJR2CBujap2rqXGaJKaPHyJ0IrUimvYNX+ncCy8PJP4ltOGTrHUIo097ZaL2zMeKYpiCdqDvS6zdrTFok3Q==", "signatures": [{"sig": "MEYCIQCmt7XnAO8uN79Qxpb8HhB5EzqXR5F9Xz+dizDO68VggQIhAPCzW1TPRUP1vXrzImjrGiRQFFXTq6uWu8l+3dlLRyJm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "templates.js", "types/index.d.ts"], "types": "types/index.d.ts", "engines": {"node": ">=4"}, "gitHead": "14e0aa97727019b22f0a003fdc631aeec5e2e24c", "scripts": {"test": "xo && tsc --project types && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.8.0", "matcha": "^0.7.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk-2.3.0.tgz_1508818375657_0.9021007190458477", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "chalk", "version": "2.3.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.3.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "523fe2678aec7b04e8041909292fe8b17059b796", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.3.1.tgz", "fileCount": 6, "integrity": "sha512-QUU4ofkDoMIVO7hcx1iPTISs88wsO8jA92RQIm4JAwZvFGGAV2hSAA1NX7oVj2Ej2Q6NDTcRDjPTFrMCRZoJ6g==", "signatures": [{"sig": "MEUCIQDq923pbuma8n5CHp1BmZnEpfSBznm9pjEsnO89V1Fd1QIgPAoDos2IHJA/kzqVf2mD25YwAbgXJ4YOKduTvo92f94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24721}, "files": ["index.js", "templates.js", "types/index.d.ts"], "types": "types/index.d.ts", "engines": {"node": ">=4"}, "gitHead": "ae8a03f2c5c49896adeb3dd4ec5350e4ab9449a2", "scripts": {"test": "xo && tsc --project types && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"ansi-styles": "^3.2.0", "supports-color": "^5.2.0", "escape-string-regexp": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.9.0", "matcha": "^0.7.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_2.3.1_1518355108425_0.3816906865374552", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "chalk", "version": "2.3.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.3.2", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "250dc96b07491bfd601e648d66ddf5f60c7a5c65", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.3.2.tgz", "fileCount": 6, "integrity": "sha512-ZM4j2/ld/YZDc3Ma8PgN7gyAk+kHMMMyzLNryCPGhWrsfAuDVeuid5bpRFTDgMH9JBK2lA4dyyAkkZYF/WcqDQ==", "signatures": [{"sig": "MEQCIGrPWo1zy8RefMcSH+1wPT00s3HsqCjGvfnnE3kKN0BMAiA90/5NkYnkzLmda2udxxQfLxRPdxx1Gf9nafuAeaTxrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24713}, "files": ["index.js", "templates.js", "types/index.d.ts"], "types": "types/index.d.ts", "engines": {"node": ">=4"}, "gitHead": "84f27d4bd86f7f482a32652ae536cd996ad204bd", "scripts": {"test": "xo && tsc --project types && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"ansi-styles": "^3.2.1", "supports-color": "^5.3.0", "escape-string-regexp": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.9.0", "matcha": "^0.7.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_2.3.2_1520012630405_0.9222977073145247", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "chalk", "version": "2.4.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.4.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"], "ignores": ["test/_flow.js"]}, "dist": {"shasum": "a060a297a6b57e15b61ca63ce84995daa0fe6e52", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.4.0.tgz", "fileCount": 7, "integrity": "sha512-Wr/w0f4o9LuE7K53cD0qmbAMM+2XNLzR29vFn5hqko4sxGlUsyy363NvmyGIyk5tpe9cjTr9SJYbysEyPkRnFw==", "signatures": [{"sig": "MEQCIHWaTr0kn5hhbrdU9rauwwyPdBW6TxZnI4Lc23AJQgTCAiBJcOQo3Y7yHbfSuuL++TjazCour+dgSoT3qw/rRcCHgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1Xf3CRA9TVsSAnZWagAA1DcP/2EhxWse6mGwicTqM2U5\nQl2Xol74cFmd6b4nCGZnGycgatfJtyhb1YoH/vL3uNqGFrQGBwAr4GoZxhGd\n7kL0xKWnfhGHFeUe//fSCklj4Aff700RteXornlDFxbK5jVELyYcXfG5xJ5i\ncAIuPb9YYXltdaSfvVcg49qIPcjRfZm5Wz8WxTaUAyD5Ag4lpWKVTgWZsU+c\nEKRQHu+UmpX2OsudafT6GL3ak7GE2+ysH1b0HcYVuf1Wdf39un+E0MXDs58C\nTLCZSASN99/KCEpjh8aa4YdXVU3x0rdf50KdKDBUMF3b6HnSfWqOS+OWZRFZ\nC0jvk58j4vmXCVb2puQI8HIuZXBlNeS59GaN3hB3rz7JMgrQC/LXycOU1x+5\nuKEKupRkkVsSRyAEUdHqx6dwkcm+TVGPnXjUMdYREL9VkyY9eB7lBYTEzH9I\nZN9H3JXrjo/dGVmFL6q+L7lCxLFsl1p+UCMxubUE9XV6C/QN4mQmiwIAwn04\nhJH1RFIFTHszVEUnAJMZ6SqRRJes5iSedAMyiUYi+1S86uQenyUqtIJbHsNO\n7+G3Jnfdw9e1+YMvk53PSJcdtt5ayOx7ezc0HLS5HD9g3bXhMbbxTupHOSAv\nVCiEoaKAmjJK7nbStTqrX3xjz85K+lNHZdKkIzPWX5TkEg8KMSGK3LxfXG8B\n+CuC\r\n=orOi\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "templates.js", "types/index.d.ts", "index.js.flow"], "types": "types/index.d.ts", "engines": {"node": ">=4"}, "gitHead": "af8b3657e96a0a6ca5190fb0d0a1345797148320", "scripts": {"test": "xo && tsc --project types && flow --max-warnings=0 && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"ansi-styles": "^3.2.1", "supports-color": "^5.3.0", "escape-string-regexp": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.9.0", "matcha": "^0.7.0", "flow-bin": "^0.68.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_2.4.0_1523939317754_0.3039215958746819", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "chalk", "version": "2.4.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.4.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "unicorn", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"], "ignores": ["test/_flow.js"]}, "dist": {"shasum": "18c49ab16a037b6eb0152cc83e3471338215b66e", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.4.1.tgz", "fileCount": 7, "integrity": "sha512-ObN6h1v2fTJSmUXoS3nMQ92LbDK9be4TV+6G+omQlGJFdcUX5heKi1LZ1YnRMIgwTLEj3E24bT6tYni50rlCfQ==", "signatures": [{"sig": "MEUCIQCTy37ycwX7bqoO0WDu7AVubgfxDHR/7neyxoLzwx8dIwIgJxP4QEC0TbUnC9iBA3w36fy7kAAd1tHPSQZtkZ1yOrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4WCICRA9TVsSAnZWagAAhgwP/2M/ItinhR06BFhLMh91\nK/ru5t71NzSzoEvI2nh4W57Wk9cU1NOYi1cI17nUvICHCL4Vq9mjvU0hajTw\ncAYtM0Lwl+G4Hk4JtuiZITYj93QY3yLSJ8zkj95JznFbH0Zd9KkZrkoGukcG\nFY9at0cfNyhBmwi5sEDAFktcw7wThQ6Wy3iIttQ0N1M6Lf1XILg9Xyq6Id/W\nlz3TbkCt6AZCS1icmDPIiLdVQuD9SfpusIDsHm5/6FJPShwmQjUlM6Kdy7lx\n6M8uhcIknpxjfPTA6/aSBC4qgXnDhuPPi9xF657/81Mswz4Tb71KOf6UqLPi\n3zk1D5PF71ujWs3wmPll9TAVGnWuNzE+X/7GVIB4qCrib3SgvRzMhL0Wo95v\nzxTpNoD23hKYwofUyV3cTFh47YwkVoPtOStRAgdE87rx+v3VjbWSThQJc3V8\nHOsIeTjpQMwAr/d2DnasHKlps/q+gnGKqhBhcf11tAKn9C7PsAQ2l6+E4Erc\nfPKqDRC6TVG7ABdwOtyNonHhrJ2JLgYj8d4mHdtsMTtFsUTOQR/+Rx0V8HJS\n9gBLmPr3yc/yEedYW68wP5tPK2SfvFTzgMBw5v0+tgIxOjUunGxDUV4a1Bpp\npCBLN7iS77FLMiMonfcD2z/SsoB+Hb+7q5eT/gua3BIUNNZEdmgw9queXw+q\n7DFE\r\n=LSlF\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "templates.js", "types/index.d.ts", "index.js.flow"], "types": "types/index.d.ts", "engines": {"node": ">=4"}, "gitHead": "48ba5b0b9beadcabd9fc406ac4d9337d8fa6b36d", "scripts": {"test": "xo && tsc --project types && flow --max-warnings=0 && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"ansi-styles": "^3.2.1", "supports-color": "^5.3.0", "escape-string-regexp": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.9.0", "matcha": "^0.7.0", "flow-bin": "^0.68.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_2.4.1_1524719751701_0.7780175911150953", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "chalk", "version": "2.4.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@2.4.2", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"envs": ["node", "mocha"], "ignores": ["test/_flow.js"]}, "dist": {"shasum": "cd42541677a54333cf541a49108c1432b44c9424", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "fileCount": 7, "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "signatures": [{"sig": "MEUCIHxDV26xkYTdx1T+EBh6zvEaa602qK7hNWXvOTB1yr5UAiEAqSxYcAo+BSotxMY2GjH1e25JFKt2I+5D19gPFGbdghE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcMNEwCRA9TVsSAnZWagAAmpUQAJgCZygaBX9qniyJ7YVF\nOXq9BNycBSnHyRd5YnaoO6HB7ejh/M4CYYGPqdSQ0OXEk1teNm7iPhGhocbW\n0eEcg0gsnVTgkUKx5p3o841VKydwy72FDgO9WJjKm2QC/mwuYHB9kI7zkq3h\nkakWBNGlKxbKNYX+7x04BXx1H8Fn1CSE//133uQnUWzM6NSXrUwpiZTzwtXi\nOybESujfKq6x6DxlYsTTScThCUodQQTslxIrdeS8PZxQL1RqCwnJSMHi81nI\nPR5BNVbAEYOsZuw88mNEtc6sHellN3ZFVlZwFDu4ZDskgoMiXZVv7Qp6AXbN\nCdsz1ej/OBFdwUfjS17igoHY3sO3+7o3IuFFaCXM4lkSE2zu79M2A7H0GL0R\nUcyfM1OC/nRcLgeEytIDBSOAgeN4tstswdyagFQ36jymeKUyz+q50ziBchey\nZnxPMGYDMKTx+me3TGpf3SbjiSstyZm8GLWPhRLbkjIDajFcFnq2HZXUu/LR\npdFJIWqnJihr9dxxiPSxddqZspb/Jo2mD2+ILNxROZB5+nzmlLnV/PsnnbxM\nPRN0iYDQt6NtXce/GOFMasLwtwidfHx8B4ybmObU3btbmg7V7Og++xpVg+h1\nQfACtop8sZyVN3l65vhonCmioqpSLQPeEkMvwGN6/7wi01BRi5VI4DdEtIet\nHcNL\r\n=DerQ\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "types/index.d.ts", "engines": {"node": ">=4"}, "gitHead": "9776a2ae5b5b1712ccf16416b55f47e575a81fb9", "scripts": {"test": "xo && tsc --project types && flow --max-warnings=0 && nyc ava", "bench": "matcha benchmark.js", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"ansi-styles": "^3.2.1", "supports-color": "^5.3.0", "escape-string-regexp": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.9.0", "matcha": "^0.7.0", "flow-bin": "^0.68.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_2.4.2_1546703152138_0.5501232329596948", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.1": {"name": "chalk", "version": "3.0.0-beta.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@3.0.0-beta.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"rules": {"unicorn/prefer-includes": "off", "unicorn/prefer-string-slice": "off"}}, "dist": {"shasum": "328a0eccc051d6e50787fe112cbcf85b5d5e4d88", "tarball": "https://registry.npmjs.org/chalk/-/chalk-3.0.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-f32K9VcIM5XJjpPHkqbrg+xN4vQVzEFNmPTgn1Ai3RBbtWT6ggFkUwZmB3/JTk0tdtjH1sl7fepaB9Vj8uVdUw==", "signatures": [{"sig": "MEYCIQCe0bs1bAdkWtb7IlPzwUC1aavccTqLOMjSpRDMQWZFjwIhAONAABWrbNk+e31MGrLVolFCW6IA03RVeQP3vX11l1fh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjZk5CRA9TVsSAnZWagAAzZ8P/iVhUy5WekDoOrbrump/\nw638F5EWWFLQNYd9SZOdZ0P7eDFJn9vfYwPitgL9jYJZ1T8fMveKbYLsJt6h\nLiNEk9to5mMgfINlj/+vOk83qEZ2yfM46jL+xTfyBLIpnKozlLkUjlm0cPXi\n6yOW26zdmyke1tp/gZafNSaJ/vJbxpjJyciccrvmfe2DWVyzwCcjP7nxpx8/\nUHdjSGvz8ul9PMwA1WieSHE/btPTqaPAPPiVtiOZWyDLhDI50+VXW7iQnvVM\nlMr1OiGjdQ2GKAxJHomvb+bjoWjWxKz3kUnk801quxABfgDHAk+Y3GIvMxLU\nVGQqiNvqYkMcqiF211xkXyFw71tiHyPqQ8pDkp4iYns4LIScr+ppoHPmJv9x\nALKBarLK5IQI1Fjw2sK5t9oOqNQ9HDZLDwGI/DTVOzUCzEMa67euv53IPRl3\nVbR8HHDDTareIWu1zpaHeOfxSoiRpx9TdV0thwWCzSuhANhUws4ebWh+NZG0\npY6Zt0lmSC+Cr3W/Oj41NgJ5MIk6uywZMjYa5wMS22qEzdhWCIDwckeuAsdz\nDi2h+9fcIHOkXxPWKn7UI91B5FCbEadThU9ASjZWSEv+BmtZ605wHRxk36Cm\noFe5WoIbblSjTpWU8ECtpyLm7o1j0aZj7nTgd0DV8ab30CzepNoECebX8AOq\nJ//W\r\n=/gvL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "source", "engines": {"node": ">=8"}, "gitHead": "48905d08052aad4c8ba53bbd9fbcd8a9faf4f6e5", "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "8.16.1", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "nyc": "^14.1.1", "tsd": "^0.7.4", "execa": "^2.0.3", "matcha": "^0.7.0", "coveralls": "^3.0.5", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_3.0.0-beta.1_1569560889303_0.3449501496211118", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.2": {"name": "chalk", "version": "3.0.0-beta.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@3.0.0-beta.2", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"rules": {"unicorn/prefer-includes": "off", "unicorn/prefer-string-slice": "off"}}, "dist": {"shasum": "d6a422d12828314a6f2ecad6863c68b706598a22", "tarball": "https://registry.npmjs.org/chalk/-/chalk-3.0.0-beta.2.tgz", "fileCount": 7, "integrity": "sha512-NKGY083nqioebgPmjRTGY/bRq34dgkXkmHmWgwDXbPs0YEkN7ZTcimhnv1KnHGW80QPta1QZiJDbQv7s+e+uaQ==", "signatures": [{"sig": "MEYCIQCR8QG2c2hRIQDlCKH8KjjOFNGvWAEqtmoIHPcJ0BWY/AIhANBUmWME4/25NdWUmjZrye5lgor0aOJPV3z/HEVysw6z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnFe/CRA9TVsSAnZWagAAN/YP/0i73lzDmsyO/x2VPyvz\nlL0W6F4HdeK3W/Mr7BNf3M06KVjax+BhLTq/KyAhyZlfCAI412HeZCdb34Aq\nXXucOoN5l7o6oJrs+KXN2GlxzHZT3HJjrU653bAuaw/FN0apVM/cLCLA30gq\nZZSM9cxJKy8LDZsszGT88iQtcUI/sp1yvkooKKV4A9XyjQaC05xTVBAbNan5\nYWQfgFKdr2Wo3A5KPND0hf9247GQzg8Lu8fE0EOtRhng+ZxUExp0aApaevl+\nZj+265abk5RMmOUa+VMln91NixSnE98PRguTZcVF+m4HuGyx3fR5jiJx5XU8\nlWvaA01CSHTVh2aVvDHa16/Cbcqzrl24xV7MSVRzUEAY5OudV/+JMvIFWo54\nIFnQqBYFbao9GoHQRqrFLqi5szR/yBYPayFGBcG3L3O6Tngz1N+OtnD+mGNC\nu7ZRt+zr8w0ooPIqKO97PBsgF7u495pvdKx2L5exw77kbltwTEiad2KmOTf2\nQDy1UN+KTUGzeArUa5tdqd3xl6r3V9WqEHsul065u2o4cqPAMXqvR1Xa8lv7\nxGbF1M/lKwGQihn37tHbXPkS/t1ghr+OS0kNaUgN8E1j/7//xx+j2bt3MAi2\nxmZbnCL0EufwXl85FJQ/Y0QFWFl/6LgJbvsmCD8ZqoP+Y8zljZDsKHjvtewU\nnRyz\r\n=5dvj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "source", "engines": {"node": ">=8"}, "gitHead": "4de1841129cf3d0a1db7a5d6638402b7828e1731", "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "nyc": "^14.1.1", "tsd": "^0.7.4", "execa": "^2.0.3", "matcha": "^0.7.0", "coveralls": "^3.0.5", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_3.0.0-beta.2_1570527167016_0.8251191799502275", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "chalk", "version": "3.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@3.0.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"rules": {"unicorn/prefer-includes": "off", "unicorn/prefer-string-slice": "off"}}, "dist": {"shasum": "3f73c2bf526591f574cc492c51e2456349f844e4", "tarball": "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==", "signatures": [{"sig": "MEUCIHFevK5+lXJPZaAa9WD2j7ykLEswtElzlNvuddi6ulb+AiEAuQyuRFvzORZkufOCblOvMRtG0vc69A7rodPmDWZlp18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxmO9CRA9TVsSAnZWagAAQlUP/0PNLoVFZvoOYYt8XPh9\n6mDNBG9olRXfrrR33bcNZdkpKfil6MU7uMkLOME+L9U6h1iqMVUK/CWKfqwS\nUPH/zKprPc2xOgDVKiEgAPm+OWCxcZrGBGUJPiP2TVlo5UshIA6DheD8TSgQ\nGBW2JrOo+IXuHkza1+rPMzfrBpvulp0gthN9JjJYQNZ66SuB1wyFPMLh1b9A\nq/Q5Kg5acnnNAE5xFogzw2gAaf5qee3v3Ozda96VFG8oeCvDXYre6oC0OUzV\ng/ODyHikvvtZx6TU5hosOSRTawXjq1lGgLL2xzWt7w8oRziTBb2NKf/irsMd\nuWel64/sBbQF9C1C1/z/MkYRS2OVQWOo/qZ/BdIvfCTniaq6sekNn6rIXJj8\nPRXmBsJXdxD6mhPKxa2YEUWHFigyI+DyNRi3EkUpCqp2ZmfkD0srhakUf97z\nB4kr+0MvNpBhWt8y7Yjz1jQC9EM73yQ1POjspYj+Fh6mr7kgkfo/AFGh4AHI\nSXlXgE0b8WUn6hl3/icMZHk2xwYyIVImklNkKfI4IhxodkjL11ji+Nn5yUkI\nf9RqxQajpLAwLyWeAT2RCSTLvxfjwKnU+bWyFWHqyGQb4aS4TLSz0wmR8raS\nroGj3AXz4oUTazsBy+kGNwzOZs3tOV/Uv30MFpnHpFpOQhW1Mvp/RiQkd4O3\nxEsU\r\n=jRi9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "source", "engines": {"node": ">=8"}, "gitHead": "20002d8bd1dfd6f68bfa8bdacba520ff6027a450", "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "6.13.0", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "nyc": "^14.1.1", "tsd": "^0.7.4", "execa": "^3.2.0", "matcha": "^0.7.0", "coveralls": "^3.0.7", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_3.0.0_1573282748902_0.03099002657701666", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "chalk", "version": "4.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@4.0.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"rules": {"no-redeclare": "off", "unicorn/better-regex": "off", "unicorn/string-content": "off", "unicorn/prefer-includes": "off", "unicorn/prefer-string-slice": "off", "@typescript-eslint/member-ordering": "off"}}, "dist": {"shasum": "6e98081ed2d17faab615eb52ac66ec1fe6209e72", "tarball": "https://registry.npmjs.org/chalk/-/chalk-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-N9oWFcegS0sFr9oh1oz2d7Npos6vNoWW9HvtCg5N1KRFpUhaAhvTv5Y58g880fZaEYSNm3qDz8SU1UrGvp+n7A==", "signatures": [{"sig": "MEUCIQD7qR9gpQpWRXV3jyQzkkap03iawVdpCjW7KHMqHUCSMAIgF1VFxJCJMi2OPph8OaYzcwNwHHTXEDg3BzcAqM0J3c0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehaBSCRA9TVsSAnZWagAAOr4QAJcb6PYIe6jxjYoonKMe\nsF+1AbJDL8JbXYEJME2KzFD1SXQEUIlUBM8C+hqfu0b2TxXs96rMzI+ueLx4\nvWIdw5d6By+eLQr2pfKgr4YrrPa0vniUd26/7pdbPTJbXpF/u0/BNI0TD30D\nSHBMuFbvVVUV0QfrjajTJXnA6LsD7LOcp9RlL8zyZRDYrstGJShO0yYUt6vp\n7+cTVX3oYkXFpfd2xBqRyeljFO5S4D9dhkGvq6vdVt9VUMXhmUzEJwtzpDpj\nBqfDfRBudePuthM+nNlNd9DIfCY+HopadNAKsRKv0fobzACNE2xedBUHWfvB\nAxVOBupA23Xok0z7dgXBKllLptQlI5mQ3e1cqQO9hUB22iRSUrSM4eEz6lrB\nzCbyzr+7WoxSPRWaoIu52/zPanuGAwc2Omk7+BHwEX0j7wiyHpxPWSkbZiw6\ngeiro1ZDzbWYCd2lOe2ZGRRQdIHCADozHz6IHorbHp9k4R5cHtmryiwmE59B\nZ4JYtRjn6uiWbr+RPVdSS9RySu95eApfTNO148IIek+E1lNLJ3VTobUmDWyY\nDW6HDIA1Iplbm54dSGgC5fwDOhZAnvrMOfOS6MMygblIuHP4Cm16woIUwWFi\nE4ErSEGMZZg2N0fkGP3oEgOjbnzXiSrYBPXZncfI+nnTtHf82kJ4D2Oc8nPp\nVbza\r\n=EnFl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "source", "engines": {"node": ">=10"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "31fa94208034cb7581a81b06045ff2cf51057b40", "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.28.2", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.7.4", "execa": "^4.0.0", "matcha": "^0.7.0", "coveralls": "^3.0.7", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_4.0.0_1585815633587_0.14399738942582263", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "chalk", "version": "4.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@4.1.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"rules": {"no-redeclare": "off", "unicorn/better-regex": "off", "unicorn/string-content": "off", "unicorn/prefer-includes": "off", "unicorn/prefer-string-slice": "off", "@typescript-eslint/member-ordering": "off"}}, "dist": {"shasum": "4e14870a618d9e2edd97dd8345fd9d9dc315646a", "tarball": "https://registry.npmjs.org/chalk/-/chalk-4.1.0.tgz", "fileCount": 7, "integrity": "sha512-qwx12AxXe2Q5xQ43Ac//I6v5aXTipYrSESdOgzrN+9XjgEpyjpKuvSGaN4qE93f7TQTlerQQ8S+EQ0EyDoVL1A==", "signatures": [{"sig": "MEUCIQDngUfyzfUivBEHqCcMvyhegi4ut++J5IACRwl3gHTy2wIgbyjwcUbiRPFMf5J95/s1JR7DUuPwMqrOHpFuzTrnmZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3z2uCRA9TVsSAnZWagAA0WAQAI74Vzh0XPfSSmh2CcWS\n+aaiM3I/TWlkYavizTfItEhGfuzf+Fi0RAzdW9SXjxYq9kZe1W+9zG+QJQ3S\nR5icdbgu3lXwAfesJUcX5EplLoSKEJkpTMLtOcff0lu4ug0+dYm5XHbCfign\n8e7vVdU50GPMM8mg20Hyl0SIuawkUngnlIeCiY36NjdysJtMa8X2QeMHRNat\nSg/aK3+SepkJyxAQSXqiI9QemV4PMI42L7pCb19bu79eBUYwaoKml4qKneX2\nH18ViBB6wKhxfNwX1K7M2PHxqd5e2t44hqliAgzXzz/bNIaU3qomm2OJYGKS\nZujI7mx3DEKzsYd7glloIBQKWWet22mc0eQSjH7q25w6tjPZpk68Ggs4xxIm\nRgJaAjhFURC916d4o/EhQYXTv+0WnS+TRwcJOBDpsrfZ1IuqErC2ZT+QnMya\nzEMemmRWMUFhdQcQYUQT8jDHuSpqxj9d6DUBBGJrM29W1CH3j0xt6DN2EwOx\nUj3HGHKTc652yrvP4JKSj8XAPUn7mHW1kEOT9ypk+sBISI3u5EYMdPr4xRKp\ngx2Ci/q9lI+IQWT1Rf2EBzluwjA3/6fz0IoVAkelxKRiQsVSsdGVXuGB/QdU\nBChW4WzwPn9FbBiuLb+9SYY9lKjHmNbn0nUklvZVrpiUpWAEePZVp97ohqRy\nrq+U\r\n=5TmM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "source", "engines": {"node": ">=10"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "4c3df8847256f9f2471f0af74100b21afc12949f", "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.28.2", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.7.4", "execa": "^4.0.0", "matcha": "^0.7.0", "coveralls": "^3.0.7", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_4.1.0_1591688622018_0.2681914768782716", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "chalk", "version": "4.1.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@4.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"rules": {"no-redeclare": "off", "unicorn/better-regex": "off", "unicorn/string-content": "off", "unicorn/prefer-includes": "off", "unicorn/prefer-string-slice": "off", "@typescript-eslint/member-ordering": "off"}}, "dist": {"shasum": "c80b3fab28bf6371e6863325eee67e618b77e6ad", "tarball": "https://registry.npmjs.org/chalk/-/chalk-4.1.1.tgz", "fileCount": 7, "integrity": "sha512-diHzdDKxcU+bAsUboHLPEDQiw0qEe0qd7SYUn3HgcFlWgbDcfLGswOHYeGrHKzG9z6UYf01d9VFMfZxPM1xZSg==", "signatures": [{"sig": "MEQCIFRLSrIoAV5Vuz5b8oIdXGeN7vlNJCSxq1vXEDPOGrdxAiAGLyD2ukbgFSt3og6x5DALaTSFTGCTd2ZCTZIqPX/CBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf+g6CRA9TVsSAnZWagAAYJ4P/3Tow5x9NagQZ4wlfmp+\nAx2ZtnEj/nASDGzd0E66OdyF69kS658zaMCBK96P/qXRYbCDJm77wq4W3Iui\nnaC6X1ZLcsbY9ME+EKHVCVXHiCjXC7EsPskkHrSUUYvwEiSK6wOH2ljfogNr\nLXQFbMT0x5cRpZZcherkrDWgJCFnD8L6+a9aDkfS/ZQZSBN0MHGZmi1AYl3B\nh/iKYq+ZhI1qUJaDhf005Oj0Vtegjozr3C+senyN4QWpUijoTF7MgJwiF/8w\nvb8e8Lzf0KlfdSvxtXeZ38m3J+y3+cphcBmkoagxsMtrtjzbFCUFIjHS6b1r\nldlAmH/xe5oemntzjX/7mXzhK6yMA515vAhKswcJ267oJ5tsnfa90bo2wh11\nBdAAr3RjWgNUb/OV1aIlFFvTCwc7O5yYKfkY7KXnL/NUGq0gpIJ1lEdTMgPL\nNaQXm41q0UEmVV9bi/BQKIUpPblVMYSJShcbjxhiZ9Yzc2vkVbzY73UXoEwE\nn+JIKCZPbC1RJQ/E8IjEtROoUFJ7MmhPm/dqMuw+/9JITGh7c4HYh1u0xqzI\nHp4l+Fc39tIje76zkT/QzvW9R3M83xOWxTTHARTJKtC6wqDytuYO8liIOEIi\nbcZd+j6YwHU8C071Dvncg2wELx7VmQefvxyWcjnuOCLyuHFOXJAsy79z3Cc4\n1lSw\r\n=t7Q/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "source", "engines": {"node": ">=10"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "89e9e3a5b0601f4eda4c3a92acd887ec836d0175", "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "7.5.4", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.28.2", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.7.4", "execa": "^4.0.0", "matcha": "^0.7.0", "coveralls": "^3.0.7", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_4.1.1_1618995257952_0.8528874341897312", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "chalk", "version": "4.1.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "str", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@4.1.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "xo": {"rules": {"no-redeclare": "off", "unicorn/better-regex": "off", "unicorn/string-content": "off", "unicorn/prefer-includes": "off", "unicorn/prefer-string-slice": "off", "@typescript-eslint/member-ordering": "off"}}, "dist": {"shasum": "aac4e2b7734a740867aeb16bf02aad556a1e7a01", "tarball": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "fileCount": 7, "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "signatures": [{"sig": "MEYCIQD4dG0uJ9LWDhLIbBJazgFGOYD+6u8gUvOP/yfEluvstAIhAK7GZOqN8KY0Alry2lZ2MBIvWjuEGISyqHydp9oFrw22", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhA+psCRA9TVsSAnZWagAAaj8QAIsfB8+2kMsY5OUrDEqr\nqbznQ57bFD3ArrUSvhg+03aVEQV8fwgQHZtDC4KUSFuZGw/1l+3A+GFxvK68\nMM9SOD7jMNy1BUfzrfi0M2TR+V4pNjqJl+/ePQlgQ1SYzCZ+JpMUXdwkiV6r\nYzXuyObH6lJHPvdch+ynLSIYsQHHCDEe1qKEsdZXJzEAOc51a17ymlt+HdGg\nYZKENPA80H6h1bxOwf74i6uUNOYRLucAjDDuZX+cBxzILh9HOzl1MLRSgEJR\nD8Yl212H5+02bZb2Mhlrl+iFcfaPwz/CWWFxTW3dFoaAEVpiK+g06vlVKR+4\nIagsh89hAyWLl4mc661vycKi+WdhVQ275bzjtnzmti4E2CKVevksJS6aJKKo\nrtgAVlI0XEWDfduEEH3Bh/MfazmRl2BlKtKQoWmQjnj8ydydEchk2nN0N8cd\nnshOuqhOVtRoCROOUBIZHP84cy7teK0HDNKypv1Lu/43F3pZl12fJNPuo+zA\nGSjCke6YI6GFn0NkZ403iMQnOb8bF/nyQwVNUf/0DXEP3wNJbGyXyrY3HLeY\nleZa/LuXwt/4dXs5+g8RlaMjcqIukABwEwpsqX8G/ASaW9Do6VuwRqD7fva+\nXQCIebFDLUTbtAbI4L0qIqIDPCxvXR67m/j0fzHvzjGDwQzOlL2Og10rI0ZT\nmil5\r\n=FyBn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "source", "engines": {"node": ">=10"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "95d74cbe8d3df3674dec1445a4608d3288d8b73c", "scripts": {"test": "xo && nyc ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "7.5.4", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.28.2", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.7.4", "execa": "^4.0.0", "matcha": "^0.7.0", "coveralls": "^3.0.7", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_4.1.2_1627646572658_0.5829286157088185", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "chalk", "version": "5.0.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "c8": {"exclude": ["source/vendor"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/prefer-string-slice": "off"}}, "dist": {"shasum": "bd96c6bb8e02b96e08c0c3ee2a9d90e050c7b832", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.0.0.tgz", "fileCount": 12, "integrity": "sha512-/duVOqst+luxCQRKEo4bNxinsOQtMP80ZYm7mMqzuh5PociNL0PvmHFvREJ9ueYL2TxlHjBcmLCdmocx9Vg+IQ==", "signatures": [{"sig": "MEMCHwQWn9IdUcKlXMudz/Dn2ckiNTICSI/byCzWG3AhrfECIAiIfvI5iXGb5Ps8mCJMHTu07kPNDH3RrsZMoyxnI6ge", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoK+PCRA9TVsSAnZWagAAX+wP/jaVhQWZpfCaDjmJsHuN\n5JQ1XlOLFdbzRfyvuZ2XAn96myIf/1UAa96Pi9gKXjVV1cnRdFW9TPgBMIzt\n42mqC2T8/qJe0a2VFIvlRlSFBMyThbyr3WnDCHvrgjfy8pp6EN10uHwSqnWn\nYL5NemSFQSc2EZ1LxulrJC8bzj3CzcB6yJXDMKUE3BfSFL0r5YxG2FLjktDw\nlL9hm/JxylEPgUIrzKNabO0W77Lcvg2nwJzxRZpDCkLC6NSLx3UV8nDyzoXT\nlvBiR1pxzjWrWbOPvTGO1+5q1HdVTOGqLWA/XjSBBpHZ7SQDgkh9f8pwP823\nep7Q3/2j5BvgleeVzOPQqwGXOq7qbAcBFibHTLd2Zi0xXBFMHtIVaw8odDVV\nAsWqTV4qAgRrefe8Q7wIzUrO42Z5ndRfqHSI6t9bJ4AeH/ZL3vrNNmTFIoLx\nJdWEu1WXSnkrFdCWkxHIjU0wyVJAIAOtK/vQw7LPmOQhEeHLLos/t7fBP9tC\nMi5UoMNZYYE41d5MENeySPAHXZ+ZVIROdAqN7mCCwfAg8lygiNfbNLiPeo9o\nld5H9hYFw4eW31fwvlX8Q6MclVR5k9755fpZUHcyDqgN6hgLJU4j0PHSvnhI\nFrB0wwi7SNJ4DiqIlKtpeNaRSwy1cnaiML3wFeeYKf0zZEAp26bf4CNbZ9XX\ndcEG\r\n=qfhV\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "exports": "./source/index.js", "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "4d5c4795ad24c326ae16bfe0c39c826c732716a9", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "xo": "^0.47.0", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_5.0.0_1637920655740_0.2838492953241636", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "chalk", "version": "5.0.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@5.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "c8": {"exclude": ["source/vendor"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/prefer-string-slice": "off"}}, "dist": {"shasum": "ca57d71e82bb534a296df63bbacc4a1c22b2a4b6", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.0.1.tgz", "fileCount": 12, "integrity": "sha512-Fo07WOYGqMfCWHOzSXOt2CxDbC6skS/jO9ynEcmpANMoPrD+W1r1K6Vx7iNm+AQmETU1Xr2t+n8nzkV9t6xh3w==", "signatures": [{"sig": "MEUCIF8zpu4flQt9Ck8M+VS0lz3b8mdY12JcKsCNaVK+2pY1AiEA4+L33unqlFikjPh1wCQQcPQ5jOpz9l5PGjjp1ERb/GA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJ6QUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoR4hAAkZ1Pzo7shU7iLgAUqhpUI79vsAQoqkKYBv9iC+Z4ogWUlcd0\r\nRmauMjugcrKH6fsokqp9jDuJLiZSAbj7nwbmWDoBR9XRrxedjsB2H0eANVlC\r\nTAecFxGdN4gxEBzE6yVOfJelpNuw2qWstXKwOZmLwZkTlqE9LETYWO7xmzfs\r\nUW42alsTOP78ZYRnUWwkeZr3Z8yt7thGUwIi9q+QFsDIt0VwE86uTLeLvv+6\r\npNI04MyKzNkKRG+PF8uW+O+j5PYIr9BPYY++g+f+rayFdwSYcoQjutF0BHdj\r\nG+7DGTrk+dhzCJ5xLSZY0rljcgyY9/KMSj+3rO7fUJalPIpqbwLClmMtfJQU\r\n2MmdM+dJfsXAfoZA5rMxP3GoOC8LC6RaScliVivockrCSJgd9Tp9KzRt+66I\r\nqNiuztSypB5SRB7TC41S8jvIHDw5PNNnNP5C7E/uBjU35yc6aX/2pyxUeXYm\r\nAOPLmVWqBcroBiE4zi4OmG1fB2Izw9DJsK31i2U98lkMLfkVaUa4tIBgVwbU\r\nkB62F3BA86yYuWucYeZL3hp9H1TG5+WBhs9+lHrb0+mH3b6ab2lZu/uCWvH6\r\nSedXquHHQxyM9JcuxDrDtmELqR87B+h0iAKzBf7KYtmyrqrxpHE0FiteQsV9\r\nUJbfD3SPAaX/IBAryNeP4Q4e/oSQDy3YUuI=\r\n=ZFqY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source/index.js", "type": "module", "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "exports": "./source/index.js", "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "bccde97f8a1bb125d4fe99e8fd355182101ff4f2", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "xo": "^0.47.0", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_5.0.1_1646765076079_0.31760153530326884", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "chalk", "version": "5.1.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@5.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "c8": {"exclude": ["source/vendor"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-definitions": "off"}}, "dist": {"shasum": "c4b4a62bfb6df0eeeb5dbc52e6a9ecaff14b9976", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.1.0.tgz", "fileCount": 12, "integrity": "sha512-56zD4khRTBoIyzUYAFgDDaPhUMN/fC/rySe6aZGqbj/VWiU2eI3l6ZLOtYGFZAV5v02mwPjtpzlrOveJiz5eZQ==", "signatures": [{"sig": "MEYCIQCGiB2UgkPRG76rTZOqtyvgHW3GZdkr1l1grc4mtEcDuQIhAJuhTuvzptwKcBUWBFc4+2YHWu3vqzlDzAAfqkac2pZ1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPZi8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY0xAAiHomYe49a9lY0i07r0BEJZGVmegNRGYPJAekN+UaRE/UOx67\r\n6EzdIa7v74VYkhVD4rHBg/O7Uqe+O0U517do414JWVWVXbkB/t5RGjaGtH1s\r\n8OQ85DCyTmIhD15M107/v3qSKWLMhE8lca1KnFYKtCrGFfmEsib8hAFsqS2F\r\nWnrEM152LuQeXnUoUwCaI5yUefG0uLe/yuKhtyWcKToxjmK6HH4ORlG+1o+K\r\nsyXX6Lfv3m40LAIWayP8KE49RjZ1kM0sFK+hasPg8hoxMQgSyM2URnjcUPVN\r\n9wi8ReRUHuaWyuPEPWfRRnYpK8IQnTo24eFaSz4Jz5Z/yVLDYhmOuvzeqXbm\r\nkv74Tnn4HlD7iju3erQQjolvl5D+mKh9ObGjl92nekeyg8G/A/w2EfMpd8FR\r\nJfFBr9Ty+vtNEYbl5iKgzjrl5uLEHKmOGfXXogLsahWB0EXuX2AV8dB5OMlX\r\ngmr68f1jvWYZmxiaKHkXVsLAW/TXl1zWOotSdeYDVDpZQ3SOKtZCez+QKnBQ\r\n1GFqIj/nBmkSGtP++w9Hk9PGAXM7cW/7pLgwT8qGLEeT2SrwnEEAUkImPNJe\r\nnLHSyaS7l7nA267jR5XQQ2SP0wR1alYrE4MKL0nw5wHau8bxLMb1ZFjxE58V\r\n/LYxUMGQDJqfgNtyYmtq8CycQRUv3kbxsHg=\r\n=0Hlp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source/index.js", "type": "module", "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "exports": "./source/index.js", "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "92c55db46f2396c18764e55e6a52dcb49884a42b", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "xo": "^0.52.4", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_5.1.0_1664981180277_0.8837577780411197", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "chalk", "version": "5.1.1", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@5.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "c8": {"exclude": ["source/vendor"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-definitions": "off"}}, "dist": {"shasum": "546fb2b8fc5b7dad0991ef81b2a98b265fa71e02", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.1.1.tgz", "fileCount": 12, "integrity": "sha512-OItMegkSDU3P7OJRWBbNRsQsL8SzgwlIGXSZRVfHCLBYrDgzYDuozwDMwvEDpiZdjr50tdOTbTzuubirtEozsg==", "signatures": [{"sig": "MEYCIQCe9oT/CKTmjgzKIXm3S+DVkuwywIySphHQrSYz9xWtGAIhAJNOTCunCt5qIDhrg5pWHvdPHorZeO13auuylbrUjTq/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRoroACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmotkg//SSk78lf5lQWol2k17UO4dnj+LpYx4gNYLk8uptAPbsRLyhx2\r\nhkmiejrW9Komrzx1FO58JwocXNS5Q2I3mUqG3WisGokfvTWI6kDVlx9l4M0a\r\n0lPo/ENgk9lPN1WUMVx60zCneP/NDxG/sJerE8cM/UnWdc/YHKDiHQEU/hzX\r\nkGAn5juElGo0ae4PAL1M15UsMg3Mk4lyhTRsoztWxYfAI1pNIAxI6ovTaVWg\r\nG/dT6b59DsG6U8dM94cwGxFGtOsnzaBfgupH5Y1T1GvwVQCgeFiLlWWXLAqc\r\nsqP67fUDJtMY1qAJCKhFm80g4eCr6NOvfZ8yovn87Hw8RRmglyOrA46MpQ70\r\nYdJbJBruOToWfR9C9VpcO7BzcPJP79ynfyX70ULe1uP6MZ6R4nJr5FfKanm6\r\ndde4ZTNvLr7ZDWhbW9n4Fr7cVMsT9Jr3f1R/YJLqBNpp0cQBP7UeeJfi+yRj\r\nAsoD1oaDgdDIjcjRBUVAKirU4EwNZ/tk7ejQg710KBK2ccgsiav5rm69TeDd\r\nlCLJdiE856wWK6ZRqY/aXdydoglVJb/L8rG0UK20gaEw4U/BGkLoyvWke/PG\r\nz8VUy2ih3MlSRNVBgXW5U15jT8ku5pnf0jT2yiNT9ke+Vk79Ke7TCPqUj+AU\r\n8htjOcAsRtRTJnQa15nytGTSRtQmQzGqVzg=\r\n=Tswy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source/index.js", "type": "module", "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "exports": "./source/index.js", "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "1b4cd21fb15ca441ab8ff1fc4ce9fcd1365e4b7d", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "16.15.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "xo": "^0.52.4", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_5.1.1_1665567464573_0.20876003648435004", "host": "s3://npm-registry-packages"}}, "5.1.2": {"name": "chalk", "version": "5.1.2", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@5.1.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "c8": {"exclude": ["source/vendor"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-definitions": "off"}}, "dist": {"shasum": "d957f370038b75ac572471e83be4c5ca9f8e8c45", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.1.2.tgz", "fileCount": 12, "integrity": "sha512-E5CkT4jWURs1Vy5qGJye+XwCkNj7Od3Af7CP6SujMetSMkLs8Do2RWJK5yx1wamHV/op8Rz+9rltjaTQWDnEFQ==", "signatures": [{"sig": "MEUCIHW+BiaXhxpezvF8SWNxOCnLYZEqgpN7iUr+4sfRrJ1NAiEA45MmChdnALfbsbbqaI0RqSrU6koyuZjSI/FwkJrgCf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRuv3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXDg//SHD6IFmJB0Dq/i5hFI0gxlnD9aTFqVEQAatIyxOJvXvEyS7M\r\n+JD3/dOU44ePZ4zH7aYT0YrUnY1oHpijO1elbyL4mhgLxauHuNpYmvgH7dyE\r\nuSKSbvCmvtB/lzeTZR5VDDvq/CYRZ4CTL3N9oZmRMV4b3yw6JGZVa7iTUYvS\r\nz5WdJ89aGNmtH8wvmR1HOfDQOeBEV438VpGhBp2lKy6WYYJVEJgddR/FutMd\r\nwJ4Ptm810A5bNMlhfCQRmjYwEgEz9EipavaM+sS6RWKxLGps+tvTRz1Ukgt5\r\n4yRaEjDpYywtB7ujl/dA2mXKjnJo92uLmirmQkSJHHjSJ8r+M8lx9y4CYdo7\r\nlpzHovkYnDacd3F9rKMZoFZZR7a/VWGCBQQM1P7xabPqCJV88Pyqv1jsjSrE\r\ne/mNaEqSUFomZG//23pavE3UAe2aCfX9V3aZay2rYDdeAmLebCAhxaNqUscL\r\nok0aOwSJG2c0c2RxceQl/7o27CY/5F5iJqg6Dcvk5MbuF0z5GIoMoIRYx4Rs\r\nouwEKeu+7ei4Du7qs55GsjQuSUdR52L5s/UtT1u/RUEvPgoonP33LSr7UxZD\r\nOuN8BdmCdD9MMfiZjf/RUJFrApOa4nCyWB4p9wwKTQc2DZ4TErJJIWwQxyz9\r\nKAkARhOYA8iaWDtpBRlVbeEI/xnbezSuqrA=\r\n=ibOO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source/index.js", "type": "module", "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "exports": "./source/index.js", "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "158bf4429ee5c40fd23d45b7d43e5cbbbdf6795e", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "xo": "^0.52.4", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_5.1.2_1665592311613_0.6166299016608878", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "chalk", "version": "5.2.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@5.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "c8": {"exclude": ["source/vendor"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-definitions": "off"}}, "dist": {"shasum": "249623b7d66869c673699fb66d65723e54dfcfb3", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.2.0.tgz", "fileCount": 12, "integrity": "sha512-ree3Gqw/nazQAPuJJEy+avdl7QfZMcUvmHIKgEZkGL+xOBzRvup5Hxo6LHuMceSxOabuJLJm5Yp/92R9eMmMvA==", "signatures": [{"sig": "MEQCIGpkIV4DWbErlKu9VI8Xrd6zVKzVBcJP1P4uiwhcauWeAiBCaqZ8GWGM91SUt7da69Vy7lSbP1Qsfns18NVexta3Jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkjEDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpV7g//SkAHVpwaXi9OSemLPPViDVvDgWITSR6UOiBP2kt/IAYeuRw+\r\nBJPNm3iIrJPK55H5xFW8hOTasUdt6pltGY6lBLkNYytOGRKx6Hj4+6gMUbvI\r\nHhrRUb3IJyGAkxQeQ1yqSpjGpubi5T0Xe7q5WFgEm+0cibDPqOBBAl2/Y2mL\r\npHW5cL2zyzTo76TW6m33PjA51ima7eSlXx76ZBYE74NZ69mS30mkxGx7/KW4\r\nN83QnYpIvZi7cfFqcMQQhnD5LK9vm1bE9wbdVvwj3r6tFt+W97egZ3qiBU4g\r\nownwJUfj7tSP0Vi0XQeLhImIh9efdxqvRK5HJjaEWDQhnyAZIw9S22aesGD/\r\nwjL6ehZRawG7TAHvMh4KC/8Yv4Wubx5dJgvGuwOjgv1+45bt+r7xfKPvnBBb\r\nFt+s8YfMvGMv685020Ht9TGmH3DwypB5RltnZoOnmvBpWRfP93uTkLZiweek\r\nbRfzqfjK1kj8V4qqphCfJYU71d9s1q7A5fnRob+MTAJ1opRyK2v30du2D/P0\r\n3IBJ6fynWJUAMHyWg7BZRDfsNWN/pYkW1UP1fMy8V93dXBa2WGuMuVwpRBxK\r\nIzrXhc52uxi1jIZDaZ2RbhcdIxtWLy+5ikYx34nqdMFquh0WWkhXsus1t3A4\r\ndI5gxKg37xdA8NYh9kwQGmZ6HsjrmqMbKUs=\r\n=tvom\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source/index.js", "type": "module", "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "exports": "./source/index.js", "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "a370f468a43999e4397094ff5c3d17aadcc4860e", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Terminal string styling done right", "directories": {}, "_nodeVersion": "14.21.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "xo": "^0.53.0", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_5.2.0_1670525187006_0.6054411598751666", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "chalk", "version": "5.3.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@5.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "c8": {"exclude": ["source/vendor"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/expiring-todo-comments": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-definitions": "off"}}, "dist": {"shasum": "67c20a7ebef70e7f3970a01f90fa210cb6860385", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz", "fileCount": 12, "integrity": "sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==", "signatures": [{"sig": "MEQCIC7jtsJDO883o5OSOE4ygbH48k0Q4SciRc0MhlEjWvJRAiAi1pflMpbvv+4KrVGVN3ZhppLjF45dpLmLJ3dk1VtZug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43736}, "main": "./source/index.js", "type": "module", "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "exports": "./source/index.js", "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "72c742d4716b1f94bb24bbda86d96fbb247ca646", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Terminal string styling done right", "directories": {}, "sideEffects": false, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "xo": "^0.53.0", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_5.3.0_1688036291769_0.9519443644747436", "host": "s3://npm-registry-packages"}}, "5.4.0": {"name": "chalk", "version": "5.4.0", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "_id": "chalk@5.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/chalk#readme", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "c8": {"exclude": ["source/vendor"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "unicorn/expiring-todo-comments": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-definitions": "off"}}, "dist": {"shasum": "846fdb5d5d939d6fa3d565cd5545697b6f8b6923", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.4.0.tgz", "fileCount": 12, "integrity": "sha512-ZkD35Mx92acjB2yNJgziGqT9oKHEOxjTBTDRpOsRWtdecL/0jM3z5kM/CTzHWvHIen1GvkM85p6TuFfDGfc8/Q==", "signatures": [{"sig": "MEQCIF9O9rt/0qVcL415fsimTeAtarR3ClkFa2x24fPx4VzsAiAyfeEfT32AN36Dl+1Wt4T8dyAGqZc0ktD3+hgIiSNnGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44129}, "main": "./source/index.js", "type": "module", "types": "./source/index.d.ts", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "exports": "./source/index.js", "funding": "https://github.com/chalk/chalk?sponsor=1", "gitHead": "83acfcf8cb17437b63beceb027180399da74f0a7", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/chalk.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Terminal string styling done right", "directories": {}, "sideEffects": false, "_nodeVersion": "23.3.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "xo": "^0.57.0", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/chalk_5.4.0_1734544841544_0.4959266765700201", "host": "s3://npm-registry-packages-npm-production"}}, "5.4.1": {"name": "chalk", "version": "5.4.1", "description": "Terminal string styling done right", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "main": "./source/index.js", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "sideEffects": false, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.57.0", "yoctodelay": "^2.0.0"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-definitions": "off", "unicorn/expiring-todo-comments": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}, "_id": "chalk@5.4.1", "gitHead": "5dbc1e2633f3874f43c144fa4919934bc934c495", "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "homepage": "https://github.com/chalk/chalk#readme", "_nodeVersion": "23.3.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==", "shasum": "1b48bf0963ec158dce2aacf69c093ae2dd2092d8", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.4.1.tgz", "fileCount": 12, "unpackedSize": 44242, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZ5wDikSytgziyeCEmOhw4VJznYEkpJZXRQgJr4o+SUQIhAK3MBAa2qCkGdIpM+MMML0rPG+R12h6M+pqFXKxYO9Yq"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/chalk_5.4.1_1734800692821_0.33369100320867306"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-08-03T00:21:56.318Z", "modified": "2024-12-21T17:04:53.207Z", "0.1.0": "2013-08-03T00:21:59.499Z", "0.1.1": "2013-08-03T01:38:53.881Z", "0.2.0": "2013-08-03T16:48:31.308Z", "0.2.1": "2013-08-29T14:15:49.234Z", "0.3.0": "2013-10-19T15:58:20.344Z", "0.4.0": "2013-12-13T19:30:32.742Z", "0.5.0": "2014-07-04T21:23:48.003Z", "0.5.1": "2014-07-09T20:24:36.498Z", "1.0.0": "2015-02-23T07:41:35.421Z", "1.1.0": "2015-07-01T13:32:13.906Z", "1.1.1": "2015-08-19T20:10:58.495Z", "1.1.2": "2016-03-28T23:32:04.003Z", "1.1.3": "2016-03-29T00:16:44.512Z", "2.0.0": "2017-06-29T23:49:22.932Z", "2.0.1": "2017-06-30T03:26:46.721Z", "2.1.0": "2017-08-07T03:56:43.217Z", "2.2.0": "2017-10-18T03:15:41.898Z", "2.2.2": "2017-10-24T03:20:46.238Z", "2.3.0": "2017-10-24T04:12:55.953Z", "2.3.1": "2018-02-11T13:18:28.596Z", "2.3.2": "2018-03-02T17:43:52.786Z", "2.4.0": "2018-04-17T04:28:37.857Z", "2.4.1": "2018-04-26T05:15:51.877Z", "2.4.2": "2019-01-05T15:45:52.349Z", "3.0.0-beta.1": "2019-09-27T05:08:09.440Z", "3.0.0-beta.2": "2019-10-08T09:32:47.141Z", "3.0.0": "2019-11-09T06:59:09.065Z", "4.0.0": "2020-04-02T08:20:33.785Z", "4.1.0": "2020-06-09T07:43:42.525Z", "4.1.1": "2021-04-21T08:54:18.124Z", "4.1.2": "2021-07-30T12:02:52.839Z", "5.0.0": "2021-11-26T09:57:35.917Z", "5.0.1": "2022-03-08T18:44:36.269Z", "5.1.0": "2022-10-05T14:46:20.465Z", "5.1.1": "2022-10-12T09:37:44.826Z", "5.1.2": "2022-10-12T16:31:51.839Z", "5.2.0": "2022-12-08T18:46:27.169Z", "5.3.0": "2023-06-29T10:58:11.887Z", "5.4.0": "2024-12-18T18:00:41.792Z", "5.4.1": "2024-12-21T17:04:53.006Z"}, "bugs": {"url": "https://github.com/chalk/chalk/issues"}, "license": "MIT", "homepage": "https://github.com/chalk/chalk#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "repository": {"type": "git", "url": "git+https://github.com/chalk/chalk.git"}, "description": "Terminal string styling done right", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "readme": "<h1 align=\"center\">\n\t<br>\n\t<br>\n\t<img width=\"320\" src=\"media/logo.svg\" alt=\"Chalk\">\n\t<br>\n\t<br>\n\t<br>\n</h1>\n\n> Terminal string styling done right\n\n[![Coverage Status](https://codecov.io/gh/chalk/chalk/branch/main/graph/badge.svg)](https://codecov.io/gh/chalk/chalk)\n[![npm dependents](https://badgen.net/npm/dependents/chalk)](https://www.npmjs.com/package/chalk?activeTab=dependents)\n[![Downloads](https://badgen.net/npm/dt/chalk)](https://www.npmjs.com/package/chalk)\n\n![](media/screenshot.png)\n\n## Info\n\n- [Why not switch to a smaller coloring package?](https://github.com/chalk/chalk?tab=readme-ov-file#why-not-switch-to-a-smaller-coloring-package)\n- See [yoctocolors](https://github.com/sindresorhus/yoctocolors) for a smaller alternative\n\n## Highlights\n\n- Expressive API\n- Highly performant\n- No dependencies\n- Ability to nest styles\n- [256/Truecolor color support](#256-and-truecolor-color-support)\n- Auto-detects color support\n- Doesn't extend `String.prototype`\n- Clean and focused\n- Actively maintained\n- [Used by ~115,000 packages](https://www.npmjs.com/browse/depended/chalk) as of July 4, 2024\n\n## Install\n\n```sh\nnpm install chalk\n```\n\n**IMPORTANT:** Chalk 5 is ESM. If you want to use Chalk with TypeScript or a build tool, you will probably want to use Chalk 4 for now. [Read more.](https://github.com/chalk/chalk/releases/tag/v5.0.0)\n\n## Usage\n\n```js\nimport chalk from 'chalk';\n\nconsole.log(chalk.blue('Hello world!'));\n```\n\nChalk comes with an easy to use composable API where you just chain and nest the styles you want.\n\n```js\nimport chalk from 'chalk';\n\nconst log = console.log;\n\n// Combine styled and normal strings\nlog(chalk.blue('Hello') + ' World' + chalk.red('!'));\n\n// Compose multiple styles using the chainable API\nlog(chalk.blue.bgRed.bold('Hello world!'));\n\n// Pass in multiple arguments\nlog(chalk.blue('Hello', 'World!', 'Foo', 'bar', 'biz', 'baz'));\n\n// Nest styles\nlog(chalk.red('Hello', chalk.underline.bgBlue('world') + '!'));\n\n// Nest styles of the same type even (color, underline, background)\nlog(chalk.green(\n\t'I am a green line ' +\n\tchalk.blue.underline.bold('with a blue substring') +\n\t' that becomes green again!'\n));\n\n// ES2015 template literal\nlog(`\nCPU: ${chalk.red('90%')}\nRAM: ${chalk.green('40%')}\nDISK: ${chalk.yellow('70%')}\n`);\n\n// Use RGB colors in terminal emulators that support it.\nlog(chalk.rgb(123, 45, 67).underline('Underlined reddish color'));\nlog(chalk.hex('#DEADED').bold('Bold gray!'));\n```\n\nEasily define your own themes:\n\n```js\nimport chalk from 'chalk';\n\nconst error = chalk.bold.red;\nconst warning = chalk.hex('#FFA500'); // Orange color\n\nconsole.log(error('Error!'));\nconsole.log(warning('Warning!'));\n```\n\nTake advantage of console.log [string substitution](https://nodejs.org/docs/latest/api/console.html#console_console_log_data_args):\n\n```js\nimport chalk from 'chalk';\n\nconst name = 'Sindre';\nconsole.log(chalk.green('Hello %s'), name);\n//=> 'Hello Sindre'\n```\n\n## API\n\n### chalk.`<style>[.<style>...](string, [string...])`\n\nExample: `chalk.red.bold.underline('Hello', 'world');`\n\nChain [styles](#styles) and call the last one as a method with a string argument. Order doesn't matter, and later styles take precedent in case of a conflict. This simply means that `chalk.red.yellow.green` is equivalent to `chalk.green`.\n\nMultiple arguments will be separated by space.\n\n### chalk.level\n\nSpecifies the level of color support.\n\nColor support is automatically detected, but you can override it by setting the `level` property. You should however only do this in your own code as it applies globally to all Chalk consumers.\n\nIf you need to change this in a reusable module, create a new instance:\n\n```js\nimport {Chalk} from 'chalk';\n\nconst customChalk = new Chalk({level: 0});\n```\n\n| Level | Description |\n| :---: | :--- |\n| `0` | All colors disabled |\n| `1` | Basic color support (16 colors) |\n| `2` | 256 color support |\n| `3` | Truecolor support (16 million colors) |\n\n### supportsColor\n\nDetect whether the terminal [supports color](https://github.com/chalk/supports-color). Used internally and handled for you, but exposed for convenience.\n\nCan be overridden by the user with the flags `--color` and `--no-color`. For situations where using `--color` is not possible, use the environment variable `FORCE_COLOR=1` (level 1), `FORCE_COLOR=2` (level 2), or `FORCE_COLOR=3` (level 3) to forcefully enable color, or `FORCE_COLOR=0` to forcefully disable. The use of `FORCE_COLOR` overrides all other color support checks.\n\nExplicit 256/Truecolor mode can be enabled using the `--color=256` and `--color=16m` flags, respectively.\n\n### chalkStderr and supportsColorStderr\n\n`chalkStderr` contains a separate instance configured with color support detected for `stderr` stream instead of `stdout`. Override rules from `supportsColor` apply to this too. `supportsColorStderr` is exposed for convenience.\n\n### modifierNames, foregroundColorNames, backgroundColorNames, and colorNames\n\nAll supported style strings are exposed as an array of strings for convenience. `colorNames` is the combination of `foregroundColorNames` and `backgroundColorNames`.\n\nThis can be useful if you wrap Chalk and need to validate input:\n\n```js\nimport {modifierNames, foregroundColorNames} from 'chalk';\n\nconsole.log(modifierNames.includes('bold'));\n//=> true\n\nconsole.log(foregroundColorNames.includes('pink'));\n//=> false\n```\n\n## Styles\n\n### Modifiers\n\n- `reset` - Reset the current style.\n- `bold` - Make the text bold.\n- `dim` - Make the text have lower opacity.\n- `italic` - Make the text italic. *(Not widely supported)*\n- `underline` - Put a horizontal line below the text. *(Not widely supported)*\n- `overline` - Put a horizontal line above the text. *(Not widely supported)*\n- `inverse`- Invert background and foreground colors.\n- `hidden` - Print the text but make it invisible.\n- `strikethrough` - Puts a horizontal line through the center of the text. *(Not widely supported)*\n- `visible`- Print the text only when Chalk has a color level above zero. Can be useful for things that are purely cosmetic.\n\n### Colors\n\n- `black`\n- `red`\n- `green`\n- `yellow`\n- `blue`\n- `magenta`\n- `cyan`\n- `white`\n- `blackBright` (alias: `gray`, `grey`)\n- `redBright`\n- `greenBright`\n- `yellowBright`\n- `blueBright`\n- `magentaBright`\n- `cyanBright`\n- `whiteBright`\n\n### Background colors\n\n- `bgBlack`\n- `bgRed`\n- `bgGreen`\n- `bgYellow`\n- `bgBlue`\n- `bgMagenta`\n- `bgCyan`\n- `bgWhite`\n- `bgBlackBright` (alias: `bgGray`, `bgGrey`)\n- `bgRedBright`\n- `bgGreenBright`\n- `bgYellowBright`\n- `bgBlueBright`\n- `bgMagentaBright`\n- `bgCyanBright`\n- `bgWhiteBright`\n\n## 256 and Truecolor color support\n\nChalk supports 256 colors and [Truecolor](https://github.com/termstandard/colors) (16 million colors) on supported terminal apps.\n\nColors are downsampled from 16 million RGB values to an ANSI color format that is supported by the terminal emulator (or by specifying `{level: n}` as a Chalk option). For example, Chalk configured to run at level 1 (basic color support) will downsample an RGB value of #FF0000 (red) to 31 (ANSI escape for red).\n\nExamples:\n\n- `chalk.hex('#DEADED').underline('Hello, world!')`\n- `chalk.rgb(15, 100, 204).inverse('Hello!')`\n\nBackground versions of these models are prefixed with `bg` and the first level of the module capitalized (e.g. `hex` for foreground colors and `bgHex` for background colors).\n\n- `chalk.bgHex('#DEADED').underline('Hello, world!')`\n- `chalk.bgRgb(15, 100, 204).inverse('Hello!')`\n\nThe following color models can be used:\n\n- [`rgb`](https://en.wikipedia.org/wiki/RGB_color_model) - Example: `chalk.rgb(255, 136, 0).bold('Orange!')`\n- [`hex`](https://en.wikipedia.org/wiki/Web_colors#Hex_triplet) - Example: `chalk.hex('#FF8800').bold('Orange!')`\n- [`ansi256`](https://en.wikipedia.org/wiki/ANSI_escape_code#8-bit) - Example: `chalk.bgAnsi256(194)('Honeydew, more or less')`\n\n## Browser support\n\nSince Chrome 69, ANSI escape codes are natively supported in the developer console.\n\n## Windows\n\nIf you're on Windows, do yourself a favor and use [Windows Terminal](https://github.com/microsoft/terminal) instead of `cmd.exe`.\n\n## FAQ\n\n### Why not switch to a smaller coloring package?\n\nChalk may be larger, but there is a reason for that. It offers a more user-friendly API, well-documented types, supports millions of colors, and covers edge cases that smaller alternatives miss. Chalk is mature, reliable, and built to last.\n\nBut beyond the technical aspects, there's something more critical: trust and long-term maintenance. I have been active in open source for over a decade, and I'm committed to keeping Chalk maintained. Smaller packages might seem appealing now, but there's no guarantee they will be around for the long term, or that they won't become malicious over time.\n\nChalk is also likely already in your dependency tree (since 100K+ packages depend on it), so switching won’t save space—in fact, it might increase it. npm deduplicates dependencies, so multiple Chalk instances turn into one, but adding another package alongside it will increase your overall size.\n\nIf the goal is to clean up the ecosystem, switching away from Chalk won’t even make a dent. The real problem lies with packages that have very deep dependency trees (for example, those including a lot of polyfills). Chalk has no dependencies. It's better to focus on impactful changes rather than minor optimizations.\n\nIf absolute package size is important to you, I also maintain [yoctocolors](https://github.com/sindresorhus/yoctocolors), one of the smallest color packages out there.\n\n*\\- [Sindre](https://github.com/sindresorhus)*\n\n### But the smaller coloring package has benchmarks showing it is faster\n\n[Micro-benchmarks are flawed](https://sindresorhus.com/blog/micro-benchmark-fallacy) because they measure performance in unrealistic, isolated scenarios, often giving a distorted view of real-world performance. Don't believe marketing fluff. All the coloring packages are more than fast enough.\n\n## Related\n\n- [chalk-template](https://github.com/chalk/chalk-template) - [Tagged template literals](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals#tagged_templates) support for this module\n- [chalk-cli](https://github.com/chalk/chalk-cli) - CLI for this module\n- [ansi-styles](https://github.com/chalk/ansi-styles) - ANSI escape codes for styling strings in the terminal\n- [supports-color](https://github.com/chalk/supports-color) - Detect whether a terminal supports color\n- [strip-ansi](https://github.com/chalk/strip-ansi) - Strip ANSI escape codes\n- [strip-ansi-stream](https://github.com/chalk/strip-ansi-stream) - Strip ANSI escape codes from a stream\n- [has-ansi](https://github.com/chalk/has-ansi) - Check if a string has ANSI escape codes\n- [ansi-regex](https://github.com/chalk/ansi-regex) - Regular expression for matching ANSI escape codes\n- [wrap-ansi](https://github.com/chalk/wrap-ansi) - Wordwrap a string with ANSI escape codes\n- [slice-ansi](https://github.com/chalk/slice-ansi) - Slice a string with ANSI escape codes\n- [color-convert](https://github.com/qix-/color-convert) - Converts colors between different models\n- [chalk-animation](https://github.com/bokub/chalk-animation) - Animate strings in the terminal\n- [gradient-string](https://github.com/bokub/gradient-string) - Apply color gradients to strings\n- [chalk-pipe](https://github.com/LitoMore/chalk-pipe) - Create chalk style schemes with simpler style strings\n- [terminal-link](https://github.com/sindresorhus/terminal-link) - Create clickable links in the terminal\n\n*(Not accepting additional entries)*\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n", "readmeFilename": "readme.md", "users": {"184455": true, "abg": true, "azz": true, "d9k": true, "djk": true, "dm7": true, "gvn": true, "kss": true, "nfd": true, "pwn": true, "sua": true, "viz": true, "xuu": true, "ymk": true, "amio": true, "andr": true, "bcoe": true, "binq": true, "boto": true, "cedx": true, "dbck": true, "dntx": true, "dwqs": true, "dyaa": true, "eijs": true, "ferx": true, "feya": true, "fill": true, "gamr": true, "glab": true, "hmsk": true, "isik": true, "j3kz": true, "jbob": true, "jits": true, "jtfu": true, "kmck": true, "l3au": true, "lorn": true, "mrxf": true, "ni-p": true, "old9": true, "owaz": true, "papb": true, "rdcl": true, "sgpr": true, "shan": true, "smd4": true, "tebb": true, "tg-z": true, "tpkn": true, "usex": true, "vwal": true, "z164": true, "zhen": true, "abdus": true, "aim97": true, "akiva": true, "ali1k": true, "altus": true, "anpdx": true, "blund": true, "brpaz": true, "bsara": true, "conzi": true, "csbun": true, "cxftj": true, "ddffx": true, "decal": true, "demod": true, "dralc": true, "ealen": true, "ehrig": true, "flozz": true, "ginof": true, "gor0n": true, "inhji": true, "jalik": true, "jclem": true, "jream": true, "jruif": true, "junos": true, "jysun": true, "kikar": true, "kikna": true, "kremr": true, "l0gin": true, "laomu": true, "leapm": true, "lensi": true, "liigo": true, "lijsh": true, "lqweb": true, "lusai": true, "mdemo": true, "megeo": true, "mmork": true, "modao": true, "muroc": true, "nak2k": true, "nelix": true, "nguru": true, "passy": true, "phydo": true, "razr9": true, "rioli": true, "rnbwd": true, "rpnna": true, "rshaw": true, "sgaim": true, "slurm": true, "sopov": true, "t1st3": true, "taber": true, "three": true, "travm": true, "tttai": true, "vitre": true, "voyga": true, "vyder": true, "xyyjk": true, "yikuo": true, "yrocq": true, "zoxon": true, "zplus": true, "zzzze": true, "456wyc": true, "71emj1": true, "aamnah": true, "abeldu": true, "ajduke": true, "akarem": true, "alek-s": true, "alexpk": true, "arttse": true, "aurium": true, "avdons": true, "axelav": true, "beytek": true, "binnng": true, "bitfed": true, "bojand": true, "bpatel": true, "buzuli": true, "bvaccc": true, "c_ogoo": true, "chiefy": true, "citguy": true, "crwnvr": true, "curcuz": true, "d-band": true, "davask": true, "db6edr": true, "dd-dxq": true, "devnka": true, "dg1an3": true, "dkimot": true, "dotnil": true, "dudley": true, "duncup": true, "edjroz": true, "egantz": true, "ekmpls": true, "eseath": true, "evan2x": true, "fredev": true, "fyockm": true, "gabeio": true, "gberto": true, "gdbtek": true, "gindis": true, "glebec": true, "godion": true, "hitesh": true, "hobord": true, "ierceg": true, "iotale": true, "isayme": true, "iuykza": true, "jaenqn": true, "jamlen": true, "jimnox": true, "jolg42": true, "joni3k": true, "jorycn": true, "jsimck": true, "judlup": true, "karlas": true, "kaycee": true, "kazem1": true, "knoja4": true, "krocon": true, "ksketo": true, "kungkk": true, "lagora": true, "lekkas": true, "liu946": true, "ljmf00": true, "mahume": true, "majgis": true, "mat2ja": true, "mdang8": true, "mestar": true, "minghe": true, "mklabs": true, "mohsen": true, "monjer": true, "mrdain": true, "mrzmmr": true, "mustak": true, "narven": true, "neilor": true, "nexflo": true, "noyobo": true, "omegga": true, "openam": true, "owillo": true, "pandao": true, "pigram": true, "praxiq": true, "qisong": true, "qubyte": true, "rangzf": true, "rockad": true, "ruzzll": true, "seanjh": true, "sermir": true, "shriek": true, "simoyw": true, "slints": true, "smituk": true, "solodu": true, "someok": true, "srbdev": true, "suissa": true, "tazjel": true, "tcrowe": true, "tedyhy": true, "vhflat": true, "vlados": true, "wenjul": true, "wisetc": true, "xlaoyu": true, "yeming": true, "yinfxs": true, "yl2014": true, "ziflex": true, "zlatip": true, "zwwggg": true, "adriasb": true, "adrtho4": true, "alectic": true, "alexreg": true, "alnafie": true, "alphatr": true, "anoubis": true, "antanst": true, "anticom": true, "azevedo": true, "barenko": true, "burl.bn": true, "cbeulke": true, "chinjon": true, "cocorax": true, "croogie": true, "ctd1500": true, "cueedee": true, "cyandev": true, "demoive": true, "deubaka": true, "devxleo": true, "dog1245": true, "dukewan": true, "dyakovk": true, "ejmason": true, "ezodude": true, "fabiodr": true, "fantasy": true, "flitrue": true, "fnouama": true, "ftornik": true, "geassor": true, "hehehai": true, "hemanth": true, "hexcola": true, "hitalos": true, "itonyyo": true, "jcottam": true, "jlertle": true, "jonkemp": true, "joylobo": true, "kaashin": true, "kahboom": true, "kakaman": true, "kekdude": true, "keybouh": true, "kiinlam": true, "kontrax": true, "kparkov": true, "kprasha": true, "lachriz": true, "lacodda": true, "langjun": true, "levmast": true, "liunian": true, "lvivier": true, "lwdthe1": true, "marinru": true, "merdars": true, "morewry": true, "mygoare": true, "nadimix": true, "nanxing": true, "ngpixel": true, "nilz3ro": true, "nogirev": true, "nohomey": true, "popc0rn": true, "restuta": true, "rhaynel": true, "ripdash": true, "rkmedia": true, "rlgomes": true, "sahilsk": true, "sahlzen": true, "saoskia": true, "severen": true, "shahyar": true, "shenbin": true, "solygen": true, "sprying": true, "sshrike": true, "stefanb": true, "studi11": true, "stursby": true, "subchen": true, "suchipi": true, "syaning": true, "taskone": true, "tbremer": true, "tengisb": true, "timwzou": true, "trackds": true, "tsxuehu": true, "ubenzer": true, "undre4m": true, "ungurys": true, "vboctor": true, "washiba": true, "wenbing": true, "xfloops": true, "xtx1130": true, "yakumat": true, "yanghcc": true, "yangtze": true, "yorts52": true, "zollero": true, "adrien.d": true, "aegypius": true, "aidenzou": true, "akic4op4": true, "alanshaw": true, "anhulife": true, "artjacob": true, "asfrom30": true, "bapinney": true, "bcowgi11": true, "behumble": true, "bicienzu": true, "brubrant": true, "chaofeis": true, "colageno": true, "cslasher": true, "cy476571": true, "danday74": true, "daskepon": true, "davenchy": true, "ddaversa": true, "deerflow": true, "dexteryy": true, "djamseed": true, "drossman": true, "dzhou777": true, "eb.coder": true, "esundahl": true, "exromany": true, "fabioper": true, "fakefarm": true, "faraoman": true, "fassetar": true, "fintanak": true, "froguard": true, "gavatron": true, "gracelee": true, "gurunate": true, "hugovila": true, "hyanghai": true, "iamninad": true, "icaliman": true, "ifeature": true, "iwaffles": true, "jackvial": true, "jasonnic": true, "johniexu": true, "josudoey": true, "jsmootiv": true, "junyeong": true, "kaybeard": true, "kenlimmj": true, "klombomb": true, "koalaylj": true, "krabello": true, "krzych93": true, "kurozero": true, "leejefon": true, "leodutra": true, "limingv5": true, "lourenzo": true, "makediff": true, "manishrc": true, "markstos": true, "masonwan": true, "maxblock": true, "meetravi": true, "mgebundy": true, "mhaidarh": true, "moimikey": true, "nalindak": true, "nicocube": true, "nketchum": true, "nraibaud": true, "oboochin": true, "ohyeah02": true, "oleynikd": true, "pddivine": true, "philosec": true, "pnolasco": true, "podlebar": true, "pr-anoop": true, "pramodht": true, "qddegtya": true, "rbartoli": true, "rchanaud": true, "rizowski": true, "robba.jt": true, "romelyus": true, "rsosenke": true, "saidgeek": true, "santihbc": true, "sanusart": true, "scaffrey": true, "scronide": true, "semencov": true, "sewillia": true, "shaddyhm": true, "shaunieb": true, "shiva127": true, "sixertoy": true, "spinlock": true, "spywhere": true, "stoneren": true, "susuaung": true, "tcauduro": true, "thechori": true, "thibauts": true, "tmurngon": true, "tosbodes": true, "uniquevn": true, "vishwasc": true, "vlasterx": true, "waiwaiku": true, "wangfeia": true, "wisecolt": true, "wkaifang": true, "xdream86": true, "xgheaven": true, "xiaobing": true, "xueboren": true, "yashprit": true, "yfyhsoft": true, "yitzchak": true, "yokiming": true, "zguillez": true, "zhbyak47": true, "zhouanbo": true, "zuojiang": true, "abuelwafa": true, "alexbudin": true, "alexreg90": true, "alimaster": true, "allen_lyu": true, "aquafadas": true, "avenida14": true, "azaritech": true, "backnight": true, "becarchal": true, "behrangsa": true, "belirafon": true, "bigslycat": true, "bobxuyang": true, "bryan.ygf": true, "chriscalo": true, "clementoh": true, "cmdaniels": true, "codeshrew": true, "ctesniere": true, "cuidapeng": true, "davequick": true, "ddkothari": true, "demon-php": true, "dlpowless": true, "dosevader": true, "dr-benton": true, "dracochou": true, "drdanryan": true, "edosrecki": true, "edwingeng": true, "emircanok": true, "ephigenia": true, "fistynuts": true, "flftfqwxf": true, "furzeface": true, "gableroux": true, "gavinning": true, "geeksunny": true, "gillstrom": true, "gochomugo": true, "guananddu": true, "guumaster": true, "heartnett": true, "hehaiyang": true, "hemstreet": true, "hewenxuan": true, "hisokader": true, "hypnoglow": true, "iceriver2": true, "icirellik": true, "inderdeep": true, "isenricho": true, "jakedalus": true, "jason0518": true, "jasoncmcg": true, "jasonwbsg": true, "jbnicolai": true, "jerkovicl": true, "jesusgoku": true, "jetthiago": true, "johnohara": true, "jondotsoy": true, "karthickt": true, "kikobeats": true, "kleintobe": true, "landy2014": true, "largepuma": true, "ldq-first": true, "leonweecs": true, "luckyulin": true, "luukmoret": true, "macmladen": true, "madsummer": true, "magicmind": true, "maniprojs": true, "masiorama": true, "mastayoda": true, "mistkafka": true, "mjurincic": true, "morsellif": true, "myjustify": true, "necanicum": true, "nice_body": true, "nicksnell": true, "ninozhang": true, "oakley349": true, "piixiiees": true, "pirxpilot": true, "ptallen63": true, "qqcome110": true, "ragex1337": true, "ramzesucr": true, "rbecheras": true, "rocksynth": true, "rubiadias": true, "rylan_yan": true, "sadsenpai": true, "sasquatch": true, "serdar2nc": true, "shakakira": true, "shidhincr": true, "sparkbuzz": true, "sqrtthree": true, "starfox64": true, "sternelee": true, "subfuzion": true, "sujeet555": true, "sunnylost": true, "sushiifox": true, "theaklair": true, "tjfwalker": true, "trocafone": true, "tstringer": true, "tylerhaun": true, "valenwave": true, "vampirkod": true, "wmcmurray": true, "xerullian": true, "yang.shao": true, "yanrivera": true, "yuxichina": true, "zapastore": true, "3creatives": true, "adammacias": true, "ahmetertem": true, "alanerzhao": true, "alexdreptu": true, "allenmoore": true, "amdsouza92": true, "andriecool": true, "angrykoala": true, "antoinelnr": true, "avantassel": true, "avivharuzi": true, "axelrindle": true, "bashkernel": true, "bengarrett": true, "bluelovers": true, "brightchen": true, "bstevenson": true, "carloshpds": true, "cestrensem": true, "chinaqstar": true, "colleowino": true, "dannynemer": true, "davidchase": true, "demigodliu": true, "domjtalbot": true, "dwayneford": true, "echaouchna": true, "edgardoalz": true, "evanlovely": true, "franksansc": true, "genediazjr": true, "german1608": true, "giussa_dan": true, "goodseller": true, "growlybear": true, "guioconnor": true, "harumambur": true, "henrytseng": true, "iainhallam": true, "insomniaqc": true, "instazapas": true, "ivanempire": true, "iwasawafag": true, "jameskrill": true, "jessaustin": true, "jmoser-amr": true, "joinjoohny": true, "junjiansyu": true, "justinliao": true, "kappuccino": true, "kphillycat": true, "kreshikhin": true, "leizongmin": true, "leonardorb": true, "lewisbrown": true, "lijinghust": true, "lius971125": true, "logeshpaul": true, "mahnunchik": true, "manikantag": true, "marco.jahn": true, "maxime1992": true, "mgthomas99": true, "micahjonas": true, "mjwilliams": true, "monkeyyy11": true, "mutantspew": true, "myorkgitis": true, "mysticatea": true, "myterminal": true, "namhyun-gu": true, "ocd_lionel": true, "omidnikrah": true, "pedroparra": true, "piecioshka": true, "pmbenjamin": true, "princetoad": true, "prometheas": true, "qqqppp9998": true, "rkoksulics": true, "robotomize": true, "rocket0191": true, "ryanwarsaw": true, "seangenabe": true, "shoresh319": true, "shuoshubao": true, "silverwind": true, "simplyianm": true, "smokinhuzi": true, "sonhuytran": true, "tangweikun": true, "toddtreece": true, "valentin_h": true, "vapeadores": true, "vincentlau": true, "werninator": true, "winjeysong": true, "youxiachai": true, "yuvalblass": true, "zeroknight": true, "zhadongmin": true, "zousandian": true, "a3.ivanenko": true, "adrienhobbs": true, "aereobarato": true, "ahsanshafiq": true, "alexboorman": true, "andrewtlove": true, "archcorsair": true, "arkanciscan": true, "arnoldstoba": true, "ashokramcse": true, "balazserdos": true, "blackoperat": true, "brainmurder": true, "brandonb927": true, "bsdprojects": true, "chinmay2893": true, "codeandcats": true, "codeprowong": true, "coolhanddev": true, "craigpatten": true, "cranndarach": true, "danyadsmith": true, "davidazullo": true, "davidnyhuis": true, "django_wong": true, "drew.brokke": true, "ericteng177": true, "ericwbailey": true, "eserozvataf": true, "fabioricali": true, "fearnbuster": true, "fengmiaosen": true, "flumpus-dev": true, "frenchbread": true, "frknbasaran": true, "gamingcoder": true, "gauravmehla": true, "he313572052": true, "heyimeugene": true, "highgravity": true, "hq229075284": true, "illuminator": true, "indooorsman": true, "jasonyikuai": true, "jonatasnona": true, "jovenbarola": true, "karlbateman": true, "kobleistvan": true, "kodekracker": true, "leelee.echo": true, "lfilipowicz": true, "liqiang0335": true, "lukvonstrom": true, "m80126colin": true, "marcfiedler": true, "markfknight": true, "mattray0295": true, "micaelsouza": true, "michalskuza": true, "mseminatore": true, "neaker15668": true, "phoward8020": true, "powellmedia": true, "prestorondo": true, "prochafilho": true, "questioneer": true, "raisiqueira": true, "russleyshaw": true, "sammihansen": true, "sammyteahan": true, "schwartzman": true, "scotttesler": true, "shangsinian": true, "silentcloud": true, "soenkekluth": true, "strathausen": true, "sunny.zhouy": true, "thangakumar": true, "thetwosents": true, "tonerbarato": true, "trquoccuong": true, "tunnckocore": true, "volkanongun": true, "wangnan0610": true, "yujiikebata": true, "alanchenchen": true, "alex-the-dev": true, "andi-oxidant": true, "battlemidget": true, "bianlongting": true, "brandonmowat": true, "brani.djuric": true, "brentlintner": true, "danielknaust": true, "dpjayasekara": true, "duartemendes": true, "goblindegook": true, "henriesteves": true, "hugojosefson": true, "igorsetsfire": true, "iori20091101": true, "ivangaravito": true, "jamesallured": true, "jamescostian": true, "josejaguirre": true, "joshdoescode": true, "justdomepaul": true, "justintormey": true, "kiandrajayne": true, "letecitanjir": true, "marianoviola": true, "mrahmadawais": true, "natterstefan": true, "npm-packages": true, "oussoulessou": true, "partsunknown": true, "paulomcnally": true, "podviaznikov": true, "puranjayjain": true, "ramkrish2079": true, "raphaelchaib": true, "rethinkflash": true, "rickbergfalk": true, "ristostevcev": true, "ruchirgodura": true, "saadbinsaeed": true, "sfpharmaplus": true, "shaomingquan": true, "spenceralger": true, "superchenney": true, "taylorpzreal": true, "thcheetah777": true, "thegreatrazz": true, "walexstevens": true, "wallenberg12": true, "wesleylhandy": true, "wfalkwallace": true, "williamsando": true, "winglonelion": true, "bernardhamann": true, "brianchung808": true, "carloseduardo": true, "chinawolf_wyp": true, "chrisdevwords": true, "codyschindler": true, "dasilvacontin": true, "derflatulator": true, "deyvisonsouto": true, "edwin_estrada": true, "ferchoriverar": true, "geekforbrains": true, "hibrahimsafak": true, "humantriangle": true, "jasonwang1888": true, "jedaisaboteur": true, "langdon-holly": true, "liangtongzhuo": true, "lucasmciruzzi": true, "manojkhannakm": true, "markthethomas": true, "mdedirudianto": true, "nackjicholson": true, "patrickkraaij": true, "pauljmartinez": true, "pedroteosousa": true, "peter.forgacs": true, "professorcoal": true, "richardcfelix": true, "robinblomberg": true, "scottfreecode": true, "sebastian1118": true, "stone_breaker": true, "thomasmeadows": true, "unitetheclans": true, "xavierharrell": true, "zhizhunbao995": true, "abdullahceylan": true, "arnold-almeida": true, "charlie.wilson": true, "chrishonniball": true, "eliasargandara": true, "forbeslindesay": true, "greenbud-seeds": true, "imaginegenesis": true, "karzanosman984": true, "lefthandhacker": true, "leonardothibes": true, "maycon_ribeiro": true, "richardleggett": true, "shanewholloway": true, "supan_20220713": true, "suryasaripalli": true, "swapnil_mishra": true, "thiagowittmann": true, "troels.trvo.dk": true, "usingthesystem": true, "willwolffmyren": true, "xiaoqiang.yang": true, "andygreenegrass": true, "arcticicestudio": true, "behnameghorbani": true, "charlietango592": true, "federico-garcia": true, "gestoria-madrid": true, "icodeforcookies": true, "jarrodhroberson": true, "jeffreysbrother": true, "joaquin.briceno": true, "jonathanbergson": true, "jonnymaceachern": true, "leonardodavinci": true, "miguelprovencio": true, "muhammedyousrii": true, "sametsisartenep": true, "subinvarghesein": true, "theamazingfedex": true, "thebespokepixel": true, "alexandru.vasile": true, "bursalia-gestion": true, "crashtheuniverse": true, "gresite_piscinas": true, "michaeljwilliams": true, "nasser-torabzade": true, "alquilerargentina": true, "damianopetrungaro": true, "gabriel.zoroaster": true, "mattthewcbelanger": true, "michaelsbradleyjr": true, "nguyenmanhdat2903": true, "theodor.lindekaer": true, "felixgarciasanchez": true, "vision_tecnologica": true, "azulejosmetrosubway": true, "granhermandadblanca": true, "daniel-lewis-bsc-hons": true}}