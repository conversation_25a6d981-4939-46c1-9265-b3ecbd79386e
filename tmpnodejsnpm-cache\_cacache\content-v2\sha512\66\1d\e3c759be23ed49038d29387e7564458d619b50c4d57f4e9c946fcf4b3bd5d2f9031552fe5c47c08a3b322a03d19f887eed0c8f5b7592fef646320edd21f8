{"_id": "concurrently", "_rev": "249-f09b423fa9b7379d13b03d56c1506bc8", "name": "concurrently", "dist-tags": {"latest": "9.2.0"}, "versions": {"0.0.2": {"name": "concurrently", "version": "0.0.2", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@0.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js"}, "dist": {"shasum": "9bf3aaa23f091da0d5bf4d3cfa0db94a8f50be75", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-0.0.2.tgz", "integrity": "sha512-1Ae6ECwq7WGijfvqVuKQ7hjW90i8X2sIjzL8VvdmyjT6SNXkkuJ4igC5hH0KFHffFY317HW6OiqKcqv4/8QbLg==", "signatures": [{"sig": "MEUCIFEI5gA6gWRKxB9cVnKa+VrYCFamm1kqtzOFBK8g8QgwAiEAsz++b0HgGXE/pVnOR9NPXPC3HwPXpG+5IIA+czaDiI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "9bf3aaa23f091da0d5bf4d3cfa0db94a8f50be75", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Run commands concurrently", "directories": {}, "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "3.1.0", "bluebird": "2.9.6", "commander": "2.6.0"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0"}}, "0.0.3": {"name": "concurrently", "version": "0.0.3", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@0.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js"}, "dist": {"shasum": "4dbd5f5d11731beb087c91dc50055cd84afbbff9", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-0.0.3.tgz", "integrity": "sha512-jNO9MQOyRDjh98lCb06BQO0lyHTF0uck6EjH3cmHyf4tbaP1PCNIN7n2RvHu6dSuiYNAUiJV1iHD1A1BZY6zXg==", "signatures": [{"sig": "MEQCIEqxkM3UwJ2C6JOQ2i/3DX/GfhgXl6pIkVC8O5wQYv1hAiB+KZ8rg6Zhwv7XwydWz7btitERaGJhW+82p7IWAlxo2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "4dbd5f5d11731beb087c91dc50055cd84afbbff9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Run commands concurrently", "directories": {}, "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "3.1.0", "bluebird": "2.9.6", "commander": "2.6.0"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0"}}, "0.0.4": {"name": "concurrently", "version": "0.0.4", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@0.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js"}, "dist": {"shasum": "9f254d838e8b34be921653395792dc45af4f2aeb", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-0.0.4.tgz", "integrity": "sha512-oHJt1IJLurwQVXDXs8Hzrsh/LfpwAVzYAoFp8TVZuBR0INvfyHJXBaYC1Bui/KZK4emyxtAVNh9gpq9JZBCe0g==", "signatures": [{"sig": "MEUCIDEVwxa9jrr3ULtN6Fi/2hIi+G+LoVIu+QPpVL0FcUKyAiEAnPyBcXykpC98Z6TRUxohtypFp0H8gW2CeV9ar6G/l7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "9f254d838e8b34be921653395792dc45af4f2aeb", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Run commands concurrently", "directories": {}, "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "3.1.0", "bluebird": "2.9.6", "commander": "2.6.0"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0"}}, "0.0.5": {"name": "concurrently", "version": "0.0.5", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@0.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js"}, "dist": {"shasum": "ed3c5c0496c8cb7138abe9792c805e1d28f821b9", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-0.0.5.tgz", "integrity": "sha512-IM2iUiRqBMrV73gcL69azmPdniTUFhrLMRIuWcGcxOZBfqOBO9R/25iD+Yd24extnTJgY3Yifyv9obj70T6VdQ==", "signatures": [{"sig": "MEUCIGXRImaRcz6XO9QSGe+Y5XazpgicyI551HM3iE3F57h3AiEA0pDr93z9x+KsUmHxGZOpzcW3ud8SizJmfq9pTb3bPss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "ed3c5c0496c8cb7138abe9792c805e1d28f821b9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Run commands concurrently", "directories": {}, "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "3.1.0", "bluebird": "2.9.6", "commander": "2.6.0"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0"}}, "0.1.0": {"name": "concurrently", "version": "0.1.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js"}, "dist": {"shasum": "fa8f89316cbc6c80777924453adf805ec065c813", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-0.1.0.tgz", "integrity": "sha512-4Ga8kY2TRL/hbFdOV0fIIkGKbBcX5znpOfGueRFNsEIyLQSiBv+aCxoQ+Ci9gQkcTtH/jO8yEG7eBMUksq3myA==", "signatures": [{"sig": "MEUCIDFGUoYf+s9+XYfEMCIiwumtIG/nhSdbF5nVqVm7noJCAiEA6drNdBUAGJ680aDHcr0g76WUNtfSCF5Nl/zD/kouCdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "fa8f89316cbc6c80777924453adf805ec065c813", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Run commands concurrently", "directories": {}, "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "3.1.0", "bluebird": "2.9.6", "commander": "2.6.0", "cross-spawn": "^0.2.9"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0"}}, "0.1.1": {"name": "concurrently", "version": "0.1.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js"}, "dist": {"shasum": "0cf319104c6421c49485d42293c1f89cb22eae73", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-0.1.1.tgz", "integrity": "sha512-gfej/aHL0+gFpUaXtytQv3IgbI/lrcYmR/fhGhp4SK2hxH7CiB085GiTZ5GxN7n2KNTFv8wB/qVB3liDnTiM+w==", "signatures": [{"sig": "MEUCIFUpVHSbYNbZsSlULzkmcCLsJyg80jR82QC0LNnH4ZkXAiEA45Y+hLEk+9H8uOpC4bg7xiE0DBzwKSmmriEzzWrDDBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "0cf319104c6421c49485d42293c1f89cb22eae73", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Run commands concurrently", "directories": {}, "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "3.1.0", "bluebird": "2.9.6", "commander": "2.6.0", "cross-spawn": "^0.2.9"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "shell-quote": "^1.4.3"}}, "1.0.0": {"name": "concurrently", "version": "1.0.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js"}, "dist": {"shasum": "5b3920edbcfac0a373d6c1300403aaa9a09298c8", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-1.0.0.tgz", "integrity": "sha512-XjR2UAqzDzrmX8hugm9KCaDD5zKXBy2gFXvDZAZMDfDNJcZPmAtW9ZXn1hf33spDJ4PSaRXEKbpzRJQZ2/us3g==", "signatures": [{"sig": "MEYCIQDpIRmKi6/ix9khwaPAtNN4cwCeIgo9hbe0tBXJcBmq6gIhAMv4k3031O498DE5OpkKsgYMGHqHFZ5N5/6Rdf6gTf3v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "5b3920edbcfac0a373d6c1300403aaa9a09298c8", "gitHead": "e21fab195b40a352e59d3a024c1915e520851ccf", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "3.1.0", "bluebird": "2.9.6", "commander": "2.6.0", "cross-spawn": "^0.2.9"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "shell-quote": "^1.4.3"}}, "2.0.0": {"name": "concurrently", "version": "2.0.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "117788a9e994e40b92df7e302f5efc5a6c3e6ed8", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-2.0.0.tgz", "integrity": "sha512-C3YRsUlFHeV6jF0hIbRtbxdkoS6k2rIVOA3nrvmO3indFbQYE0hYBrpEo+iOjE8SDe2Z563d1acgpQ9ij+u/VQ==", "signatures": [{"sig": "MEQCIBTkssbhUQkF2LAlojRG2L5RhSIrVVtDMhzlxUW28z1oAiBJQ4Lne6k1XXNSfjOAnDE46s8UCW/NGGrJVbSMqbcrwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "117788a9e994e40b92df7e302f5efc5a6c3e6ed8", "gitHead": "534ad6abec3564bc931227bf7a9d8658d3cd416b", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "3.1.0", "moment": "^2.11.2", "bluebird": "2.9.6", "commander": "2.6.0", "cross-spawn": "^0.2.9"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-2.0.0.tgz_1455661050086_0.9889803037513047", "host": "packages-5-east.internal.npmjs.com"}}, "2.1.0": {"name": "concurrently", "version": "2.1.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "1ec19c83f235adb58a7c52447be1b287def8387d", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-2.1.0.tgz", "integrity": "sha512-M/ghFH+xRUaYuoKtnMLfKt4wEHIhSQNAAnNU0yCdiVCNuOMLCxD0Q5nOpYxlvpiBwOq3BffLi8fudJ2y5LBqZA==", "signatures": [{"sig": "MEYCIQDhxEzujjWY4W7DtqP0YDlq6amyv1LhKIiY/2KORl72FwIhALUk3Nl2iQVRo0OUx7Yq3lI0h3AdTCxbmUmi+e+QtVTz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "1ec19c83f235adb58a7c52447be1b287def8387d", "gitHead": "5fc456439a946169e3b914dc1846e6c9b785cd9c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "4.1.2", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "moment": "^2.11.2", "bluebird": "2.9.6", "commander": "2.6.0", "cross-spawn": "^0.2.9"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-2.1.0.tgz_1463220235537_0.5137625059578568", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.0": {"name": "concurrently", "version": "2.2.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@2.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "bad248e0bb129fb1621768903a6311d45d56895a", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-2.2.0.tgz", "integrity": "sha512-sT1UC+4d8o8PeuYqitiK9Qcw8/RHidSSSabnI7ijFKsfr8p8dM/zfpsl0Ym5JR+9/1fN0GYd0ctLI6bVNRYhIA==", "signatures": [{"sig": "MEUCIQC08aXsCIogcMxu1v5pkf43o+T75MjzKTJ34TY0nj62UwIgbWG5CWU1nrx3mBPE+lfmngiQh9I7sgO649Oy1xqtqIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "bad248e0bb129fb1621768903a6311d45d56895a", "gitHead": "497a6c35a19df981ba67430feb7f8612ab0bfd29", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "6.1.0", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "moment": "^2.11.2", "bluebird": "2.9.6", "commander": "2.6.0", "cross-spawn": "^0.2.9"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-2.2.0.tgz_1467712409992_0.07769871852360666", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0-dev": {"name": "concurrently", "version": "3.0.0-dev", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.0.0-dev", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "df4e017938522258d5cef9c213a7e20ac814ce5e", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.0.0-dev.tgz", "integrity": "sha512-2Shk+Q2z/2dzLZpdeBM7i1PtQhw8SlW7D4umNoQgn7rN7VD4TJRrP9RghAXGrPjNPfwn8Vcj3G1JgyT7lXp1Ww==", "signatures": [{"sig": "MEUCIQDtWE27EV+B/oHVjfjHuOXQLhV1veB7PMKF5whcp4BB7AIgEhQfaXT8S9uJoQ57Ju7Twf9cQP/UzDwUj6C3lyWzb+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "df4e017938522258d5cef9c213a7e20ac814ce5e", "gitHead": "29adfc6c5a66686a1d86940c4ef4413e4a7cb920", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "moment": "^2.11.2", "bluebird": "2.9.6", "commander": "2.6.0", "spawn-default-shell": "^1.0.0"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-3.0.0-dev.tgz_1474236710979_0.5285459866281599", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0-rc1": {"name": "concurrently", "version": "3.0.0-rc1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.0.0-rc1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "70bc5597d5e415bbc38573c57b694c9bf585fbf9", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.0.0-rc1.tgz", "integrity": "sha512-ddjFvOspMjdxMWX4O0/ZKjPO1VHDcgv3nreF9SkJVko5Tp4EOgEf7vg0hIDv8E2od1nrh2eYI/4PloDSzJZT/Q==", "signatures": [{"sig": "MEQCIBOEH8PvfNUrezYoCwngbu1yxxNeuWuv1ilRJNGyYiE4AiBqX5lILig+0tQ5uy7nPwGYmNPCz7pdJJE4AtX9T56T/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "70bc5597d5e415bbc38573c57b694c9bf585fbf9", "gitHead": "a9e76a05525af904e869c3c9fdb95b015024e57f", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "moment": "^2.11.2", "bluebird": "2.9.6", "commander": "2.6.0", "tree-kill": "^1.1.0", "spawn-default-shell": "^1.0.0"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-3.0.0-rc1.tgz_1474324239053_0.17175340978428721", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "concurrently", "version": "3.0.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "b61d1cc72b2ed2824eff34649e5779bf6dc59004", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.0.0.tgz", "integrity": "sha512-r8OYC5+ej0M6evqgQKRrJ/CCip6E/yioJI54tVfMEum12NhH0D76SxiU5F2epJIJoAFSNTB0pzxWTWgwVInVMw==", "signatures": [{"sig": "MEUCIQD2qJTCJ0qr7XOTh7E3/yxqVM7nmC08dshzvjNJmkVyywIgBCqTZZ6QAOMyO73BrKC3bH6A20OOaHsiQLJ1JdF4bTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "b61d1cc72b2ed2824eff34649e5779bf6dc59004", "gitHead": "dd79809dd327792e4294c78451bef9164738567d", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "moment": "^2.11.2", "bluebird": "2.9.6", "commander": "2.6.0", "tree-kill": "^1.1.0", "spawn-default-shell": "^1.0.0"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-3.0.0.tgz_1474882975696_0.7272696373984218", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.0": {"name": "concurrently", "version": "3.1.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "dc5ef0459090012604756668894c04b434ef90d1", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.1.0.tgz", "integrity": "sha512-memNK1sQh47EUHEkLY0aVjpzdNT0c+paC+lYzb2sSVU9evCaM50ZIlsCQDLbFIwf4Jo8gRq61eU+Yv4wnNTe2A==", "signatures": [{"sig": "MEUCIQCMD9xIm1lDmq/Hh7kb/lBoOfZFSrb7Z8qNir5jWfZJbAIgV+YoMVGadC1HZo/8bGEJHx75DrZdU7Ov0BxdjNuVotk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "dc5ef0459090012604756668894c04b434ef90d1", "gitHead": "867adbabe4222ba1ccb0a06099f7fb939ea1d38e", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "moment": "^2.11.2", "bluebird": "2.9.6", "commander": "2.6.0", "tree-kill": "^1.1.0", "spawn-default-shell": "^1.1.0"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "releasor": "^1.2.1", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-3.1.0.tgz_1475403231154_0.7748375695664436", "host": "packages-12-west.internal.npmjs.com"}}, "3.2.0": {"name": "concurrently", "version": "3.2.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.2.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "51905754c99c03a960fae845afd85e1e9d220af6", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.2.0.tgz", "integrity": "sha512-DAjrohA0WLKBf1w9TB2qlgHI47iQRX66Xa6hg8TsjWR8sxlMhDBrGstQcm8+CZI4sS3h8sYRkzXMURFnMvJxvg==", "signatures": [{"sig": "MEQCIFbeQ8dICexmgtGKGqNUu8S2pIYpambaqtSTER/NsGIsAiA8YIFKEAc+uMXt0wGxjAXzBTbww5QX+6Ot6J9BQ3PwFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "51905754c99c03a960fae845afd85e1e9d220af6", "engines": {"node": ">=4.0.0"}, "gitHead": "7661fc4c4f04fc79de11dae82db99704ba1c99d0", "scripts": {"test": "mocha"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "date-fns": "^1.23.0", "commander": "2.6.0", "tree-kill": "^1.1.0", "supports-color": "^3.2.3", "spawn-default-shell": "^2.0.0"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "releasor": "^1.2.1", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-3.2.0.tgz_1486597646994_0.286185085773468", "host": "packages-12-west.internal.npmjs.com"}}, "3.3.0": {"name": "concurrently", "version": "3.3.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.3.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "d8eb7a9765fdf0b28d12220dc058e14d03c7dd4f", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.3.0.tgz", "integrity": "sha512-6NkwIHQ1cbHA8nkWAfcW8srWgQJmw+dNqt2K5Xxx6M9B9hZoeCNefWLN5WHy0lwnWAIXEZNKa9hRIjaSbnBo4w==", "signatures": [{"sig": "MEYCIQCHCM569/VNN+BzXKWt6tR49KdR3sDhooeComr3QOZwvwIhAINJI9D9AhOCmZ/No+PyohOyzXgI+dMiv+7aGiKR5dzK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "d8eb7a9765fdf0b28d12220dc058e14d03c7dd4f", "engines": {"node": ">=4.0.0"}, "gitHead": "63440dc72e9641892f3eee25582ca1fcc1caf206", "scripts": {"test": "mocha"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "date-fns": "^1.23.0", "commander": "2.6.0", "tree-kill": "^1.1.0", "spawn-command": "^0.0.2-1", "supports-color": "^3.2.3"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "releasor": "^1.2.1", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-3.3.0.tgz_1486921024089_0.19308558432385325", "host": "packages-18-east.internal.npmjs.com"}}, "3.4.0": {"name": "concurrently", "version": "3.4.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.4.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "60662b3defde07375bae19aac0ab780ec748ba79", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.4.0.tgz", "integrity": "sha512-wd8f4pBIKp/lLEJnBHtPTXf+IOzx+TjeL7boKsMGHm5dyr7wqrIQZ4Z2O7dEH2UGZMhOWwlNGETs23u8ZaeLKA==", "signatures": [{"sig": "MEUCIFSKQ8aYJuAimKKMCyIISYihGRCFEWl9odYgpAdsuMtiAiEA3X51mbYTaVaFMnFkapzSRUT2gaAz9ALBhixeXNvJlVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "60662b3defde07375bae19aac0ab780ec748ba79", "engines": {"node": ">=4.0.0"}, "gitHead": "072bdf20687dec8317181a254a3e5c3dd0467a4b", "scripts": {"test": "mocha"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "date-fns": "^1.23.0", "commander": "2.6.0", "tree-kill": "^1.1.0", "spawn-command": "^0.0.2-1", "supports-color": "^3.2.3"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "releasor": "^1.2.1", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-3.4.0.tgz_1488425255418_0.17213288904167712", "host": "packages-18-east.internal.npmjs.com"}}, "3.5.0": {"name": "concurrently", "version": "3.5.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.5.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "8cf1b7707a6916a78a4ff5b77bb04dec54b379b2", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.5.0.tgz", "integrity": "sha512-Z2iVM5+c0VxKmENTXrG/kp+MUhWEEH+wI5wV/L8CTFJDb/uae1zSVIkNM7o3W4Tdt42pv7RGsOICaskWy9bqSA==", "signatures": [{"sig": "MEUCIEumgzhpEx1pjA+RJQSZGZI47mZ9WXAeCPJAI257lw+JAiEAyxtHHB3wTwZDpxBg2bFcvLmDoeMeLBi4xdLKw//swuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "_from": ".", "_shasum": "8cf1b7707a6916a78a4ff5b77bb04dec54b379b2", "engines": {"node": ">=4.0.0"}, "gitHead": "c2b68faa26e33af099fe95610d7416c8d2897d09", "scripts": {"test": "mocha"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "6.10.2", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "date-fns": "^1.23.0", "commander": "2.6.0", "tree-kill": "^1.1.0", "spawn-command": "^0.0.2-1", "supports-color": "^3.2.3"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "releasor": "^1.2.1", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-3.5.0.tgz_1498096612683_0.996132883708924", "host": "s3://npm-registry-packages"}}, "3.5.1": {"name": "concurrently", "version": "3.5.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.5.1", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "ee8b60018bbe86b02df13e5249453c6ececd2521", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.5.1.tgz", "integrity": "sha512-689HrwGw8Rbk1xtV9C4dY6TPJAvIYZbRbnKSAtfJ7tHqICFGoZ0PCWYjxfmerRyxBG0o3sbG3pe7N8vqPwIHuQ==", "signatures": [{"sig": "MEQCIAi2TNlfJdAsRou784A0anaJvpCyh3jIoJV2RfUZmXxHAiAgaRTm1xlZyjVK0yHelJTiYOLmgBYmjZz+0cOnbODziw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/main.js", "engines": {"node": ">=4.0.0"}, "gitHead": "14dbb9486191608c1dfe8b1a7b7acea6ec3ea423", "scripts": {"test": "mocha"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"rx": "2.3.24", "chalk": "0.5.1", "lodash": "^4.5.1", "date-fns": "^1.23.0", "commander": "2.6.0", "tree-kill": "^1.1.0", "spawn-command": "^0.0.2-1", "supports-color": "^3.2.3"}, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "releasor": "^1.2.1", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently-3.5.1.tgz_1511226260085_0.8456398623529822", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "concurrently", "version": "3.6.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.6.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "c25e34b156a9d5bd4f256a0d85f6192438ae481f", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.6.0.tgz", "fileCount": 22, "integrity": "sha512-6XiIYtYzmGEccNZFkih5JOH92jLA4ulZArAYy5j1uDSdrPLB3KzdE8GW7t2fHPcg9ry2+5LP9IEYzXzxw9lFdA==", "signatures": [{"sig": "MEYCIQDy9bGWB993Wg3sj/TR2Dxtvzpjzw2Fs7mfXTakmq+NZQIhAIsBlR2sZluPz8/Q2Rp8LYwZxmUmCoFFqx9s6R0o775A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 546702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKPljCRA9TVsSAnZWagAAlT4P/jZa4DgBoPim9Aze9MBG\ndPdZY4EJ6VIOIOjgPjvbavmEdF4lcJTaRwA/rdvZP1oGQhwepVjXw0AnYOag\nJiLvsZldUExa/BZY2JbwxNAg/Mqc/EH+t1ut2tb4nGbcxYrMCGpTKa8k1kFT\niboIjpSSbztAuJgPwCcxP7x8SZkOKxb4iS5lC6LSdbdjH/OWLGy52e0mMGfA\nNLWk7K6nLy1r03ync0j5OJVeMqHzlgPz4jio5J51AT7/mwc7BsEbynczOdK0\nrg5oC+9r8/MptZ/9IMOUbp0yiJpn0Ap9NXoU999DUwmP1aaJqpsz2QCEmj8n\nWtYxSSQnqXb8twF9aLifkPoyXhBlSB2sGPUDZIDQN3hWl4i5SvdUJVc0Vqk0\nhT91VdIyBdFJZ//B7KVfEumn68oxey+IbWHi0zahaKdo4S/furMXpdGWCqN8\nrLW+O0lKDZBLh4GekIHvrhBs7I60scGamSlI9Lvwyfeb42zAqn+/MyzOYKIZ\n4WCUxSzjhEFJU4MIBlFi0Fl2FMNmzuIht/a2VWLlZbzFSQ1NBuWWzhUe201O\n3o/38oSS1NObsVN0WqTosrhS7BB9cCi5emsJcChJmsXDBiIvWvquGorN9QMV\n9KgPm7OVCtP6VkGsx+uztGpNzQV7saqcQm9y+Ld/uHGii6/iV+vJG7PSUs0B\nAOra\r\n=UOp+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/main.js", "engines": {"node": ">=4.0.0"}, "gitHead": "b4574e1b76a86410ad2b7cb14ae8f0c6c3a314b3", "scripts": {"echo": "echo", "lint": "eslint .", "test": "mocha", "echo-test": "echo test", "echo-sound-beep": "echo beep", "echo-sound-boop": "echo boop"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "5.7.0", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "9.10.1", "dependencies": {"rx": "2.3.24", "chalk": "^2.4.1", "lodash": "^4.5.1", "date-fns": "^1.23.0", "read-pkg": "^3.0.0", "commander": "2.6.0", "tree-kill": "^1.1.0", "spawn-command": "^0.0.2-1", "supports-color": "^3.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^4.1.3", "eslint": "^4.19.1", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "releasor": "^1.2.1", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_3.6.0_1529411937852_0.5507228195843503", "host": "s3://npm-registry-packages"}}, "3.6.1": {"name": "concurrently", "version": "3.6.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@3.6.1", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrent": "./src/main.js", "concurrently": "./src/main.js"}, "dist": {"shasum": "2f95baec5c4051294dfbb55b57a3b98a3e2b45ec", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-3.6.1.tgz", "fileCount": 22, "integrity": "sha512-/+ugz+gwFSEfTGUxn0KHkY+19XPRTXR8+7oUK/HxgiN1n7FjeJmkrbSiXAJfyQ0zORgJYPaenmymwon51YXH9Q==", "signatures": [{"sig": "MEUCIEUZDUFXwagildVz5pdZitrH6sYEFuQ5ZOudIWg0gD9OAiEAo0Ni4R5N1A5YIl3z4LOtck6XwRLd59AZIk0FIgRPQdI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 547881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVypsCRA9TVsSAnZWagAAAg0P/1st+FqsnFb07EXzExcr\ndbyI+bX6NmVACpwXn9VfLzHVujI6GfcYWXYPWeQEpUDCVp/iqdMgSpWHIKS2\n/icYa9X2TxUyy27m09R2l78OU5m7qoby6nDploMWylR0CZHO8fQKiQ0PmLw1\nSxbeWjlj69lierInAJw+Q3/noM90ct4ZQcgOs5r6nYe9/y1Qd2YWAUff/ULq\nUeOxLmCR8U9lmSx+t1hqCee/Roc6G6lRvnwJ9ge/rKEa3bk02nCDHfU0a04Z\nU3QhquJdLTOD8i3NKNppWsEGkj7c3M5xT5T1/iA0CGP4bP7ikUceNIRbtgZB\n47tktvG4Pj7BVD5lfctLEMovofm/pxyzwIwwuueqRktNBGRgaXeKhic70RCL\ncXNoTD225gkA32w09tdZf1g/ox3njzOOEFkyP9YA2CCPWF1WrYRFsjvZA8d2\nfby2U/rLxSE4RhcbH6imkdNHrJWs8whGb9ot/VZm4JJGxWezLQQlou982tdm\n+k7wQjPAgQZZpsdCmt5QwNoRd3HavJvAaleoJWuNIvdLNIv9UJQ3Vk3pwMVt\ndhN7cek6yCFmXHxmcXHZgaWXy58pbpjlAYetMIZMQqg8MCPsygJZ8NKb6KQ9\n04BVICwnW973FShbKfpXxoi+kCLCqR87t5DzRo0/1Pxqt7f14PXXiARlBoSB\nlmEv\r\n=pZp0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/main.js", "engines": {"node": ">=4.0.0"}, "gitHead": "ba6f25a6c190b321de86e39c73b38d74ec403f01", "scripts": {"echo": "echo", "lint": "eslint .", "test": "mocha", "echo-test": "echo test", "echo-sound-beep": "echo beep", "echo-sound-boop": "echo boop"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "5.7.0", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "9.10.1", "dependencies": {"rx": "2.3.24", "chalk": "^2.4.1", "lodash": "^4.5.1", "date-fns": "^1.23.0", "read-pkg": "^3.0.0", "commander": "2.6.0", "tree-kill": "^1.1.0", "spawn-command": "^0.0.2-1", "supports-color": "^3.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^4.1.3", "eslint": "^4.19.1", "semver": "^4.2.0", "string": "^3.0.0", "shelljs": "^0.3.0", "mustache": "^1.0.0", "releasor": "^1.2.1", "shell-quote": "^1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_3.6.1_1532439148818_0.8246682172086934", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "concurrently", "version": "4.0.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@4.0.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "./bin/concurrently.js"}, "dist": {"shasum": "11f56878fe18ad30128e7827fd47a22d5e51fc62", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-4.0.0.tgz", "fileCount": 50, "integrity": "sha512-b0jRVBGJhotzlknTNUnW9921A9/MW6nnukpa9cOXQ1tmnj5WzdR0spGSAQbnjVfy89nRvv23omwqYNeWMgYyjQ==", "signatures": [{"sig": "MEQCID8EbvuE9H1ZeQytTndpBZO3g0xtbvOj81LYv6ffpe4EAiAlE7vcnTSa8tJO+XH1p+R9qEbQZBouAzRpSeVlyI9iTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 578503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgGeqCRA9TVsSAnZWagAAv7AP/RTqUbbQAWfd68hZ8WWZ\nZ/Lc1Iyg2KNSeSlGm7iThG0OuS7QBZtrV1PMI+3CYZ2+JsoKX0FzJ9s4K33W\nH3B7OTIoGLg0RoGaRgjakANVkldw+SVM6WXHLxGVPKBQogAp2eAmXUsGBHSa\n8YcRTxGhAg6bnoSFkUGl/jGzultUIfitnPsmITKIsAEIjlslRgWA0WNUDG7p\nAPLbDTid1LSZzqORvAZ2mrUjF0c3qM9Srf4cn+29pg7TMds35/dj5JQpv8iz\nAn+l+dOFEs4kp+Isr6Gj8YXX0aBYSsrOfazQSM6ZOydwmd8I0I1HEXyosixC\nrvIXwkFo1VejCf2hQLJXXWxuQlO8vbR0J+/usBInxP2hrxkJC1KS/p7ytrKU\n82FvrA3G/XXINlNkTmWvhej1mRDP1JTPgUr0OXkwyUlB7n6bVTEjO8DpiHnd\nLR0Qp4BOUPdBe3RR3Cbfgd7AT3+fv5xsevSsxdO1O1CVUVr8y5HQaUWqsOWv\n7sETAt1GqXcT5ORoKmMCFPkyCsFqjtrMt7QJcCjMizFI01+8G/3tjvuCsGuv\nTYWZmwbfm+cyg44cxAhj+IsOswaWx6+0fgIYZAithiFgpCoaV9rrRj5YEPLK\nZMRkKUJdm2ingg364OECCOhYBLM0129OaeFIxi+i/dnJSdl9unRxqGlFG2Ae\ncG1U\r\n=2w/4\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "3690676886510e37ede19d1ce1494f0064d898b1", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"rxjs": "6.2.2", "chalk": "^2.4.1", "yargs": "^12.0.1", "lodash": "^4.17.10", "date-fns": "^1.23.0", "read-pkg": "^4.0.1", "tree-kill": "^1.1.0", "spawn-command": "^0.0.2-1", "supports-color": "^4.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.5.0", "eslint": "^5.4.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_4.0.0_1535141801645_0.5361323447451778", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "concurrently", "version": "4.0.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@4.0.1", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "./bin/concurrently.js"}, "dist": {"shasum": "f6310fbadf2f476dd95df952edb5c0ab789f672c", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-4.0.1.tgz", "fileCount": 50, "integrity": "sha512-D8<PERSON>+mlI/bfvrA57SeKOht6sEpb01dKk+8Yee4fbnkk1Ue8r3S+JXoEdFZIpzQlXJGtnxo47Wvvg/kG4ba3U6Q==", "signatures": [{"sig": "MEUCIAbHczz8bql1VVf2SxUAmDOtJJrq7NIQQAhf3OfxLVKNAiEA+S3wuJttSc3MJ1GARWaBIkmAWRb0M5W/gKAnB0gY98U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 578710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgsbwCRA9TVsSAnZWagAA71sQAIus6LGke3JEo+Z1unDI\nRKu0PpC0W8r1lrrNRBKeJ8W4a02iq3ZqjflpIG4DVzD14FsBdDtdOXTpzC24\nyvPOyIZi+K1cel8T9RY8WdCwDwRIEpFEsrJxDAQpOsqlEz60rPFRVsZSZm8X\nsL9rrz7avy8eUfTmxnoWXkyEAp3BEqGnI12OUdDM+hOQq2ZY7G1nVRjaO4QA\nfvhWEwkYm2DDSWB/TwLBPb3EHev3suGYvx2Gi/jCZ3AUzNkX6I9D77qa7OTG\nem31ILUdPaGfyjMsnQhUB+ZYp8qePYBBYJv1ebDhRzlJE6eI1twP0anH5+ED\nWGlMnVRwsHQVRyPEhGkRIL/O6Kt/n6uSAq1A7YXnNma6LCt2M6QslLJDqizG\nxA4Il/9KZez6SUNmTMVea9azxDbS8l2oYM56ygWYWa9T0dGq1bLPjbmZ73BJ\nEnWTQEMKoOATQMH6nLx8Wna9wpQiohwpjvelp690OMuEXQ1PcQr2A98jE/Xr\nTBjO2C9us9uGYvkqLK6Dh0mic/nwo/M1x2ZydtV74mIaTnIS6E42i20KoQlS\nFAbZhjgBCTRRdNjIceHtZV1xNwpdtq683Lk4rzhdnBbEWB4vxliENK47XzNr\njkj9Iur7FvnoylnuFpjJNFH0ecdF78QbwBWwLrfkvcHmmnrPXNGRccpN6RpT\nGp38\r\n=JIo7\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "b4c08944f3c9f44463492cc86b54870fab390546", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"rxjs": "6.2.2", "chalk": "^2.4.1", "yargs": "^12.0.1", "lodash": "^4.17.10", "date-fns": "^1.23.0", "read-pkg": "^4.0.1", "tree-kill": "^1.1.0", "spawn-command": "^0.0.2-1", "supports-color": "^4.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.5.0", "eslint": "^5.4.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_4.0.1_1535297263292_0.878536154255867", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "concurrently", "version": "4.1.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@4.1.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "./bin/concurrently.js"}, "dist": {"shasum": "17fdf067da71210685d9ea554423ef239da30d33", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-4.1.0.tgz", "fileCount": 50, "integrity": "sha512-pwzXCE7qtOB346LyO9eFWpkFJVO3JQZ/qU/feGeaAHiX1M3Rw3zgXKc5cZ8vSH5DGygkjzLFDzA/pwoQDkRNGg==", "signatures": [{"sig": "MEQCIGsR0KXHvsP2YlDy4lqwZ6RQ8SkR049grNmgbDWHzQLeAiA2oAbmSOEzgP9LdN9MFS/CLaDukZatNyf/UlSt5GnYYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 580756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb8fo6CRA9TVsSAnZWagAAs7MP/jY4YtQrw9ziOVW2Nbfb\nlRg2yN5zt1PVFPZIb03JFiDYV+458WdFe5YAE0X6PSAtOTCKYVyA8VvQiq4h\nyu0GRQPxdkTQdCzL5Qnz6widTxtk67rz9uL7wId1TRv9x1AjWZaUFtMcB5mj\nEqCghg95y6pF+JEvO58YVKa6y7uPuw6LNWYceV4rL1woz5AfiSNvuQ/PQmxv\nLLOPMCLY/yVLZuq7DZI17tX0cVbv9NoAzBZmQ5GE7uPfXKpHjdlcRcvXPOiv\n33BRa/Y4lC+V+9dN+cGJ8FbG67CRozf2yjjXvkQDyyPXn2zsOsDmOHRbrePk\npe49pePwykqNu2x/nQbVVauUN1bIOE6aTn41cFJSQbPqHTO0FPKjwX6qNMYh\nYpIGL4wt7quLTUtBK8PfuelJ+CmImEB20s0BflH7TXzLnSgFNGIQXKcpXRDL\nIEAaffDx/HFoxUb8XuW6cvTrAXAohgiLvA/SNcHF3T0Y2xWIHQZBRZN51VKi\niMwn7sLkOvUHGYbhE7MQIwQ/ggCNAL69hc+1e10ft6tBgr2KukmCMbgGORJ2\nXFHO8HgIkIv0BFZNdj5I+06D7NBZM/W2ImIZtbJ5Nm5AXPWsSrG6Obb8GDz/\noAlsh0dQzivW2tAZlcYZjo6KkpIMiEJMDKYig3qVGRCie5HwwVpmLFTqelGM\nuMiM\r\n=BcLx\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "313de5305f74f99c8c1215860473890ae1f1cc28", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"rxjs": "^6.3.3", "chalk": "^2.4.1", "yargs": "^12.0.1", "lodash": "^4.17.10", "date-fns": "^1.23.0", "read-pkg": "^4.0.1", "tree-kill": "^1.1.0", "spawn-command": "^0.0.2-1", "supports-color": "^4.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.5.0", "eslint": "^5.4.0", "coveralls": "^3.0.2", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_4.1.0_1542584889584_0.5185890166711771", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "concurrently", "version": "4.1.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@4.1.1", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "./bin/concurrently.js"}, "dist": {"shasum": "42cf84d625163f3f5b2e2262568211ad76e1dbe8", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-4.1.1.tgz", "fileCount": 50, "integrity": "sha512-48+FE5RJ0qc8azwKv4keVQWlni1hZeSjcWr8shBelOBtBHcKj1aJFM9lHRiSc1x7lq416pkvsqfBMhSRja+Lhw==", "signatures": [{"sig": "MEYCIQD+5Kwv68rhdAMRQ8JvPOjBTNX2+28AQmaZUo6qBByRdwIhAJwCG4Kk35DDABZjAWepDCQEnKsyfx6Ev4gCyGNTmXcV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 581627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEoh0CRA9TVsSAnZWagAANX0P/2O1p3FB4eLt+t8rQuAY\nC21FAO8gbnElu8r4sBpNZJ06F4kA+lI8DsVpM8WwXDLG0uEXV/mKGREmaHhp\nOYfzlbXzFeasLtTxpCKTZQpUSBXs+8Oc51vy4xRSfx8Ja0Nin6zmCXK9XLQr\nnqCRAf+FpAtyDUOg5i8Kn+GNpDzJdNR9Soc4YWjOpdwkNunOQF9xvNfsmMOr\n+mhioiRSOOH63eFGMm+buv+xc6IFRyDC6eL/zWsP8ooLOvHki2VHu2Vsm4er\nG7GmHvT2KmjJUGTkNAl0qPIoueB1k2GTzqe4Lcre1TigkHu7wl3SqKdBaRUs\nYOj7xXejzzx+nSAoFBv/8n5UtSv1nPTPDRnUqtB2UnlOG8bUv+h47tMQpzEJ\nkC+gScthk+gFmXGfO1bT3gZQTdjJuu4pXhAxLMj6kVsx3/MaE4hQ3UIUR74Z\n90vbU3l4Z/LS8CHxtGfF3o/Eq2PuEWsb9YoT0EFrSnhdBo8AJlPkJI+K+OYq\nKbGpSIo2W+FnpVreEdj1cCmyA1dHsBkww6CpNR7PaMo1VQasepEMkOywmPkd\nQAE0Bu0APt5DB2MH5YgVNmtdpg0ZD3vWAqkc27/EeBPSUaFZftSeERKBG/MB\nzFLHIyAK2B4rARItjQbCWENwT5FqIIWOKHflnVL3HC8xT7BiRFIN+fEUsi5d\nGK2p\r\n=pCBb\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "918196510ad41e6edcf86dd5fab4c5050f467a5a", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"rxjs": "^6.3.3", "chalk": "^2.4.1", "yargs": "^12.0.1", "lodash": "^4.17.10", "date-fns": "^1.23.0", "read-pkg": "^4.0.1", "tree-kill": "^1.1.0", "spawn-command": "^0.0.2-1", "supports-color": "^4.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.5.0", "eslint": "^5.4.0", "coveralls": "^3.0.2", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_4.1.1_1561495668060_0.7717203367583803", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "concurrently", "version": "4.1.2", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@4.1.2", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "./bin/concurrently.js"}, "dist": {"shasum": "1a683b2b5c41e9ed324c9002b9f6e4c6e1f3b6d7", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-4.1.2.tgz", "fileCount": 50, "integrity": "sha512-Kim9SFrNr2jd8/0yNYqDTFALzUX1tvimmwFWxmp/D4mRI+kbqIIwE2RkBDrxS2ic25O1UgQMI5AtBqdtX3ynYg==", "signatures": [{"sig": "MEYCIQCNDTzxOTjwX4jPEuwO/xGFT9Ah2kMtp/WVopsDwBt78gIhAMwPMoIWdif5B/r0gavp4Yze3XDegdm/7Fc8kphUvqWU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 582230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdV0H5CRA9TVsSAnZWagAA/XEP/0QuutGzVnB0smjYajOc\nGoJ2hNWhIGZwK+wLANye25EmT7VyIyHcR2CKNJE80fyec691kj5xzroY2Eiy\nXjPTbEArKfTxgeeihHc43IUw7CvdQ39dRcdFvZ+P9MY14hd4/ebknP3hqgvS\nNasCUHQJqvUnKjJMmHU6T16UIcSVSIAyVOZR9ZO/WjNFrXFokQc5YeIDwpt1\nO2l+D2mfYgrR4/qfSEfiHcc4fdt+H+VbmGvHtarQ7l28QNcfXsDtJI+5LZno\n3G63fspkjQE7Uis1kgjABsNUwRkAtKT8HOv2s5c/JV63MW1qW0w6xAajyWRX\nxF5pxdWWjpdKyzyzwd4bxCZdDkJgg1jmscPJPXIhF39bXguKG2MJPuW9Cc6m\nSdYFajHjGbrHoY4oZ6KvOsabhe0fpIGukscAY07DUxE3L3zAKxRs0OQbUstf\ngjwvEc8vl5RONmBE5jVa5kjXDkDquJZuj6JoItiauu1orI208xU40S12HGll\nd+ooEISu1ZF60XAmK5P1G0N7kbL5Rb/2Grhv7ODbxgdjD8j4EGYktb5DVBJ/\nYic+kZ6lwCpztQYAGn1QzGlviev0mKzwJ3MX50Q3EfTGgDzUu6hJGWuEOPkt\nJMkvwdzJtUTlZnLcXxhaTvmzrQBRTGJvpmSXUcFkJzLLJW3WHufbwWMDt00D\nGAQn\r\n=UMxu\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "e9c76bc04fc82b0d27a391f52b5471db348fec23", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.10.1", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"rxjs": "^6.5.2", "chalk": "^2.4.2", "yargs": "^12.0.5", "lodash": "^4.17.15", "date-fns": "^1.30.1", "read-pkg": "^4.0.1", "tree-kill": "^1.2.1", "spawn-command": "^0.0.2-1", "supports-color": "^4.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^5.16.0", "coveralls": "^3.0.4", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_4.1.2_1565999608456_0.48718672470615254", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "concurrently", "version": "5.0.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@5.0.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "./bin/concurrently.js"}, "dist": {"shasum": "99c7567d009411fbdc98299d553c4b99a978612c", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-5.0.0.tgz", "fileCount": 50, "integrity": "sha512-1yDvK8mduTIdxIxV9C60KoiOySUl/lfekpdbI+U5GXaPrgdffEavFa9QZB3vh68oWOpbCC+TuvxXV9YRPMvUrA==", "signatures": [{"sig": "MEUCIGL2fmdoWYlLkIWEdnW+2g6p2a0BZGFjCVz4IgVuRdJdAiEAmsKvgXwxWtwYdh5RF4sHkisuRh40AQbJkcf2vAx7/RI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 582542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmqRzCRA9TVsSAnZWagAAYe4P/2MdNtcv0vpPo2Egoc+6\n73rbv5F+htynoTE7Vw6SCzG7OdpKTMGoocfIiCkx9MZ88JJD+5nE0gmL9v3N\nJpbSu9JmUf1qik8R7wLqR65sVoxek3MyMjmXUV2nOxOZuGD+P5Po2kfFgbDD\nFTRR2Kw2Psyr4FdYKIn/D/HXUZ4NnBhDmw41X24VnRWBQSG0b70PWWxzHzru\n16X8Gx/spWx2QmrWSIlnIkDemhQPiDEPlX4rryDqavK7V+RWiSgIocGyAThB\n3OjHT20mxE4mSw9to0qSY7leA7B6QvrwD6h9lz4pPYEPSH+MVTzKUOKBoSIv\n8ilHL0KIbnrgXOGhRW+eMU3MtEctvZX6PC+jWpC8sZfLO4iJ2iVph2GYgABz\n66gsclM3jc8UwncHFaYYnTvrJudvRLTVVCJ3N6+GYc2X6KUVnOAqDeldzCWb\njEeT8QrUXHdR2sbNEoRN15OtkBxjHoH4NEJLCpI5nJq+yTxHS8PzTGY/q1Uh\n8sWUOZ5Ul8QIb3ZHNgYjhjwE/NBNFawo8yDk7+opwVqYUCsmTslQHArVL8aI\nPt03XUO1zpLkqz+/Z3lK0DoVl01rElzAhS8IZD+oGveqYYBXix4qvlj49T0I\njanMq3zS14KgnZ70YgS/5qHqeI6OlCGoGna7VNG4iQS0FKVK60b9MmEqc5xQ\nJ3rJ\r\n=+P5Q\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "2c4e1b7439edf573cfd60a079d0fde2c48ccd789", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"rxjs": "^6.5.2", "chalk": "^2.4.2", "yargs": "^12.0.5", "lodash": "^4.17.15", "date-fns": "^2.0.1", "read-pkg": "^4.0.1", "tree-kill": "^1.2.1", "spawn-command": "^0.0.2-1", "supports-color": "^4.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^5.16.0", "coveralls": "^3.0.4", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_5.0.0_1570415731136_0.16413639972102545", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "concurrently", "version": "5.0.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@5.0.1", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "./bin/concurrently.js"}, "dist": {"shasum": "9d15e0e7bb7ebe5c3bcd86deb8393501c35dd003", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-5.0.1.tgz", "fileCount": 23, "integrity": "sha512-fPKUlOAXEXpktp3z7RqIvzTSCowfDo8oQbdKoGKGZVm+G2hGFbIIAFm4qwWcGl/sIHmpMSgPqeCbjld3kdPXvA==", "signatures": [{"sig": "MEYCIQCfL9Z6qVRk3N0nW/fGDONG+JzPd1sr14zfiYLr00fDAwIhAJrVD03odw9pXY9v3FMcPH4IK6L4zjJhHZuqURW6A78U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7ryMCRA9TVsSAnZWagAAH2MP+QHqmyVugpAacZrv8nfN\nkgysidIttE7zBrVNJOhT2wgYhjd0GW6IPj5hzNRDLmmLYnRB2fqF2Nynwzgy\nOftKxygEwQVEdsl2TL2yT3KkMH7OAxHRo6WCbqokGFVSyTsj5UmC9ka8xsCa\nYpe9nmg2LD/KmbiR5beh4p7j7PKI3anew239DTRgGiSpns0QpNckzTSiIraz\nJ0bb7ca9j3f5ThC3rvM7X6F3o8+9RqoHxLAOZdYSbekj3qImkU9vEjtKjA2Y\n0IcA9XPjiJ6fKnCPskx+uKk6w0nKq7ofIhOeU5S1nQj3DWFKWQ2Wp9SKjSR8\nOYZV8J4jc4sdA34rNPOIewygxSnHzWES/61rJugOTRafmbdhX9QAMe+3CUU7\n2CjH4OlRi1TTRhJPSlAIo4oXOc1NZTier7bJYd21kzj088xd/72IzADMDcUo\nxNcKGn5yClGIsIS7RB+3qOVrHOu8qQSt3281EcW99LduBhFLGxzARqVqAPsg\nuuVseBfZ+VcUoxPmYgPXVpONgCmKmI5PjHa5FcWMjzUCiPaNFYixGNDVnnoy\n/tDlCOK+Y616iYveusj3F27WlIkuXzlRDpIpxNv2K75T+kGZX34NwEphGTUl\nEfIsPISJ/wAf6J882MuM03Q8EP6XT0oVpqqttAjeXr5YAHEsDnkClBZIlD1z\nOi9T\r\n=8Zdd\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "f9fd69ffdda1825e2af65ac49be1003b5c565c59", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"rxjs": "^6.5.2", "chalk": "^2.4.2", "yargs": "^13.3.0", "lodash": "^4.17.15", "date-fns": "^2.0.1", "read-pkg": "^4.0.1", "tree-kill": "^1.2.1", "spawn-command": "^0.0.2-1", "supports-color": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^5.16.0", "coveralls": "^3.0.4", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_5.0.1_1575926923745_0.5089976469384645", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "concurrently", "version": "5.0.2", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@5.0.2", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "./bin/concurrently.js"}, "dist": {"shasum": "4d2911018c0f15ddec34a8e668fc48dced7f3b1e", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-5.0.2.tgz", "fileCount": 23, "integrity": "sha512-iUNVI6PzKO0RVXV9pHWM0khvEbELxf3XLIoChaV6hHyoIaJuxQWZiOwlNysnJX5khsfvIK66+OJqRdbYrdsR1g==", "signatures": [{"sig": "MEYCIQCJNvBzTd9ESBYy6Vt3D7I+jsXeyf/SygyxRkyDy/vefgIhAKwapSh9VALuJEVvgl+KtcO+ptMrgCEy9Ad54mMuWKbh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+Y72CRA9TVsSAnZWagAAxRYP/RXehjMsTThckVBXdbwa\nyY7F4i+NlCbXkNClQ5T0QXbkGFG3388b0PWX8XBY5un1wXfKb1jTavf4+VwJ\nkK3lKhw0Ko3SXttWALVzL+FLk/FJ39ZcgYWOLweXPp+uLn5aKiht2t+K62dV\n4To2bihIVPJfdp0LCJPxgMHbZUIUOiMSpYxS34l9VyaSyfWV+nUxe6sTkOQ8\nLEAURrMvdY4M4bXcjf9jk37oXE/T2n4Dv06Rb6FgFZ65HcrjMOilTZK1wqNj\nsOSUyFGkwSVn3KTij4lHMCeSX86byIeBiAPnuc2cm7QIn1Y54/syOZd5uaE8\nuCrnOs/JzhLsyAy5SotiuOHDshPSI4vMvAD/EOcgHm/gEVVR95dfGzLuhGi+\nHJIPhxz9skdpFKS7JK1EPvNZlhoWSxEIBYwBcUpIIkLBom8ZerSX+sT3SPrY\nO+SiA5onLpJGVznk5Eis0KESzHRGrEFYT/9oBT81COuSCKueDYi0ueMSp8IF\nVxyVN+PIIXzOe6bk6YX+sHwWbzvIA2gkUbX7PB8fjbOU1YV+a2CR/6ODazH2\nHXASUNh53I/dKdpDtEfDWt4EKdBye43DXerSSKeCOik6RnrIppB4PW18iPjZ\neXbpSDTAmc8NzU9Ct1mrWE6gqAk+Ej4FZM7VAtu3DRr6kh0n5dK+bC8wgs2p\ntCkt\r\n=G/6E\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "84ba2de5dfe64f813c8e8d49e7301f191f09f15a", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"rxjs": "^6.5.2", "chalk": "^2.4.2", "yargs": "^13.3.0", "lodash": "^4.17.15", "date-fns": "^2.0.1", "read-pkg": "^4.0.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^5.16.0", "coveralls": "^3.0.4", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_5.0.2_1576636149689_0.8469110905307626", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "concurrently", "version": "5.1.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@5.1.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "./bin/concurrently.js"}, "dist": {"shasum": "05523986ba7aaf4b58a49ddd658fab88fa783132", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-5.1.0.tgz", "fileCount": 23, "integrity": "sha512-9ViZMu3OOCID3rBgU31mjBftro2chOop0G2u1olq1OuwRBVRw/GxHTg80TVJBUTJfoswMmEUeuOg1g1yu1X2dA==", "signatures": [{"sig": "MEUCICX7HzKHuRK0ZdnhRaW45mxG2qL8svX7lLsk9mEON+37AiEAoAASEg+QGBqpRY82HeXmBDE7WlRX1b2zMIOg8vS2mfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLpbgCRA9TVsSAnZWagAAEfAP/1EBpDnMLBW14PlOutsq\nYdMoVuIEUb1q9G+UibZKR0RcF8nV+6bdPjxvQo8zepugId55aVrkn8e0vR5v\nZEmWTiKNdCMC82YjRJvh+wTntx1oe0Uhou7bcy5Koy3j5JeUN/HROqI3JEsE\nDGzHeFOuFpWIsoP3qdNTDCS8kFO2HsY1+c4WiF0lxwDmzHgl85oJmSLG+joM\nqP/Qvuzf3K2PWxYzHvNWqaU87WilyB98b4EdqEoOyq/MP4CoGLM6EA0vplBp\nZauXPQqDtMS+eFLUE1rn1mcHioZEVvuXbrHtrxGwcmDDkgBSus0zCtBOspFr\nDQtEC+UIEOT9TjQJiQmb67MfgAvCv9MwsgqoQyS2pGaElhKjV8SUPv5jwsyu\neeMyVFCqU916irtPDuRf2Rg4cE/8AG/rwlWTQ3j/hGBU8wYQrp0k9KLvc/PW\nRO0z/IwW5qhqZXpmsVwsfYPpP0/AKd0EzLv5I56xNYkF0cmABxgJbAoVQxEQ\nqqEJpRpvL6F2fuz+wIHlGfyOT86BNqc+b20pK4vPqhNDtAG8oJweGlsRRMkC\nyKggaF0og8pAtKGTPwalMYVgrw7cDprrnymYO0RWnvxnsYcCtGYLJS2nvXA+\nnve2OGCMzdfaQgI6Vd0Y4OO34+F/8IqXrKcdXBR14NLfaGH+ng5nKVPnaCjN\nd0cQ\r\n=ZvqV\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "9a14ada8761209fc18c375a917e98ae475ddfdc4", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"rxjs": "^6.5.2", "chalk": "^2.4.2", "yargs": "^13.3.0", "lodash": "^4.17.15", "date-fns": "^2.0.1", "read-pkg": "^4.0.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^5.16.0", "coveralls": "^3.0.4", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_5.1.0_1580111584167_0.4606303942598462", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "concurrently", "version": "5.2.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@5.2.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "ead55121d08a0fc817085584c123cedec2e08975", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-5.2.0.tgz", "fileCount": 23, "integrity": "sha512-XxcDbQ4/43d6CxR7+iV8IZXhur4KbmEJk1CetVMUqCy34z9l0DkszbY+/9wvmSnToTej0SYomc2WSRH+L0zVJw==", "signatures": [{"sig": "MEYCIQC5jaI7I5z5eb+4n1GjYHfzOZYjj40gA/TtNNs8D3fAaAIhAMOjACjlZwOvdPQs+N/vidLiQeLaJX3sy7emZTdaOB3T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeo7dtCRA9TVsSAnZWagAAUlEP+wQ3zNqA+YAsJvsUltam\nn7W7WT84sqFxT1Q66CqMGBlsB8lS/axA60tSTlKX8QpzCmy/waXqpRE+ATPW\n0IYtWIAgPCTP8saPvj+bc1gFdueFd6EX2l2AbwkYS+ydFb3/Kg6PgNa0hRbW\nFaH8YIJAMqE0tQx3SLaJETl4k50YuFOCDSEAmrFHb46LblFFnVc48Tl4hFoJ\nUDJZouUItapJ60evKZCX87Sl4DCqO5YZ+JV6yf4NqBA7TOLmESlqxDdterQ3\n2jgJcN2GWRex/tcXroQlseQ8VgnN9ONKjWrtqqujP9b8iC2FArbDdOK5Na3l\nbLWHWcHll23axyPc8rixUX3SePio+l2+0eyA5nur61vQzaKEJeb4Jlz6E6XL\nttIS/rSYu2p3IgCohqHXVDE3QOsa9XnnB5kQnxFRgTONCij+zSJXyiscxg6H\nox2MMtuJKEzvCmrnUEAHgZn/uo50iWn5Ay7k6FKHaFo8KYk0L9p1FcYPwXHL\ngVHQwJB/ZO4ZNzUmJcdB8iFT0nW9C5bUeX6QufhgrZxr9ysZDhrBcdLlcrbt\nViaVO40OKHPslZ9Mv1i5o7kpx6T0v9r/cjUHgLU84F3xbG/AOAqF25hKrzN2\n3aK3R9VGUwDLxFBOOBsB+ebpCXZpKGSHWZ8S7ycEb6vuHC9xlzp66eAlGdq1\n+Kip\r\n=SeK4\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "54b6456eb5054ec32de9d81717b143d2ca8f8f6d", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"rxjs": "^6.5.2", "chalk": "^2.4.2", "yargs": "^13.3.0", "lodash": "^4.17.15", "date-fns": "^2.0.1", "read-pkg": "^4.0.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^5.16.0", "coveralls": "^3.0.4", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_5.2.0_1587787629060_0.7102524213582462", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "concurrently", "version": "5.3.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@5.3.0", "maintainers": [{"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "7500de6410d043c912b2da27de3202cb489b1e7b", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-5.3.0.tgz", "fileCount": 23, "integrity": "sha512-8MhqOB6PWlBfA2vJ8a0bSFKATOdWlHiQlk11IfmQBPaHVP8oP2gsh2MObE6UR3hqDHqvaIvLTyceNW6obVuFHQ==", "signatures": [{"sig": "MEUCIQCt3mGStZNpszwfa4PGgFNviNh9yKWewlH2pHn6HxcRhQIgToJkQAYqD1zlQ6x+wBllZi3QJMAfC0wRrtGJw0cKTBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLVwzCRA9TVsSAnZWagAA2fwP/Ayx5Z2e1LpiQkmjbrHt\nYE++1hrPn8+BoxAxurouAAh8FvFTGENBPArYLGPI/wphNDN/eK0j+w+8miH2\nf72qoxXjzC3J6lpYGXADhzUBd/taNlr+LsyEHjRKv4yIiZZD8tnDSWxRBYtj\n0UliFXmlkiSZ707ie7G/E48940fhRk3bZ7ZN/9UYUwhQPQjeGU2csB9Ag+sn\nakUwrXFuN33UFHLFsl76yD2Qd0Q4AeOeAJ+UhGLJEpGAPYlfKDrKLbM7Ekw3\nMnZHcPPIKWmIDpJsGA03yorIbSsIajWKoI4kTVD8lf6E3dcwVrfFmnE37aKY\n5+KUl142HoIWPadvRz2BWW5R8N4jSTnnuXtbDmK3fq21eH8uiTg/kC7zwexD\n3itTq19BYu95+B/bu7WKUWN23dXqFDcuSPSypRTtVs+EGazWIya0HVL8o4Lk\nyWKk/c4irlmzqGJnROv8gzVQgWJhQJWFJwYATrJtWbEOH/QQdjkP+aoUYcGv\nBs+CLqAPvYlbNpJFRmHJBXewneINB6gvYP7OOJ0S68dK23WtZzB3EwzuUL5E\nwWOwIxNRUcESliFdxkVjTDITe0M8u+paNgJupTAHnGaqEJUDr+4m3B/+dwrR\nFl1DP1D2wdcQzll4L9h+jiZ3Nd66pljMTFOhT6zVQmJFeaGMedvVWANsdNhy\n0mMO\r\n=THau\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "943219cd4ebbd9773fe5c883cd56a4893a0d1973", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"rxjs": "^6.5.2", "chalk": "^2.4.2", "yargs": "^13.3.0", "lodash": "^4.17.15", "date-fns": "^2.0.1", "read-pkg": "^4.0.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "eslint": "^5.16.0", "coveralls": "^3.0.4", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_5.3.0_1596808242893_0.06461526177497645", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "concurrently", "version": "6.0.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "c1a876dd99390979c71f8c6fe6796882f3a13199", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.0.0.tgz", "fileCount": 23, "integrity": "sha512-Ik9Igqnef2ONLjN2o/OVx1Ow5tymVvvEwQeYCQdD/oV+CN9oWhxLk7ibcBdOtv0UzBqHCEKRwbKceYoTK8t3fQ==", "signatures": [{"sig": "MEYCIQCJcUm9Lm8loKUiqZwAJs4Dvhzetrqr4P5/jijUXqHZLQIhANEZDjW//9w0udTJhApQEnmdirspzLSIvo/aMjfNe8aB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMOYLCRA9TVsSAnZWagAAfBAP/2J97T22JR/mmjAPNmcw\nb7xyPVIBJdg/M5IhHvghDw9cRHp8RbTzvj1c1u0VSGQ8KW9qAU1oR//F60vB\ns4cPV55VD1ibAJf7T04GUPtv5ci82OSEmq5xb37M9NLqtf7SoFLOTPE3yseg\njr879XMSV8xwIzGsBbRMgbV+jdpAclJGVXMmI5UZ4TjBWNgdbHfWvh239cpb\nwN7AIm2A5TWCyuyepe6bwTj3dJSJdVzlqtBg5td82v5L7l1EPaqPdHwkTrFu\n1LaZlCEU/GVqX2vhP3+cByd2BqPKqv+td5EydKtQuBeQvh7+vA1/U63gIH+B\nvDbLul6lIOhVHWKofShth0FDNDvy6U+enc5e6w6M6BWNJTVN0Jyc6YLwk6Mp\nbw9oVTCPE2OpRAsAmjvyPeFUXJo/SjoTo+8CLCyBupIBk23lf9SqUIADmE9Y\nYfiVsyoLzOGT489wig+Bp6kf8i1JsD/3rKA9UQ8kZa3OrQH2QEdnTdeDz1GN\n9sTTf39HvnD5PfWXW+icsh5jy3IWUpMwksMCg2uR5MpltBH1y+Gg8cgJfbLr\n3ftc1p5Iccr4aJS2u0LmILZpJjHmUlLscmlzfKdPapMixwu0KBi4sa/NS8Mu\nT147rxAjCce4+dSV3j9s6FOYusVD5/HfKxtKxQnnVfGtVaLE3prNXab/xZ/r\nk7bf\r\n=sNhW\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "343d1abe814908c3fe551ed994c152b226deee7d", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.20", "date-fns": "^2.16.1", "read-pkg": "^5.2.0", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.0.0_1613817355062_0.42207921867524467", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "concurrently", "version": "6.0.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "b472efd9398bd9f5b117e22f72c3e50bf0a8a651", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.0.1.tgz", "fileCount": 23, "integrity": "sha512-YCF/Wf31a910hXu7eGN9/SyHKD/usw3Shw4IPYuqIsxxC39v92engYlIlOs/zXnBJtX/6aVuhgzfhZeGJkhU4w==", "signatures": [{"sig": "MEUCIQDPXWSQpVGVOOjNurY34LX0QFCwfvxulm0heB/9hbWLqwIgfrz9O6mJLYXdAF80QL6ruaSSdt1nfSqGYJrjnYBt1cU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJga48sCRA9TVsSAnZWagAAiKYP/37z0ER9jv45YkOR77BY\n/RepgCdcQPE5U+caN45/RrlQ9sEgmAxPY5Y4aGSJ539h+dmN1w6pgvJ0rM3n\nuSA3dkFVacOY5Yh4k8hOO1vECVfDSgiB6euiaGw5BdCcSEJqPBM5vPkYyuOM\n+8E79LAtVtXEOYkeZ/ObImjxxop472Yk4EA/Zb5QiaRglVFvIkwyKe9S795K\nlgA2F3co+u+pWJq0vCNA6d6JCx03rOiP3rj0vzJbSa8lq1aU93Zr2vS8mPY3\npwVUTrZjplMePqqhcuTIPAh1b0ZGSzOXJydJ9SIcZFz96fCMYNpJiYfIA8gK\nhQJ3l717lPBwTB29MCOYwTEf3oySfv/iP9tR+st+jhoai22W8t9yCvbANX61\nC+vZwzJIzRQqdjdqKzt1r+nrSvWGxznbGfcAR0jYU4EzbFNZDfZ7/C5jFAcw\nqicqZcJ9bKhGmchdE6sI4ITgvhEqS78ebBRnwdqY8bmVroXEjj/xgV9V6VMy\nmejUtiAX8YHBYm1EuqYLQrAWj6TvlJiAYo40BvbiVxU7aXygz9r3nZ10BgOw\n1aJGN+u4yAYmsUNjJCs/R67MoB/PcpNVd5eM5QmBudZxo4us+4VaaFLzcn8L\n7SvJiKi+1uJMd2auo2maqxTGALUwFnV7b5RPCXhe2BNsuFhBpvm7aGPYv+dD\ndrtq\r\n=PD4Y\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "e36e8c18c20a72e4745e481498f21ca00e29a0e7", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.20", "date-fns": "^2.16.1", "read-pkg": "^5.2.0", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.0.1_1617661740161_0.06741555085691364", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "concurrently", "version": "6.0.2", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "4ecdfc78a72a6f626a3a5d3c2a7a81962f3663e3", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.0.2.tgz", "fileCount": 23, "integrity": "sha512-u+1Q0dJG5BidgUTpz9CU16yoHTt/oApFDQ3mbvHwSDgMjU7aGqy0q8ZQyaZyaNxdwRKTD872Ux3Twc6//sWA+Q==", "signatures": [{"sig": "MEUCIQDJ+Pxw3DGKGbtvIblzxcvhz0XJonujMG/1qFxZl/nyZwIgAdqkyLZRR1jdKa4/TQbqFX0Df0aSWtOEmqFcQkApYP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdNBVCRA9TVsSAnZWagAAweEP/1SZabH+DWHTocMmZ5aY\nAxW3BHkE1eHrZGe8b+9W+bEPBiStD7tRHuVa+/XhkUN6N+aLFgbuCZdKNQva\nw2dCjItx1iLx1f5T3SJxyOUbPU4Kev2l9GZFU0RJBV+8Xcahyry3K5esrT8F\nfBHMWXukM6wLpOUcM4uIN0dBuODqKLxuxT/Ui3vZ0L9WH4+QNzqKb8v/TkxN\nZ9ZwtszAU/snsWeUBYKxX7KCZ4d1caTKEExr1zHJql/c7en5BtTIxOZ2uhMi\nmg+waUYzsDPS+5bXypcHxXjizbKmtxNrBwX3TA3MCj4eQQ2Nlm0wOmaWfC/K\nTrcMPPZ4lzOGzL4cETlelFhHZCEcxzoUR99LjVyC4QLHrWr+xBgy+ZXGnXHU\nj/1WdZhyksi7aTj63boaP6GrqNFLNE1T7dJrQH+kEUjKb3VTZGp1usB6aW07\nuxJJHHEuOFzOJJPl5cYACPug4VmLm4oM8HYjFe4Dh/jzewl62CM+xRdLXjIZ\nDKWH7eyKzSc+3ads6NVeL8XNMyfdeZrWPCjGIxFFMNbb8Ajbif8F2pmNT6Ir\nukT2EOTH5Vth+rj2lciTkL/D8zu0ndu5SMMAD7p+WX/aIPuadb+1CDY3Lh2l\nDmfSqh6u4nrOq0xZnRMK4fbrRKNDqXvGUOKnjJM6oWAYYxJPcPya2hi3cpSv\nY4Ag\r\n=GFKg\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "ccb6b8daeb1f84438c4afbc1e9fd56f73b0852dc", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "read-pkg": "^5.2.0", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.0.2_1618268245155_0.9667230297129321", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "concurrently", "version": "6.1.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "00d22525d3fcdce7f34cc7f3c9753f90a57d6208", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.1.0.tgz", "fileCount": 38, "integrity": "sha512-jy+xj49pvqeKjc2TAVXRIhrgPG51eBKDZti0kZ41kaWk9iLbyWBjH6KMFpW7peOLkEymD+ZM83Lx6UEy3N/M9g==", "signatures": [{"sig": "MEYCIQCijaW/YxqRyhCCkje0Iq1JSBmKs8Q/R+D1DWOcgs7TWQIhAOOGAzt57T20aF/lYLEwMwDaSDyJfEtVwaGBDLRyvI6A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglf+PCRA9TVsSAnZWagAACigP/062dpdip/bHQiq20Ro0\nNpB6bJoIxcrnRb5T7M56bXm7aY32WxNE8EpnM++DdjsM76Mu/JLQ1Lc6MmgB\nj3yYfxEFGRDR1J5aeXVWkkeY1l45fwiK6Nn8qEWfFhLKaIcyXl8TbnfEfVgH\n/zl/gCxviS5+JrETW5ZhO4QWrW6J/d+tVtHYL1KmPJy5cWoGxBPLSrqKhXd4\nM0ZYhAWnqirZrTfS816LM/NAl36WlugYv0tLpw0FFMmj+d+Ng1bfYGxpvPKL\nVxr8rGux4bnWju6JhSBXVnqoPHeAR3RMRhdZflLPQRguiZbMncqNOSpklFTl\n7yR3WHeaWH9rYGLGCt7DnsC0TQvCx25ePPzREYIDiZhyBPmaHeSp3dkGNGtJ\n4QUZhCai0dUGpLajnVrusLHF/j50H/oAn9fDrZ7BIyrT7QMQaSFhf3ZmJh4E\nrulzP3pE62gAGK2u1yzVbZ6ZgKez0jGLWqRmnWQIQszruT4vzhVtROwUGMSp\n10chSc1QJ+wXJFACqETh5bsrNUo2EYMO5mL7s9FtTvdHL3LEKevuZa6JGeus\nvIYo0ccXRqFBdOv5SarurKXzOqW9moMRfHPMyHrkroZngLrcX2lecSS0ZBYC\nRpM8o024X2W/fNy/6YBXnlGP4oFrokhFoGVvxN/oVEOCRpKtRxyxi9dZWWiP\njlI1\r\n=H7fx\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "aad79faa4177451bf5035488267c642f41c8fd4c", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "7.12.0", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "read-pkg": "^5.2.0", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.1.0_1620443023203_0.8568441432410232", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "concurrently", "version": "6.2.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kimmobrunfeldt/concurrently#readme", "bugs": {"url": "https://github.com/kimmobrunfeldt/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "587e2cb8afca7234172d8ea55176088632c4c56d", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.2.0.tgz", "fileCount": 38, "integrity": "sha512-v9I4Y3wFoXCSY2L73yYgwA9ESrQMpRn80jMcqMgHx720Hecz2GZAvTI6bREVST6lkddNypDKRN22qhK0X8Y00g==", "signatures": [{"sig": "MEQCIBF5eUIyKe025z6iQiFnco09+lTEgrmqnp3NrhBb4ik3AiAYKzNIVLCs1XCYUBzENXzzb/GQ/YtAQ1OZT8TyVDp2IA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgq4PCCRA9TVsSAnZWagAAclIP/18REGEz0ThsotbGs8Qx\ne/VWEoPVuVOCA2s0GoemshjjIU9fHMilCfafB1TS1TrlXhZRnZ8NnNdm7z/b\nE6xrchzp8kUeKsDHTjMZa6rmf0y0L5D9AoHH/FoyaXVlfchjrqCp52IpgURS\nL9debiu81vppjbcS7S7E8aFCw+oHJBDySBVJLciphCHvdAGTmIM/ymJ7w6Hf\nF4eoSDsMYMM0d79e+WztEkF7YhR3bVqiH3e/Qrzp9KU27oyLg78o5jhz2jMW\nbJjSYfnFKKvSW/IHK9VXXV0AZJHjczxp/sqJLsMsgTFbO4QTCDGSJkyrXWN8\nijANhZtZWxZsiWWQQPBcICBa9auAkwwbq0l24C6wXLo2MWxdqzXDKoxuUfDL\n3honUk1IzBoSzn7t8mAwbkSQ70bIIT8IUjIH0G3Zlg/6qZi1XkvZW0aEuLPm\n3nhYl3YDJFGS/IODi9HOpM/32gmpw0dbdwHUzhbAtWtGI+eL5kPlxyJ07rmS\nYzoROoqwkZD4iOi/PpfAXJktcPDe5C8hopv8740MJQHX3GaXovZA8DqvHkI7\narKOS1ttq9L3K59cB6nBgC74PxMnf5I4of2ATSlpd4SqzQap9MwtKvPcywsA\nVduJv3MO93b0OP73dR/XeCo1ipQJkKSUZ/gKpi8cHF7fdq52duW/wop4L5Yt\n+fl+\r\n=eUjj\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "becac73c7e768db2e4457f08f9409ec8cd0bd318", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kimmobrunfeldt/concurrently.git", "type": "git"}, "_npmVersion": "7.12.0", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "read-pkg": "^5.2.0", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.2.0_1621853121989_0.27801587430834385", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "concurrently", "version": "6.2.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "d880fc1d77559084732fa514092a3d5109a0d5bf", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.2.1.tgz", "fileCount": 39, "integrity": "sha512-emgwhH+ezkuYKSHZQ+AkgEpoUZZlbpPVYCVv7YZx0r+T7fny1H03r2nYRebpi2DudHR4n1Rgbo2YTxKOxVJ4+g==", "signatures": [{"sig": "MEUCIQDR2QyYvuRXv+VuaCguk1tiFcHG9Zd0ejM8vluAvSZ3fwIgMToF2/3Vi9wv+jrlRR0vDtdu1tYcX2ZTLnCucGx1yCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhD635CRA9TVsSAnZWagAAnpoP/0ZzyLjCbNlf03eBB2j8\n22y2Erg+HfLa6vIFIb9sIphddgncWYQhofGQJ/5dKf9eJVsTZO8a1pkBlOoH\n9AM0g5FpfRWFRQSlJkQzKdvrUhancFsGDvsnbGNVFL0+LCqjPJJEtRru/ngd\nbXWN2djR7yGmS+BSm4wMepN4r8GIy7Xruq1tT6gZICEPRrfVq9F5GNGvX85n\nHGp1BPZ8wu+wV91SGzYlPOgF99US+t3sLyhFd39xQ37sMvk/hVjuHTY4GX9d\nK4Jfcg0n1sJ76B/vZMrbc4vk3DPHwM+kKfDl5vv0hqMDOnDHGjxSHwyhlHkU\nRekAcQYPYfBDcJ95qlh48OuDVjPr7FQ4m8TSzrFBKWXFUdCzkV2KwPg05nU5\nl7vYazHNqm9klRt2QuOFqeuGofkbNTpAOIegQt/jCqFe57pPM4A06o16i/dO\nSKVO38QjerDZoyRbqqvvmjNGoPsMdbdi5bVfQ2t2ATRAiJclAfYnafz+k7Yd\nFUv0SWR98f+FmX7O56y2JxAPyc5Vch9hYxsd+JIE/OqF0BjGAjXByIEX7U0l\nTOB7/L+lukalMQ1U/fZ5DHK5FFT3acCbFdDyvp6D2OZS2CEgDtuuegRZKyh8\nFs8lBQJ5Ij7T3Cz/1d3XPJ7tiydYcfa9FzWBXhf3U6o2gZMROp5V5nt52oUj\n+ymJ\r\n=2IA/\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "0bc490fa98b302488b6fa9cf25d5cea239d38321", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "7.20.5", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "read-pkg": "^5.2.0", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.2.1_1628417529090_0.338928228628681", "host": "s3://npm-registry-packages"}}, "6.2.2": {"name": "concurrently", "version": "6.2.2", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "81c11b85d9a7de56ad4388ff6aab4a94ddea51f2", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.2.2.tgz", "fileCount": 39, "integrity": "sha512-7a45BjVakAl3pprLOeqaOoZfIWZRmdC68NkjyzPbKu0/pE6CRmqS3l8RG7Q2cX9LnRHkB0oPM6af8PS7NEQvYQ==", "signatures": [{"sig": "MEQCIHA2aigfvRu5rQb+yZl8mxrMdI6lW6pBT7KcvDKRzxKOAiBoOv8ZzQpfzyO3/SsE3e+NyGzXnLh81ogtXKdgS/WPKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102944}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "105445c08494474e90d32e215401fd921b75b471", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.2.2_1632783222532_0.7686845231225712", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "concurrently", "version": "6.3.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "63128cb4a6ed54d3c0ed8528728590a5fe54582a", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.3.0.tgz", "fileCount": 39, "integrity": "sha512-k4k1jQGHHKsfbqzkUszVf29qECBrkvBKkcPJEUDTyVR7tZd1G/JOfnst4g1sYbFvJ4UjHZisj1aWQR8yLKpGPw==", "signatures": [{"sig": "MEYCIQCOrjZZ9tS9geuUTQ9FghzwO6dPIBpuBinL+z6SyFZJ1gIhAPja+UdxhDFdIif1CM0GkoQ35yDaeHYvc3pEdhqCi6k3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103824}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "08eda4f14a99dc637bdb76a66b3719a07d002788", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.3.0_1633152382053_0.14181418020190506", "host": "s3://npm-registry-packages"}}, "6.4.0": {"name": "concurrently", "version": "6.4.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "5387ee86be435a0eb51c292ade8a00acf479f170", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.4.0.tgz", "fileCount": 39, "integrity": "sha512-HZ3D0RTQMH3oS4gvtYj1P+NBc6PzE2McEra6yEFcQKrUQ9HvtTGU4Dbne083F034p+LRb7kWU0tPRNvSGs1UCQ==", "signatures": [{"sig": "MEQCIEUXiTrxeFcA9Jh+isiyK1zQzeoltiNpQ5mSeh3Q2p4NAiAlYL9JTQP+PmeA7+tkMApBr1A3q4juM16UaAfyMogFSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106721}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "0da5d93d7d801c9dfc2d30f4fd6d58eb2661d143", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.4.0_1636765913440_0.04344497721596907", "host": "s3://npm-registry-packages"}}, "6.5.0": {"name": "concurrently", "version": "6.5.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "57082bfe797f93cf4a437ce54742c56823a8ea05", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.5.0.tgz", "fileCount": 42, "integrity": "sha512-BjC0KYxXtO6EiWa46gfWYzcLULPBCb+5+VZFtzCemR/co0QSHU4mysEvmT6cnedPpQ9dtJzb+2G694o3V7TgLg==", "signatures": [{"sig": "MEUCIQCbOy3IVxjpKoZixaTZg4ryzjZyRIWQMIF3QK5LrQVYwAIgQvrbFGJWsTfNw1VHYBrwJwchMz6XOJoADdBVM9jn1sw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvE8SCRA9TVsSAnZWagAAEwEP/i4Oti6j2hR4TBtRMpZq\nyUhKU6UPVWcKkNfvTE5CkwGuAxXyBUAl2tydW43TlteJj3z31DJyi287DOu1\nsVr7IaYb7SDtNgV2Gww7v+HMpPjaMbyOQsiU2VpvDHnY7itTpxw+KM2zprGI\nXYEkmmC5r7YWscSajCsWF/ONXg303tTv8LC3drw3dpcsFY3iOvPMB/ecnxIG\n8eWckoqZEcmHqPJYwIxbwz3Cr6IoRAmvAfHjyBv1VQnQlHLgSfvQKJCPUp2+\n1nL4sDo9jNTyKASU96NXh+xRXizlbsQd2oaRLQgX0cF1mqNKwBaUwk8BUFZW\nG7PxHQEkA1++aQl+HJobJ/asxbB7i8lxXJmrzqtKSTERI1CNmrXPFTDFmSMk\nnX0Zy8IwtcaNAEGP156xHC/XqKDiLjIU9BmgW0/IYDPNTO3PfoMWGEW3tzM4\nqnd75DDTh8onHQizcmvHtXpA8XPJABwvXQuqnQJd3ZEko2iLNAwea080BuZ7\nthQSSt15RRIwce8bBricTCZxFZ8FNJly4ILxkLSCYmD7gGDEPpqTr75FB1UH\nF6qGd3fA3rfBLgNSFV84Igg8eOXxLFDGRuos06fAaW5ptPdE5yWko7PW90G9\nlZtrsXDB50LVxxOnw/Xf5xEUAvU4LHFfKuYXGKKyneOfDHoAE8s1wAmOme0J\nVY8v\r\n=4TQT\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "ecc5fa05a351aff9e316a3e82a17d27e066bf79f", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.5.0_1639730962000_0.5123874351510207", "host": "s3://npm-registry-packages"}}, "6.5.1": {"name": "concurrently", "version": "6.5.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@6.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "bin/concurrently.js"}, "dist": {"shasum": "4518c67f7ac680cf5c34d5adf399a2a2047edc8c", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-6.5.1.tgz", "fileCount": 42, "integrity": "sha512-FlSwNpGjWQfRwPLXvJ/OgysbBxPkWpiVjy1042b0U7on7S7qwwMIILRj7WTN1mTgqa582bG6NFuScOoh6Zgdag==", "signatures": [{"sig": "MEQCIExa1Pv8jIpnDAP4q2P9dXXR24qAqlp2l1s75TT/2vL7AiBtLXPSZkl9OZ0NBPCQphEuPXOlAa+78nFildQ7yKRlzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvtuCCRA9TVsSAnZWagAA0XMP/i94MMbkEwF4uCy6XStd\nbt+8UU1k1v+gATOMlpC4Miu1fAO3HIILjrkrWBm3hWrzcumrmX4aHNhtW0D6\nO3esMRixm3QkuoYPBqiSW15xYIdDI6YGTm8BjIpyOOSTUHIkTLWduajfhGyL\naC+NbbqRIxfVgdTN2iHllo6PeLIt/KthjL19gX+c8vIeQ/vvGv+bHYp7LBZ6\nBfI7lcf/XfLTXxT/C7JqcCt/vn9NHyqA93UaeM71o7bN0zaOSh5xvrRavc2g\nW9jGlvLx+WeW1NZIuDV80xuxvB75oN7K8F356CGjyExevDkLsc3GbM//lQRn\nKwOvprohSBeH0lb6GJtx6CsIuvfFQeOsAyVoBAcgzER63qbpxN5vAYvQc8BN\ngr/UbM0rc8M6th6cz9iv0RyBYMd6qUCJJLlJhiSVGP9Qkd4xz4vhwwjOPmqp\nd2ke9CxeAXNaepocKkZ/BdFd7pW2vietnpkXlNMR9Eb/NsMDRvNG778ao6Yr\ntDfkhOcCG16I6kSyKC/m/ctPJjPYYM19gbYUMPOSQvJTiAk9ttoHzvivRBl8\nkVyBsWHQecFygxV4Es7MpzkRt3UKo9smZX7TVkkCknK/ljLpWY6cdTYHdCVh\njhkTU8TTJMrD0LLaJv5mrgsDmZf+sll8byvb7m9kcwjtPosgt4hurfmeBHCc\no6o5\r\n=k2Ja\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "engines": {"node": ">=10.0.0"}, "gitHead": "041e0908cadd00c478ab3b8a6cd3d7af9da198bb", "scripts": {"lint": "eslint . --ignore-path .gitignore", "test": "jest", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "coveralls": "^3.1.0", "jest-create-mock-instance": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_6.5.1_1639897986196_0.09453014815875416", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "concurrently", "version": "7.0.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "78d31b441cec338dab03316c221a2f9a67c529b0", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-7.0.0.tgz", "fileCount": 69, "integrity": "sha512-WKM7PUsI8wyXpF80H+zjHP32fsgsHNQfPLw/e70Z5dYkV7hF+rf8q3D+ScWJIEr57CpkO3OWBko6hwhQLPR8Pw==", "signatures": [{"sig": "MEUCIBk4vXNM5TF7AY5UgiKH9lCxxS17PgvYK+hZR66/ptehAiEA+NOo/bEq/YDRnr9+UCvn2rbPu8SZFc5Om4FQpQ8cajY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0k4hCRA9TVsSAnZWagAASFIP/j8gToCb2hzHip7cEslT\nmgkWKDdXrhxFWE0C/ACHaV8CwM9qu+crd7YOkCtocytK8DqEHKa6lF3DFEM8\nejY1FwMMiQ3XPXdu6qi5RERJMVJHfBtgp5S6pYwPjLgix7ep0A3o8gtbZmvg\nXRNjeU3AlUOMf9JTohUFlL7wyYyFuEAjHtLeniNxz1X6Z7Bl8bsiOGXLpDde\nSdQlfMtNxuR/kcPtrAgevi2NIzM8Ok/K/2shJp2C+6+gP5dmS+E1nfzH84lJ\n+xm6cQz88AWvg4cFUlFxpJVwQYmL4VgwYCQOn81NqaN5Sqhw9XbQa8cKSaM0\nniUeMt23T2Bn+NHncV/TAOV9zhQedBpc0HbjaBElbhDJKr3qTAoivSREw2Fd\n+tyqWiXJpK964ur4v+l8JNtnQj2vErYqT3cmr46ut3GSQsb3/jaEvDIHwUNx\nYtvyNhdFhmywPxamOlj2051IBea4a1fQDkOKZ3MIDSDvAN/FGYrslvwJlxt8\nTrCXwVoIw+kdh/mWZ5RuWTi+CLVIFOES8ZuQI+BW8lOa5zlrKDj3JSrxP/dO\nnG5aYlrl6oL7qC6/vEmXP8YlFjJLO/40Ibod4xuRbVHXBPyMfWParmQC8p2U\nr9wckuNIi7QioAaF8PDvH/3SKWzocyI2E3MKanJNe73q1KQqASGsk5VH+2Vk\n7iqK\r\n=dS9d\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/index.ts"], "testPathIgnorePatterns": ["/node_modules/", "/dist"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "type": "commonjs", "types": "dist/src/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "exports": {"import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "gitHead": "379c7dc08283014c794978d1aab0d537db5470e2", "scripts": {"lint": "eslint . --ext js,ts --ignore-path .gitignore", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "postbuild": "chmod +x dist/bin/concurrently.js", "prepublishOnly": "npm run build", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "ts-jest": "^26.5.6", "ts-node": "^10.4.0", "coveralls": "^3.1.0", "typescript": "^4.5.4", "@types/jest": "^27.0.3", "@types/node": "^17.0.0", "@types/lodash": "^4.14.178", "@types/supports-color": "^8.1.1", "@typescript-eslint/parser": "^5.8.1", "jest-create-mock-instance": "^1.1.0", "@typescript-eslint/eslint-plugin": "^5.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_7.0.0_1641172513731_0.41517110187670947", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "concurrently", "version": "7.1.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@7.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "477b49b8cfc630bb491f9b02e9ed7fb7bff02942", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-7.1.0.tgz", "fileCount": 51, "integrity": "sha512-Bz0tMlYKZRUDqJlNiF/OImojMB9ruKUz6GCfmhFnSapXgPe+3xzY4byqoKG9tUZ7L2PGEUjfLPOLfIX3labnmw==", "signatures": [{"sig": "MEUCIQDaMC8T9RW91vPoMoh+blPwZC2M8YTQYFq84/M75/lrLwIge6wk4Re0PIfYbKUOH7DG6SoeaAM5KlK/NGRZbGL9vI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101173, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSB2aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDDA/9ENVQjsc6ZyrMASfd6BL6xfMmfL+b5W1aP+awHWLAZZqKRXoo\r\n5lK7a01V89oB5B/gqTvlAvFD40EsdOwW/EtijvPOPEFkGAvHrQXWAdXTgXRR\r\nuyLjj9bd2qXYk/Z2hazzQjzzcTzH9QWBtPq1nVS9YR+oAOGDpMJBpDeGOOBX\r\ndcgPI/nAjZYPHL31fHNkZ+JM1kYWfhBVJa9ytJNTyrKs/wkRxulu0fswKqgu\r\n3RKzT/9mJXpPXhAt09X21i/Jxuu+<PERSON><PERSON>+/8nkFJxiRDnEns7m4/iQ6Sw3f8o\r\nJYrjkqqTprEGKLPcEI8Tp9F5qJXfP4Og72Ne1eo5/KHdkRWFbZR+s2iV8FU7\r\nff/isR6IbKa3MNjLslYsyoGyg8CFAsg77skbgHWneNlb1iIAIKNM/lqRK733\r\nUT6Levhu4i8gDrqF8HWZCBJ1V1g82nMjUMnpPQcL/ta/qQ15M3lXoWGpLJwo\r\nl5WUFva7jsEpvws6/KtC8AYpM+3Hzx3/PEifJb90pxP7Wf/XXcQRt+EyXx+1\r\na3906mL2LV2qNZ57d/0iBrG4nISB+MuRrVSbZQceCsrSXIjkhhoRu6UHObs4\r\nsdMXJzjtDyfC7u7bsLdlWxN8GVe68FmNKb4yIG7lHi3TCtQnIVWN55uH+Z5E\r\ns9tiMC6blZMWOILd4FF6/zeVAd6ooN9WzUA=\r\n=CtPV\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/index.ts"], "testPathIgnorePatterns": ["/node_modules/", "/dist"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "type": "commonjs", "types": "dist/src/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "exports": {"import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "gitHead": "1762f88bcf29744466515bf6900c26f697f4c45f", "scripts": {"lint": "eslint . --ext js,ts --ignore-path .gitignore", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "postbuild": "chmod +x dist/bin/concurrently.js", "prepublishOnly": "npm run build", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.22.10", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^16.2.0", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "ts-jest": "^26.5.6", "ts-node": "^10.4.0", "coveralls": "^3.1.0", "typescript": "^4.5.4", "@types/jest": "^27.0.3", "@types/node": "^17.0.0", "@types/lodash": "^4.14.178", "@types/supports-color": "^8.1.1", "@typescript-eslint/parser": "^5.8.1", "jest-create-mock-instance": "^1.1.0", "@typescript-eslint/eslint-plugin": "^5.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_7.1.0_1648893338115_0.7816197648511833", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "concurrently", "version": "7.2.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@7.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "4d9b4d1e527b8a8cb101bc2aee317e09496fad43", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-7.2.0.tgz", "fileCount": 53, "integrity": "sha512-4KIVY5HopDRhN3ndAgfFOLsMk1PZUPgghlgTMZ5Pb5aTrqYg86RcZaIZC2Cz+qpZ9DsX36WHGjvWnXPqdnblhw==", "signatures": [{"sig": "MEUCIQCKxrf1cyxECAH9Ksyal1EgZVR1QNFm/p5IeFmrVOl08QIgF3pyoagXXwir4yfyzGSmMoiIv914E8KgOHwiTF9W48o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109303, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigGmjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRTRAAitL9oSgXMdgqLpBcKvoafRSwEGMmocYhv34zRps5+Ah/pGCM\r\ndgoeH5vx6sym9vrskX4E749ReuUZ+NP+OdT742ka9VG8tZwWf7So/xHStyBI\r\nB8qzFa0hu1cCdNnb5tc/8C0d07nSmi1OHU/1ZBbuWPBgqeZurpr1DHb6J4ap\r\nBFV54zc2inE3jt5hEO/e+GsOI4HnF3y0LACy2ocX2luJM+3MioyFjjAoPS9k\r\nlBlPKVOXoc8oMcLCiPA+xnDxoNZPBq8iJ8Bnt1io1Od9v8c3mZoC9eawjeYz\r\ngx8CPeHApcB0J3rSjj8KLEh0Vm7SSqXdMPhUHxOe72qMHtQuULmUz++M8XIZ\r\nikdJjeEwCXSKXf3os365avSxIly4Rkvcuuai4n26M6PBEkMFTiOmf8/b71FT\r\n988W2f2PW4Hi9n5Lc3+BSTjWdCaM3IkxAsCvzEM3mn7jMBfceaavNVqhNixe\r\nsr3YWOoq2zzqY36mDdtpM98emn1ZwjJaQv3Vn23QMBm+qGUSLX+A/wVwZccx\r\nqwK1vyJY/c2qTH8ChWZd4HqeXUf4ZLx6zrdV86BqqyJlqOq9ClP/J5xmBEDn\r\nQ3DjyOKnofClxq3IKOBCJxqZu9uYz0aNkC6mu0950ZDP3n4dKrO0zu0f6isf\r\nxeuqR+7Iy+PHRfY6prG3ZqYHrPrzTzbQK18=\r\n=JS1B\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/index.ts"], "testPathIgnorePatterns": ["/node_modules/", "/dist"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "type": "commonjs", "types": "dist/src/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "exports": {"import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "gitHead": "55b8caec381657084af9fc52d593afc2668e2c5e", "scripts": {"lint": "eslint . --ext js,ts --ignore-path .gitignore", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "postbuild": "chmod +x dist/bin/concurrently.js", "prepublishOnly": "npm run build", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.7.0", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "12.22.10", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^17.3.1", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "shell-quote": "^1.7.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.17.0", "ts-jest": "^26.5.6", "ts-node": "^10.4.0", "coveralls": "^3.1.0", "typescript": "^4.5.4", "@types/jest": "^27.0.3", "@types/node": "^17.0.0", "@types/yargs": "^17.0.8", "@types/lodash": "^4.14.178", "@types/shell-quote": "^1.7.1", "@types/supports-color": "^8.1.1", "@typescript-eslint/parser": "^5.8.1", "jest-create-mock-instance": "^1.1.0", "@typescript-eslint/eslint-plugin": "^5.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_7.2.0_1652582819301_0.7161942427575889", "host": "s3://npm-registry-packages"}}, "7.2.1": {"name": "concurrently", "version": "7.2.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@7.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "88b144060443403060aad46f837dd17451f7e55e", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-7.2.1.tgz", "fileCount": 54, "integrity": "sha512-7cab/QyqipqghrVr9qZmoWbidu0nHsmxrpNqQ7r/67vfl1DWJElexehQnTH1p+87tDkihaAjM79xTZyBQh7HLw==", "signatures": [{"sig": "MEUCIH+D21VljULBgsIBZcw5mtqQd+lzz6nbppVGJXRhWlhjAiEAy7SIh5C0EV10qkpJe4URjuXbzVwHzIEPqrudVUWY6Ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiirkjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3Yg//fqR0J7rXtlwovpKwgWh+/mAfvMwZWHZBmIqrA4quJ1HtYvkV\r\n4oOPlBP1A4XiZ7fbJnb+JnvapYu9Air3W9BhsXuTUc/E/pB7euS1caNyl/bn\r\n5eE+69KHPQBazk1pvN9jvoWTyyV29cxMHMau7Ra3VMorJmVcpcYETnC791v4\r\nEbsEbFVhGuZuZ5GzK2Z68L5bC8k2kzhX+3vx2FBB3CjwIbgO9gM4LI7e7Cxg\r\nfok1surnPcu+VhQx2Rkf3NuPyhqU7mqYXeQHwYqZbPWp8qQpgsSwxVm2wFn6\r\n86mHKFd+Eu3DQ1CV47M6PTsiUruGwMB4ij94OyA52kv0vLBLbYMxA8PvjJxH\r\n18HzhKbPKhawJtbY+Hz/AtmWFpeOkptVGHrFOeQ6KJcyzyOgLMzVOF/aOgvI\r\nE1N3HpHnrQvEE9l4IHwACCZHjXs7VVyO5eKAj+LCaSsy4P3a3BdH/fJidGRW\r\nI6Uhvp3hjjHQFits9xn3tBXlFKBNKbmbq6MFijyHnk/tL1ANRLBzro2qJtHd\r\nPIQKAxcLkkh5cjqPHJUty9+PPtezihlXw93yD8m0p5Ni8aiUY2YLctBs7WSK\r\n+XSgmo5z4c7t8nFCJcvJKh22k2CY3d/B/oehaM1IHFI8MhuH11vAdXq4gFwz\r\nWxgyZ6xC3njyfD3qJYkxpp2j6ZZ3mW66bGQ=\r\n=QYIi\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/index.ts"], "testPathIgnorePatterns": ["/node_modules/", "/dist"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "type": "commonjs", "types": "dist/src/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "exports": {"import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "gitHead": "723d7c315d0a5758414c9bc04b4a56a985454bb6", "scripts": {"lint": "eslint . --ext js,ts --ignore-path .gitignore", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "postbuild": "chmod +x dist/bin/concurrently.js", "prepublishOnly": "npm run build", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "14.19.1", "dependencies": {"rxjs": "^6.6.3", "chalk": "^4.1.0", "yargs": "^17.3.1", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "shell-quote": "^1.7.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.5.1", "eslint": "^8.15.0", "ts-jest": "^27.1.4", "ts-node": "^10.4.0", "typescript": "^4.5.4", "@types/jest": "^27.0.3", "@types/node": "^17.0.0", "@types/yargs": "^17.0.8", "@types/lodash": "^4.14.178", "coveralls-next": "^4.1.2", "@types/shell-quote": "^1.7.1", "@types/supports-color": "^8.1.1", "@typescript-eslint/parser": "^5.8.1", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_7.2.1_1653258531277_0.5897176201290546", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "concurrently", "version": "7.2.2", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@7.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "4ad4a4dfd3945f668d727379de2a29502e6a531c", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-7.2.2.tgz", "fileCount": 53, "integrity": "sha512-DcQkI0ruil5BA/g7Xy3EWySGrFJovF5RYAYxwGvv9Jf9q9B1v3jPFP2tl6axExNf1qgF30kjoNYrangZ0ey4Aw==", "signatures": [{"sig": "MEYCIQC0EughCN5pJ/4AGyD3haL2NyekFbv5A615zM9uBkAeOwIhAIFgN5QGARTd10yDQS9WIF9xbi2DZo1Bm2f2ZmyBaj+e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqQ18ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNYBAAl6By8D11rO5DUw1CoSZXCsfBlQOua30qUF5gc/tdbbj0kBYc\r\nB87lfXN+PHMLE7fw2VGsjdYcB/P4559OYZRaQCMIzAcZS3ydStP00vI9tGyb\r\nMXJK2UIuKmVJrru66y/pAxcOl2BJqjZ5maJFBiVO+IvJdyBmqqYzaP5SNWgX\r\ncItdGRdQhyG9xuLanhZl4ZKKnEl0Rqvu96N1el6Nd/WGwv16YzlfIIkiKdZf\r\n4IJRK7k/GT4Q5cl/onNQD6RFefepHxKehQ+kvTFGCP+s5yMWGGSVaW9xWWT4\r\nswP1h+lqjp6ODXRDEOKrqa+15VKje1K4V8Agl2XPMAFm5nHIu/inT0bhiLf1\r\nOslfEcvSw0ziV6Um0iqaTZgOq5OCBbzdJ+gAe7EEhMzB8Pf4qBGs6HRVAaTm\r\nO9xRAJ2AeFG5MOIpIENiDngxMe3G78Z3zQBrF1YTOBEBU4y9eKUGmdUxFpCE\r\nh68s9I4UXCu/cZrM558a39fC6MKMUqn0sn2ZuVWbjo1P4iBHhnai3ptoZ5QC\r\nk0Iit4n57XAWltfXRYMEA13Haw4NRip2onoiAaZDftq1FF5KAEmbJCw+bep4\r\nlfS1Tf6SSvDj/RV47dOMviu8v0CdHTbORXBJ3lMy8sq8R7R1252LSmKm03z1\r\nR0kOd3O66D8+5AlT+yL12Yt6IisE8lvfLdo=\r\n=9Jww\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest", "collectCoverage": true, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/index.ts"], "testPathIgnorePatterns": ["/node_modules/", "/dist"], "coveragePathIgnorePatterns": ["/fixtures/", "/node_modules/"]}, "main": "index.js", "type": "commonjs", "types": "dist/src/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "exports": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "gitHead": "494ff341292ba11ff2fa06dbc04b74490739f6b1", "scripts": {"lint": "eslint . --ext js,ts --ignore-path .gitignore", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "postbuild": "chmod +x dist/bin/concurrently.js", "prepublishOnly": "npm run build", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Run commands concurrently", "directories": {}, "_nodeVersion": "14.19.1", "dependencies": {"rxjs": "^7.0.0", "chalk": "^4.1.0", "yargs": "^17.3.1", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "shell-quote": "^1.7.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.5.1", "eslint": "^8.15.0", "ts-jest": "^27.1.4", "ts-node": "^10.4.0", "typescript": "^4.5.4", "@types/jest": "^27.0.3", "@types/node": "^17.0.0", "@types/yargs": "^17.0.8", "@types/lodash": "^4.14.178", "coveralls-next": "^4.1.2", "@types/shell-quote": "^1.7.1", "@types/supports-color": "^8.1.1", "@typescript-eslint/parser": "^5.8.1", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_7.2.2_1655246203804_0.3589186668774311", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "concurrently", "version": "7.3.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@7.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "eb45cdbc8df43da195f619aba218a980cae49184", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-7.3.0.tgz", "fileCount": 53, "integrity": "sha512-IiDwm+8DOcFEInca494A8V402tNTQlJaYq78RF2rijOrKEk/AOHTxhN4U1cp7GYKYX5Q6Ymh1dLTBlzIMN0ikA==", "signatures": [{"sig": "MEQCIAj+PONX78ikLgDjAZwCq9OkMbdue6BNdOIgdZYLKGkFAiAu7eqqPtIvFBv+dI984BHFg6Ush3sPcrhOoEqplKCFUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1fESACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4jA/6AkYEUkL5AxWfstMFW+o8tgxu/KTppawE9di0nwYO5RF3fUA7\r\npaJCusIxf7tF1m86L0qJ+Pg3XeYIHnOgXmsZ7fGJYClwfnUh3+O2NO7S2IK0\r\nHnJw5p9s9Kqbe4nOW7H3LOdakx8bqYeSSua3APbKGVQkjuaFY6yl9LKw2p+t\r\nkvXqmTkvkHMuhUJuLzEkZQoVFthuAjAR/NzJlSpHX5OwhUenkK485oDDzIeN\r\nWeHCPVhf3rmKODrPz+EN29KSRBryPH5NMfCoHvVWZT8Qkt5QzzxrJBURbLIH\r\nq55crW63Px4DGM4V4Nn8SkDKpLrF06OKs6XSWMtlThJduxj/PTsv7pAa+mYK\r\nIE2h55dp8VVJVC929+aAOqsUbM/+4Nopp2agzbYxkQRCmga/qMMK3xGxzHfy\r\nk4E8sjUDD5phO/TuEGAg/mYT/Knfv7NLC04/Ewm31WsRtoJV5iAchdLb4PEX\r\nIbzamObiE62h6jTwfXoa4SpVJeNevkMVnWuv8X/k3nBkrqDlTDUjq+qgK0l+\r\np24r7fcejV3BetfRo87Cl8TuOroAIl5V1jLsmx+mpFLiLUWGuqxTvlsrAucj\r\nUlC8mtP2hmkWuTton/HWYy+Fagsw/O0TasZodiBY1F+lr3kFhnHxCLfOsEcv\r\n6RY8D5suQoyy288sZXe+lJSDS7W/uAZn6rg=\r\n=4E8v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "types": "dist/src/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "1220aeb66c99ee65ba464c789dbe0f6055afaf07", "scripts": {"lint": "eslint . --ext js,ts --ignore-path .gitignore", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --ignore-path .gitignore --check '**/{!(package-lock).json,*.y?(a)ml,*.md}'", "lint:fix": "npm run lint -- --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "npm run format -- --write", "prepublishOnly": "npm run build", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.{js,ts}": "eslint --fix", "{!(package-lock).json,*.y?(a)ml,*.md}": "prettier --write"}, "_nodeVersion": "14.19.1", "dependencies": {"rxjs": "^7.0.0", "chalk": "^4.1.0", "yargs": "^17.3.1", "lodash": "^4.17.21", "date-fns": "^2.16.1", "tree-kill": "^1.2.2", "shell-quote": "^1.7.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.5.1", "eslint": "^8.15.0", "prettier": "^2.6.2", "@swc/core": "^1.2.204", "@swc/jest": "^0.2.21", "typescript": "^4.5.4", "@types/jest": "^27.0.3", "@types/node": "^17.0.0", "lint-staged": "^12.4.1", "@types/yargs": "^17.0.8", "@types/lodash": "^4.14.178", "coveralls-next": "^4.1.2", "simple-git-hooks": "^2.7.0", "@swc-node/register": "^1.5.1", "@types/shell-quote": "^1.7.1", "@types/supports-color": "^8.1.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.8.1", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.8.1"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_7.3.0_1658188050024_0.6570150356675402", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "concurrently", "version": "7.4.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@7.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "bb0e344964bc172673577c420db21e963f2f7368", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-7.4.0.tgz", "fileCount": 53, "integrity": "sha512-M6AfrueDt/GEna/Vg9BqQ+93yuvzkSKmoTixnwEJkH0LlcGrRC2eCmjeG1tLLHIYfpYJABokqSGyMcXjm96AFA==", "signatures": [{"sig": "MEQCIAVVXInAa1A4+F/VSIjaN/BMAhkun2WdbTmIex4XXGFFAiB75Ze2/Vfx2XfpV4ghsLv7775w6xzo/4e/59u2vpYUYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGFlqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjQQ/9G0vjS2hrDFhVImQdHSI24TBtPmeya9UUWltp4ysHt77xvMj3\r\ncr9uxe4dEH205fkQqlX3A/ajL9g+PpPHeL/iftzC5oml5VpP7vcG4Lyjk2gT\r\ngnqJoPJRH/+vINxR8BYxjrqafTUscxNFlFqPDv+td7hyq45gz9VTy3Og11cD\r\nNpFeijF3V8wNAo8I70IFM5lxnp+Qh1Lrx2d/7gqSagsGg17uDVnnkEBq2UiG\r\niNd7p+gNWLqHkjA0XLcui6YxpVkBRElTKMtDf+j1exkr6/bRtElpnXg6eUvV\r\nKu/xyF7rEXTWPUQkB5igmj+6Vms6XC+pP4VsYSjCr5Y4xruSaOLf2VTnKRF0\r\ny4/uJJ8r2Err5gaTsrIhat2Pjoib6UJW8JjHqZqyA+ozzzL6wol9CfhQJmH6\r\nD0V92DPe1s3fsPJoK6XWQBFUXHtNfgyLr84/eZZC+hIhyKFGnyb9uNFOjSBe\r\nJBYcEGraosdayoVowM8od3NivqiBUXumPX12NOzqInl+2FjWukKkrj73uC/3\r\nMD7FNk14T5LvTfIqFbITUbLBLxnPWQLJMrCSDZ/Rd0vPxMhfU+3RHApyr1F1\r\nBnjBfYoQx5w0RjMkhys+k4oXevxKIlB7moyiA0oWTcPf0vyDtN6sEllq/wtx\r\nqxa6XeUd6UHWI1E5MrSfkMkA1XNjcndREIY=\r\n=otn6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "types": "dist/src/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "gitHead": "eb90407711becd6d16fa29ea48f7cdd14add651c", "scripts": {"lint": "eslint . --ext mjs,js,ts --ignore-path .gitignore", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --ignore-path .gitignore --check '**/{!(package-lock).json,*.y?(a)ml,*.md}'", "lint:fix": "npm run lint -- --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "npm run format -- --write", "prepublishOnly": "npm run build", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.m?{js,ts}": "eslint --fix", "{!(package-lock).json,*.{y?(a)ml,md}}": "prettier --write"}, "_nodeVersion": "16.16.0", "dependencies": {"rxjs": "^7.0.0", "chalk": "^4.1.0", "yargs": "^17.3.1", "lodash": "^4.17.21", "date-fns": "^2.29.1", "tree-kill": "^1.2.2", "shell-quote": "^1.7.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.21.0", "esbuild": "^0.15.1", "prettier": "^2.6.2", "@swc/core": "^1.2.224", "@swc/jest": "^0.2.21", "typescript": "^4.5.4", "@types/jest": "^28.1.6", "@types/node": "^16.11.47", "lint-staged": "^12.4.1", "string-argv": "^0.3.1", "@types/yargs": "^17.0.11", "@types/lodash": "^4.14.178", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.1.2", "simple-git-hooks": "^2.7.0", "@types/shell-quote": "^1.7.1", "eslint-plugin-jest": "^26.8.2", "eslint-plugin-import": "^2.26.0", "@types/supports-color": "^8.1.1", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.33.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.33.0", "eslint-plugin-simple-import-sort": "^7.0.0"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_7.4.0_1662540137867_0.14853948548497975", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "concurrently", "version": "7.5.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@7.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "4dd432d4634a8251f27ab000c4974e78e3906bd3", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-7.5.0.tgz", "fileCount": 55, "integrity": "sha512-5E3mwiS+i2JYBzr5BpXkFxOnleZTMsG+WnE/dCG4/P+oiVXrbmrBwJ2ozn4SxwB2EZDrKR568X+puVohxz3/Mg==", "signatures": [{"sig": "MEUCIQDy8K9Ecok+PyxPtqPVmKL6skVZk7SkEYwvSCdquAq2HwIgH3OrIIXdw9tP2a912yflnUyEFt7R0goWEGDH9UWVk/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjU4VTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOCA/8CWccUf6MJAPLHUSXiQULP8Lrpz3M+5bKDvqp6tMOjVVNiPHc\r\n2V3PZ8FVsJzHUQPV+BRRhAlR0zY9cY4ijdfUS+tgkOuN+oNwGQm4u46i3V3r\r\nZxeleEGJQqIeenL0/dzGGwULXLO28C31Lxt1VFnxkUzy8vJvL6DoliGP4rd7\r\nwMg00+p6X1ZzKJIuZcHTDEUcwyVdANhMBqXIJuQBjU3iQb7CGXdOoIOWZHgs\r\nD2HqH3x94UY8q1H0xcMG7uezP8wEEHHtZ6RbvIwedSSgA532iGwW5YZKK90z\r\n2nNRsFMeokvcC/j7gcOXfC7QlK2oLfCxA583rqLAnFS0DJoqeuY33m+hfdTC\r\nYq5Mo98kTpV0r6pMgrpH1bDnAE6UMk3oQWWHvRX+Ur0qDvTG1RT7jwjNKDVy\r\n1P0n2N907ocD5m2lioJPZ17yEjhpVyh6TIggizsrkicoijcnuZHs5hmx2s1L\r\n3ih5CRgOxgYOpyq8i60kLzcC6RW8LV+POSBTPppMXK5PbvajgBKeh9ik2Gjz\r\neqf7nvIhMZbMqZMGGmW91h5JDMKBhKrl1jcVN42/YqyoR9T5N8zM4/WjToP5\r\ncElxmFTU0NSLEnA0xXW0YE9wi3JnPHr1JcOPkXtoMcxLyAfzBwHp/Rs0lMJi\r\nAWF/gehw5uH4kf4IesMpG/fWSeLpzf+/zew=\r\n=71gc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "types": "dist/src/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "gitHead": "875d2dfe9cbea779c67f301e9520e5f81f502f1e", "scripts": {"lint": "eslint . --ext mjs,js,ts --ignore-path .gitignore", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --ignore-path .gitignore --check '**/{!(package-lock).json,*.y?(a)ml,*.md}'", "lint:fix": "npm run lint -- --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "npm run format -- --write", "prepublishOnly": "npm run build", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.m?{js,ts}": "eslint --fix", "{!(package-lock).json,*.{y?(a)ml,md}}": "prettier --write"}, "_nodeVersion": "16.16.0", "dependencies": {"rxjs": "^7.0.0", "chalk": "^4.1.0", "yargs": "^17.3.1", "lodash": "^4.17.21", "date-fns": "^2.29.1", "tree-kill": "^1.2.2", "shell-quote": "^1.7.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.21.0", "esbuild": "^0.15.1", "prettier": "^2.6.2", "@swc/core": "^1.2.224", "@swc/jest": "^0.2.21", "typescript": "~4.8.3", "@types/jest": "^28.1.8", "@types/node": "^16.11.47", "lint-staged": "^12.4.1", "string-argv": "^0.3.1", "@types/yargs": "^17.0.11", "@types/lodash": "^4.14.178", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.1.2", "simple-git-hooks": "^2.7.0", "@types/shell-quote": "^1.7.1", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-import": "^2.26.0", "@types/supports-color": "^8.1.1", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.33.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.33.0", "eslint-plugin-simple-import-sort": "^8.0.0"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_7.5.0_1666418003421_0.8502002174328318", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "concurrently", "version": "7.6.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@7.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "531a6f5f30cf616f355a4afb8f8fcb2bba65a49a", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-7.6.0.tgz", "fileCount": 55, "integrity": "sha512-BKtRgvcJGeZ4XttiDiNcFiRlxoAeZOseqUvyYRUp/Vtd+9p1ULmeoSqGsDA+2ivdeDFpqrJvGvmI+StKfKl5hw==", "signatures": [{"sig": "MEYCIQDpfFq9ixDIcfY0iTS3fYSaIh+oItF+O/z/6XpPLFALqgIhALaE0ae7y218L4SV+q/jgyirdgSRb9O0z2B+VPO0+abJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjep9NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqn+xAAm1NJYs6/lp25Y0WtlsMWHytyCct/3HtV0natBPqdbdu44Xr2\r\n6KqV7SbCRHPtrQeADEZu14fUzMF2IaIuEMH4+VLVTcn91aEAbUXCv9bo+oiT\r\nhoooWBMNfhlJNYy/IcAXaS0FakE7A75yVvcodgFhSAAVBb1Hd11Q96Kz01+a\r\n7hm3ORxGF3u4e3sfa4tgvDambdF0VSDFO2tHUDieJKWU354a9Ss4UAF4iTGr\r\njw8GswypahlTVyhygA1MN9HUTGJ2mrjW1wxjN83y6xKh67fTWZ2vF1oz1jQ6\r\ndBaGYRocS/HoMfHfMc5m2ypyqxHVez1/vfVDQ2f14CGktzZL0cPs6GQu7HmY\r\ncYIKlLL8QHIvZeEx4R/BAay02eryD2A4X7JEy2C7eaf2d3iDbAO+8U+qsDuS\r\n1NnanjqsjTmdhOCFcQhjfCV8iwROtlOlGFCfSwli/bA/9KvCRcrDMVkil7dh\r\nS3B5ri8Oy6WtdlaU2XqvOFtfaDrTD7mxq4ViN1BQSIkpGlaeofHJaF9cV69g\r\nLx7E5GPkHPSxcM+CXJVGVFrJbtNXcl2issfBVwHUYI9G0zGbFONOW1ciztu4\r\n/Co1NImvfF5xc3nmeF3RHU6RxzcHUn2n7CNt3hsLWNbgpyDut/QTrBnAxepo\r\n3/7ShujBiw9ln+t6nNiyVoBEg2NMMEZAono=\r\n=Kl3w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "types": "dist/src/index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.0 || >=16.0.0"}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "gitHead": "97248d940f2cd82c69cb47925400dcfd70efcfc1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "npm run lint -- --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "npm run format -- --write", "prepublishOnly": "npm run build", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.m?{js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "16.16.0", "dependencies": {"rxjs": "^7.0.0", "chalk": "^4.1.0", "yargs": "^17.3.1", "lodash": "^4.17.21", "date-fns": "^2.29.1", "tree-kill": "^1.2.2", "shell-quote": "^1.7.3", "spawn-command": "^0.0.2-1", "supports-color": "^8.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.21.0", "esbuild": "^0.15.1", "prettier": "^2.6.2", "@swc/core": "^1.2.224", "@swc/jest": "^0.2.21", "typescript": "~4.9.3", "@types/jest": "^28.1.8", "@types/node": "^16.11.47", "lint-staged": "^12.4.1", "string-argv": "^0.3.1", "@types/yargs": "^17.0.11", "@types/lodash": "^4.14.178", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.1.2", "simple-git-hooks": "^2.7.0", "@types/shell-quote": "^1.7.1", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-import": "^2.26.0", "@types/supports-color": "^8.1.1", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.33.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.33.0", "eslint-plugin-simple-import-sort": "^8.0.0"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_7.6.0_1668980557395_0.7686948079950968", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "concurrently", "version": "8.0.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@8.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "1f3f1b72d8daa45b1935ea9f43a690d98601dd9c", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-8.0.0.tgz", "fileCount": 55, "integrity": "sha512-1fjagjL+RgPRAx9Wi8Yv866Whtx34MRdk9qf6wwxpQoYL2mD+lUZMOe9RXYULC6eBl6e4sde6cu8bpyg9Rd9/w==", "signatures": [{"sig": "MEUCIHYDW82XFcfAsMfsQFS1K1amwnz8jpolkD89t4NeyY4vAiEA1X/6Cvw6OZmzzAFtvO+pu0pdrf1prCCORJn4i9/IBAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkI28CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6NBAAm+GVkZ77/rfXA7O5ycDZwWW72wxiZNX92j9O0z3UqGQQExpM\r\nc2mWCJth61ISPE6nx2whJvoYhOOJi8C9bSCgWMuFKQucl4lf9R3Y7wsB6ffq\r\nARoep0Rzn6oFGIfWNXAxL91p6gBt/tFgvCsQp9gx0DRxX6hzxB8fMggb/G0Q\r\nTRCUwC/JsekbjjEkEtbJKeMH7rc9XQgfHMrwRwQsjc54o2y51n1+CbGlUaKl\r\ncP0Yg41RueWVEW5g5uXiv/qP6EBZ/eFgFAX3Bq8z7uzHemM7JIw5oLysh/At\r\nebKmOtS+mreUXryfi0gszVqhS1hyCS08hisO0LU+3s1+uojt3cOAB1ebDF1P\r\nFHSpiGWbXqYOWK4cBRHZH47KIwLOqYpQgId1ugdpmAbIOJajl4IPb4ozzypr\r\nAhtQB5lhP5Xr/b+BEWSZnWeePIPrCC05Q4JHsly6wDJlCD3TUhzbvmt8QFkr\r\n0ekpP/1ftcyNuf8yoa5Wz0yKhHO0Aq2SZ1Xb3tLHl1akadzJBGhCrHafVGZs\r\nGwOiHxHsofApupRe7dWdQHv0xGh/mnkoqs+Mo8wie6rLvO5dEYxO/PbXcJS4\r\nMXqyBpWDheYqqxz8LR1Q9tBz3sJccFUBbpkVYTv6Bk8Qc4UGE9+op6zV4Vx8\r\n7gD8+PsL2ydZUnMzCLoICfUqzVvtuxmZhqo=\r\n=x2Ao\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-8.0.0.tgz", "types": "dist/src/index.d.ts", "engines": {"node": "^14.13.0 || >=16.0.0"}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/7l/zbk3jvj15kx350hll64vssn00000gn/T/60402193bad6fff3d2bee1869973279f/concurrently-8.0.0.tgz", "_integrity": "sha512-1fjagjL+RgPRAx9Wi8Yv866Whtx34MRdk9qf6wwxpQoYL2mD+lUZMOe9RXYULC6eBl6e4sde6cu8bpyg9Rd9/w==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "16.16.0", "dependencies": {"rxjs": "^7.8.0", "chalk": "^4.1.2", "yargs": "^17.6.2", "lodash": "^4.17.21", "date-fns": "^2.29.3", "tree-kill": "^1.2.2", "shell-quote": "^1.7.4", "spawn-command": "0.0.2-1", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.3.1", "husky": "^8.0.3", "eslint": "^8.31.0", "esbuild": "^0.16.13", "prettier": "^2.8.1", "@swc/core": "^1.3.24", "@swc/jest": "^0.2.24", "typescript": "~4.9.4", "@types/jest": "^29.2.5", "@types/node": "^14.18.36", "lint-staged": "^13.1.0", "string-argv": "^0.3.1", "@types/yargs": "^17.0.18", "@types/lodash": "^4.14.191", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.0", "@types/shell-quote": "^1.7.1", "eslint-plugin-jest": "^27.2.0", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.26.0", "@types/supports-color": "^8.1.1", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@typescript-eslint/parser": "^5.48.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.48.0", "eslint-plugin-simple-import-sort": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_8.0.0_1680043778531_0.6695133104179414", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "concurrently", "version": "8.0.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@8.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "80c0591920a9fa3e68ba0dd8aa6eac8487eb904c", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-8.0.1.tgz", "fileCount": 55, "integrity": "sha512-Sh8bGQMEL0TAmAm2meAXMjcASHZa7V0xXQVDBLknCPa9TPtkY9yYs+0cnGGgfdkW0SV1Mlg+hVGfXcoI8d3MJA==", "signatures": [{"sig": "MEYCIQCYVjWn1kOlA4bx6hyS3rhyUmeoIGInWfmaKtkjO3b4hgIhAOQwwxTWdJhA8B68AJARpgy1+858yJRtTrUaRMl7gzEe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkI4qJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnGQ/8Ce/8N+XWCFb/HXEB04qKEORZpbLTo7CEiINjeUm/G3BUIkwR\r\n82pipWe7zyW12oJcUzCFC2UmKFkxGpUY0i4Sdh7QfjYJcgHo0tkGe6zvsFzy\r\nRLRFt8byPRcXNwY5rLYioTJEtTOl3HazLpppp9/r0NqLtVwTyO+1h8XxArso\r\nRSD3lrPK6ld5Ot7nYr+bKlzwvpNr23m/W3VqhEa4EuNWijwoNcLJUSjvmsAi\r\nGB8V814TD585VE0a2JvMIptwTnANITo6S+7w/CrOjO93iDJQtYLbzZSY4yYV\r\nly7Mzg0d6ljQS9cMqpIj3wfbSP0OScFv8an+kd9DJKfLHyzh502ZXJEIbLAF\r\nXCPjjsjyFzaVlgxfbg0FXHBEucHFoKlQdCXRVpjGlXd1U23HEJwPOh5W0e9x\r\nJT/ISwp3s318x4OuL4RSdZpU4aAdEVLemTB6/nm7i+l+nFWHDzo+StIivSiJ\r\neneaUIo5KIVFpByN04+l2ldFgVWkLBGAoR73kbnz//ggymWXjjLgWFAhKryb\r\nEgcmaKNPsMxif0S0UG6Pg9AEyBz52KB+lnQ/NlGyN3flfsJ7MLFr0FLB9YNv\r\nuGjAd0sB6x8xigvVFZoIALEM6X7tnj9+l1zpezS2XbVyKHtQGNfXI/wZ7xG2\r\nSyC1u7t/J0k6M0+8BQVgPfl0jbLv3llV0mk=\r\n=xRCk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-8.0.1.tgz", "types": "dist/src/index.d.ts", "engines": {"node": "^14.13.0 || >=16.0.0"}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/c8e145910d4a5d3b5a8f0a05f56a05dd/concurrently-8.0.1.tgz", "_integrity": "sha512-Sh8bGQMEL0TAmAm2meAXMjcASHZa7V0xXQVDBLknCPa9TPtkY9yYs+0cnGGgfdkW0SV1Mlg+hVGfXcoI8d3MJA==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "16.19.1", "dependencies": {"rxjs": "^7.8.0", "chalk": "^4.1.2", "yargs": "^17.7.1", "lodash": "^4.17.21", "date-fns": "^2.29.3", "tree-kill": "^1.2.2", "shell-quote": "^1.8.0", "spawn-command": "0.0.2-1", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "husky": "^8.0.3", "eslint": "^8.37.0", "esbuild": "~0.17.14", "prettier": "^2.8.7", "@swc/core": "^1.3.42", "@swc/jest": "^0.2.24", "typescript": "~5.0.2", "@types/jest": "^29.5.0", "@types/node": "^14.18.42", "lint-staged": "^13.2.0", "string-argv": "^0.3.1", "@types/yargs": "^17.0.24", "@types/lodash": "^4.14.192", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.0", "@types/shell-quote": "^1.7.1", "eslint-plugin-jest": "^27.2.1", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.27.5", "@types/supports-color": "^8.1.1", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "@typescript-eslint/parser": "^5.57.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.57.0", "eslint-plugin-simple-import-sort": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_8.0.1_1680050825603_0.662387640021419", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "concurrently", "version": "8.1.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@8.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "6363592e0cbc0c02169b035aa46f856c13993b38", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-8.1.0.tgz", "fileCount": 55, "integrity": "sha512-0AB6eOAtaW/r/kX2lCdolaWtT191ICeuJjEJvI9hT3zbPFuZ/iZaJwMRKwbuwADome7OKxk73L7od+fsveZ7tA==", "signatures": [{"sig": "MEUCIDRgQt6JfozF4VCXannS+EOWiGKy4ZEPnVuwgjGF+qc0AiEAp2/t/rKYe4IfgUc9FYHNIlwxPk316IzkKtE+2uw2Riw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120176}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-8.1.0.tgz", "types": "dist/src/index.d.ts", "engines": {"node": "^14.13.0 || >=16.0.0"}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/96a2b326eadb7a52cfc920fef672e4fc/concurrently-8.1.0.tgz", "_integrity": "sha512-0AB6eOAtaW/r/kX2lCdolaWtT191ICeuJjEJvI9hT3zbPFuZ/iZaJwMRKwbuwADome7OKxk73L7od+fsveZ7tA==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "16.20.0", "dependencies": {"rxjs": "^7.8.0", "chalk": "^4.1.2", "yargs": "^17.7.1", "lodash": "^4.17.21", "date-fns": "^2.29.3", "tree-kill": "^1.2.2", "shell-quote": "^1.8.0", "spawn-command": "0.0.2-1", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "husky": "^8.0.3", "eslint": "^8.37.0", "esbuild": "~0.17.14", "prettier": "^2.8.7", "@swc/core": "^1.3.42", "@swc/jest": "^0.2.24", "typescript": "~5.0.2", "@types/jest": "^29.5.0", "@types/node": "^14.18.42", "lint-staged": "^13.2.0", "string-argv": "^0.3.1", "@types/yargs": "^17.0.24", "@types/lodash": "^4.14.192", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.0", "@types/shell-quote": "^1.7.1", "eslint-plugin-jest": "^27.2.1", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.27.5", "@types/supports-color": "^8.1.1", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "@typescript-eslint/parser": "^5.57.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.57.0", "eslint-plugin-simple-import-sort": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_8.1.0_1685608264263_0.6945838137818454", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "concurrently", "version": "8.2.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@8.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "cdc9f621a4d913366600355d68254df2c5e782f3", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-8.2.0.tgz", "fileCount": 55, "integrity": "sha512-nnLMxO2LU492mTUj9qX/az/lESonSZu81UznYDoXtz1IQf996ixVqPAgHXwvHiHCAef/7S8HIK+fTFK7Ifk8YA==", "signatures": [{"sig": "MEYCIQCbqW96o8RUtM7tZYhn9fJW9KTdz5lNiyjlxAdSKLOcagIhANl58kD+HSimEo+uGdV7apx4WQGy1dLU+QvCz+MA3yVv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120621}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-8.2.0.tgz", "types": "dist/src/index.d.ts", "engines": {"node": "^14.13.0 || >=16.0.0"}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/d70cf681d9ef57ba56375af24545c80c/concurrently-8.2.0.tgz", "_integrity": "sha512-nnLMxO2LU492mTUj9qX/az/lESonSZu81UznYDoXtz1IQf996ixVqPAgHXwvHiHCAef/7S8HIK+fTFK7Ifk8YA==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "16.20.0", "dependencies": {"rxjs": "^7.8.1", "chalk": "^4.1.2", "yargs": "^17.7.2", "lodash": "^4.17.21", "date-fns": "^2.30.0", "tree-kill": "^1.2.2", "shell-quote": "^1.8.1", "spawn-command": "0.0.2", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "husky": "^8.0.3", "eslint": "^8.42.0", "esbuild": "~0.17.19", "prettier": "^2.8.8", "@swc/core": "^1.3.62", "@swc/jest": "^0.2.26", "typescript": "~5.1.3", "@types/jest": "^29.5.2", "@types/node": "^14.18.48", "lint-staged": "^13.2.2", "string-argv": "^0.3.2", "@types/yargs": "^17.0.24", "@types/lodash": "^4.14.195", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.0", "@types/shell-quote": "^1.7.1", "eslint-plugin-jest": "^27.2.1", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.27.5", "@types/supports-color": "^8.1.1", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "@typescript-eslint/parser": "^5.59.9", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.59.9", "eslint-plugin-simple-import-sort": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_8.2.0_1686301444083_0.555489737346871", "host": "s3://npm-registry-packages"}}, "8.2.1": {"name": "concurrently", "version": "8.2.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@8.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "bcab9cacc38c23c503839583151e0fa96fd5b584", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-8.2.1.tgz", "fileCount": 55, "integrity": "sha512-nVraf3aXOpIcNud5pB9M82p1tynmZkrSGQ1p6X/VY8cJ+2LMVqAgXsJxYYefACSHbTYlm92O1xuhdGTjwoEvbQ==", "signatures": [{"sig": "MEUCIQDISI0hNrbyi6JBHFkR9itAw7A590k74ls9BkbfYXyu/wIgLsp2P1rZ4WGENaFBvVDjXKV5n6AyJBPJfEGWa8o4Ctw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120999}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-8.2.1.tgz", "types": "dist/src/index.d.ts", "engines": {"node": "^14.13.0 || >=16.0.0"}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/dafbc3b7d0494f83ba5ea28417f0eafa/concurrently-8.2.1.tgz", "_integrity": "sha512-nVraf3aXOpIcNud5pB9M82p1tynmZkrSGQ1p6X/VY8cJ+2LMVqAgXsJxYYefACSHbTYlm92O1xuhdGTjwoEvbQ==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "16.20.2", "dependencies": {"rxjs": "^7.8.1", "chalk": "^4.1.2", "yargs": "^17.7.2", "lodash": "^4.17.21", "date-fns": "^2.30.0", "tree-kill": "^1.2.2", "shell-quote": "^1.8.1", "spawn-command": "0.0.2", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.6.3", "husky": "^8.0.3", "eslint": "^8.47.0", "esbuild": "~0.19.2", "prettier": "^3.0.2", "@swc/core": "^1.3.78", "@swc/jest": "^0.2.29", "typescript": "~5.1.6", "@types/jest": "^29.5.3", "@types/node": "^14.18.54", "lint-staged": "^13.3.0", "string-argv": "^0.3.2", "@types/yargs": "^17.0.24", "@types/lodash": "^4.14.197", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.0", "@types/shell-quote": "^1.7.1", "eslint-plugin-jest": "^27.2.3", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.28.1", "@types/supports-color": "^8.1.1", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "@typescript-eslint/parser": "^6.4.1", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^6.4.1", "eslint-plugin-simple-import-sort": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_8.2.1_1692689024585_0.9976203225621192", "host": "s3://npm-registry-packages"}}, "8.2.2": {"name": "concurrently", "version": "8.2.2", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@8.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "353141985c198cfa5e4a3ef90082c336b5851784", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-8.2.2.tgz", "fileCount": 55, "integrity": "sha512-1dP4gpXFhei8IOtlXRE/T/4H88ElHgTiUzh71YUmtjTEHMSRS2Z/fgOxHSxxusGHogsRfxNq1vyAwxSC+EVyDg==", "signatures": [{"sig": "MEYCIQDBWLTwD1abaLFkEF2jdseNAwN7ZZYBFMZDV+Gg6y3svgIhAJZRLQPwOZcNrpIxd0GTONRPFWEa8NA7dbavTYfiNeOR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121843}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-8.2.2.tgz", "types": "dist/src/index.d.ts", "engines": {"node": "^14.13.0 || >=16.0.0"}, "exports": {".": {"types": "./dist/src/index.d.ts", "import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/223f36f94a05b8fe1bdca708bdc30056/concurrently-8.2.2.tgz", "_integrity": "sha512-1dP4gpXFhei8IOtlXRE/T/4H88ElHgTiUzh71YUmtjTEHMSRS2Z/fgOxHSxxusGHogsRfxNq1vyAwxSC+EVyDg==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "20.8.1", "dependencies": {"rxjs": "^7.8.1", "chalk": "^4.1.2", "yargs": "^17.7.2", "lodash": "^4.17.21", "date-fns": "^2.30.0", "tree-kill": "^1.2.2", "shell-quote": "^1.8.1", "spawn-command": "0.0.2", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.7.0", "husky": "^8.0.3", "eslint": "^8.51.0", "esbuild": "~0.19.5", "prettier": "^3.0.3", "@swc/core": "^1.3.93", "@swc/jest": "^0.2.29", "typescript": "~5.2.2", "@types/jest": "^29.5.6", "@types/node": "^14.18.62", "lint-staged": "^13.3.0", "string-argv": "^0.3.2", "@types/yargs": "^17.0.29", "@types/lodash": "^4.14.200", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.0", "@types/shell-quote": "^1.7.3", "eslint-plugin-jest": "^27.4.2", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.28.1", "@types/supports-color": "^8.1.2", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "@typescript-eslint/parser": "^6.8.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^6.8.0", "eslint-plugin-simple-import-sort": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_8.2.2_1697679456021_0.32955543896165995", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "concurrently", "version": "9.0.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@9.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "b74e85bfbed9b920c54128f8cf7f91f23cc19112", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-9.0.0.tgz", "fileCount": 72, "integrity": "sha512-iAxbsDeUkn8E/4+QalT7T3WvlyTfmsoez+19lbbcsxZdOEMfBukd8LA30KYez2UR5xkKFzbcqXIZy5RisCbaxw==", "signatures": [{"sig": "MEQCIGzntp2tRyahOrBdtnY5n2xzVh3K7Pzu465ILggTlM6RAiAcM3M5rjdBu/a4/CQ1PaKLgkS31KjvxR4O00fMVFQ7fw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 401266}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-9.0.0.tgz", "types": "index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest --selectProjects unit", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "test:smoke": "jest --selectProjects smoke", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/dbd1cea7d00c4d0c2d03657bdf1ad925/concurrently-9.0.0.tgz", "_integrity": "sha512-iAxbsDeUkn8E/4+QalT7T3WvlyTfmsoez+19lbbcsxZdOEMfBukd8LA30KYez2UR5xkKFzbcqXIZy5RisCbaxw==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "20.17.0", "dependencies": {"rxjs": "^7.8.1", "chalk": "^4.1.2", "yargs": "^17.7.2", "lodash": "^4.17.21", "tree-kill": "^1.2.2", "shell-quote": "^1.8.1", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.9+sha256.daa27a0b541bc635323ff96c2ded995467ff9fe6d69ff67021558aa9ad9dcc36", "devDependencies": {"jest": "^29.7.0", "husky": "^9.1.5", "eslint": "^8.57.0", "esbuild": "~0.23.1", "prettier": "^3.0.3", "@swc/core": "^1.7.23", "@swc/jest": "^0.2.36", "typescript": "~5.2.2", "@jest/types": "^29.6.3", "@types/jest": "^29.5.6", "@types/node": "^18.19.50", "lint-staged": "^15.2.10", "string-argv": "^0.3.2", "@types/yargs": "^17.0.33", "@types/lodash": "^4.17.7", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.1", "@types/shell-quote": "^1.7.5", "eslint-plugin-jest": "^27.9.0", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.30.0", "@types/supports-color": "^8.1.3", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@typescript-eslint/parser": "^7.18.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "eslint-plugin-simple-import-sort": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_9.0.0_1725798477244_0.9071592760775973", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "concurrently", "version": "9.0.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@9.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "01e171bf6c7af0c022eb85daef95bff04d8185aa", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-9.0.1.tgz", "fileCount": 72, "integrity": "sha512-wYKvCd/f54sTXJMSfV6Ln/B8UrfLBKOYa+lzc6CHay3Qek+LorVSBdMVfyewFhRbH0Rbabsk4D+3PL/VjQ5gzg==", "signatures": [{"sig": "MEQCIDq3BppKNhcr701VZHoE3pEqVQ4Y6bWU4G7Lhpjnb5imAiBtTw7KeAmySE0RduRFW3IJyC+Ou64xKmotXac/7YhDqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 401478}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-9.0.1.tgz", "types": "index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest --selectProjects unit", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "test:smoke": "jest --selectProjects smoke", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/beefaf478320d2fd5cbcc283178d6caa/concurrently-9.0.1.tgz", "_integrity": "sha512-wYKvCd/f54sTXJMSfV6Ln/B8UrfLBKOYa+lzc6CHay3Qek+LorVSBdMVfyewFhRbH0Rbabsk4D+3PL/VjQ5gzg==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "20.17.0", "dependencies": {"rxjs": "^7.8.1", "chalk": "^4.1.2", "yargs": "^17.7.2", "lodash": "^4.17.21", "tree-kill": "^1.2.2", "shell-quote": "^1.8.1", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.9+sha256.daa27a0b541bc635323ff96c2ded995467ff9fe6d69ff67021558aa9ad9dcc36", "devDependencies": {"jest": "^29.7.0", "husky": "^9.1.5", "eslint": "^8.57.0", "esbuild": "~0.23.1", "prettier": "^3.0.3", "@swc/core": "^1.7.23", "@swc/jest": "^0.2.36", "typescript": "~5.2.2", "@jest/types": "^29.6.3", "@types/jest": "^29.5.6", "@types/node": "^18.19.50", "lint-staged": "^15.2.10", "string-argv": "^0.3.2", "@types/yargs": "^17.0.33", "@types/lodash": "^4.17.7", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.1", "@types/shell-quote": "^1.7.5", "eslint-plugin-jest": "^27.9.0", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.30.0", "@types/supports-color": "^8.1.3", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@typescript-eslint/parser": "^7.18.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "eslint-plugin-simple-import-sort": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_9.0.1_1726102043330_0.876947798047174", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "concurrently", "version": "9.1.0", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@9.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "8da6d609f4321752912dab9be8710232ac496aa0", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-9.1.0.tgz", "fileCount": 72, "integrity": "sha512-VxkzwMAn4LP7WyMnJNbHN5mKV9L2IbyDjpzemKr99sXNR3GqRNMMHdm7prV1ws9wg7ETj6WUkNOigZVsptwbgg==", "signatures": [{"sig": "MEYCIQDQN/GVW+w3vrXU2o5ZjN6Vjtep33+arAcoPagQFCyf5AIhALnTPvX82Hm1hTEeEe4g+XEGEvI+l6NnoG8VA79xTq+h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 404046}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-9.1.0.tgz", "types": "index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest --selectProjects unit", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "test:smoke": "jest --selectProjects smoke", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/40a985bfa93e6581d8d72ef5b4b6ba99/concurrently-9.1.0.tgz", "_integrity": "sha512-VxkzwMAn4LP7WyMnJNbHN5mKV9L2IbyDjpzemKr99sXNR3GqRNMMHdm7prV1ws9wg7ETj6WUkNOigZVsptwbgg==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "20.18.0", "dependencies": {"rxjs": "^7.8.1", "chalk": "^4.1.2", "yargs": "^17.7.2", "lodash": "^4.17.21", "tree-kill": "^1.2.2", "shell-quote": "^1.8.1", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.9+sha256.daa27a0b541bc635323ff96c2ded995467ff9fe6d69ff67021558aa9ad9dcc36", "devDependencies": {"jest": "^29.7.0", "husky": "^9.1.5", "eslint": "^8.57.0", "esbuild": "~0.23.1", "prettier": "^3.0.3", "@swc/core": "^1.7.23", "@swc/jest": "^0.2.36", "typescript": "~5.2.2", "@jest/types": "^29.6.3", "@types/jest": "^29.5.6", "@types/node": "^18.19.50", "lint-staged": "^15.2.10", "string-argv": "^0.3.2", "@types/yargs": "^17.0.33", "@types/lodash": "^4.17.7", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.1", "@types/shell-quote": "^1.7.5", "eslint-plugin-jest": "^27.9.0", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.30.0", "@types/supports-color": "^8.1.3", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@typescript-eslint/parser": "^7.18.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "eslint-plugin-simple-import-sort": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_9.1.0_1730722543831_0.4899962246904801", "host": "s3://npm-registry-packages"}}, "9.1.1": {"name": "concurrently", "version": "9.1.1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@9.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "609dde2ce12f4f12d6a5ea6eace4c38bb7ab2ead", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-9.1.1.tgz", "fileCount": 74, "integrity": "sha512-6VX8lrBIycgZKTwBsWS+bLrmkGRkDmvtGsYylRN9b93CygN6CbK46HmnQ3rdSOR8HRjdahDrxb5MqD9cEFOg5Q==", "signatures": [{"sig": "MEUCIQDD7JEC5A+joNwEuNCRXSuVzoxU9I1O+Lpbv5C0bakaBAIgaR8KFpRzXFCawRHAgM2PPwOv9peRH02cnH7RI9x3Ux8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405593}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-9.1.1.tgz", "types": "index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest --selectProjects unit", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "test:smoke": "jest --selectProjects smoke", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/6f0570c3e52a0f9e0963e1514fb0873a/concurrently-9.1.1.tgz", "_integrity": "sha512-6VX8lrBIycgZKTwBsWS+bLrmkGRkDmvtGsYylRN9b93CygN6CbK46HmnQ3rdSOR8HRjdahDrxb5MqD9cEFOg5Q==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "20.18.1", "dependencies": {"rxjs": "^7.8.1", "chalk": "^4.1.2", "yargs": "^17.7.2", "lodash": "^4.17.21", "tree-kill": "^1.2.2", "shell-quote": "^1.8.1", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.9+sha256.daa27a0b541bc635323ff96c2ded995467ff9fe6d69ff67021558aa9ad9dcc36", "devDependencies": {"jest": "^29.7.0", "husky": "^9.1.5", "eslint": "^8.57.0", "esbuild": "~0.23.1", "prettier": "^3.0.3", "@swc/core": "^1.7.23", "@swc/jest": "^0.2.36", "typescript": "~5.2.2", "@jest/types": "^29.6.3", "@types/jest": "^29.5.6", "@types/node": "^18.19.50", "lint-staged": "^15.2.10", "string-argv": "^0.3.2", "@types/yargs": "^17.0.33", "@types/lodash": "^4.17.7", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.1", "@types/shell-quote": "^1.7.5", "eslint-plugin-jest": "^27.9.0", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.30.0", "@types/supports-color": "^8.1.3", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@typescript-eslint/parser": "^7.18.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "eslint-plugin-simple-import-sort": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_9.1.1_1735299839388_0.9895852758479009", "host": "s3://npm-registry-packages-npm-production"}}, "9.1.2": {"name": "concurrently", "version": "9.1.2", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "concurrently@9.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/open-cli-tools/concurrently#readme", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "dist": {"shasum": "22d9109296961eaee773e12bfb1ce9a66bc9836c", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-9.1.2.tgz", "fileCount": 74, "integrity": "sha512-H9MWcoPsYddwbOGM6difjVwVZHl63nwMEwDJG/L7VGtuaJhb12h2caPG2tVPWs7emuYix252iGfqOyrz1GczTQ==", "signatures": [{"sig": "MEUCICbmyq1E9swcVrNzFBYrEILJbKBauyGxLSKhGM1Xt5YkAiEAkwrkiAqcLS6MTGL0lUwQYbpRV/dFlwF8NLjpoiICQGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405643}, "main": "index.js", "type": "commonjs", "_from": "file:concurrently-9.1.2.tgz", "types": "index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "scripts": {"lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "test": "jest --selectProjects unit", "build": "tsc --build", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "lint:fix": "pnpm run lint --fix", "postbuild": "chmod +x dist/bin/concurrently.js", "format:fix": "pnpm run format --write", "test:smoke": "jest --selectProjects smoke", "report-coverage": "cat coverage/lcov.info | coveralls"}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/ecf5fa78f5ad1e1cda55b6c2925c47df/concurrently-9.1.2.tgz", "_integrity": "sha512-H9MWcoPsYddwbOGM6difjVwVZHl63nwMEwDJG/L7VGtuaJhb12h2caPG2tVPWs7emuYix252iGfqOyrz1GczTQ==", "repository": {"url": "git+https://github.com/open-cli-tools/concurrently.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Run commands concurrently", "directories": {}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "_nodeVersion": "20.18.1", "dependencies": {"rxjs": "^7.8.1", "chalk": "^4.1.2", "yargs": "^17.7.2", "lodash": "^4.17.21", "tree-kill": "^1.2.2", "shell-quote": "^1.8.1", "supports-color": "^8.1.1"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.15.9+sha256.daa27a0b541bc635323ff96c2ded995467ff9fe6d69ff67021558aa9ad9dcc36", "devDependencies": {"jest": "^29.7.0", "husky": "^9.1.5", "eslint": "^8.57.0", "esbuild": "~0.23.1", "prettier": "^3.0.3", "@swc/core": "^1.7.23", "@swc/jest": "^0.2.36", "typescript": "~5.2.2", "@jest/types": "^29.6.3", "@types/jest": "^29.5.6", "@types/node": "^18.19.50", "lint-staged": "^15.2.10", "string-argv": "^0.3.2", "@types/yargs": "^17.0.33", "@types/lodash": "^4.17.7", "ctrlc-wrapper": "^0.0.4", "coveralls-next": "^4.2.1", "@types/shell-quote": "^1.7.5", "eslint-plugin-jest": "^27.9.0", "safe-publish-latest": "^2.0.0", "eslint-plugin-import": "^2.30.0", "@types/supports-color": "^8.1.3", "@hirez_io/observer-spy": "^2.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@typescript-eslint/parser": "^7.18.0", "jest-create-mock-instance": "^2.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "eslint-plugin-simple-import-sort": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/concurrently_9.1.2_1735618990173_0.299624386377221", "host": "s3://npm-registry-packages-npm-production"}}, "9.2.0": {"name": "concurrently", "version": "9.2.0", "description": "Run commands concurrently", "packageManager": "pnpm@8.15.9+sha256.daa27a0b541bc635323ff96c2ded995467ff9fe6d69ff67021558aa9ad9dcc36", "main": "index.js", "types": "index.d.ts", "type": "commonjs", "bin": {"concurrently": "dist/bin/concurrently.js", "conc": "dist/bin/concurrently.js"}, "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "git+https://github.com/open-cli-tools/concurrently.git"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "dependencies": {"chalk": "^4.1.2", "lodash": "^4.17.21", "rxjs": "^7.8.1", "shell-quote": "^1.8.1", "supports-color": "^8.1.1", "tree-kill": "^1.2.2", "yargs": "^17.7.2"}, "devDependencies": {"@hirez_io/observer-spy": "^2.2.0", "@jest/types": "^30.0.0", "@swc/core": "^1.7.23", "@swc/jest": "^0.2.36", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.7", "@types/node": "^18.19.50", "@types/shell-quote": "^1.7.5", "@types/supports-color": "^8.1.3", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "coveralls-next": "^4.2.1", "ctrlc-wrapper": "^0.0.4", "esbuild": "~0.25.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.30.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^10.0.0", "husky": "^9.1.5", "jest": "^30.0.0", "jest-create-mock-instance": "^2.0.0", "lint-staged": "^15.2.10", "prettier": "^3.0.3", "safe-publish-latest": "^2.0.0", "string-argv": "^0.3.2", "typescript": "~5.2.2"}, "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "scripts": {"build": "tsc --build", "postbuild": "chmod +x dist/bin/concurrently.js", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "format:fix": "pnpm run format --write", "lint": "eslint --ignore-path .gitignore --ext mjs,js,ts .", "lint:fix": "pnpm run lint --fix", "report-coverage": "cat coverage/lcov.info | coveralls", "test": "jest --selectProjects unit", "test:smoke": "jest --selectProjects smoke"}, "_id": "concurrently@9.2.0", "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "homepage": "https://github.com/open-cli-tools/concurrently#readme", "_integrity": "sha512-IsB/fiXTupmagMW4MNp2lx2cdSN2FfZq78vF90LBB+zZHArbIQZjQtzXCiXnvTxCZSvXanTqFLWBjw2UkLx1SQ==", "_resolved": "/tmp/2bcc4eee15b657839d9d39cc7462db7f/concurrently-9.2.0.tgz", "_from": "file:concurrently-9.2.0.tgz", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-IsB/fiXTupmagMW4MNp2lx2cdSN2FfZq78vF90LBB+zZHArbIQZjQtzXCiXnvTxCZSvXanTqFLWBjw2UkLx1SQ==", "shasum": "233e3892ceb0b5db9fd49e9c8c739737a7b638b5", "tarball": "https://registry.npmjs.org/concurrently/-/concurrently-9.2.0.tgz", "fileCount": 82, "unpackedSize": 416776, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIH1M+3tbv1Y7PzqM1xWzlRanbrPPDGdqjbeTl9sOthBRAiBEkU9U7BFSvshpaXS6NYTdEoYMw1AZHi+5fOI8DUKQ+A=="}]}, "_npmUser": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/concurrently_9.2.0_1750575242293_0.06237164475613155"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-02-07T23:20:06.011Z", "modified": "2025-06-22T06:54:02.724Z", "0.0.2": "2015-02-07T23:20:06.011Z", "0.0.3": "2015-02-07T23:22:04.342Z", "0.0.4": "2015-02-08T12:07:04.356Z", "0.0.5": "2015-02-08T12:11:11.872Z", "0.1.0": "2015-05-25T20:07:28.302Z", "0.1.1": "2015-05-27T13:28:24.906Z", "1.0.0": "2015-11-15T12:46:44.558Z", "2.0.0": "2016-02-16T22:17:32.438Z", "2.1.0": "2016-05-14T10:03:57.812Z", "2.2.0": "2016-07-05T09:53:32.327Z", "3.0.0-dev": "2016-09-18T22:11:52.833Z", "3.0.0-rc1": "2016-09-19T22:30:41.122Z", "3.0.0": "2016-09-26T09:42:57.562Z", "3.1.0": "2016-10-02T10:13:53.432Z", "3.2.0": "2017-02-08T23:47:27.450Z", "3.3.0": "2017-02-12T17:37:06.398Z", "3.4.0": "2017-03-02T03:27:38.062Z", "3.5.0": "2017-06-22T01:56:52.813Z", "3.5.1": "2017-11-21T01:04:20.254Z", "3.6.0": "2018-06-19T12:38:57.964Z", "3.6.1": "2018-07-24T13:32:28.931Z", "4.0.0": "2018-08-24T20:16:41.792Z", "4.0.1": "2018-08-26T15:27:43.429Z", "4.1.0": "2018-11-18T23:48:09.810Z", "4.1.1": "2019-06-25T20:47:48.253Z", "4.1.2": "2019-08-16T23:53:28.694Z", "5.0.0": "2019-10-07T02:35:31.311Z", "5.0.1": "2019-12-09T21:28:43.922Z", "5.0.2": "2019-12-18T02:29:09.851Z", "5.1.0": "2020-01-27T07:53:04.276Z", "5.2.0": "2020-04-25T04:07:09.174Z", "5.3.0": "2020-08-07T13:50:43.107Z", "6.0.0": "2021-02-20T10:35:55.263Z", "6.0.1": "2021-04-05T22:29:00.300Z", "6.0.2": "2021-04-12T22:57:25.328Z", "6.1.0": "2021-05-08T03:03:43.357Z", "6.2.0": "2021-05-24T10:45:22.097Z", "6.2.1": "2021-08-08T10:12:09.246Z", "6.2.2": "2021-09-27T22:53:42.690Z", "6.3.0": "2021-10-02T05:26:22.234Z", "6.4.0": "2021-11-13T01:11:53.624Z", "6.5.0": "2021-12-17T08:49:22.172Z", "6.5.1": "2021-12-19T07:13:06.401Z", "7.0.0": "2022-01-03T01:15:13.930Z", "7.1.0": "2022-04-02T09:55:38.348Z", "7.2.0": "2022-05-15T02:46:59.443Z", "7.2.1": "2022-05-22T22:28:51.814Z", "7.2.2": "2022-06-14T22:36:44.008Z", "7.3.0": "2022-07-18T23:47:30.213Z", "7.4.0": "2022-09-07T08:42:18.022Z", "7.5.0": "2022-10-22T05:53:23.676Z", "7.6.0": "2022-11-20T21:42:37.562Z", "8.0.0": "2023-03-28T22:49:38.702Z", "8.0.1": "2023-03-29T00:47:05.774Z", "8.1.0": "2023-06-01T08:31:04.438Z", "8.2.0": "2023-06-09T09:04:04.295Z", "8.2.1": "2023-08-22T07:23:44.734Z", "8.2.2": "2023-10-19T01:37:36.240Z", "9.0.0": "2024-09-08T12:27:57.501Z", "9.0.1": "2024-09-12T00:47:23.581Z", "9.1.0": "2024-11-04T12:15:44.195Z", "9.1.1": "2024-12-27T11:43:59.627Z", "9.1.2": "2024-12-31T04:23:10.412Z", "9.2.0": "2025-06-22T06:54:02.518Z"}, "bugs": {"url": "https://github.com/open-cli-tools/concurrently/issues"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "homepage": "https://github.com/open-cli-tools/concurrently#readme", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "repository": {"type": "git", "url": "git+https://github.com/open-cli-tools/concurrently.git"}, "description": "Run commands concurrently", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gust<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# concurrently\n\n[![Latest Release](https://img.shields.io/github/v/release/open-cli-tools/concurrently?label=Release)](https://github.com/open-cli-tools/concurrently/releases)\n[![License](https://img.shields.io/github/license/open-cli-tools/concurrently?label=License)](https://github.com/open-cli-tools/concurrently/blob/main/LICENSE)\n[![Weekly Downloads on NPM](https://img.shields.io/npm/dw/concurrently?label=Downloads&logo=npm)](https://www.npmjs.com/package/concurrently)\n[![CI Status](https://img.shields.io/github/actions/workflow/status/open-cli-tools/concurrently/test.yml?label=CI&logo=github)](https://github.com/open-cli-tools/concurrently/actions/workflows/test.yml)\n[![Coverage Status](https://img.shields.io/coveralls/github/open-cli-tools/concurrently/main?label=Coverage&logo=coveralls)](https://coveralls.io/github/open-cli-tools/concurrently?branch=main)\n\nRun multiple commands concurrently.\nLike `npm run watch-js & npm run watch-less` but better.\n\n![Demo](docs/demo.gif)\n\n**Table of Contents**\n\n- [concurrently](#concurrently)\n  - [Why](#why)\n  - [Installation](#installation)\n  - [Usage](#usage)\n  - [API](#api)\n    - [`concurrently(commands[, options])`](#concurrentlycommands-options)\n    - [`Command`](#command)\n    - [`CloseEvent`](#closeevent)\n  - [FAQ](#faq)\n\n## Why\n\nI like [task automation with npm](https://web.archive.org/web/20220531064025/https://github.com/substack/blog/blob/master/npm_run.markdown)\nbut the usual way to run multiple commands concurrently is\n`npm run watch-js & npm run watch-css`. That's fine but it's hard to keep\non track of different outputs. Also if one process fails, others still keep running\nand you won't even notice the difference.\n\nAnother option would be to just run all commands in separate terminals. I got\ntired of opening terminals and made **concurrently**.\n\n**Features:**\n\n- Cross platform (including Windows)\n- Output is easy to follow with prefixes\n- With `--kill-others` switch, all commands are killed if one dies\n\n## Installation\n\n**concurrently** can be installed in the global scope (if you'd like to have it available and use it on the whole system) or locally for a specific package (for example if you'd like to use it in the `scripts` section of your package):\n\n|             | npm                     | Yarn                           | pnpm                       | Bun                       |\n| ----------- | ----------------------- | ------------------------------ | -------------------------- | ------------------------- |\n| **Global**  | `npm i -g concurrently` | `yarn global add concurrently` | `pnpm add -g concurrently` | `bun add -g concurrently` |\n| **Local**\\* | `npm i -D concurrently` | `yarn add -D concurrently`     | `pnpm add -D concurrently` | `bun add -d concurrently` |\n\n<sub>\\* It's recommended to add **concurrently** to `devDependencies` as it's usually used for developing purposes. Please adjust the command if this doesn't apply in your case.</sub>\n\n## Usage\n\n> **Note**\n> The `concurrently` command is also available under the shorthand alias `conc`.\n\nThe tool is written in Node.js, but you can use it to run **any** commands.\n\nRemember to surround separate commands with quotes:\n\n```bash\nconcurrently 'command1 arg' 'command2 arg'\n```\n\nOtherwise **concurrently** would try to run 4 separate commands:\n`command1`, `arg`, `command2`, `arg`.\n\nIn package.json, escape quotes:\n\n```bash\n\"start\": \"concurrently 'command1 arg' 'command2 arg'\"\n```\n\nYou can always check concurrently's flag list by running `concurrently --help`.\nFor the version, run `concurrently --version`.\n\nCheck out documentation and other usage examples in the [`docs` directory](./docs/README.md).\n\n## API\n\n**concurrently** can be used programmatically by using the API documented below:\n\n### `concurrently(commands[, options])`\n\n- `commands`: an array of either strings (containing the commands to run) or objects\n  with the shape `{ command, name, prefixColor, env, cwd, ipc }`.\n\n- `options` (optional): an object containing any of the below:\n  - `cwd`: the working directory to be used by all commands. Can be overridden per command.\n    Default: `process.cwd()`.\n  - `defaultInputTarget`: the default input target when reading from `inputStream`.\n    Default: `0`.\n  - `handleInput`: when `true`, reads input from `process.stdin`.\n  - `inputStream`: a [`Readable` stream](https://nodejs.org/dist/latest-v10.x/docs/api/stream.html#stream_readable_streams)\n    to read the input from. Should only be used in the rare instance you would like to stream anything other than `process.stdin`. Overrides `handleInput`.\n  - `pauseInputStreamOnFinish`: by default, pauses the input stream (`process.stdin` when `handleInput` is enabled, or `inputStream` if provided) when all of the processes have finished. If you need to read from the input stream after `concurrently` has finished, set this to `false`. ([#252](https://github.com/kimmobrunfeldt/concurrently/issues/252)).\n  - `killOthersOn`: once the first command exits with one of these statuses, kill other commands.\n    Can be an array containing the strings `success` (status code zero) and/or `failure` (non-zero exit status).\n  - `maxProcesses`: how many processes should run at once.\n  - `outputStream`: a [`Writable` stream](https://nodejs.org/dist/latest-v10.x/docs/api/stream.html#stream_writable_streams)\n    to write logs to. Default: `process.stdout`.\n  - `prefix`: the prefix type to use when logging processes output.\n    Possible values: `index`, `pid`, `time`, `command`, `name`, `none`, or a template (eg `[{time} process: {pid}]`).\n    Default: the name of the process, or its index if no name is set.\n  - `prefixColors`: a list of colors or a string as supported by [chalk](https://www.npmjs.com/package/chalk) and additional style `auto` for an automatically picked color.\n    If concurrently would run more commands than there are colors, the last color is repeated, unless if the last color value is `auto` which means following colors are automatically picked to vary.\n    Prefix colors specified per-command take precedence over this list.\n  - `prefixLength`: how many characters to show when prefixing with `command`. Default: `10`\n  - `raw`: whether raw mode should be used, meaning strictly process output will\n    be logged, without any prefixes, coloring or extra stuff. Can be overridden per command.\n  - `successCondition`: the condition to consider the run was successful.\n    If `first`, only the first process to exit will make up the success of the run; if `last`, the last process that exits will determine whether the run succeeds.\n    Anything else means all processes should exit successfully.\n  - `restartTries`: how many attempts to restart a process that dies will be made. Default: `0`.\n  - `restartDelay`: how many milliseconds to wait between process restarts. Default: `0`.\n  - `timestampFormat`: a [Unicode format](https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table)\n    to use when prefixing with `time`. Default: `yyyy-MM-dd HH:mm:ss.SSS`\n  - `additionalArguments`: list of additional arguments passed that will get replaced in each command. If not defined, no argument replacing will happen.\n\n> **Returns:** an object in the shape `{ result, commands }`.\n>\n> - `result`: a `Promise` that resolves if the run was successful (according to `successCondition` option),\n>   or rejects, containing an array of [`CloseEvent`](#CloseEvent), in the order that the commands terminated.\n> - `commands`: an array of all spawned [`Command`s](#Command).\n\nExample:\n\n```js\nconst concurrently = require('concurrently');\nconst { result } = concurrently(\n  [\n    'npm:watch-*',\n    { command: 'nodemon', name: 'server' },\n    { command: 'deploy', name: 'deploy', env: { PUBLIC_KEY: '...' } },\n    {\n      command: 'watch',\n      name: 'watch',\n      cwd: path.resolve(__dirname, 'scripts/watchers'),\n    },\n  ],\n  {\n    prefix: 'name',\n    killOthers: ['failure', 'success'],\n    restartTries: 3,\n    cwd: path.resolve(__dirname, 'scripts'),\n  },\n);\nresult.then(success, failure);\n```\n\n### `Command`\n\nAn object that contains all information about a spawned command, and ways to interact with it.<br>\nIt has the following properties:\n\n- `index`: the index of the command among all commands spawned.\n- `command`: the command line of the command.\n- `name`: the name of the command; defaults to an empty string.\n- `cwd`: the current working directory of the command.\n- `env`: an object with all the environment variables that the command will be spawned with.\n- `killed`: whether the command has been killed.\n- `state`: the command's state. Can be one of\n  - `stopped`: if the command was never started\n  - `started`: if the command is currently running\n  - `errored`: if the command failed spawning\n  - `exited`: if the command is not running anymore, e.g. it received a close event\n- `pid`: the command's process ID.\n- `stdin`: a Writable stream to the command's `stdin`.\n- `stdout`: an RxJS observable to the command's `stdout`.\n- `stderr`: an RxJS observable to the command's `stderr`.\n- `error`: an RxJS observable to the command's error events (e.g. when it fails to spawn).\n- `timer`: an RxJS observable to the command's timing events (e.g. starting, stopping).\n- `stateChange`: an RxJS observable for changes to the command's `state` property.\n- `messages`: an object with the following properties:\n\n  - `incoming`: an RxJS observable for the IPC messages received from the underlying process.\n  - `outgoing`: an RxJS observable for the IPC messages sent to the underlying process.\n\n  Both observables emit [`MessageEvent`](#messageevent)s.<br>\n  Note that if the command wasn't spawned with IPC support, these won't emit any values.\n\n- `close`: an RxJS observable to the command's close events.\n  See [`CloseEvent`](#CloseEvent) for more information.\n- `start()`: starts the command and sets up all of the above streams\n- `send(message[, handle, options])`: sends a message to the underlying process via IPC channels,\n  returning a promise that resolves once the message has been sent.\n  See [Node.js docs](https://nodejs.org/docs/latest/api/child_process.html#subprocesssendmessage-sendhandle-options-callback).\n- `kill([signal])`: kills the command, optionally specifying a signal (e.g. `SIGTERM`, `SIGKILL`, etc).\n\n### `MessageEvent`\n\nAn object that represents a message that was received from/sent to the underlying command process.<br>\nIt has the following properties:\n\n- `message`: the message itself.\n- `handle`: a [`net.Socket`](https://nodejs.org/docs/latest/api/net.html#class-netsocket),\n  [`net.Server`](https://nodejs.org/docs/latest/api/net.html#class-netserver) or\n  [`dgram.Socket`](https://nodejs.org/docs/latest/api/dgram.html#class-dgramsocket),\n  if one was sent, or `undefined`.\n\n### `CloseEvent`\n\nAn object with information about a command's closing event.<br>\nIt contains the following properties:\n\n- `command`: a stripped down version of [`Command`](#command), including only `name`, `command`, `env` and `cwd` properties.\n- `index`: the index of the command among all commands spawned.\n- `killed`: whether the command exited because it was killed.\n- `exitCode`: the exit code of the command's process, or the signal which it was killed with.\n- `timings`: an object in the shape `{ startDate, endDate, durationSeconds }`.\n\n## FAQ\n\n- Process exited with code _null_?\n\n  From [Node child_process documentation](http://nodejs.org/api/child_process.html#child_process_event_exit), `exit` event:\n\n  > This event is emitted after the child process ends. If the process\n  > terminated normally, code is the final exit code of the process,\n  > otherwise null. If the process terminated due to receipt of a signal,\n  > signal is the string name of the signal, otherwise null.\n\n  So _null_ means the process didn't terminate normally. This will make **concurrently**\n  to return non-zero exit code too.\n\n- Does this work with the npm-replacements [yarn](https://github.com/yarnpkg/yarn), [pnpm](https://pnpm.js.org/), or [Bun](https://bun.sh/)?\n\n  Yes! In all examples above, you may replace \"`npm`\" with \"`yarn`\", \"`pnpm`\", or \"`bun`\".\n", "readmeFilename": "README.md", "users": {"j_n": true, "mgol": true, "tztz": true, "bengi": true, "bjmin": true, "jalik": true, "kikna": true, "lavir": true, "lgh06": true, "lokua": true, "majac": true, "razr9": true, "xrush": true, "xyyjk": true, "71emj1": true, "ayyash": true, "daizch": true, "dgmike": true, "dkblay": true, "evan2x": true, "evangu": true, "h0ward": true, "haibxz": true, "ilex.h": true, "inoder": true, "johsew": true, "jsalis": true, "lestad": true, "mcacek": true, "naokie": true, "nchase": true, "noccer": true, "noyobo": true, "quafoo": true, "somlor": true, "womjoy": true, "xx1196": true, "askyous": true, "aslezak": true, "enhezzz": true, "ezeikel": true, "fallion": true, "fdaciuk": true, "gpuente": true, "juanf03": true, "keenwon": true, "kontrax": true, "laoshaw": true, "mkiramu": true, "mu57di3": true, "pasturn": true, "restuta": true, "rparris": true, "sirreal": true, "tdreitz": true, "teabyii": true, "thiagoh": true, "tsxuehu": true, "ungurys": true, "vdsabev": true, "walaura": true, "working": true, "bcowgi11": true, "bpolonia": true, "buru1020": true, "cfguidry": true, "dbuggerx": true, "drinchev": true, "dtiziani": true, "elmarini": true, "faeliaso": true, "gavatron": true, "gramotei": true, "juandaco": true, "korynunn": true, "neonleif": true, "niksudan": true, "rbartoli": true, "rybridge": true, "shaddyhm": true, "swttfam3": true, "thomasfr": true, "winsonwq": true, "ycjcl868": true, "alex-cory": true, "alexxnica": true, "animabear": true, "hawkrives": true, "heartnett": true, "largepuma": true, "mikestaub": true, "qqcome110": true, "raschdiaz": true, "rubiadias": true, "shadowwzw": true, "tomwayson": true, "wukaidong": true, "ashish.npm": true, "axelrindle": true, "cfleschhut": true, "jakewlacey": true, "jaredreich": true, "kasiriveni": true, "leizongmin": true, "leonardorb": true, "mihokrusic": true, "monolithed": true, "pantyuhind": true, "ridermansb": true, "rocket0191": true, "ta2edchimp": true, "vitorluizc": true, "wedneyyuri": true, "willaustin": true, "albertofdzm": true, "arturmuller": true, "candybubuyu": true, "flumpus-dev": true, "hal9zillion": true, "iandstanley": true, "jakeklassen": true, "jamesbedont": true, "kevin-smets": true, "leelee.echo": true, "mrmartineau": true, "paulhanna33": true, "pmcalabrese": true, "scotchulous": true, "wangnan0610": true, "adrian110288": true, "bianlongting": true, "cameronnokes": true, "ebrahimiaval": true, "hugojosefson": true, "ivan.marquez": true, "shaomingquan": true, "sundaycrafts": true, "wangrongding": true, "wesleylhandy": true, "will.nielsen": true, "yonigoldberg": true, "andrewbaisden": true, "augiethornton": true, "brettcelestre": true, "miguel_fontes": true, "scottfreecode": true, "sir_moustache": true, "vladimirkazan": true, "yinyongcom666": true, "augusto.altman": true, "julianopadilha": true, "thevikingcoder": true, "yazanrawashdeh": true, "icodeforcookies": true, "joaquin.briceno": true, "ognjen.jevremovic": true}}