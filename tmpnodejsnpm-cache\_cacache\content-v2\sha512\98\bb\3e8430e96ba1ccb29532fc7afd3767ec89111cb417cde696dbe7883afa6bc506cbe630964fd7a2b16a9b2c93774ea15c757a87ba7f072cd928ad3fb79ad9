{"_id": "@rollup/rollup-linux-arm-gnueabihf", "_rev": "154-fe3b9d6147b1447040bc9a9b2c8d5bc3", "name": "@rollup/rollup-linux-arm-gnueabihf", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.2"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-0", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "0978e14bcf0f0e7086ba5e6b2a8ddba21d62bb8a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-0.tgz", "fileCount": 2, "integrity": "sha512-y+hzUn/UWjzRNYHa6hyNADgKJWfX9OEE70AIMpKnfU2ARcyWjNElSo9MmMtEIHiaT3h71Np9VtB1OPPHFoLkjQ==", "signatures": [{"sig": "MEQCIELDlokpZcwn1gPK6Ib1sjoSy6+58mfZy55wjgfugjNDAiAF7fW82iXyZTRTCloXoBAIbX7Xk5CNzZy5ePKEvCpTDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 751}, "main": "native/rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Next-generation ES module bundler", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-0_1690831079829_0.6649920679226307", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "80a56800967ae54c62ad634805cbc930e8919abe", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-x9/apQzG25cKWRcvSMg+9mfqScsEccIAn9ULySsRVhbbUE+9LYXjvjvD3RqRoj3gjlOhTAzUoZzMtrTEXatcig==", "signatures": [{"sig": "MEUCIQC7OWWXlXcHkshZDteCwuWl38g8gtumZ+aUfA6AzegBAwIgLKNUtlbSqSl3MifZO/DPTX5+wWSqUkOF5g+WbcWle0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2220124}, "main": "rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-1_1690865344394_0.04627557070218824", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a10b5fb2826d3d81ff1fe744b3b7fafea317e8ea", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-Ypmc6jJYR8fXxpQURS5JZs7csCV/Cir3p8VjOZDi2rlz8kwfyKYaYx84RxtpcKuEA+5nLgazRR/kuXgDmNN+Kw==", "signatures": [{"sig": "MEUCIC+HXN+pvA6V5qKHegIVyQSROCfDRI/HCNOMkOPP1NCHAiEAxpQdQMrRr7MvozaTnV8BMCDriXsJ2QLg2p91L1WRX7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2220124}, "main": "rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-2_1690888597874_0.937649888240528", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3fa8a7ad83f616f0a3b2d1f909ca3d7a4ef71e42", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-E+L49M38hWcv6F6b1akT5w1nWgQMi8Gm0yz2iBE2W3CROvELQteUv1a7JcvIHhwkIkJHoQ3ne/UvacA+RtaIfQ==", "signatures": [{"sig": "MEYCIQDN/1RB9w8NmNmDB8TGWpMBIrihcoYadc3OElZBorCUKQIhAMBta4Feyjy0Ncc2XTtYkPOXT1ZW/QzZ+XUz+cuKpsbX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2207836}, "main": "rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-3_1691137024175_0.34595038240705933", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "450246c104511ab0ccb080756a38e0bcb886c33d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-D7ZvxWQAfdHNw2KgNup3g4SOwRTMUs6h5bs/shzT9716lVPoLGgjCISopZ8OhxIh0UaXUXwKjb4w8QnUQO2U5A==", "signatures": [{"sig": "MEUCIQCfhdV0HTUK1icInT9GPjzOCOmEBHmwlSI8hXMckSWSkwIgczbnSMi9ba/yiKS2qu7lHQdpCDJrPYl53+0Tnug9lnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2207940}, "main": "rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-4_1691149008267_0.17232598014163014", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b046fb833e3842d7407816e62add41174acd10b8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-vkU+03jm01e15muBrLUVx0plcAiT7xov03RNfEjpJfH+oe9K7/zl9YHIlTRzTOskYP/4iy6GW8D6oLNGfNJ1Tg==", "signatures": [{"sig": "MEUCIQDLpxfWRx/+ZpVs4Yxzw8tx2Gq0lYvU9Id6b7u/27uHYwIgNodYxd8pqxczA0zZjxfHNW/Qlktsqja8NaW3F+21lKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2517763}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm-gnueabihf.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-5_1692514615377_0.30413845241138904", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "756455bea1c330635690335d9662bb0ab186284c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-4VPd3Rjepu1g6HYUt+DP/LkCPtHqbhJq/ZvnhEWY19UK/my3yeXqpwPS9JsRW2Jqzup2FMFfOyyTNnobWiY+Pg==", "signatures": [{"sig": "MEUCIQDY5JdPaUGNKiMUlXPa7P6/wZMof/yL6Vmqay6G8sswrAIga+uFTPqinUOfot2Nkhx6CgVngo65erE33gRqUU5TCiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2517763}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm-gnueabihf.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-6_1692517903836_0.38607274654745694", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "2ac7e9e4201a59373ce108e4fc8f352e3d00d536", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-ZMVVPDmSpyXmmMJpmBA+3uIEfRkphbAzxpU21aufMLBoCFmYRHkVypKpN+WBzsnZy/f4YuEDXXV7Pq91P3kZnA==", "signatures": [{"sig": "MEUCIQCBaSiEJN7fOHStZoThskdW7aeCdN/Y8InAd+Mc5yMDEgIgDgTMCF9m/sNc1tYUt1aIvuKiYNyqEUZY2zoLISMaPE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7972685}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm-gnueabihf.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-7_1692527617896_0.15356850562542523", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "499d5e082ff90386372fddbf3c2fbc92ecc8bf9b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-vZ9hbXj6ThodX1FxpTAQBQTSP+Ft8yHUgTy1Jhx36hFmIyTNRyB4LZaj+wS2kaglJvzkTQGY3JjtYQSUrh/toA==", "signatures": [{"sig": "MEQCIHXQ+RiweFOaI4nZxoFoPYiulIxmF67R2PJB/UiVsukuAiB20RAOqgMfHKULX59YtRs1FY+09UhZi3PbGYwCSq96lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7972685}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm-gnueabihf.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-8_1692530550089_0.8381338745716145", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d31b93a244e4fe47ef9d018cfffa40d753e4b031", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-e2wVQ1dhgH5sn55lLK/MWkSOx1z3snGWLpNRITB0oSXuHC9iop/i55mImBI+ZqZvqzi+blGrt+cr1hmOkPXLMQ==", "signatures": [{"sig": "MEQCIC3RL3SJxisd92gKsSxsxfpFN8HG8mY77VGxXdJkZAKuAiBQJJ4IFV1sd1tWsujKVB0s7+TUpxRuSRiXE/SR8ExdTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7972685}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm-gnueabihf.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-9_1692541755839_0.8247600101312413", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7fb16fcd2f0883efdb4de8f38316e4e7c660a65f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-bdbiEV08FM6RxP9wmoUcqVaKKtU0XsPMeYNBIv4MfuCcJHJ85WQhRIQmS4iBUTn66/5JAX1rj8tx16ttRnVV7g==", "signatures": [{"sig": "MEUCIQCw7orUJhyTh+/pIp80VDH/1VUReHWM9Aw/ug7C3xEQygIgf/3YxOK+8E4ib0r7tK14v28GAgQ7RpjYDg6s6Du/g1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7978745}, "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm-gnueabihf.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-10_1692631814310_0.06295088801230553", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "45d796e210977babf4dbfa636447f2c752061b74", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-D6IsBaSAtC86EiRQ8wdOjmnHgt8pScwdu5tQ+8Ebw/WpvlfEhu4g9bsy4IBe9gqlrtu9dLPU6YFc6p/7SkSanw==", "signatures": [{"sig": "MEQCIHGUfNflIhD74ma5ZpojqokU1davxgo+EUQsmRX22krHAiAq0VrW+kHXIAMAELzZwiGBNVs3u9vblvah3sTD0cFHXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2515924}, "main": "./rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-11_1692785765240_0.5641483280688846", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "cf054f754af495bfc8a0bf6639a04723d386a1c5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-rfC1da7VMhsBbSx6NKQ7IzuHwaPZll31DRYk5eOaK4BSlGQy7YBTQ2GEQmPjMnTxBBP2GnXhYsyB7NPEIPFAXA==", "signatures": [{"sig": "MEUCIQDH03OO7ux+CmyryDpY86ovzN8o2/grxHJAe4DfKE5JvwIgNkQIsXtCoags7HsM938KbhXaBc9CTEkAW+5WauiKMho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2515764}, "main": "./rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-12_1692801638332_0.23493739706684624", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a5f2c2bca50d70c2e0cf1447a8aa06b2581b8c7e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-mHCAi74s6+4ootYWqO1SQGMz/y+6Z4KN0fLmeG47ePXgRxDrneYjxIGuQtkff87OI4lna5TqpRoMnR0SxxfxiQ==", "signatures": [{"sig": "MEUCIQCjXjuABN8/jRDfoStYgcmwN4J93RzXhmICU30Y/4YhkgIgMq/jChunmDRP9H22s27Q546h1uGkw0nSXpZVaimrT4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2520636}, "main": "./rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-13_1692892121546_0.9022026737571374", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d64b1f513363950f41f6b8da359fe95ddef1d0f7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-AjjQiE3OyeMO3pX/2qaHkVk/CKjkcAlsoB3XTxYV+E4aftih6hhlW/O1n6RYj7hksegb92vGfPS90SwUZCnzAg==", "signatures": [{"sig": "MEQCIHt8nVcxePLkqUMf67RHh9gora/k7ZHlHe5BJwPoZCVmAiArx8DYZ8Ju8hirdiKJoSRMGTN2RxPrg/ijrcjAHl6HvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2480968}, "main": "./rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-14_1694781272010_0.5283507753090937", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4689d01f364ab461d2028874211fc41bffe6bc9e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-/ztJ0c3uLo5rzwV522KJ62z2vkxk0E1KMpDnKAJu7MslQ/gF9Hyt35VJPvX9oVqhav4zE93d+HRfMtTRMoGl6w==", "signatures": [{"sig": "MEUCIEykbsWivqLEbs753HUgUEXpQmpOaF2hR4Vn7D32sn4IAiEAtdL7s7wKk+QkZEQSFuaG+bDs/XIN9R5FlUJBIDpeSGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2480968}, "main": "./rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-15_1694783219230_0.09950178386366182", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "ee7638fadd3eebce6ca8656957e61f2379e27e06", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-B8MDKF9h1yv1yS2HYM2alD/aYDevE/wP952FDnaBztYFm2ie3IKm1O0OoVUYHfqtzVYHeTSrkgDVlWeqWelJdw==", "signatures": [{"sig": "MEUCICy8oou8bmpY1htIWF8o21vOlqtnmjXEiTWXWjcFQyQgAiEA4nv9uUCdPx7IEENEDtCBt/4k686aA35rPPFlAb9yoJI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2480968}, "main": "./rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-16_1694787443431_0.35341069284369375", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "877222a972b8b9c850371ddd95a977c263f31d71", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-mR0EM+mJILHUrQUqI5rjU1C5IFjRPqLjeurTewwMXJCitpirjYipbXTeNOMd7hSkPEQ5+D/mR8okCFXEoLrwpA==", "signatures": [{"sig": "MEUCIQDRcXPPMcNI2nUM0pGajCc1lv5YhJpdhcg/C3yXgI8KtwIgKOHwdshBQURGfdIaoWwSyYl4lVf+tVdfYmls0JuKvbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2480968}, "main": "./rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-17_1694789953786_0.43829360897069236", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7d32b9ab02945667a9078b7fcc9d3046d859cd4e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-wsiOq6034yFnmHUlC40IM6op42coU1XzGJ5EBeUfVvKxKH4X5Ua7pVKJgQHMWiXYX07ld9PxvLe5R/LfardcWw==", "signatures": [{"sig": "MEUCIFvtUgrMKlHzeCVPUv4uk9QUfPtIcoX6cSkn7qkmKAk/AiEAlcC/g+zfB+tUx2FMeV0xUxXhJX0uGR41oUv9N7nVcbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2480968}, "main": "./rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-18_1694794220619_0.4279643332452405", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "acf1fc53c42b0d14e685a69beb26f5ef73d5543d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-k9ebVTIgr2pMgsF8V+OGz0h61F9f7rRDn1cm3zoBC+d8g1Y6SoHy8Tvb2Am2JOAB49a4LWL9bUTWyONIKM8Q6Q==", "signatures": [{"sig": "MEYCIQCAHnqNbCPj0TT/gnEeUu9ZcfaFSbixryZCms9F39092wIhAMiej0QZb4m7Oa6653LlM0mVlt5Lko2xxmfol9liluKD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2480968}, "main": "./rollup.linux-arm-gnueabihf.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-19_1694803864613_0.40713397640980875", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "85b99f084b50e1738fcc4de27a6e7a724581099e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-4BXS+Vs1onieo2k+y8VaUASQ/5JhZwrVZC6FhPwkTsAxvQaxeh/6qV8tlgSs8DlSPDBh4J8ST8aEbPVHO/conw==", "signatures": [{"sig": "MEQCIHc7laW3U87wynIVJaHqzKtlWsZowfKdhlN/QuENTJJ4AiBS6NZF89UrFiGTLpXCygOl36GHY4p2V/U0/H3S/HbngA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2488326}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-20_1695535844153_0.22027562026956593", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "8cb1e185e925abd801cbae6161d73e9036678f10", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-sFfI+B/UKKTzRmLryIf+yzvcCKf6wFSGW0NKTR/VL6SQvoEhrF37PiqtuL1mR0mOF6+rVKOHyTW7ypovx/4kWA==", "signatures": [{"sig": "MEUCIQCG4Q3bHuflvrPZJR+qlMd0OT1h+zzj7pgRmsdOTUHEkwIgOSWoOjleHU8d4JnpXr09nDxhAKsAouPfK5/2BtO1FJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2488326}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-21_1695576150975_0.043743380737879756", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "093124fa8baf2ec126fe51111f4406fda4f1cea2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-JjryELLVYI08DjSYJdhe30qqLdyOPmhdLdN0A+xvDsJR4BAgmP8OaMNb3XNdAV9KiskfizzQa2k0RZb135v3Rg==", "signatures": [{"sig": "MEQCIFZWLKvFcPoZr3XktjK6zgDXB7O+pkC9ayxeRoPoCtoJAiAL0lLmnS8HEFFtnJRFARSaCNV2Zf2mR3AQ/gCGaD0veQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481706}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-22_1695745057193_0.9355058212569654", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "75986351edb1d7f77b12e83047e2e4443a934678", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-+eCcJMaejbxvKSBTGMAlYVRk/qYnKENJUvrDVSQGALZPuqALWgI7kdI/MJkfV4aUTyvf5ejb5lkX6/wPoMp38w==", "signatures": [{"sig": "MEQCIDwGfaqIS9C9m4ZYoLzQXjF+iaIeFvfANZq6ukYab+qKAiAz/5d/HuDXfgU3Zqgxu7/2nvvFdxdgb7tBcAkQb1QepQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481690}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-23_1695759270682_0.6802165253113894", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f23b8fe252596d2e506af5a8de338bb06a6f91fd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-ZCNBOaw2NV3BnpQ049VCPJSamss3wAoCunFcWYfhWgGyu9C0hiRvZAcKvhd7e/9EhuoIxsNxMLwI46NmZx9WBQ==", "signatures": [{"sig": "MEQCIBnkwgsBzuFdYe0bWfSNjji+SBdqcPD9Iyx2F63qlwasAiBaTfQu4wTzXXwJK4etWUkmOGxmYQRv313bO3uxQPh9dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2523150}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-24_1696309973713_0.11454460492170448", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "853682168901dd7635ad6b5e169ff5812bba4807", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-R5A4bpLGrADbThCVrn9kNsyw6hrYAiWiGBVw+5ZPo2GdwBMmFXEt0RagPgKU+eu3RTEYm08XzmcNKA95sPg7pw==", "signatures": [{"sig": "MEUCIQCUH6N+qPCJG2xNsVaaXmoVaaMjZZehaU2t5oRhZb+PGgIgHuztPOA5rpw2vkIxdQxa42p+7arDoMfFSMEHN3vW+P0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2521966}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0-25_1696515185515_0.17811082224148045", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "fb6d797c78701f9e95361587ac22d58a5a896faf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-W4Elp0SGWqWOkdgoYniOp6ERrhHYRfMPikUZmnU/kAdLXQ9p0M0meF648Z6Y7ClHJr8pIQpcCdmr7E2h8Kn7Fw==", "signatures": [{"sig": "MEQCIEEYFvoOK5YGGsq9GKADm2K6gCV4ZBIRvKnasholYaQTAiBw08d7ToyHVFeBxYhHbhTXU1a3+ePvkqMX7HSCv1cFOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2521963}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.0_1696518887642_0.512493693198562", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "0d742738db6a14d8a85c7d601d15e61544c73335", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-UIvgXnd1Y5r9JiYzd1O8+rmyX+xfzNI04wHU7xhkJOKPTopia2NPV/vmyLokgjVffwY9kjfB8a9ZC+1ApGR9GA==", "signatures": [{"sig": "MEUCIQDKRorhoCQMtmV9xom/s+QFyrleqaOhCcYQFrV/F8/ybwIgdDHmsdsXpbiMSVW6OPdyMmpaoEBqBxROIsU4O6H7pRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2510459}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.1_1696595811840_0.4609669875383553", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4797cb038d0cb4beecaa56fdb1769aac4253b02a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-DVFIfcHOjgmeHOAqji4xNz2wczt1Bmzy9MwBZKBa83SjBVO/i38VHDR+9ixo8QpBOiEagmNw12DucG+v55tCrg==", "signatures": [{"sig": "MEUCIFzsW0Vm3TAhNeCyhyF7frFIfke4OoDm6FdoFvbUbWYNAiEA2q6C9c16HxhlUQUGTL8MyraswrRCSeHfDzBoMmND7Lk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2510555}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.0.2_1696601931583_0.5205336967844252", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7dbe4dfa66e34a1d69dd71fd8d5d2d12dd1e192e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-H5LILHYvZHTeaqIuNg4pw5lx4jj/mTz7tUXqZ1aFWBmnq9h34nHfA/SxyCZ5JCIyV/enLnXqposjx0i0Wrgg5g==", "signatures": [{"sig": "MEUCIQDe04oxjLKkn+VJLj7B29jJhVejJk10YjdCvgQY8GU8+AIgb9BN66xw1GEw8rJTviKLW/s5ii/zccQj1u2RZmJ1O8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2552031}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.1.0_1697262743106_0.9194232804327676", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a50c36a63f36a117bbec386312fc7ca7fcb81693", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-oy5I33I5hrPi8au6Ue6l2CKcVWArYCSexSy49RqGlzq9lx+cqYYfv+L1yItKKnRnb5KmN2jh1yzzZdkleQIikA==", "signatures": [{"sig": "MEYCIQDUq8/eyeYvzwHyQ99ogSJbOhcSga0OvE21WSze2gDffwIhAM48Zn3CvgWerdjlfnhkTO/BtlbrQZLN9MrrTah8bQKX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2656863}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.1.1_1697351517042_0.855354182946181", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4024e78006328cb50f7889e512fce2835984a721", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-73mb6Fk3WnygqCbYHPk7EWAUvmfKZr2teKD+Qb3i6nLQ3wEihEJixg8psUi0pmge77qmuONjOIiJnT8+hTWqEQ==", "signatures": [{"sig": "MEYCIQDJKzAwiFLbbIMq//lsAG218m3I19n9LJyUWwMusea9qAIhAIxiqpgdE5Gry9F43iyzQeY8qxnS9j2szDslfEPMpDxS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2656863}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.1.3_1697392115997_0.2458119775576919", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "52d706c87a05f91ff6f14f444685b662d3a6f96a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-HnigYSEg2hOdX1meROecbk++z1nVJDpEofw9V2oWKqOWzTJlJf1UXVbDE6Hg30CapJxZu5ga4fdAQc/gODDkKg==", "signatures": [{"sig": "MEUCIQCF/vntlGqOujQ6HPoyTlCwqSldhZHyAPyFOZf2Kib8twIgEFKm95eEkbocJFBDmGEY4d8QiOJgC/c6oxjbLDXxYRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2640735}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.1.4_1697430858527_0.7248456882044685", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "ed0bff049b47126635908b095d421c699fe11996", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-WngCfwPEDUNbZR1FNO2TCROYUwJvRlbvPi3AS85bDUkkoRDBcjUIz42cuB1j4PKilmnZascL5xTMF/yU8YFayA==", "signatures": [{"sig": "MEQCIGiXedzpYN4BRfJnzqFReePvGH0ObnJMKjezHT4m6XMdAiAdu1BN1c3OorRNvUUuB6f3acwf1A23AR/04HvYa+tSLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2645663}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.1.5_1698485021535_0.40768067571944977", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "1463b02073a09ae091e285bff8d15260c8297d9a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-0tx59L4Kzy023EBtCe2Vp/tpgNbVitSqS6tep2IwkyzYw4YRJt43i81OWGDIhowiSozVLOFwnT557b2l+6GYyg==", "signatures": [{"sig": "MEUCIAcmEDDnNwC9dU5Jz+QGFKgNLVuti7JBY8dK9TKJBwT/AiEAmY78V4Cw92Fv6vOiEBK7u+i8g5jwSPXFFFUjgCAnx9o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2645663}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.1.6_1698731124822_0.8200892285174528", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d4913e0d66ca9ae10863c2b81300bd0c79386390", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-f4K3MKw9Y4AKi4ANGnmPIglr+S+8tO858YrGVuqAHXxJdVghBmz9CPU9kDpOnGvT4g4vg5uNyIFpOOFvffXyMA==", "signatures": [{"sig": "MEUCIQDWEPEFgyY1UoE1/xM2PYQPQzEurOTGqcObglmoKsL3NQIgXEIoPGEkh2Sl0Qeo7Zkm4Ze8e/BjGJTK9SdX0w8HZQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2662511}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.2.0_1698739850446_0.7063155451490064", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "2ad3d190af01d7fc8704e8e782c4a24006a9f21a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-wP4VgR/gfV18sylTuym3sxRTkAgUR2vh6YLeX/GEznk5jCYcYSlx585XlcUcl0c8UffIZlRJ09raWSX3JDb4GA==", "signatures": [{"sig": "MEYCIQCVLSwULuJNIDwvo2VbxtSjT/gYHD4bD+VZET51l+6VOQIhAKwOAEnWs7vIKa8BSx+Bg9nV15pGC7wEumnnhFiJJeVi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2652527}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.3.0_1699042392558_0.6493200165452879", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "cdd775414a33b33d8fc92e60f8df536163c4959a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-eG/9q+W0KPLu4xG3EwqDsG+wz9VoPMW0IDZ4bXdq2yyi2qA/CcmHb5956ZOw9PPAmL2krHvDaPyQIzFkZP0BLA==", "signatures": [{"sig": "MEUCIGObERoRN5KAovelj4HA4DS0noNy+BKYJCJszfTlXfmbAiEApG0ewd5rMwmHEpEN99iXpZvomgut4WSBsb/mQfzIFbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2710831}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.3.1_1699689482853_0.6065187666208351", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "694f52dabf24a7c063e4ec647bf3fde19b276ca0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-kavnkaV50Gu6vESlOAwUad92wYY9mUrcaPmhzOQZKlNFnzWAUYyD/uhHmWvY7Z2chtwhWlng0LvCRBF5QiPO7w==", "signatures": [{"sig": "MEUCIDblwfudMjgNMAHY0kvDCY3VMQM6/9/pm2aYNy8+1VVbAiEAxc3MnZQwzfTgg699po8NZn23tHZl4mZjFq6JH8DJbSc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2379527}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.4.0_1699775401371_0.8306982174114688", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d78d7ad358d24058166ab5599de3dcb5ab951add", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-9zc2tqlr6HfO+hx9+wktUlWTRdje7Ub15iJqKcqg5uJZ+iKqmd2CMxlgPpXi7+bU7bjfDIuvCvnGk7wewFEhCg==", "signatures": [{"sig": "MEQCIAjTvj0uneYWWGoRDT/o3UQpgF51XXwmpFmkfmAJGaIwAiBgsnvXNg9hb2/2yTNwRHqHQecK+0OI2ofdtdSKy/u6Fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2379527}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.4.1_1699939531142_0.6624603609811504", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a0e6b2a1d67a4ba0c2a61985175f65c05abc5f73", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-VpSQ+xm93AeV33QbYslgf44wc5eJGYfYitlQzAi3OObu9iwrGXEnmu5S3ilkqE3Pr/FkgOiJKV/2p0ewf4Hrtg==", "signatures": [{"sig": "MEUCIElhhXj5Z219JHeSRNopgb6Ejr7QStgHzvjYk+CjoVVkAiEAm2Y2TyTfqYaNS9VRPKAo7cf2pIfjhqn+vvwU8PVzqBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2381255}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.5.0_1700286739759_0.30838984939810743", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "501a336b1dc4cb350a1b8b4e24bba4d049902d74", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-ZFPxvUZmE+fkB/8D9y/SWl/XaDzNSaxd1TJUSE27XAKlRpQ2VNce/86bGd9mEUgL3qrvjJ9XTGwoX0BrJkYK/A==", "signatures": [{"sig": "MEQCIDMQMqL+p13JZgyx39h2MEx40GA05aeuf7LKqwheeGPcAiBGz8+IMCvG72XaGbYWYKx2CO4+V6oUz/MFSr779sD7+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2381255}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.5.1_1700597597280_0.32605597093690397", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "aa98197f06d9d795a317152ec8d95e65a369053b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-7ZIZx8c3u+pfI0ohQsft/GywrXez0uR6dUP0JhBuCK3sFO5TfdLn/YApnVkvPxuTv3+YKPIZend9Mt7Cz6sS3Q==", "signatures": [{"sig": "MEUCIQDd/TvCVEXc4vRNYAmlxR2gELJ744/Dob4vI2izJV+4EAIgbj6FkmnRQauQxzvmKYX7nDgw9ajUvylFpVrG1u31wEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2390599}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.5.2_1700807395000_0.6216598751988307", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "12fad1802f500a0196ab0bb4dbb776aaabdedcc7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-tBTSIkjSVUyrekddpkAqKOosnj1Fc0ZY0rJL2bIEWPKqlEQk0paORL9pUIlt7lcGJi3LzMIlUGXvtNi1Z6MOCQ==", "signatures": [{"sig": "MEQCIAmUtK/7oZWvvgj2+TSs1mzWqda2LAxy68p50Bq7d+HaAiA5JDMxI4sfVtsCbNTeIbD3O4HyC1sog7GAwU5fCsVA7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2390599}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.6.0_1701005961308_0.3441133782454282", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5e972f63c441eaf859551039b3f18db9b035977d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-EfI3hzYAy5vFNDqpXsNxXcgRDcFHUWSx5nnRSCKwXuQlI5J9dD84g2Usw81n3FLBNsGCegKGwwTVsSKK9cooSQ==", "signatures": [{"sig": "MEQCIGRnqf91UaEf87hNT6poIzWjqVQsF42toZOLoBiCkrOTAiBXp1hyT+Dj1pf92Hs2+fOVrMYV0sGNpvDxDbTnJKAv9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2390599}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.6.1_1701321797288_0.517886127889351", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c495ba7aa13427aaeb4ea0864ab9c2a3d5aa9c09", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-zhye8POvTyUXlKbfPBVqoHy3t43gIgffY+7qBFqFxNqVtltQLtWeHNAbrMnXiLIfYmxcoL/feuLDote2tx+Qbg==", "signatures": [{"sig": "MEUCIQCzOmDBapboGIQhY6OTXqLSuhsX1/Jmkz3jxt+ZtynLbAIgYQ9r/pWOcSSFj3HtkpcDVsqUpj1VfAJ0Z+j+iPJHbFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2376391}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.7.0_1702022289954_0.753944059364795", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f2015d6e4ff41417f2e2c55b3d9625346e355c57", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-JsidBnh3p2IJJA4/2xOF2puAYqbaczB3elZDT0qHxn362EIoIkq7hrR43Xa8RisgI6/WPfvb2umbGsuvf7E37A==", "signatures": [{"sig": "MEYCIQDW0zzKUOML1DmQParnsAir9fV3zLCQGx1NpOTk3EJ1ngIhAOC6kITc1/dNtjgJLPbOT4HhZ0eshkB7NqIyfQMXvI2h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2376391}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.8.0_1702275903687_0.9227242566207743", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "57ece7bb1b7659a3ea2ace580a63b8f92b3161f1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-lHoKYaRwd4gge+IpqJHCY+8Vc3hhdJfU6ukFnnrJasEBUvVlydP8PuwndbWfGkdgSvZhHfSEw6urrlBj0TSSfg==", "signatures": [{"sig": "MEUCIQD3vGBKGuKEC/l3L7fJ3TnpAaTCuKrD6L6btWQgtOrsoAIgIL3UQl8V0Jo6ajlS5K2/7q5mzg0ZRxQt9EIlEKiWMj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2376391}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.9.0_1702459465136_0.04666154059795091", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7a4dbbd1dd98731d88a55aefcef0ec4c578fa9c7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-Yqz/Doumf3QTKplwGNrCHe/B2p9xqDghBZSlAY0/hU6ikuDVQuOUIpDP/YcmoT+447tsZTmirmjgG3znvSCR0Q==", "signatures": [{"sig": "MEUCIQD81K9m/zYguEDn1UMYRF4eM8Kq7ZFwUeDIe1CyhYMDnwIge6mpyGcYOYkHNOTmdCXwS5J7b40y/6V6lYyV5EDubq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2380231}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.9.1_1702794378777_0.8353402381073385", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "147069948bba00f435122f411210624e72638ebf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-ewG5yJSp+zYKBYQLbd1CUA7b1lSfIdo9zJShNTyc2ZP1rcPrqyZcNlsHgs7v1zhgfdS+kW0p5frc0aVqhZCiYQ==", "signatures": [{"sig": "MEUCICe8xvF3KkNm/0CetHfGeI0R3AXClzwdnOtM72heqpKgAiEA1QNs1qB84djateCdZRLiQrb+hSzf+hSmQj71xjEDLiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2390279}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.9.2_1703917414721_0.5521293807381582", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "73874e5d96999a0e8a055bc94526b9e68c6d00e1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-cO6hKV+99D1V7uNJQn1chWaF9EGp7qV2N8sGH99q9Y62bsbN6Il55EwJppEWT+JiqDRg396vWCgwdHwje8itBQ==", "signatures": [{"sig": "MEQCIE8ONiDS0wluWPlmdFMq/NayU016o65qAVzGdh0xGHqeAiArLG5e4DEoqmj1VzU2Uj5mIc19I6zKjWbzmM6A8t1h/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2381063}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.9.3_1704435653981_0.4326812095317747", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "cd933e61d6f689c9cdefde424beafbd92cfe58e2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-g21RTeFzoTl8GxosHbnQZ0/JkuFIB13C3T7Y0HtKzOXmoHhewLbVTFBQZu+z5m9STH6FZ7L/oPgU4Nm5ErN2fw==", "signatures": [{"sig": "MEUCIC7S1ylmKPlYrKHzIPWf2Qj4ocvC57F7wKgXdlDrrVczAiEAgxE4bYKYXMk4q687CYRqWKDd+jxcj2OiU1sIAbNaipA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2381191}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.9.4_1704523147649_0.1852858491097229", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "120678a5a2b3a283a548dbb4d337f9187a793560", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-Q0LcU61v92tQB6ae+udZvOyZ0wfpGojtAKrrpAaIqmJ7+psq4cMIhT/9lfV6UQIpeItnq/2QDROhNLo00lOD1g==", "signatures": [{"sig": "MEUCIBcUL3Edr/T7+trLyOiQoEfZDqe/nR8pGBfjgh234QVXAiEAxmaW95PQHoANWG7buOIAS4Tv0CdtAI7kqnErHSDfI0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2388167}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.9.5_1705040180764_0.6940893913301209", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "09fcd4c55a2d6160c5865fec708a8e5287f30515", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-oNk8YXDDnNyG4qlNb6is1ojTOGL/tRhbbKeE/YuccItzerEZT68Z9gHrY3ROh7axDc974+zYAPxK5SH0j/G+QQ==", "signatures": [{"sig": "MEUCIEgNegilQ9OZSVVbP1TSLsbVbQohjx4mHb+utZ/SkjgDAiEAuvvqxOXpHSYC33T+y5YHJwwdAO1RJJCirQh5BvqhrKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2371655}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.9.6_1705816346186_0.3814406960686585", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4763eec1591bf0e99a54ad3d1ef39cb268ed7b19", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-Fz7a+y5sYhYZMQFRkOyCs4PLhICAnxRX/GnWYReaAoruUzuRtcf+Qnw+T0CoAWbHCuz2gBUwmWnUgQ67fb3FYw==", "signatures": [{"sig": "MEUCIQC2uDhjNJTvEuUxFVpGbz7zAK++KB6+1LkoezfQ21yqqwIgfzf7952GSjGaJkMN1kTfdh/ggI4jdI0hX5hbJBk4RyM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2447496}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.10.0_1707544728584_0.13384525194228192", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f7cc62debb348fb109724c882b99adc5c3f73e31", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-X17s4hZK3QbRmdAuLd2EE+qwwxL8JxyVupEqAkxKPa/IgX49ZO+vf0ka69gIKsaYeo6c1CuwY3k8trfDtZ9dFg==", "signatures": [{"sig": "MEUCIQC+QK595NXXZti+r3orBogyVRDwUcoWZxfKSzmzM2DSBQIgC0sPBkIDRdXQgT3Vf1PUIDnoQu3PFdYDB5XTvRlylIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2447496}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.11.0_1707977389752_0.6910987500023271", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "48e899c1e438629c072889b824a98787a7c2362d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-a6w/Y3hyyO6GlpKL2xJ4IOh/7d+APaqLYdMf86xnczU3nurFTaVN9s9jOXQg97BE4nYm/7Ga51rjec5nfRdrvA==", "signatures": [{"sig": "MEQCID5vUziX3pD0iOF/a9QQf3W2Z7h1maI+ew2ZcBszgrvKAiAM8XV8yBHLtycRepOsk5+cSSk1r57QXgci3OTRc6op4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2434184}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.12.0_1708090349020_0.23226271556499922", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d851fd49d617e7792e7cde8e5a95ca51ea520fe5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-uBkwaI+gBUlIe+EfbNnY5xNyXuhZbDSx2nzzW8tRMjUmpScd6lCQYKY2V9BATHtv5Ef2OBq6SChEP8h+/cxifQ==", "signatures": [{"sig": "MEUCIQC++tnLPqnaR1K/fDOAaDCh4D7+fNcwO6zceNlGB76eHgIgQd1S5+qV7nxWOqWo7+oOT2Vur8KdFuMoZd030X+IWTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2442568}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.12.1_1709705024955_0.5911854236110379", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3d3d2c018bdd8e037c6bfedd52acfff1c97e4be4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-8wZidaUJUTIR5T4vRS22VkSMOVooG0F4N+JSwQXWSRiC6yfEsFMLTYRFHvby5mFFuExHa/yAp9juSphQQJAijQ==", "signatures": [{"sig": "MEUCIQDTtFluL7IUqPFQ20myUTdlMS3WE7osuDGb5euced7EIQIgLK1KYpMiQaPLNFQWuymBXO8nKVCoHgMKcLF2JY6aZrc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2443208}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.13.0_1710221328276_0.37908447925372357", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3763f2fef0facf70ba034643ed1ed9c02b1b5c9a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-GwDS38pdRUbB+7gkRjjx/ghlKRy4+zsJ5liSqKz9znyQs/v7ZSlGp4LxtggSWrWN9ccohVtAI5mj3ZyairzwwQ==", "signatures": [{"sig": "MEYCIQCIbFNkuTRT7S8/UAj6smwuQlRGLlDWTuNDlCzoXv/g0QIhAMIqlbY6+qyKUFuj/I5iikHCaLnVWGhUts8bR5+pybLO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2447114}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.13.1-1_1711265971432_0.3986941003435529", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "69c3b896e3ee1c3487492323a02c2a3ae0d4b2e7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-dBHQl+7wZzBYcIF6o4k2XkAfwP2ks1mYW2q/Gzv9n39uDcDiAGDqEyml08OdY0BIct0yLSPkDTqn4i6czpBLLw==", "signatures": [{"sig": "MEUCIFXoc/YmMEZAVRvcrcDEIpfWRcLIP6eh8jPPOhDL+mtdAiEAm6viL9+wy1GLCeM+D5lBJUifI3sR+e3Fs77rIXE60go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2447112}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.13.1_1711535270276_0.14998176502990201", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "2ce200efa1ef4a56ee2af7b453edc74a259d7d31", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-GYbLs5ErswU/Xs7aGXqzc3RrdEjKdmoCrgzhJWyFL0r5fL3qd1NPcDKDowDnmcoSiGJeU68/Vy+OMUluRxPiLQ==", "signatures": [{"sig": "MEQCIEbllXQymqiP5OvkVro2a0eUlRsIm7Lrd/MIJSIYrsDCAiA3fsgm8PYh9TCDeXVBgX/dAyqKekE62ZxkL2I5677sbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2447112}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.13.2_1711635226819_0.17187229324051634", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "822830a8f7388d5b81d04c69415408d3bab1079b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-ygrGVhQP47mRh0AAD0zl6QqCbNsf0eTo+vgwkY6LunBcg0f2Jv365GXlDUECIyoXp1kKwL5WW6rsO429DBY/bA==", "signatures": [{"sig": "MEQCIE7BKCv0Et17J7cgqvy24ERY9uwr6vZEAyjLDNIZtjCiAiBnBmkjyVpRnArrFjtPIyI7Ll6ulixb6CGdZDjvYwGGpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2462016}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.14.0_1712121782935_0.21729474140176874", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a87e478ab3f697c7f4e74c8b1cac1e0667f8f4be", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-mS6wQ6Do6/wmrF9aTFVpIJ3/IDXhg1EZcQFYHZLHqw6AzMBjTHWnCG35HxSqUNphh0EHqSM6wRTT8HsL1C0x5g==", "signatures": [{"sig": "MEUCIF5H5w+DFMCC9trPZeipcd8w0VxGKpjWWbGfCrdtgqGDAiEA5A/gyzDZ9ZlOCscy3KuxdFI4fI06WOgm58zbjPASTbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2446784}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.14.1_1712475347335_0.6765307027758067", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "44cffc07d04d659cb635aec11bef530d5757ee6a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-nwlJ65UY9eGq91cBi6VyDfArUJSKOYt5dJQBq8xyLhvS23qO+4Nr/RreibFHjP6t+5ap2ohZrUJcHv5zk5ju/g==", "signatures": [{"sig": "MEUCIDPp6KLJwqb8ZvsqFgbTWUg438YwOGpLClpPy6xcyML/AiEA9kGuqrMqn/vYoHwWXIlpWcvplO9v/ueOfOohuzdRPec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2457792}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.14.2_1712903027798_0.8664735873143501", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "53ed38eb13b58ababdb55a7f66f0538a7f85dcba", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-ge2DC7tHRHa3caVEoSbPRJpq7azhG+xYsd6u2MEnJ6XzPSzQsTKyXvh6iWjXRf7Rt9ykIUWHtl0Uz3T6yXPpKw==", "signatures": [{"sig": "MEUCIEG+lOEIqKO6/JnQqrv5o2nTGdoNVYyLz8H+hSVqdAQ/AiEA4vHgyi3J+qFPX2YUs+T/+NvmMxycptM5HxPKR7oT+oA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2454344}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.14.3_1713165519035_0.9905043430401173", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "0036b835f17ca9e84c188c419399493bd5739986", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-QGOIQIJZeIIqMsc4BUGe8TnV4dkXhSW2EhaQ1G4LqMUNpkyeLztvlDlOoNHn7SR7a4dBANdcEbPkkEzz3rzjzA==", "signatures": [{"sig": "MEUCIQD8Q6Ss1zCp7yTJvyJt5GcIWC+oZvQ08lFCebXMuIbXjwIgVfqLPtiZPhX9e1UO+GjRcjc9rt81I8FxTiapKRTCIeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2522824}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.15.0_1713591442407_0.3224011135797533", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "dae816a34b2ea0a29a8e2aacf939199b5ed8d565", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-r2TGCIKzqk8VwjOvW7sveledh6aPao131ejUfZNIyFlWBCruF4HOu51KtLArDa7LL6qKd0vkgxGX3/2NmYpWig==", "signatures": [{"sig": "MEQCICaZGRpwnaAUgheWCWoFMOebW54J2IUWDNn6kAiWlTJSAiBaoOwdX734stjQ+x0IS7kUl0TNAMZCe3WkWJfrzwr4Kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2522824}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.16.0_1713674535439_0.6418865428229734", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "0154bc34e6a88fb0147adc827512add8d3a2338c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-K<PERSON>+WGZjrh6zyFTD1alIFkfdtxf8B4BC+hqd3kBZHscPLvE5FR/6QKsyuCT0JlERxxYBSUKNUQ/UHyX5uwO1x2A==", "signatures": [{"sig": "MEQCIDwdSvXXQZOm9Lepq8yGfnHH04qNgELJgOB5pY5ENxM5AiAy2lE6WNXkT9gbXDDOjiQCHIt+rVMj6TsZkQ6lWTZxfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2522824}, "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.16.1_1713724208924_0.48804056542350405", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "450ecf66f30a51514413aafa79d28561db73151c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-/bjfUiXwy3P5vYr6/ezv//Yle2Y0ak3a+Av/BKoi76nFryjWCkki8AuVoPR7ZU/ckcvAWFo77OnFK14B9B5JsA==", "signatures": [{"sig": "MEUCIFDjI9YQ1z24WRJXYMAnb0kGg1VqXsIjMgnMJmGvJnv9AiEAx3uvX5JhEdWFE6w8NSEZmmhdHmLgyWypdzjbTX6JZVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2522853}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.16.2_1713799166640_0.3297386454504827", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "e99605cb49f6be9e96759417da2afabc32b88bed", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-ij4tv1XtWcDScaTgoMnvDEYZ2Wjl2ZhDFEyftjBKu6sNNLHIkKuXBol/bVSh+md5zSJ6em9hUXyPO3cVPCsl4Q==", "signatures": [{"sig": "MEQCIAT1O6ZKvUn+qS+vasXUq7HLp1hylAsn8uS1HfoxVb88AiBqv3Xw+LmooUG84RJdBMaruvAvKsxRfo/qg8M2Ec1LlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2522853}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.16.3_1713849162085_0.9663991936183371", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "40d46bdfe667e5eca31bf40047460e326d2e26bb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-ADm/xt86JUnmAfA9mBqFcRp//RVRt1ohGOYF6yL+IFCYqOBNwy5lbEK05xTsEoJq+/tJzg8ICUtS82WinJRuIw==", "signatures": [{"sig": "MEYCIQDYeCM6WY1BS8/SvlX1AvpE8EubU0qlYxD5s9MFAJbXhQIhAIalioCVD0gQNSD2jkZKLFQnHdK2TTlNLcAYYKKVceP4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2522853}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.16.4_1713878114800_0.8468543820990391", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "186c3aa4a9f6db70a4d1b8b6f75b98f1dc1d004d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-Vb2e8p9b2lxxgqyOlBHmp6hJMu/HSU6g//6Tbr7x5V1DlPCHWLOm37nSIVK314f+IHzORyAQSqL7+9tELxX3zQ==", "signatures": [{"sig": "MEQCIEuF4M+OhnCSib9ZdkBcGYtDNRiYxdtjOd+JAEOnIX4bAiBvEmFkGaYDfEOuJzvzvJ1cSOOLfyq67N6pz8JoqUwBhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2530981}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.17.0_1714217399362_0.9819290811278065", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "8851254fc581d860940ab27009c07dde80666e82", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-Lq2JR5a5jsA5um2ZoLiXXEaOagnVyCpCW7xvlcqHC7y46tLwTEgUSTM3a2TfmmTMmdqv+jknUioWXlmxYxE9Yw==", "signatures": [{"sig": "MEYCIQCu8AReLY4shybafUJb4k9A3eqb9qWKq86jcbahdqSYcwIhANHw93yD7R43FyYOjO50cGiAPa3gK3k6jqfgFzdq8vXs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2530981}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.17.1_1714366682426_0.36950690435638234", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "1a7641111be67c10111f7122d1e375d1226cbf14", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-3reX2fUHqN7sffBNqmEyMQVj/CKhIHZd4y631duy0hZqI8Qoqf6lTtmAKvJFYa6bhU95B1D0WgzHkmTg33In0A==", "signatures": [{"sig": "MEQCIElrxCWRRzxMcX4VrfAY3g2X/icuvO340nQr8gxactu6AiAoW1ogcukYgH7ZTXXhXzaYZE0Zs65+CyZygrlaslWDzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2530981}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.17.2_1714453256199_0.542209389571118", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9f1a6d218b560c9d75185af4b8bb42f9f24736b8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-C/zbRYRXFjWvz9Z4haRxcTdnkPt1BtCkz+7RtBSuNmKzMzp3ZxdM28Mpccn6pt28/UWUCTXa+b0Mx1k3g6NOMA==", "signatures": [{"sig": "MEYCIQDLkHeIIWGpUdGKeInaMI0o7OR/Oya4EHU7S3mrWkz/FgIhAKdJdVPncZ5KPe1aAOyAl2g+qlhEunNiCtg/1AA7f/3D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2528621}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.18.0_1716354232805_0.3284177616783486", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "46193c498aa7902a8db89ac00128060320e84fef", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-P9bSiAUnSSM7EmyRK+e5wgpqai86QOSv8BwvkGjLwYuOpaeomiZWifEos517CwbG+aZl1T4clSE1YqqH2JRs+g==", "signatures": [{"sig": "MEUCIQCgfqZOHDCFgAh6nw6GBvwBvD1yxn7JxLolCiqdpep5HAIgaWbeePVMD3YQfaqg5LHRc6Ho1yDhneMDtjZqwKbRObo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2345421}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.18.1_1720452320731_0.33665938073607116", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "e9d9219ddf6f6e946e2ee322198af12466d2c868", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-2Rn36Ubxdv32NUcfm0wB1tgKqkQuft00PtM23VqLuCUR4N5jcNWDoV5iBC9jeGdgS38WK66ElncprqgMUOyomw==", "signatures": [{"sig": "MEUCIQDZQ40czNJ/36ll1N/sCIrivHh7ui3xQIigiW/Rj7ZxTQIgBG5o/W5m82hRp1au/tf7fONoGKeSnb00bQTTZ01zm6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2316557}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.19.0_1721454383168_0.7630096199802265", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "e22319deb5367384ef315e66bc6de80d2bf2b3ae", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-MXg1xp+e5GhZ3Vit1gGEyoC+dyQUBy2JgVQ+3hUrD9wZMkUw/ywgkpK7oZgnB6kPpGrxJ41clkPPnsknuD6M2Q==", "signatures": [{"sig": "MEQCICpV9WRlaa8r/Or+O16PtcAURocjEbk+1bsHSVU0jGhvAiBWNd7ex8F3+IhxX/FlAhJfvCKULBbrNHO4sZNeiOJYGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2274373}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.19.1_1722056049387_0.6900497976431663", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "8f6c4ff4c4972413ff94345080380d4e3caa3c69", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-r+SI2t8srMPYZeoa1w0o/AfoVt9akI1ihgazGYPQGRilVAkuzMGiTtexNZkrPkQsyFrvqq/ni8f3zOnHw4hUbA==", "signatures": [{"sig": "MEYCIQCOw3+NoghXxHGkAG8i5YEMaWcd+2mFARL371Orw5qccgIhAPNgnaLJmqjo72P27GitxmG2ib7NSGYjffrhjZ/EIJKb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2274373}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.19.2_1722501182369_0.2594810134630745", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "41bd4fcffa20fb84f3dbac6c5071638f46151885", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-jMYvxZwGmoHFBTbr12Xc6wOdc2xA5tF5F2q6t7Rcfab68TT0n+r7dgawD4qhPEvasDsVpQi+MgDzj2faOLsZjA==", "signatures": [{"sig": "MEYCIQDY0AJyDvXEgzvDpVtG1InVmZQ6PT2UD6S+rRrk41SDygIhAO67lt/XtxB5ogm+CBQVbuqsb/ZzXz0HGtMZweY08Sxn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2283141}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.20.0_1722660539312_0.0598920767236264", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "422b19ff9ae02b05d3395183d1d43b38c7c8be0b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-pWJsfQjNWNGsoCq53KjMtwdJDmh/6NubwQcz52aEwLEuvx08bzcy6tOUuawAOncPnxz/3siRtd8hiQ32G1y8VA==", "signatures": [{"sig": "MEUCIQDz5vJPmfxiMpQf6ehQV2TySiq0p+CIBWpIrFVkPbyt9wIgfcYpMNZ0DQtwn5RhFyKihGS6eO+mf9QWUyO4qEgEd6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219653}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.21.0_1723960542412_0.6802511146319574", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7d1e2a542f3a5744f5c24320067bd5af99ec9d62", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-sWWgdQ1fq+XKrlda8PsMCfut8caFwZBmhYeoehJ05FdI0YZXk6ZyUjWLrIgbR/VgiGycrFKMMgp7eJ69HOF2pQ==", "signatures": [{"sig": "MEUCIQCCzuNsBIXuQ9Dl7VMI2V5LLoqP0Fby8MdyE2oo5eYw4gIgZbdIK0ZSSNLD1H3fgNUsNUop0qz+TiUuixOdFIrk2+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2214597}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.21.1_1724687662809_0.3798555959428167", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "1609d0630ef61109dd19a278353e5176d92e30a1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-ztRJJMiE8nnU1YFcdbd9BcH6bGWG1z+jP+IPW2oDUAPxPjo9dverIOyXz76m6IPA6udEL12reYeLojzW2cYL7w==", "signatures": [{"sig": "MEUCIQDBSvGxuLNG8slOAW9e5FxME3x37RZ7M60gTeKO8wzbmAIgeQ4F2cy7eDgcDDUH2l+WbngcDwdMfjfbW4kmNOVdgho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2212677}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.21.2_1725001474453_0.11718673170269289", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5669d34775ad5d71e4f29ade99d0ff4df523afb6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-btVgIsCjuYFKUjopPoWiDqmoUXQDiW2A4C3Mtmp5vACm7/GnyuprqIDPNczeyR5W8rTXEbkmrJux7cJmD99D2g==", "signatures": [{"sig": "MEYCIQCd7ZR1FKaLhCYNSLymRQrX+EkwKj3zy9dgvd3zDFiRnQIhANTF0KlaBqMiqJr0rlC1r0QRoXGiTO5zOLyUdS58YYP7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2216013}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.21.3_1726124758411_0.7198823172511812", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3a32fa4e80a62a6d733014838b1123fe76b060fe", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-9pxQJSPwFsVi0ttOmqLY4JJ9pg9t1gKhK0JDbV1yUEETSx55fdyCjt39eBQ54OQCzAF0nVGO6LfEH1KnCPvelA==", "signatures": [{"sig": "MEUCIAQKSSplErpRscPvNrws1/R9hcqpkyXw2ltRbV7t8z6KAiEA2HVLOvWPhzfeswMSWmHlutG7Rae2EWbcaR/sn6CO08Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2214613}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.22.0_1726721740306_0.14542379042905051", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "fca0a3d65e14f78ec84af024efa8f65d65206acc", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-F4DgRk//u604Np1eFoGUzE9TgGE6LMvjnX2tM24ePB34JlED9utc4T3iK5x8CWC/agH+zuN7q/hJF5AtWR+JOA==", "signatures": [{"sig": "MEQCICM0LC36ySIqZ6widYieHXIRhIxrfIsaUNjJbncsPrShAiAqXaZLpi3Lna6Zq48jcBeZZ7YSDTqwnUNjGtu/kSItdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2215701}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.22.1_1726820522230_0.8608555528428352", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d86df2d8c600ebdd7251110a3357c53e0a583ace", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-GBHuY07x96OTEM3OQLNaUSUwrOhdMea/LDmlFHi/HMonrgF6jcFrrFFwJhhe84XtA1oK/Qh4yFS+VMREf6dobg==", "signatures": [{"sig": "MEUCIQDwtcyPQI2fPNNvXxq0kRQ7c4MumEOi5xBZbeqpEHI6cgIgFLqfqCkjXdtMp4d4Skj0VNiiaSrVpgAWMYQgTRBZmpM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2215701}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.22.2_1726824833915_0.8300157152754326", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "8243e830d541a5554a1901b54431b64b657e591d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-1eWKIc7lIKJJ+RmFatIvUJ0U9QuidImufePzclLXwGhOOmFfDUf525zJAsVHE+YDkdL5oN3ThpPlE7KktVwC/w==", "signatures": [{"sig": "MEUCICLTzZvbBscNxVr9S9yMvCW5RsFEHQatEFu2M+tJFPNPAiEAjh4O9GhMHkR9O5/YG7QgurqMweERxUFBgG1PX/yDwj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2215703}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.22.3-0_1726843687670_0.2200689788845347", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "782812f18dfeebd4e92191c58f13c3738c6325c6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-tY4B/12eysJZ9gdOAKQbTsWC+3YVpopcxNvXxXUg6+OMSlVh0raMwlWqQNIxfGPqanuiR5JOes3dAVckt/NMow==", "signatures": [{"sig": "MEUCIFh3Lhv8KHOjx3LDHZUVftBHgkL9gqMIDkhZq9Fi1nCmAiEAvHh/NOC29vvdD+Ts+7/J3ffVBrnCbhrPSWJHRk5jkU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2215701}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.22.3_1726894998468_0.23432737586262253", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f23555ee3d8fe941c5c5fd458cd22b65eb1c2232", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-j63YtCIRAzbO+gC2L9dWXRh5BFetsv0j0va0Wi9epXDgU/XUi5dJKo4USTttVyK7fGw2nPWK0PbAvyliz50SCQ==", "signatures": [{"sig": "MEUCIQCTIC2dlje4b8l7MAJUlz2dtL5IYmCEHbHd3Tu55Aqq5QIgVgXQ/IXP3nkEm0kcLTsJYWUaGCcCRtgFGWAbNcQhrME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2215701}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.22.4_1726899090340_0.13297906357579858", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "217f01f304808920680bd269002df38e25d9205f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-PNqXYmdNFyWNg0ma5LdY8wP+eQfdvyaBAojAXgO7/gs0Q/6TQJVXAXe8gwW9URjbS0YAammur0fynYGiWsKlXw==", "signatures": [{"sig": "MEUCIQDCkH13BaQbgQTFrXUKTbkV3dB5TmuVyO1SfuLV9Cmh/gIgfrrOt12XhN8V0aPAak+EW8ItN1nTKfIIjvY8nBIApl8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2213269}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.22.5_1727437705511_0.3728834101365768", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c35a35414d11c028db1e11b158b3947d1fa3abb0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-WAeZfAAPus56eQgBioezXRRzArAjWJGjNo/M+BHZygUcs9EePIuGI1Wfc6U/Ki+tMW17FFGvhCfYnfcKPh18SA==", "signatures": [{"sig": "MEUCIAHyo5sI+nPLsFW1FBbal9lUwTrpTjeJyR3MoIuBdINSAiEArYTMfZ1k2vfzsgZOA4nLM0BanQEsBDxPeiuQSxHDjkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2213269}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.23.0_1727766616208_0.5571312006833955", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "62dfd196d4b10c0c2db833897164d2d319ee0cbb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-0KXvIJQMOImLCVCz9uvvdPgfyWo93aHHp8ui3FrtOP57svqrF/roSSR5pjqL2hcMp0ljeGlU4q9o/rQaAQ3AYA==", "signatures": [{"sig": "MEUCIE9WQC0nTwpLTgo2oERpxj+5NME4NhDk42yI5iHjPaSjAiEAo8i0Xhcsmyto++WC5n4IV69uuJUmh0cngXknpxS+aEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2221813}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.24.0_1727861849772_0.6270478669871018", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4aeb84cad0815f0ffdc19c91e7508cbe6f76b669", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-W+drJRBL1+N1/zaq+8y/CtQ3VP5wxMXwCy7obFl9r5jJ5EFNEYAqchuPfYTleYOoA46bwXAprCL+OVK3BTrWWw==", "signatures": [{"sig": "MEUCIDHdhRzp2JyhVBRsJAWOOu/kvFa8jtOfVzYwVFZI0edyAiEA0JN7z/uYFW7y5igmGkmmheTNkAkzQGrp8ErH1oUw+ew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2254797}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.24.1_1730011393852_0.44583474907389387", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f274f81abf845dcca5f1f40d434a09a79a3a73a0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-ArdGtPHjLqWkqQuoVQ6a5UC5ebdX8INPuJuJNWRe0RGa/YNhVvxeWmCTFQ7LdmNCSUzVZzxAvUznKaYx645Rig==", "signatures": [{"sig": "MEQCIHfHsBlJaQQRVxX84amaB2v2ajCSWo4KfntO/fZ6fq+dAiBLm7P+wHFRmFxZZ5mDVAit33me8PuhsJYp2cJY9osgJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2254797}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.24.2_1730043620538_0.6278062377053717", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "243b8d6f05cfd5a8572a2587c92e4df8ff7b73cb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-Fp31gPlEj7d3W2doe2ME72Gpmm2UOS5YbjmmENoGSsDmk++O9KETty72dCKtyYbPk9CNNRTVCRK1L7H/cmeWRg==", "signatures": [{"sig": "MEYCIQDKkYIyHI+//4nyCtLHlzpWrIk7jfDW9C3k31WbYP5SowIhAN1+hZJVju4UPSvwUqQ3xszfjLX4dgjo6TEpxMHel9rk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2254799}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.25.0-0_1730182524068_0.3035694244379359", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "bb45ebadbb9496298ab5461373bde357e8f33e88", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-KRSFHyE/RdxQ1CSeOIBVIAxStFC/hnBgVcaiCkQaVC+EYDtTe4X7z5tBkFyRoBgUGtB6Xg6t9t2kulnX6wJc6A==", "signatures": [{"sig": "MEYCIQDBb4OiN4+8noh0oSrePDsJqs8Ohf+YwPDLy4C3dLuqvwIhAMpOziVB8Y/0gdZ7evBAWJZ1Ye4HaVN1WSCB+OvRHO4P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2254797}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.24.3_1730211261146_0.5898284801905394", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c85aedd1710c9e267ee86b6d1ce355ecf7d9e8d9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-10ICosOwYChROdQoQo589N5idQIisxjaFE/PAnX2i0Zr84mY0k9zul1ArH0rnJ/fpgiqfu13TFZR5A5YJLOYZA==", "signatures": [{"sig": "MEUCIGZl6NCCeHIyXFA5Vq+r/shBzXplwonR4hvA6O3w8d/aAiEA9Ura89J7QIrlxY9k2HMpiBAgOLkDBGOKGqC2bx68/aw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2256845}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.24.4_1730710041605_0.02530505675270045", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9ba6adcc33f26f2a0c6ee658f0bbda4de8da2f75", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-uD/dbLSs1BEPzg564TpRAQ/YvTnCds2XxyOndAO8nJhaQcqQGFgv/DAVko/ZHap3boCvxnzYMa3mTkV/B/3SWA==", "signatures": [{"sig": "MEQCICroEfGmq98KWsURvjWMOT5MMpF6F1hfN8vnQ2ebFiGmAiAWOhMP/zsoGWiKQmxswTgarlObR4z1g7kp2XOFSWwMLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2249933}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.25.0_1731141455071_0.34924136846544074", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "ae71d6aa81e702c4efb72c1a67a6a4e790267a1b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-paHF1bMXKDuizaMODm2bBTjRiHxESWiIyIdMugKeLnjuS1TCS54MF5+Y5Dx8Ui/1RBPVRE09i5OUlaLnv8OGnA==", "signatures": [{"sig": "MEUCIQCiKlDf59QuBh8tcBSsn2gO5iogYnuIMkzsqTuFik9hiwIgC3BFRlobpxctkWTvcoEg1gH/JeiGpZRByWoTfeNlZXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2249933}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.26.0_1731480311920_0.8698093185130185", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9e8b723585b1ef3806a49ff52772310bedc20a64", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-lpfo3/nAt0KRRwQLjo4flD7dXTlYh5Gpgj/IZsqpUSxuIi2iSSjDYubABOkfyTsmp/HHsFk+Jnt1eM9RCEsoIA==", "signatures": [{"sig": "MEUCIGvT22qA3PT9zxOMHIyJVb5+HeCx9Okz0FmSJN5eAyrfAiEA4UmZjDl1RDPC6uGb4prFvwfkHEaII+GyCAfGWT4hKxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2249935}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.27.0-0_1731481405786_0.6129024931542744", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "40a3885eba5505a63c98dcc026610c6fd0b43495", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-29OzLXZtPsqETGwcDG9xMyMGFu98OIttrajf4X7n+kOK10yfNVuzrmw7hWsRWBpguagDl9YdMT6PynxKHxKMGw==", "signatures": [{"sig": "MEYCIQCrl5ZF6LHCF4u5Uyl4w+TE8iysKBqjrxKBAvuFz6EALgIhAOidamq8QqBacsNvmQqoWoM4pXONO+Q3DnV2OlBabjw0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2249935}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.27.0-1_1731566003501_0.9075579246987195", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f55db33c6e5b38a756788a08c8e834903d92d489", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-FMXxMZ7qnMULwgdmGSFVlOduAhFyqDPoK1A2Q8HBkzGYX9SMFU3ITKfLdIiCzTaaj/pt1OiEbpF2szUw6Kh++Q==", "signatures": [{"sig": "MEUCICn3NILPK0TILKpOeyPd7lhI4iBNcX+PK8AL9iUzL/ocAiEAt225r40SmA4+UddNhaJyf0qb5T4PYjkisJBf5HRY7Kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2253645}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.27.0_1731667248258_0.8810480394824598", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d8c5a9ff53156862b3a5c7531c731f55a619282d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-At/eCT2KeqzUqt2QlqinNyCArZxvTjrLOIReBORkHPdFJL4mKBuH9dqX8qC33f+pNZFE5f15NUydlmrpqOCe7g==", "signatures": [{"sig": "MEQCIHVS6GIAWJ9zZ2U+yiZFBl8I+Ma4ZHQElNLnzSEqzYmrAiAJdWIJvIgbZsg/cVUzC7VP5wYV0EXNfFmxW2RgagySrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2253647}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.27.1-0_1731677300831_0.0927753954524555", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "bd0ae1c9f0bbce908bad0d5fa9ffc2f573e35353", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-lkk05Jd4NIWN2It2mpOiIymvunf/3mM9KZ4B7N7cpgmlnKkWFWaMBNHuB6taxquEXreup3cfNfB1jWE1Pl0U0Q==", "signatures": [{"sig": "MEUCIDevo9i+4MKaU3Ap/1E5ZHw5YV9ywLD+Y0woFdNeGQtxAiEA4WhBb2I8ZcsfjKcGq5oeHLPonyeMAMhr3PNWui0klTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2253647}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.27.1-1_1731685096445_0.5325166293314683", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "bd3456bcb144aea59c587cc20e72cba97729db6b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-AYyiqk5p6qH/Rfgm9WsMs559S8ICzwBTS7hu8J9vya1lqCg9Htw6U/KV+gsJ1SE4Z4IctkbIkQy24htk9nfs6A==", "signatures": [{"sig": "MEUCIQCvw/b5LzZYN1LgL9lguK/Greybz5++OitSnQnTQSZeYAIgEjG9+DvArk6v4A+fDZ/s+IImNW1Jfkx0TdsJ8j+Evs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2253645}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.27.1_1731686874621_0.944333877981224", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "0c17a3c31bab9e8519038e7bf15a81e0ac35dcd3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-6npqOKEPRZkLrMcvyC/32OzJ2srdPzCylJjiTJT2c0bwwSGm7nz2F9mNQ1WrAqCBZROcQn91Fno+khFhVijmFA==", "signatures": [{"sig": "MEUCIQDdlC7GaQ8FBYQmdPYwXPDgUk4PxF18VJILmnY9VkRk7QIgVfBCVvoB3kIUUdRrQCHjHeludr8sQwBp9Lf62XyUqOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2253645}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.27.2_1731691216971_0.8247359740335696", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3692b22987a6195c8490bbf6357800e0c183ee38", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-h2Ay79YFXyQi+QZKo3ISZDyKaVD7uUvukEHTOft7kh00WF9mxAaxZsNs3o/eukbeKuH35jBvQqrT61fzKfAB/Q==", "signatures": [{"sig": "MEYCIQDrxjaoxAwzgT9zaolKcRHqHX+m9FT2q+HrJBrUNDERNwIhAPS1O0ZuvFqsrMDqh3z0Kdws07BNp0i9f5kULg50SOem", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2253645}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.27.3_1731947989024_0.3714713494241424", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "08f67fcec61ee18f8b33b3f403a834ab8f3aa75d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-9OwUnK/xKw6DyRlgx8UizeqRFOfi9mf5TYCw1uolDaJSbUmBxP85DE6T4ouCMoN6pXw8ZoTeZCSEfSaYo+/s1w==", "signatures": [{"sig": "MEUCIQCGI4pPn0xnkxvQxgXbXBOc9jEdbuFtCGdiA41Eqz849wIgPSrA6qMks+AsftCuXPXfu/g+snQDY9rhMshP87ukYjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2254733}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.27.4_1732345232580_0.4733357118431254", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "0d02cc55bd229bd8ca5c54f65f916ba5e0591c94", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-WXveUPKtfqtaNvpf0iOb0M6xC64GzUX/OowbqfiCSXTdi/jLlOmH0Ba94/OkiY2yTGTwteo4/dsHRfh5bDCZ+w==", "signatures": [{"sig": "MEQCIAWzGFtQnrKIMhVjK9GQj3LBu1hhWj3xkUrSFE2HGMJeAiBEnIrmjW863dE9lMJJ3uLfvZ8hGktJl31/cI2I9Vp4Ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2247765}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.28.0_1732972560489_0.5745340456231403", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "378ca666c9dae5e6f94d1d351e7497c176e9b6df", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-QAg11ZIt6mcmzpNE6JZBpKfJaKkqTm1A9+y9O+frdZJEuhQxiugM05gnCWiANHj4RmbgeVJpTdmKRmH/a+0QbA==", "signatures": [{"sig": "MEYCIQCWhZV2ccGgg0uYUcrwCy5JHyhxjRGss2+l4LJ2J34jAAIhALKoppHrw7tFSEARq7ZP3B3rsLcBmYtMrkLT2ue7Eftw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2236949}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.28.1_1733485509072_0.5457876094144514", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "df9693dc3f468a02496242a1ae89622f992165e3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-Hc61oKHoNd4xaNeRw82Gb4ouJEOKB6IYQJ1VxIQyOBHeU10/JEIa+e3787TmQEexIVIH0bpWXKFDq8rhoz6ZkA==", "signatures": [{"sig": "MEQCIGCf2kfy1rlgiGIROipBK6H6tRfOBYwAMIrlGUftWjRQAiAYZl1gYBzot1zfViMSh1LouNrx8P3neSvakUDIH20Wcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2239319}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.29.0-0_1734331206953_0.07061116387771915", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4a4c478e43d96eb417b4839a581f30acf9e1c743", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-qEX3YIxBMGuKNMQ2B5VBPVWJewycQREPepRnEDJuhH6yU74CSTiSC44HsUuiYX0BC0dcj4F3MBW1N9b9StraAg==", "signatures": [{"sig": "MEUCIQCXQFOZ/ML7OqMGenC+2fAoZAyMZ+gZzlVOkrTbdHoKewIgMIJ3yNHP0ccBZ3Sr6NGuQtd32/Sgh0XrHpvLJfh78c0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2239319}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.29.0-1_1734590263368_0.99439520482715", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "311b645300d130b3fe471f715040cccd08c3c1b0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-Yq2UzCHas3aD7m/hVlJ76iSUVXXqQCy49V9oylp9Uq1GiTwkOP64EDzlBfNzdvQBDbZefCxDEY6wBSBHHnLHfg==", "signatures": [{"sig": "MEQCIC99Ri/VK5sJDGAkHNPUJY4yttZ0vSxPFVuXA4BK0KX6AiBuLbYc8h6FSJVap7HdmWn2xbAbjlmdOtK6jZGUH/D64A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2239319}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.29.0-2_1734677776288_0.12410894565998176", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "6d6e76da28a91a066bbe8af41e21ba0c61b42afd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-IPSCTzP8GRYzY+siSnggIKrckC2U+kVXoen6eSHRDgU9a4EZCHHWWOiKio1EkieOOk2j6EvZaaHfQUCmt8UJBg==", "signatures": [{"sig": "MEYCIQC3Wwammem9aW+82Wy3Ae5irDtCey4MV7PBKc2ModmtdQIhAPIZxrsq64hLYjKs9YdaVajKv2BwN7wQccXPbYBHFdTc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2240725}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.29.0_1734719858054_0.19131159962467859", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "443a6f5681bf4611caae42988994a6d8ee676216", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-Py5vFd5HWYN9zxBv3WMrLAXY3yYJ6Q/aVERoeUFwiDGiMOWsMs7FokXihSOaT/PMWUty/Pj60XDQndK3eAfE6A==", "signatures": [{"sig": "MEYCIQCT1txkCCZTRoXWHbLeoH5RMQ70r2d2AiyfrzcwwTLCEAIhAK1VbyG9sUNbGZUgjlul2//WukCy1DoWREdh27j5eRJ3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2240725}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.29.1_1734765375591_0.0961077531498411", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "947c459dd3eaca6bca6bbc838e29352a7a8e57c1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-28oVTo1F6BQCgl9LJSGOpZ1aIISaYHNL0jlG0JgujZTJmvWyKIVztPBpFMRLDzw2X95/ti3cw8kbfrvjvilqCg==", "signatures": [{"sig": "MEUCIQC6FILx8/EmWP0cv9vturJw5UghStbpWRtTOxc5+Lgd3gIgAYENQWFyDMie+xFFKZNlSQ2lpEzPPfJuFcEVnVFeW5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2240727}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.30.0-0_1734765447856_0.21950355371735775", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d8b4a770c2b385bc548560a9ca2d7d3ed63fa7ca", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-V/VA69qQV4erislFXehAjjmFysDEEBlLvlB1daS1Nwv7Q/MFvxuR2UT/ggMaZyiThTfss2uI+Cz5P8DNBhOI8Q==", "signatures": [{"sig": "MEUCIQD7kCfGqMl85rJPiRXc/6nRF09HijckirOZHox5LHInpgIgUSN1M4EpQ8f7PA7/g95ij5XVzcHKCTYouT0UjK4+09M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2245975}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.30.0-1_1735541550639_0.9738078480388443", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "14a39257111abbc38412805c9162819d3bb248c1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-h4VgxxmzmtXLLYNDaUcQevCmPYX6zSj4SwKuzY7SR5YlnCBYsmvfYORXgiU8axhkFCDtQF3RW5LIXT8B14Qykg==", "signatures": [{"sig": "MEUCIEX6LCpx8PIFiZzOeUontcf4ikIdHiFpTO5PqCLI8e7JAiEAzVySzMNOZMIQG6cjESFhStaXAYFuPiWGPpSCUv6LCjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2245973}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.29.2_1736078876561_0.37178149991622766", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "bdaece34f93c3dfd521e9ab8f5c740121862468e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-bsPGGzfiHXMhQGuFGpmo2PyTwcrh2otL6ycSZAFTESviUoBOuxF7iBbAL5IJXc/69peXl5rAtbewBFeASZ9O0g==", "signatures": [{"sig": "MEYCIQClWMUDRyrk7brKeICKLl73L/m+v1G9dMuio9dhzyOuJAIhAPdI6CS15xRDPsvQtQU233EkJLSaAvEvItp6iyL6hQ4J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2245973}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.30.0_1736145412349_0.8262277114805232", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "b9da54171726266c5ef4237f462a85b3c3cf6ac9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-PaMRNBSqCx7K3Wc9QZkFx5+CX27WFpAMxJNiYGAXfmMIKC7jstlr32UhTgK6T07OtqR+wYlWm9IxzennjnvdJg==", "signatures": [{"sig": "MEUCIQDg4woF3WIcQzFWVWEvrbF/0kAOd1udYvLA2uqzKhG6igIgL3mRDOMrNkCeP0/bY6IcpQHlhpmWhAJt+MKdioPQJ/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2245973}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.30.1_1736246165690_0.6667211706084635", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5bff0ff2080b51f913acc08b155706a793e58c0e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-TUgxNoRNXBzNhkox5ZUI0M/XMMHOt+khCaxdLi2IjaG2l4YHwW8yXxydkny3CMjerrZkk+pKlZD3xBblm4QRGQ==", "signatures": [{"sig": "MEUCID+9vhk/zTvPDJ4AugjvewfCZmEpVFqlP7zuPVTUEO4DAiEAwxGynN6fM18zctyOwTU2+FFCJtkD1AQDyjNFCeyi0cM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2244367}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.31.0-0_1736834275122_0.413634216807079", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "e455ca6e4ff35bd46d62201c153352e717000a7b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-0O8ViX+QcBd3ZmGlcFTnYXZKGbFu09EhgD27tgTdGnkcYXLat4KIsBBQeKLR2xZDCXdIBAlWLkiXE1+rJpCxFw==", "signatures": [{"sig": "MEUCIQDF3X2L3Wp6Dku3twoOt9A92bMXpodWUfBWUK4baaIsUQIgQ8RRiMsRqiWJrVXEuGDRKUrlx06cVDVaS/ZsHyEzFYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2257293}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.31.0_1737291420256_0.6957563893448637", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "3bd5fcbab92a66e032faef1078915d1dbf27de7a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-3pA7xecItbgOs1A5H58dDvOUEboG5UfpTq3WzAdF54acBbUM+olDJAPkgj1GRJ4ZqE12DZ9/hNS2QZk166v92A==", "signatures": [{"sig": "MEQCIByNvNtHxiHtS+f39Yk90avtaeTe5TvXTLzdKcSNy+qYAiBLoAZyt++pwam+Jnuk6mnrR4Od4nRL7aBnWZUxBiD4CQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2259917}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.32.0_1737707267645_0.8641960158725517", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "9241efc2add3e972e3efe5597e4996042475a797", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-NqgOeg3aCD9+d/zUyxEEohLoCyMUh3M7FmWEe1FwAjc7/C606PkJsxnrESdXvakjkmSmWhNm3OvgDix0b3mLBw==", "signatures": [{"sig": "MEQCIHHFCqTFpAEBfVad9OHilJAxfxJXNc2CS7D0f5XC2CetAiB+FIpX1q93OwsI6te0r+Kdury+bBQgn6nDt91NhyWUNw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2259919}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.33.0-0_1738053021770_0.015550442416102817", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "92c212d1b38c105bd1eb101254722d27d869b1ac", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-PKvszb+9o/vVdUzCCjL0sKHukEQV39tD3fepXxYrHE3sTKrRdCydI7uldRLbjLmDA3TFDmh418XH19NOsDRH8g==", "signatures": [{"sig": "MEUCIEa114dbR36EPZULQq4Fmjkm906drHj5H4Yx4fZR4uzoAiEAoG6ztQjkwNyzQg8rhPSsZ7DF+3esskrG7SKIiiro+EI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2259917}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.32.1_1738053209952_0.25553130119369327", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "e29f74605d8355969318f1a7488e1d623b80ab54", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-KZLksFX9g6dZuuiRqC/qVLDn58U3fgDij8rAKqcznbIJi/J1laoE3A2xYZZCo0zITr14K5/4Gecr/HrrhCQ7/w==", "signatures": [{"sig": "MEUCIEBv0wHKTWFItEjQONETkJnXZwSy+BkkEAfLns4xW/65AiEA48L6BFWp2vVyQUeqqX4T+1eqBgf3JRpsBQ3qvbpXoYI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2239253}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.33.0_1738393933387_0.6573759400872201", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "dc04b05503ab0d1b0f646d3f15fef92c1cfb4a17", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-mCIw8j5LPDXmCOW8mfMZwT6F/Kza03EnSr4wGYEswrEfjTfVsFOxvgYfuRMxTuUF/XmRb9WSMD5GhCWDe2iNrg==", "signatures": [{"sig": "MEUCIBYMo+EuJ7drzK7ZlB9CbTdTbJVDBeHswxSQuF7zW4DbAiEA0tmUetJgTr8TyMrOxZLnG40XA0eDZzeJ7GXJoM+gOiQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2239253}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.34.0_1738399236031_0.7312514639242915", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "1ca88b1244678e74984efe89b1e39b9ae74671c7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-UBXdQ4+ATARuFgsFrQ+tAsKvBi/Hly99aSVdeCUiHV9dRTTpMU7OrM3WXGys1l40wKVNiOl0QYY6cZQJ2xhKlQ==", "signatures": [{"sig": "MEUCIEqrENHuDO7ZpxilBorgE/CR79if4UbxTJfoHwSd98cfAiEAuCxxrpR8u0OZ5EG6836C75hBN7YHHMKI9U/lixXx2UA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2239253}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.34.1_1738565906299_0.7670274123944425", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "abe62406d7864499e35e36563ddca0168508f816", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-lfqTpWjSvbgQP1vqGTXdv+/kxIznKXZlI109WkIFPbud41bjigjNmOAAKoazmRGx+k9e3rtIdbq2pQZPV1pMig==", "signatures": [{"sig": "MEYCIQDM9K3nmlOnUjjZ7M1VuWIQu7j8NP4POt+cA5w7wcEpmgIhAO7SCoywPoy9YgSMj7QajKzdg+MXAtH2EcYzZVqaIslL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2239253}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.34.2_1738656615721_0.5120077614208953", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "aaaa3f678ab3bcdf8ebda600ed2a9f04fe00d9cc", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-MW6N3AoC61OfE1VgnN5O1OW0gt8VTbhx9s/ZEPLBM11wEdHjeilPzOxVmmsrx5YmejpGPvez8QwGGvMU+pGxpw==", "signatures": [{"sig": "MEUCIQCgSx8dGxD3Cqlm6WQvNlZqvda/BOLpUUWLFTkjHxgdeQIgSA64Bnb9pL/hMoJSYuqqPNsiDJra9SpVmR8bGx5OPEs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2239253}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.34.3_1738747338644_0.09154078268211863", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "48abd91a8829048221cded46d88edc0fa20797a3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-kmT3x0IPRuXY/tNoABp2nDvI9EvdiS2JZsd4I9yOcLCCViKsP0gB38mVHOhluzx+SSVnM1KNn9k6osyXZhLoCA==", "signatures": [{"sig": "MEQCIDBhvlt8up/2DtQ1RIyneI0sDw0/JUKxYCIeFjsU1pGKAiBxnacbd8SZuH26dv1TnQa11NulcJiVSv/aheQ0WGefpg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2239253}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.34.4_1738791084506_0.8136561593930849", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "4481c1b9b64c64f5c41f88d33733b899a6326919", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-6jyiXKF9Xq6x9yQjct5xrRT0VghJk5VzAfed3o0hgncwacZkzOdR0TXLRNjexsEXWN8tG7jWWwsVk7WeFi//gw==", "signatures": [{"sig": "MEUCIEsPDqjpqoEggqXCYmqTlC7aMWt5elXYwP9rrDTXnNG6AiEAlUu9BF+wZliEe1t3e/weXQSO+gvRwwf0tHRQpYJXFT4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2236501}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.34.5_1738918395649_0.5495401886581035", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a844e1978c8b9766b169ecb1cb5cc0d8a3f05930", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-88fSzjC5xeH9S2Vg3rPgXJULkHcLYMkh8faix8DX4h4TIAL65ekwuQMA/g2CXq8W+NJC43V6fUpYZNjaX3+IIg==", "signatures": [{"sig": "MEYCIQDCPG5TQWdiJOBXchxbJVRF0dP6oselN+0M4jCLZ2dzNQIhAM9/Swq7wROV7NPPRUV1RLhXCbFSvRA0y4h/yeX//Jic", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2221821}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.34.6_1738945940468_0.07795864761123084", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "95f27e96f0eb9b9ae9887739a8b6dffc90c1237f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-Z0TzhrsNqukTz3ISzrvyshQpFnFRfLunYiXxlCRvcrb3nvC5rVKI+ZXPFG/Aa4jhQa1gHgH3A0exHaRRN4VmdQ==", "signatures": [{"sig": "MEUCIQDX3mukn8xlySl53FmCbY39FRUqVnQFDRIl1fnCsduNQgIgG5GQMv5oIAPG1Cd5usAf/OCWRTmKxW7OIY1CkrFvvCI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2226173}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.34.7_1739526852868_0.9507031798560424", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "980d6061e373bfdaeb67925c46d2f8f9b3de537f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-A4iphFGNkWRd+5m3VIGuqHnG3MVnqKe7Al57u9mwgbyZ2/xF9Jio72MaY7xxh+Y87VAHmGQr73qoKL9HPbXj1g==", "signatures": [{"sig": "MEUCIQDNsEogxAoZB5xdP9fQ2VZCN+AWNDIQfb9HlvvJQ+GWGwIgBpgxA80SWCqdSz1H7LLqo/udUNhBZIlhZHdy4PBboqE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2226173}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.34.8_1739773597993_0.7785756578262915", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "48c87d0dee4f8dc9591a416717f91b4a89d77e3d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-88I+D3TeKItrw+Y/2ud4Tw0+3CxQ2kLgu3QvrogZ0OfkmX/DEppehus7L3TS2Q4lpB+hYyxhkQiYPJ6Mf5/dPg==", "signatures": [{"sig": "MEYCIQCAgteC161st92TpAE7Idx1Zb3dwY7jBCuVD8n4mZ6dLAIhALjbKlR/U5Z4Chzj2Tiw1Webfh52a1fAoN3xPO/8cZNy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2340229}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.34.9_1740814370090_0.15806681963666946", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d300b74c6f805474225632f185daaeae760ac2bb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-mrA0v3QMy6ZSvEuLs0dMxcO2LnaCONs1Z73GUDBHWbY8tFFocM6yl7YyMu7rz4zS81NDSqhrUuolyZXGi8TEqg==", "signatures": [{"sig": "MEUCIQC8ZgQ3/LoKz0jCnHai0H5jPQKzbQD/OMeelSaJZJWFYQIgAPiy6SfMIY+XRNKm0s9DGI/IkMbXt1E1u6Ubltc+gd8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2343813}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.35.0_1741415099176_0.007802176050807796", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a66910c6c63b46d45f239528ad5509097f8df885", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-bvXVU42mOVcF4le6XSjscdXjqx8okv4n5vmwgzcmtvFdifQ5U4dXFYaCB87namDRKlUL9ybVtLQ9ztnawaSzvg==", "signatures": [{"sig": "MEYCIQDCTxyXgvUIVr9x+9lhkzBQSLBENSKy093zvOFI64c2NQIhAN2dVl6PurKAO0QZcJW7kOCEDYv9djCuVM+fi4CGncau", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2349189}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.36.0_1742200556898_0.44029644682471525", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "5addf1a51e1495ae7ff28d26442a88adf629c980", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-hhAALKJPidCwZcj+g+iN+38SIOkhK2a9bqtJR+EtyxrKKSt1ynCBeqrQy31z0oWU6thRZzdx53hVgEbRkuI19w==", "signatures": [{"sig": "MEUCICPNcjwDcTGQlPHdCedBAyrJDft6HmSHt7mfZXKfo206AiEA5bRIwM6d935AyPEE6WwVjggH4/szGaj4biyyXl3O04Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2347269}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.37.0_1742741839168_0.8038981696516341", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "7de1584c09adcac08f90d1e500c679c428b6eb36", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-mimPH43mHl4JdOTD7bUMFhBdrg6f9HzMTOEnzRmXbOZqjijCw8LA5z8uL6LCjxSa67H2xiLFvvO67PT05PRKGg==", "signatures": [{"sig": "MEUCIGsSATLegKRllg/0F6ezQVqbak6sPSvx5n4VcZOPUUuFAiEA57WyK4Im48Eb9VF+cEco86BpKdukp5sdk7g7Rbg9V4k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2347205}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.38.0_1743229760500_0.32749083319113614", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a1f4f963d5dcc9e5575c7acf9911824806436bf7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-mz5POx5Zu58f2xAG5RaRRhp3IZDK7zXGk5sdEDj4o96HeaXhlUwmLFzNlc4hCQi5sGdR12VDgEUqVSHer0lI9g==", "signatures": [{"sig": "MEQCIF1LV7v6hi9LZh39yPwnjMmmdqvZU9E1pe3XH+EK1QvRAiBmplPVoWUE9L3bkDGjHEOui5DhJJq5lNrik6tH7Hkhig==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2347205}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.39.0_1743569385390_0.2786101456169865", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "d80cd62ce6d40f8e611008d8dbf03b5e6bbf009c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-y/qUMOpJxBMy8xCXD++jeu8t7kzjlOCkoxxajL58G62PJGBZVl/Gwpm7JK9+YvlB701rcQTzjUZ1JgUoPTnoQA==", "signatures": [{"sig": "MEUCID9a5hyuSEI09K8i1/byE1B3//cSVoBBJUML06GC3FcVAiEA/9c6kMyEG6u/JnQf3yMyZsjjaVv20quTpIJG8zIVAZ0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2374605}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.40.0_1744447188853_0.7200132007419069", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "c8814bb5ce047a81b1fe4a33628dfd4ac52bd864", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-ehSKrewwsESPt1TgSE/na9nIhWCosfGSFqv7vwEtjyAqZcvbGIg4JAcV7ZEh2tfj/IlfBeZjgOXm35iOOjadcg==", "signatures": [{"sig": "MEYCIQCy8vSGmR7V8+P5b+EQa9AtcG8kxHISLEZ8WIXAFZhVzgIhALkTq+8HK1aKt9/xhZ8M6xtOCtDOq0py0uPpuKDLfaxb", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2380901}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.40.1_1745814936347_0.8114263010730325", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "819691464179cbcd9a9f9d3dc7617954840c6186", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-de6TFZYIvJwRNjmW3+gaXiZ2DaWL5D5yGmSYzkdzjBDS3W+B9JQ48oZEsmMvemqjtAFzE16DIBLqd6IQQRuG9Q==", "signatures": [{"sig": "MEYCIQD4dNKA/l7ok1aY+5f352jd+tT3KAuP/7628hw4/pQALwIhAP3cCAbetPNQ7p41JLHQrYkUEW+6sgF3eH0/CM1LIDbt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2386085}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.40.2_1746516426428_0.5639002915556797", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "643e3ad19c93903201fde89abd76baaee725e6c2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-46OzWeqEVQyX3N2/QdiU/CMXYDH/lSHpgfBkuhl3igpZiaB3ZIfSjKuOnybFVBQzjsLwkus2mjaESy8H41SzvA==", "signatures": [{"sig": "MEUCIQCxA2l9Q5iZudtE7lwnnDENtgIwl0qdJYs5OhQwzHcD5AIgSJ4dI7laEJvmua7e+kuMhNim8lM2f/RrhL/O2fIR9CM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2580597}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.41.0_1747546424056_0.8830851168516334", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "49f766c55383bd0498014a9d76924348c2f3890c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-wC53ZNDgt0pqx5xCAgNunkTzFE8GTgdZ9EwYGVcg+jEjJdZGtq9xPjDnFgfFozQI/Xm1mh+D9YlYtl+ueswNEg==", "signatures": [{"sig": "MEUCIHZ9GpDCEs8+MwzDqTsJCRVURZ4hG13EqzsRp/uWmbUxAiEA1iRNCE0hCOc4cNahqak7E0yo9l+PZhPOiPspQaM2ZYA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2692877}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.41.1_1748067287633_0.8825111885374366", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "77f2d60fb7bf31e3282561c94221aebb68ed4059", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-q2aqiDKNKNj9jG/6JIHakx1hmj6A8bxc3U6rpZjL8znx3O4OGpPJcyEiJSxmFd8olpjfRezh7PONU+3RTQmj0A==", "signatures": [{"sig": "MEUCIQD6Xh0fdMEP7/e+UYc+o95VDF3zaWNLVBZXQsBeOJysqwIgbKWRuCM0lXFUdSeim5cnf+eGuDw5MNTMbOBKsqECLnM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2677581}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.41.2_1749210045963_0.6050811993553047", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "05e881cc69f59415fe8c1af13554c60c7c49d114", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-UsQD5fyLWm2Fe5CDM7VPYAo+UC7+2Px4Y+N3AcPh/LdZu23YcuGPegQly++XEVaC8XUTFVPscl5y5Cl1twEI4A==", "signatures": [{"sig": "MEUCIQC923W5kP+1CHb2PHwMWA8eKF6NDdaat75qT+5DlbzqNwIgBZEvh+ixts8uuPWDRij3mz3PXsHldDvz63Xf3RS9ssc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2677581}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.42.0_1749221306337_0.5403150722927947", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "a59afc092523ebe43d3899f33da9cdd2ec01fb87", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-gTJ/JnnjCMc15uwB10TTATBEhK9meBIY+gXP4s0sHD1zHOaIh4Dmy1X9wup18IiY9tTNk5gJc4yx9ctj/fjrIw==", "signatures": [{"sig": "MEMCIFnsGhYBiRr1/dozDcx7hsObriEqHy1usdzXTlt5nTNBAh8hiEszqdM1YvuTPGgeCLptM25Dgi/bhbTjlokhR7D3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2677581}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.43.0_1749619373805_0.9882813539100719", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "feb81bd086f6a469777f75bec07e1bdf93352e69", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-x+e/Z9H0RAWckn4V2OZZl6EmV0L2diuX3QB0uM1r6BvhUIv6xBPL5mrAX2E3e8N8rEHVPwFfz/ETUbV4oW9+lQ==", "signatures": [{"sig": "MEUCIEUPrgpq31mdZah7klE6yrDsQHuvHOC3Mh7gzqdSx2HfAiEAv1+mGlFMLdSlRATxbmHw0fRB9FY6OZbWCHV+sMf5lgc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2687117}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.44.0_1750314192057_0.49345983360580714", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm"], "dist": {"shasum": "f6f1c42036dba0e58dc2315305429beff0d02c78", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-n0edDmSHlXFhrlmTK7XBuwKlG5MbS7yleS1cQ9nn4kIeW+dJH+ExqNgQ0RrFRew8Y+0V/x6C5IjsHrJmiHtkxQ==", "signatures": [{"sig": "MEYCIQCLvj0AFRyVZokWzbWyAGRzDnQDOBdSRfobOo4zEhPoJQIhAJP9ChZ+dkqtjKzBxVw+OWSZ6D8ZPSMXNV1LV+8wBjga", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2687117}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm-gnueabihf_4.44.1_1750912469711_0.24926251484436568", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-linux-arm-gnueabihf", "version": "4.44.2", "os": ["linux"], "cpu": ["arm"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-arm-gnueabihf.node", "_id": "@rollup/rollup-linux-arm-gnueabihf@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-+xmiDGGaSfIIOXMzkhJ++Oa0Gwvl9oXUeIiwarsdRXSe27HUIvjbSIpPxvnNsRebsNdUo7uAiQVgBD1hVriwSQ==", "shasum": "7ba5c97a7224f49618861d093c4a7b40fa50867b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2635533, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDM82i4Nsr0344h00cxha3FAa5rEgqwEnAeV1D7k/78bAIhALmHZ6p50/ubGWJn+Wl7owPJTVgtd4vywurvtqTv4HT6"}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-arm-gnueabihf_4.44.2_1751633782391_0.43364580083546866"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-07-31T19:17:59.768Z", "modified": "2025-07-04T12:56:22.861Z", "4.0.0-0": "2023-07-31T19:17:59.993Z", "4.0.0-1": "2023-08-01T04:49:04.619Z", "4.0.0-2": "2023-08-01T11:16:38.200Z", "4.0.0-3": "2023-08-04T08:17:04.405Z", "4.0.0-4": "2023-08-04T11:36:48.429Z", "4.0.0-5": "2023-08-20T06:56:55.654Z", "4.0.0-6": "2023-08-20T07:51:44.006Z", "4.0.0-7": "2023-08-20T10:33:38.145Z", "4.0.0-8": "2023-08-20T11:22:30.384Z", "4.0.0-9": "2023-08-20T14:29:16.129Z", "4.0.0-10": "2023-08-21T15:30:14.673Z", "4.0.0-11": "2023-08-23T10:16:05.531Z", "4.0.0-12": "2023-08-23T14:40:38.592Z", "4.0.0-13": "2023-08-24T15:48:41.807Z", "4.0.0-14": "2023-09-15T12:34:32.300Z", "4.0.0-15": "2023-09-15T13:06:59.514Z", "4.0.0-16": "2023-09-15T14:17:23.729Z", "4.0.0-17": "2023-09-15T14:59:14.057Z", "4.0.0-18": "2023-09-15T16:10:20.937Z", "4.0.0-19": "2023-09-15T18:51:04.854Z", "4.0.0-20": "2023-09-24T06:10:44.440Z", "4.0.0-21": "2023-09-24T17:22:31.137Z", "4.0.0-22": "2023-09-26T16:17:37.449Z", "4.0.0-23": "2023-09-26T20:14:31.005Z", "4.0.0-24": "2023-10-03T05:12:54.065Z", "4.0.0-25": "2023-10-05T14:13:05.780Z", "4.0.0": "2023-10-05T15:14:47.968Z", "4.0.1": "2023-10-06T12:36:52.132Z", "4.0.2": "2023-10-06T14:18:51.880Z", "4.1.0": "2023-10-14T05:52:23.287Z", "4.1.1": "2023-10-15T06:31:57.476Z", "4.1.3": "2023-10-15T17:48:36.264Z", "4.1.4": "2023-10-16T04:34:18.762Z", "4.1.5": "2023-10-28T09:23:41.783Z", "4.1.6": "2023-10-31T05:45:25.075Z", "4.2.0": "2023-10-31T08:10:50.731Z", "4.3.0": "2023-11-03T20:13:12.828Z", "4.3.1": "2023-11-11T07:58:03.177Z", "4.4.0": "2023-11-12T07:50:01.665Z", "4.4.1": "2023-11-14T05:25:31.373Z", "4.5.0": "2023-11-18T05:52:20.165Z", "4.5.1": "2023-11-21T20:13:17.602Z", "4.5.2": "2023-11-24T06:29:55.241Z", "4.6.0": "2023-11-26T13:39:21.542Z", "4.6.1": "2023-11-30T05:23:17.604Z", "4.7.0": "2023-12-08T07:58:10.208Z", "4.8.0": "2023-12-11T06:25:03.953Z", "4.9.0": "2023-12-13T09:24:25.348Z", "4.9.1": "2023-12-17T06:26:19.032Z", "4.9.2": "2023-12-30T06:23:34.937Z", "4.9.3": "2024-01-05T06:20:54.252Z", "4.9.4": "2024-01-06T06:39:07.865Z", "4.9.5": "2024-01-12T06:16:20.998Z", "4.9.6": "2024-01-21T05:52:26.379Z", "4.10.0": "2024-02-10T05:58:48.787Z", "4.11.0": "2024-02-15T06:09:49.989Z", "4.12.0": "2024-02-16T13:32:29.246Z", "4.12.1": "2024-03-06T06:03:45.146Z", "4.13.0": "2024-03-12T05:28:48.504Z", "4.13.1-1": "2024-03-24T07:39:31.640Z", "4.13.1": "2024-03-27T10:27:50.570Z", "4.13.2": "2024-03-28T14:13:46.963Z", "4.14.0": "2024-04-03T05:23:03.163Z", "4.14.1": "2024-04-07T07:35:47.564Z", "4.14.2": "2024-04-12T06:23:47.975Z", "4.14.3": "2024-04-15T07:18:39.233Z", "4.15.0": "2024-04-20T05:37:22.622Z", "4.16.0": "2024-04-21T04:42:15.640Z", "4.16.1": "2024-04-21T18:30:09.117Z", "4.16.2": "2024-04-22T15:19:26.837Z", "4.16.3": "2024-04-23T05:12:42.324Z", "4.16.4": "2024-04-23T13:15:14.956Z", "4.17.0": "2024-04-27T11:29:59.530Z", "4.17.1": "2024-04-29T04:58:02.621Z", "4.17.2": "2024-04-30T05:00:56.447Z", "4.18.0": "2024-05-22T05:03:53.056Z", "4.18.1": "2024-07-08T15:25:20.937Z", "4.19.0": "2024-07-20T05:46:23.322Z", "4.19.1": "2024-07-27T04:54:09.709Z", "4.19.2": "2024-08-01T08:33:02.523Z", "4.20.0": "2024-08-03T04:48:59.541Z", "4.21.0": "2024-08-18T05:55:42.627Z", "4.21.1": "2024-08-26T15:54:23.039Z", "4.21.2": "2024-08-30T07:04:34.673Z", "4.21.3": "2024-09-12T07:05:58.618Z", "4.22.0": "2024-09-19T04:55:40.533Z", "4.22.1": "2024-09-20T08:22:02.552Z", "4.22.2": "2024-09-20T09:33:54.175Z", "4.22.3-0": "2024-09-20T14:48:07.901Z", "4.22.3": "2024-09-21T05:03:18.742Z", "4.22.4": "2024-09-21T06:11:30.647Z", "4.22.5": "2024-09-27T11:48:25.777Z", "4.23.0": "2024-10-01T07:10:16.417Z", "4.24.0": "2024-10-02T09:37:30.048Z", "4.24.1": "2024-10-27T06:43:14.091Z", "4.24.2": "2024-10-27T15:40:20.820Z", "4.25.0-0": "2024-10-29T06:15:24.294Z", "4.24.3": "2024-10-29T14:14:21.392Z", "4.24.4": "2024-11-04T08:47:21.880Z", "4.25.0": "2024-11-09T08:37:35.314Z", "4.26.0": "2024-11-13T06:45:12.171Z", "4.27.0-0": "2024-11-13T07:03:26.057Z", "4.27.0-1": "2024-11-14T06:33:23.759Z", "4.27.0": "2024-11-15T10:40:48.545Z", "4.27.1-0": "2024-11-15T13:28:21.174Z", "4.27.1-1": "2024-11-15T15:38:16.763Z", "4.27.1": "2024-11-15T16:07:54.980Z", "4.27.2": "2024-11-15T17:20:17.332Z", "4.27.3": "2024-11-18T16:39:49.285Z", "4.27.4": "2024-11-23T07:00:32.866Z", "4.28.0": "2024-11-30T13:16:00.754Z", "4.28.1": "2024-12-06T11:45:09.284Z", "4.29.0-0": "2024-12-16T06:40:07.136Z", "4.29.0-1": "2024-12-19T06:37:43.625Z", "4.29.0-2": "2024-12-20T06:56:16.517Z", "4.29.0": "2024-12-20T18:37:38.309Z", "4.29.1": "2024-12-21T07:16:15.831Z", "4.30.0-0": "2024-12-21T07:17:28.087Z", "4.30.0-1": "2024-12-30T06:52:30.843Z", "4.29.2": "2025-01-05T12:07:56.854Z", "4.30.0": "2025-01-06T06:36:52.650Z", "4.30.1": "2025-01-07T10:36:05.904Z", "4.31.0-0": "2025-01-14T05:57:55.358Z", "4.31.0": "2025-01-19T12:57:00.448Z", "4.32.0": "2025-01-24T08:27:47.839Z", "4.33.0-0": "2025-01-28T08:30:21.951Z", "4.32.1": "2025-01-28T08:33:30.197Z", "4.33.0": "2025-02-01T07:12:13.597Z", "4.34.0": "2025-02-01T08:40:36.268Z", "4.34.1": "2025-02-03T06:58:26.503Z", "4.34.2": "2025-02-04T08:10:15.989Z", "4.34.3": "2025-02-05T09:22:18.916Z", "4.34.4": "2025-02-05T21:31:24.719Z", "4.34.5": "2025-02-07T08:53:15.953Z", "4.34.6": "2025-02-07T16:32:20.677Z", "4.34.7": "2025-02-14T09:54:13.109Z", "4.34.8": "2025-02-17T06:26:38.223Z", "4.34.9": "2025-03-01T07:32:50.324Z", "4.35.0": "2025-03-08T06:24:59.445Z", "4.36.0": "2025-03-17T08:35:57.211Z", "4.37.0": "2025-03-23T14:57:19.384Z", "4.38.0": "2025-03-29T06:29:20.744Z", "4.39.0": "2025-04-02T04:49:45.646Z", "4.40.0": "2025-04-12T08:39:49.066Z", "4.40.1": "2025-04-28T04:35:36.568Z", "4.40.2": "2025-05-06T07:27:06.657Z", "4.41.0": "2025-05-18T05:33:44.297Z", "4.41.1": "2025-05-24T06:14:47.913Z", "4.41.2": "2025-06-06T11:40:46.217Z", "4.42.0": "2025-06-06T14:48:26.593Z", "4.43.0": "2025-06-11T05:22:54.045Z", "4.44.0": "2025-06-19T06:23:12.268Z", "4.44.1": "2025-06-26T04:34:29.953Z", "4.44.2": "2025-07-04T12:56:22.591Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-linux-arm-gnueabihf`\n\nThis is the **armv7-unknown-linux-gnueabihf** binary for `rollup`\n", "readmeFilename": "README.md"}