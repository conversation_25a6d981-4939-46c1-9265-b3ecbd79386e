{"_id": "sass-embedded", "_rev": "99-946cb793014d0d229a2c53b837994f0d", "name": "sass-embedded", "dist-tags": {"latest": "1.89.2"}, "versions": {"1.0.0-beta.0": {"name": "sass-embedded", "version": "1.0.0-beta.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.0.0-beta.0", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "c6de5bf1275ea5c03be111fa8af7238f266bec52", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.0.0-beta.0.tgz", "fileCount": 48, "integrity": "sha512-8B7Sf46Ra1ZOfryauAYj6Whmga2gqFkMuhahnsBhz3325aPpRmf/8vCR9D8d0b2Ctrd+dWQIc61ANUK7ZyR+bw==", "signatures": [{"sig": "MEYCIQCROB9wUkO5EaaLu1sORIH1CnL5flFMc3/dIHj4G9WpEwIhAOsL+Z8wtTijCU6XX7aJGunzlziveEjO+TMIdiIHNRNs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 632673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeb/UCRA9TVsSAnZWagAApnoQAKA5fwc5gPjDvXxYQSo4\nBbI5/BYA9rIjMW78lqAuR57MdIPQRX73b2basyd8MeL/HJJziGvNfKQMWEHZ\nJMnIHGeh9bqgy6pe61fHOUE533LBTY/HUi/8Vhy9F5rnBYKXMMvli/1gqOAa\ncwmTc3kMzmgfSb5UNh5owvz3/iomj6P/FExJa9XWZtbVnuY8toJhtyAQVYTU\n+SqODDJLSj/d/HiRPSF5ZmIUWiyvbgzX5jS2RH3p26CASdQl2BbaSXzazrqr\nbbpTRpoc/lShmJ7vQRTD3f6O4P5MQGTvmJHzDUQnoAEDmS6S5j2CnUW9rtOz\n+1KmvIofhsklL49eGfLkWeKfs34fKrWBFlBlJWAiKh4+QhViBMMJa7CrRkYg\nGLT5ItlqR8SWuefvVNnHO/gnl71SIPX7Z9hpkODuti+9FlsIMw2nnFCP4Mb6\ngaRYijJhnA18AFUJTn/8RXQrvSE862B5977GZpXStXbbBVenpvp5oQKHTxvU\nWs3DClGQmjuFTjkrIoAr4GBgiP0tvXwyc5bu5Hs6d4c56pb7FFgAv7HriRx5\n7bFg0/yZ82smcnU4AkWXGO0deHHdrDho2MBLwAaroVM9T/GsrJ6pwfERrlnL\nQP7IEJ2854WjyjbxFYA7GhoLHgn7QqE7fPyOUvengZr+iSk2E6K7sk8eKQtz\n68V9\r\n=YOrV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/lib/index.d.ts", "engines": {"node": ">=12.18.2"}, "gitHead": "8afbbd91b648519cc8a62bda9c3ee06ced8fc35b", "scripts": {"fix": "gts fix", "test": "ts-node ./node_modules/jasmine/bin/jasmine", "build": "ts-node ./tool/prepare-dev-environment.ts", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^6.5.5", "semver": "^5.7.1", "shelljs": "^0.8.4", "immutable": "^4.0.0-rc.12", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^2.0.2", "yargs": "^16.2.0", "protoc": "^1.0.4", "jasmine": "^3.5.0", "ts-node": "^8.10.2", "@types/tar": "^4.0.3", "source-map": "^0.6.1", "typescript": "~3.8.0", "@types/node": "^12.18.2", "@types/yargs": "^15.0.12", "@types/semver": "^7.3.4", "ts-protoc-gen": "^0.12.0", "@types/jasmine": "^3.5.11", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "jasmine-spec-reporter": "^5.0.2", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.0.0-beta.7", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.0.0-beta.0_1618591699985_0.9061330322824523", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.1": {"name": "sass-embedded", "version": "1.0.0-beta.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.0.0-beta.1", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "3997aa2e3597cf5529b2a6d90e2be922753ccc1a", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.0.0-beta.1.tgz", "fileCount": 48, "integrity": "sha512-D2lArV7yPeViSxiyPmbj8HtmM9In6GY9Qkqq5YqLNweyPpU5gyi7I4dBAvCCQKLe7QOeWy5IzAx61TyFm0bw9w==", "signatures": [{"sig": "MEUCIGwzuylbw0VdlefXBs8ZDZ1dn53m/hgpQBk8Fy/9aRLwAiEAtzwY3dje2ttCaOTd2KBhB/8XPxjDDbWh3s750CG0J4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 632829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfdPHCRA9TVsSAnZWagAAy4MP/1HFX/GV1cTFfMn8wV+P\np4GoVxSKuefXN4xVcSMvIzbJanwQYccCBCKdmDmfWG/nYdyF/k7BAbfy4mJ5\nAwcLSWzcb8XtSEHjU81sMgv1XowYZpH0wvdOAGzCkSszwjAvAbyGaOq6S9xN\naSZevH9f8RjHp0eD9az87Fe+G1s9/eIgCIyIwWIgGPWlwf1NMLs8A1fumYm2\n1wmsroRCtJPo99/DmohkpZwpjvVJHFzFNf3yKA5taXYnCX/Ke40awih8NdKE\nTVf+OQXbfO6De95LKRbMQVWyreEywdoUL3yXTW1vWtFv1cEBQsPwg/wmn80C\n+oH2sitoY7C5vcyF8I5MIQ/jvEDG8E53gXoQNjyM6Tg0mDI7RpDnRGAk6rpy\nmPYgIZzLmwDEw1xKhzIAyr+gv3rcxbSlYu/9Gf9+uQT6vMgyNcsQlfJiSfpk\nRDk8PHtADJTbaaBwbCaQixvYHx0avyRVbaYslG+x4pgFrOSlA5zzbCzJ/OcV\nG1IHAdcsroeOeLY4m1HTM3LlvCK+oE8hFl3VKTTo757XWsZZyEpbbIU/6Y/v\ne46pCadSaxDW/2MX3F/q7pG8/q3dpovxz62f+62VOxh4wtffJWnN+uy1YCzI\nHdLFpxE/GkPtAhXbjcVHm6++F8x+OajLy+dm76/L6gSDMceilv07EFVGs0Gt\ntZX5\r\n=yvxC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/lib/index.d.ts", "engines": {"node": ">=12.18.2"}, "gitHead": "661fa06465e8cae1ecc518e11dc6dae24dccb573", "scripts": {"fix": "gts fix", "test": "ts-node ./node_modules/jasmine/bin/jasmine", "build": "ts-node ./tool/prepare-dev-environment.ts", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^6.5.5", "semver": "^5.7.1", "shelljs": "^0.8.4", "immutable": "^4.0.0-rc.12", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^2.0.2", "yargs": "^16.2.0", "protoc": "^1.0.4", "jasmine": "^3.5.0", "ts-node": "^8.10.2", "@types/tar": "^4.0.3", "source-map": "^0.6.1", "typescript": "~3.8.0", "@types/node": "^12.18.2", "@types/yargs": "^15.0.12", "@types/semver": "^7.3.4", "ts-protoc-gen": "^0.12.0", "@types/jasmine": "^3.5.11", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "jasmine-spec-reporter": "^5.0.2", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.0.0-beta.7", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.0.0-beta.1_1618858950771_0.2952899115630083", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.3": {"name": "sass-embedded", "version": "1.0.0-beta.3", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.0.0-beta.3", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "dc59b2a8187f3333cd5256e2535f4e78baa4c38e", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.0.0-beta.3.tgz", "fileCount": 57, "integrity": "sha512-20tcW7LQKKbRx7sb+Om3AAdEVvhbuVHhefzylL72tlsLxtumx6ml9nughZZ5qfHeOj3SYCDuddp8IYgJej/gEQ==", "signatures": [{"sig": "MEYCIQDYTBwxx/oheeEMlTc1lXzVK690Czab6pmYrBSYDMWaAwIhALEd9xob4BYnxAv7SC23s/YXw1sKSEEXCHxWM7kqdDtJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 680923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHDAhCRA9TVsSAnZWagAA5j4P/0+6XPPr4kQ1SOcu9n0V\nNvHpjJNakaMiEqNIOSqQwAqRwsL3U3s89ql2opmoS2+sLx8JsU0YQa8nQB87\nAniFJKQVk8gYtnpQoorpihrAgqX+Jm0q6YIioSrAh6Bovhtm5i/W2kOJlWhS\n/w/KQ6NDyyFWrir+zfmkrVtrXbutiJO6cwDqJ6i7BJKfNzPwGgPjKeHeTWuP\nw5TS0Mc/nrv32LYLoAK65RWEvbWrpdTfBXYaGGEeiA06KtPH3pBw+zT/mfkC\nsfZ19hs6Yd02+1fKhTnvBBwyaRBmIc8KxRjSsMh9JjGofTWpZm9zjggW5ziu\nXq79ZVVhCHD6NJeagFZFG9+7ZgBvekEOR0oPuz6TqiUzugRpgG3z2xApp0jA\ni8hlpQI38JHQiKQ9eq/JNsjDWcxgs3w8JbcGebetJ2rPhPEjIiEUpD2nnArV\nJr91OQmWbXkhWTYxBKILp2JEwltnqHnpb2HAOf2rISvMngzRBAWWFdR0jRUF\nJzSm+WsePZFgOfkz7/jU2qedF+46S5FA3t4sUt0q0qAKqlXpgSTjDMBlFcQD\nKE5lMcMOSQGvAoeCK07PDnvOMEGaojhrFEUoJSyEbpb4LZN/ETnzlPINtlR7\nDELJKfUzUH0cKVkXrqT+dXAbmORMsKjzBSX6Gr3kJikReJ7Cq+qd1fNtBTv6\n6H+p\r\n=FI/+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/lib/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "17e71811d3f558b01b6e3be21c09e872a1a5ab88", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "ts-node ./node_modules/jasmine/bin/jasmine", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^6.5.5", "semver": "^5.7.1", "shelljs": "^0.8.4", "immutable": "^4.0.0-rc.12", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^2.0.2", "yargs": "^16.2.0", "protoc": "^1.0.4", "jasmine": "^3.5.0", "ts-node": "^8.10.2", "@types/tar": "^4.0.3", "source-map": "^0.6.1", "typescript": "~3.8.0", "@types/node": "^12.18.2", "@types/yargs": "^15.0.12", "@types/semver": "^7.3.4", "ts-protoc-gen": "^0.12.0", "@types/jasmine": "^3.5.11", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "jasmine-spec-reporter": "^5.0.2", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.0.0-beta.9", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.0.0-beta.3_1629237281717_0.7977254246715118", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.4": {"name": "sass-embedded", "version": "1.0.0-beta.4", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.0.0-beta.4", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "774b8b2fb23702bd15ecc1e63faf409e9137b80a", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.0.0-beta.4.tgz", "fileCount": 70, "integrity": "sha512-P/kG+SuxhKPIorYIbHbtj/EhDGlNnOSJmNiADgEbw4KuxHSrF4nUbX+uWrvuLzr/lNvoq717Cv4cmcqV5pt01w==", "signatures": [{"sig": "MEQCIFK9qZ2nmD3CYpQjpA9BAxosq/+SIq8KCfrN1aA7e73hAiBWnbI7RLncQZdV8iJ3uQsRTUQmDjS+z7pvIZo0tANOoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 719537}, "main": "dist/lib/index.js", "types": "dist/lib/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "dedeb975000915201bce20b7d88bfa02aa55303e", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "ts-node ./node_modules/jasmine/bin/jasmine", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^6.5.5", "semver": "^5.7.1", "shelljs": "^0.8.4", "immutable": "^4.0.0-rc.12", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^2.0.2", "yargs": "^16.2.0", "protoc": "^1.0.4", "jasmine": "^3.5.0", "ts-node": "^8.10.2", "@types/tar": "^4.0.3", "source-map": "^0.6.1", "typescript": "~3.8.0", "@types/node": "^12.18.2", "@types/yargs": "^15.0.12", "@types/semver": "^7.3.4", "ts-protoc-gen": "^0.12.0", "@types/jasmine": "^3.5.11", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "jasmine-spec-reporter": "^5.0.2", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.0.0-beta.9", "protocol-version": "1.0.0-beta.11", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.0.0-beta.4_1632953293815_0.21256052035686235", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.5": {"name": "sass-embedded", "version": "1.0.0-beta.5", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.0.0-beta.5", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "0edf7fe999d81e5de7f6fa6354fb7ba517dac1a4", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.0.0-beta.5.tgz", "fileCount": 79, "integrity": "sha512-07lI9vN4nFeOw1NOPrBu0GuJjMpY+KbOtHrDbgURfB3HsplyCdhzn109JApesGRMkStfv+gNcPLZIrjoqOi3kQ==", "signatures": [{"sig": "MEUCIAIY2Dk6eAsKZ0erUEbmQn8fBaNISMybP6XP3zPbvs6RAiEA9yutPZT2G2WL3r6dD/fw5fGJSoiaQ8SoAGftAQ1Ltww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 764349}, "main": "dist/lib/index.js", "types": "dist/lib/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "ea074cd4ab353d844181f46ddb3fea70559ffcb3", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.0.0-beta.9", "protocol-version": "1.0.0-beta.11", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.0.0-beta.5_1636394158577_0.5127405850358624", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.7": {"name": "sass-embedded", "version": "1.0.0-beta.7", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.0.0-beta.7", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "a861750779d5deba3c74f3a0429ffe5cf31caa94", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.0.0-beta.7.tgz", "fileCount": 91, "integrity": "sha512-0yhvZpFx/LhQU21MFWcMuYh9HvvLtNJzUOvketZJtiFJKHdSyx88s1EwP2gCcrVCXmGtF+zxk2U/I8Hg4KYYNg==", "signatures": [{"sig": "MEUCIBbyVdDpIF08EmKC/FiNVJ9Kql2yq7f3cQRoMJaSfWZ9AiEAwjYs5EWkbvEEAyS+mG7YJv5IgHBylOifgl7WaMCwsF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1000217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzOhqCRA9TVsSAnZWagAAfZMP/3557mLT1DQuyeX2GSDI\n8yKgBRkjNpjmTgAuW1Cb4TzJwV4as3Jl6nIv8HQ/XGuXfOcHR2HxiqFqtQk3\nWJJleCZl9Ys2r1gdjGA8tne7isRPakYAKq8k4Vx6o+b4Ao2gayC/cTdL+hrD\nMtpDgXWv6FWYdZVIQXWpLOxgqtAuMkML2hreLAvL6m+I+FlIlQFwLo6l8C5q\nuccu/urP+sQ5fhHpVyLzzXc7s6LcaEXsrBCeFyOaAEG5HMMGnDLnT3e03UPi\n6hrQomN7POp1FndRvHH5RwB30Iu98CN7d6KY9z9WkQYnJTeJcdMzdK3nL7oB\nPXIM1bobsk9JarELYMnmIa4hAO7G6/ll0nJGu+W8gTREhUQ5NxxamuMAO/CD\nT0Uqp94PF77Ztw7cjQrKI/lcGYssIA6vNDWstPIY2QiEJoX196773VEGGKID\nsyKDN9/GzoxwF77nZA/qNIygb+F1LGINf5KKCSBZxMGcjQC3Fc2wxKSwmj6Z\n+1o1yBBQcugLbaeOseATMU0dAr1NAAhtycqY9cFdkFT0O86LMzYk0VunUcvn\nB2QT533pKhcSDFeY3REdHRkUe+a0ynvsvajWJKa8x8fGkgjm5KwnOIbYBn/8\nMYpZOz+IgPqp98koqPEIUXlT3m5pshCL7M5XMarC9GLWeWIp1DvwKUOh9pj8\nLDRb\r\n=G3uj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "00888e0c0db32fdcf44dac2affc2748092f35feb", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.0.0-beta.14", "protocol-version": "1.0.0-beta.16", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.0.0-beta.7_1640818794695_0.7421400491814043", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.8": {"name": "sass-embedded", "version": "1.0.0-beta.8", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.0.0-beta.8", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "d0d1fb246d75d6a4a134a3da558e58fcf50db8ba", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.0.0-beta.8.tgz", "fileCount": 97, "integrity": "sha512-SnvTp/gKqFoqj/7DDWWqiZTs/8gCd6WjH0vl2jGOlt/CwxWO8NU0u9Hpo3zUMfHjBvJnVo5HgRfaJSsiAjKvkQ==", "signatures": [{"sig": "MEQCIFza7tIwXGJAjn0CXQenYRBDRIjuLMonUoFCxwJ91rwrAiAsIVscEPUK5U24STeyq4R3EsJ5K3WsmqFy7637uwl/8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1025257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1jccCRA9TVsSAnZWagAAb3AP/2OPm5yqF39kBkAXUii2\nasq27Uj1kWL24tWX+SMiTepzehcK4cJs8/CPtGel9GpVtW9RhAIYRiFLW/1u\nBsMbjVS3b8rH0CZk7Um8Xs6wzsDbraYWXmgP2+7KFXjJwFs/eemPSVCQ7NJd\nOqF0oLIVUXDFcRzG2ddj16ZfCZWTwyw+/hjswQgtwif8RLLQMeyDMcilJVLw\nU5cXkmOOXrK4OpZ/LEyZJsZTeDbwxC42F+ICnLAUWk0PVwBCnt5za20xQAlY\noQ2+2A3ESPV7m8YGpQrywAVij4N3UG3yiAgMOcRtt4u5Bav61q6Zma1rgWYn\ny+RnJWjA/tK5DH/dR1wvJSnNJ8+3f2T8o6Ms7VaAinWiJLSwMFZ4NWKVcDSG\nJMym5RKJAC9QNp4urdu9nIqFLiAc7HxO0cFahadjrvLl7VQ+4LhvQKD5bXua\nekmkZa+3KUkMFoCyf4VLtA0yzCNyfmDgmZeTDiURe1X/S7Z2zIQiPl9QSvl/\n74MjmyozS2t1ke4ZMpx9UHPbol89NmSCMpmJZXvbHTltcZw/zAUG+E8xDyc1\nHyOJuNKu6uhEBAY2Zm9bnbs/T8O60F0t/d/WWxQ3nGfXM3Ki7cuDAhr/V/pz\naSqL6MGKNZhkf/AEJHs6WC1nNebmi91xkYF5oBJScc65/0W+Qs6NVBSvCQTz\nmEHI\r\n=mkCP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "36523563e6d8ba9f6e6dcdf5bf374977dc27c329", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.0.0-beta.15", "protocol-version": "1.0.0-beta.16", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.0.0-beta.8_1641428764781_0.8153666965257786", "host": "s3://npm-registry-packages"}}, "1.0.0-rc.1": {"name": "sass-embedded", "version": "1.0.0-rc.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.0.0-rc.1", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "770cbdf1856bae9bc5a39accbae323f438141d0d", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.0.0-rc.1.tgz", "fileCount": 117, "integrity": "sha512-7gaIdNn9kwAiUHojkqC7yKEYJdz6D/G/6Kr+yp1vJgdJlQf9zqaExQfL5JcjyOYvdWzX6CMwqggBLxlxUKsGBA==", "signatures": [{"sig": "MEYCIQCD59FLXrbaAJeMG4xq/9LEWcDucNzGY9YbJVXNYIp5CwIhAMhS4os4PaMuJOZqG8b1jo5UFyElb4aOQ/uAobp/MFDj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1086760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6eQFCRA9TVsSAnZWagAA5wcP/Rp4IkMkuP2e9hPBxemb\n2RDcnhBkhtQZ6NDtcZmmLFbknR1JYb18R2wNkAmZtBOgT5gRmJEarjjvVn7l\nLPaggdOlM88vP9aiE+x/a5BwLU3EvMSa2mTydvmG3rsK5jB71NZEsQ3IMS7p\neyzew9jhoKyRDkcg3QBuFGrIf8UHvOLiLzRJgUdxiG3wLqmHXLgxrJT09Q2F\nTZI76WTsILDpQF9RkMIfSRDViLjtxNSwwiGfkrFdAigcX4G0WM0l7cYhBUVF\nxYFmqnD+Ia+gd7wRNiDQYAEK+gVpNRMfqJL8BBFa03nkpGoDRq63AtWS6lG3\nIGL4m7Z81E8xWxQEO4g6iFQTCXaiXkv6zaHp+i/q3LqV+us/K//fwSoQM4HW\nTc5UFhqtLAoluv7GYw3pJFS1GTeIqiaCoDoSBrbWBotyJjmwtKlt7CfXhFXP\niHeGvWKjsR4A3viRSrGxF7RHYrpoXrTRhvw+S2OCY9n4gattThDj8UmksPXJ\n1MPfiLBgt6G9+AS8e8TcAII/ZX8Urbj99amRTPBTXE07j+SEp6aUBDrKDgN0\nbVzsHmW6Xg7NjK4W8FG/7dPa60hOcqpGLFe3E1tWs1nh6ysSxqMR8lFU4PK4\nI4R6esrxoZq84fd7VgwVX2lHPZdre/L6ILlZWmVBVLjCzQipu/sQXLBBV8Gm\n5ykX\r\n=eXcB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "d82e7b32595c80ee0c36626f2f09bb8d3e848c78", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.0.0-beta.16", "protocol-version": "1.0.0-beta.16", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.0.0-rc.1_1642718213189_0.49325512370707547", "host": "s3://npm-registry-packages"}}, "1.49.7": {"name": "sass-embedded", "version": "1.49.7", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.49.7", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "e2f571c01b680e947266ea289fc937273bafd2b2", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.49.7.tgz", "fileCount": 117, "integrity": "sha512-NFf8Maw1jAZAJAGLoyDXLPa0GgUBolXcqsM+W1Fi3ZFNqgS0V8/xz3+SH7u2k6MM1i/3uZDDoe1MGjF+GxpBsA==", "signatures": [{"sig": "MEYCIQCy8KmkiZnlzwXP4hyTuAK68mvAq/Cvy/WCalbUIGwLAAIhAM7ePmHT5wwZ7Djk2g1GEGDJGv7te3k6QhhT9alxTpuu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1165349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+bUaCRA9TVsSAnZWagAAwzEP/3KXOMxt6+Ou0MWGEIE3\nQEToGFQeB8RN/jgdyjAT238LNvB1C1mBePuEOW6NY6rC/V43YEEx0WWHtvVb\ncMBSZgIZA4jKA+W/2+dNJcyAuxZoquX/ya2LtY99dn+qRE84ia0fXfGdYRj0\nYd45/xe5R2+SEKNc4qXqlM0+zEX/336TZMq3cfjHvyy6Y7L9h/hC8o+zbb9N\nV54KRrIAiV1H5XbR0x1xeAcr8D+cKv4sfvx9HxykmJsY7Cxe3seUL0XiDBn2\nQFBusbLhbPuZzsf57EqQ2J9d0F+2OPNihqddkn/YbRM1XtaFS1Ho/srTrzO7\nmyU8pAjMUzN+C+1uOkdCziw5XLF9yArI1EWQrVnFvQLqRRwix7SHvnSUN2Ws\nNXv9uYGDAZ3AambKvdCLA+AVZKWNUPYUGcKlgNkQ9f0waQQ6IIbrhwFC9txv\nRoLq8d5MtR5s0CfANhhF5cTMABpipldDlpGimuI0I7lH3ushwnSriBLCN7OY\nWoV5Wwu0ENQ2RmH+Gxymy289PpOqPzKjUnoIg+lz+c3Rf1ecrF6ZnDwdj95r\n5W1K4J3DAIX46RUB5KCMnDBuI4JRu2gux511HjVnzmUUKXy5MbqdZPeBoCH4\nQRDyEhVqLHJk+NDmWnAFLux289tQN++IJEUhzzEDXDQ4sIiYELGm674q7hv/\nUew9\r\n=NaBC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "8d3a982a9707db865bb40b1f68a96b466b7c5cb4", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.49.7", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.49.7_1643754777806_0.0011160755955978985", "host": "s3://npm-registry-packages"}}, "1.49.8": {"name": "sass-embedded", "version": "1.49.8", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.49.8", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "066a9ac5a1abf4e02805c1d1a55ba5c2fb671bb0", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.49.8.tgz", "fileCount": 117, "integrity": "sha512-fktiTPZd4VATlNJWXB6XU5xONO4VcCU9c1khvbXaUTRCwfhOMKYfKp3xl7mfhREcupSB8PLoxBGlm33nxngkeg==", "signatures": [{"sig": "MEYCIQDHiXpUcyKhFJDmom+u+flL0DW8SHTDwigBAehnCpTVQAIhAJVvZ3dYMNgcVyAF//6iRjA7eZ+0Vlao9fsiUskAUBjx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1166377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDsONACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonxQ/9HAjjFnMZ+ezlsXgw0zvrzQOVbZgMLljl0lF2xE3FkeYuWkGn\r\nkxH/SAUfk8ctSva3pceLZyIBFro3sUuvRFjOxn3SwtvNwS7wIZaSm/jZZKYL\r\ncoUHXbnwnPw5FH9tktualgr3r3t105Re/VDSQsGudRy64F8HYIogYEJUjcJe\r\nsM00zK2QOvGJK+qwosbmPWm02PL4p2TlGQbJAKswdWNsyXbix867meEMVW55\r\nPTr+k69d6r9BrZ+NyWrYgQHCvf89iWFiyqpKsAl34FDxg6Rkt1AWPpLvRuQG\r\nYCOZ7otDjDCeVzObzQhN/ZoSN6q97u33i/RW4Ku9AraKvkAl6hN3OLiV/LyL\r\ns1a0s6W9TYk4PKlgw0yCrxRd7gdv2pCfB/fR4ba5LzR4csozhY4InbfcK2UD\r\nslzXu+D2rxuBB2Du351trNS91gkdZvQyROhYBqTXmQVVV/BTDSBQ/1b+L93e\r\nbVTIQUa4/trQgnlVJR0maMlJOsCkf5iMdKyU2IoPI5vpLz99y9Qv8Q0tEtBH\r\nL9Lffo8rsudptNKnJlKYSKtB9MrJxSTsjgd9d+fEZAjPRQ4FvJEBzeK8fhju\r\nJr+KC7pcZtWd0kKtQA491lUb3kM5hGZG/7SNT6a8VI+KtrRRZZDJsC/uMKWl\r\n1oWY4U4bZXXQjtps7lJoZBMBI4mfdIoRcL0=\r\n=kEd7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "159fe8308b99c8e006450b1ecf09bf17387e3fe2", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.49.8", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.49.8_1645134732787_0.7338065483274925", "host": "s3://npm-registry-packages"}}, "1.49.9": {"name": "sass-embedded", "version": "1.49.9", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.49.9", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "6f82abc14cd05c511906f4793c396acf651e0a77", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.49.9.tgz", "fileCount": 117, "integrity": "sha512-FMzLl2Ts1gQBSu3zPKzu2oxAW/U6nbTjoGcn506M2o27BiN1N7aZZS6zHRSRaGlE9C/y2IuMNX5v5uswRJeQLQ==", "signatures": [{"sig": "MEYCIQCKfxGXTrfiVSrDO9YsObGppK1cF6fBz52ux3dQtzPnPwIhAO4XsEDTjLSkneh6m9FOcUIyXlvRbyzBHzefGUlaj1JR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1166738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/CzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4vQ//bGcKcZatAuKCCMUNYgKnS4ZwhB8++PcN1zc2vykc/LYzkcYT\r\nmbfV9U6iLAW/SnLyaRn4p3/xFXeLmgBbMKYjiuEuyL7jpZ+68hfmH5JMYwlV\r\nlJwFtUjzq0iZwmAh+/rDBCIo3696NLRt1qjtzHOmPbZmqMV1WOWo78OIsD1j\r\n8kvRQ/auRAa7Mmg56x26dLe71mpBGV/hzFXNQ82PP+p+MldTDKODbQVszxN4\r\nKvooxscAMMfUi3luPqISpyzY7RMHzNO+QcxSa+JC6NWyC2GT+OuuvtWhBCc7\r\nnxqC7t3aKbfg7BaPbGAaCBRvQkiBn7avohdsRBsIjmoSaGkZ5hBbek9vJbsZ\r\nfL2CLOdFNJnm0LnGwntYf4cOkLkJRBcRljKCbKzRIyKHO8veu3UNaSUNS5lH\r\nzv91byXJty2szIJwhRuWPuuUSuAyllQsk5Ooepn/+R3PH4dskJ+kR0ChzI9T\r\ntgKH5tW2VfjxgvfjugoXK259csELfEY8Ga5Y2zg+JJOnxZJJCceXUWcvqtaT\r\nxfGYtA0Fw01cuV3at8TQ2Eoa7qUYuK4Ncmwfl7cy69ObEuQrUfD2sIS8TYnV\r\nZn9kcwPlk4F2xWEbId4hgdoPKSwEeB0TyUBCwVho4B61gEep2yxRIhSdxi+0\r\nQOcZs0y76vSkpopzHnD61vzWtFuINKRWxuU=\r\n=r7lp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "921b83e449f422abb94a280aedc38bd09365f7d7", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.49.9", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.49.9_1645736115705_0.7700450746764156", "host": "s3://npm-registry-packages"}}, "1.49.11": {"name": "sass-embedded", "version": "1.49.11", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.49.11", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "38bb2cb6cedd7e9619b745f4b66d1100799cce32", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.49.11.tgz", "fileCount": 117, "integrity": "sha512-X0I+z0Iao7VeRk/bfEVWx/Kux/ztBk29SHZ3HfVmiNs2+8wAJCm7AKkruqWZBB8C8scFLGEsX0Vhf1wgV+vzEw==", "signatures": [{"sig": "MEQCIBKMSZFhLswjY5TmNlN48d1dP03DxKGZHKRyMBAXkP2HAiApbMP73fXdfe8diLTcXQr2V9Oq82TRURwSsxlpH6+iHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1167693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiR4ckACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxGw/+Nh5of94y5vGBzzQfWLRWVI2R+97lFHygpjiAYS4YF0Rky4tT\r\nca7F/AWPThuEK4YV7dQwKiL37SZaKXC00CBonGhyVHpHZxhkvr3wepttSwPe\r\nczo5ss+RNigFHM8VB8AbBZgxAa5aQ4AaVJj+jMhnVqBrP7fozlamTWm7OMZX\r\nOvV6k4Q9VhI0tWx/EfqX8x/rcz4wrNgez1TKPiXuSa8WmEr49Wvesai1251m\r\ngzFyiiUOLxQEMA3pP3gwuvJxyKlY1rYjR12th+xy7NaDW3mbd75xt+MH3ZTk\r\nT63f4ku5PtNM/se0Z/Fb5LIphj2ykakyab93aLXeP4G+tvmx6yCc5j9n/+s6\r\n2NBphNFkAc+SnDCUpzZk6ccpthaPEeLaOXLqKD3WKW63VWEIVuYBEzks1FZ5\r\nnFpQuzlQ9sas6HEISjTwzf3T8H9vT9rq6VrlC/qPaV6DVvEfx2BQvVi0i4f2\r\n7u4Rv6a64liRbXBEJrJFzFopGZrOMZVjkNPepgERqPIhcc3wVP8cerm4xNYf\r\nHHPgc3WigBDillV6ucQYVy4d+EPNogAb8rk0ZRPVfU7WmYc0FT6qYsc1OsUj\r\npHBgjOrIqazBPbX0g4bvZ7S+jPOZkSV/Lj1BMeI3Ga+K4P8eX7TVSyrkoxZS\r\nWV0ygmSeQPf8RpCJL/SZZl55t016prLbZdw=\r\n=oApG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "7cfd803b6f7be9c076266d70546083ded88b9b2f", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.49.11", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.49.11_1648854820455_0.06498186943826578", "host": "s3://npm-registry-packages"}}, "1.50.0": {"name": "sass-embedded", "version": "1.50.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.50.0", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "b42be2c90c9ec8890eff34d9416823b0ce015c21", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.50.0.tgz", "fileCount": 117, "integrity": "sha512-WCYA7FR0euku/VkpejpjTE6zjL8+h0eFZrLLjx42wCjb9/qKKyFWLdgsErXwnq3jDOMyUY4dYJfenRIVMcEXog==", "signatures": [{"sig": "MEQCIGkvmuuAgrFkp+Jhhfv4K2G6chiG8SbfniE9H6rAlxftAiBz4mH7gkvBOxYD6Fn5/a28JiDw/W0Kn5b6Xq7kyLqb5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1168719, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTlIOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKYQ/+O7/glZINZ2gM78c9c5x4qA/9hsb1aJbTsuH87ppftlkl6XMX\r\naTlT1BRe4vm2/i3xJBZIIVzgXcz8RSGZV4xRDhu7MTJiZl16iEvFZQg25WGa\r\nctUexSqQXGspmIVGUTinPbomocNpD0Zfzfa2M378X3rxKP2ZIdVHSaeXcq4g\r\nsSEQsb5ZupV0BrqZAUFtVPUvbvsb+bQlilMBAkzd4pjbRovX2tShcO+eryZO\r\ncz0M0hMbIVu5oYXcNOyFpkBu4dzIStj/OH3Si1Moc4VY8pLDE/JW5JLO1dyy\r\nFlVykDwSLcCnqkB24LqFEsYiLl8WoozDbIYV3S4b9/u9d1bM92Hq2bqFJwKb\r\niPSyzeYnxosnFtYtPy//hWaxBQfVYW9fJJifc4Xz16CVAgyq8aFeoZd7DfX/\r\nTrlHDHjcGfoDMekWAcR37UGg2EVKCF4Hf0ur1a+caI3uDnEEy/YbFRiak8Ks\r\nwwHN9zLjoIF8qwRLN2Z4yKVpkCN+yflYN9T3w646edt2bZFZ2lf9Q4tleNw0\r\nWMNOsQ0UATDyJAAq9Q5vGUeG7VU3rj1G4VkxkDJN659Lv87HKQkXkJbNLoH4\r\n6Vi+yR7/M8B4D86m8mAKKqUAIZxuEAZR1qjVWMFNEUsOhZOF7dwcSIzHPbgB\r\nfW1l8Dmdl0pknxtnmHhhH1UJyAHaEAjYjSA=\r\n=nBkN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "492295bb311df45f3212260e6400946836050533", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.50.0", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.50.0_1649299982002_0.04028351397470198", "host": "s3://npm-registry-packages"}}, "1.50.1": {"name": "sass-embedded", "version": "1.50.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.50.1", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "dd40de0e06a471b4dc38c48b4093218fe3f68845", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.50.1.tgz", "fileCount": 117, "integrity": "sha512-Md9NUbCqaXVaAKfR/+/KxjWpkvS7TGohhlcFVccgtElcycebGcPWCBh6eZKoGD3s6R9rmu+6OP9CzS6Up8rAeA==", "signatures": [{"sig": "MEQCIGgzuJeTz9UIFkjI7soysyXRRO7UZ6GyjDjwFPcuOt5MAiA/yn6Yzj2DOcd8zGEfCDv1VvMEeKlg7bI/U3zIp/87UQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1172373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXg42ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpufw/+NbFuFyZa8CJe8gEBMqJl9axkI5qPIVMf4pBHrvuFXF5VAHZL\r\nn8siVC/7pFBDQhzuMRQLEVDckdHAn1vFJXhPjSU0c6fSjE+7LZtBQA1wDHcI\r\nIixxaZFkqfL0M8hcDypU08hPtsO/KwWQvSTOD7Co1ZJQ4rL7ofZlKfn1Snaa\r\nmZ3jlLgSuCoH8nw3Vx6nEQDS1J4gfvkZPv2cUx6vWJXLSYjsUSJ6VyIxSl8T\r\n81q5SJL8IZ+RVEy5oaNhrWwZANZ9i216GiCHXhm9j8IKG00ZS9NcYdngZ5sf\r\nEks1gCOJs99EXEdmBwxwyuwz8tfsDP3JFMWfM8Ysp41vUd/J/r1XyxvTVibo\r\nqd14FDgQ+8BQCbYJnVJ4E495upnSbFbRjHd0YyWQMkp+kR7P/AqZApAfIF31\r\nxnI2EOEh9p218uxoX72pR6cMMdP2OSCeAaBypvrZw3AwnkG2x5CHQdxv9dQk\r\niB/WEFqsYfuFQ+xwiSVtpRIXotDqL7t2AL+Bm0ez3hCfc2UAmorTYjNpmjXb\r\nRdxlxnSOToK7U9FzPz1atEx4N/UYmuugWyoeDPdSMVTcPnkOZEsFuLN9bBXm\r\nCqP/ORoPQQMr4vWBKLk76jzPSKuSPMC5/Uq2/oFNi5PxDsDW36W6xIGtsPnz\r\ntrH0a7JKQE5lZAPMZLwXr55aWaw5kzFqeVE=\r\n=W1/w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "5c398d8ee0830595561604ff59238be0b2d3f30f", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.50.1", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.50.1_1650331190588_0.8083869309375176", "host": "s3://npm-registry-packages"}}, "1.51.0": {"name": "sass-embedded", "version": "1.51.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.51.0", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "cce523eb5548e6cfde3d8c8f8467471f500d9596", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.51.0.tgz", "fileCount": 117, "integrity": "sha512-Nw6jem/deA5O104GYfkER2gCzZeY+VeLGXOiDhtX2veBzuvzJxSD+GVcDRB/QoXcNa1EZD0mrAzo5MujRl4HJA==", "signatures": [{"sig": "MEUCIBBvNGqRZQiyxhrvUOYS958sZz9SCBzV9R9tskfrYXozAiEAonV0Y1hXQIjS2GmW8RuzNlwdV5wklNQGEpC6yR0KewE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1172780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifX3qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDmw//YCcReurq6xiT1BE9ZqOjo3cjsSYdPvZGXiqC0sZv2DGiMudS\r\n7ewg/1+NsBGnDcM7xMMoa4pwrwtQDNmhqh3IZpexvmb9wOWg0cqbCs5HKWXD\r\nku8HrYZL47xIMM4/OcAjHLiUq/656AUX+BKAJIxfX6jic7AfOLkCCl9C78CT\r\nSrhnv0+LfRpfszuynmVt33+RlQlypzFuQhxHSYjSauMCorMu/U84UwDRpIwu\r\n6ObaOTECxbRieOeDQuVtFOjjLn1aQnq0v1eLlkWhzimgRVU0GbX4V/8TWAY0\r\nUrxKJtR3CSe1qmCuLFJcfduJmQkhNLNS1ytqK9o6/GlV1AysuhJDslVOPAV5\r\n49BpLB4bJUS7ZZ1y2gKVxXrIuF9DXqRI5MomiRpo6vOe+1cPfhC62HX7FeNj\r\nX2J/Z2j9O0Etu+8/gjXraS5OwJMo68k6xm04qbDRsSBukcGsgnChsOrXfVsp\r\nfljNnqN9NM3s6NPZwfE+dQqvXugmlF6NHfnI7r7Q1PiNGAxgMexQ8GYZ+jXR\r\n/usoLwyQ5PkmVIG8J5uwIYQ5J7sY/EimOyo96G6SwNLBEBkURAc2hE6POZKF\r\nO+PaLj9BE8t1ExFLmFXnRqNrWSFDM7luZuMYI1YtZ8Fo/qFZAHDiF4ph6E8j\r\nqJNwXi/BMG2cd14BaNDbe3rYiFoduE5n4XY=\r\n=+mhE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "e81ab6840f71060b38abb6193429359f0158777e", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.51.0", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.51.0_1652391402127_0.7424347858613651", "host": "s3://npm-registry-packages"}}, "1.52.1": {"name": "sass-embedded", "version": "1.52.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.52.1", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "ba41f925a1184b63e30750dc4ff96405dfda6deb", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.52.1.tgz", "fileCount": 117, "integrity": "sha512-WIA0m2SEFSyF4nQtqMGx19k6rIvuRK2CrDGu67KtQflgWDaYFPExbnrZQhxXq3T1i9waPtLRbbzQ7kw0hae4rw==", "signatures": [{"sig": "MEQCIEn4zy5TVpqZZUqHAw7iikOti/ZDvHbSunk37nahDDeAAiAKrzhY98dALwZ7GDgX/AehNb7PeVDiqtYZ8LMd5t7AaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1174122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiiBd4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeTw/9EOGALSnYX4HaEyzvJ01J5Z1tfsY9x4BGpehpI5BRWvwEsbhM\r\neFXZbUfWDtqsppEfTwKJbWuZhvLcjZkfdBZVCc3uz5dK2PDHZvZYhxGewmwm\r\nw99PUaW2PT7lJRxDlG9+s8dGRupUsUt7tyXFL/8/3c9fbgxxZ8nE4T86oymZ\r\nlm8ZwtORCbOQXE+ph+TXbOT8IpucGNQRx2yJy6o42ryHprwG3NLIiNDmmaBy\r\nx3ha8tAD3Mm5ubnOUq/VRh76tnhiClSuzfmqiHa0EAkhO0pg/j7k0z6k8Cxw\r\nIBDoQ0ZTghU5Hvvn5otwjFi2BQFh0/I1XzFpiK7WoNTXpRGgUFO9nSrT3U5Y\r\nIqna72qmjqNjxBRVfOYFGf30VZ4dBMhtPXuofa1H0+wdyFAM84wbCl/nBk/2\r\n0Yq+XEQ5xFCU/wmYoYQ11AORngEs9QCocSou8HownZlE5SwzNTcziSCdlZ7B\r\ny/7VH6iPIkCYSI2Nhyay9HVhX5mPjUhIfsWaY6QnsJ5P6vxrchHC23oMseSM\r\nxIeHhybNdW8SH/MRQNAUJkdo8V2e9ltmOicMXSgbooTP4o4z1d20l3HllfB4\r\n+BXvF5gqDSZIQqLVvbz51na9Kww6U4rutPYs5ygoP63UywKVtEM8fFBNPf5K\r\nrYSYippZWk27UZIySoz+6NTOgOUrgpDfgkU=\r\n=tLqR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "244a4e2380cea4c4e4efd63c36af1ad69c7b90dd", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.52.1", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.52.1_1653086072169_0.4796185561388173", "host": "s3://npm-registry-packages"}}, "1.52.2": {"name": "sass-embedded", "version": "1.52.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.52.2", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "fb55406ec057d7a94de4bf3ac1e388ba96bbf937", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.52.2.tgz", "fileCount": 117, "integrity": "sha512-6dnFhUT2OBxVdn+nPpNJSJymIM0bdmgtL7oWi+cCKuDdxCKc/uA/yBjinUP8Psve30Z4yTtCoRjWRMvAy1iHGQ==", "signatures": [{"sig": "MEUCIDIdevjF5Gm4aiql5EkMJ9pgAQE2Qb5dhg9C4vnmdUMJAiEAuufOOuTMNpsGWSow5CEFYLZLHd2IHjek8RJsnlmkG3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1175124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimXe6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3LhAAge6apGcNtlURQPGkMXl3AJ+9+r/y1QmQwCDQlBh8ley7nihb\r\npmJub0O63jFDPQq1lLyGHimFM7O6h57ClwucFKF9geJ+Vp08itSdBClno1qj\r\n4a5Mqcs2ovLJwyE7F50K1tf+J7uYabI6yFAUE/XYqckD46YooyK3trhPQiqH\r\ndNpiuylCe+bHOcPWwEX8b3DtOMRLhuo1hc/0bgcXEfKXiLmjd+EEQZcHLLFx\r\nrEEj7rQSCbMLugDkhbr4PM9lHBF+qXtjV/xPTYTKyaKQXrfYbvixCSQakiJr\r\nZd14dn3DwkiQ0OPjpgecmCmpFYchCJXnAdKbzrKyoMN19ehvYlhjxEXPhw0P\r\nctXFAmqr7tXSNhJGFEU1rpVBhbEx8kRilrIaBwxlrNdramb2ujHI2grhFBf/\r\nznbjGB9LHnFjWzNscdzgnzoi4yp38A+rsafoLrIlJeYKZn1jNQRlKWThxHGs\r\nzEUUuMhjS6eLYmh3J2CwjoMecW9R9NtH7Qr8X9kPb2ueTL2zDIOCQLw15Hik\r\n0GQ9o7gE7JL+VZwnc+HQDhrdWDdPkk/NRrrBu8jq/tzb+Lu9mx2PuTtZ5aRb\r\nJk8EPlM8QxuFMnptdC/HzT5yJhMhTxf/SpDHCSrvkfQXNSr0kuQ/dgATHLMO\r\nJJlVKGK9DzR6VSwjsKIaaH5UlpePES4PO24=\r\n=iM2u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "40e342b8ddc492555c569d37125657e9ca26d368", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.52.2", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.52.2_1654224825948_0.7661512849575969", "host": "s3://npm-registry-packages"}}, "1.52.3": {"name": "sass-embedded", "version": "1.52.3", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.52.3", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "d9c8c801a2d91e7c2cc62c94f90c734b2df93938", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.52.3.tgz", "fileCount": 117, "integrity": "sha512-Y24MIIjIr4kWnsiZYbWaAXwmGmxQoBuvcUWBuEBLK1lPg0/R/kM1NKT0a7f11Ir9h8nvPZDDhf+/opdfWJEYtg==", "signatures": [{"sig": "MEUCIQDdpUBH10OkymDk7FXF72PgAEbbiWcAKlCl5Bl8+/go9QIgM2xiXPxSq9BariracIA9iF8uUYa/PfJTu+ORW3ZRcTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1175289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioTzPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQ0Q/+MHltO1687+4K+OpImnuw0cCDpn11Maj7Ael4uQHXL3iBdWlq\r\nGHU1bNNCRbdIgC2uD/U15b/yokarwxh21He5LQgp+ULp4aEG40yuQMrTA0v1\r\nNqKYXbj9hAR9FKSyUeV73GjmKn05scsATJ22dsNCZ0nhczhXHkf/0LcBoIKR\r\ngA8/gvRTwOt+PHhxkm7JQsivk7Mqp3UrnMgtf2PXK8u1hkVanY2qdn+hamcl\r\nkvaM9BEB1YPNk4EtAOQ1WOnteGVh6YPrCK4gbRViMSvInfHO94tRbBgB6X8y\r\n/KAlPMsV+IXZ1vQm+Qy7v8FAQAnOzEGSOOkpDnCl1kFBf/qjGLXyH7WCuPy5\r\n3hYoSlkEsD6OX+H7ksc62d+pkJSte4wj7z/U2HbDEc32vgaDRnBorfy6GK4o\r\nyN3L0NdZIKpapVg9QwAg9nhEArzlsYAYrtI2MQDgzQoaQofuTMM5iXkujyfc\r\n203gZ3RFjKIAIriIRQuNwVbMv3Sewh2rmkA21Kn+e0fXP7IEIeSyfauz0CFp\r\nrCpo/TKHQ5rvZmRNXl0sso7fswLaXY91DELlBCzhdtZnzX5mBqdLWs3HwjpC\r\ncYEYDPEadK0XYAKPfd3OdhzQssOSnIKAWDqu8IUPWFijhiigMZL8FHfztJZu\r\ne3OjQQDI6HRuSV47eC4zVkUdsEag/BPJ4ZI=\r\n=sehK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "221ee943c494a1653ca75b39c87da08f1843f4cb", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "node-fetch": "^2.6.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.5.7", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.52.3", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.52.3_1654734030851_0.23735258035027607", "host": "s3://npm-registry-packages"}}, "1.53.0": {"name": "sass-embedded", "version": "1.53.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.53.0", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "ea93055764610c7fbabe96e1acfff2a53e4e4f24", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.53.0.tgz", "fileCount": 117, "integrity": "sha512-Af8hOO191Lhv6fh7Qh3DemPIVT/v0RWSd14eXTA6AA1UEAQW4TB9/XOVi8SkuL/Tj+zTf2ctan2gzsmdT5lZdQ==", "signatures": [{"sig": "MEUCICcrMglZfefD/nD/PdWaQZn1VeNOSfQFSZdF5+v9gH9hAiEA7Hk8a3DGt9BUGBgEPS02Yc8H43y0AyTtRnuvqBjN7d4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1175806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJis3UIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpG7w/9G3TbRSiP2t5/zcl0TRhe2j7+tRevGUxMjzYOaRWxrz/crsbX\r\nImRZqWlW2A1EkVjxFpGm9D/mNhxT+frypBWFd8iNZfPp0Tctca+baiPh9pzA\r\nECYVZ2eE4Ra83OED8n8mmrFjb3YhdPosHiPfD2L/mc/gpNkww9Yub1Ol7njL\r\n+AlyxM+9NKeYFileXlIhIGkuYIYMcj73d/iBAZJeVaUn0iIAnzefZRJPZrei\r\n7UNz21ACYTYez7VuWOkNwDmdIEcbzOBxyW5R3zrXQy4AkI2q319/p7DI6jj1\r\nOi3H3dfqtF3qCAxNyM+7M9Mh/DuMOa/aPe+odoKg5B/8mk0keYziOpuUS7jw\r\n3xzKFP2DWSupr66YqUvM42+aVjtCDDzJfN48ZtT1BDjl8Wjvb9Xqtx/Jkm6J\r\neD52Qg+KWeMrUlnjdEwKQbYMek68fARFKEwLhJ3X1LuzOMDG1gvYodaaJjoH\r\nbDopWo0P8elHbzlhZjBYYOC4MuapGrBTxzKTGhOlNVPM6csKhgWP9PX1hZEe\r\n/vhUwu7cnSg8SjLxRBCWHN/oMFMoSFs4cY9icFuYG49fP1BXS1hygk3DEMl3\r\nfLjberw8ga9cQUKFBQy48N4wZ2dWBxqzF7tOwryzqchHhMzKRmXuRGnU5A3d\r\nHK54MT/Ji0Dd27g3h2BhtyjrOsIaGWNljyY=\r\n=1G8A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "62482de2dd9823326e20e879614197451eb568d6", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4", "make-fetch-happen": "^10.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "minipass": "3.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@types/make-fetch-happen": "^9.0.2"}, "compiler-version": "1.53.0", "protocol-version": "1.0.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.53.0_1655928072524_0.7906465577436959", "host": "s3://npm-registry-packages"}}, "1.54.2": {"name": "sass-embedded", "version": "1.54.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.54.2", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "4642983820a799c26221b8d9dcf91af1d33ad2b9", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.54.2.tgz", "fileCount": 117, "integrity": "sha512-TVyYETdQrM+Gq5p3D2sy4TBi1EwIjOhgLD7DnSQQaYDLfdepstNdgIUMkQHmayQLxizIJhbexj1wrMB8WSJ0LQ==", "signatures": [{"sig": "MEQCIHFzBY1foyaK+4n8L4NUdJ5jCs4U9D3YZ9LVzlvW5Cr2AiBSKQ644mUUEhxzgA9Fx8sKQn9E50wsPkj8Z5aFddQ3+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1181709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6xBnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsoQ//TW6GR2gUFeIqBQy2fgHJ2/vQTFYQbjAUdIte2iZX08fgRemR\r\nyX0EmqblhmUynV6yw7+5CNvXp5ODA+sFyIRG46Ei3LWJ/2z7RScC/RsireE1\r\n77l5G7ChsXBxxOwVHBQuTbjE7K/FjMTzC6hd2cYkQd3ADR4UF04ZdU7/0+KR\r\nFmhJ+hNtOBOfHa4y+qM2vEgV/CPlhfwqxYQQhn0qfJvqxmBwO0uV0O36F8gs\r\nr+KG1BBdQPnAMj28zXuoceEg868lqQanqNyRFXS5dFflVuCf6NM/kc8ui191\r\nHGavUc92hFwdhIp2HeU8sCFJOh2EbxY2HYWHdGijAwhzVtFLwPU477JCf2XW\r\nru+TDpg/HKS7hqVYbjDRwJPwO52LPJhFhGAPwUu/u0TNc5QcXq+AfPN8Iwui\r\nqSHST1dndZAflwkBJfGEk/xhB5v5ibetwGhx3mGDs9l3R+ZArboAUDi2E7dI\r\ngZja2ehG4Xnp7knTcOsarylThMlmTQ30patMCU/lusk73BaCX0RKTObuOP/U\r\nyciOfuLJQKUh2SZcIr3/bVFsiAdLMvSwj1d1NgkS9E5SK3/dHXpQIplEmfVZ\r\nTbIU7MCh+bwDHSBe4KQVbdmbNft1ZwrAve++qnIkXhGacWGbyRBuWwpsspdB\r\nhepBJPcrIA+LBLpt54xIxPwWdoSd1PoX/E8=\r\n=HyXB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "f94281e7d96c286d84743717713088469e81de2d", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4", "make-fetch-happen": "^10.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yaml": "^1.10.2", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "minipass": "3.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@types/make-fetch-happen": "^9.0.2"}, "compiler-version": "1.54.2", "protocol-version": "1.1.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.54.2_1659572327656_0.5749184102986433", "host": "s3://npm-registry-packages"}}, "1.54.3": {"name": "sass-embedded", "version": "1.54.3", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.54.3", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "47956ed9c334055021638926a33da560bae037ac", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.54.3.tgz", "fileCount": 117, "integrity": "sha512-8dOBVIv6tZQkQOktYc/yQBcUCLn0MSkHZZQqF6izNMo6N4ZfIHMEu9cY19y7DuUu4EVsmm6jZzdDBNjnEVcn4w==", "signatures": [{"sig": "MEUCIHAIE4Y8Jdh2N6pHunVJPr0q+XujzXr3UO41DJUj2yIyAiEAzBeyK9FfO+pN76fc8DKvUB+mPfuZF65JFT7LWUrCvpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1181769, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7DgNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoX8A//QyvWRklxXclXpkStW6u/BkcyMzYkQgogXtkx2PsRLBF+IOvm\r\nghmTBiZ6o7p8zJz/Jghj83UrvPGei9DZfIwCgiyadYZrd5hJEEYTYFaKUe4T\r\nYv8gKHnBBEWaytLOGaEzKs92ayhtyjt1jKAvXe7UJndP6otTRL6KlSf32FN4\r\nDGXM7UgNJ/yD73poVPpd8scsPbettJb/uei+zLzQ+EXo5sXfJVAUjujkQpk0\r\nbI2OvtPsHgVmkeXgsRdf/ur6Owhkr2lwr+d9IDvm9IoMzf7uJSb3mlcLL2Zj\r\nxpCk91nnS3cu/VCX0z+Dp0o9CBLo5lI7yOC59I1+tj/57ygny0qYoa7bB7bZ\r\nO7kEGPMNKqR6k2vML2Na0wTuvlYjPwXlbnW6CT8colDDVHmy6Mfo9hXCeIl2\r\nKj+bdxslXYBFy6/0sEJqJBrTgEWsO0Yya0Xq6uW4Ue8FgoeQ0Z2Utzih5Xmj\r\n8Pk6ovO6vn46ZRoaM8kp1+UZgfUQav59WPyOnAJMsWGoGLZTTeDAqkm6YEND\r\naGkNBWgic33d5AQuSScDLJUGX2EQfaQf4xuYEqZPj/tVCI5huj3YvoKpftkg\r\nuJdsLk8yV6T/8hZtmtZRoNSFa4wI0NCeno8ydhnu8lH6HWDmFuMxFtMheZ6j\r\nPnVFPmfWnUAWbI/160vIyENCaxs1EyenbaE=\r\n=nPgd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "2ae850edaa79481dff1f8e61258ab338345e8141", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "yaml": "^1.10.2", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4", "make-fetch-happen": "^10.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "minipass": "3.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@types/make-fetch-happen": "^9.0.2"}, "compiler-version": "1.54.3", "protocol-version": "1.1.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.54.3_1659648013656_0.8441485580330643", "host": "s3://npm-registry-packages"}}, "1.54.4": {"name": "sass-embedded", "version": "1.54.4", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.54.4", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "c5997f79ce3ee20e185b62899613c4e76f138a03", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.54.4.tgz", "fileCount": 117, "integrity": "sha512-zsL+LlpCCCnUqIXA6MrnPLuBuOF5zaYbth3u9mmx6jw5yScCfBo39Ax601XLcSWhTek7dBMjj5LdEYzScQkI4A==", "signatures": [{"sig": "MEYCIQDG1GAGV/wZr0bwR0esTfOMyyLJgaUv1TR+n6otfqaO7AIhALRwmYm/5iII5MwVjkZy8yrnCBYC6hkVWUtVm8l3z7A5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1181893, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8whDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNpQ/6A5CH8M8ZIkY52Orx3WuUWpxuYhiYAwNAD9zW8rVCskT1CljF\r\nhfw7Rw16HsapvoNqLzyd1Ne7/qwT+NHRjKbTLIvgcw2obFnz9wibNTKKqzNM\r\nKGl+NU3NzLHPApAurjXc4DDyUnQkpMTl8pyR49orKMshQ7c5QclojRAgmWiE\r\nZkEl4gvZXzQndQeJtOz2MOoOxoMiGK9vx08UviDhON+ETPoucIXJWbvaxhMr\r\n44PvDgKqwqH39TTEkS9ERryok4fftKMab0hQ5L6zts6o/nwfpnadEV/wfTm+\r\n/pzbw3xJXSJPUpSHe5Y79GqYHgp6dGsBEEMpsiwOTHupoWVsEFX4Nkp1gAYT\r\n0/f2G3xePWYamLrNNVKrLhc9GteKrAa+/90Jzb+IeQp1ysgdOK25nRGbkNV/\r\nK+OPCzVK24otlD+FML5d5J47YQNWlWFAir+3SGEBq2wWajkXhSqQt5+KG/Lp\r\ntMIh4HSwmYDFu2RTLLZpoElzTlY2BFWB2bFh1Bb9uf/J5eKUglx1P83Ys1GL\r\nqhCIKfSy0qW64N1RWd4P34NWxWdwBVj2NH/L7ohi29ZCU662RQMXEgHzmBmM\r\n2ZOP1R8wDkYYfxwZuIUkfS+eIF6QKrNFTMmMfP6tSdKmmwtfXgdXiEfGqb8I\r\nDnxiErpO8VXxXEU6LulmkzXFFDiXZLRrBBk=\r\n=oaoQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "ba98b9d500c8ee4c446e1038113ef82038294ace", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "gts check", "clean": "gts clean", "compile": "tsc", "postinstall": "node ./download-compiler-for-end-user.js", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "yaml": "^1.10.2", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4", "make-fetch-happen": "^10.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^3.1.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "minipass": "3.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@types/make-fetch-happen": "^9.0.2"}, "compiler-version": "1.54.4", "protocol-version": "1.1.0", "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.54.4_1660094531602_0.6528511082268944", "host": "s3://npm-registry-packages"}}, "1.54.8": {"name": "sass-embedded", "version": "1.54.8", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.54.8", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "6ae468c4c178a5d3712648d81f03f66baefd7de9", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.54.8.tgz", "fileCount": 122, "integrity": "sha512-5AXs5U1mWwuLqjYgSAbSUJxK+P+AGEcz4/wqfy2fMtAuYrPiE8MKnMSvbXUpAQFJp57YSUrzdYpbzohC7OQU0g==", "signatures": [{"sig": "MEQCICEa3FX7F6yMpN8Tbg6jhV0gN0LmfSHDdeV8Jy7Di2C3AiATz5EZX7gBzcqbCebPuI+bZi/SE7TT1i1qWv5MctSn6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1198001, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjD97SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCeg//UCV+2ZHveJoX9mvzjP7ow8yxSGLlCyPyvEPljynfZl5mncjI\r\nrHU/Yg54aYclqkUolZA9K8of7Atq+q8iAQ7JGP5dj3MU4NZJpqtJm6w3wqWO\r\nC32BFkfGaQ3HwOwP8gjTaY4J4st4PBA2Ow2HJ4eN4XlPFpzW4JhVWEma+49s\r\npa6iogX+IG5UkevQfjH8pKAZdeGDKjDiDZcDDhgPFqswxyLVsuZfzFr6tFij\r\n/gb0uEpEKyDzCkynzADSJUAz2wweNA/Ax7Kgil8j8K/BqCIeiqW+uOac0Uwx\r\nS84SKtCg0bqsEkDaHlgvZeVkxcvDJDt8fXDI0/jdc/2FpHK0nCvXdJ5OChdc\r\nMnn4nNijjYely5mxY2Q+6Wyasx0yTDfU7ifHpSMVx4urs1bN4XT4cEyLeSvk\r\nytwxgeYBtGjrxFg+dMJ5/Z9jd09Ss/j+6Y9ofPAcwaggUYlvXyReDZMlYW4c\r\nWWrJOj+tGnwVdnyrVS4TK4rGVtYUYOX1u5HXnLld426IdxikJWx+XOfLI+e+\r\nrUcaJlrKa0NKKQG1KiH/JcJRZXraU2FO4ONxIVDr9wqrgt6aOujwrMXj1Feq\r\n/JAR+RSfwgitE1QMeVAC9FL9hF539H6PyD2WQa/5rZUKI2ustu8r5YhD5Fwk\r\nUOKqod9gS9C7Ke+x5iGjKDRJceHFrRVfiFM=\r\n=kbir\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "fa3d4030186e9d43c729af166d9bb8a113338bd2", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "yaml": "^1.10.2", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4", "make-fetch-happen": "^10.1.2", "sass-embedded-linux-arm": "1.54.8", "sass-embedded-linux-x64": "1.54.8", "sass-embedded-win32-x64": "1.54.8", "sass-embedded-darwin-x64": "1.54.8", "sass-embedded-linux-ia32": "1.54.8", "sass-embedded-win32-ia32": "1.54.8", "sass-embedded-linux-arm64": "1.54.8", "sass-embedded-darwin-arm64": "1.54.8"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "minipass": "3.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@types/make-fetch-happen": "^9.0.2"}, "compiler-version": "1.54.8", "protocol-version": "1.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.54.8", "sass-embedded-linux-x64": "1.54.8", "sass-embedded-win32-x64": "1.54.8", "sass-embedded-darwin-x64": "1.54.8", "sass-embedded-linux-ia32": "1.54.8", "sass-embedded-win32-ia32": "1.54.8", "sass-embedded-linux-arm64": "1.54.8", "sass-embedded-darwin-arm64": "1.54.8"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.54.8_1661984466285_0.9637066890943997", "host": "s3://npm-registry-packages"}}, "1.54.9": {"name": "sass-embedded", "version": "1.54.9", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.54.9", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "5d56db62e152644319034e90031fd6482cea1aea", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.54.9.tgz", "fileCount": 122, "integrity": "sha512-CDPvqH2obX6GURzaDMtCcqJBWleh48uoEnA5jkM6DzFcjLbRa1ClLlx0NhNNGGh4J4ahcPg8sC74LX/AkD8Ldw==", "signatures": [{"sig": "MEYCIQCJBHZAztlkQPWKh2fs+DkSuG2fA+RDCy67ixXQkzSwSwIhANUCIaKqnOBq78Ax1mblO/2oYxtEJ8wklgQHWzK//XqM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1198127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGRTVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpD0w//egIyc/MANZsxLLzV0mLGl+defRQIpBBOFwHO5zbcU3KIqXs6\r\n+EZWAV3wmCNxJ6fGbyxcgWFkOYCT3V6qMfb5gmXMZyvk+WZwBUIynBl12fRm\r\nPQLclN3nnNotrHeGn/GNEYVycwIoskkiBdpYp9spu1IMSWn6AAOl/qQP1fzj\r\nBzpIGnRQp7dwfrjoToVpmLVTO+k/KqCfKaTb9wXR/uinFQCvV8mneTN3fg2W\r\nFCe8Zkd3oPTAAeVnqO+ryzNIwb//Y0s/kZQ/qJXM1pAbvoaNvOyRi08yup4Q\r\n2L3ViUb3wrfmEMJB2iX18dYXmda1zecB2f+TCJ38rtbllE09/WOJX261HWvG\r\n25+HGiDkcKMYiKELtWqZoL341kELUp6ec8MzVSO9xLlWev4xgvhnLZopLWzm\r\nn1kEQwbQnj4phHLBAHeeBBYnh2GKQqHT7Hm3P9XGFaCvtZ6yxZXBqfJjeWco\r\nVctmUFcc2VoOamxCJLWQZ99PtrFYzQPzQ3k0A5gymuToJx0u4T/5FW2Ra0J1\r\na+7dt+T2N5zT+ZnCV/957gzUfPvZyo3G4/AK57hOvgj2Bl2P9o+EPRzxDz9X\r\nSBCbmKJdMOoOmMJZEU5JJeir9804MGMEAnYZFAwhTsG5sqXudH9H5pinhAgG\r\noVnO8Q6Gt2tIvsWxg5P60262N4Z3cmbpdzY=\r\n=pAMo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "cfb225f7776ab7104e7296b48a411ee38d05db77", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"tar": "^6.0.5", "rxjs": "^7.4.0", "yaml": "^1.10.2", "semver": "^7.3.5", "shelljs": "^0.8.4", "immutable": "^4.0.0", "extract-zip": "^2.0.1", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4", "make-fetch-happen": "^10.1.2", "sass-embedded-linux-arm": "1.54.9", "sass-embedded-linux-x64": "1.54.9", "sass-embedded-win32-x64": "1.54.9", "sass-embedded-darwin-x64": "1.54.9", "sass-embedded-linux-ia32": "1.54.9", "sass-embedded-win32-ia32": "1.54.9", "sass-embedded-linux-arm64": "1.54.9", "sass-embedded-darwin-arm64": "1.54.9"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "jest": "^27.2.5", "yargs": "^17.2.1", "protoc": "^1.0.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "minipass": "3.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@types/semver": "^7.3.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@types/make-fetch-happen": "^9.0.2"}, "compiler-version": "1.54.9", "protocol-version": "1.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.54.9", "sass-embedded-linux-x64": "1.54.9", "sass-embedded-win32-x64": "1.54.9", "sass-embedded-darwin-x64": "1.54.9", "sass-embedded-linux-ia32": "1.54.9", "sass-embedded-win32-ia32": "1.54.9", "sass-embedded-linux-arm64": "1.54.9", "sass-embedded-darwin-arm64": "1.54.9"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.54.9_1662588117184_0.7794289125591338", "host": "s3://npm-registry-packages"}}, "1.55.0": {"name": "sass-embedded", "version": "1.55.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.55.0", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "982a2bb1090bfc11952f0205743be327d3ec7134", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.55.0.tgz", "fileCount": 122, "integrity": "sha512-6AkKEyRjxz37iNwOdTOW94I33j5mxcih9Z604q1w0M1dOTKaZgtyNI67O0zb4akwZ9dVZ2VSJBXqY8/1TZRBIw==", "signatures": [{"sig": "MEUCIQDIfHsMnW0K6LGqAg6gzwB7M1OO/IqjxJA6eaX27HRuGgIgaMESrmtLaN5UbtZVS1zCtflmWNI5OhDbkfIiTVJT2Kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1199895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLOllACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrO/xAAiE8Fs2C0nyOVJU3g086yF9tcPRGeu9JuO8DvDUgjNp0B8Rq0\r\njdam7syErXb7gXpQZ33J4jJipUNWIy5pEdlqMUv250SQm1/8H2BRbkDmFcBP\r\nvg5NWzbj81Oaw1ZM3kEb5Z6jjyDOScVUbRMKvNonXggPhnv0fo2AiHwotK7y\r\nf+3k+hAW3arT0Be7vUWYFIA1cCQsJTZ3wACUrcbL5PAMQKSAbrAGZwxESFUb\r\nzQOs6pua//Ro84muNU8H7V6s0sTZjL99tOIrqsCQouA6I9PY+oegwc0/TVdZ\r\njlDblmmv/oO4hollVoVZuzzlAmCzO/MP33SU6tkFRlPRftpVYSL6YMF+PUaF\r\n8ibKjn3UjoWEpymzKcpv4D7tjKZlCq4Ldlc7tV/73GN+0tHUt11VaDKdw0bx\r\nKTFhauG9f8aHVeKHeke7ZQafcWi0dfgzQT5za5T0r5kgdp5Txr2+Vn7s7aR5\r\nCmghoxMFBFQDqYjNV9tl8oaBS88VPq5ODDB2RIewn7JHchaRi75sC0qmBXRH\r\nsflp4vdrystKGfrTMN8b8r87ohjFl02CrtlHVHx1C8Jqj8I7SWVh+Svrh6Pz\r\n9nnR1OLPbrOY6RJ+fHv+pxy7j8Um8xMHSkpBTsWGGMi7lZU8Jv2sg6mxU/hV\r\nypQzhEa0/I3QzBDxHZQWcBB3I76kOXD6yxI=\r\n=wOhw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "2283c926b5e7db32d18e682bae61e27024dad25a", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4", "sass-embedded-linux-arm": "1.55.0", "sass-embedded-linux-x64": "1.55.0", "sass-embedded-win32-x64": "1.55.0", "sass-embedded-darwin-x64": "1.55.0", "sass-embedded-linux-ia32": "1.55.0", "sass-embedded-win32-ia32": "1.55.0", "sass-embedded-linux-arm64": "1.55.0", "sass-embedded-darwin-arm64": "1.55.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^27.2.5", "yaml": "^1.10.2", "yargs": "^17.2.1", "protoc": "^1.0.4", "shelljs": "^0.8.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "minipass": "3.2.1", "@types/tar": "^6.1.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "make-fetch-happen": "^10.1.2", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@types/make-fetch-happen": "^9.0.2"}, "compiler-version": "1.55.0", "protocol-version": "1.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.55.0", "sass-embedded-linux-x64": "1.55.0", "sass-embedded-win32-x64": "1.55.0", "sass-embedded-darwin-x64": "1.55.0", "sass-embedded-linux-ia32": "1.55.0", "sass-embedded-win32-ia32": "1.55.0", "sass-embedded-linux-arm64": "1.55.0", "sass-embedded-darwin-arm64": "1.55.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.55.0_1663887717401_0.7325338923469054", "host": "s3://npm-registry-packages"}}, "1.56.1": {"name": "sass-embedded", "version": "1.56.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.56.1", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "749fb3c3cbee26b7234224727b33a0475023b867", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.56.1.tgz", "fileCount": 122, "integrity": "sha512-8VuohdRoGfqVWgBNeC+iqek1KXIVWYcG6AOQ6rJvRUe08HbdPQgp0+fseDQX7E5UxaoM8wvU5VBwCbZvPwFZQw==", "signatures": [{"sig": "MEYCIQDBQIk8BOdfgNB0RlFeI7nAsxzY0UDkIxlL3ViC3oaVdwIhAIn/OhLjfISdHLLFia6gug2uWOhMZIF8qJH6PWYOd1qm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1203687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjbCf+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLEA/9H8MTu4ea285hfR1X6jDpmXGWxdlYRFF/xT/sOZN7FCK2phwi\r\nYaosPB3eL6+fHvfRx1Ayxax9XKGCg9lwWR6ji7sOHVXmPq3s9BRhh0Bfvb7L\r\ntiXxxiOilqiBZEc85ML2luFRxSUGTDkSuNPYnChPboivicPez7XMVbDrvE65\r\nzqAKoyQypx1A7plu6xtIm29pBq1Ln/+Tn2s7LsPC/4XujZWBxJyIskE7bDOm\r\nv2254qaYlGAyXGTKyUbrUNpWjs3CVshdGik6iSYdj6631RL6gR6ft6eCzt/r\r\nwN04ykUFdykkqXv3ihZZJfKpog1L0OzmSPZKy6QHvS4DBOjDu+GeU1+e+2d5\r\nKhL1ueHCRgTaLu4MrZ01BvxNQq1oktpAdmisPdPvVW0FPikC8bql3TjeRofM\r\nveW+z2lTR9nu9O9eqZZuMipYuMMUi73wu87InAor8SvbFCO7nA3Xkz0F28jW\r\nAkBlXAR8HzOIKgbGnid25hZyanPWR0R5BtDoMxBB49HveXZpabEoOb8EseIY\r\nG/flMApUO6MWCVGiHcCN+WnLiorLdEpPzwedkSeKk68jDWs/C2BBwkqlOhR2\r\nuv4JrdZcgjKDg2cgvHX0McCLBcgu8sDhEdy7rmY0iR+hYizyvhvxEpODiyDT\r\noFlr76vftW6wl3ReTSrkkFvHQkQP9IxY2GU=\r\n=BWwp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "644c64d59749639415035fa7fbb6422a7c8be2a2", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4", "sass-embedded-linux-arm": "1.56.1", "sass-embedded-linux-x64": "1.56.1", "sass-embedded-win32-x64": "1.56.1", "sass-embedded-darwin-x64": "1.56.1", "sass-embedded-linux-ia32": "1.56.1", "sass-embedded-win32-ia32": "1.56.1", "sass-embedded-linux-arm64": "1.56.1", "sass-embedded-darwin-arm64": "1.56.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^27.2.5", "yaml": "^1.10.2", "yargs": "^17.2.1", "protoc": "1.0.4", "shelljs": "^0.8.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "minipass": "3.2.1", "@types/tar": "^6.1.0", "node-fetch": "^2.6.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.6.0", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.56.1", "protocol-version": "1.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.56.1", "sass-embedded-linux-x64": "1.56.1", "sass-embedded-win32-x64": "1.56.1", "sass-embedded-darwin-x64": "1.56.1", "sass-embedded-linux-ia32": "1.56.1", "sass-embedded-win32-ia32": "1.56.1", "sass-embedded-linux-arm64": "1.56.1", "sass-embedded-darwin-arm64": "1.56.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.56.1_1668032510206_0.25404264693528766", "host": "s3://npm-registry-packages"}}, "1.56.2": {"name": "sass-embedded", "version": "1.56.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.56.2", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "9b64f142e1fdbd16db910009bfe070a1e75b28b6", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.56.2.tgz", "fileCount": 122, "integrity": "sha512-aSW5Qio0FwVCRAdHQqdvxl8tgrrHvAa/RmngBV33Xw3XaILhw7Dzfh8W/j8KO7jZo5VufCnKRa0g/5neQyQCZw==", "signatures": [{"sig": "MEUCIQCKYcL4Vxx9TX5LcC7yCCeVocOckLBCG4rl2WlQkV65wAIgdRalpvzwYDST9ZcqLOQcwbPEDRjQFbu9xPLBePhURgc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1203837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkm/aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmog0w//cWyrxyNDl00gLRvQ+t2OQEX6k5JqDJqf21fD/75V/zRBGEy9\r\nfM5YJ8jfTS4/yKAvUvpD8SeDvUFrVP7h7zW3xDlndR8BUs9fBfyqictFjGRV\r\ndEAR2fOSQEp04sqGGigcZBJU/Bz1/2tsnwbliwR0DpbpVnBDUrgA4QXBHvA2\r\nb7nqgOQHyumxdAzDc3fT5sTfJ6o1w0gQygUFzHng/H4JEWCQwDfHXtVs63sT\r\nhvqS7tpRro3dWE2dWOy/YRtJtipFKFtnwZdEOKtGJ15QPaXfSugVxT1d++I/\r\n9HUBKfxWsdf+84zQzIMocSMQMdGk5LhjzDVwDkzc3xyhqlvMF6Dqh7gXkzxc\r\nu01eeYSxUoXPQ9dVd9KzqBdTMWZnNRPi1wUtUgLLF6b81yNEIinxT76UE1GT\r\nLEg/fAP/4BHZASDx/YT/EzoK/gf8INOnpIPg9yUTkCB2xRAsNKO6CmWwQDB8\r\nbCiAkkNCBVH9kJfiUWaQZMcSSujTrN8LmfiZ6rJjwqRIIMqzmMeV+ve0Zmb3\r\nwBQHHxJ56QmmLKDAFJ6bDaKoQGx2k+w+Q04E7vE/qjRXskKbNnJbaygJgoto\r\nFFrVVrqpXgbjbVeIuD9ptemTZH7ogm/DHexFkHHkh3jg5oZnHvTEVKrK2uRO\r\nbZS4okdKquYoPM2Bf1MdQ687WzxJx4/t3n8=\r\n=LyqS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "bba6a4f18488427477244aed6f62f73eb6d15e8a", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/prepare-dev-environment.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4", "sass-embedded-linux-arm": "1.56.2", "sass-embedded-linux-x64": "1.56.2", "sass-embedded-win32-x64": "1.56.2", "sass-embedded-darwin-x64": "1.56.2", "sass-embedded-linux-ia32": "1.56.2", "sass-embedded-win32-ia32": "1.56.2", "sass-embedded-linux-arm64": "1.56.2", "sass-embedded-darwin-arm64": "1.56.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^27.2.5", "yaml": "^1.10.2", "yargs": "^17.2.1", "protoc": "1.0.4", "shelljs": "^0.8.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "minipass": "3.2.1", "@types/tar": "^6.1.0", "node-fetch": "^2.6.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.6.0", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.56.2", "protocol-version": "1.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.56.2", "sass-embedded-linux-x64": "1.56.2", "sass-embedded-win32-x64": "1.56.2", "sass-embedded-darwin-x64": "1.56.2", "sass-embedded-linux-ia32": "1.56.2", "sass-embedded-win32-ia32": "1.56.2", "sass-embedded-linux-arm64": "1.56.2", "sass-embedded-darwin-arm64": "1.56.2"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.56.2_1670541273926_0.9587401435255081", "host": "s3://npm-registry-packages"}}, "1.57.1": {"name": "sass-embedded", "version": "1.57.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.57.1", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "2f689cf83ab72896e938f4a31e13758da65de938", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.57.1.tgz", "fileCount": 128, "integrity": "sha512-O0s796x76bRSJIdmJ4lrK+zJLtF3XeP+0tbJzR4NAPSDnWqHLk2boUYSdfx4DnF4x9rGmcVlMWFE8UvnLMNCmw==", "signatures": [{"sig": "MEUCIQD/t15CzcK8W2K/b9YG5oy0ce6DsUtX9PApmcJAqnVeDgIgXmu892FZe3PeYqvN+F2MQ1Eaw7C4opPvu3s8eG5Gnsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1203937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoQB9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp03A//aGNGmfJIvjDvJa+gQThUNWnEIq9SRdAZ6GMlpBiWukymewds\r\nNRawtvCeqoTfOAJzIkAjEnacyOjiZk9b+Dn2qEs4YGhpsUjKlmPZiyYkY4wW\r\nrQU/sm7tvh834w6O6JXIGX3h1q4cQFUG25r5GiroSDev/PhpVLcWIHBStBgE\r\nvLvN5xeGTZ3ev3ZyuCMhTXgx8gpE0Z6mQKo74kHQ5lUMafQ9mwdHQZ3IkhVf\r\nOdk85V6/0y9qPpe3+x8H2k/6kKWBbVo0GTeKVzEoPw6UvkSzTH5njcQVAVkQ\r\nCc0uOE7JMONxtOUwIa9p98Or1ar2zA262jiov2GRnPoHf2oEP+r4YamR3Rzr\r\n4+FArIdgkufOQqhzjDhzuNUh4cGjY0YtehBt5fwTo1J/yAwcmVJaDb/gn7Ge\r\nOThYuDHce0xIh+88oIxuyPCNFgtLQU0V7KmKIDGqspzeyDQ7BUdRS5OcGbs7\r\nvKdvkvhvWGn3kBX4WAJOjpRliRRowceqGwG/RjGXkepKeHE870RxDJmuH16X\r\nejo9ursZTFukZ0M8Vu+Y1WEDNhYO7xZu83odT31pnA/FBxXJj+RJsxTNsinI\r\nU40gJ/JL7ZbWyiDFq6GM4spj1oNWynlEEmOs+McEPRZs0RbSrOk8r+rx0eZT\r\nGK+nDCdYCvOTXoNGN0IdbZq+kqNMTYM+dGA=\r\n=YBpl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "092b54fdde893d7bbc1865fa30db91194e3b76ee", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "15.14.0", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "google-protobuf": "^3.11.4", "sass-embedded-linux-arm": "1.57.1", "sass-embedded-linux-x64": "1.57.1", "sass-embedded-win32-x64": "1.57.1", "sass-embedded-darwin-x64": "1.57.1", "sass-embedded-linux-ia32": "1.57.1", "sass-embedded-win32-ia32": "1.57.1", "sass-embedded-linux-arm64": "1.57.1", "sass-embedded-darwin-arm64": "1.57.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^27.2.5", "yaml": "^1.10.2", "yargs": "^17.2.1", "protoc": "1.0.4", "shelljs": "^0.8.4", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "minipass": "3.2.1", "@types/tar": "^6.1.0", "node-fetch": "^2.6.0", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "@types/node": "^16.10.3", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "source-map-js": "^0.6.1", "ts-protoc-gen": "^0.15.0", "@types/shelljs": "^0.8.8", "@types/node-fetch": "^2.6.0", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2"}, "compiler-version": "1.57.1", "protocol-version": "1.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.57.1", "sass-embedded-linux-x64": "1.57.1", "sass-embedded-win32-x64": "1.57.1", "sass-embedded-darwin-x64": "1.57.1", "sass-embedded-linux-ia32": "1.57.1", "sass-embedded-win32-ia32": "1.57.1", "sass-embedded-linux-arm64": "1.57.1", "sass-embedded-darwin-arm64": "1.57.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.57.1_1671495804868_0.6233752435915321", "host": "s3://npm-registry-packages"}}, "1.58.2": {"name": "sass-embedded", "version": "1.58.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.58.2", "maintainers": [{"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "c3f69897ebfed8359ecd4400b2d8d2ed8d0a641a", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.58.2.tgz", "fileCount": 126, "integrity": "sha512-ct7GRCtPpsRWLbCnJvUkPGQMwGuS7TBfmfb5NTFzSPizuMmIycgfT3B+tbVDeSqu9lkNar6/uCOrK8twqNawxg==", "signatures": [{"sig": "MEUCIApG0soktC9d8xq51bFYak4A1QIOsT6WhJeXpIAFAKshAiEA38cpIxDbd5hlHxx3dqC2zigpDfPHQQcKQeb6zzkJ8ZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7uItACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRLxAAlmdryTvfPNvfgahsK36S8HzNNvJo6CemtdvfWyBKV45f8K3T\r\nHUL8Wmwqa1SUvA3uKui4wPUQ+lMDA2gHugjTWFSo9BoMvmJjBmCFkXa7/7QP\r\nkSfKGdxs5EoQJWOh25LTAPr9lIeW+TQhj5CuHXj6QeZxe7/v+oi1jY8bnXm2\r\nkzSdp4mm+cBO0knTsbJd5C/aUxMoFfnCWa9OrcYK559q0SOzgKDYAhcoISdP\r\nevKk4WZ3Ry+z/7uFKFeWmKTcBAdqWW13Mv9wJwWIxzyix+Ivz4XY2ewi54+j\r\ne+VW2eF1QPwe+gTDCjBTcks/cYXUW4AdjRmTaVHVaplD28Q91OXaeBTQJ0mh\r\nnm2Jlg2XoxRYsrXXy59qItoTiiBcWZy0K1qPERC6+vOJcjqlCh/P+efpNvAq\r\ntS3UnBPwtl7d/LXUWxL2p7da0r0udo3qYUFzE2w0aY9zQQF6/2HigUaKMO6m\r\nNNyEl+j7Jw/Nn+kDTiUOQFS20nt6lmTzs6uWl69fds1NtfPWTDWib0ZRkpUD\r\nqoClXSp6mnLbyRyev8cqScWioK4lDGzwkxAPRuehhaG22A6gWt4I7/OkDr8q\r\n9HHEvOckon1MB2T1JpLA7tCmBr8kEGsOYMnKTX9YD/Ej5Z72nSZL7/EWEJYV\r\n8IkAYt/TBKO+tMR9Wgz3c6QZl/056vDhNlE=\r\n=De4C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "050f43c2425718141e2f5deab85ee33fe4c6a37e", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "deprecated": "Broken release, please use another version", "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.58.2", "sass-embedded-linux-x64": "1.58.2", "sass-embedded-win32-x64": "1.58.2", "sass-embedded-darwin-x64": "1.58.2", "sass-embedded-linux-ia32": "1.58.2", "sass-embedded-win32-ia32": "1.58.2", "sass-embedded-linux-arm64": "1.58.2", "sass-embedded-darwin-arm64": "1.58.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "4.0.3", "@types/tar": "^6.1.0", "path-equal": "^1.2.5", "typescript": "^4.4.3", "@types/jest": "^29.4.0", "@types/node": "^18.11.18", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.58.2", "protocol-version": "1.2.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.58.2", "sass-embedded-linux-x64": "1.58.2", "sass-embedded-win32-x64": "1.58.2", "sass-embedded-darwin-x64": "1.58.2", "sass-embedded-linux-ia32": "1.58.2", "sass-embedded-win32-ia32": "1.58.2", "sass-embedded-linux-arm64": "1.58.2", "sass-embedded-darwin-arm64": "1.58.2"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.58.2_1676599853474_0.670039965673219", "host": "s3://npm-registry-packages"}}, "1.58.3": {"name": "sass-embedded", "version": "1.58.3", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.58.3", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "14bbfe2ddcccd5cbed0feb8426cf8e2d944e018c", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.58.3.tgz", "fileCount": 128, "integrity": "sha512-PKU971G3mRgHfimkUJ9rGcYNkviz36muU/QtxTfkPB/YLWyVwHmlU+vvZ7WsBiauvWJapEwAoBDdJ5FlTYm3lQ==", "signatures": [{"sig": "MEYCIQDIvT06cGIgrngiDVPzpqpauwppIKdVKSzUt8YuqyBtUAIhAJaHupJXdXdW4JxEJuXHv8Sqqge2XyRO2vzh6zBMqJ2m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 621520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8CVxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozHw//VU9Flg/EAd474T0cmZos5auk+gmf+auK3ad4VXdQWFanWe3T\r\nz0cE5awPZcdoEz4Hn3Nl+GTQXe5Yw8jHLbA/kI9QvyVQ5Ax+kqMgpzASJeLx\r\n1fsR6bBYcm6s5g3f17reNYslAbkKdzqw8ie4bC3SzIDGm62GAnDbZptQ892M\r\nbkQhcvFSimWBPM13S/wgrEVBjMs2MwTJ7MM36nxxy8kqiCoIwkebruefsnwM\r\ngqQCjDeXLBed36HdQzG9vsRv6GdS9qPupn++CRDX6Fav/aqo/Er2cQViNQXm\r\naPpADDWYI5J4jPzDj7wwfzd189G+huv01e4lUddm4nwrHhxHl7/Qp0pBSTiG\r\n8mXGP1KxCtbuX4+kUGatHZylPWgE8wKpw6MZuFS/4Ms8xfXdSYmByIVGzSP4\r\nZuu3Tf+k05IUD4nhYYI9pZ2R4NLg48OenWCQV1ZzPRU3ddw/aQNLrMlHnY9y\r\nkievkTZTkKZbQY9Js/HV5qGfszL00mgESB0YYBWnuRqXf122J7mhIJlw0t9A\r\nJznGRbtMZUJLHBcpDmZkhIVm3ILjgX+bALORO7X5iJp6sRMN3b+WGN1UrYcD\r\nwg/9RN2+XE0uhjfr+RZO0XOXhYciOdMHc7U8s44Hv0a5rEcnAWiXVydc1NvA\r\nY5O6UGUi9BLkUG3ENxbGBkp4z+qOzyufw9Y=\r\n=kuFN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "12e87331f43081003148a4c9750d4351be3301a0", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.14.1", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.58.3", "sass-embedded-linux-x64": "1.58.3", "sass-embedded-win32-x64": "1.58.3", "sass-embedded-darwin-x64": "1.58.3", "sass-embedded-linux-ia32": "1.58.3", "sass-embedded-win32-ia32": "1.58.3", "sass-embedded-linux-arm64": "1.58.3", "sass-embedded-darwin-arm64": "1.58.3"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "4.0.3", "@types/tar": "^6.1.0", "path-equal": "^1.2.5", "typescript": "^4.4.3", "@types/jest": "^29.4.0", "@types/node": "^18.11.18", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.58.3", "protocol-version": "1.2.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.58.3", "sass-embedded-linux-x64": "1.58.3", "sass-embedded-win32-x64": "1.58.3", "sass-embedded-darwin-x64": "1.58.3", "sass-embedded-linux-ia32": "1.58.3", "sass-embedded-win32-ia32": "1.58.3", "sass-embedded-linux-arm64": "1.58.3", "sass-embedded-darwin-arm64": "1.58.3"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.58.3_1676682609688_0.6274670790864789", "host": "s3://npm-registry-packages"}}, "1.59.2": {"name": "sass-embedded", "version": "1.59.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.59.2", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "779f1323cc202bf4e5540bfbcad6630b6b14742d", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.59.2.tgz", "fileCount": 128, "integrity": "sha512-lZd1lxfVhHyTrnFCkuYn/65SgM6ysbnidIxTX6ZmYjb82Ied4DC0zvkheFh5wn5K76XzV2SSL18BgYShAXGCbQ==", "signatures": [{"sig": "MEQCIGtmuIzWBaTxGkv3sYhrDs4aQs8sStCfXkEkJ9ibDo2qAiB+PmD9y9A3Lxpyt1KlWNT4aXO+Mfw7f57D7Rx0P6cMLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 622609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkC930ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAHQ//Xz+PlpBkmoFDJ5Ud9q+BU9QSZ+LyvtL2Tr9Y82HHLHBinBso\r\nh48XfL26CNntOx4M5INQ3L+ClTn9bg0TFR2/PAWMAXURpDNPo1/cfJwro63N\r\n2U6ZRaWhOpqZKOey64WtQ1rDVY0sV2lqz4mJFX8vEoBcuEM0048D106IHMKN\r\n507V+jNViaP6z8tTmnLLKpMmW/Ma951fpV8g05mk52gvLEU8MicK0HXx1Hs0\r\nP4HJ5Nx66Qj0/OTgCcw59JaNun46WYk75zOjbg1luhJJ9DzCPIjbCeZv90Bn\r\nOphFhChpyeAdu/KDVN5l4eFTk0W9jPAp8w8kn274JgJT7KHV2HLwxD3cHLdT\r\nQKsZnVVfEk/i9nvLfr/yc1x+ULfbogj4utP05zoqenQtX8uQM7rjWJlV3R1C\r\n1FnALBjo/587C6NLv/Lwgng2HkivIX6As6As2S+5brEPa9YM6/y2SCXTnKnR\r\nOjGlgijBAFvLzJW4b+PEAVS1C8/PoIfiTTDrxSgaxe2MpAhWLT5NdJfLGSiA\r\nJuzVfa0txpYbvmjA+mSeQOunD5NymSpm08ZG9B+ZgUgfe0U27hfno/128HM7\r\ngM26zmXtoSyviIh317VFRGbV72Q375pF4DXE9nBuv6Ha4TCvQVHp4+iWtHUd\r\nv5yTI0FqIEs7YkMfkyx0l+NiNLeVpcH1eNc=\r\n=oUQ7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "003bc451763c7719e8787780f1f4163f2ea66c43", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.59.2", "sass-embedded-linux-x64": "1.59.2", "sass-embedded-win32-x64": "1.59.2", "sass-embedded-darwin-x64": "1.59.2", "sass-embedded-linux-ia32": "1.59.2", "sass-embedded-win32-ia32": "1.59.2", "sass-embedded-linux-arm64": "1.59.2", "sass-embedded-darwin-arm64": "1.59.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "4.0.3", "@types/tar": "^6.1.0", "path-equal": "^1.2.5", "typescript": "^4.4.3", "@types/jest": "^29.4.0", "@types/node": "^18.11.18", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.59.2", "protocol-version": "1.2.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.59.2", "sass-embedded-linux-x64": "1.59.2", "sass-embedded-win32-x64": "1.59.2", "sass-embedded-darwin-x64": "1.59.2", "sass-embedded-linux-ia32": "1.59.2", "sass-embedded-win32-ia32": "1.59.2", "sass-embedded-linux-arm64": "1.59.2", "sass-embedded-darwin-arm64": "1.59.2"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.59.2_1678499315982_0.7222548982473811", "host": "s3://npm-registry-packages"}}, "1.59.3": {"name": "sass-embedded", "version": "1.59.3", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.59.3", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "b53d25acf0f9a86925853119832887d310fc3b88", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.59.3.tgz", "fileCount": 128, "integrity": "sha512-HLyVxBCPzd4Z9EXrX11w3auKaajF3JWWETxagiGCxKKAsmA6SXDnXAkvU3gGhZQMFfDq6CiI5S7ISRn1dp2DCA==", "signatures": [{"sig": "MEUCIEmssEPu8PSV2NFAVHqBNwUR4wlFRZj9lql+tINWgqmdAiEAy49Y8FuFpXfVfzZinThxInpUg8I92U6l/UUz/sOKvVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 622609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEOwoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYoA/+NAnvnKyBfxkDAuLyXBaTG78nucvk28kAQz+OoxoJZ+Uyjr0R\r\nlPX8h9+9XWAQOHxxVkB6gnQevqvau4gLVM9KkM+We/VZoI5WSXWjQ9QRaOyy\r\naYihJHtzar1jnH57lKpagS3s/Dg1SNIywlQieuVPNiODF1EiePu3rDQqgn9f\r\n4hMkuVEdI0ZV+8scWNPmb2H8NJr59p20ESUYtUrlG6Q7WTKUZoy5knGI0Com\r\naFhZgZeHL57g7Q8COg7lPeb4jdj/Joj6Fc8h0VHhDbK+/an1zjBynFS6yxJV\r\ne2/pClaq3gt9ymNiLIYJmSdoJCVypsDdFmsPbhFGRhegnkAVxQzmu6iRtWRH\r\nhjDXuU2zVa+W43ZiKfFLK+qxIE1uS1kwDniLUYhN9a52awmzc2jKhL4Mp8eW\r\n/wTGyzH+GcFsx3JwojKqVDRgQaYmWEIumubrzKRAvKxxsdGCWVQ9f4UZPcVt\r\nGB/YRLK8i4REHty8k2BjsOxDl1Bljl3+/UmKShXyA4uN2iAA6l/wPyktSSBG\r\nSGZSl/64UI9s0TbwcGnxGMdLSsn7tTprnx8L2LdyK4EIQX9ghBUL1wZNmmDp\r\nxPAvwucte4sMXW27SvxZgmdpWeNys1tjrVUNhWlqQI0sMf9Dbb1uOEovycd5\r\nTwwyN+ejhlxrW0UNmQahax7bT9mRnVCu/zo=\r\n=tkxY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "7548747df8e26d77db93efa0052609cdb4be7430", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.59.3", "sass-embedded-linux-x64": "1.59.3", "sass-embedded-win32-x64": "1.59.3", "sass-embedded-darwin-x64": "1.59.3", "sass-embedded-linux-ia32": "1.59.3", "sass-embedded-win32-ia32": "1.59.3", "sass-embedded-linux-arm64": "1.59.3", "sass-embedded-darwin-arm64": "1.59.3"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "4.0.3", "@types/tar": "^6.1.0", "path-equal": "^1.2.5", "typescript": "^4.4.3", "@types/jest": "^29.4.0", "@types/node": "^18.11.18", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.59.3", "protocol-version": "1.2.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.59.3", "sass-embedded-linux-x64": "1.59.3", "sass-embedded-win32-x64": "1.59.3", "sass-embedded-darwin-x64": "1.59.3", "sass-embedded-linux-ia32": "1.59.3", "sass-embedded-win32-ia32": "1.59.3", "sass-embedded-linux-arm64": "1.59.3", "sass-embedded-darwin-arm64": "1.59.3"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.59.3_1678830632247_0.20310982497480845", "host": "s3://npm-registry-packages"}}, "1.60.0": {"name": "sass-embedded", "version": "1.60.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.60.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "c7c1e60108d28b4f7cf6f0b8934a51c05a2f686f", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.60.0.tgz", "fileCount": 128, "integrity": "sha512-pTCcbAaymfRX0Zkc17fGAVRBX3PPnj0vmWg93oGpeP/IMncxa58s+OPgDNIjSTDJ5wZGjTc95L1QY/zlubjC0g==", "signatures": [{"sig": "MEQCIFaeYpgA4ntdbO/hoDl+BniiBP/2t2CZsIOWIE8qJMy+AiApBF4nnL8Ejl73hmVPIDhLvLwhtHEu1+fZO7qjq98p4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 623371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHORtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCbg//W8QTDvmIgeRVhJwA06s9TiIE6Y40KDG78VMK1X06pia17rzU\r\nwvOL8s4WcXVshizu+R0p91S6fbmftnqsbz8dHMTaKp0yhnaAv+erXHvZuw61\r\ndaotToKxGIPVEDj8oKhAHrtEYJ+Zy9RORZ6uckMC2BT17nMZHKzhEtYb0jTu\r\n8+fhLRj4/iubcs9dgeHYzVZIyI2S+73JkaPYDOlbTjMN8lxdrdMgnzFBwwNX\r\nv+2XCnZoUMUQrIr1K88gq8/SMsnkSNhymjwx0qArvD7gkVY1yTZiZTBNh274\r\nSGYZx0G+6/RtxsJ6trg0XrfGizv/WBwH8NjSWwFEfUnggc7FTzZRCzkYZu6y\r\nNtOJ24DmjurAfiKakM1pzMn04ki3+qdXoPxlqeT8OmY/pqQehf8nrjCcebz8\r\nv2F3t2HEmVLU0lqTjUEozuK0Hu1bTkolZl6fvyncZiQ2KMJhynr5pmNT7rJm\r\nXHz1AeqKu1S30yt6afbMYXtELW+1DZKC7jcVbVpoCztMgEQY+nRO2fgTyUPX\r\nfb0aPdkbg1verdt2Emqwhdq6VTH0vh8oAF9+pvaH3Y+NNXP8snheug1RnOEZ\r\nV6v/agJnIjazaNZhL+tYxLTN5uToa5eLzmi1SH2uwPe92PJPhFQXxZ9GLt/o\r\nOgwpRbyr91avlr4I7l6yWdOKDoqPCnp8XYQ=\r\n=h9Fr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "8612f48f502904d4553b5ee0f871b305711a5f73", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.60.0", "sass-embedded-linux-x64": "1.60.0", "sass-embedded-win32-x64": "1.60.0", "sass-embedded-darwin-x64": "1.60.0", "sass-embedded-linux-ia32": "1.60.0", "sass-embedded-win32-ia32": "1.60.0", "sass-embedded-linux-arm64": "1.60.0", "sass-embedded-darwin-arm64": "1.60.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "4.2.5", "@types/tar": "^6.1.0", "path-equal": "^1.2.5", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^18.11.18", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.60.0", "protocol-version": "1.2.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.60.0", "sass-embedded-linux-x64": "1.60.0", "sass-embedded-win32-x64": "1.60.0", "sass-embedded-darwin-x64": "1.60.0", "sass-embedded-linux-ia32": "1.60.0", "sass-embedded-win32-ia32": "1.60.0", "sass-embedded-linux-arm64": "1.60.0", "sass-embedded-darwin-arm64": "1.60.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.60.0_1679615084946_0.27554172023125023", "host": "s3://npm-registry-packages"}}, "1.61.0": {"name": "sass-embedded", "version": "1.61.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.61.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "847a8fccbdf69ef6f0988257916f874b95324157", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.61.0.tgz", "fileCount": 128, "integrity": "sha512-xfwayccjo2SXpOVLcWHAtbze+F7pORddKwjZm4aFzNF2qw8gZ+VW03oFP6WmvSYAyzn9Kics2G1MZdqs6qmWkw==", "signatures": [{"sig": "MEUCIBCirp4+bZEWJ0LuKdxHSeC2LwiLFfAuXgH5ckFMXXUmAiEAvBiLKC0EAXFSWznT6fIhq8O2y4HUEfjOmmOIegRVaIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 623677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkL0fMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmRA//ddt+Ko3ScDEH2RxLnnIVNv+/wHVNRhQGVVfxsR8IU+sGTM5Q\r\nTKjqCGZ//mvRc45tS7S+T4FZyvd7d26GRKAVYvoUjIPlvJarDEsOvcAc8eZR\r\nzXwpT+o4ROzAfAJWQfnGtBAHw3ILgw83CwTCUuaB7phAKqRShDb+qDLrhoWi\r\nlfcWJ7D44C3KB0kdFE4czoq38Jw0323eC3p1kuTJaChekwU+TkkfrTCoG9Nl\r\nlfLCLEVK1iOuiVKthCQgP6A81GXBqFrDCEJ/FAiKzI24uIHxIfqw8fFwYc97\r\nHr7tbX7+PUhdOne8Dy3I2t520dLUdTSxC0/EqzOPU+oLeehLjmVSPEydMNQE\r\neB1jgHgnosYLH8JP9PjeojYLbJKVGZcGGTTYIwQ1ENLBjwP8hxW16UGK0KOP\r\nUAW+oFjNfDGwPZb8ozYbE8ht5pLlxu25el29EdW0pMErev1pQhpDkqrhNwbq\r\nGnC3I/SbE75TbeX1k/o1GYFBJ8S75UnhtxvgkIbCw6b0P14+qiNmnkE+4cRL\r\nYuc5okPWDv9uSskL0yJ4Pss4IAsiYr1G6NdfDNnar6MFmh0j6pNwphNdra9h\r\n/UeKZrLWQocu8b62chiJf+GPLYTNDceYiAVGH7FYwphpywrm1hBwD7KeZQ7W\r\nzcyn9E56R6GO6+ZzNU5wHLk1dKQB+mSJdF8=\r\n=T7JE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "d66b12feef10c781e9ec2646a543324f825816ee", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.61.0", "sass-embedded-linux-x64": "1.61.0", "sass-embedded-win32-x64": "1.61.0", "sass-embedded-darwin-x64": "1.61.0", "sass-embedded-linux-ia32": "1.61.0", "sass-embedded-win32-ia32": "1.61.0", "sass-embedded-linux-arm64": "1.61.0", "sass-embedded-darwin-arm64": "1.61.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "4.2.5", "@types/tar": "^6.1.0", "path-equal": "^1.2.5", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^18.11.18", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.61.0", "protocol-version": "1.2.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.61.0", "sass-embedded-linux-x64": "1.61.0", "sass-embedded-win32-x64": "1.61.0", "sass-embedded-darwin-x64": "1.61.0", "sass-embedded-linux-ia32": "1.61.0", "sass-embedded-win32-ia32": "1.61.0", "sass-embedded-linux-arm64": "1.61.0", "sass-embedded-darwin-arm64": "1.61.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.61.0_1680820171879_0.8053792075085151", "host": "s3://npm-registry-packages"}}, "1.62.0": {"name": "sass-embedded", "version": "1.62.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.62.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "43cc1ee78d317b7bc090bb3edf89e965bdc64015", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.62.0.tgz", "fileCount": 128, "integrity": "sha512-SwTIG6UmrMiT94/v8G+2pPf6i+XwY4hOQxm8HZl0ld0st2KdGDj/SBXDznFl7+sJ6tFq6hvVvrB9rW5Nj7EhuQ==", "signatures": [{"sig": "MEUCIERpxyYDJQkBpH0wlkf10SxoeC+oSc3+n0yn2HyKyWC+AiEAkuwnNtCs8El+Qn0EAp6gWKD7LakMwE0Qmx/RRu2BRL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 623677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNexzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZ/w/8DSf9ZadDt+/sJVY7/rk24APFMSXL2H009pkGGQi9kFdWb7hI\r\nSPeSZOJA/0SA6OwdDGe822mbr6dwgF7hsIaGx1Khp/Ij+F1DyXuVlivAZoo+\r\nNyl2dda0SqoMpG+x9833sGIQ5cxiFtuvtkM/fVEqXao2bQoY5Fb+3F7yBzF8\r\nhkgQM3DFxmR80i64ofADwonug25gdbWclgjcrJqTTW/xo6ChEHGQ9HhlwtwW\r\nlLk+mYwRJoQKrduqxzp2cFdYbkr3G12r9bn3+ARgZEoY3MRMg//q4aScSwb+\r\nJhIHhBYOaOREIGjMx8E+3NFGUuPeR+28bH6hRUsC06CQsePwrpd76bSHobL/\r\ndqe2cncQF7x8hAKJt2/Ht9Svt7FJ57QOT3gS+fwCfVOgNN0quq/SuPuvuitH\r\nzATasa4QY4UbU4xAsQKw+FIFj7sg+Iy8t7XYYT/BjtYMsyNpGYE0crhx1go4\r\nfppSt+UNz25CwivI2ony7w9qFoj/8YwEg8FbdIY/YtTNjdB7jLRJIjYNvqft\r\n6ud1EQRPBoWN5qinktv4otVv1k3Sc0EZ1BAhSNEfaCHyihVdfLlCncJHtn86\r\nAoA5X0zZgJz16zjpwmO2HQx/Lcv4eHiGFXFgcn2UQRQGavynvjEebmyoOXRt\r\nHUPlcQwBQEzwxhrwxF5Qb3cJLfkA1NeNchI=\r\n=LqOc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "gitHead": "60df05b866096fa3be4e1beefd37eccc0b3cd607", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"rxjs": "^7.4.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.62.0", "sass-embedded-linux-x64": "1.62.0", "sass-embedded-win32-x64": "1.62.0", "sass-embedded-darwin-x64": "1.62.0", "sass-embedded-linux-ia32": "1.62.0", "sass-embedded-win32-ia32": "1.62.0", "sass-embedded-linux-arm64": "1.62.0", "sass-embedded-darwin-arm64": "1.62.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "4.2.5", "@types/tar": "^6.1.0", "path-equal": "^1.2.5", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^18.11.18", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.62.0", "protocol-version": "1.2.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.62.0", "sass-embedded-linux-x64": "1.62.0", "sass-embedded-win32-x64": "1.62.0", "sass-embedded-darwin-x64": "1.62.0", "sass-embedded-linux-ia32": "1.62.0", "sass-embedded-win32-ia32": "1.62.0", "sass-embedded-linux-arm64": "1.62.0", "sass-embedded-darwin-arm64": "1.62.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.62.0_1681255539619_0.027785468239176714", "host": "s3://npm-registry-packages"}}, "1.63.5": {"name": "sass-embedded", "version": "1.63.5", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.63.5", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "0a86df057878a1cde7dc3d08a5d9e2addd7b1a3d", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.63.5.tgz", "fileCount": 127, "integrity": "sha512-O1wKG1oINiAUHVqkF+pcf+SJMe9iMm4xPrWGbVHahrOpwj0smCMAsU0lPwOtmuq06fJy2hdd/j4D/oXXUdv5+g==", "signatures": [{"sig": "MEUCIBoD5kDQ3XwpHmqc5ylFCRX3UxqIKQebyybIxISqj+GUAiEAryf0W9XZHw3FDg1gMS7ZWErBDSfraKudRiXd9Oni614=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 623719}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"import": "dist/lib/index.mjs", "default": "dist/lib/index.js"}, "gitHead": "f698ad1c661431102dace3b8ac50599744547835", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc && cp lib/index.mjs dist/lib/index.mjs", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.63.5", "sass-embedded-linux-x64": "1.63.5", "sass-embedded-win32-x64": "1.63.5", "sass-embedded-darwin-x64": "1.63.5", "sass-embedded-linux-ia32": "1.63.5", "sass-embedded-win32-ia32": "1.63.5", "sass-embedded-linux-arm64": "1.63.5", "sass-embedded-darwin-arm64": "1.63.5"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "6.0.2", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.63.5", "protocol-version": "2.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.63.5", "sass-embedded-linux-x64": "1.63.5", "sass-embedded-win32-x64": "1.63.5", "sass-embedded-darwin-x64": "1.63.5", "sass-embedded-linux-ia32": "1.63.5", "sass-embedded-win32-ia32": "1.63.5", "sass-embedded-linux-arm64": "1.63.5", "sass-embedded-darwin-arm64": "1.63.5"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.63.5_1687311113502_0.5229775430370875", "host": "s3://npm-registry-packages"}}, "1.63.6": {"name": "sass-embedded", "version": "1.63.6", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.63.6", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "cabdf560d41fa56c91a77f409ebdb70a93f88771", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.63.6.tgz", "fileCount": 127, "integrity": "sha512-Vt6sHVNHzvpDJchIsHQyXXjGifwCnMNBGpDJLpb2jT1/ESTPyNBrikGjVmIQCHZBGT86cUxxKCUfHPCqrerDaA==", "signatures": [{"sig": "MEQCIDr5Ri4dumJcgWGMogSt9SIxoHv9kNMHwyEsJ7iMyolcAiB1L7PKoBFTJeI83sinzFCKI68oLtknLpHzrc/8HFmRAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 623727}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"import": "./dist/lib/index.mjs", "default": "./dist/lib/index.js"}, "gitHead": "886dba4ab8260076c64e131bb0cca282e1495d4f", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc && cp lib/index.mjs dist/lib/index.mjs", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.63.6", "sass-embedded-linux-x64": "1.63.6", "sass-embedded-win32-x64": "1.63.6", "sass-embedded-darwin-x64": "1.63.6", "sass-embedded-linux-ia32": "1.63.6", "sass-embedded-win32-ia32": "1.63.6", "sass-embedded-linux-arm64": "1.63.6", "sass-embedded-darwin-arm64": "1.63.6"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "6.0.2", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.63.6", "protocol-version": "2.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.63.6", "sass-embedded-linux-x64": "1.63.6", "sass-embedded-win32-x64": "1.63.6", "sass-embedded-darwin-x64": "1.63.6", "sass-embedded-linux-ia32": "1.63.6", "sass-embedded-win32-ia32": "1.63.6", "sass-embedded-linux-arm64": "1.63.6", "sass-embedded-darwin-arm64": "1.63.6"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.63.6_1687386662233_0.6584998391812926", "host": "s3://npm-registry-packages"}}, "1.64.0": {"name": "sass-embedded", "version": "1.64.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.64.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "0fde82f0d2d537cc4f3b6df9de7f12b8d8fb72f3", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.64.0.tgz", "fileCount": 130, "integrity": "sha512-lr48TpBdTWG3u/0gtGFhF9tVLaLvdJoRkM5rc8x7nyIzzFekqJAuCtn3X/Y8npb24Bz5Qc5SVrtOplNIHM+SBA==", "signatures": [{"sig": "MEQCIHrkDJ/rR10CFFGMmtP0ok1GovXCShXk8lNo5StO3e/VAiBY0JSYRVNUxDcu2vvTplSlXaNMDGYuHJrz3AVin7Ujfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 647820}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"import": "./dist/lib/index.mjs", "default": "./dist/lib/index.js"}, "gitHead": "2b1f4ecaf83a9e949976427f522291f674a04a57", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc && cp lib/index.mjs dist/lib/index.mjs", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.64.0", "sass-embedded-linux-x64": "1.64.0", "sass-embedded-win32-x64": "1.64.0", "sass-embedded-darwin-x64": "1.64.0", "sass-embedded-linux-ia32": "1.64.0", "sass-embedded-win32-ia32": "1.64.0", "sass-embedded-linux-arm64": "1.64.0", "sass-embedded-darwin-arm64": "1.64.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.1", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.64.0", "protocol-version": "2.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.64.0", "sass-embedded-linux-x64": "1.64.0", "sass-embedded-win32-x64": "1.64.0", "sass-embedded-darwin-x64": "1.64.0", "sass-embedded-linux-ia32": "1.64.0", "sass-embedded-win32-ia32": "1.64.0", "sass-embedded-linux-arm64": "1.64.0", "sass-embedded-darwin-arm64": "1.64.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.64.0_1689813313373_0.9891397759673133", "host": "s3://npm-registry-packages"}}, "1.64.1": {"name": "sass-embedded", "version": "1.64.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.64.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "67acc619ddc0353d619b03a7d0a2196a8907f15e", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.64.1.tgz", "fileCount": 130, "integrity": "sha512-dZSKFPOwITHxM8dYDg8VkwyOYZnKpY2R3rBKwXz+shLvhRCPJlEowsSZLwx19YPUoQFeh7RgqI5lloMzAzrryg==", "signatures": [{"sig": "MEYCIQDdpehZVk+n18nlRM00PJOwM3gBkA5wzbSGgcz822A0UQIhAIST9UWR0w8rxKZpIXz+jIfW0g5uZKMQuyqrpAt25NDj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 648235}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"import": "./dist/lib/index.mjs", "default": "./dist/lib/index.js"}, "gitHead": "da21436155f4c59ee867efa8d1479e1476b16626", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc && cp lib/index.mjs dist/lib/index.mjs", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.64.1", "sass-embedded-linux-x64": "1.64.1", "sass-embedded-win32-x64": "1.64.1", "sass-embedded-darwin-x64": "1.64.1", "sass-embedded-linux-ia32": "1.64.1", "sass-embedded-win32-ia32": "1.64.1", "sass-embedded-linux-arm64": "1.64.1", "sass-embedded-darwin-arm64": "1.64.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.1", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.64.1", "protocol-version": "2.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.64.1", "sass-embedded-linux-x64": "1.64.1", "sass-embedded-win32-x64": "1.64.1", "sass-embedded-darwin-x64": "1.64.1", "sass-embedded-linux-ia32": "1.64.1", "sass-embedded-win32-ia32": "1.64.1", "sass-embedded-linux-arm64": "1.64.1", "sass-embedded-darwin-arm64": "1.64.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.64.1_1689985746597_0.5207995555832037", "host": "s3://npm-registry-packages"}}, "1.64.2": {"name": "sass-embedded", "version": "1.64.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.64.2", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "5904373318c25f6c9d89b8aa43d9574118672eba", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.64.2.tgz", "fileCount": 130, "integrity": "sha512-BSBudEBf83mUVTJLr7tpXkYn2xLn4MHN9ss+TiYV7LQeaAyW9TCPMz/tTr82UOUjK/140OhgJUwBJ/jMYvBUsQ==", "signatures": [{"sig": "MEYCIQDBMilTI14Zqrr0W3w7hc3ew6KhPYu/8vXfZ5Lisu7uUAIhAPdkx38EIPmSRGhJbMCF6TDRqE1U4Ys0Gq3BonLbWSeW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 648235}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"import": "./dist/lib/index.mjs", "default": "./dist/lib/index.js"}, "gitHead": "4426f815c628df78b3406f0cca911f0b4727eeb3", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc && cp lib/index.mjs dist/lib/index.mjs", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.64.2", "sass-embedded-linux-x64": "1.64.2", "sass-embedded-win32-x64": "1.64.2", "sass-embedded-darwin-x64": "1.64.2", "sass-embedded-linux-ia32": "1.64.2", "sass-embedded-win32-ia32": "1.64.2", "sass-embedded-linux-arm64": "1.64.2", "sass-embedded-darwin-arm64": "1.64.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^4.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.2", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.64.2", "protocol-version": "2.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.64.2", "sass-embedded-linux-x64": "1.64.2", "sass-embedded-win32-x64": "1.64.2", "sass-embedded-darwin-x64": "1.64.2", "sass-embedded-linux-ia32": "1.64.2", "sass-embedded-win32-ia32": "1.64.2", "sass-embedded-linux-arm64": "1.64.2", "sass-embedded-darwin-arm64": "1.64.2"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.64.2_1690848027805_0.8388199825704628", "host": "s3://npm-registry-packages"}}, "1.66.0": {"name": "sass-embedded", "version": "1.66.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.66.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "73dcf9a006486c8d1220973149dcbe1647712c01", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.66.0.tgz", "fileCount": 130, "integrity": "sha512-7PqQW3bNIGadntvPsBy3gwjAVdPf9iG+WmdoN3VcWXLbYtHn/Tl5YqRRhqfTxW2ue9xw1QIlEsxYAQ71zKgfHQ==", "signatures": [{"sig": "MEQCIDPn5HccKu79E8k7P7uwzyTLLTaTUGiS2jxU4zI7i1SsAiAQq78INpwKzdd5cdjHAqV+gUhEK0JBKYWDaNPE68Z5og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 649482}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"import": "./dist/lib/index.mjs", "default": "./dist/lib/index.js"}, "gitHead": "c77d44793cb79be28caff1dd0adf7d502f1d5058", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc && cp lib/index.mjs dist/lib/index.mjs", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.66.0", "sass-embedded-linux-x64": "1.66.0", "sass-embedded-win32-x64": "1.66.0", "sass-embedded-darwin-x64": "1.66.0", "sass-embedded-linux-ia32": "1.66.0", "sass-embedded-win32-ia32": "1.66.0", "sass-embedded-linux-arm64": "1.66.0", "sass-embedded-darwin-arm64": "1.66.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.3", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.66.0", "protocol-version": "2.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.66.0", "sass-embedded-linux-x64": "1.66.0", "sass-embedded-win32-x64": "1.66.0", "sass-embedded-darwin-x64": "1.66.0", "sass-embedded-linux-ia32": "1.66.0", "sass-embedded-win32-ia32": "1.66.0", "sass-embedded-linux-arm64": "1.66.0", "sass-embedded-darwin-arm64": "1.66.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.66.0_1692301877274_0.19375474181413188", "host": "s3://npm-registry-packages"}}, "1.66.1": {"name": "sass-embedded", "version": "1.66.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.66.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "76fcdbf217e33a1875a940d3d84a03cc74156388", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.66.1.tgz", "fileCount": 130, "integrity": "sha512-bHTFMSpcIJxrCav9ac69dMMYoiWIhiw/88eApojX7j1gsCAAx3nQMkTKr7FKIVVofWla8sE1KM9qtFya2k8svw==", "signatures": [{"sig": "MEYCIQDp2DNno/RNeSxzzI08M2/0xd0/snAZHomDIgXo2EMPNAIhANRAmOzwIu4N/2ORySU21wivMQvygZBGquKguIgSv9kb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 649482}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"import": "./dist/lib/index.mjs", "default": "./dist/lib/index.js"}, "gitHead": "489abd5c1cbdf1b9c53246b71bf10538ddf7e18b", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc && cp lib/index.mjs dist/lib/index.mjs", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.66.1", "sass-embedded-linux-x64": "1.66.1", "sass-embedded-win32-x64": "1.66.1", "sass-embedded-darwin-x64": "1.66.1", "sass-embedded-linux-ia32": "1.66.1", "sass-embedded-win32-ia32": "1.66.1", "sass-embedded-linux-arm64": "1.66.1", "sass-embedded-darwin-arm64": "1.66.1"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.3", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.66.1", "protocol-version": "2.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.66.1", "sass-embedded-linux-x64": "1.66.1", "sass-embedded-win32-x64": "1.66.1", "sass-embedded-darwin-x64": "1.66.1", "sass-embedded-linux-ia32": "1.66.1", "sass-embedded-win32-ia32": "1.66.1", "sass-embedded-linux-arm64": "1.66.1", "sass-embedded-darwin-arm64": "1.66.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.66.1_1692397379392_0.6048543938231303", "host": "s3://npm-registry-packages"}}, "1.67.0": {"name": "sass-embedded", "version": "1.67.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.67.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "2f64fd40af7e7c4e7eb1ba405d2e05c7df1e5356", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.67.0.tgz", "fileCount": 132, "integrity": "sha512-iHkwrJ4rUlHPU24DQVDzW3CtXnQIYVKkgaKvG+1sBQrGx+RFVSP6vnaUJrXNrYcYUa0+3Fo1NZA4ufoo7mL16w==", "signatures": [{"sig": "MEUCIE/GQ7Ul8Q1S0mXisdAUw7JM6ESUXM9ZTW3HYv+e4eCUAiEApkC85xwzs+TRgXGWFc1xC4EYtcLOVl0l5u9IAG7Bc9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 654886}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"import": "./dist/lib/index.mjs", "default": "./dist/lib/index.js"}, "gitHead": "f7509e60f8a73fb2ab653c3c5430bc7845e02f52", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc && cp lib/index.mjs dist/lib/index.mjs", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0", "sass-embedded-linux-arm": "1.67.0", "sass-embedded-linux-x64": "1.67.0", "sass-embedded-win32-x64": "1.67.0", "sass-embedded-darwin-x64": "1.67.0", "sass-embedded-linux-ia32": "1.67.0", "sass-embedded-win32-ia32": "1.67.0", "sass-embedded-linux-arm64": "1.67.0", "sass-embedded-darwin-arm64": "1.67.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.3", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.67.0", "protocol-version": "2.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.67.0", "sass-embedded-linux-x64": "1.67.0", "sass-embedded-win32-x64": "1.67.0", "sass-embedded-darwin-x64": "1.67.0", "sass-embedded-linux-ia32": "1.67.0", "sass-embedded-win32-ia32": "1.67.0", "sass-embedded-linux-arm64": "1.67.0", "sass-embedded-darwin-arm64": "1.67.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.67.0_1694653795536_0.4297304404621318", "host": "s3://npm-registry-packages"}}, "1.69.1": {"name": "sass-embedded", "version": "1.69.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.69.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "ea5aa7d54bf6c852bcf3d9b0fd1f77fc51cee81c", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.69.1.tgz", "fileCount": 155, "integrity": "sha512-ngjXJRJfQuPBSbJ8jB6FCSmlRkFsGPbgFOy6CrVhLEpZta0FE3rImimcbD5e+y8/CbeTgiaSlJsRhikOlfEPww==", "signatures": [{"sig": "MEYCIQCs6vWxRrGAM2rWw+05lnCGJEc66cupcJUWZGZM4iBAwgIhAOPq9HOQwXUtzwhiK8K6YiqM+D5zXI8Hpi2QFBSIqpcE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 720753}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/lib/index.d.ts", "import": {"types": "./dist/lib/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "1e01172550df8a97736fffe9441c1e4ae1a44194", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.69.1", "protocol-version": "2.3.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.69.1", "sass-embedded-linux-x64": "1.69.1", "sass-embedded-win32-x64": "1.69.1", "sass-embedded-darwin-x64": "1.69.1", "sass-embedded-linux-ia32": "1.69.1", "sass-embedded-win32-ia32": "1.69.1", "sass-embedded-linux-arm64": "1.69.1", "sass-embedded-darwin-arm64": "1.69.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.69.1_1696897353581_0.9161466798984763", "host": "s3://npm-registry-packages"}}, "1.69.2": {"name": "sass-embedded", "version": "1.69.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.69.2", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "3d68cf30da4c14b5b2c009d8cb47f9a245c65c6d", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.69.2.tgz", "fileCount": 155, "integrity": "sha512-B6vRMgpKkWagflo57FXvrpWxizQDJwCB7vquV3WVXzGsEWxRIX4CUWNR/Mq6lMohnkzuUb3ctW54Zrt/716l9Q==", "signatures": [{"sig": "MEUCIQCiAnOJnC0zbNzQPFlEBbr8pMcS28wOCS9yT7WBbhmsIgIgQAVsQmXQ3Jb0Pf9NAPD/kRXZko+WXvPvf4CEaPqgBlo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 720753}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/lib/index.d.ts", "import": {"types": "./dist/lib/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "b010e0d2dd2c2650f269ecc09d31467024a05c27", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.69.2", "protocol-version": "2.3.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.69.2", "sass-embedded-linux-x64": "1.69.2", "sass-embedded-win32-x64": "1.69.2", "sass-embedded-darwin-x64": "1.69.2", "sass-embedded-linux-ia32": "1.69.2", "sass-embedded-win32-ia32": "1.69.2", "sass-embedded-linux-arm64": "1.69.2", "sass-embedded-darwin-arm64": "1.69.2"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.69.2_1696964853301_0.8537066144188821", "host": "s3://npm-registry-packages"}}, "1.69.4": {"name": "sass-embedded", "version": "1.69.4", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.69.4", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "d40e4b4eee8f4b56e6c105f77d7a32e4835b6dfc", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.69.4.tgz", "fileCount": 142, "integrity": "sha512-j<PERSON>yuZ9qLKosJQV1w+3XA9REJmBHionM5uoJUz3FIG8cxv1m6phL++994w2rKJR16oh4V76I0FjzbavZKig3z0Q==", "signatures": [{"sig": "MEUCIQD5JBZw4G1frJrYHUvpEuf6ACdr7N1csc/QI/RoQyF2XQIgbRRH2c8S7P14A3DUrxCgAde7izMz6SMSHU3RZhYUMO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 675302}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "cc2d47070d47b47c06d9931d3dc79404caada92f", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.69.4", "protocol-version": "2.3.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.69.4", "sass-embedded-linux-x64": "1.69.4", "sass-embedded-win32-x64": "1.69.4", "sass-embedded-darwin-x64": "1.69.4", "sass-embedded-linux-ia32": "1.69.4", "sass-embedded-win32-ia32": "1.69.4", "sass-embedded-linux-arm64": "1.69.4", "sass-embedded-darwin-arm64": "1.69.4"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.69.4_1697580148058_0.46649352110696163", "host": "s3://npm-registry-packages"}}, "1.69.5": {"name": "sass-embedded", "version": "1.69.5", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.69.5", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "ae217d4b17b0fb07e5ed146917c9c9de0c4383c6", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.69.5.tgz", "fileCount": 142, "integrity": "sha512-0YNcRcbSpgGd4AnE+mm3a3g4S97puFLIZ0cYJgbwdD4iGz/hiOzE+yh72XS+u1LMhE+pQfNeC9MNnRsc8n1yRg==", "signatures": [{"sig": "MEUCIFBmYMpZRopF9AbaGzg8nefVRPQ6GQEqkm0CVvUzvJKjAiEAxakY93MWMgBMrmipBkiszqGH9k21fxS4xBfrvMkICSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 675302}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "b2e7b46abf0a06d2a698416f5b042445ef1e85b1", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.69.5", "protocol-version": "2.3.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.69.5", "sass-embedded-linux-x64": "1.69.5", "sass-embedded-win32-x64": "1.69.5", "sass-embedded-darwin-x64": "1.69.5", "sass-embedded-linux-ia32": "1.69.5", "sass-embedded-win32-ia32": "1.69.5", "sass-embedded-linux-arm64": "1.69.5", "sass-embedded-darwin-arm64": "1.69.5"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.69.5_1698283102224_0.4279084282993657", "host": "s3://npm-registry-packages"}}, "1.69.6": {"name": "sass-embedded", "version": "1.69.6", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.69.6", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "ec67a2317e125b886e676269fa33ad1ffcfc0578", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.69.6.tgz", "fileCount": 142, "integrity": "sha512-PMX0ortCXTYwAIzu/D4LUOPmBVYMaaZB5t0s5BumO8ZZkyCb/we2rE1NmQV7Uslu9X0ap4BVokxN4OBZocaOdA==", "signatures": [{"sig": "MEQCIDthWdEVuz/VMg/Yd5UFjAabMmbNOqTT7+eajsUNbo8yAiAJQEpX0RxLOkvp/grjCxTLyAy4A6T7b9y4lcNN5IXzdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 683399}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "5d97b33f7802dcbb1f2e7720b306e5b145faa973", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.69.6", "protocol-version": "2.3.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.69.6", "sass-embedded-linux-x64": "1.69.6", "sass-embedded-win32-x64": "1.69.6", "sass-embedded-darwin-x64": "1.69.6", "sass-embedded-linux-ia32": "1.69.6", "sass-embedded-win32-ia32": "1.69.6", "sass-embedded-android-arm": "1.69.6", "sass-embedded-android-x64": "1.69.6", "sass-embedded-linux-arm64": "1.69.6", "sass-embedded-android-ia32": "1.69.6", "sass-embedded-darwin-arm64": "1.69.6", "sass-embedded-android-arm64": "1.69.6", "sass-embedded-linux-musl-arm": "1.69.6", "sass-embedded-linux-musl-x64": "1.69.6", "sass-embedded-linux-musl-ia32": "1.69.6", "sass-embedded-linux-musl-arm64": "1.69.6"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.69.6_1703810598038_0.8127127407369026", "host": "s3://npm-registry-packages"}}, "1.69.7": {"name": "sass-embedded", "version": "1.69.7", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.69.7", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "99adf2fbf901a465bcdbb750674a6b35d0aabb99", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.69.7.tgz", "fileCount": 142, "integrity": "sha512-XzAOC/Y+w6a+g6qYctydSEXzZ/UVjD0SikGvjIjlGJBCxCCEI9Q4Ws8063L71Djdl8q3OUpZls9dokjcLHNe9Q==", "signatures": [{"sig": "MEUCIEPFjS9vu2EKaYpkxjAxdCYGhWEpyb4h1YZrqhQNA2zrAiEAwMWsi9cSItfNBM9hmBy3g1t/wUWkTyFUexqwpuvve3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 683399}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "ad6f671dd86cea81e500300dd3e300256c8d0fea", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.69.7", "protocol-version": "2.3.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.69.7", "sass-embedded-linux-x64": "1.69.7", "sass-embedded-win32-x64": "1.69.7", "sass-embedded-darwin-x64": "1.69.7", "sass-embedded-linux-ia32": "1.69.7", "sass-embedded-win32-ia32": "1.69.7", "sass-embedded-android-arm": "1.69.7", "sass-embedded-android-x64": "1.69.7", "sass-embedded-linux-arm64": "1.69.7", "sass-embedded-android-ia32": "1.69.7", "sass-embedded-darwin-arm64": "1.69.7", "sass-embedded-android-arm64": "1.69.7", "sass-embedded-linux-musl-arm": "1.69.7", "sass-embedded-linux-musl-x64": "1.69.7", "sass-embedded-linux-musl-ia32": "1.69.7", "sass-embedded-linux-musl-arm64": "1.69.7"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.69.7_1704234702312_0.38889790044652606", "host": "s3://npm-registry-packages"}}, "1.70.0": {"name": "sass-embedded", "version": "1.70.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.70.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "558f5e9776c6e4b91d9859a4dd325ac7c2b91391", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.70.0.tgz", "fileCount": 146, "integrity": "sha512-1sVSh5MlSdktkwC2zG9WuaVR6j7AlDxadPmZBN0wP4GhznMQTvpwNIAFhAqgjwJYhwdWFOKEdIHSQK4V8K434Q==", "signatures": [{"sig": "MEUCIDbA7HT/jyT7q/GjC2dZxhLaMhJ94i2zdK+rBUHhYo3fAiEA64eTLJqVR9Q26EqDTY0Hgi0IA63wJk0Px8XQI0zStcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 702114}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "e3f84628eb3008cab044195d77efb01af96934b8", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.70.0", "protocol-version": "2.4.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.70.0", "sass-embedded-linux-x64": "1.70.0", "sass-embedded-win32-x64": "1.70.0", "sass-embedded-darwin-x64": "1.70.0", "sass-embedded-linux-ia32": "1.70.0", "sass-embedded-win32-ia32": "1.70.0", "sass-embedded-android-arm": "1.70.0", "sass-embedded-android-x64": "1.70.0", "sass-embedded-linux-arm64": "1.70.0", "sass-embedded-android-ia32": "1.70.0", "sass-embedded-darwin-arm64": "1.70.0", "sass-embedded-android-arm64": "1.70.0", "sass-embedded-linux-musl-arm": "1.70.0", "sass-embedded-linux-musl-x64": "1.70.0", "sass-embedded-linux-musl-ia32": "1.70.0", "sass-embedded-linux-musl-arm64": "1.70.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.70.0_1705548034971_0.5464103583231337", "host": "s3://npm-registry-packages"}}, "1.71.0": {"name": "sass-embedded", "version": "1.71.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.71.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "0a58e3b101d595077d0ecf51a993364f52cb23a5", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.71.0.tgz", "fileCount": 146, "integrity": "sha512-iMdpt7J02qQ/4pSUlDrcYqHYOIHWRthsFp34Xr1UCaJM7tbt6xuLmfNBYRjQOsyyqfoPZlZQPxbEqrF1y0YAyg==", "signatures": [{"sig": "MEQCIFXUyABZkBYzwrRqyWdTC+KfSPAKSjUi5qCxwECdXoiYAiBSUEdEXyht06iCE+dCrpTM+L71VwiDqIz5GVE048QvmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 714031}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "6944074bb227660883253d2c57183301dd1bd1d6", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.71.0", "protocol-version": "2.5.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.71.0", "sass-embedded-linux-x64": "1.71.0", "sass-embedded-win32-x64": "1.71.0", "sass-embedded-darwin-x64": "1.71.0", "sass-embedded-linux-ia32": "1.71.0", "sass-embedded-win32-ia32": "1.71.0", "sass-embedded-android-arm": "1.71.0", "sass-embedded-android-x64": "1.71.0", "sass-embedded-linux-arm64": "1.71.0", "sass-embedded-android-ia32": "1.71.0", "sass-embedded-darwin-arm64": "1.71.0", "sass-embedded-android-arm64": "1.71.0", "sass-embedded-linux-musl-arm": "1.71.0", "sass-embedded-linux-musl-x64": "1.71.0", "sass-embedded-linux-musl-ia32": "1.71.0", "sass-embedded-linux-musl-arm64": "1.71.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.71.0_1708049033208_0.8166265632535281", "host": "s3://npm-registry-packages"}}, "1.71.1": {"name": "sass-embedded", "version": "1.71.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.71.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "566f800026edbc3a56c5fff9b61f4bc88eacb464", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.71.1.tgz", "fileCount": 146, "integrity": "sha512-nOmqErO1zd1wjvTbDscLZZ3fv5JPeQfaKuo0UCjYm7qPbpQcycp0l3nFZHxovjLjCetJ9IrLOADdznFYKV0f1A==", "signatures": [{"sig": "MEQCIGWM0agSoTEfJ8WGE1Mc4FJ79aKxL4TPNC8MBxHoWLACAiAFf7Abg2oVO7D6F3SV5LIBUg4W5BU1gLG2lrHyq1n9kQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 714770}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "02db7359cc2a8918fc7c2e05dc1a16f3a15f21b1", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.71.1", "protocol-version": "2.5.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.71.1", "sass-embedded-linux-x64": "1.71.1", "sass-embedded-win32-x64": "1.71.1", "sass-embedded-darwin-x64": "1.71.1", "sass-embedded-linux-ia32": "1.71.1", "sass-embedded-win32-ia32": "1.71.1", "sass-embedded-android-arm": "1.71.1", "sass-embedded-android-x64": "1.71.1", "sass-embedded-linux-arm64": "1.71.1", "sass-embedded-android-ia32": "1.71.1", "sass-embedded-darwin-arm64": "1.71.1", "sass-embedded-android-arm64": "1.71.1", "sass-embedded-linux-musl-arm": "1.71.1", "sass-embedded-linux-musl-x64": "1.71.1", "sass-embedded-linux-musl-ia32": "1.71.1", "sass-embedded-linux-musl-arm64": "1.71.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.71.1_1708481576786_0.7595837297285637", "host": "s3://npm-registry-packages"}}, "1.72.0": {"name": "sass-embedded", "version": "1.72.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.72.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "c2d036614a557d5eac68ada985456702097503ad", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.72.0.tgz", "fileCount": 146, "integrity": "sha512-MVdnYEEFVVkmsaAaEa9nWoIhCyezG6afJl2a7OF/WKl4PTGtCjgM9+yJBGUtAF9aJ36Us5hwNV8yqffnCJruyg==", "signatures": [{"sig": "MEQCIEdXQVhh5wyKT3m4V4cf0HzCozmwxVmtIa6eDH/Eng5qAiBSjO0xbNHmgS4eJMSIIfVwVEG2eJN/d9M4aaSzhYR00Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 714780}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "04adc7981207d14c99001e810b8c09beb25d2ba2", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.72.0", "protocol-version": "2.5.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.72.0", "sass-embedded-linux-x64": "1.72.0", "sass-embedded-win32-x64": "1.72.0", "sass-embedded-darwin-x64": "1.72.0", "sass-embedded-linux-ia32": "1.72.0", "sass-embedded-win32-ia32": "1.72.0", "sass-embedded-android-arm": "1.72.0", "sass-embedded-android-x64": "1.72.0", "sass-embedded-linux-arm64": "1.72.0", "sass-embedded-android-ia32": "1.72.0", "sass-embedded-darwin-arm64": "1.72.0", "sass-embedded-android-arm64": "1.72.0", "sass-embedded-linux-musl-arm": "1.72.0", "sass-embedded-linux-musl-x64": "1.72.0", "sass-embedded-linux-musl-ia32": "1.72.0", "sass-embedded-linux-musl-arm64": "1.72.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.72.0_1710365020101_0.7560688914390998", "host": "s3://npm-registry-packages"}}, "1.74.1": {"name": "sass-embedded", "version": "1.74.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.74.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "234059f0141cc80245953e957ae1c9f1be9e0da5", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.74.1.tgz", "fileCount": 149, "integrity": "sha512-VnrsLUfLVuLNq8Zrz6oaYmJ+hK5saaOhuwRC58yAcXYM+CRbS+6us3ab2+PE2a94H2V+cPU4XFvqibfz3C/XSw==", "signatures": [{"sig": "MEUCICqUf2iSWNwOwXVVDHrPx4rGFDN8mU+fQpPmw8ZdXloPAiEA/8m0aCNt2Vvt2/eRAFTI4ykmac7WJFElHv9qo4TVUvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 735028}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "eabbe379fe1224be32a1b8c36d7c0f3ca1913fe2", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.12.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.74.1", "protocol-version": "2.6.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.74.1", "sass-embedded-linux-x64": "1.74.1", "sass-embedded-win32-x64": "1.74.1", "sass-embedded-darwin-x64": "1.74.1", "sass-embedded-linux-ia32": "1.74.1", "sass-embedded-win32-ia32": "1.74.1", "sass-embedded-android-arm": "1.74.1", "sass-embedded-android-x64": "1.74.1", "sass-embedded-linux-arm64": "1.74.1", "sass-embedded-win32-arm64": "1.74.1", "sass-embedded-android-ia32": "1.74.1", "sass-embedded-darwin-arm64": "1.74.1", "sass-embedded-android-arm64": "1.74.1", "sass-embedded-linux-musl-arm": "1.74.1", "sass-embedded-linux-musl-x64": "1.74.1", "sass-embedded-linux-musl-ia32": "1.74.1", "sass-embedded-linux-musl-arm64": "1.74.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.74.1_1712193646661_0.701689630254239", "host": "s3://npm-registry-packages"}}, "1.75.0": {"name": "sass-embedded", "version": "1.75.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.75.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "99fc600bf38506a2033852487349dd8b177d0207", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.75.0.tgz", "fileCount": 149, "integrity": "sha512-8ZhQYJSCcjMRClyPpA09ZQ9p0Q9NtYxfMbhifBgUoQZC47Co5QJa0ykhfV/SY6mIqK7aAhMF7NAD5h0MEe2vpg==", "signatures": [{"sig": "MEQCIAYvBnGMOLMtDI9AokeDk4bHbEAntLkI1ZvGCSwd6YUzAiA/KTNRYybZgLL1Xz3/xmG9FuueNyxelWJ4CNWHKOWD6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 734135}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "0432b4ccfc0f9cb26e55e00778d033194dbd39e2", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.12.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.75.0", "protocol-version": "2.6.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.75.0", "sass-embedded-linux-x64": "1.75.0", "sass-embedded-win32-x64": "1.75.0", "sass-embedded-darwin-x64": "1.75.0", "sass-embedded-linux-ia32": "1.75.0", "sass-embedded-win32-ia32": "1.75.0", "sass-embedded-android-arm": "1.75.0", "sass-embedded-android-x64": "1.75.0", "sass-embedded-linux-arm64": "1.75.0", "sass-embedded-win32-arm64": "1.75.0", "sass-embedded-android-ia32": "1.75.0", "sass-embedded-darwin-arm64": "1.75.0", "sass-embedded-android-arm64": "1.75.0", "sass-embedded-linux-musl-arm": "1.75.0", "sass-embedded-linux-musl-x64": "1.75.0", "sass-embedded-linux-musl-ia32": "1.75.0", "sass-embedded-linux-musl-arm64": "1.75.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.75.0_1712877949614_0.9101887550994141", "host": "s3://npm-registry-packages"}}, "1.76.0": {"name": "sass-embedded", "version": "1.76.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.76.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "8deee368848618699db6f31b2bf60624be07a857", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.76.0.tgz", "fileCount": 151, "integrity": "sha512-dPtyZM5f32g/pVx7JjYxbeGyAOmbIAkotHmMMp63z8Nz+tqU6noc7GjGD+jml/w81YObJ7Tom2FvHAj24FNinw==", "signatures": [{"sig": "MEYCIQCvljaNOPJBrHe2h9bkQ85qs25xHP2GuMoJskvs/I7W9QIhAIPiZlWeKZ/oM5DQkdDVLMBZjdWpeyrlH9sPhwBBT09Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 740818}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "7cf4e6e7e96f081fedd55e51408c999cf1ed3b9c", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.0.4", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.76.0", "protocol-version": "2.7.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.76.0", "sass-embedded-linux-x64": "1.76.0", "sass-embedded-win32-x64": "1.76.0", "sass-embedded-darwin-x64": "1.76.0", "sass-embedded-linux-ia32": "1.76.0", "sass-embedded-win32-ia32": "1.76.0", "sass-embedded-android-arm": "1.76.0", "sass-embedded-android-x64": "1.76.0", "sass-embedded-linux-arm64": "1.76.0", "sass-embedded-win32-arm64": "1.76.0", "sass-embedded-android-ia32": "1.76.0", "sass-embedded-darwin-arm64": "1.76.0", "sass-embedded-android-arm64": "1.76.0", "sass-embedded-linux-musl-arm": "1.76.0", "sass-embedded-linux-musl-x64": "1.76.0", "sass-embedded-linux-musl-ia32": "1.76.0", "sass-embedded-linux-musl-arm64": "1.76.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.76.0_1714513804726_0.6934436894389009", "host": "s3://npm-registry-packages"}}, "1.77.0": {"name": "sass-embedded", "version": "1.77.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.77.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "8591ba5b090cdd885be40ab613b52b3700a8b524", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.77.0.tgz", "fileCount": 151, "integrity": "sha512-4GOuLIWQ3vm1XnmRlBz8Rj11rCBceqvkSvEy+ZZGjsR2Y9/+75Dh+zXCtPwOuJFHZsV7qbCqCCSxbpUA0TiQ3w==", "signatures": [{"sig": "MEYCIQD6VevlM42AIWgXDIIaZHV1CpE9gPJEbbzGcnTrUmSUxgIhAJVhoVHEv8GdZ4flAfJ2scwrr2pvHkuFRsszOCT9MwcA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 740818}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "bce32f3e8f29494c0a64dd2b4c23cfdc48ec7880", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.0", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.77.0", "protocol-version": "2.7.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.77.0", "sass-embedded-linux-x64": "1.77.0", "sass-embedded-win32-x64": "1.77.0", "sass-embedded-darwin-x64": "1.77.0", "sass-embedded-linux-ia32": "1.77.0", "sass-embedded-win32-ia32": "1.77.0", "sass-embedded-android-arm": "1.77.0", "sass-embedded-android-x64": "1.77.0", "sass-embedded-linux-arm64": "1.77.0", "sass-embedded-win32-arm64": "1.77.0", "sass-embedded-android-ia32": "1.77.0", "sass-embedded-darwin-arm64": "1.77.0", "sass-embedded-android-arm64": "1.77.0", "sass-embedded-linux-musl-arm": "1.77.0", "sass-embedded-linux-musl-x64": "1.77.0", "sass-embedded-linux-musl-ia32": "1.77.0", "sass-embedded-linux-musl-arm64": "1.77.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.77.0_1715044837509_0.5464778830944765", "host": "s3://npm-registry-packages"}}, "1.77.1": {"name": "sass-embedded", "version": "1.77.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.77.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "f8b4ec0253841ddf4397cf230974c48983b25b74", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.77.1.tgz", "fileCount": 151, "integrity": "sha512-+NkNlkEZQ5j9D6O+C8eH/v7JpccNImKM7DIzax84SfwOgBtOm7sqTCUe7hEzB9Cj5vtGl4QZbh2R+jTy7j8tXw==", "signatures": [{"sig": "MEUCIQCxtEuVTkdw01OkrifJPcVHOSk+XEQBIxtgtuS7ulrHKAIgUl1jujKXTuo/bpahUIXPe+T4tK2yH56vFxGpX9URt/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 741025}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "a8036afa5b6ee3b47527ed451c99446533080e83", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.0", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.77.1", "protocol-version": "2.7.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.77.1", "sass-embedded-linux-x64": "1.77.1", "sass-embedded-win32-x64": "1.77.1", "sass-embedded-darwin-x64": "1.77.1", "sass-embedded-linux-ia32": "1.77.1", "sass-embedded-win32-ia32": "1.77.1", "sass-embedded-android-arm": "1.77.1", "sass-embedded-android-x64": "1.77.1", "sass-embedded-linux-arm64": "1.77.1", "sass-embedded-win32-arm64": "1.77.1", "sass-embedded-android-ia32": "1.77.1", "sass-embedded-darwin-arm64": "1.77.1", "sass-embedded-android-arm64": "1.77.1", "sass-embedded-linux-musl-arm": "1.77.1", "sass-embedded-linux-musl-x64": "1.77.1", "sass-embedded-linux-musl-ia32": "1.77.1", "sass-embedded-linux-musl-arm64": "1.77.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.77.1_1715383126833_0.9446102023324598", "host": "s3://npm-registry-packages"}}, "1.77.2": {"name": "sass-embedded", "version": "1.77.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.77.2", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "d86e54e70d6bf4692aa0969bb7c7109dc3286116", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.77.2.tgz", "fileCount": 151, "integrity": "sha512-luiDeWNZ0tKs1jCiSFbuz8wFVQxYqN+vh+yfm9v7kW42yPtwEF8+z2ROaDJluSUZ7vhFmsXuqoKg9qBxc7SCnw==", "signatures": [{"sig": "MEUCIQCqd3X78H1Aexk8JhwiRnmm21PJXSiRf2j8TEZWsNJqNgIgOVhIs+u7vOA8WMaUsh+GjrjqwNxGSHrwm+7x9XTybSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 741025}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "38e7d6aaa114c03368a20ac247413349b31ef008", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.1", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.77.2", "protocol-version": "2.7.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.77.2", "sass-embedded-linux-x64": "1.77.2", "sass-embedded-win32-x64": "1.77.2", "sass-embedded-darwin-x64": "1.77.2", "sass-embedded-linux-ia32": "1.77.2", "sass-embedded-win32-ia32": "1.77.2", "sass-embedded-android-arm": "1.77.2", "sass-embedded-android-x64": "1.77.2", "sass-embedded-linux-arm64": "1.77.2", "sass-embedded-win32-arm64": "1.77.2", "sass-embedded-android-ia32": "1.77.2", "sass-embedded-darwin-arm64": "1.77.2", "sass-embedded-android-arm64": "1.77.2", "sass-embedded-linux-musl-arm": "1.77.2", "sass-embedded-linux-musl-x64": "1.77.2", "sass-embedded-linux-musl-ia32": "1.77.2", "sass-embedded-linux-musl-arm64": "1.77.2"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.77.2_1715898830488_0.4675907781949964", "host": "s3://npm-registry-packages"}}, "1.77.5": {"name": "sass-embedded", "version": "1.77.5", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.77.5", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "c21da62af45b56a3ffb2e4e9a663389efb56b77a", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.77.5.tgz", "fileCount": 157, "integrity": "sha512-JQI8aprHDRSNK5exXsbusswTENQPJxW1QWUcLdwuyESoJClT1zo8e+4cmaV5OAU4abcRC6Av4/RmLocPdjcR3A==", "signatures": [{"sig": "MEQCIDjWRWPCtcCuSVI6G72bBNUsIhgCkuFyY+yL54b0ErpGAiAp4ujtJbvi6nb56wltNU29cmN3WvFfOysVxy9BFS/7WA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 745326}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "274b1ce9e3594d1ad116a0acf759dc5183cae402", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.77.5", "protocol-version": "2.7.1", "optionalDependencies": {"sass-embedded-linux-arm": "1.77.5", "sass-embedded-linux-x64": "1.77.5", "sass-embedded-win32-x64": "1.77.5", "sass-embedded-darwin-x64": "1.77.5", "sass-embedded-linux-ia32": "1.77.5", "sass-embedded-win32-ia32": "1.77.5", "sass-embedded-android-arm": "1.77.5", "sass-embedded-android-x64": "1.77.5", "sass-embedded-linux-arm64": "1.77.5", "sass-embedded-win32-arm64": "1.77.5", "sass-embedded-android-ia32": "1.77.5", "sass-embedded-darwin-arm64": "1.77.5", "sass-embedded-android-arm64": "1.77.5", "sass-embedded-linux-musl-arm": "1.77.5", "sass-embedded-linux-musl-x64": "1.77.5", "sass-embedded-linux-musl-ia32": "1.77.5", "sass-embedded-linux-musl-arm64": "1.77.5"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.77.5_1718150885965_0.4641413288516436", "host": "s3://npm-registry-packages"}}, "1.77.8": {"name": "sass-embedded", "version": "1.77.8", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.77.8", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "dist": {"shasum": "d8d885ccd59c6040fcccd345299a115187d65726", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.77.8.tgz", "fileCount": 157, "integrity": "sha512-WGXA6jcaoBo5Uhw0HX/s6z/sl3zyYQ7ZOnLOJzqwpctFcFmU4L07zn51e2VSkXXFpQZFAdMZNqOGz/7h/fvcRA==", "signatures": [{"sig": "MEUCIB6VPaS3iV7sB5m0YfMXhzJiPhFJsU8gaTH/8rww01BhAiEAsJzkOr84yaCOiUsULXZo2DQdZVFYb2sXOzLC4xhaXZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 744197}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "65014ac8fbe097f1c8135240f0c3d79f1a890a5f", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.15.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.1.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.77.8", "protocol-version": "2.7.1", "optionalDependencies": {"sass-embedded-linux-arm": "1.77.8", "sass-embedded-linux-x64": "1.77.8", "sass-embedded-win32-x64": "1.77.8", "sass-embedded-darwin-x64": "1.77.8", "sass-embedded-linux-ia32": "1.77.8", "sass-embedded-win32-ia32": "1.77.8", "sass-embedded-android-arm": "1.77.8", "sass-embedded-android-x64": "1.77.8", "sass-embedded-linux-arm64": "1.77.8", "sass-embedded-win32-arm64": "1.77.8", "sass-embedded-android-ia32": "1.77.8", "sass-embedded-darwin-arm64": "1.77.8", "sass-embedded-android-arm64": "1.77.8", "sass-embedded-linux-musl-arm": "1.77.8", "sass-embedded-linux-musl-x64": "1.77.8", "sass-embedded-linux-musl-ia32": "1.77.8", "sass-embedded-linux-musl-arm64": "1.77.8"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.77.8_1720728936547_0.11381154734166099", "host": "s3://npm-registry-packages"}}, "1.78.0": {"name": "sass-embedded", "version": "1.78.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.78.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "8d11e16e2899d455ac9b2e6b223d19bee3a5e7bd", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.78.0.tgz", "fileCount": 161, "integrity": "sha512-NR2kvhWVFABmBm0AqgFw9OweQycs0Qs+/teJ9Su+BUY7up+f8S5F/Zi+7QtAqJlewsQyUNfzm1vRuM+20lBwRQ==", "signatures": [{"sig": "MEUCIGN/G7LMvCitECKKsKJHcV6mceRtOyOtc3byINaRrpErAiEA/nau3vhkw5AbiZENqou1Ka1ClgZoRM+RP/nouQZa0Zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 753674}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "80a503895b20b44263e7f3b7ce60bd6eb4dc458e", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.13.1-4", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^1.0.0"}, "compiler-version": "1.78.0", "protocol-version": "2.7.1", "optionalDependencies": {"sass-embedded-linux-arm": "1.78.0", "sass-embedded-linux-x64": "1.78.0", "sass-embedded-win32-x64": "1.78.0", "sass-embedded-darwin-x64": "1.78.0", "sass-embedded-linux-ia32": "1.78.0", "sass-embedded-win32-ia32": "1.78.0", "sass-embedded-android-arm": "1.78.0", "sass-embedded-android-x64": "1.78.0", "sass-embedded-linux-arm64": "1.78.0", "sass-embedded-win32-arm64": "1.78.0", "sass-embedded-android-ia32": "1.78.0", "sass-embedded-darwin-arm64": "1.78.0", "sass-embedded-android-arm64": "1.78.0", "sass-embedded-linux-riscv64": "1.78.0", "sass-embedded-linux-musl-arm": "1.78.0", "sass-embedded-linux-musl-x64": "1.78.0", "sass-embedded-android-riscv64": "1.78.0", "sass-embedded-linux-musl-ia32": "1.78.0", "sass-embedded-linux-musl-arm64": "1.78.0", "sass-embedded-linux-musl-riscv64": "1.78.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.78.0_1725405361804_0.1883932024574264", "host": "s3://npm-registry-packages"}}, "1.79.1": {"name": "sass-embedded", "version": "1.79.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.79.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "7450141a3a53527d1ec83e40ede025454e9fcfd0", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.79.1.tgz", "fileCount": 161, "integrity": "sha512-UIgG9sHZZ1fT28bxOGi6RUTyvuNvnNQ5nUCdeDGOiS+pGhxLNMdoSFd1cwF8cF5+JUfS6PK8TVAc+aNX4Q3ZGQ==", "signatures": [{"sig": "MEYCIQDavZnGLos1HhKohAfxQ8s4JSoa0xbKfz8SWH0OI5QvGwIhAIak9V35LMZK1aVwgTIBoSFZALzSYb6ZDCRPUeBct0MI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 691690}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "59dde59163a58a33aa8180dbf8e23d2216913712", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.79.1", "protocol-version": "3.0.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.79.1", "sass-embedded-linux-x64": "1.79.1", "sass-embedded-win32-x64": "1.79.1", "sass-embedded-darwin-x64": "1.79.1", "sass-embedded-linux-ia32": "1.79.1", "sass-embedded-win32-ia32": "1.79.1", "sass-embedded-android-arm": "1.79.1", "sass-embedded-android-x64": "1.79.1", "sass-embedded-linux-arm64": "1.79.1", "sass-embedded-win32-arm64": "1.79.1", "sass-embedded-android-ia32": "1.79.1", "sass-embedded-darwin-arm64": "1.79.1", "sass-embedded-android-arm64": "1.79.1", "sass-embedded-linux-riscv64": "1.79.1", "sass-embedded-linux-musl-arm": "1.79.1", "sass-embedded-linux-musl-x64": "1.79.1", "sass-embedded-android-riscv64": "1.79.1", "sass-embedded-linux-musl-ia32": "1.79.1", "sass-embedded-linux-musl-arm64": "1.79.1", "sass-embedded-linux-musl-riscv64": "1.79.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.79.1_1726619852490_0.35609819414149735", "host": "s3://npm-registry-packages"}}, "1.79.2": {"name": "sass-embedded", "version": "1.79.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.79.2", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "205c0dfd797b3c2fefa44d4e60d83c2b0c547777", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.79.2.tgz", "fileCount": 161, "integrity": "sha512-PYnPJJJcZ79NBLhC72MfZ+EWLkTIA2pizTEiNTtxXjLmqtKNkXO+TL9qXgTmot4F72ERVZqjQjBQUcfkNbXg/w==", "signatures": [{"sig": "MEUCIAR30yoyXtwTk/b6gut4XvsA32cjWCPNj0PuIi+tvGRVAiEA41DbnkhJXBcpuRcj+5G8DZRlg9mLNS1/upJgChg+xIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 691914}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "87eacff26dde8f0e04bf041bba028178bd65d015", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.79.2", "protocol-version": "3.0.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.79.2", "sass-embedded-linux-x64": "1.79.2", "sass-embedded-win32-x64": "1.79.2", "sass-embedded-darwin-x64": "1.79.2", "sass-embedded-linux-ia32": "1.79.2", "sass-embedded-win32-ia32": "1.79.2", "sass-embedded-android-arm": "1.79.2", "sass-embedded-android-x64": "1.79.2", "sass-embedded-linux-arm64": "1.79.2", "sass-embedded-win32-arm64": "1.79.2", "sass-embedded-android-ia32": "1.79.2", "sass-embedded-darwin-arm64": "1.79.2", "sass-embedded-android-arm64": "1.79.2", "sass-embedded-linux-riscv64": "1.79.2", "sass-embedded-linux-musl-arm": "1.79.2", "sass-embedded-linux-musl-x64": "1.79.2", "sass-embedded-android-riscv64": "1.79.2", "sass-embedded-linux-musl-ia32": "1.79.2", "sass-embedded-linux-musl-arm64": "1.79.2", "sass-embedded-linux-musl-riscv64": "1.79.2"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.79.2_1726787617058_0.1899289947167786", "host": "s3://npm-registry-packages"}}, "1.79.3": {"name": "sass-embedded", "version": "1.79.3", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.79.3", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "c4054ce2233d44639104f09e4c45e0da6a305165", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.79.3.tgz", "fileCount": 161, "integrity": "sha512-zUve2qCn6uSOMZnZazLzrDWq//OQWFle5G45vJjv3B/ADIA3TXVgqHqN3u7D2vGajOGREz0HN5nhliSoKmQqZA==", "signatures": [{"sig": "MEUCIQC3TvX30CpUzD/XEFvfcxLuIBIpjtCL3Qn5BANAMET+fAIgJeNHC/sEoWYb+GR6dsk8miQXuti1FFh0aXD/t0NUN0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 691914}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "8818ed15f18365876a763d6d8b96d79b79066b96", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.79.3", "protocol-version": "3.0.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.79.3", "sass-embedded-linux-x64": "1.79.3", "sass-embedded-win32-x64": "1.79.3", "sass-embedded-darwin-x64": "1.79.3", "sass-embedded-linux-ia32": "1.79.3", "sass-embedded-win32-ia32": "1.79.3", "sass-embedded-android-arm": "1.79.3", "sass-embedded-android-x64": "1.79.3", "sass-embedded-linux-arm64": "1.79.3", "sass-embedded-win32-arm64": "1.79.3", "sass-embedded-android-ia32": "1.79.3", "sass-embedded-darwin-arm64": "1.79.3", "sass-embedded-android-arm64": "1.79.3", "sass-embedded-linux-riscv64": "1.79.3", "sass-embedded-linux-musl-arm": "1.79.3", "sass-embedded-linux-musl-x64": "1.79.3", "sass-embedded-android-riscv64": "1.79.3", "sass-embedded-linux-musl-ia32": "1.79.3", "sass-embedded-linux-musl-arm64": "1.79.3", "sass-embedded-linux-musl-riscv64": "1.79.3"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.79.3_1726867487205_0.21894248031726415", "host": "s3://npm-registry-packages"}}, "1.79.4": {"name": "sass-embedded", "version": "1.79.4", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.79.4", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "45d52b9ca754c526763a1e255d4b98f788ff755b", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.79.4.tgz", "fileCount": 161, "integrity": "sha512-3AATrtStMgxYjkit02/Ix8vx/P7qderYG6DHjmehfk5jiw53OaWVScmcGJSwp/d77kAkxDQ+Y0r+79VynGmrkw==", "signatures": [{"sig": "MEUCIQCbMSaXvktOLhCl526ilZiIxlRs13PoQtuXyEODn9QoKAIgMLfBRAc3Q3iD5lLHh5jZTxOB+lujgY6HfjNjzVW/wvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 691914}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "1a63ffa13be89b0ecc0477ca64c8357c763cc9fd", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.79.4", "protocol-version": "3.0.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.79.4", "sass-embedded-linux-x64": "1.79.4", "sass-embedded-win32-x64": "1.79.4", "sass-embedded-darwin-x64": "1.79.4", "sass-embedded-linux-ia32": "1.79.4", "sass-embedded-win32-ia32": "1.79.4", "sass-embedded-android-arm": "1.79.4", "sass-embedded-android-x64": "1.79.4", "sass-embedded-linux-arm64": "1.79.4", "sass-embedded-win32-arm64": "1.79.4", "sass-embedded-android-ia32": "1.79.4", "sass-embedded-darwin-arm64": "1.79.4", "sass-embedded-android-arm64": "1.79.4", "sass-embedded-linux-riscv64": "1.79.4", "sass-embedded-linux-musl-arm": "1.79.4", "sass-embedded-linux-musl-x64": "1.79.4", "sass-embedded-android-riscv64": "1.79.4", "sass-embedded-linux-musl-ia32": "1.79.4", "sass-embedded-linux-musl-arm64": "1.79.4", "sass-embedded-linux-musl-riscv64": "1.79.4"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.79.4_1727494336024_0.24738364449525396", "host": "s3://npm-registry-packages"}}, "1.79.5": {"name": "sass-embedded", "version": "1.79.5", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.79.5", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "c8f21531aa8f9ac846032e5da633861c2379d9fa", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.79.5.tgz", "fileCount": 161, "integrity": "sha512-QFdalnjGFkbNvb6/uQGmP4OIN+GQ5/R77eu0PsXduDB1YP5JW5DSWFVDAyK6l6C54P+3J3eXkjuPYC0mcwX+AA==", "signatures": [{"sig": "MEQCIH7jtzKdzqHOCXIFl8Me0n8ms2B487xVS2Lda+193zV6AiA+1vaIsdvtoSFZSdfF9c3htJH9FBQBHwdMAvj4obNebQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 692167}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "d8ac21782f7f03449d28cf92729b725a539dab9e", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.79.5", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.79.5", "sass-embedded-linux-x64": "1.79.5", "sass-embedded-win32-x64": "1.79.5", "sass-embedded-darwin-x64": "1.79.5", "sass-embedded-linux-ia32": "1.79.5", "sass-embedded-win32-ia32": "1.79.5", "sass-embedded-android-arm": "1.79.5", "sass-embedded-android-x64": "1.79.5", "sass-embedded-linux-arm64": "1.79.5", "sass-embedded-win32-arm64": "1.79.5", "sass-embedded-android-ia32": "1.79.5", "sass-embedded-darwin-arm64": "1.79.5", "sass-embedded-android-arm64": "1.79.5", "sass-embedded-linux-riscv64": "1.79.5", "sass-embedded-linux-musl-arm": "1.79.5", "sass-embedded-linux-musl-x64": "1.79.5", "sass-embedded-android-riscv64": "1.79.5", "sass-embedded-linux-musl-ia32": "1.79.5", "sass-embedded-linux-musl-arm64": "1.79.5", "sass-embedded-linux-musl-riscv64": "1.79.5"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.79.5_1728693348078_0.6282804833953777", "host": "s3://npm-registry-packages"}}, "1.79.6": {"name": "sass-embedded", "version": "1.79.6", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.79.6", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "4ad27799dc7a87cd7c5da0750fb3e6c8d3ac4ff8", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.79.6.tgz", "fileCount": 161, "integrity": "sha512-5Wp7wTEq8q/ykrWdVrXmVTzcQT0eSx+eOLIL5W9sSRnKti+FuyPvPz84wizLDmcUe8K4gdzP+ASKykE5/w0Z0g==", "signatures": [{"sig": "MEUCIQCKAtdeBzwo1C/jKzOfdxQoR7H73PDK3BV8m2GCWwLwTAIgOHTATnsRRhcsJhKF5yRzWQz9HAU+euHCOF4T5oaSq+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 692167}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "4bd755889a5a400159bbd33270fae6101b4815b9", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.79.6", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.79.6", "sass-embedded-linux-x64": "1.79.6", "sass-embedded-win32-x64": "1.79.6", "sass-embedded-darwin-x64": "1.79.6", "sass-embedded-linux-ia32": "1.79.6", "sass-embedded-win32-ia32": "1.79.6", "sass-embedded-android-arm": "1.79.6", "sass-embedded-android-x64": "1.79.6", "sass-embedded-linux-arm64": "1.79.6", "sass-embedded-win32-arm64": "1.79.6", "sass-embedded-android-ia32": "1.79.6", "sass-embedded-darwin-arm64": "1.79.6", "sass-embedded-android-arm64": "1.79.6", "sass-embedded-linux-riscv64": "1.79.6", "sass-embedded-linux-musl-arm": "1.79.6", "sass-embedded-linux-musl-x64": "1.79.6", "sass-embedded-android-riscv64": "1.79.6", "sass-embedded-linux-musl-ia32": "1.79.6", "sass-embedded-linux-musl-arm64": "1.79.6", "sass-embedded-linux-musl-riscv64": "1.79.6"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.79.6_1729121983975_0.9338540882101691", "host": "s3://npm-registry-packages"}}, "1.80.0": {"name": "sass-embedded", "version": "1.80.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.80.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "12384e568b6e673bcc27fca027a74e9e91733005", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.80.0.tgz", "fileCount": 161, "integrity": "sha512-Y6kJlbhUW1F/34c120Lrp+SGjLwT/d/zTqhpVsL+IPDXZmjRhAp1WYjw5b8e+V1r5dT6aFbYW9NF4NMFZ6YLSA==", "signatures": [{"sig": "MEUCIHz8c7GofODQCBev8Pc23Twm4wSarpqFxeXx+XKIx4SWAiEA9mWGW+x50Vsu7wfRotPJ7vwrX7nK23yp3Eg5dvFtRaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 692408}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "69122e11116ebb48843bbaf73ee24445390c5903", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.80.0", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.80.0", "sass-embedded-linux-x64": "1.80.0", "sass-embedded-win32-x64": "1.80.0", "sass-embedded-darwin-x64": "1.80.0", "sass-embedded-linux-ia32": "1.80.0", "sass-embedded-win32-ia32": "1.80.0", "sass-embedded-android-arm": "1.80.0", "sass-embedded-android-x64": "1.80.0", "sass-embedded-linux-arm64": "1.80.0", "sass-embedded-win32-arm64": "1.80.0", "sass-embedded-android-ia32": "1.80.0", "sass-embedded-darwin-arm64": "1.80.0", "sass-embedded-android-arm64": "1.80.0", "sass-embedded-linux-riscv64": "1.80.0", "sass-embedded-linux-musl-arm": "1.80.0", "sass-embedded-linux-musl-x64": "1.80.0", "sass-embedded-android-riscv64": "1.80.0", "sass-embedded-linux-musl-ia32": "1.80.0", "sass-embedded-linux-musl-arm64": "1.80.0", "sass-embedded-linux-musl-riscv64": "1.80.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.80.0_1729125357606_0.8139574803692495", "host": "s3://npm-registry-packages"}}, "1.80.1": {"name": "sass-embedded", "version": "1.80.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.80.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "0dd7a3c799218f498fe3e3a7a42fcc8c243064ea", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.80.1.tgz", "fileCount": 161, "integrity": "sha512-FQiaiA2Bc1a3/nXdl9cAz19cKKcW5uU+k1JUWx5Tt1UcSYmV0B+5V0GDHwtyF7UeCuoBMRl3B3LxQ6n317HLYQ==", "signatures": [{"sig": "MEUCIQDgcV+RSYobaLCIvaGpBFIu9VWF+xtu2ZL9FuZlwwrp3QIgEbLHi3/vzuQmHaRyXrSRtYGGfR3xAgY0D2OWNVZfM+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 692408}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "a854ae52908ca12b0a700e9c23119a76b46d6186", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.80.1", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.80.1", "sass-embedded-linux-x64": "1.80.1", "sass-embedded-win32-x64": "1.80.1", "sass-embedded-darwin-x64": "1.80.1", "sass-embedded-linux-ia32": "1.80.1", "sass-embedded-win32-ia32": "1.80.1", "sass-embedded-android-arm": "1.80.1", "sass-embedded-android-x64": "1.80.1", "sass-embedded-linux-arm64": "1.80.1", "sass-embedded-win32-arm64": "1.80.1", "sass-embedded-android-ia32": "1.80.1", "sass-embedded-darwin-arm64": "1.80.1", "sass-embedded-android-arm64": "1.80.1", "sass-embedded-linux-riscv64": "1.80.1", "sass-embedded-linux-musl-arm": "1.80.1", "sass-embedded-linux-musl-x64": "1.80.1", "sass-embedded-android-riscv64": "1.80.1", "sass-embedded-linux-musl-ia32": "1.80.1", "sass-embedded-linux-musl-arm64": "1.80.1", "sass-embedded-linux-musl-riscv64": "1.80.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.80.1_1729135743464_0.7827375819497537", "host": "s3://npm-registry-packages"}}, "1.80.2": {"name": "sass-embedded", "version": "1.80.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.80.2", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "432647734502dfc6698041a33fef86f525e4c37e", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.80.2.tgz", "fileCount": 161, "integrity": "sha512-L0Ny0m0C4B3Lz+h/2I4w9jJVkvSqgU4EAsqUhBlaf8iWaR67f1k/jI1CmtXfW39eiYb7TV/JWaDEWGQxlwremQ==", "signatures": [{"sig": "MEYCIQDgfeUoGcuWDCaB3g7jrt33Zu9l+mP1WS3U9a3fDLg9jgIhALsR8MTEESOFmhvpi3SctJ+cnGjTbt1/6lG4Qln04l69", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 692408}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "2f8276910b0a8487e08295fdf60ef9ef9754cdb5", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.80.2", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.80.2", "sass-embedded-linux-x64": "1.80.2", "sass-embedded-win32-x64": "1.80.2", "sass-embedded-darwin-x64": "1.80.2", "sass-embedded-linux-ia32": "1.80.2", "sass-embedded-win32-ia32": "1.80.2", "sass-embedded-android-arm": "1.80.2", "sass-embedded-android-x64": "1.80.2", "sass-embedded-linux-arm64": "1.80.2", "sass-embedded-win32-arm64": "1.80.2", "sass-embedded-android-ia32": "1.80.2", "sass-embedded-darwin-arm64": "1.80.2", "sass-embedded-android-arm64": "1.80.2", "sass-embedded-linux-riscv64": "1.80.2", "sass-embedded-linux-musl-arm": "1.80.2", "sass-embedded-linux-musl-x64": "1.80.2", "sass-embedded-android-riscv64": "1.80.2", "sass-embedded-linux-musl-ia32": "1.80.2", "sass-embedded-linux-musl-arm64": "1.80.2", "sass-embedded-linux-musl-riscv64": "1.80.2"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.80.2_1729199065807_0.4290944397444285", "host": "s3://npm-registry-packages"}}, "1.80.3": {"name": "sass-embedded", "version": "1.80.3", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.80.3", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "9b0d99605f4f9921cb432f8052d33b03312acead", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.80.3.tgz", "fileCount": 161, "integrity": "sha512-aTxTl4ToSAWg7ILFgAe+kMenj+zNlwHmHK/ZNPrOM8+HTef1Q6zuxolptYLijmHdZHKSMOkWYHgo5MMN6+GIyg==", "signatures": [{"sig": "MEUCIFUBhxE+N0XU1tpxbLsY7cn5BtpX/HoH0VR4CanH/clsAiEAvOBLBWVotZkRP4yuufK2hivAgk+I5oAwu4O4twij4l0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 692408}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "40de6ec7fd5c423a9c8afd5d3f45c16ebd9f20d4", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.80.3", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.80.3", "sass-embedded-linux-x64": "1.80.3", "sass-embedded-win32-x64": "1.80.3", "sass-embedded-darwin-x64": "1.80.3", "sass-embedded-linux-ia32": "1.80.3", "sass-embedded-win32-ia32": "1.80.3", "sass-embedded-android-arm": "1.80.3", "sass-embedded-android-x64": "1.80.3", "sass-embedded-linux-arm64": "1.80.3", "sass-embedded-win32-arm64": "1.80.3", "sass-embedded-android-ia32": "1.80.3", "sass-embedded-darwin-arm64": "1.80.3", "sass-embedded-android-arm64": "1.80.3", "sass-embedded-linux-riscv64": "1.80.3", "sass-embedded-linux-musl-arm": "1.80.3", "sass-embedded-linux-musl-x64": "1.80.3", "sass-embedded-android-riscv64": "1.80.3", "sass-embedded-linux-musl-ia32": "1.80.3", "sass-embedded-linux-musl-arm64": "1.80.3", "sass-embedded-linux-musl-riscv64": "1.80.3"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.80.3_1729296105180_0.7804348469356692", "host": "s3://npm-registry-packages"}}, "1.80.4": {"name": "sass-embedded", "version": "1.80.4", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.80.4", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "4a8ebd9d455c4ffccffd5fd32ac05df8f9f38fcf", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.80.4.tgz", "fileCount": 161, "integrity": "sha512-lPzKX5g79ZxohlPxh0pXTPFseWj9RfgYI0cPm14CH5ok77Ujuheq/DCp7RStvNDWS8RCQ8Ii6gJC/5WTkGyrhA==", "signatures": [{"sig": "MEYCIQCB5Yjtzgyf0b1rhEhYJyG7q698/amQri9JtKCgwC85agIhAPBPgJYRGlAQcIH6EMTgBAdgkAvRSeU58K176ZJEvi7f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 692408}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "6d60eea34bf5fd4a83e59268ecabc613385b67ad", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.80.4", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.80.4", "sass-embedded-linux-x64": "1.80.4", "sass-embedded-win32-x64": "1.80.4", "sass-embedded-darwin-x64": "1.80.4", "sass-embedded-linux-ia32": "1.80.4", "sass-embedded-win32-ia32": "1.80.4", "sass-embedded-android-arm": "1.80.4", "sass-embedded-android-x64": "1.80.4", "sass-embedded-linux-arm64": "1.80.4", "sass-embedded-win32-arm64": "1.80.4", "sass-embedded-android-ia32": "1.80.4", "sass-embedded-darwin-arm64": "1.80.4", "sass-embedded-android-arm64": "1.80.4", "sass-embedded-linux-riscv64": "1.80.4", "sass-embedded-linux-musl-arm": "1.80.4", "sass-embedded-linux-musl-x64": "1.80.4", "sass-embedded-android-riscv64": "1.80.4", "sass-embedded-linux-musl-ia32": "1.80.4", "sass-embedded-linux-musl-arm64": "1.80.4", "sass-embedded-linux-musl-riscv64": "1.80.4"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.80.4_1729724645161_0.4949103993082209", "host": "s3://npm-registry-packages"}}, "1.80.5": {"name": "sass-embedded", "version": "1.80.5", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.80.5", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "1cd9dd03a2059aee80ac753e77c0a341fd01e9bc", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.80.5.tgz", "fileCount": 161, "integrity": "sha512-2rmFO02+NMk1no+OElI+H3+8wf0QDcbjmdIBATJhnPLKzbYbRt88U40rGN4pQWETFZ4hxppOGmGxn9MnHppHjw==", "signatures": [{"sig": "MEUCIQDdRxvQJXsmBAdD3tPzAERy6LZHj7tQDc5reZVT7OEv3AIgJLaTGMvNd4a6ZyjZftWIA1tFnd9qo1o5nwRwDW7kUno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 693255}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "768c1f61af6a245cfce7c9aca921384d452f70ca", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.80.5", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.80.5", "sass-embedded-linux-x64": "1.80.5", "sass-embedded-win32-x64": "1.80.5", "sass-embedded-darwin-x64": "1.80.5", "sass-embedded-linux-ia32": "1.80.5", "sass-embedded-win32-ia32": "1.80.5", "sass-embedded-android-arm": "1.80.5", "sass-embedded-android-x64": "1.80.5", "sass-embedded-linux-arm64": "1.80.5", "sass-embedded-win32-arm64": "1.80.5", "sass-embedded-android-ia32": "1.80.5", "sass-embedded-darwin-arm64": "1.80.5", "sass-embedded-android-arm64": "1.80.5", "sass-embedded-linux-riscv64": "1.80.5", "sass-embedded-linux-musl-arm": "1.80.5", "sass-embedded-linux-musl-x64": "1.80.5", "sass-embedded-android-riscv64": "1.80.5", "sass-embedded-linux-musl-ia32": "1.80.5", "sass-embedded-linux-musl-arm64": "1.80.5", "sass-embedded-linux-musl-riscv64": "1.80.5"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.80.5_1730240575603_0.17031118055415329", "host": "s3://npm-registry-packages"}}, "1.80.6": {"name": "sass-embedded", "version": "1.80.6", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.80.6", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "efd66c12c7f117c2dcb558e8bf6bd00cc5cd49b2", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.80.6.tgz", "fileCount": 161, "integrity": "sha512-Og4aqBnaA3oJfIpHaLuNATAqzBRgUJDYJy2X15V59cot2wYOtiT/ciPnyuq1o7vpDEeOkHhEd+mSviSlXoETug==", "signatures": [{"sig": "MEQCIHjj9MbDfI7GCc0/u4TbU71YH237HomXHkasb61hZGTRAiAoYY5XrIfsxtGyPguJCbW6m77jLvy1p/OCRWt0V0jO6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 693255}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "350cbc9c59579cdf0f4535010610395e7738c950", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^4.0.0", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^5.0.0", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.80.6", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.80.6", "sass-embedded-linux-x64": "1.80.6", "sass-embedded-win32-x64": "1.80.6", "sass-embedded-darwin-x64": "1.80.6", "sass-embedded-linux-ia32": "1.80.6", "sass-embedded-win32-ia32": "1.80.6", "sass-embedded-android-arm": "1.80.6", "sass-embedded-android-x64": "1.80.6", "sass-embedded-linux-arm64": "1.80.6", "sass-embedded-win32-arm64": "1.80.6", "sass-embedded-android-ia32": "1.80.6", "sass-embedded-darwin-arm64": "1.80.6", "sass-embedded-android-arm64": "1.80.6", "sass-embedded-linux-riscv64": "1.80.6", "sass-embedded-linux-musl-arm": "1.80.6", "sass-embedded-linux-musl-x64": "1.80.6", "sass-embedded-android-riscv64": "1.80.6", "sass-embedded-linux-musl-ia32": "1.80.6", "sass-embedded-linux-musl-arm64": "1.80.6", "sass-embedded-linux-musl-riscv64": "1.80.6"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.80.6_1730504187037_0.7242868848834765", "host": "s3://npm-registry-packages"}}, "1.80.7": {"name": "sass-embedded", "version": "1.80.7", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.80.7", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "68c51318dd6fba5d802c80de8c1df20c0f8ea966", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.80.7.tgz", "fileCount": 154, "integrity": "sha512-OwF0QvpDUjW2udPCvxgaObU0tQHycpsIgCDtHBVHuOqZ2LN0OkkY+uxSO7bOaw9wD7vXtt+1V+jiIZDTxiSRVQ==", "signatures": [{"sig": "MEUCIQDZH39lPCOWAkxgf7TGpiXJSGIb+eYtdQyyy1o0cQpSLQIgJrPxjmYBFYF0hViMsw0w7MNj+3EEZLcbCM34Hj5Sh/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 793109}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "a884d81e154db2e507593aa91a878cd666a85073", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.80.7", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.80.7", "sass-embedded-linux-x64": "1.80.7", "sass-embedded-win32-x64": "1.80.7", "sass-embedded-darwin-x64": "1.80.7", "sass-embedded-linux-ia32": "1.80.7", "sass-embedded-win32-ia32": "1.80.7", "sass-embedded-android-arm": "1.80.7", "sass-embedded-android-x64": "1.80.7", "sass-embedded-linux-arm64": "1.80.7", "sass-embedded-win32-arm64": "1.80.7", "sass-embedded-android-ia32": "1.80.7", "sass-embedded-darwin-arm64": "1.80.7", "sass-embedded-android-arm64": "1.80.7", "sass-embedded-linux-riscv64": "1.80.7", "sass-embedded-linux-musl-arm": "1.80.7", "sass-embedded-linux-musl-x64": "1.80.7", "sass-embedded-android-riscv64": "1.80.7", "sass-embedded-linux-musl-ia32": "1.80.7", "sass-embedded-linux-musl-arm64": "1.80.7", "sass-embedded-linux-musl-riscv64": "1.80.7"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.80.7_1731454925410_0.20168468163973974", "host": "s3://npm-registry-packages"}}, "1.81.0": {"name": "sass-embedded", "version": "1.81.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.81.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "91d04fc1e21aa32e35daff69ce0db2dfcd1652a1", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.81.0.tgz", "fileCount": 154, "integrity": "sha512-uZQ2Faxb1oWBHpeSSzjxnhClbMb3QadN0ql0ZFNuqWOLUxwaVhrMlMhPq6TDPbbfDUjihuwrMCuy695Bgna5RA==", "signatures": [{"sig": "MEQCIC+0oQxsBcchlVWPt0QeDS0/f6X05z7yStFkHIc1NaInAiBfoL4Pmlu8HQCKL6dy7o+BXt6r0mW80ofWLCeEmoHNTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 793109}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "5b974cf31ca67de10dc136b10b3ef183713c7de1", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.81.0", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.81.0", "sass-embedded-linux-x64": "1.81.0", "sass-embedded-win32-x64": "1.81.0", "sass-embedded-darwin-x64": "1.81.0", "sass-embedded-linux-ia32": "1.81.0", "sass-embedded-win32-ia32": "1.81.0", "sass-embedded-android-arm": "1.81.0", "sass-embedded-android-x64": "1.81.0", "sass-embedded-linux-arm64": "1.81.0", "sass-embedded-win32-arm64": "1.81.0", "sass-embedded-android-ia32": "1.81.0", "sass-embedded-darwin-arm64": "1.81.0", "sass-embedded-android-arm64": "1.81.0", "sass-embedded-linux-riscv64": "1.81.0", "sass-embedded-linux-musl-arm": "1.81.0", "sass-embedded-linux-musl-x64": "1.81.0", "sass-embedded-android-riscv64": "1.81.0", "sass-embedded-linux-musl-ia32": "1.81.0", "sass-embedded-linux-musl-arm64": "1.81.0", "sass-embedded-linux-musl-riscv64": "1.81.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.81.0_1731635454377_0.8625345341197199", "host": "s3://npm-registry-packages"}}, "1.81.1": {"name": "sass-embedded", "version": "1.81.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.81.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "f796d4b8100387b6a3ec64cc1a1d47f3aec9370e", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.81.1.tgz", "fileCount": 154, "integrity": "sha512-T9QS45YmdllHp2D/Xmdjy9s5hpP95QSlvrVAhyX9cxSAi88IU5/nHTs+6yHjfSKwJdUpSaUmcCUkZgEDriW16Q==", "signatures": [{"sig": "MEUCIQCP9oV5WFfHJZsC5b+oxtimnDQag2Qu7yoDKfvl/rc9lwIgN3sC/d0TWl53w7PcAkQb2RFy0meTnysA300cFXmePTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 793287}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "e9876a654a874658da3504e6560f3cf01f2fc452", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.81.1", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.81.1", "sass-embedded-linux-x64": "1.81.1", "sass-embedded-win32-x64": "1.81.1", "sass-embedded-darwin-x64": "1.81.1", "sass-embedded-linux-ia32": "1.81.1", "sass-embedded-win32-ia32": "1.81.1", "sass-embedded-android-arm": "1.81.1", "sass-embedded-android-x64": "1.81.1", "sass-embedded-linux-arm64": "1.81.1", "sass-embedded-win32-arm64": "1.81.1", "sass-embedded-android-ia32": "1.81.1", "sass-embedded-darwin-arm64": "1.81.1", "sass-embedded-android-arm64": "1.81.1", "sass-embedded-linux-riscv64": "1.81.1", "sass-embedded-linux-musl-arm": "1.81.1", "sass-embedded-linux-musl-x64": "1.81.1", "sass-embedded-android-riscv64": "1.81.1", "sass-embedded-linux-musl-ia32": "1.81.1", "sass-embedded-linux-musl-arm64": "1.81.1", "sass-embedded-linux-musl-riscv64": "1.81.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.81.1_1733190333470_0.2889263445809074", "host": "s3://npm-registry-packages"}}, "1.82.0": {"name": "sass-embedded", "version": "1.82.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.82.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "2fe1f592840458d08c0b6d4893f8af6cdad7b379", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.82.0.tgz", "fileCount": 154, "integrity": "sha512-v13sRVVZtWAQLpAGTz5D8hy+oyNKRHao5tKVc/P6AMqSP+jDM8X6GkEpL0jfbu3MaN2/hAQsd4Qx14GG1u0prQ==", "signatures": [{"sig": "MEUCIBjiHgtjLRVUbzAKAsO0jCCctOX0RdhWOpgsrWw3mQaHAiEAwR9bq5XEBemJWl/9Pk5POZNCflvU+fwyz9G/d4Ew6/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 793287}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "ebeb169c4385d7882ee28bb0677bc8e0ecbc0135", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.82.0", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.82.0", "sass-embedded-linux-x64": "1.82.0", "sass-embedded-win32-x64": "1.82.0", "sass-embedded-darwin-x64": "1.82.0", "sass-embedded-linux-ia32": "1.82.0", "sass-embedded-win32-ia32": "1.82.0", "sass-embedded-android-arm": "1.82.0", "sass-embedded-android-x64": "1.82.0", "sass-embedded-linux-arm64": "1.82.0", "sass-embedded-win32-arm64": "1.82.0", "sass-embedded-android-ia32": "1.82.0", "sass-embedded-darwin-arm64": "1.82.0", "sass-embedded-android-arm64": "1.82.0", "sass-embedded-linux-riscv64": "1.82.0", "sass-embedded-linux-musl-arm": "1.82.0", "sass-embedded-linux-musl-x64": "1.82.0", "sass-embedded-android-riscv64": "1.82.0", "sass-embedded-linux-musl-ia32": "1.82.0", "sass-embedded-linux-musl-arm64": "1.82.0", "sass-embedded-linux-musl-riscv64": "1.82.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.82.0_1733268367882_0.8349519374227463", "host": "s3://npm-registry-packages"}}, "1.83.0": {"name": "sass-embedded", "version": "1.83.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.83.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "e2400eaf7432dea61bac61a924c0b02099fec954", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.83.0.tgz", "fileCount": 154, "integrity": "sha512-/8cYZeL39evUqe0o//193na51Q1VWZ61qhxioQvLJwOtWIrX+PgNhCyD8RSuTtmzc4+6+waFZf899bfp/MCUwA==", "signatures": [{"sig": "MEUCIQDYuoDrI4gO8MA3UQaRcrQGbmdk+OWSkw/dV0MbqeH2LgIgZ0cRzZIe3vB4MvJRbFviBZKfALH4UT2PGblvKZe7Lok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 793941}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "1cc223b0755fb618d6b060cdc3a084e4916cf699", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.83.0", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.83.0", "sass-embedded-linux-x64": "1.83.0", "sass-embedded-win32-x64": "1.83.0", "sass-embedded-darwin-x64": "1.83.0", "sass-embedded-linux-ia32": "1.83.0", "sass-embedded-win32-ia32": "1.83.0", "sass-embedded-android-arm": "1.83.0", "sass-embedded-android-x64": "1.83.0", "sass-embedded-linux-arm64": "1.83.0", "sass-embedded-win32-arm64": "1.83.0", "sass-embedded-android-ia32": "1.83.0", "sass-embedded-darwin-arm64": "1.83.0", "sass-embedded-android-arm64": "1.83.0", "sass-embedded-linux-riscv64": "1.83.0", "sass-embedded-linux-musl-arm": "1.83.0", "sass-embedded-linux-musl-x64": "1.83.0", "sass-embedded-android-riscv64": "1.83.0", "sass-embedded-linux-musl-ia32": "1.83.0", "sass-embedded-linux-musl-arm64": "1.83.0", "sass-embedded-linux-musl-riscv64": "1.83.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.83.0_1734043933391_0.242027718652573", "host": "s3://npm-registry-packages-npm-production"}}, "1.83.1": {"name": "sass-embedded", "version": "1.83.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.83.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "baee74a20688db6b4ec521f3be535f607147765e", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.83.1.tgz", "fileCount": 154, "integrity": "sha512-LdKG6nxLEzpXbMUt0if12PhUNonGvy91n7IWHOZRZjvA6AWm9oVdhpO+KEXN/Sc+jjGvQeQcav9+Z8DwmII/pA==", "signatures": [{"sig": "MEUCIETm1YBDCZOS9Q52oDXOV+hhyYn2PICdTqIz/eoJ3yS6AiEA42JNIUcRZUT0aGmp7SNTXAF2WAl/kRV7xn1QF+mAf58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 793925}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "7a78715426ab22df8a377e558c0329f6f816f8bb", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.0.0", "gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "minipass": "7.1.2", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.83.1", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.83.1", "sass-embedded-linux-x64": "1.83.1", "sass-embedded-win32-x64": "1.83.1", "sass-embedded-darwin-x64": "1.83.1", "sass-embedded-linux-ia32": "1.83.1", "sass-embedded-win32-ia32": "1.83.1", "sass-embedded-android-arm": "1.83.1", "sass-embedded-android-x64": "1.83.1", "sass-embedded-linux-arm64": "1.83.1", "sass-embedded-win32-arm64": "1.83.1", "sass-embedded-android-ia32": "1.83.1", "sass-embedded-darwin-arm64": "1.83.1", "sass-embedded-android-arm64": "1.83.1", "sass-embedded-linux-riscv64": "1.83.1", "sass-embedded-linux-musl-arm": "1.83.1", "sass-embedded-linux-musl-x64": "1.83.1", "sass-embedded-android-riscv64": "1.83.1", "sass-embedded-linux-musl-ia32": "1.83.1", "sass-embedded-linux-musl-arm64": "1.83.1", "sass-embedded-linux-musl-riscv64": "1.83.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.83.1_1735955131840_0.034545849576150145", "host": "s3://npm-registry-packages-npm-production"}}, "1.83.2": {"name": "sass-embedded", "version": "1.83.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.83.2", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "57ea2a009da4bb7af98e3701a634e5aefa33c7f5", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.83.2.tgz", "fileCount": 154, "integrity": "sha512-oDRM+5Mw3tFfQi/SXx1Lfx+5qa2oEh4J8h3c7mK3W4mhvgoD1od8ZK0aov5NbzjFbj4XL1U5f30SoNw+/RqrKw==", "signatures": [{"sig": "MEUCIH2WwEqefzgVbCQ0SruepQpkQjp5lIQoyzRSeRSFtP+ZAiEAm4WZk8njgH+nd/zdXqs4Ja+P7rGhdtPiUZ/Pjhfo3ZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 789804}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "1bc194248f649a2153ec6b223eface45db4dd4e1", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.83.2", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.83.2", "sass-embedded-linux-x64": "1.83.2", "sass-embedded-win32-x64": "1.83.2", "sass-embedded-darwin-x64": "1.83.2", "sass-embedded-linux-ia32": "1.83.2", "sass-embedded-win32-ia32": "1.83.2", "sass-embedded-android-arm": "1.83.2", "sass-embedded-android-x64": "1.83.2", "sass-embedded-linux-arm64": "1.83.2", "sass-embedded-win32-arm64": "1.83.2", "sass-embedded-android-ia32": "1.83.2", "sass-embedded-darwin-arm64": "1.83.2", "sass-embedded-android-arm64": "1.83.2", "sass-embedded-linux-riscv64": "1.83.2", "sass-embedded-linux-musl-arm": "1.83.2", "sass-embedded-linux-musl-x64": "1.83.2", "sass-embedded-android-riscv64": "1.83.2", "sass-embedded-linux-musl-ia32": "1.83.2", "sass-embedded-linux-musl-arm64": "1.83.2", "sass-embedded-linux-musl-riscv64": "1.83.2"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.83.2_1736816960256_0.18674951800483064", "host": "s3://npm-registry-packages-npm-production"}}, "1.83.3": {"name": "sass-embedded", "version": "1.83.3", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.83.3", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "22754f8dfa1f22fe8b1d56e1d8c8743b8a90f75f", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.83.3.tgz", "fileCount": 154, "integrity": "sha512-KNS8tP8XX4O0e/IJoergJCExoOJdEdI++I1+IPO3njRU7mx14Z6eXFz/d+LD2mmh8//ti9pvfDO/XZKxUHK2Ow==", "signatures": [{"sig": "MEUCIAGsntSDzo6zqD4XEtA38Vh1HaoE/eolfwbAtToL1dBnAiEAta03+3JGCqyRQq2O2hrgrZHkOjhASBgJkPXrVTsLrMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 789804}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "28d64e6ce63eab494c9a9be26626564f92424fe1", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.83.3", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.83.3", "sass-embedded-linux-x64": "1.83.3", "sass-embedded-win32-x64": "1.83.3", "sass-embedded-darwin-x64": "1.83.3", "sass-embedded-linux-ia32": "1.83.3", "sass-embedded-win32-ia32": "1.83.3", "sass-embedded-android-arm": "1.83.3", "sass-embedded-android-x64": "1.83.3", "sass-embedded-linux-arm64": "1.83.3", "sass-embedded-win32-arm64": "1.83.3", "sass-embedded-android-ia32": "1.83.3", "sass-embedded-darwin-arm64": "1.83.3", "sass-embedded-android-arm64": "1.83.3", "sass-embedded-linux-riscv64": "1.83.3", "sass-embedded-linux-musl-arm": "1.83.3", "sass-embedded-linux-musl-x64": "1.83.3", "sass-embedded-android-riscv64": "1.83.3", "sass-embedded-linux-musl-ia32": "1.83.3", "sass-embedded-linux-musl-arm64": "1.83.3", "sass-embedded-linux-musl-riscv64": "1.83.3"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.83.3_1736820015353_0.21335165090425745", "host": "s3://npm-registry-packages-npm-production"}}, "1.83.4": {"name": "sass-embedded", "version": "1.83.4", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.83.4", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "9b05cdc22ae71a1b27b5996a39054ba59bebc04a", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.83.4.tgz", "fileCount": 154, "integrity": "sha512-Hf2burRA/y5PGxsg6jB9UpoK/xZ6g/pgrkOcdl6j+rRg1Zj8XhGKZ1MTysZGtTPUUmiiErqzkP5+Kzp95yv9GQ==", "signatures": [{"sig": "MEYCIQD7EBxMofHvrEp72Sj1+DP3t5OXnwA2zeWQxubaRuQ1+gIhAI/cdaDrRTlqhnQeLnlR/s1hizN0qAB4YtaUsE0KvL4h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 789804}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "2b3c83f2ade011560f74c1414f794ad9fd4da94a", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.83.4", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.83.4", "sass-embedded-linux-x64": "1.83.4", "sass-embedded-win32-x64": "1.83.4", "sass-embedded-darwin-x64": "1.83.4", "sass-embedded-linux-ia32": "1.83.4", "sass-embedded-win32-ia32": "1.83.4", "sass-embedded-android-arm": "1.83.4", "sass-embedded-android-x64": "1.83.4", "sass-embedded-linux-arm64": "1.83.4", "sass-embedded-win32-arm64": "1.83.4", "sass-embedded-android-ia32": "1.83.4", "sass-embedded-darwin-arm64": "1.83.4", "sass-embedded-android-arm64": "1.83.4", "sass-embedded-linux-riscv64": "1.83.4", "sass-embedded-linux-musl-arm": "1.83.4", "sass-embedded-linux-musl-x64": "1.83.4", "sass-embedded-android-riscv64": "1.83.4", "sass-embedded-linux-musl-ia32": "1.83.4", "sass-embedded-linux-musl-arm64": "1.83.4", "sass-embedded-linux-musl-riscv64": "1.83.4"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.83.4_1736837484867_0.7159926371136527", "host": "s3://npm-registry-packages-npm-production"}}, "1.85.0": {"name": "sass-embedded", "version": "1.85.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.85.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "a491fe3f1b90440d6be3a2459520f2e7c67a40cd", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.85.0.tgz", "fileCount": 154, "integrity": "sha512-x3Vv54g0jv1aPSW8OTA/0GzQCs/HMQOjIkLtZJ3Xsn/I4vnyjKbVTQmFTax9bQjldqLEEkdbvy6ES/cOOnYNwA==", "signatures": [{"sig": "MEUCIDoEn5tSx3PIW9Y1hp/aK5SAEJttQ2hwX+p8jb5igTzbAiEAxLqdDCkAyxbjLsIsVWDTZDaRbRuPg3eyt41+htkWK50=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 790454}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "3b72e22b6b3392c213e868010efe3cd8fc17895c", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.85.0", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.85.0", "sass-embedded-linux-x64": "1.85.0", "sass-embedded-win32-x64": "1.85.0", "sass-embedded-darwin-x64": "1.85.0", "sass-embedded-linux-ia32": "1.85.0", "sass-embedded-win32-ia32": "1.85.0", "sass-embedded-android-arm": "1.85.0", "sass-embedded-android-x64": "1.85.0", "sass-embedded-linux-arm64": "1.85.0", "sass-embedded-win32-arm64": "1.85.0", "sass-embedded-android-ia32": "1.85.0", "sass-embedded-darwin-arm64": "1.85.0", "sass-embedded-android-arm64": "1.85.0", "sass-embedded-linux-riscv64": "1.85.0", "sass-embedded-linux-musl-arm": "1.85.0", "sass-embedded-linux-musl-x64": "1.85.0", "sass-embedded-android-riscv64": "1.85.0", "sass-embedded-linux-musl-ia32": "1.85.0", "sass-embedded-linux-musl-arm64": "1.85.0", "sass-embedded-linux-musl-riscv64": "1.85.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.85.0_1739494449878_0.45652370687132104", "host": "s3://npm-registry-packages-npm-production"}}, "1.85.1": {"name": "sass-embedded", "version": "1.85.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.85.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "868cdf067a3b515eb72cb0039e61e72c4c56c856", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.85.1.tgz", "fileCount": 154, "integrity": "sha512-0i+3h2Df/c71afluxC1SXqyyMmJlnKWfu9ZGdzwuKRM1OftEa2XM2myt5tR36CF3PanYrMjFKtRIj8PfSf838w==", "signatures": [{"sig": "MEYCIQC0yxYsRZyImcEtrRXxPjyyoahhEtCSShvvluae97fN8QIhAI/EK0mb795V3gnmdaBMyqX/aJXgXKwhCWiZCEOMmGuT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 790305}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "01b4df1ed85c014164216737ab5bc8855c52731c", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.8.4", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.85.1", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.85.1", "sass-embedded-linux-x64": "1.85.1", "sass-embedded-win32-x64": "1.85.1", "sass-embedded-darwin-x64": "1.85.1", "sass-embedded-linux-ia32": "1.85.1", "sass-embedded-win32-ia32": "1.85.1", "sass-embedded-android-arm": "1.85.1", "sass-embedded-android-x64": "1.85.1", "sass-embedded-linux-arm64": "1.85.1", "sass-embedded-win32-arm64": "1.85.1", "sass-embedded-android-ia32": "1.85.1", "sass-embedded-darwin-arm64": "1.85.1", "sass-embedded-android-arm64": "1.85.1", "sass-embedded-linux-riscv64": "1.85.1", "sass-embedded-linux-musl-arm": "1.85.1", "sass-embedded-linux-musl-x64": "1.85.1", "sass-embedded-android-riscv64": "1.85.1", "sass-embedded-linux-musl-ia32": "1.85.1", "sass-embedded-linux-musl-arm64": "1.85.1", "sass-embedded-linux-musl-riscv64": "1.85.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.85.1_1740447660349_0.1023008785859254", "host": "s3://npm-registry-packages-npm-production"}}, "1.86.0": {"name": "sass-embedded", "version": "1.86.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.86.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "c05c6141f1aea407f9a4eea78790209fdc74f0df", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.86.0.tgz", "fileCount": 154, "integrity": "sha512-Ibq5DzxjSf9f/IJmKeHVeXlVqiZWdRJF+RXy6v6UupvMYVMU5Ei+teSFBvvpPD5bB2QhhnU/OJlSM0EBCtfr9g==", "signatures": [{"sig": "MEYCIQCrjcUY2HhpN+zGhJ5FcS0J8iMqmzZDDbGn0UgFjwHTjAIhAOymeRA7KKVCNqYPWg4fExCjDvE38Ug3lvFCLqaNIxmf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 790713}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "65ed46a2b4e1ad6a8f4ba2791a2bb4babf1837e2", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.9.1", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.86.0", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.86.0", "sass-embedded-linux-x64": "1.86.0", "sass-embedded-win32-x64": "1.86.0", "sass-embedded-darwin-x64": "1.86.0", "sass-embedded-linux-ia32": "1.86.0", "sass-embedded-win32-ia32": "1.86.0", "sass-embedded-android-arm": "1.86.0", "sass-embedded-android-x64": "1.86.0", "sass-embedded-linux-arm64": "1.86.0", "sass-embedded-win32-arm64": "1.86.0", "sass-embedded-android-ia32": "1.86.0", "sass-embedded-darwin-arm64": "1.86.0", "sass-embedded-android-arm64": "1.86.0", "sass-embedded-linux-riscv64": "1.86.0", "sass-embedded-linux-musl-arm": "1.86.0", "sass-embedded-linux-musl-x64": "1.86.0", "sass-embedded-android-riscv64": "1.86.0", "sass-embedded-linux-musl-ia32": "1.86.0", "sass-embedded-linux-musl-arm64": "1.86.0", "sass-embedded-linux-musl-riscv64": "1.86.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.86.0_1742249431391_0.6011621339697519", "host": "s3://npm-registry-packages-npm-production"}}, "1.86.1": {"name": "sass-embedded", "version": "1.86.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.86.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "970e7221ae95e2706907fff4975ac33b949c26e2", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.86.1.tgz", "fileCount": 154, "integrity": "sha512-LMJvytHh7lIUtmjGCqpM4cRdIDvPllLJKznNIK4L7EZJ77BLeUFoOSRXEOHq4G4gqy5CVhHUKlHslzCANkDOhQ==", "signatures": [{"sig": "MEYCIQDjRR81X6OgVc678FnnRh05qt3t/mWoBqpBM6KDQ74uOQIhAO8O3UEItURxBVEuOu16JOSg5Ce2VZhzeeXYDHk89VEV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 790713}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "350fe6f468da438cf4a97212adb5ace9c2a82d38", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.9.1", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.86.1", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.86.1", "sass-embedded-linux-x64": "1.86.1", "sass-embedded-win32-x64": "1.86.1", "sass-embedded-darwin-x64": "1.86.1", "sass-embedded-linux-ia32": "1.86.1", "sass-embedded-win32-ia32": "1.86.1", "sass-embedded-android-arm": "1.86.1", "sass-embedded-android-x64": "1.86.1", "sass-embedded-linux-arm64": "1.86.1", "sass-embedded-win32-arm64": "1.86.1", "sass-embedded-android-ia32": "1.86.1", "sass-embedded-darwin-arm64": "1.86.1", "sass-embedded-android-arm64": "1.86.1", "sass-embedded-linux-riscv64": "1.86.1", "sass-embedded-linux-musl-arm": "1.86.1", "sass-embedded-linux-musl-x64": "1.86.1", "sass-embedded-android-riscv64": "1.86.1", "sass-embedded-linux-musl-ia32": "1.86.1", "sass-embedded-linux-musl-arm64": "1.86.1", "sass-embedded-linux-musl-riscv64": "1.86.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.86.1_1743456118068_0.6192176454497345", "host": "s3://npm-registry-packages-npm-production"}}, "1.86.2": {"name": "sass-embedded", "version": "1.86.2", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.86.2", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "50239e02bddcc750c33132efbb8ee6c014248769", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.86.2.tgz", "fileCount": 154, "integrity": "sha512-ER9yUk71007a+6azLBR0RzA4Vd4VtXpaRpI+HXqpEIARhleTKYUxXrh6nY+272q91xAzoXqBKVlTizOvNmb5yQ==", "signatures": [{"sig": "MEUCIQCuXF7sr90SNrMboaOu8C85zYG6wJngbGUj4cEw8tTADQIgGSxrVNwWmrrbwbMZoQigL7QuW6BiWFgQTQ/oQx3hkm0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 790713}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "23a3127894fca8e283e9242087548f0cec8010d6", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.9.1", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.86.2", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.86.2", "sass-embedded-linux-x64": "1.86.2", "sass-embedded-win32-x64": "1.86.2", "sass-embedded-darwin-x64": "1.86.2", "sass-embedded-linux-ia32": "1.86.2", "sass-embedded-win32-ia32": "1.86.2", "sass-embedded-android-arm": "1.86.2", "sass-embedded-android-x64": "1.86.2", "sass-embedded-linux-arm64": "1.86.2", "sass-embedded-win32-arm64": "1.86.2", "sass-embedded-android-ia32": "1.86.2", "sass-embedded-darwin-arm64": "1.86.2", "sass-embedded-android-arm64": "1.86.2", "sass-embedded-linux-riscv64": "1.86.2", "sass-embedded-linux-musl-arm": "1.86.2", "sass-embedded-linux-musl-x64": "1.86.2", "sass-embedded-android-riscv64": "1.86.2", "sass-embedded-linux-musl-ia32": "1.86.2", "sass-embedded-linux-musl-arm64": "1.86.2", "sass-embedded-linux-musl-riscv64": "1.86.2"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.86.2_1743636638205_0.4029152206905715", "host": "s3://npm-registry-packages-npm-production"}}, "1.86.3": {"name": "sass-embedded", "version": "1.86.3", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.86.3", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "33358bfc13108c5b59b9904fb55ed56773b73037", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.86.3.tgz", "fileCount": 154, "integrity": "sha512-3pZSp24ibO1hdopj+W9DuiWsZOb2YY6AFRo/jjutKLBkqJGM1nJjXzhAYfzRV+Xn5BX1eTI4bBTE09P0XNHOZg==", "signatures": [{"sig": "MEUCIQCq1FI+CCWgLXpwgjL07E3tl4mqN+hPg+xmDVGs2rGJwQIgG3FsRYMfWOn9veH8UomiyQRvNH/GGZQvPKhb+rPWDY4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 790713}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "9678d15d931a957a4de241b8554cb904de89120e", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.9.1", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.86.3", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.86.3", "sass-embedded-linux-x64": "1.86.3", "sass-embedded-win32-x64": "1.86.3", "sass-embedded-darwin-x64": "1.86.3", "sass-embedded-linux-ia32": "1.86.3", "sass-embedded-win32-ia32": "1.86.3", "sass-embedded-android-arm": "1.86.3", "sass-embedded-android-x64": "1.86.3", "sass-embedded-linux-arm64": "1.86.3", "sass-embedded-win32-arm64": "1.86.3", "sass-embedded-android-ia32": "1.86.3", "sass-embedded-darwin-arm64": "1.86.3", "sass-embedded-android-arm64": "1.86.3", "sass-embedded-linux-riscv64": "1.86.3", "sass-embedded-linux-musl-arm": "1.86.3", "sass-embedded-linux-musl-x64": "1.86.3", "sass-embedded-android-riscv64": "1.86.3", "sass-embedded-linux-musl-ia32": "1.86.3", "sass-embedded-linux-musl-arm64": "1.86.3", "sass-embedded-linux-musl-riscv64": "1.86.3"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.86.3_1743726700258_0.6489363593377213", "host": "s3://npm-registry-packages-npm-production"}}, "1.87.0": {"name": "sass-embedded", "version": "1.87.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.87.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "0e784c398bc67402b17c5ff78981a56586d00ada", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.87.0.tgz", "fileCount": 154, "integrity": "sha512-1IA3iTJNh4BkkA/nidKiVwbmkxr9o6LsPegycHMX/JYs255zpocN5GdLF1+onohQCJxbs5ldr8osKV7qNaNBjg==", "signatures": [{"sig": "MEUCIQC5bDRXo6VyaSvwT9OETFIHyk80IDguoZ8J1Er8EfLPUwIgfBO3C/D2HhR+WaEAjOlf2eA0JOOQTNhqv2uTAagFc9w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 790713}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "8f882dd34158651801af330fcc616c88619e157c", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^6.0.5", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.9.1", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.87.0", "protocol-version": "3.1.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.87.0", "sass-embedded-linux-x64": "1.87.0", "sass-embedded-win32-x64": "1.87.0", "sass-embedded-darwin-x64": "1.87.0", "sass-embedded-linux-ia32": "1.87.0", "sass-embedded-win32-ia32": "1.87.0", "sass-embedded-android-arm": "1.87.0", "sass-embedded-android-x64": "1.87.0", "sass-embedded-linux-arm64": "1.87.0", "sass-embedded-win32-arm64": "1.87.0", "sass-embedded-android-ia32": "1.87.0", "sass-embedded-darwin-arm64": "1.87.0", "sass-embedded-android-arm64": "1.87.0", "sass-embedded-linux-riscv64": "1.87.0", "sass-embedded-linux-musl-arm": "1.87.0", "sass-embedded-linux-musl-x64": "1.87.0", "sass-embedded-android-riscv64": "1.87.0", "sass-embedded-linux-musl-ia32": "1.87.0", "sass-embedded-linux-musl-arm64": "1.87.0", "sass-embedded-linux-musl-riscv64": "1.87.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.87.0_1745277607562_0.33737681527005337", "host": "s3://npm-registry-packages-npm-production"}}, "1.88.0": {"name": "sass-embedded", "version": "1.88.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.88.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "a3562d46a040efc6629c8d89f5264cecd7ccdd7d", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.88.0.tgz", "fileCount": 154, "integrity": "sha512-GQUxgZFuej3NZ1TSPUHU8aebtYdnIeXqYsbNEEKBtE+SC7/Gr18KH1ijTAZHPw25OUfQCdtJaRy6Fo866dHmgw==", "signatures": [{"sig": "MEYCIQCApIIVAlobJzX3zD8Ns80TWyI/hex9CorhJTyGrULNmQIhAKuPKutuUddBkV1AfbHTIb/mBC1v3yG7nt0Vn4TBjbUn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 800834}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "ba6e09f4b0ef4c571e978d56644be1976701af39", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.15.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^7.4.3", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.9.1", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.88.0", "protocol-version": "3.2.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.88.0", "sass-embedded-linux-x64": "1.88.0", "sass-embedded-win32-x64": "1.88.0", "sass-embedded-darwin-x64": "1.88.0", "sass-embedded-linux-ia32": "1.88.0", "sass-embedded-win32-ia32": "1.88.0", "sass-embedded-android-arm": "1.88.0", "sass-embedded-android-x64": "1.88.0", "sass-embedded-linux-arm64": "1.88.0", "sass-embedded-win32-arm64": "1.88.0", "sass-embedded-android-ia32": "1.88.0", "sass-embedded-darwin-arm64": "1.88.0", "sass-embedded-android-arm64": "1.88.0", "sass-embedded-linux-riscv64": "1.88.0", "sass-embedded-linux-musl-arm": "1.88.0", "sass-embedded-linux-musl-x64": "1.88.0", "sass-embedded-android-riscv64": "1.88.0", "sass-embedded-linux-musl-ia32": "1.88.0", "sass-embedded-linux-musl-arm64": "1.88.0", "sass-embedded-linux-musl-riscv64": "1.88.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.88.0_1746917358453_0.08330156720584392", "host": "s3://npm-registry-packages-npm-production"}}, "1.89.0": {"name": "sass-embedded", "version": "1.89.0", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.89.0", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "042575f94364ff4b7b142f8e2be003f348b62b86", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.89.0.tgz", "fileCount": 154, "integrity": "sha512-EDrK1el9zdgJFpocCGlxatDWaP18tJBWoM1hxzo2KJBvjdmBichXI6O6KlQrigvQPO3uJ8DfmFmAAx7s7CG6uw==", "signatures": [{"sig": "MEQCIHCDH0ZEyrOOX/gEMH4+nXH3WDv0woNODVIVJLZjoXssAiB3whGpA42FiIqt4TJFoAr9+7ELILcOuqLorSS2cJwbHw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 800836}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "f2e50b2f9ad3b953937dfdcd09dcd6a8edd26a62", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.15.1", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^7.4.3", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.10.0", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.89.0", "protocol-version": "3.2.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.89.0", "sass-embedded-linux-x64": "1.89.0", "sass-embedded-win32-x64": "1.89.0", "sass-embedded-darwin-x64": "1.89.0", "sass-embedded-linux-ia32": "1.89.0", "sass-embedded-win32-ia32": "1.89.0", "sass-embedded-android-arm": "1.89.0", "sass-embedded-android-x64": "1.89.0", "sass-embedded-linux-arm64": "1.89.0", "sass-embedded-win32-arm64": "1.89.0", "sass-embedded-android-ia32": "1.89.0", "sass-embedded-darwin-arm64": "1.89.0", "sass-embedded-android-arm64": "1.89.0", "sass-embedded-linux-riscv64": "1.89.0", "sass-embedded-linux-musl-arm": "1.89.0", "sass-embedded-linux-musl-x64": "1.89.0", "sass-embedded-android-riscv64": "1.89.0", "sass-embedded-linux-musl-ia32": "1.89.0", "sass-embedded-linux-musl-arm64": "1.89.0", "sass-embedded-linux-musl-riscv64": "1.89.0"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.89.0_1747357573406_0.1775158325793844", "host": "s3://npm-registry-packages-npm-production"}}, "1.89.1": {"name": "sass-embedded", "version": "1.89.1", "author": {"name": "Google Inc."}, "license": "MIT", "_id": "sass-embedded@1.89.1", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "homepage": "https://github.com/sass/embedded-host-node#readme", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "bin": {"sass": "dist/bin/sass.js"}, "dist": {"shasum": "5a9c0ba1450bbfe4d8ff0c1a2d1ca13a772cc1e4", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.89.1.tgz", "fileCount": 154, "integrity": "sha512-alvGGlyYdkSXYKOfS/TTxUD0993EYOe3adIPtwCWEg037qe183p2dkYnbaRsCLJFKt+QoyRzhsrbCsK7sbR6MA==", "signatures": [{"sig": "MEUCIFxOUR8ZP0HKr1M/HwerQdHfSfRKv0Cs8bJj+k/PhgrgAiEA6BS47A/PMmmh5uEwYkwtfNRdWeVIwMj8QOsj/rAuWhU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 800437}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "exports": {"types": "./dist/types/index.d.ts", "import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "default": "./dist/lib/index.js"}, "gitHead": "4eb752e53872dc785d8df5871b9c128bb2b262d4", "scripts": {"fix": "gts fix", "init": "ts-node ./tool/init.ts", "test": "jest", "check": "npm-run-all check:gts check:tsc", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts"}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sass/embedded-host-node.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "directories": {}, "_nodeVersion": "22.16.0", "dependencies": {"rxjs": "^7.4.0", "varint": "^6.0.0", "immutable": "^5.0.2", "colorjs.io": "^0.5.0", "buffer-builder": "^0.2.0", "supports-color": "^8.1.1", "@bufbuild/protobuf": "^2.0.0", "sync-child-process": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gts": "^6.0.2", "tar": "^7.4.3", "jest": "^29.4.1", "yaml": "^2.2.1", "yargs": "^17.2.1", "shelljs": "^0.10.0", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "@types/tar": "^6.1.0", "simple-git": "^3.15.1", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "extract-zip": "^2.0.1", "npm-run-all": "^4.1.5", "@types/yargs": "^17.0.4", "@bufbuild/buf": "^1.39.0", "@types/varint": "^6.0.1", "source-map-js": "^1.0.2", "@types/shelljs": "^0.8.8", "@types/buffer-builder": "^0.2.0", "@types/supports-color": "^8.1.1", "@types/google-protobuf": "^3.7.2", "@bufbuild/protoc-gen-es": "^2.0.0"}, "compiler-version": "1.89.1", "protocol-version": "3.2.0", "optionalDependencies": {"sass-embedded-linux-arm": "1.89.1", "sass-embedded-linux-x64": "1.89.1", "sass-embedded-win32-x64": "1.89.1", "sass-embedded-darwin-x64": "1.89.1", "sass-embedded-android-arm": "1.89.1", "sass-embedded-android-x64": "1.89.1", "sass-embedded-linux-arm64": "1.89.1", "sass-embedded-win32-arm64": "1.89.1", "sass-embedded-darwin-arm64": "1.89.1", "sass-embedded-android-arm64": "1.89.1", "sass-embedded-linux-riscv64": "1.89.1", "sass-embedded-linux-musl-arm": "1.89.1", "sass-embedded-linux-musl-x64": "1.89.1", "sass-embedded-android-riscv64": "1.89.1", "sass-embedded-linux-musl-arm64": "1.89.1", "sass-embedded-linux-musl-riscv64": "1.89.1"}, "_npmOperationalInternal": {"tmp": "tmp/sass-embedded_1.89.1_1748650116596_0.10949959404057208", "host": "s3://npm-registry-packages-npm-production"}}, "1.89.2": {"name": "sass-embedded", "version": "1.89.2", "protocol-version": "3.2.0", "compiler-version": "1.89.2", "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "repository": {"type": "git", "url": "git+https://github.com/sass/embedded-host-node.git"}, "author": {"name": "Google Inc."}, "license": "MIT", "exports": {"import": {"types": "./dist/types/index.m.d.ts", "default": "./dist/lib/index.mjs"}, "types": "./dist/types/index.d.ts", "default": "./dist/lib/index.js"}, "main": "dist/lib/index.js", "types": "dist/types/index.d.ts", "engines": {"node": ">=16.0.0"}, "bin": {"sass": "dist/bin/sass.js"}, "scripts": {"init": "ts-node ./tool/init.ts", "check": "npm-run-all check:gts check:tsc", "check:gts": "gts check", "check:tsc": "tsc --noEmit", "clean": "gts clean", "compile": "tsc -p tsconfig.build.json && cp lib/index.mjs dist/lib/index.mjs && cp -r lib/src/vendor/sass/ dist/lib/src/vendor/sass && cp dist/lib/src/vendor/sass/index.d.ts dist/lib/src/vendor/sass/index.m.d.ts", "fix": "gts fix", "prepublishOnly": "npm run clean && ts-node ./tool/prepare-release.ts", "test": "jest"}, "optionalDependencies": {"sass-embedded-android-arm": "1.89.2", "sass-embedded-android-arm64": "1.89.2", "sass-embedded-android-riscv64": "1.89.2", "sass-embedded-android-x64": "1.89.2", "sass-embedded-darwin-arm64": "1.89.2", "sass-embedded-darwin-x64": "1.89.2", "sass-embedded-linux-arm": "1.89.2", "sass-embedded-linux-arm64": "1.89.2", "sass-embedded-linux-riscv64": "1.89.2", "sass-embedded-linux-x64": "1.89.2", "sass-embedded-linux-musl-arm": "1.89.2", "sass-embedded-linux-musl-arm64": "1.89.2", "sass-embedded-linux-musl-riscv64": "1.89.2", "sass-embedded-linux-musl-x64": "1.89.2", "sass-embedded-win32-arm64": "1.89.2", "sass-embedded-win32-x64": "1.89.2"}, "dependencies": {"@bufbuild/protobuf": "^2.5.0", "buffer-builder": "^0.2.0", "colorjs.io": "^0.5.0", "immutable": "^5.0.2", "rxjs": "^7.4.0", "supports-color": "^8.1.1", "sync-child-process": "^1.0.2", "varint": "^6.0.0"}, "devDependencies": {"@bufbuild/buf": "^1.54.0", "@bufbuild/protoc-gen-es": "^2.5.0", "@types/buffer-builder": "^0.2.0", "@types/google-protobuf": "^3.7.2", "@types/jest": "^29.4.0", "@types/node": "^22.0.0", "@types/shelljs": "^0.8.8", "@types/supports-color": "^8.1.1", "@types/tar": "^6.1.0", "@types/varint": "^6.0.1", "@types/yargs": "^17.0.4", "extract-zip": "^2.0.1", "gts": "^6.0.2", "jest": "^29.4.1", "npm-run-all": "^4.1.5", "shelljs": "^0.10.0", "simple-git": "^3.15.1", "source-map-js": "^1.0.2", "tar": "^7.4.3", "ts-jest": "^29.0.5", "ts-node": "^10.2.1", "typescript": "^5.0.2", "yaml": "^2.2.1", "yargs": "^17.2.1"}, "_id": "sass-embedded@1.89.2", "gitHead": "8a6f9d82b023d4325519dc7bf89fbe3d24e30501", "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "homepage": "https://github.com/sass/embedded-host-node#readme", "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Ack2K8rc57kCFcYlf3HXpZEJFNUX8xd8DILldksREmYXQkRHI879yy8q4mRDJgrojkySMZqmmmW1NxrFxMsYaA==", "shasum": "bf6cd7d6ace06f0430bbf3913c95a8be83367883", "tarball": "https://registry.npmjs.org/sass-embedded/-/sass-embedded-1.89.2.tgz", "fileCount": 154, "unpackedSize": 800437, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFWFpQj9XuDTIRsKQTAeljCgKP4DK1s0jXhoCYIvEcS9AiEAvxm45mPoZ0tmB2Bl6rHlqrPxh9Toz63PuTphn+Mm6UQ="}]}, "_npmUser": {"name": "sassbot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/sass-embedded_1.89.2_1749498140605_0.6142213973815438"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-04-16T16:48:19.985Z", "modified": "2025-06-09T19:42:21.033Z", "1.0.0-beta.0": "2021-04-16T16:48:20.121Z", "1.0.0-beta.1": "2021-04-19T19:02:30.957Z", "1.0.0-beta.3": "2021-08-17T21:54:41.857Z", "1.0.0-beta.4": "2021-09-29T22:08:13.985Z", "1.0.0-beta.5": "2021-11-08T17:55:58.718Z", "1.0.0-beta.7": "2021-12-29T22:59:54.902Z", "1.0.0-beta.8": "2022-01-06T00:26:04.965Z", "1.0.0-rc.1": "2022-01-20T22:36:53.489Z", "1.49.7": "2022-02-01T22:32:58.243Z", "1.49.8": "2022-02-17T21:52:13.162Z", "1.49.9": "2022-02-24T20:55:15.929Z", "1.49.11": "2022-04-01T23:13:40.665Z", "1.50.0": "2022-04-07T02:53:02.236Z", "1.50.1": "2022-04-19T01:19:50.844Z", "1.51.0": "2022-05-12T21:36:42.254Z", "1.52.1": "2022-05-20T22:34:32.387Z", "1.52.2": "2022-06-03T02:53:46.183Z", "1.52.3": "2022-06-09T00:20:31.089Z", "1.53.0": "2022-06-22T20:01:12.753Z", "1.54.2": "2022-08-04T00:18:47.820Z", "1.54.3": "2022-08-04T21:20:13.921Z", "1.54.4": "2022-08-10T01:22:11.871Z", "1.54.8": "2022-08-31T22:21:06.543Z", "1.54.9": "2022-09-07T22:01:57.379Z", "1.55.0": "2022-09-22T23:01:57.618Z", "1.56.1": "2022-11-09T22:21:50.379Z", "1.56.2": "2022-12-08T23:14:34.144Z", "1.57.1": "2022-12-20T00:23:25.058Z", "1.58.2": "2023-02-17T02:10:53.683Z", "1.58.3": "2023-02-18T01:10:09.954Z", "1.59.2": "2023-03-11T01:48:36.163Z", "1.59.3": "2023-03-14T21:50:32.510Z", "1.60.0": "2023-03-23T23:44:45.105Z", "1.61.0": "2023-04-06T22:29:32.110Z", "1.62.0": "2023-04-11T23:25:39.841Z", "1.63.5": "2023-06-21T01:31:53.682Z", "1.63.6": "2023-06-21T22:31:02.499Z", "1.64.0": "2023-07-20T00:35:13.539Z", "1.64.1": "2023-07-22T00:29:06.810Z", "1.64.2": "2023-08-01T00:00:28.117Z", "1.66.0": "2023-08-17T19:51:17.664Z", "1.66.1": "2023-08-18T22:22:59.669Z", "1.67.0": "2023-09-14T01:09:55.706Z", "1.69.1": "2023-10-10T00:22:33.926Z", "1.69.2": "2023-10-10T19:07:33.613Z", "1.69.4": "2023-10-17T22:02:28.354Z", "1.69.5": "2023-10-26T01:18:22.491Z", "1.69.6": "2023-12-29T00:43:18.320Z", "1.69.7": "2024-01-02T22:31:42.585Z", "1.70.0": "2024-01-18T03:20:35.165Z", "1.71.0": "2024-02-16T02:03:53.431Z", "1.71.1": "2024-02-21T02:12:57.090Z", "1.72.0": "2024-03-13T21:23:40.394Z", "1.74.1": "2024-04-04T01:20:46.903Z", "1.75.0": "2024-04-11T23:25:49.868Z", "1.76.0": "2024-04-30T21:50:04.927Z", "1.77.0": "2024-05-07T01:20:37.692Z", "1.77.1": "2024-05-10T23:18:47.069Z", "1.77.2": "2024-05-16T22:33:50.687Z", "1.77.5": "2024-06-12T00:08:06.107Z", "1.77.8": "2024-07-11T20:15:36.780Z", "1.78.0": "2024-09-03T23:16:02.038Z", "1.79.1": "2024-09-18T00:37:32.773Z", "1.79.2": "2024-09-19T23:13:37.283Z", "1.79.3": "2024-09-20T21:24:47.475Z", "1.79.4": "2024-09-28T03:32:16.262Z", "1.79.5": "2024-10-12T00:35:48.575Z", "1.79.6": "2024-10-16T23:39:44.248Z", "1.80.0": "2024-10-17T00:35:58.007Z", "1.80.1": "2024-10-17T03:29:03.793Z", "1.80.2": "2024-10-17T21:04:26.081Z", "1.80.3": "2024-10-19T00:01:45.446Z", "1.80.4": "2024-10-23T23:04:05.345Z", "1.80.5": "2024-10-29T22:22:55.974Z", "1.80.6": "2024-11-01T23:36:27.281Z", "1.80.7": "2024-11-12T23:42:05.820Z", "1.81.0": "2024-11-15T01:50:54.635Z", "1.81.1": "2024-12-03T01:45:33.862Z", "1.82.0": "2024-12-03T23:26:08.110Z", "1.83.0": "2024-12-12T22:52:13.658Z", "1.83.1": "2025-01-04T01:45:32.052Z", "1.83.2": "2025-01-14T01:09:20.512Z", "1.83.3": "2025-01-14T02:00:15.523Z", "1.83.4": "2025-01-14T06:51:25.071Z", "1.85.0": "2025-02-14T00:54:10.189Z", "1.85.1": "2025-02-25T01:41:00.522Z", "1.86.0": "2025-03-17T22:10:31.636Z", "1.86.1": "2025-03-31T21:21:58.261Z", "1.86.2": "2025-04-02T23:30:38.430Z", "1.86.3": "2025-04-04T00:31:40.658Z", "1.87.0": "2025-04-21T23:20:07.795Z", "1.88.0": "2025-05-10T22:49:18.760Z", "1.89.0": "2025-05-16T01:06:13.692Z", "1.89.1": "2025-05-31T00:08:36.878Z", "1.89.2": "2025-06-09T19:42:20.861Z"}, "bugs": {"url": "https://github.com/sass/embedded-host-node/issues"}, "author": {"name": "Google Inc."}, "license": "MIT", "homepage": "https://github.com/sass/embedded-host-node#readme", "repository": {"type": "git", "url": "git+https://github.com/sass/embedded-host-node.git"}, "description": "Node.js library that communicates with Embedded Dart Sass using the Embedded Sass protocol", "maintainers": [{"name": "nex3", "email": "<EMAIL>"}, {"name": "sassbot", "email": "<EMAIL>"}], "readme": "## Embedded Sass Host\n\nThis package is an alternative to the [`sass`] package. It supports the same JS\nAPI as `sass` and is maintained by the same team, but where the `sass` package\nis pure JavaScript, `sass-embedded` is instead a JavaScript wrapper around a\nnative Dart executable. This means `sass-embedded` will generally be much faster\nespecially for large Sass compilations, but it can only be installed on the\nplatforms that Dart supports: Windows, Mac OS, and Linux.\n\n[`sass`]: https://www.npmjs.com/package/sass\n\nDespite being different packages, both `sass` and `sass-embedded` are considered\n\"Dart Sass\" since they have the same underlying implementation. Since the first\nstable release of the `sass-embedded` package, both packages are released at the\nsame time and share the same version number.\n\n## Usage\n\nThis package provides the same JavaScript API as the `sass` package, and can be\nused as a drop-in replacement:\n\n```js\nconst sass = require('sass-embedded');\n\nconst result = sass.compile(scssFilename);\n\n// OR\n\nconst result = await sass.compileAsync(scssFilename);\n```\n\nUnlike the `sass` package, the asynchronous API in `sass-embedded` will\ngenerally be faster than the synchronous API since the Sass compilation logic is\nhappening in a different process.\n\nSee [the Sass website] for full API documentation.\n\n[the Sass website]: https://sass-lang.com/documentation/js-api\n\n### Legacy API\n\nThe `sass-embedded` package also supports the older JavaScript API that's fully\ncompatible with [Node Sass] (with a few exceptions listed below), with support\nfor both the [`render()`] and [`renderSync()`] functions. This API is considered\ndeprecated and will be removed in Dart Sass 2.0.0, so it should be avoided in\nnew projects.\n\n[Node Sass]: https://github.com/sass/node-sass\n[`render()`]: https://sass-lang.com/documentation/js-api/modules#render\n[`renderSync()`]: https://sass-lang.com/documentation/js-api/modules#renderSync\n\nSass's support for the legacy JavaScript API has the following limitations:\n\n* Only the `\"expanded\"` and `\"compressed\"` values of [`outputStyle`] are\n  supported.\n\n* The `sass-embedded` package doesn't support the [`precision`] option. Dart\n  Sass defaults to a sufficiently high precision for all existing browsers, and\n  making this customizable would make the code substantially less efficient.\n\n* The `sass-embedded` package doesn't support the [`sourceComments`] option.\n  Source maps are the recommended way of locating the origin of generated\n  selectors.\n\n* The `sass-embedded` package doesn't support the [`indentWidth`],\n  [`indentType`], or [`linefeed`] options. It implements the legacy API as a\n  wrapper around the new API, and the new API has dropped support for these\n  options.\n\n[`outputStyle`]: https://sass-lang.com/documentation/js-api/interfaces/LegacySharedOptions#outputStyle\n[`precision`]: https://github.com/sass/node-sass#precision\n[`indentWidth`]: https://sass-lang.com/documentation/js-api/interfaces/LegacySharedOptions#indentWidth\n[`indentType`]: https://sass-lang.com/documentation/js-api/interfaces/LegacySharedOptions#indentType\n[`linefeed`]: https://sass-lang.com/documentation/js-api/interfaces/LegacySharedOptions#linefeed\n\n## How Does It Work?\n\nThe `sass-embedded` runs the Dart Sass [embedded compiler] as a separate\nexecutable and uses the [Embedded Sass Protocol] to communicate with it over its\nstdin and stdout streams. This protocol is designed to make it possible not only\nto start a Sass compilation, but to control aspects of it that are exposed by an\nAPI. This includes defining custom importers, functions, and loggers, all of\nwhich are invoked by messages from the embedded compiler back to the host.\n\n[embedded compiler]: https://github.com/sass/dart-sass#embedded-dart-sass\n[Embedded Sass Protocol]: https://github.com/sass/sass/tree/main/spec/embedded-protocol.md\n\nAlthough this sort of two-way communication with an embedded process is\ninherently asynchronous in Node.js, this package supports the synchronous\n`compile()` API using a custom [synchronous message-passing library] that's\nimplemented with the [`Atomics.wait()`] primitive.\n\n[synchronous message-passing library]: https://github.com/sass/sync-message-port\n[`Atomics.wait()`]: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Atomics/wait\n\n---\n\nDisclaimer: this is not an official Google product.\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}