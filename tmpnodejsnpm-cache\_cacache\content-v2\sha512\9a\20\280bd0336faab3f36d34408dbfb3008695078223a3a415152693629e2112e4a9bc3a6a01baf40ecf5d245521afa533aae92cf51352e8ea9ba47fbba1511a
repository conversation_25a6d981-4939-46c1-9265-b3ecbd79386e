{"_id": "math-intrinsics", "_rev": "1-c70720a6cd2492f31b90c842a967f3a5", "name": "math-intrinsics", "dist-tags": {"latest": "1.1.0"}, "versions": {"1.0.0": {"name": "math-intrinsics", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "math-intrinsics@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/math-intrinsics#readme", "bugs": {"url": "https://github.com/es-shims/math-intrinsics/issues"}, "dist": {"shasum": "4e04bf87c85aa51e90d078dac2252b4eb5260817", "tarball": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.0.0.tgz", "fileCount": 36, "integrity": "sha512-4MqMiKP90ybymYvsut0CH2g4XWbfLtmlCkXmtmdcDCxNB+mQcu1w/1+L/VD7vi/PSv7X2JYV7SCcR+jiPXnQtA==", "signatures": [{"sig": "MEQCIAXJKs46CEt6igdqs+e/s8LpJQfWt2YBaaiPAZaOlmfVAiB5aTtl7a/MG9HBimUvmb5CY1LZdhpdfYmLWT688oUiJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16470}, "main": false, "engines": {"node": ">= 0.4"}, "exports": {"./abs": "./abs.js", "./max": "./max.js", "./min": "./min.js", "./mod": "./mod.js", "./pow": "./pow.js", "./sign": "./sign.js", "./floor": "./floor.js", "./isNaN": "./isNaN.js", "./isFinite": "./isFinite.js", "./isInteger": "./isInteger.js", "./package.json": "./package.json", "./isNegativeZero": "./isNegativeZero.js", "./constants/maxValue": "./constants/maxValue.js", "./constants/maxArrayLength": "./constants/maxArrayLength.js", "./constants/maxSafeInteger": "./constants/maxSafeInteger.js"}, "gitHead": "ab35063973ed5ad04099e3ea05f81eaa152a1f96", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "npx npm@'>= 10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/es-shims/math-intrinsics.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "ES Math-related intrinsics and helpers, robustly cached.", "directories": {}, "sideEffects": false, "_nodeVersion": "23.4.0", "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "@types/for-each": "^0.3.3", "@ljharb/tsconfig": "^0.2.2", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/math-intrinsics_1.0.0_1733951841384_0.08063935626020458", "host": "s3://npm-registry-packages-npm-production"}}, "1.1.0": {"name": "math-intrinsics", "version": "1.1.0", "description": "ES Math-related intrinsics and helpers, robustly cached.", "main": false, "exports": {"./abs": "./abs.js", "./floor": "./floor.js", "./isFinite": "./isFinite.js", "./isInteger": "./isInteger.js", "./isNaN": "./isNaN.js", "./isNegativeZero": "./isNegativeZero.js", "./max": "./max.js", "./min": "./min.js", "./mod": "./mod.js", "./pow": "./pow.js", "./sign": "./sign.js", "./round": "./round.js", "./constants/maxArrayLength": "./constants/maxArrayLength.js", "./constants/maxSafeInteger": "./constants/maxSafeInteger.js", "./constants/maxValue": "./constants/maxValue.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "npx npm@'>= 10.2' audit --production", "prelint": "evalmd README.md && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/math-intrinsics.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/es-shims/math-intrinsics/issues"}, "homepage": "https://github.com/es-shims/math-intrinsics#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "es-value-fixtures": "^1.5.0", "eslint": "^8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "math-intrinsics@1.1.0", "gitHead": "84ffc824e044a91da1b847bde30c0286519eabf1", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "shasum": "a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9", "tarball": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "fileCount": 38, "unpackedSize": 17323, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFgq81O8lUTfjmAVEvZwrh1aB0y53XM8+MIXpsxPGuqjAiAkoLeAbUGHU0nLKgg3vkqB4OiwtGItmru5voe4A3uJcQ=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/math-intrinsics_1.1.0_1734587889699_0.8404103287928804"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-12-11T21:17:21.383Z", "modified": "2024-12-19T05:58:10.044Z", "1.0.0": "2024-12-11T21:17:21.566Z", "1.1.0": "2024-12-19T05:58:09.877Z"}, "bugs": {"url": "https://github.com/es-shims/math-intrinsics/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/es-shims/math-intrinsics#readme", "repository": {"type": "git", "url": "git+https://github.com/es-shims/math-intrinsics.git"}, "description": "ES Math-related intrinsics and helpers, robustly cached.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# math-intrinsics <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nES Math-related intrinsics and helpers, robustly cached.\n\n - `abs`\n - `floor`\n - `isFinite`\n - `isInteger`\n - `isNaN`\n - `isNegativeZero`\n - `max`\n - `min`\n - `mod`\n - `pow`\n - `round`\n - `sign`\n - `constants/maxArrayLength`\n - `constants/maxSafeInteger`\n - `constants/maxValue`\n\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n## Security\n\nPlease email [@ljharb](https://github.com/ljharb) or see https://tidelift.com/security if you have a potential security vulnerability to report.\n\n[package-url]: https://npmjs.org/package/math-intrinsics\n[npm-version-svg]: https://versionbadg.es/es-shims/math-intrinsics.svg\n[deps-svg]: https://david-dm.org/es-shims/math-intrinsics.svg\n[deps-url]: https://david-dm.org/es-shims/math-intrinsics\n[dev-deps-svg]: https://david-dm.org/es-shims/math-intrinsics/dev-status.svg\n[dev-deps-url]: https://david-dm.org/es-shims/math-intrinsics#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/math-intrinsics.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/math-intrinsics.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/es-object.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=math-intrinsics\n[codecov-image]: https://codecov.io/gh/es-shims/math-intrinsics/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/es-shims/math-intrinsics/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/es-shims/math-intrinsics\n[actions-url]: https://github.com/es-shims/math-intrinsics/actions\n", "readmeFilename": "README.md"}