{"_id": "tsx", "_rev": "121-d545ca3164b01c4c74cc89bcbbb516da", "name": "tsx", "dist-tags": {"latest": "4.20.3"}, "versions": {"1.0.0": {"name": "tsx", "version": "1.0.0", "keywords": ["TypeScript", "IDE"], "author": {"name": "basa<PERSON><PERSON>@gmail.com"}, "license": "MIT", "_id": "tsx@1.0.0", "maintainers": [{"name": "basarat", "email": "basa<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/basarat/tsx#readme", "bugs": {"url": "https://github.com/basarat/tsx/issues"}, "dist": {"shasum": "f8b515835caff098172c7232bb2b1330be449265", "tarball": "https://registry.npmjs.org/tsx/-/tsx-1.0.0.tgz", "integrity": "sha512-Qwo1kYs446koq4FD86mgGjkqcQmtNOyqJ2Q8Ddaibs2ALbyFlSf/KjDNKNYqImvfpjCOmsO6gF9yQXMGlXmDBg==", "signatures": [{"sig": "MEYCIQCKqVUbTtxCxXahMUey7BAymUaeKpPvgTJodQ3VEiK12QIhAKwfbRz/29gsSA34m3A8ZacYpLBpi+XatB1DXNqEL/EH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f8b515835caff098172c7232bb2b1330be449265", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "basarat", "email": "basa<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/basarat/tsx.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "TypeScript Experience", "directories": {}, "_nodeVersion": "3.0.0"}, "2.0.0": {"name": "tsx", "version": "2.0.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@2.0.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "bc13e25e25d603f8a09f57df8583a14fb613352d", "tarball": "https://registry.npmjs.org/tsx/-/tsx-2.0.0.tgz", "fileCount": 3, "integrity": "sha512-8Y6SsOteqUBA7vjt49BVQGYX8zmk0lVIEhnfQwn7heLQmA8wlOWmiJPIaRnuhE3EJY4MBlESXnp1H+Z+1R2s9A==", "signatures": [{"sig": "MEUCIGgWTGCXpfrNATcvUTqcAExgCSAjl6dWP+swQ/k+Omc1AiEA6VN00Ai+UIh2TRLiCAEkGfwALhDfx9SxtT4oSgzLVpA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJievTOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTLA//QYCi1tqAR8tX5/iWVlZaYbklTkUGK686ycOyW+HHOwLMf7Po\r\ntGORcedL7nN8vuvKz4IWMZVcFMIWKtYApOVRjaytA7DaiNg4yUqfpfhuWfSh\r\nDCeetl+FX3Gh/m7hVj5a4dQVqJVE364LALKsOx39fGy5GQVHwCvusmbbpeCT\r\nGqpxBiTGdhqwNHi6vYR/VqPmfWaX30uFOh/GvaNU2uN4Ki4MuJj8tXZ2p2cF\r\njEqy8I2okt6bJU7U0jW/4LjUuzg+1ypBfIOGra9o95pNbLpbjiovRS6cXQmN\r\nWflUZFA//h09vT6+81Qg5LHHHD5hVIjr5Gg1NYjNWviBFo8DMvsbflkxF24j\r\nIOF3HcJfiFx3M5NtHavZvA62EScWE3Qf+JNaWbOohIx7LIi+ysjJqOKF5vQP\r\nMSyD0UCGOybErK8sqAEg3vHaeT7+63BFeQG7wWPnbmX9R2wFGtwj1QwdXSdx\r\nV6SdzEUwURq3Jw8nHLdfnLwTU5rmaj+XNQYxJe6tAKkIJC0lzSidDH+UNVKR\r\nLYPrKKYckA96fBuQS3NIKa4bO6rjinJ6WtFW8o94Anu+jO62yiQsrF04hfh6\r\nwE/KxHmCeEAx2x2ZSPHyqqO2tOVH7re27qd+M0pCuZ9k/BhU6MWXWjZ+filq\r\nhVgXprqS3ADTUq3j76YxfURdF2p/AqMuBZY=\r\n=YG0S\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "gitHead": "12e64c43401c5f2f7cf41aac716a3432c7ff225c", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Node.js runtime enhanced with esbuild for loading TypeScript & ESM", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^1.0.0", "@esbuild-kit/esm-loader": "^1.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.0.3", "semver": "^7.3.7", "pkgroll": "^1.2.2", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.31", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_2.0.0_1652225230026_0.9465029692780131", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "tsx", "version": "2.0.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@2.0.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "9a00daace970793d61c0624e11d374d8cac2d055", "tarball": "https://registry.npmjs.org/tsx/-/tsx-2.0.1.tgz", "fileCount": 3, "integrity": "sha512-qOzBwD5DkUodG7PjIUTPmRdAknYHC4jh7cOchnUScJCyayI8Cy8WqoD1rc7+zhM6x5a4YDoE5CJ+t3a92K4j2w==", "signatures": [{"sig": "MEQCIHJjaAK1y8Fc+aSNG48GdzPlLDSWGgxGWnC2I31ZtQUMAiAv11eJ/xkV2n7CVM6vg/ZEy7M0rfq2jH7DvirU66I26g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiewWGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqqfg/7BKT2V6qED1gHv9+JXJyMebRsT+kYQ7uouMSUFsEcLr/xQIf8\r\nD8oDSYc57yQXajNCPo0L9WS9sfYdqlnmq8eGAFh9ZRCw1FvpJu8wo40nh9Gw\r\n5qQesd57SR/7gLOt3I26yU3/Hkhg39ZJtar8+Qy8bSp0621O1fyUWxfnhQyb\r\n5vNEkS6Lok3CVWix0HfltcXOQLaEZuzK7mBeeNqkVGiFwNU8NuGzTEwuHzbS\r\n28Xdvhiv4SCaaDNcc0dKcNhFgJscRc6OklpSOtBn2v3MMIqln8O2w+dUH16Y\r\nH7Av0mpJUnQLtrn+PSXuZ9tJunadGiKjFlC3O8uBEg64rnDNIPt5PeFU131y\r\nR1Hj9oe+r9Cprz7ESL0nj8hVRgKgZQImKHT9cTDI5paXRnno0IbO4S8Jsy2A\r\n05ZFbq0WhZjrn7EQlSh3bH5Gn/wk5SBBBoduC9rLrtMWfIb3K/biqK8DcVWE\r\nvN76VlVHedLvLtugHkGhv4qxaksxPx7q8VnwREjM1SZaHCNU3MQYl8iFJqoU\r\n6VejZn4woAsqiYayqKAzzyg/6j/huw65i/HLyyYhgKrfyPqndvw91fJU3iqX\r\n0034TMJDaK7J3Aaw8WpLmFXhtZb0oJHD9tTWcRSzr/M0W0CM+qYKmjryzbDb\r\nMl7tK9hr5IgoZCgfEuo/5BXO3Sf4fLx7Ni8=\r\n=lccy\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "gitHead": "ccf7ee1d506a02513dd4352b369adf2239c4b3ef", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Node.js runtime enhanced with esbuild for loading TypeScript & ESM", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^1.0.0", "@esbuild-kit/esm-loader": "^1.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.0.3", "semver": "^7.3.7", "pkgroll": "^1.2.2", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.31", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_2.0.1_1652229510710_0.960803908333246", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "tsx", "version": "2.1.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@2.1.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "79455ba446ce3b6eed94fdb82d1664ef73b081be", "tarball": "https://registry.npmjs.org/tsx/-/tsx-2.1.0.tgz", "fileCount": 3, "integrity": "sha512-+b4qULxZUFzGGJ4N40wDv0krYW5JWjNB5TkfC3bS4DIA4pa4IE82F/XAInnj0+sWL1XvKzDyFbXvpe8UHSkBfQ==", "signatures": [{"sig": "MEYCIQDdP+m+tPbZ6eGtly4GrxWkdS5jHxmPl9CbHDvUODcWkgIhAL4dJIWcEEpbzDRppdIQd+dxwq1c69W60KOM7h/6Si+v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifqDCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1ZxAAnh3MlgM1bQ+XGQ3fRjKHur+Z8UEXDoobYcKl+ksE8QrBciqI\r\nefJk20BSLE262gAG2i/g+MUJej2tEKbcsbBjLswJvYg6p/0bawPo8iqfJ9Jf\r\nSscyAXW50ctkTJWZDwfjo9+R2SDJJBxVFByOvdzGHJKY1mxpZ7KOYE3B0JIQ\r\nnOddfEj4gy9BDPZqtFzODoTEGLUYCvpTa0lgBRdIYyNQpdX1dFvV3qFsRtfd\r\nyx8fIzNyOK/q+jCUDN8Lm1GpVU7PBPmE4M6qOE/LSEFwqCSnhkVqpd/svrOx\r\nvPQUyM+ToGNbvkP38FDWQCZ6iBGHp7WKrTr/OonFAjtHIn81qhOMohsq3B5p\r\nI/TCBUUtFOnMTUlVh5ze46gNeWAKiLUblskEkSnf3MN5GeWmPV1atEQGVsra\r\ntFVGixZC2NCH00ieGe9ghSfQR6ILzB/QJlEDSMHr+l+MLr4DT35JuOIyGDGz\r\n2D++wUPDLJfCLJAXrhIK0GpklfkfH9Pw30Lu1ZJ1HCWU0kVNaqGXM3z23SjK\r\nl9Sz2MtUOz3S5xdlzDNwxpazWHgt+IBiFVq4kN9trP9/7lhzzrnY7aB8bGU0\r\nQ8u+jhto31fXNouaavBu6IMl1dls11wBJX4Ray5f16ECQdoIrslDQrbfdGKg\r\nUWfBJ9ut1r3PT6CEjIIOW2mVEA3/il7no1Y=\r\n=3xqW\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "gitHead": "2aeb6cac4f262c5e38ca080de8b6bb2e5244a769", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Node.js runtime enhanced with esbuild for loading TypeScript & ESM", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^1.0.0", "@esbuild-kit/esm-loader": "^1.1.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.2.2", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.31", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_2.1.0_1652465857804_0.10025145012003067", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "tsx", "version": "3.0.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.0.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "c056399ddf7ee7ba1471edcb946d4ded139b6f96", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.0.0.tgz", "fileCount": 3, "integrity": "sha512-e6GTuGfETuzdg8rEXgaX3++kn2spgc6yNsWh+PzTh6KiaE/D5m3OeMoefLVynByTmGQmFNBCMxBerM9642wVtA==", "signatures": [{"sig": "MEYCIQDV+NcdD+jevu0xqoT6Zo9pipMnvwuZWWocanAcljY+lAIhALYzgYi62tbLUqK15i7/6ZPzQfaoHDkWXFowTZIJ19+4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJig9VCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrn8A//c4u9q0YmJXTjnz8dqJZaST0vEeO8bzsuP4+FH4PnMnbuAjUG\r\ngCVLBAu4ympgsxwc2wARFyXBleaF3WxNh7xlWH4ZDuOGroaVas0lWruOh3wd\r\nsEdghEnCMSx7R90T/nheti0MmGWqGCPTZoX6yqaDZbp6EFPSzXv1eL3zFFso\r\nSJzd0B2pTW1cYWQ36cl8D95t70DJv61YmaEniQJxWiTO4KWQBFFldPRBnYCA\r\nvwcVzUzCSEjRXgrZRM2mL7koOc/rNQk1LN4EdIKobx0Ui1R3gCRboxajW/BU\r\nIGWpqCD6h6pnDQpbBbQ2etUY25EKycQdnURJE0SbxR7k25iFcwlidxIwOvRy\r\nWUbJFcMm4CqBouMXfUXzoiIfhm5ufZi71CFu3Yd6WjpJshf1oQ/QmGN++CM8\r\nDYJatrDxvhM1aQfoLrfLoEwz38/cz311denXWbSF5UABud72+tdb9dqNABpO\r\nSOlOyCwx5xypzWWfo4tinv60lQip+bBlxqDzxB0M2tP+MyAGhu1Kk0mmofov\r\nIRIj3+FudK3Bj8oqkLn5LUnzBtBURH/WjWgcP96GGaUCzC5Ty+FwUC5B0u8z\r\ngR8N16ZK1uOrQEYl64tHKABwVc28jDQ8zb+qr2V54M947Tu/q0v4mVwUBe/r\r\nbf+B9GG/ldBrZJ4W4ji9nLskd4WW8OE+1jg=\r\n=Kya/\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "gitHead": "c56b4e2072ebd6c569cd90529f33e1b79903ea86", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Node.js runtime enhanced with esbuild for loading TypeScript & ESM", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.0.0", "@esbuild-kit/esm-loader": "^2.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.2.2", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.31", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.0.0_1652806978263_0.12493945716451216", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "tsx", "version": "3.1.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.1.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "ce2fff02a4daa6dfd8c1c748bef8f2b3627ec9a0", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.1.0.tgz", "fileCount": 3, "integrity": "sha512-MRW0qPzeo6veO4smXFqtUREC4lMXXeIvl4N6TULvecJOrQYHvtPxlRy9rxzQMjR8vix97c0QJMlhAN1rS72Djw==", "signatures": [{"sig": "MEYCIQCAAiZCQiYWTUUCfJ/beVnpr9yvkYqjVMDGgt2CpjfXRAIhAPsaMm271o74kVfiCMe1AVjAJEja2hRT/lsMAbozDkRF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJig9yoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUDA/+INrKgkkFF2i+VpR6nV5RAs0kIU5Xc9r/AbgqH+hNoz64vdRF\r\nYTMe2/Vb8Hi6DqImpVioI6AokHuD8O/gv2PCS8mrcMjkR50+wnCC5Sgyl2nD\r\nT2EWxZHIiUZf0hFbi5q0ffJcrK1Ag1jTB3R4AXsdBnJjJ91oA2DkCOP0D5Nb\r\nPp8OczRntb/KTlZ7simMPEfC3+RLlFcj8bwJeGmHznb7dd8biUUq755pta4N\r\n7tKYowO8rETVVL+WxuOSyjUhIzqMptzfK9nltTfwO7v7A/Mx3qZ2hVX3Seln\r\nKgyOkPBYK2dDCn2Ah7Fo0uzgevmb8fdVbHgYnLgBqiySKmApvJtB2tgSRJu2\r\nbRyX+ixmJcAcxuEU9jXDmEPf53Q2JHk2CrgJ3p3+9Eah1jFXXg5rrFKPqp4+\r\nDxU1BVjwgX6/HB7KyMT/TwJDKhUI8ZEXV2HI44bSXr4NMdNaas9gWeC+/8cy\r\nc5RQl7pPh483n/gJfIihxgXSXpyHsfm7V+Y3iPGZvIQIS0W06Q9r8cg53Yz/\r\nRGUCtl7dUTGqsGgjdY+yxvCjs1AWnvI39FIHGBuLCe6lHUbQiTVyKZhmjFfs\r\nyXAXvLiKmTvARquYR0jnyqNTzw0B20oI4UnlrzXkuZzRafJvYyMZTfwCRrgx\r\npRghn1xodArEJLb/hIlfwWepoNeV1yfnJEQ=\r\n=Dcgb\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "gitHead": "6680dcefcb510d5b56ef392304798945279bac06", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Node.js runtime enhanced with esbuild for loading TypeScript & ESM", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.0.0", "@esbuild-kit/esm-loader": "^2.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.2.2", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.31", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.1.0_1652808872431_0.6174978046684061", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "tsx", "version": "3.2.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.2.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "c8f37626eaf9e063cedba666ce53aa7037f5b67b", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.2.0.tgz", "fileCount": 5, "integrity": "sha512-7K/UTePojiu6wOiWXG3RiZN7PWOwJUE3aSWnFiOKJ/A5MP2VhCIZdPIJWFcsv2KC3GG8KKpvf45MTfYnvIv2gw==", "signatures": [{"sig": "MEUCIQCmuP/0ercqYeTXm9fcATe0s0lTXRw+7ozNOL4Ncn2oAgIgEe+OGUP/TXgyh+P1bv+UBnmHoqej8Rr4Kg2puOEnUiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihDaFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfRBAAikCrGT1sC2EX6DeQbNmOlK/6wCD+Nj2G44NqmaLzsiaMOObc\r\n9AGl468c0+t36SpGUqyBbYp+svL15LcxvrySK10lDdjLzdqy97zhtnKDDu06\r\ncP+EMwMz8+zawZv3gYABkBM6iBkIHy9ToijwjYVg7ZuCVyitfS1DvOoP6wyn\r\nDdmIChAe4245h2xoapQebBSUeS9CWnBxBmTbeRVvUpnng6tWH76a+o8fV3iX\r\nLhMJltxk62H5heG/r7K5RSCLGnWGzJZhAWEARtPgmhbLDGBpg3vQYMsdm4ZG\r\ni0IoIArRwqTRid6hXM8gKq58G4fPfn5UQh35YabXjwJquJwpMJcGWfSYWJq0\r\nV8UvLRSj2ozLqcKFqyjWx84IOZ2825ieQwUPfnG7Al+bCTepIyPCdL5NoXsd\r\njO67NLL/GrZ1cG4GFXdNWWN0MP+I8JopqvXrp8a+BlBDmRpXLYlxdnwrYEPk\r\nJ1bf8dgM/5Od8Bnn050dVQiQH+AuwgnHpXAD30EYVBALUaQ7VrxVWEGc7QKO\r\nLyLyt+eQPsxMKqZpxCRbKwAM2YDf4Z4nCy+Lyrm780Aax0860PNBv0RwnYrt\r\nuLOanZz9yXCZyVkEQmo2AFR+U9o7oDlhAHluxew36E9q68uEyk2t0bAjyv3C\r\n55RPSYxS6mexHLzwekvCjcOpsf067jIQw8Q=\r\n=WJbQ\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {"./repl": "./dist/repl.js"}, "gitHead": "b2a33d28ea48f2afcaffe43725e4cc8f8f37947d", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Node.js runtime enhanced with esbuild for loading TypeScript & ESM", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.0.0", "@esbuild-kit/core-utils": "^1.1.0", "@esbuild-kit/esm-loader": "^2.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.2.2", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.31", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.2.0_1652831877195_0.7508116531324214", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "tsx", "version": "3.2.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.2.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "2c9cc0837af3f7f8cdabf1c523ee3e6c6782ca22", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.2.1.tgz", "fileCount": 5, "integrity": "sha512-j+Z0kzm/+WMMgbKotcOJml3hHd4Tq1Dr/V5rL82NrNP2dbq0DbyQ9TplIZ1xOZQcsZmb4W1IrK7tueGWyppJjg==", "signatures": [{"sig": "MEYCIQDfsWRDnLbhaAi9NDk5Yhbd70mdYs7hs9WVgPDQyZJbnwIhAPM3b0GIR5FQF62bAxPOe5JBmxAeXEgVWLAQtA1iCfmD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihNsmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroRg/+KxwpZnqVZvKQTudbZC+e/LUmFyGfJO87+w+UbrZlpbMV+SON\r\nOTBcJf5wzmrWLYArBSoYSDXF7BzTlQPV2mybdgh1KG9PFqAweaLUCrrfsvZb\r\n9DXlI9yThQ/a2qNK9M9svqM9C8i8jhbmpSXTJNQ2XuuqeWOJoQvIGCvZL7wH\r\nugtLquvjAjDtMtSy16n6ZuNGHbpnjiTuZPKUnG3mDP+k19PMneGwpZDauLf7\r\npH+GlN/28ajyt3zm00NFHlhrlntwiM8qytQDijv+NVTlktT7DUjcOZB9WWsv\r\nLTiW7Bdp89sn12M1ZbwYiGBe34iczmQlaryzcq0p9CorK3B4MlsnPOTtvH0Y\r\nk+omEs8muX59Y6w6cKRZ2GX8cinGM07pHnB2WCW+JQUsXuSEF8vwN6UX5BAn\r\nDgfXcHKzzJbYS7HRYzrYe9Gz3geHZR+bAsnJAyYwNEQzSUuucjXxvS4tSb4f\r\nDRFFPmMH0Aae746tuq8IUZzjDMUterv8mSe0QpTjIySiziUCRuyFkxwEkINH\r\nhPgwxUG/wCv6+75gy3g+I4ssmz4jC3h0cBB9PV+oSiO9rinuCjj2T+QxuMxU\r\nBjZBiciY+cPEGqpl5b7k9bGMjv5yifwEXFwZBLFsQeAhPY6EW7IO76VixhWI\r\n10eNU2DPlIhNP/qet/i24zyfFo9SlbGmjfI=\r\n=DJ0u\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {"./cli": "./dist/cli.js", "./repl": "./dist/repl.js"}, "gitHead": "9cae68a37ecb349a2b3ba99e8979c954f3fea946", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Node.js runtime enhanced with esbuild for loading TypeScript & ESM", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.0.0", "@esbuild-kit/core-utils": "^1.1.0", "@esbuild-kit/esm-loader": "^2.0.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.2.2", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.31", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.2.1_1652874022007_0.40991532449320034", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "tsx", "version": "3.3.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.3.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "0b558314b2bf41e72aaa78cead6aa1e2834f225d", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.3.0.tgz", "fileCount": 5, "integrity": "sha512-fOzOyMZzxuOSUMg+QUQrO69SVPxFYqpJJ5N3dYbRnLhMixfhcLgx/Tsq720R5zgM8IqNz+c5i/cvTrEYjCHiuQ==", "signatures": [{"sig": "MEYCIQD11ljQBeK1omQSD+NZEeo7AcUxPhf3Dq5aqU5kGzTV9QIhAPhT8TiKq8H1YjfG4m48KNIsyYjokcptinmZNUcSs/lo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihRpKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5IQ/8D/qepdIRSJ5VoVvOruFQ6Mqv4FjMWHTIR17ug5VNtsYZkdAp\r\n1vaLKbIL8T4pXsea87X0n9ZB1kQC0VEsMvy6NSyDeEG/xqNUI8FRsu2wdx1+\r\n3Z+b/FcUHR774lDtKcrl4lBN/tyZooNggtvJ7EgH8ib/7muX7jUJbZwIGscE\r\nF1GFVvlQ+C0uX/+lyMxYPHMgmE5xd/hSFv68YWwnBvu5WN6gZieI/FYDporZ\r\nlpSGQcOzO1dLG6bU63szReMfJhbPbSSw9wViYbrFvf50b3ziZBqCfcvKLQyB\r\nxno5zlKRg2TX09cGhg/wim4OOwpuj6O1F2kRJ9q+EKAs8XM6ph/JTy7klrif\r\nMntC5OcKuTOLlHEp9GonPba1WpyMSOF8K12p3jdyxbGpLblssMaFo8Zg/kNi\r\naIAcaciY4Xjg1bZAVy/VCiFL+xoZ0lOqXaDj3n0VYSWmSf+qYbUiwuJRNb0J\r\nbGGj6rnB+mfdyqd/5RauWB/dBBjTBqvQkmI0z31JcpaqasEwYwkI9csA+e9c\r\nClcXEbrJ/WGLYIfah/sOuVvOe8yx0GZp8oYGCEc0biAVmnBFLVlPJXNfmdKH\r\n2qDIUo26Nyjb9V4+o6EnvvwIUlyZIzk+j7q2AU1TAcKKYjV+WLAbTp/6oqYb\r\nPGbBkk8Opw3pNPHcLCc8IqCaw0yJk2QVBSc=\r\n=mujN\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {"./cli": "./dist/cli.js", "./repl": "./dist/repl.js"}, "gitHead": "c63ab45b71b6f1aa326937ca0dfbd379ceafa2b3", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Node.js runtime enhanced with esbuild for loading TypeScript & ESM", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.0.0", "@esbuild-kit/core-utils": "^1.1.0", "@esbuild-kit/esm-loader": "^2.1.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.2.2", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.31", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.3.0_1652890186621_0.055119040294154154", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "tsx", "version": "3.3.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.3.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "61292c997a688eba193cbc321744df9c90d905a0", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.3.1.tgz", "fileCount": 5, "integrity": "sha512-YYaW4+MWtSsc659Olt1XKPl9AEWHTkcb3X1UPe7JHp1ykYuOWJcGQEq4KZYo2Q9lWgTdV4hkxW81M8txJ06srg==", "signatures": [{"sig": "MEYCIQCPqJtscB3Jqz5liGhDVDZI5tO5k46OKg293uQJ/CrcrgIhALOYHq1RC7Cnb/p8SaMFqMUtv1nb4Ow2lH8cCY/9jlze", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihVf5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCNQ//e2YiQugW0xKZwMLpCbNJp8TuwOTWs0kU4tRwx9wG6Ti+1opg\r\nG4i4BRwo4RKDQSJvsQ5k1WpFQ1sPEHD72D97gbt/edcfy9O37bOFD4Tn6HdA\r\n5TNpowZ+6NmN8dRctwkkmRN7+urXtvk2ujH7GhLDnWmG87GrPjQo8H/O2A9u\r\nFOGwBLcE2oZXUXsYibJvAXoRkvH0rrsz6sn6keT7FA1nAix5R8HA0TKryniI\r\nM7pg4xu9Ff3KK4xSfxEjyAc2Lu977WdDE64UmV8OCWGH+IIt6JzhoxA4OoUK\r\ndMiM7MuBKVxWnkUlhZoXcv6mELZISBWjlE8hEQxXufXxuGxhPtKaqQ7rYfy6\r\nsj8PRll8T+IyNH/PxlUkaZFHMCK8NOTOZ06Ps2f2VvYVREqxi+gtmWuazrRp\r\nK0tD5bVcxtMu6OMpGq8CIcsOIIfDlh9mrv6m6iQD/DsX1/E48yft9GYsLZdz\r\nghenXDvmqn3oJ9oglPEpHe4hMQYGTzF7Px+Az+VmEdks9ha66CX7gqKDdIQZ\r\nCGAuODGyJrux3z2CtxNUdWWkcU2U6nzPjy/CeJesJPubVZVE3JwayByqoCRA\r\nqvgkmxsjAyZPlSsJh67e+0hIUyTyqtXmpduk27Qy+74j4+md6/9hEiZkicB9\r\nF7m2A8Ek9M7VM8WtOHNFpSPYB3R4dTTMA4M=\r\n=AR/Z\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {"./cli": "./dist/cli.js", "./repl": "./dist/repl.js"}, "gitHead": "1273318a215091bdc9229cbff0f31fec30c97a0c", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Node.js runtime enhanced with esbuild for loading TypeScript & ESM", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.0.0", "@esbuild-kit/core-utils": "^1.1.1", "@esbuild-kit/esm-loader": "^2.1.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.34", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.3.1_1652905977383_0.5132793577997836", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "tsx", "version": "3.4.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.4.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "20aed3831464c0183396d7c4f7c813a8c8429647", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.4.0.tgz", "fileCount": 7, "integrity": "sha512-WWakMoC5OqUXvOVZuyAySyETjAZ9rJxZXRbbOhYXDCeHF95hQUBa07UwUFu1yprlnrJ/W7XWfA99YTNKO//KxQ==", "signatures": [{"sig": "MEQCIHR22ZiFjuKrcM/rBiPgu7CcND2GFSg9ZzIcS9Wka4nTAiBTcwWRUtvXfwLZf3WcecRGzqfzc7QVap/ttI2ylczdHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihmHpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkPhAAhxiqNewAVNUVM/pNl1mIMFJ35mw7866wQtjU2VPX6hWtjKX/\r\nKSKb0aC6x1S/a8pk66xEtpv7ClSgUX/LqiilgyGzQeFQjsVY7Txmd/04Pzd9\r\nA+TbsoUjV52pIWNbDOgxl8nynhsIBCJSMELV3RZ6QDUHvWNhq7aAQgAqVKbE\r\nd0LSyyTsdB4CkXBSQ5miAvafAZ5H8r25P1gGYtIlcAOgAHVt2w0wIOLXyIfu\r\nj/Ym5nx2CzblhYGCPDSQp5ABqjJVLoCUto9tzALCH7YKdejvruiBfJuDl8Vv\r\n7niQcP+sm/D3E3ryZ2J5tzX/fMZUsInD5WcdJZkfvyjkloehhEd7HB5fJBmH\r\nb0quVysJmMbZEpPpFZg2zzPcSvRanDRkpML/xhF/j09sN+QrjZ3PTXpLUkm/\r\nA+qLQCEcv3SFwErT1L8WMNfFXQb10mhGPXaGuF5RyNOf5V6YQB5/P2mvOpJ5\r\n34T+yw3/YaiIycJyDlO0TbF5qBaL0lsmFsAsfv0WLR2ucsLUyvVsBJJlMM+/\r\nOc4D5gSzQReI68ZXKNUr6fhAG9r+rhJJwTj7VIT0oOp+6k25rUyMjqogWGok\r\nW1x6YZYMMdAbGwH4/xx6Zl8Aasj6u892bBTQifx6ugfchHjlCS5/p32i9pif\r\nxzaoMDNZfpc5eFAevHX0Jqu/zE3s0OjkCKw=\r\n=D/Dc\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js"}, "gitHead": "a271f4d1773bfb0ffdd072aa36e996c6e8602b32", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Node.js runtime enhanced with esbuild for loading TypeScript & ESM", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.0.0", "@esbuild-kit/core-utils": "^1.1.1", "@esbuild-kit/esm-loader": "^2.1.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.34", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.4.0_1652974057568_0.8857279161621041", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "tsx", "version": "3.4.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.4.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "463808d768673b63f0875303052fa14909cd0bd3", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.4.1.tgz", "fileCount": 7, "integrity": "sha512-ksemgcHyR+35wmPWyWBnZPCyPjt+vZj0DIo7Ze6dJzA9zoPo6LVRx/JgJ+hsGgVhFxLsZhTgqORZQORc2WOzKA==", "signatures": [{"sig": "MEQCICwWf2aRJa0jN6ksRS3ZrnL8SjO8hi7vzmtWDr/OM/9CAiAYZXE0HZKxmuYMGU7cWonZ53qduA9QiEZnK//16LrV1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijYYQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6xA/9EuOt2w5tmbV5kRfYj+WZ/qJQ/LqtZ5xSrULMzsQxYilfkDw5\r\nvHrAprwSCg3ddbsgWjLg9dRFOJvAcrhowV+/WqlShofVV4uYdHYeOYUThSGn\r\nUtTdkjG9hSjqn1EpSBk4sRopT/dI9IopR1nfsLuey3pla4VCwVVo9owo6hnD\r\nTt5FLFYaZuvRR/uNTtTsadVoR4MFlPeD58KWeq24ihTxLaNWuMtID9xhAaTL\r\nxX0La/VJJ96Uo8rR4/MGuSvWfLlxkM6riBqd0Fw1aNsphqU9SGgUjoXYfRRm\r\nY3mHxONMVgue1xAI0mkCNIt/lH3/3A8fiaWFfTYYM/KWNERxAa15uNa3OSgx\r\nug9gyDVhk0fb8u4CFFsZibXwaNCxHPDtQTjYypg/VJHCs46IQBQRKqtJwr4z\r\nY34jXFoUcHs/SUFYuQwlNcC/hUUBdypeS9sTcu58hrNsDRHA48dWgKuin8QZ\r\n4hbzg9/VtpFwo0br2LcWQ23bB2OWHRiAOsYsTp4Of5hjdeBuRKSb7btBIuOl\r\ngxl809N2D7eLImKGp0UZOhUdLHDVOsNEw2CJFBkIJeUWoXizBUQC3G/VF+42\r\nOWd8/z/Dncy449jorXyD2QKpF0J6/7VOHpEhy+MOJq4zkLH0iRuYijoUkQOl\r\nXeSAK+xsq7umLVvvnh6dgTUdzuTqVbitvRc=\r\n=819q\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js"}, "gitHead": "85f52194ab466d92283a2a520df2cb85db18eedd", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.0.1", "@esbuild-kit/core-utils": "^1.2.0", "@esbuild-kit/esm-loader": "^2.1.2"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.34", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.4.1_1653442064593_0.7210398853908122", "host": "s3://npm-registry-packages"}}, "3.4.2": {"name": "tsx", "version": "3.4.2", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.4.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "6197b57a07dbaad5bcdc7e1a5a1be4fc88bdb64d", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.4.2.tgz", "fileCount": 14, "integrity": "sha512-Rd1gm2noOUiVynF+VFxo4bVBNbzS6haWKWtlQ0bEfCLLEqm+GG3R98D3Rqk6foQ3NnJk6JAWOx1ragwcAPj4Lg==", "signatures": [{"sig": "MEQCIHa7KP6UKJuDYkYj+Xus2zOKMo2og3QnjTQ0FsjU2Yz0AiBn6gTqAKmyLXdNOX+90rTFi/LkgrAGjBYGcisqiB1IbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijpiJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0Kw/9Fv/Yyi8x8HYIwLjz/qvDb9cWufJt/WZlMfsJh4R5UNM6EHJr\r\nEgzGCquQ1+jW8CKOR1wxwndIJuiQOm5uP1rvXnS9/teaRi3L33g6dLoNDBt/\r\nUuk5FDCqYef5Pwv3q9IkX9WONbA0uOqdyUL3V3helFQNWB2daV1IGFrGSBOF\r\n4GzEm8jrjbW5qxRIVNoCxdvWHXCvuemoEqeyHyf+R5c0FslftAkj7R3l0qcm\r\nIhWP0YXuPSKJT/ILgVetYGhFdcEZxCS3Ql3M0esQl+bGZO7AiU8AHSbknp6u\r\nvZYpQ5JpktmuEOTRNIEZjYIud58pRUW3WZ1nS/uBxp8awf5BKCibZvDJByMR\r\ngdIlXhHbXm0oxyFVmCY1mxcgg+hvb5537RqDo9wk8X8MsN2WsIAzYVPtTgC2\r\n/Uz1Ah6H9ycbIsRsOU/nCwv3RT3gjJIy/6PMlYC/WNCg0MC2/JS0Zp0cLygM\r\nGg18Lr4ZiOsT7OLMOOwW/w9nywt/aBfvyNquxFonX6clZNnwuCt/YhbAXPqo\r\n1yldz6axUHAhpRUUnXRk7I5a6lUffuueFN3Ikh9u1P0corSen+kBf/I7hcCb\r\nj4wfxswvxlaoGH/iEsqwWu8yYNvpv2ARQrkTho5nlnQusXkQTTGVssj9p8ZA\r\nbmBeahlZ9tI87hDn7isMD30EfKHemQJ12kE=\r\n=pH9h\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "3e81d19bf759b512eb74360861f5abeb9d638ef0", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.0.1", "@esbuild-kit/core-utils": "^1.2.0", "@esbuild-kit/esm-loader": "^2.1.3"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.0", "execa": "^6.1.0", "eslint": "^8.15.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.6.4", "@types/node": "^17.0.34", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.9", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.22.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.4.2_1653512329298_0.8678739244834315", "host": "s3://npm-registry-packages"}}, "3.4.3": {"name": "tsx", "version": "3.4.3", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.4.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "c9a30c85db250003c147eb47c2251d5c0f950783", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.4.3.tgz", "fileCount": 14, "integrity": "sha512-2joLNBKxg6Jn+qEMlzMrS2K6uxBG2faQNX7J/w8cO56ysCxJPbfeyWX5Qxbv0l8GeCkkc0Q0/wlH9eEI5LkZeQ==", "signatures": [{"sig": "MEUCICxZwZVJsSFcWSTyAGohP6PYdmpSmwXJwIy9DhQNuJRGAiEAhGy5TUYTH1CqC7UKIIPi3MuN4+YsNQty5x5zQg3XcNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 237544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqrFyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7kw//auQnghJ84szIA6cM/x9LKh+aHyZN5VhCKfWqJgLEjYdfuevv\r\nERYFNuQAIVA2Z04mbFyNmlOEYCudFBoAdUU/pl7P7KNxL+ig5Ar5KzI3LO4g\r\nOO4ZfRvOoY5wD1S0Lav6ZhRS6YboncmhknSR+eTG5yu2j/IDCevj4MY8mfN4\r\n/pSlnRwzPEJYbPcZxo/Z+pVGHKjnow7NSTZ44TlekUWa2fHqktSvA8U2jAUZ\r\nEwdGp2Jhxe3y5efdLiKOgBmUi5CTAMlOWdAOjFMfeWYpMI33C4GeywA9ceQe\r\nKGZ3FgVIFRP/aSWyAyXPoMFV3hs88OgcazXSlQ6GvmLUJ7vhArF/DUGy1p3A\r\nLhp3yxw9+y4859jPXCMHXKAuHc8dJFZl8qBbHKiUHqe24442O8Yba35JmwGb\r\nEjl5i9NQjA8+PBzjXk5T7RVZamUUbY+6SZOm59pyLG8IdvkNrxCTa9kXccIu\r\nVUTHl8mLDpVvrBpeR6B+CxMW9EHMl1ola6eS5mTF2TrmH4RNDvES92xLpM9D\r\nCzXnxE2nkwhvbXfmJe0ixiIlsE4GXA2OpeGGp5WE/CjAhRIuwM477GWNFhr5\r\nq5lfn+kQNgemT4ItzZXfsWTQW2ArKhB2ttfARXtKutznfRKWg6EPF665Kmkf\r\nm5PPXr1ovnpg12HE6w5/rs52QM/F4D+taTY=\r\n=TG8F\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "10e31f96bcbb762e03ca16618d47632af4ff7fde", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.1.0", "@esbuild-kit/core-utils": "^1.3.1", "@esbuild-kit/esm-loader": "^2.2.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.1", "execa": "^6.1.0", "eslint": "^8.17.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.7.3", "@types/node": "^18.0.0", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.10", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.23.1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.4.3_1655353714017_0.6616207527762683", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "tsx", "version": "3.5.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.5.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "bc5bcc8745ff21351ff3c5af3194f6f8c09ef5d0", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.5.0.tgz", "fileCount": 14, "integrity": "sha512-hLqNvjXt4EjliUKCY2mJA+tTJ3WRdPpqYdU6Q76Maa4VJONj4hBa9dPtcyRJEPFJiNxoJ0gqMak9ki0zXioJDw==", "signatures": [{"sig": "MEUCIQDVSQ4FDXvggNHuec4plU8r9C3/u0jnYrZTu5qhBeHH0AIgaBBEAUalwmiMPWoSp3juEl5zwAhBkSIEHJFHQw+91r8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 237544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisjuhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGqw/+N4GCw3AGbaOPRN+EJl8BDpSFvpdys72X9T3Ug1hsBfdoK+m6\r\nVthvKxCfQOj+++PSqikcS2fw5tDwH/6RgQM23fUtKwFfd8Bkd+Lf5lJP8E7G\r\nAUwn5SsV27GHVggg9262RG3r583Y5VC88MJPBW08OOLu9w5jTn2LpgoBw7vh\r\nAv6rkUNZexOdiFBpmZpZjkGWDtihX++rONgbzDdfYpR+Xpn+MiUpIB6nCrTz\r\npiuAOcT28j+Gl11frBjEbY/DikuaFY9zcp88DqCIhyOZ8cLyzlkQcjVfJdSd\r\nz3KB42ExQ3AbjG1ItAQSnP9/wSQjR3hpOxON84LNt/4EuThuGXbSmntUwkNH\r\nOKG3cQF6bj/uJN04sOty9fPmBSJPycg0bXHFZafwl+vRMhuCLqL9BM1IrFRa\r\nG/xyBMcm9MGvZavyb5N2O+GuhywmRt/qFL0tCGG+vJLOk6+q7OFCJGPmy9Wn\r\ncz8SukBed1XUmlAmrFfDUaC53b7jajDdxht+Gw6UTaGMzp7uWKmUMemtnDjg\r\nMOH5yFGGmFc1oFerDRRGJX7Gsy51lYnynFLTFzTsvQsez8bjpZd9UZyvgTbb\r\nEF9eTW3O/jxkLAIkrhi0lsWOMItawrZXrfE8KvYKTmDZjl2iuCJSzfLvm5vl\r\n6VKSNf9bDDHqQIoaCAX36RHmJNzwK2JyuNk=\r\n=pbbD\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "4b5531df5f31b1a7377ced18e418a670d6746a3b", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.2.0", "@esbuild-kit/core-utils": "^2.0.1", "@esbuild-kit/esm-loader": "^2.3.0"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.1", "execa": "^6.1.0", "eslint": "^8.17.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.7.3", "@types/node": "^18.0.0", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.10", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.23.1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.5.0_1655847841257_0.0952574965941766", "host": "s3://npm-registry-packages"}}, "3.5.1": {"name": "tsx", "version": "3.5.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.5.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "4b3a3f7ddcb16e7cf17cc681da789b10bf365c95", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.5.1.tgz", "fileCount": 14, "integrity": "sha512-g4XEV5uTHOU5Lq3Fv5CgaHapBNrlfXn6tRUMPYd9u8LVD7kNcxksasC625IkwPZQlmAYPSZ8W9aJWgnhAzV3zQ==", "signatures": [{"sig": "MEUCIQDeypva4xAcK23OGgWKqJwxpxARIVuu7OZTytPyVYaB3AIgSu1S2DW3igT1YwNtpulJQrkU/7JAnWUIhp8O6Sd+SQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 237732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitMQcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp43g//U6YAAm4YHLFXyOJaeOVsUwhSnTIVTeirkuB6DyYWnr8YS3sN\r\nzCSIFFBCIevD1pRn+lJmgQWgOICgfZ1qvLfMEluhy+E9pWRD/wSdn3BsazRJ\r\ntKzocEBjp7WoT+RzWJ8S3+VatiRNOiWdxr+OWa8+m6kP8xLXQH3P0Coy7K8Z\r\nnxIQISETEizBNl1x6+rOdJ8SxN6O/ODvFxDp3Y58qZMXCeVdq97JD/KlE4Y5\r\nuo8OU2NSDEt8vCZfPmb4aDueoZ5uTG8qBmCmWRIk5lkD5YzenTarQvm/w+KH\r\niVVNiR88XWzto8yKHyYdWT5fJrdooOWOVBroJwIWw4RXJjuDLCavqJtRNSEf\r\nodHGPNlwKzGPO/1IDSSD6rzcxKfftczwzcE3UYiMAElDWLkxDfKiXJHcUpXx\r\nfkefawvZiqZNhbE9sXHl2vcI2MTu8IldrC8NH3I7tOfUk5zI/v7Kp2lwoASs\r\nMyjI2B3/y7Y4M4/ftrX1mFmxdtk1ciZKQz7q+B5SbNPxI2a3yRlXDrzd04Up\r\nfps+Hqzd/WGJaLm7mIn1/3xT4XpNB6SVu/NIE2jlaKGiKAZU9JbgQkeMngoy\r\n1R02+uoZiEc1qY0RIVxM3fRrMYSmCAq3zRMCUsgJDEQNTACR//quifqNDw8+\r\nQA5C/8AigFOdnapbJhEbryETaJa1kJqYMrI=\r\n=PjuM\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "2609f2d63d0510ad6b8acbb3112f31b1c49a7827", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.2.1", "@esbuild-kit/core-utils": "^2.0.1", "@esbuild-kit/esm-loader": "^2.3.1"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.1", "execa": "^6.1.0", "eslint": "^8.17.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.0.1", "typescript": "^4.7.3", "@types/node": "^18.0.0", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.10", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.23.1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.5.1_1656013851947_0.8969006465378093", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "tsx", "version": "3.6.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.6.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "32b6fa3d1a348fbd2ae1e6e6b98139e4acd8178f", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.6.0.tgz", "fileCount": 14, "integrity": "sha512-XzqSxPmyJnI7ZtEX/CLE/CSDkqbL7dK4jwtJRIZpV0EhCxWqtb1OqJPlUc4CVS3/MFdpt8ZxLpvPFohWRTHbzw==", "signatures": [{"sig": "MEUCIQC/VftI1t26ZeA6OuY6KwWLH2YLJTT1zEPyQYSrRDg4ZAIgEF9F8QHnZC4gzSYjs1q1fXfIExUWFSyJv9diNpDY1dk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 240094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitP7CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiBA//aN58qGFhKT4wza3jSZYfaPMAPtB9QNIUISx4eHx2fGeOJ49q\r\nL65eFEauyt73TidYn1VJrJ3i5hbQp8o5aWksZalYJtMzowPhqjwnfQRJVuCm\r\nco7Ifm9zymkP7E4UIsRcv3u5B41bn+PZewMvrgAC0yCnTBha0rtf/f9m87jn\r\n3IlnuJrLO5DXR15yza8rJQdwP0j6XA60qbYBtCKKsJhCzZtMLYvUwhiM3u1Q\r\n1jXbfiLNtiNEF2AjvdtBL7C/6R9gbiX0pqSPJmggv5U/ELsyMhufNBGQEPxU\r\nf/dIPd1BxupmyA+IBkCDE/16OQoGyO+R49Q/l6qsXe3xQsxlWFCHB1KeiMQt\r\nbwbQ9P94y1Nty6cisxmKAVY01GjiO5OHJSHjeiMfnaURPTDDyH4LhIhiDkHc\r\nEmTE7ug8wXTLm+QkFdgLCe0Ezq6eByc8wJoA2sGVntCmsA+Sl+cJU0QQIzvs\r\ngcVebljrPT9JJoTlKgrP9JkMJJaGNzD9iN9tFiz0b+MbjlWRp6820nQ1zPwD\r\ncdJU/mikO/G0I4QI7bY1TeFG780IFH6EF26i6jVEgp2E5XTYvV7+J9JbXRJB\r\neyRKaDQhV+Wd2NS7xG3jsF6g2CejciJuDfnRsG/HZ1Z5besuDpXFJ3n5MN3q\r\nn+EeZ1tLzHUIrx5ZCXAaHCRngwemruIlwMs=\r\n=ujtV\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "9d7bb20b365e4515244f3a92ea9408bce6878948", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.2.1", "@esbuild-kit/core-utils": "^2.0.1", "@esbuild-kit/esm-loader": "^2.3.1"}, "eslintConfig": {"rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe", "runTestSuite"]}]}, "extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.1", "execa": "^6.1.0", "eslint": "^8.17.0", "manten": "^0.1.0", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.0.1", "kolorist": "^1.5.1", "typescript": "^4.7.3", "@types/node": "^18.0.0", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.10", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.23.1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.6.0_1656028865853_0.963049345710731", "host": "s3://npm-registry-packages"}}, "3.7.0": {"name": "tsx", "version": "3.7.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.7.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "a40b9c2d5e6c81d1c6390a4d6f59daa245c6cefd", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.7.0.tgz", "fileCount": 15, "integrity": "sha512-cYPixpDs2AeCjhTspIeCjgiRzXTnIeaYrQT9jkvqlenxRYZpT7cLSRR0PcWUqy8VESDjkTI0/HkYW/qWC4Sj1A==", "signatures": [{"sig": "MEYCIQCqlL/J2Y/3rr6jm8fi1dQbOsomSzIi/nKU97P3hNgHbAIhAPBPdDn3TsjKiEbfOhpiyFGIXNLlVWcpUHasYXXY/0hK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 243690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiv9XEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp30A//WhM8IjUv96DI/O1UPbO3jtW95lAXqMrrIimNPccEIas0r1dT\r\n6qTNPCL3GMT+bag73tkDYEo4sd+SevHJ0/EHphtzjE50sWC4tBcxLL6SMzqT\r\nZam130L1rcCKxQ0ANZtRIm8QeeJH3oj+oYo+0jcv+qMYNYiTRc3VKy5mxce2\r\nLl9lDAsvVijvsg7qRaPQCxZuqxp5L7QwD0iUk/d13l6eVIJPEWqWM34dyqPP\r\nngIXDubXaIzlGP0gmjCT3XYemB+etFj5o4pOpa+iBH0div5YDNtlWiDELIF+\r\nfHyqiFHr4uqJnHjFsUd/nho0TfI4z/rI43ZSU5xX1szmqmJ7i3BnUbwo0IJk\r\nmqhHIHqH6RzW/h5OmTR6ugFs3MAolPya5lvdAwrE/zXNk7GLzHJCwTRUJqcn\r\n1rZue1642DHIx/gGkZEqnbN1afA3hB8oqZGjpvXiOAa7U3cySa6AAZEPFyZh\r\nVmkNOvn1NyvNEqRYBsulxnfYEyCe2ZbZTglY/2LpfzroEyHgAk29esh7ilwS\r\ncGokKJkp/XIKBnKEIYAqpIJWxn4aUahv4HvBmSazOvjBxvBK5AaEoqnse1hK\r\nkT2dd+UnyA45okU9CZA/wD2I5V23LprtPrn1vYbDiAhMeBIQc+R5/q4nx7MU\r\nMoJ88DYjEmKVEsucz9O1qpgQ/b9kKAK4x60=\r\n=AFHL\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "6fe25249679e62a2f67764eda2a816ba0de246c0", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.3.0", "@esbuild-kit/core-utils": "^2.0.2", "@esbuild-kit/esm-loader": "^2.4.0"}, "eslintConfig": {"extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.1", "execa": "^6.1.0", "eslint": "^8.19.0", "manten": "^0.2.1", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.1.0", "kolorist": "^1.5.1", "type-flag": "^2.2.0", "typescript": "^4.7.4", "@types/node": "^18.0.0", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.10", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.26.2"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.7.0_1656739268392_0.06233897353216533", "host": "s3://npm-registry-packages"}}, "3.7.1": {"name": "tsx", "version": "3.7.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.7.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "36b76628c33abc8cd9cbab60c15a00e50c631f22", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.7.1.tgz", "fileCount": 15, "integrity": "sha512-dwl1GBdkwVQ9zRxTmETGi+ck8pewNm2QXh+HK6jHxdHmeCjfCL+Db3b4VX/dOMDSS2hle1j5LzQoo8OpVXu6XQ==", "signatures": [{"sig": "MEQCIHjD5Y8O18EgJAq0CHMb9EoG/Bz4Pc8Wyzv3TYRa3kmCAiADUG+wsSSva5yt4PrP2mleH5y8g4RNw/qOEwGM41H0vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 243802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwJJQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6+g//WYQHTVSc123aOIQlIkv6gck4x+xrru8zC4BwPwgep+zZ9Xeo\r\n+ryzoETBFRYAsP3BLGS9RftFRNotX7GnTfrwQPVjTLRJeyqGImZ+STwokLN7\r\nDNxqoiU7DDdwwVnPhgQa6xdplL4hNiXUOv0vQZ9gW8n3nBxbJRqtJHUDEgYx\r\nqCv5sbQCZwrCupZVaAtim9zRpEzO4+3jMhumIkl8SsESzakB0dHvervWl0lP\r\nyXOK8/Gjp9kFqRUeeA8sd6UviruXlINI9GyyDjjUJIakakJ5AMvsiVmCXUY+\r\nwuKSLAlcgqF2Uc2sq+UOn+cp4mFkVRWQhtwmCyqR0bdAkmASN4mjlZ5g30Mt\r\nblR1xkhVpQqFutlbu3/Fno+vfBavm5qvXG3cS80HeEeG/knANQ6SNALgnX3c\r\nF+N37vWaULbvsaSwkZMOWE8Gy4y7QoG2CwnmqrHcaMCkcY9wSo4US6K6Ql69\r\n5qzqyRnQqchhfh9gupXNsWTQTtBBk9JFs3x4NGuwjhnhzlsQlpaVJFKDP8Rm\r\n22P61fEuz0le0NpKujxxv2K9LCnIK4NGu873YKXHhhmetV+JnP06GynK21FS\r\nYvrGqZoDZqLX0IIZ+T891G3enmtFZMGOqHsokgTw4B19a2glaqhF2xPSe6tb\r\nJ/SVtTIzy87fboxdsuq6YtjO8Z5K7urvLzQ=\r\n=Alna\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "30c56dbe74ec4c8c8e6b31ad8bd40cfb1002eecb", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.3.0", "@esbuild-kit/core-utils": "^2.0.2", "@esbuild-kit/esm-loader": "^2.4.0"}, "eslintConfig": {"extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.1", "execa": "^6.1.0", "eslint": "^8.19.0", "manten": "^0.2.1", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.1.0", "kolorist": "^1.5.1", "type-flag": "^2.2.0", "typescript": "^4.7.4", "@types/node": "^18.0.0", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.10", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.26.2"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.7.1_1656787536501_0.9416828406052238", "host": "s3://npm-registry-packages"}}, "3.8.0": {"name": "tsx", "version": "3.8.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.8.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "40528c8e5d44fa98877db9dfd463b46fb1c00a23", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.8.0.tgz", "fileCount": 15, "integrity": "sha512-PcvTwRXTm6hDWfPihA4n5WW/9SmgFNxKaDKqvLLG+FKNEPA4crsipChzC7PVozPtdOaMfR5QctDlkC/hKoIsxw==", "signatures": [{"sig": "MEUCIQDRS1oapfWK9SVsvXzYLdixNGQ9C98CB6c7Kr7AhvcY9gIgRVfB3I6lr/3qMrmr3wJa+vbCd7u35CXVJxFPU1yacao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244014, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizhhHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVjQ//cmxCOKcBT74+8fCBDuA3RydAt/7mSSqmHvIKoZFJ2ShLt4x/\r\nIy9xVgxn+VPdI9513EsO72zjJoa1aetBqoMbnNXSMST4i2zTumYPP7CBO5MH\r\nNcTyGRlPTOTcLvfNqsPh5fNwh/W99u5JgDKjq1AgHXdu9Aq9Ma1Uq8Fg3nIs\r\nS67rRsbk7kzaSSpM+XvnSceckd52z/RtqNgu54k4YZtRXtWKael320QKaZ82\r\noVNZ3VATL6523k9gAX0mt1BHaAIvScAPRJ+qcDLcw8Wn2zwbvQGIMD3XkojQ\r\neac8Rezg+WDYAXjo1SjQLDhFyI4wasCCKBYQ7KHflq4iQFDKG2YIcIAzoTUH\r\n8QGO5qyz3n6YPc7sE0EeyKM2NlnipfLd2peleMbsrJbobM9nCvgix3YvApVu\r\nVqBhM+V9iVfqzBsNTuyD9gTqRdW7+EqocKoPBhDheCAuzYmQQlKlRpVkVljI\r\nb/H1b7NZAEi8UQ2FMpf67mAFmrTXpGAYnCea5c+Ou0S9FEZDQu82aBxB8kCQ\r\nyu3ANv4fMQPpA8ZWzOwh9sUliwoWgD+dV0gKRsu+476lVPS21Q67roT8oTNp\r\nhEs3EOaFhyIMh3KPTD2ybTgIAJ5cMguuz529cFbyNVGLLEylbDl6afheeQD6\r\noe9g2x+uoq8aepImzpIjwyj1KbUt+xmG7nc=\r\n=iHgO\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "a64c8be944a3871d1005c9a517da7a6312516dce", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.3.1", "@esbuild-kit/core-utils": "^2.1.0", "@esbuild-kit/esm-loader": "^2.4.1"}, "eslintConfig": {"extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.1", "execa": "^6.1.0", "eslint": "^8.19.0", "manten": "^0.2.1", "semver": "^7.3.7", "pkgroll": "^1.3.1", "chokidar": "^3.5.3", "get-node": "^13.1.0", "kolorist": "^1.5.1", "type-flag": "^2.2.0", "fs-fixture": "^1.0.0", "typescript": "^4.7.4", "@types/node": "^18.0.0", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.10", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.26.2"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.8.0_1657673799104_0.0922852970082586", "host": "s3://npm-registry-packages"}}, "3.8.1": {"name": "tsx", "version": "3.8.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.8.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "b23a362859f73a6592b12b2c8c537ed78ab29850", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.8.1.tgz", "fileCount": 17, "integrity": "sha512-YA2fDf1V9j/6qX/QSnapMmzulbqlx7FeVL6d9ySHDJoECkslAlZO38UuyFCiNPjam74hbyHbJfUF+n2ZT14KDA==", "signatures": [{"sig": "MEQCICnBZGLKGxSyL2p9Gdo+NEhaevnI+pxtMHzg+ZQ9nA5YAiA05eFrvj0vDG0jc0JTm3K0tpczEOXsU6ZcILwj+gBHtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 241676, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8GImACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKmw//ZOoF7SHdiXw4KxzqDfXACJfxovX0H8hgefh4ijLPsHmBcBDF\r\nvkFexAbdGpBEFx4cYGMagtLLXNUInACFFYHk9Xw4v1kED/FQUiJi+F5Z59oz\r\nxpSgbX7ph3SLvL3QPYZbtARZa5028dZfmlo8g7rzEJHm1IVbE7fzRNkxEyun\r\n7DIZjOAgnUehzyFAiHS0ZagZl2Y3pJlhPA7Cd2cMsjFiE+cuVNuCCewyNASu\r\n8YTXnnc04lOkgLRzc4212xiCcKejRRv6kM/tZgT8nA8mZvtm3Zde1oj//A50\r\nRgfAO2dFLzGProjkJyS0T78vhJF6tigZ9dagI5kRGcv4HxcHQ3qbs2jUBdxQ\r\nxvsGokpaMBqKt1z5zcQq8uWX43Oe5R+a9pEmEaM6xTxQP8ECl5FSueSu4Xel\r\n9Glhu11fsnqTA7azZVAIWIugEdSqt15HaUv8ijO5e78f5yWLtK4qCz09lRLU\r\nTFOmv8wH1KPWmA/saTlbRyt4DrCOs89By9M78V2KSUVmewFGeEOFUSW0MTA/\r\nIQSLZKaXb4NmtBhHPcY/LTZxpx22/4IRSlNj9wjc6b+etkJnAChdVZ4kRoPR\r\n+CpQ0z396l/0FObQG2DJVbxnHbCkZYn/MW5Wa7ykD8ROJFsJyuKzpZPHpE4S\r\n/MgiMEC2jCNwxBfWPmzCng6867SbYraEHOo=\r\n=M2gT\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "af38265c171ee18b02fe3749354268d9ed5f4fe2", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "prepack": "npm run build", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.3.3", "@esbuild-kit/core-utils": "^2.1.0", "@esbuild-kit/esm-loader": "^2.4.2"}, "eslintConfig": {"extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.1", "execa": "^6.1.0", "eslint": "^8.21.0", "manten": "^0.2.1", "semver": "^7.3.7", "pkgroll": "^1.4.0", "chokidar": "^3.5.3", "get-node": "^13.1.0", "kolorist": "^1.5.1", "type-flag": "^2.2.0", "fs-fixture": "^1.1.0", "typescript": "^4.7.4", "@types/node": "^18.6.4", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.10", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.27.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.8.1_1659920934719_0.021402197520851596", "host": "s3://npm-registry-packages"}}, "3.8.2": {"name": "tsx", "version": "3.8.2", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.8.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "5522dbe28890b63dedb659fc86fb9c5224207c5e", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.8.2.tgz", "fileCount": 17, "integrity": "sha512-Jf9izq3Youry5aEarspf6Gm+v/IE2A2xP7YVhtNH1VSCpM0jjACg7C3oD5rIoLBfXWGJSZj4KKC2bwE0TgLb2Q==", "signatures": [{"sig": "MEUCIQCFFJjP2ZvLZ12MVh/jB9YmDJzQ7AFf56O0jexKZEwCfAIgAIIf27nKdrA3CG0YLiWDZPrEtJX50YGFmIqyACdSEOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 241744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9TGkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpv/w/+LK3D1F2wFmAdAbShvDCBUHAf0gLJiTuflt7sY2JeoIRv3COT\r\nL1RnhUjMB42ckSqKVrVvCzGqRRwHoMOM/1woeIgiSGHJiWI5XKgcc4hEHBD9\r\nDPYPKH4jKlsL8bjv1aP1o0C+NufLvDZW+GekeT5AJ5mHwSl+6h1Xms/BZZjE\r\nKPR+tmL8uM2CqDdbY67hH1raRalRjyXDFX2BInZYXIPHjcMn2318SAAIcOQi\r\nDFjO47OcZIU62vhx4BRzXf6nF1jxt2RZ4RG+7Ljeos9lwa/z6zoj922VMIid\r\nfs13fkE8nhcYsbQj8xpj6Ev2I34DVnCG6tZ0DcC27CI8dfyiSBGBJxOXNddu\r\na0nG8EhsfHxLGnrbf4iiYzPDkq15tTDh/+tnN5F/dbd1gS5l8gN46eR9RmGY\r\nXufGPFVZg5DDHEYTIkdT30gmctpeYWpKBeVzSrsCINErr+u2qKBl65FWEw0b\r\ngLO+OyowKv18Yp7nz97fzJKZpbsmXEPeRaJ7Gdj+MYbUR5noHjDzM/o4qsru\r\nJ/RwkAHvNJeyXQ+N5qi0DP07EyuX11Hk8edAR1Bkb8mu156e9XjbsNZZUQK+\r\nuCpwArVH56jrQeIhEZGCwSnNNFODk+KH7RcHFPvyJRs4MAPQHu9rFqtFWdMZ\r\n5uHDvQyn2TfRCAgKC/PHeMJUUUoNMk6t+6g=\r\n=o06Y\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "2efc7684cbcd973c73458751b252c13a2495d8c3", "scripts": {"lint": "eslint --cache .", "test": "node ./dist/cli.js tests/index.ts", "build": "pkgroll --target=node12.19 --minify", "prepack": "npm run build", "pretest": "npm run build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.3.3", "@esbuild-kit/core-utils": "^2.1.0", "@esbuild-kit/esm-loader": "^2.4.2"}, "eslintConfig": {"extends": "@pvtnbr", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"cleye": "^1.2.1", "execa": "^6.1.0", "eslint": "^8.21.0", "manten": "^0.2.1", "semver": "^7.3.7", "pkgroll": "^1.4.0", "chokidar": "^3.5.3", "get-node": "^13.1.0", "kolorist": "^1.5.1", "type-flag": "^2.2.0", "fs-fixture": "^1.1.0", "typescript": "^4.7.4", "@types/node": "^18.6.4", "cross-spawn": "^7.0.3", "@types/semver": "^7.3.10", "@types/cross-spawn": "^6.0.2", "@pvtnbr/eslint-config": "^0.27.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.8.2_1660236196288_0.9171406565858067", "host": "s3://npm-registry-packages"}}, "3.9.0": {"name": "tsx", "version": "3.9.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.9.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "315738e2fa19dc5f2efd05517ed8fd3a52461acb", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.9.0.tgz", "fileCount": 17, "integrity": "sha512-ofxsE+qjqCYYq4UBt5khglvb+ESgxef1YpuNcdQI92kvcAT2tZVrnSK3g4bRXTUhLmKHcC5q8vIZA47os/stng==", "signatures": [{"sig": "MEYCIQCzsyldQ+Dd1ZtoIOZRsTWhnY/vtuiwXYi4Eme1nsFKiwIhANfp2bC0+Twk413V/dRGX1xvmScltgqDOZe4/jSCAAC+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 242380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDL+rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorJhAAl9+W1qE45yIqtbV2LfyPvUJFBSYC9Mh2FJpi085FOKwFdXgg\r\n79IxCb79rfwk3PNDusQM3uS4YMEyVS90qTTSMszXn3qolJ1ZZAFo7Sc6YRzC\r\nV6sJtahMfEzFZhqavyCQY2gIAVd6hE0vJkOsNdh1OxpE7BWforUYVM6HAekn\r\nqBHT7pTl2epatN9B4IuJhpsm7K/AYhkAYLiEuquoiLujlMhJQkh1tQFPa+p4\r\nRCa1SgV4wrDcGv4B9itEZiC5VOunpuOmzEKLDozqHuNx15iScHyJ9B9FZdY8\r\n1Zx8DKN1ZM2wgLD6Sd5rvLfD3A7shMELmtcohq8hGw0F1ns8eTIY2QsU5K5I\r\nzAmgXVt/6GxMD+pkwD0YN6viYFk+RCTd3rCyq0+grSSTUCjqcAG5yxwxunH7\r\nJ0XCCd2Ac3U7bYDK1IcbSLafNu9DUJl6H417U2QGXNzzprlPNvRpzaAwCZaY\r\naDMdoHmIUkvi5xKbNM36rpZ7qc+pDhzfGdQvtzwu0c9f1nJ5ptAoaQiERRYG\r\nOMnvzidTD2Dbx6qpu/Yz8qQYN0QaqF2TCb19v+PQr4CyDlOxI4k9vu2bmpF+\r\nTFA7P6plvSaaIXQKHh92zhlSnpTudbSfV1KeHP8xBp5g2Jkza8pj9SFBtNLB\r\nwVRVq61aPatzt+efetLJE+uyr/vBPCjq5lM=\r\n=djvN\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "afde7587b58e96153a7b243d4d0bfaf2f997a026", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.3.3", "@esbuild-kit/core-utils": "^2.1.0", "@esbuild-kit/esm-loader": "^2.4.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.9.0_1661779883675_0.5677834990504418", "host": "s3://npm-registry-packages"}}, "3.10.0": {"name": "tsx", "version": "3.10.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.10.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "8e9e25333d9f97cb7f984366c36ee51c55e4fed4", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.10.0.tgz", "fileCount": 17, "integrity": "sha512-ojkfrn6mPwdubwVRh+LXj8qgtFSoSz2bxs2Ad/0cFdW1lru/JoyDwWfnyLygETMM8qFmi/97mCJY3IguUF2XdQ==", "signatures": [{"sig": "MEUCIQCiOf26Ret+l7+kVvm2l7+5T0SpAeSU4sq9xlrYwyWEBQIgHeQbquvF0x+2ly/mNAzSEgIz4W3e1c+dyW5VFH99pgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 243974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjP8mNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrk/xAAoATsgVXOY+G+9oO2CLQV5dW84qzEG5kxAdy1j+T9cLV1F5GD\r\ntGeeSlOZnozjQifjdrzKwgkju7NvmXmKiSgH6t3qkcCHJOdTx8BAiBLw/PCL\r\ntErYt4hJ5JPHaatjZeLwPfR/iutAMs4WvgSMzOgGggOZRhDu1lAEQSWoUCX/\r\n6Io60ON7pSte9dbOWYrSS7Pt0ky33iJZudLYOekt7kzTzqhnsEN2mqHx5Ezs\r\nqZmSrs22vFCE9hfMqGECmFjd5Fipf5lCTLSNXqrU0qKmh+z3YvwgeOEePAej\r\nu8NiLzQ+6yhyom16Rm0OEiNQNdlLdixz6Y9AHrxLuqDRHb9Lo76NrrjnqfJe\r\nUlsccrRdt1TVBS5NlkMNeC1I71cK21mZjdxUncQHfeGPb0A549YAfVscvRar\r\ndaUrTX6be9lYS6wN66lQTWU1ogitz6j72XG+WAqNE9YnqIUAy2TJAva75wXk\r\n0w37Pjy1h2ug9ii6QlBQCb6yl9b6Daj+h7JRnaB0svo9Od9B68gXD/H07kBr\r\n6iHHqrKkS4p1skq/JNAWSHIi79+n2PB5weLoEFJ8H5Ezps3X1q0Qy1M9dMOx\r\nhbHTi6QxzSKhOWgXIBEchF7R4PS1hyicn18i5m8fjZcFpCLXAtP4M4rQEbvz\r\nPPCrexE+p2yzFk/BOaHFkcjAGw0lCOvljAw=\r\n=P+Cz\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "5062245a3c36822ed4d8eacd3a814611313e80bc", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.0", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.0"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.10.0_1665124748961_0.9229936579560469", "host": "s3://npm-registry-packages"}}, "3.10.1": {"name": "tsx", "version": "3.10.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.10.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "4ffb1229077f648bbf46c0ed2b098f4cd8dd6d6d", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.10.1.tgz", "fileCount": 17, "integrity": "sha512-Gh6xoW4xrdnLs6hYZydVHIQtrgmbZ/DbnJoLsYoI8MxhKAIyu8R7RyF0D5qg9UKi74Nmr4iSlijdz7Q43IGLyQ==", "signatures": [{"sig": "MEQCIAYC6cD+UHL6DS4lrFbok6POTtTkuLEXmeu0MpZcZDmBAiA2K4ZbfqRY0P/53HbcB1NNSuc/r0Ti/NyA/OEr/+TObw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQAMlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUUA//fmCSNesZBJCpZZ0hEvY1/h7GxqfaEZnqSHADN3/v/kOrY1Bk\r\nFMTm+doqhyTJ3AxSGiozZAqUoEBSTmQ54hTgBGqeMZMJfK1TbQLFAV7FRKbc\r\nLIm8SgwfVjCeyl+zNOVXRshBGv0BQJxYHFb7tYMl/26vJBCfLDukFvSP6Y+e\r\nDzJdnA2pMkdq273hNsTE8sLaXUFVUQhmm8gzLRJKtgKnd1gJlltiLV2LpUdl\r\nRVPrRYUOsklHP9UnX/FaClalA5eO1MYRUt7Ls3KlDHH4duA9uq/6UBEr2Qfz\r\nf4+9kjNEdkh2auuUzITmiTZ1ZrCV8Mmg7oY2xUfvsAASAaUQL4mxZIw4eAHC\r\nxbOOWCFSXYkGUUCHyXIj1RJxOm0Bfsd5ca6VZMVK2PNdobA5XGwxhkI7gs/7\r\nqObvbqkAQe5EXCLXeA5dmuPcdGQaSoSYHHMlbA357W3hxhd7VgvkotRN3krM\r\nXMZZPBjhqHz8uXxf58yVOBYo+luwLs3a6gTrWaHqMT96sMY8ffZlX5s81wnu\r\njLWO5MofHbb6w8TZjBeLO+vt99pY7lzPw3QJmMkyxTMcQRnONssYKMcd/UYJ\r\n/11EjznGQvW1s9SQziDnWr127io3iFj63Z55roxWm3RhsYkbm28B0LyIJXB9\r\ntUZ6T1tud/GPFhChbMr3fowVr+l05SalBRM=\r\n=on0g\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "332f24261ecb22c8a35dc5ff7babd7abaee6afdc", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.0", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.0"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.10.1_1665139493053_0.4996307503265214", "host": "s3://npm-registry-packages"}}, "3.10.2": {"name": "tsx", "version": "3.10.2", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.10.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "0779f766b904c6d182901e6df6ea5ccaa2244645", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.10.2.tgz", "fileCount": 19, "integrity": "sha512-xqXmj8Fi8Cz5MpghMN8E6OYMo8P7JMCRsd3k67ORvteHy5j6iMIHr2GqiSY9QkKTzzGwWHalRY3CvH3l0dXUmw==", "signatures": [{"sig": "MEUCIQC+c4l83nV4DqGuUl+jV2UBVugzDuSQC+xCjCStGQJLFQIgJcW42R99XDlLaauPVcWuiFpVa092I2dMP3NWTrAtQJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTZUTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmorwg/7BzaS3r/m4veKDdQ9AduTyl+RTKVHQXL36bMXdyIhspkiNdAB\r\nVmNT4zrp0mJ72XdDo/d817F8/ipUMzp7DXbY52KBECf6DAf8sIA5zGWS7Ssp\r\nhYVsMYRAg0lgWnOdAajgIQlqKQA4wxmYQR+QLphmDpYkxGiioPsxH0f+h9Uy\r\nRBFY0SbRIVVTXVe2kjzNp98HnAxHJIv9lzSHS5aknh218snWGaYF/j5G9EBW\r\nhfS7QPKpQhxDAOy0ij5Bboc6LrPbxqE39/tHQhDo9Z4hH9wggXLWCOhlTRt8\r\n1v3Ad0OcLKKSvFraxVhq0gAEaC+W+QEEdrlruNfTuPMPcdzEe1x99rC2GyQE\r\nb3L9rJoGqOWXviH7IM/D6htPxjPnMXm2SKUbE3gDh0XiWLbp0FAdgCzXkG9A\r\nC7oAMSOe0WVy+IIjDPIX55FvBScDeu5SW0BRN9dhahHQzh++4jRG0DkQETAn\r\ndcKXl0i+x61+vOEXAJnU3F+rSqhwAt9QrGziA0Xd+k/WGRYFJuRhqua9DYTl\r\nZPt57f2RZiyF/2M6693sxasD+U6zh4uI6ovWY8QV9Gtwn5zmnsMrboR5AglH\r\nr9APY7YbtKrwHZlKUfX3mBnkhoP77VaH0JNa4FHpdkbydudqjWTofHOQyJOV\r\nqqfWZUSCp0QYpUsc8hrF3yoVgYLhVJG6yAE=\r\n=+R/9\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "5aaa60fd77c69d5b452ee5c2d33827b25769e5dc", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.0", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.0"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.10.2_1666028819497_0.32193120997068436", "host": "s3://npm-registry-packages"}}, "3.10.3": {"name": "tsx", "version": "3.10.3", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.10.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "33e5324dcde3b40fd2ab37b8dd1be820ad38f04b", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.10.3.tgz", "fileCount": 17, "integrity": "sha512-yni2/n09VMOihGw6fCTKAowvQZ7BOWRVlpW9RnJPDakoHqHIlpxWieUUr5fnQa3zWoR1Jb902Un4Bv/JsKQ4RQ==", "signatures": [{"sig": "MEQCIBPpM780Wj098/OzzIw4yiYL49ic5yDEyWmdIUDpBlcYAiBKR5vEmbE0Vl36K/pmoyrPjvA2YsrbBBNAdBi1ExemEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTgGJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrduA/+K6vnk62jD86g8Aw3buzNvSWM1paw8XRj/Azm2civSYgmIiY4\r\nPysFZ2nkSMfcvi1N02yV8EjapohLDdtRlLig36VnJRjNhNHDQdXkFjOtccNE\r\nKJnmT969HXRfQFCGE3sCLEGjYee0kFr9Y8PccdihlquNTKfbyir0blFRGWkV\r\nVqYd1BCS4WpkO7Di+/fWMWCoYWQgz/J4zoqW7NAvOEsgSzloSL9rVYkXGUvL\r\nI4+zJPpyyS2Nd7+sUNp/6OdJdKnL76fHNaou8qi5pQXjRUZdD4KjxNTNqwrI\r\nX1Y3qHE4Vg0W7yp8qXuh131ebrlN9X0+ggOlItzgi9WxufwphJe+sdcnmURv\r\nAUZcU91+3uAwmQoKC9E2qPjbLJ8YPcaorXfiC1UwgBBDn+IOZZkNPYgegCko\r\nUzdPtIj4PViWcncVqs+1ZCI9YVKshlUMd/jR78/ktY96QQO0SPX2GqCXt3ie\r\nzyccwtGZsqBRRQMFPZGuGNPwCX2z3kqnu/MIIWm79ytvvUoeNNMlQyPQwtQQ\r\nTEPi5eYdN/Q3DYxq5VBXvvTabHW9v9rK/Hiwl3CuLNvvUOvPJGAV1rdtAqXQ\r\nKqK2sjYuudB5BaXkfCBhQxxltfkFEHbwTScjA5BfQjy74MtWJo9jNRymqcVv\r\nVGkaqC1DAF/9/jOZOPJwExtN4lGBy9gMjYY=\r\n=aO7L\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "817b4a5a7ca289d90610ceb7ecaaa70bea6ea728", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.0", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.0"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.10.3_1666056585338_0.9104802505894287", "host": "s3://npm-registry-packages"}}, "3.10.4": {"name": "tsx", "version": "3.10.4", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.10.4", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "f46a876747f517263027e68121335bb294641b34", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.10.4.tgz", "fileCount": 17, "integrity": "sha512-sBxeSgUAPVqRgBPk6TY0yFiNDUlva9rA+Nj46oYEd89FXVD2MFWChFhybTtq7Qh1Ies/O75mQNwpLuO7m8gToA==", "signatures": [{"sig": "MEYCIQDWEE7fuVwoqS0wBVzeNlJDvYmaZXVfxgRksSrAQIj53wIhAKbbwcUvMarPiWD9Gj7VFsdrA9GddPrPY/MtlosWPRi2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUCSIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsuQ/9ET9aXdJX4Pv2Sm+fdI9gU+KtPOEwWASxjJNchfLkxXMSrzaG\r\niUGfU3kFfAj8d5bIXC9uT/Ld5ZUUzQ07dXSo+3B1wLhghJqgLvNtALO1LBas\r\n2NHp/ajMANhWyKbHHG9p/cNPhCfJ0y+jj/BGFzTvgAqDmBQRhYibOhJv9i+g\r\nfLnosHTYNjXEi+QZaacdA1OfNwRYJOqgubhvZpyGoD3OB2bxJA5t0sra9jpb\r\nFmN9vnygGhLQx51cnDeCroyYvAvcDML3u9EPFKpyvCUW++xQhSbfMDm3wl+7\r\nkGPpDIxYa9tiRsw9meMWgKSWkOoimSpeSbpviCHVckDMhkbp81HLf3r4SMdT\r\nqOnjvD50aYmFq7GBsrJsfI4jW1G2BttYhX+aJPpg8LP1pW/Fel/5EaX32Bvc\r\nDpbuGRtehCmHTIPTD+6GcJplcf2Zp8Pby4LnbC24EOnHO3vYeoleAUJORyGg\r\nxBM0Gef1zg59rv8NXxSnpRYMAiiNi4gg0IF3dZNIVsXZnPf/U2WFUaW4XFdW\r\n70CeL2az60CLaiENoVGYxxXGIHjFCcFcAbGrFDxsYpxHUKDh7cViVY5k+PAM\r\nNBX+q5jKpXdJGSefU6EkWTibONWGriFP96AYIDYM3SSltj2E9W0VYw+wIs18\r\nJE+lhfYsnX2fJ+XiDSV9CSLTT7qmdgVqF48=\r\n=CWS6\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "b7dddfb23307e2bf72b12006a447e0e0ec43113e", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.0", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.0"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.10.4_1666196616357_0.8113410257214906", "host": "s3://npm-registry-packages"}}, "3.11.0": {"name": "tsx", "version": "3.11.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.11.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "866dd2de4b4b26d92d3e132b9795db2f65db44d3", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.11.0.tgz", "fileCount": 17, "integrity": "sha512-q+q4xxu41+AafVwvAGqtNJ1ekPFd33ZhTMXvgIpHMqv/W89efwDRE9IyjhEAZm5iTHsshKaf1BYWSk789BrNCA==", "signatures": [{"sig": "MEUCID4Gy3EV1GuF5GK2GigE1BCfMwbJEnzs4FlBrmi/JKkqAiEA6he4vrOaqUliK4qwxXbsO9ekiaHA48e2itksI2WWmgU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 246217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVdYwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXAw/9EkNYx6ggw4D/6bdsM+3yqsJASDy0pXEFuzHRSEfsz5NvYWGw\r\nVQRnslGVF/0XBStWrLCdTVJfl9ZjMiA63XsGNpKbgRz1jWsym+KymvZE+kqp\r\nisd2C0dNytSb83yE7pOE3i8RRzBJGtx33GNc30VlCwdupM8wJhLYszzCSJyS\r\nzRdrhnxXKUW/Px+lDjrHl+h6ftknSZFNV+vJ3PPQ03rLmnaHfdqXRNZaI0ZI\r\nNxRDobWBrqMfu52CcXBdOAU5v6c8G/rMr51fdI9eULHdLG33UgBwAZkiOjn7\r\naizlM4WM4Ttuex6g9EA2N/MEx9XaG+3+mLV1hTuMjwvaLRXE3hKM2U5GEsrl\r\nHrdhDfNxXibLcWnEp35E1j1dnZPLAix4QUo0m35M6PmFQokzCPPcVHCQ3mSC\r\nB/L74yTaURVHWngxb0UctbDQcBUBRxOOVQI2aThQcb/Neg//hHPoIpx9TIZ/\r\n9tx+5bBE1tzlW14blPHUBeKr7seqvE2onanfWfrzDXeQMLMwBb/4VxLG2/pB\r\nRt4voN+0hI7R4chw+XjLEQSmz5Q4ShH36EGxKPUheIeOqW9T/VmF4D01RTcy\r\nS2ii1QMyZxuJTsb3vTW+TJKHDogGaQNk6EL5kkHg7MpVCm7coo5Qx1sH96nZ\r\n9j3uskxphrDHjM4Mu6zyefyE7O3Kv043Xbk=\r\n=rfCU\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "5d8cc04c5a2252bc55bd28b46b8010562ac6d0fb", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.0", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.0"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.11.0_1666569776047_0.15082112751352428", "host": "s3://npm-registry-packages"}}, "3.12.0": {"name": "tsx", "version": "3.12.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "0df550aa4311da32a382b1f31941fe1b41cfba8a", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.0.tgz", "fileCount": 17, "integrity": "sha512-Z9drA8U1/jRJRWVTS2rOwdsCfkAShwfYBlE+6BVZsZChDsgYsEZZe44MGNNK6V6wQTTtu1kRNV57MsKRMJfdpg==", "signatures": [{"sig": "MEYCIQDN8WMHrueN3ARC3zuE8Ri1E4rKQtDdiLxRWQrs4C5PbwIhAM0lSTB/FSU025iB1X8p0mXAdZzTBBfI2ATbDUcTsvLe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjbKpuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwLg/5AViQV/8AQcHIb1uPtLqIEB1cG5cQm3y1PwhCOlOScqupDd18\r\nUK/0aNRCxDh1DTZcsfRoCH4pSRyjuS8Cbe3/6acuiYHLKg+C0j9bnjeyRyc7\r\neadsQvnTJfMMSgmxIS22DmWYlEwheVIstRy6yUhUsmMT+3QfsU6bjmlNIoBe\r\nK4F6LbRloQ82+ZmAApY7PvQ7mvBK7C9lj5xYV17155RKodNOUUq38MRgK/0x\r\n7OR/rPasCpYOyQFo/EmR9fZrOXk7T12SqzveOTgoviqd2HV3NI3H2WV0vRGi\r\nhH91imXPk1iY0sa/T4qsfMOqOI5mG0vDxLmuplix+6P6eewRlH2yqZfMHKbY\r\n9kLg+0k1nCZhjp1Me/ntg5DNLKhIQgMLEmZVkVfI8qShhJPMuq4p3V/JzTYj\r\n9AWzRBL2ciLC6JUkkP3R02B0j/Dj6tWbU61DHEtpSm7Oznnc5aL31aIGzJbI\r\ne0i2fZBHGTJb1Od2lyDc/aiGZxSzvDRxKtJiTEVmzO7cwSGrGsWgBVrSXxqz\r\nZOXsuksTi8Gvd1VcYF+MfjvXjak1FfaC+wf6Drvp3gQ/x4mmoc4kFmPw9g/J\r\nTAG4QqlMm5yNNx1+w1zsd94bCDnZU1xi5eNfBEWQalTc5UvpGz+dBnH+kByY\r\nfNXeqqeJEARS2Rs9cR1leox1//9Egtg9hmM=\r\n=v0BT\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "eb0bcc8593857174c86190a5034f32d83a4c1f54", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.0", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.0"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.0_1668065902111_0.9945887370450401", "host": "s3://npm-registry-packages"}}, "3.12.1": {"name": "tsx", "version": "3.12.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "d07532004c573bfea50a4ac687aa7270b3e34277", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.1.tgz", "fileCount": 17, "integrity": "sha512-Rcg1x+rNe7qwlP8j7kx4VjP/pJo/V57k+17hlrn6a7FuQLNwkaw5W4JF75tYornNVCxkXdSUnqlIT8JY/ttvIw==", "signatures": [{"sig": "MEUCIBE29xIbSMd9SqdQdd00zLH9NkWY39felNUkiyD4roo6AiEA7p5eyM5tEs9N0s4fuqDKmcvBoH8mo85hXI4G+5qubRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjb56hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbKg/+KIy4+gbK4c+Hdt6UMtWyQs+mbQ3vqrX4GtbyDznoqR/PbVAi\r\ngXPqaQTrV0G4IW5p5msBtVG49LB1sb5XedgN4oz54XKwikmBrsiw7J41vmT1\r\nF5i0n9HeTSfY1Z7X6J7vYR4cETJo/KjVv7G/RCx9zRrv9t+yoy1QBrfpdRJK\r\nHj3CppZmCIznrwXJDTPsK6PRX95R9PgPQYVarQld9vu8x3wK0b+8kbsJhfJ4\r\nB0ZisnbxcohdQrzme46jGhg2syE4lwU67dF2MCc6FBRWdD/HVraLF/4TGSj2\r\noi59lWdUgIUTLj1zeIq2JRigQzMGnBZ/sAs9ViTH/cT9qtyyyLpUgHrGZVAV\r\nWW7NK8uaECp0mKj7EzVjp0H0J11Fr4VizNNSJlBkCWYAzZEWsznPRTovj/fI\r\nB82eTgrapttOhQ9WWwxz8Y1iGMoNLu+i1VDv9BTm2NBsDOy2b07UQj2nEDbY\r\nBXn9nd++Q7eecx+1kKgdT9Y55rMa6ct6CZwQxQ5w/M2B8nHGAVJGFEKBIwUj\r\ngWU+ZpCHh0+Zm+PnlkN8lwoFY0mCVDQvDtC0UIGh9DD+wrFBzLezwNT7tqmA\r\ndDFsAVh2oC9ts6DxtzNOkcdiK8wjJMoqA6yAiwTz5bNalbDeJM9FX5zfb4zs\r\ncxmwVFuOrE52V7n7u6HL6ZHgvuxOq70kBNY=\r\n=RvPm\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "cb89f95ba5274833fcea033c8c818c829c5e02be", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.0", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.0"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.1_1668259489175_0.749965026173856", "host": "s3://npm-registry-packages"}}, "3.12.2": {"name": "tsx", "version": "3.12.2", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "1c8a4fa08a97739e6eedf0ad464bd8218e1a64f0", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.2.tgz", "fileCount": 17, "integrity": "sha512-ykAEkoBg30RXxeOMVeZwar+JH632dZn9EUJVyJwhfag62k6UO/dIyJEV58YuLF6e5BTdV/qmbQrpkWqjq9cUnQ==", "signatures": [{"sig": "MEUCIBbGcNMjTferxwQmviJBJCMO4Fr9+GcN0aT3LMLW2wWEAiEAovSdlZV8IcMSUrcg8tXStUvIUyTumDJAfGSAjI5boOk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju8qdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjmQ/7B08+DydrQLKOW9isTEFnJx9NuM69OxswWtdPt1kWUMmxphJo\r\nMhYgUzGRiZVJQn86+hjAl806V6HU3oLcqtOhz65UQGw3yAJLafk5qheLYuEC\r\nh5CQUhs33eiWmWzMMmizS5ax5Izgqa6YvR34Fnc0Ym8iB/UcGf2LWdkx/jE9\r\nxBECv8DwTswkLSNsY0cmEJ5P7ha4I0IhJLlCUpAq9J0Xak8gLc5pUF3Vv+8+\r\nWfZoSgALJUJ5vwG2xYTShZsASLGZ3hrZ8CdVt89NbHhRebuzciHvWPHuRwMJ\r\nR4va+BQNBDtkiIMvUTPliO+wfP04EZgPgOMzlysQtMk/1yJhLpVTNGOf8Nas\r\nd9bt18aurauccObsNbZX/EaGSZ0eRDeU8mCiiNT2IJEcYbYalFSJjk5cjuFy\r\nO/8jC+isl3gIbnQFrcz1uBRAOw8c+lvjYx6VYx8XTO0714QgATuKfLpHBnu2\r\nnfEcCyDxJBBjSWnXL7sspCo24xZ7/x3oPrT9+2JaTSahLciDfD98qNuYAn6G\r\nM0+XfvVH+urbDUXHZtqugIV8edN8ffbttMLwau7CBID4F2B/JhhcO2cRM7lv\r\nTyxh4gY7xoFLtXq1kpJwM3n6wwWNeb5XC5tcehM8kbk3B0S9gXokAoAyKwkp\r\nLNBuczTxpcyCPAbEPswbuHvH+RCH88sOsjU=\r\n=D7Hh\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "67837e80d1df992c8b85c106934697f6ae6486eb", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.1", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.4"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.2_1673251485379_0.7923223477736254", "host": "s3://npm-registry-packages"}}, "3.12.3": {"name": "tsx", "version": "3.12.3", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "b29f6c9246d4e3ea46451cd81d7cbc98f45c4b8a", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.3.tgz", "fileCount": 17, "integrity": "sha512-Wc5BFH1xccYTXaQob+lEcimkcb/Pq+0en2s+ruiX0VEIC80nV7/0s7XRahx8NnsoCnpCVUPz8wrqVSPi760LkA==", "signatures": [{"sig": "MEUCICvTtMmvaVu4shRxWsAQS3Fp3VG9Y/lChSP5PJwxijo8AiEArAeFVzA72dN0oHRZ013jUp7JC7hmXW6lnpakqig/BjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4HOHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7pg/9GAQw4gBWVdvxTWAmknN6PWAsPL4tvzPcVWcAUwNKNTUzvj01\r\nfpOZPb2jXvOZhR5AFqZinjDHZ1YXqKGf4v02FauAJArkPXVVleKfDfuA4TUs\r\nLwS3p3S2vKNnvB4QSRE+yUwivclbE6+LeCKEwfSuPDniBcwyy6zcUiFSx21N\r\nQrcT8Bnqu/lmOMdIQ5T8ybQesJJR04+/AnKP/xg/JWGyaDY1Bq8wAx+F/ePj\r\nBwP94I9UvLhFTWDZw6omVZw+pZquzBqrOWsBCVhE94CFxLyA0rlZH7xLhZKw\r\nRbpncaI0Mb7uI9fBw9DSfTSCbRUelmSg2SoMKk+p49iZoUPbOSC67+TWNfBs\r\nHHff1TaHZBWD1XAtrWbrree3oDVEUIJ4dzKJ7lpDUHauvvQYImkouNTZ0VIg\r\noMeGLLWdUPq5+qfeTu9iCkUlmOcvH2DEkIUJs4K7fdw+sOKTKoZCjHZqjDOl\r\nNeRKnUl7YUZWhv/U2k8i2YXQanC1TDGv6rOBmpJq2n8FuSp3gkQ91QIPXKo9\r\n33AlyjkpKmPiHzlq/+dwmZZx71LQ0Onimdic42xMd2J1crqYba/cHBxZl2sj\r\njCUTO/uKdPg5yVCYCjTBQxRppFCjZJ9rLFspyVAflNPjKwG0CTVUHXfJfY6x\r\ncC/oRcnJgeVMW2zXvcwDNYLQUiKTMOtbjQ0=\r\n=b4Hj\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "7a35586ea3a8b46ab0f36c305e2d2a343ff13cd3", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.2", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.3_1675654023138_0.8165820080958908", "host": "s3://npm-registry-packages"}}, "3.12.4": {"name": "tsx", "version": "3.12.4", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.4", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "afb04080850d2a125a44642d71d6cd65b621c0c6", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.4.tgz", "fileCount": 17, "integrity": "sha512-IZ0Sqm2AwB3pgE+n/g63jLNoZZXinbXfdvNWB06wdwey+/7dlOurdS1Vb5pBGa7VO9nTjAOODbQgKCgHk7JsPA==", "signatures": [{"sig": "MEQCIFoStM8y6WhMK0BCLOth3qkddoo3s6pI3yccbKIXeIJ5AiA5t+VKr+1y36OkmZk57GHJNmPLLgMk91dDAgs8BOTUiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDdUPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjOQ/+KTXwLC78swkDp1sMZYKxveqE04G0ke4y4dUblQxBBlWY1Xrv\r\n11obmY55Exs6G2shhuvf7jkz+uNxg9tEF8kZrQ/DUApBPLoDr/3UpdvY2PJQ\r\nNDJ3ihG1bcNnMEC3/rxKmE77JtQIagi1cAQ5Je5OPdFC4pRt9G/evrjrqvoH\r\nny2Qzlxjrg4Qpxs9ZfCDza0AHJ8AT1gGtLA2JZCs8ZdBspWOs1pJLAy7myxD\r\n+ScvOnWescIn3YT8s3ZOAuMioIMi/b7IdbiAKnr8bbosq57G+MXBQpx6q5wL\r\nj/n8pRwykZUsmjbQMlUBn4UygE2sgqcBIY+A3RuVeZij6dJJ599hzarUGfTS\r\ncQWG90C0kj1bTrRpLV4ulIsttdSO7+zob6IMD4+smPlJpH6QheiopZcuuX+k\r\nYuu3CLu8bbuT4XsAheZF0b6bty2GuFcyEdXd21SKX3oKZQu2DI4m+H2y7Zzt\r\nrkpXHTfSLOl0jkRtKIsTnT4+lGcqNYRZ0aywEJaOrVFuePkmDHacPUG5hc1c\r\nj6OKJ2WURcQwO30wP6EccDLsAeLhP6diUuJNJFgdjMFCwNZ9ZZ0LQd1Ym5Xy\r\nOde5qoHarxp/TSQDtAVkNnHmf2jsuRsIdHDmN0UYxfzNyKD3SEuiI5CYncEt\r\nZVq3LdFlEFNU5FYbK1bD0nF5kbc+49lRo1c=\r\n=pXkq\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "b6793b2a87327e0540ee97b2ccab46595373463b", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.2", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.4_1678628111084_0.25487686373639473", "host": "s3://npm-registry-packages"}}, "3.12.5": {"name": "tsx", "version": "3.12.5", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.5", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "6d60d3c9ca2c5f9b8d2cef2cf099867591244264", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.5.tgz", "fileCount": 17, "integrity": "sha512-/TLj30xF1zcN9JkoFCyROtIQUi8cRQG+AFchsg5YkWou3+RXxTZS/ffWB3nCxyZPoBqF2+8ohs07N815dNb1wQ==", "signatures": [{"sig": "MEQCIGHWbeWnXVQit6mxyAY8pxoD6+YVnNdzq4m462h7CKtRAiAlqOcz+7+Q/awarJTI0O/7xgxuo8xRJGq6088oX7povQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDsh9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrw8A/9EygzS4InROAdYnceNNqZG7sEKbsbBieSTH8fj5qk9dw7M4L7\r\nKcmV0LNnsD0cC63NwMGm27e3XzHSFKEiPLorBdG7ne9y/HtTQS5ttJAxe9oz\r\nmS4+ta5uaPIb250wkSriKGb+HIYqS/9Qoc/SD7s4kq1ADnSERf8ZiwARyQrL\r\nQuWUgSuHFKo2YtG3xsRiBKPKdmlSzc10aBe6HhGJIEuUCjdqFt9/iQudjs3E\r\n0Wfn7JXcM2OTYczLr8Hy+1rXjC3fOXzh9H0M8/iQQLcvLOdiOae/G7UXoPt5\r\nW7rUO0pLXLpK5jM0tjXmP3Q9z2+IXA2kb942eTPIgvyuuvdu9kxrPfm11Q5Q\r\nfb5YZX1wdETtcwCjvwhfi0dOwMRhJJ9p3wHBVt8coZz3PWF18PY9k5h70v1s\r\nLKPHG2P5dqGwDE58/2bJ2ru4n6BOPYSPhsjQnTfF53zop2SqskWGCX0fXMaW\r\nseZ+BBKWMtvF0hJeLBXGmXbeJrSHr79VIuZH61AayoRwZg3ncHslnwfhnRQo\r\nzjd+ywm40N+aL5yPf+BrEc+LmRvaHaLB5+mOOjFE67XYWUkTk9qRb3ZpcjjM\r\nVL4nd6mUve8rbrWqhyorNq3QUnL84SKDo7qzTpRf2IF2CPAvs9AnYgPhgZ95\r\nRUuo1GJZjV218eL9Wgl0nE0Ea9+MRBJJ0G8=\r\n=aN+t\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "afa3c78ebd90d4d9bb6270ad32997aaf585a1f04", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.2", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.5_1678690429045_0.8972711418738006", "host": "s3://npm-registry-packages"}}, "3.12.6": {"name": "tsx", "version": "3.12.6", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.6", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "36b3693e48b8392da374487190972c7b80e433b4", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.6.tgz", "fileCount": 17, "integrity": "sha512-q93WgS3lBdHlPgS0h1i+87Pt6n9K/qULIMNYZo07nSeu2z5QE2CellcAZfofVXBo2tQg9av2ZcRMQ2S2i5oadQ==", "signatures": [{"sig": "MEUCIHQEoeuIJPSvkQNM8xsLC4EeK1CCwetYv1avg7CpRD8hAiEA4spVYUvvwtIajxW/2fEF5HgMR1Azo8pY01ImLWGus64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGJexACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5og//eWL5eyt5iJOUDckdyxZko74gWXW185/wL/m8MrVVx25XXl9/\r\nqbqBHJ0RYzxxSH1M86w8riEQIYuuzxQX/UyoKJdzSW04seoVATqEgEGN965I\r\nq9R752/C25kobYN+ntMW8/kMbC2gQfEthRhpFmn56cCQmI28AMjJFOWvkSkx\r\ns0XQGTHqMJBvg8fwjb6b3URs8tP1jgbkJ6K7KulWxvB65zKwwNh6Sy8tBFvH\r\nAOOftAEcjAK1TiW1/x0NVJT9onponyL2/146Z9iRugl7rQoy4pP3kKLsLEOl\r\n2fDQCBbJa9u6E/GU/Emf2smFVZFoaeXqUtys3Z5syLrHkBZ233tN7HsUEXs9\r\nqaIhZUsgJYI3qYLhp7joQaeMYwXwcXqb4+R1N9F141tQF21mFonB6bUV6a72\r\nF0wL74u0/GqdEkOEe8SqIY4TdqOpA2wkwpvMBuB7c7ZJcy6tazAV9TcSVEPV\r\niAsmOlpzHeEgMeHoeZPYUZ3tJWSw6pyYpMf3GBYFG8EiTp6UdNbxDKoboiLu\r\n3khKD5SPxeybt/rGB4WdXmcWeS6WgHL4V9hm6sPEDcPQBNcCsUhO9q2Fog8C\r\nIptSlWj1u5ATRfk64xDDbqH9s1pYBohSOJga8QleFBgtcrmRvSJ/NiqKzGiF\r\nyT4IgGB+6jXn716RvbHB+Qohpdc6LY9pYiI=\r\n=Mzbx\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "8e1958db41addf52bbe10505099b9e8ed223e62c", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.2", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.6_1679333296558_0.3233424749200138", "host": "s3://npm-registry-packages"}}, "3.12.7": {"name": "tsx", "version": "3.12.7", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.7", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "b3b8b0fc79afc8260d1e14f9e995616c859a91e9", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.7.tgz", "fileCount": 17, "integrity": "sha512-C2Ip+jPmqKd1GWVQDvz/Eyc6QJbGfE7NrR3fx5BpEHMZsEHoIxHL1j+lKdGobr8ovEyqeNkPLSKp6SCSOt7gmw==", "signatures": [{"sig": "MEUCIQCj7kInNt/oRXUcdK9KF5h7ZRrNAC/DQL8aSHoU7ob+qwIgDG3EDaYPDlifYVZ3K6v3ijT3eA1eEcXv4iBimijfHIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245537, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTbqnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpY+BAAkGEzGr7xEgcHFQ/lmHqnAKquIkFp8h4aTIUiIJF0TeGYwBlz\r\nHQSzflMMKK6bjAxA7fHSVhHcQfJFGRoihAhR70BCCWPBdA+fFN1KJqWL6xz+\r\n7M1jI14fWgc1CMq7vpkzHIazytrtY32J+/9qnQ9jBSV7J3JhTEyX49q2Xgwi\r\nP3DWwZgaJ1eBxHHh20gmkU5uYBXy+5+NWSENEFHMIEELPx67L8ltsF4TQZBr\r\n6DIUBp6PIFrnHylSrIJBOt2RWSIzwza4POa/qzXw3p39E2rcRZIvayetbeXR\r\n574U0Av4bDinBW7BYlb7lxrGWmc5K7/hcsGRWij27XqbHo03hZcthYyCOY2L\r\nri35Kl1KD7dvavHMN0tW+m78sLLzdV9cYTXZb/fTAwMJWyPzl3WydboAqAN1\r\nkiwQ4lMZfUl+4GolPz97aNVP3aVfkH1Z5YoU3hKBbhba2nBt0TFrfqemhT+F\r\nc9Duo3IlYVlFqLVo+N2e4MQRs5vdptdbIPEc9PYawSMpP+b9GzNcEphmMwbl\r\n2wJrg/WhTAj1RNENLb7Fo12CHF05PjETQWiv7qBLnOfZqjJ64nqCRxAYxKnO\r\n9LUA1biquECzO1JlzNyL3pZaev//NKvJVJBMvotbmDb1UPynFcv2AdcxNRYD\r\neHFx+70tfA4waZDdr4q9oOFEI7RV0L85fzc=\r\n=ge1k\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "5366c311c81d63b081af6ec1795463e747b1ebf0", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.2", "@esbuild-kit/core-utils": "^3.0.0", "@esbuild-kit/esm-loader": "^2.5.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.7_1682815654886_0.13797544307194598", "host": "s3://npm-registry-packages"}}, "3.12.8": {"name": "tsx", "version": "3.12.8", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.8", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "e9ec95c6b116e28f0187467f839029a3ce17a851", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.8.tgz", "fileCount": 17, "integrity": "sha512-Lt9KYaRGF023tlLInPj8rgHwsZU8qWLBj4iRXNWxTfjIkU7canGL806AqKear1j722plHuiYNcL2ZCo6uS9UJA==", "signatures": [{"sig": "MEUCIQCc+tzVKH3XwjaDtB9rCEKvZ1d/CNTNmODx2NE/GNw0YwIgfVhX7HAxN/kNJjEn8V6eVKRSoinx6JX+P6Risj2UIw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249762}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "e9116f906c87244034fcdb6827fd7ba066748491", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.2", "@esbuild-kit/core-utils": "^3.2.2", "@esbuild-kit/esm-loader": "^2.5.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.8_1693566621676_0.9409335399444347", "host": "s3://npm-registry-packages"}}, "3.12.9": {"name": "tsx", "version": "3.12.9", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.9", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "026e920d91930f005ab57cf2fbf1513292e1bac7", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.9.tgz", "fileCount": 17, "integrity": "sha512-N9Cpuiy55IcemQ2kouH1ZqDvjUa31mkhIYmnJKnvDp0sLQrK9MUrRuN/XzW70iTk5a2kX2A63wLYWORwa34vug==", "signatures": [{"sig": "MEYCIQChUt1pEsEYKwsBfKzbJhqX5pFfC6SGNExZwiCoo8+AWAIhAL/z44TXzzq3+1pDOOc6J80ITCsviZP8eDbaTv8KFNAL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250396}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "f295c506acc30ba9d88e01e0e8530db81a3dd36c", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"fsevents": "~2.3.2", "@esbuild-kit/cjs-loader": "^2.4.2", "@esbuild-kit/core-utils": "^3.3.0", "@esbuild-kit/esm-loader": "^2.6.1"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.9_1694523105264_0.30700693246184096", "host": "s3://npm-registry-packages"}}, "3.12.10": {"name": "tsx", "version": "3.12.10", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.12.10", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.js"}, "dist": {"shasum": "073b7c8368c61dd6b816523eb203e6c8099c27ec", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.12.10.tgz", "fileCount": 17, "integrity": "sha512-2+46h4xvUt1aLDNvk5YBT8Uzw+b7BolGbn7iSMucYqCXZiDc+1IMghLVdw8kKjING32JFOeO+Am9posvjkeclA==", "signatures": [{"sig": "MEYCIQDllZgu6aFqWW0pw0aO7CGiHeRjb1xGcx6c4Vtwyn9CwwIhAMa+LbTey4yCZbcwjs5MpCU9yjmJ+tfBhIy4oi6hAOfv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250399}, "type": "module", "exports": {".": "./dist/loader.js", "./cli": "./dist/cli.js", "./repl": "./dist/repl.js", "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "ccde4657810931507209da3b4f2ea85bbf525d2d", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"@esbuild-kit/cjs-loader": "^2.4.2", "@esbuild-kit/core-utils": "^3.3.0", "@esbuild-kit/esm-loader": "^2.6.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.12.10_1694608488614_0.48631514479744586", "host": "s3://npm-registry-packages"}}, "3.13.0": {"name": "tsx", "version": "3.13.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.13.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "f860e511b33fcb41d74df87d7ba239a0b4012dbb", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.13.0.tgz", "fileCount": 31, "integrity": "sha512-rjmRpTu3as/5fjNq/kOkOtihgLxuIz6pbKdj9xwP4J5jOLkBxw/rjN5ANw+KyrrOXV5uB7HC8+SrrSJxT65y+A==", "signatures": [{"sig": "MEUCIQCNO1uyCE/dCtbo33a59/ltRPJsWSBp2aG5crEFCqGZKwIgeSfa6xP8r6Njl8UxLzgfR3lh8vggVz9D7Ac7+krCHU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 386855}, "type": "module", "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "e46366d2308afdf2dd197165854ff48f94a4b753", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.13.0_1695563708666_0.44394130091227835", "host": "s3://npm-registry-packages"}}, "3.14.0": {"name": "tsx", "version": "3.14.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@3.14.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/esbuild-kit/tsx#readme", "bugs": {"url": "https://github.com/esbuild-kit/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "be6e2176b6f210fe8f48124fb6e22e0f075e927b", "tarball": "https://registry.npmjs.org/tsx/-/tsx-3.14.0.tgz", "fileCount": 29, "integrity": "sha512-xHtFaKtHxM9LOklMmJdI3BEnQq/D5F73Of2E1GDrITi9sgoVkvIsrQUTY1G8FlmGtA+awCI4EBlTRRYxkL2sRg==", "signatures": [{"sig": "MEYCIQDTGi9TiFvG2B9ToQ7I35QGYdOBvyBEO0gs6yw3iOVu0wIhAL+A+pF9dHI+cS/3t3Pg//lj5v+eiD1iV9rgXJilpwJI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 388723}, "type": "module", "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "7e916f5a1db5b21589cedc1dc91b0c498f87adf9", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/esbuild-kit/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_3.14.0_1697579944824_0.9075101356302893", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "tsx", "version": "4.0.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.0.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "08675c880204de55cfd6a4216f5f52aa899432db", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.0.0.tgz", "fileCount": 29, "integrity": "sha512-jd3C5kw9tR68gtvqHUYo/2IwxaA46/CyKvcVQ4DsKRAPb19/vWgl7zF9mYNjFRY6KcGKiwne41RU91ll31IggQ==", "signatures": [{"sig": "MEUCIQCer1Myo90fwXfwA0+zYo/HxSpKPssd2Q7mL6KP6mYWPQIgZ3Sk3xvaG+0a6Wob1QnHmvCRU+tPIeSj0qHqZQ+kRc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 387828}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "2995601ce5fe7d91608833f3ca9a04cab3a54f59", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.0.0_1699516300463_0.9261202620964288", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "tsx", "version": "4.1.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.1.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "d63aa74aa23a82502ea31704760719ba26938434", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.1.0.tgz", "fileCount": 29, "integrity": "sha512-u4l17Yd63Wsk2fzNn1wZCmcS9kwJ/2ysl7wuoVggv2hd3NjLA5JQPpyJMXoWSXOwOvoQUzNcu/sf/35HEsnXsg==", "signatures": [{"sig": "MEUCIQCFBLQ4UYv1nbIZ78LHeV7Uq6O3rgE/Qz/cdF10MyB22gIgBg7efzNQI4tUiz1WyGA08uYU+E0wnzksm0N36pk8r28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 387367}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "bf033b0dd759ba51edfd451e3cb5c90f32f11190", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.1.0_1699615677148_0.8155865310079373", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "tsx", "version": "4.1.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.1.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "cd08732708aad17b28c28358c341b1d9d0ed21bb", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.1.1.tgz", "fileCount": 29, "integrity": "sha512-zyPn5BFMB0TB5kMLbYPNx4x/oL/oSlaecdKCv6WeJ0TeSEfx8RTJWjuB5TZ2dSewktgfBsBO/HNA9mrMWqLXMA==", "signatures": [{"sig": "MEUCIQClg434oPu57fMA0sXLIaF1NC4U/et2F3S61aw58K2POAIgcFxh5kFMpJ1X9S9y717kNcTOjbDHhpXC1FivoneSh3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 387369}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "0039b605a4c6875f2b3308c99dada684bb16beff", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.1.1_1699671584851_0.3978416972633503", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "tsx", "version": "4.1.2", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.1.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "32ffc80efa6f77748928293fa8f77fcf2b90feab", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.1.2.tgz", "fileCount": 29, "integrity": "sha512-1spM1bFV6MP2s4tO4tDC7g52fsaFdtEWdO4GfGdqi20qUgPbnAJqixOyIAvCSx1DDj3YIUB4CD06owTWUsOAuQ==", "signatures": [{"sig": "MEQCIBID8kyM8Ten3mpD2fjuwaKjMBOwcBLVHH0kVzKxsTGxAiB9AiS14VlHm9f33clv1j2BDWVg/Spw6H2KcUSmBTUlmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 387383}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "6eac49bb78e812bb75af73bf96e7c8d7ee1c7269", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.1.2_1699943165903_0.5901986448332885", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "tsx", "version": "4.1.3", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.1.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "afcc6e26a0395a9287a37376b510c0d8e97dee25", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.1.3.tgz", "fileCount": 29, "integrity": "sha512-DLiTy1eri4nhqgVVy+15YKC6Ij2BMFxGdDMkVrSDkNuISUJLv7n0NgZpFLpdM+qmwXar34XllgYi4cxkNMbDwQ==", "signatures": [{"sig": "MEUCIQCHWwLreVhNEmDsIyNXv+vWIsh2qD2hc/J0dBqn+OebawIgQEGwDJdwv112k0ODL+QFIF5tQE23EYvWoHfWPSG1Xbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 387588}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "497f69a65bc12b961c1114cd036063721cf6aa94", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.1.3_1700216946506_0.7203942334367088", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "tsx", "version": "4.1.4", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.1.4", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "e8c18cb5e890b410915d3cfa92b94d5e3147f465", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.1.4.tgz", "fileCount": 29, "integrity": "sha512-9X7uBCIyUsvMzIH+o8m+5o/5eL461cChCF+XUtOZsPr1a4pZx2lTQx0Muu5G5VwJWZwAGKBe3sJHLk82BENAVw==", "signatures": [{"sig": "MEYCIQCb8nSYIEw8fa9FAIvAdWaqy1esmRh2msFtcn+qRIbQzwIhAKz0kfxXYnJcPKE1bry82zMyQqNRdS5sg7SLKr9uGaZM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 386666}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "4d3d33cf1692c81fad6ce2a2b18b6dee94d3f2c6", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.1.4_1700376461243_0.6116733330516557", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "tsx", "version": "4.2.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.2.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "cc7d62bbea50b98ea37341b55d86cd4872500a27", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.2.0.tgz", "fileCount": 29, "integrity": "sha512-hvAXAz4KUYNyjXOjJJgyjT7YOGFUNLC8jnODI6Omc/wGKaZ7z0FvW5d2haqg1GLfX49H3nZOpLYRlHMYGI8Wbw==", "signatures": [{"sig": "MEQCIFjj0aUAPAX8N72TEyCFo614h2HkWwMXLSNVf4B/gIkTAiAmb3rjN2R/UHmpiBu9zfmRsvI6IOUCN5g5fDfh5xB/cQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 387540}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "9233f6799f5e7e5e595156b6d89268ce4b61991d", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.2.0_1700549894566_0.07436384181901445", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "tsx", "version": "4.2.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.2.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "ea37cdc0cd6f47e2e13d763f373b292677dcd555", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.2.1.tgz", "fileCount": 29, "integrity": "sha512-rvRrYn6Q5/97TjafXgye7/yrR5oEBlKgOfNXcqpZ87Tu5lh7DFcq/VtznTbpbnV7XMj6Ghvf8j8PlYTQse/SKA==", "signatures": [{"sig": "MEUCIFAGaEHxDjYNLOYHyOE/YkBiwG56f91aLEPCeXLMK3gyAiEAvA2pwYt0GHL0jkKEmg1YeioBjx1gXTxGw7sOmr9kt90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 389492}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "7a0eb7e826c4330521298f2833d03f74431b0ff1", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2", "source-map-support": "^0.5.21"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.2.1_1700663883646_0.8582664692708097", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "tsx", "version": "4.3.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.3.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "81cd6f29c74ad7f794f96c69c1ce3e5bdb4e1748", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.3.0.tgz", "fileCount": 29, "integrity": "sha512-zalfbBdr7tvYok5sSbnsv4uL+DhT1wRZwbWwuOXjhH8YtJxN2bpl6lpXMxuPThMAKzZ2qgrhuf5ckq/uSsm3CA==", "signatures": [{"sig": "MEYCIQD8zenTI/u+fMhR5NqwsYOJM5CYnj5BVTQQHLPoFHW6iAIhALEsGOybsfN7pr/fs3U1X6ZR7QgUVOISJarHp+OFdGSj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 388518}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "15b4277502db96ed3e6028c1ba1bd6e9d491f535", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.3.0_1700673355080_0.9319429724624086", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "tsx", "version": "4.4.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.4.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "01c4ff94f7f9186e34c0b37ba3b34c7546df1b3c", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.4.0.tgz", "fileCount": 29, "integrity": "sha512-4fwcEjRUxW20ciSaMB8zkpGwCPxuRGnadDuj/pBk5S9uT29zvWz15PK36GrKJo45mSJomDxVejZ73c6lr3811Q==", "signatures": [{"sig": "MEYCIQDReQLmIaHJ024XmDZct6QkmUn2weSKhGofMtXX5UdX0QIhALl5d9tLMJj/juWf10qv4UKoDTvxxgckvM59wfzEVQx/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 388915}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "af1b911c0ac3bd13e12a8b71588b2178bf73e345", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.4.0_1700737819462_0.5191845332798257", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "tsx", "version": "4.5.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.5.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "47384fffbd9d2e0273cfa6cf8b4caa4bb83dc742", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.5.0.tgz", "fileCount": 29, "integrity": "sha512-hgxdziy9KLaHh9KE+a6tIZFP6kb0MLq/1D0sJVifbGP4QVEYhy6+2FNn7MyCm1pMc63p9CW/L1OzdqTNPxs6rg==", "signatures": [{"sig": "MEUCID9hSeFNs3j9WF67BARiYRy+IpxgUIpdB0/AAO+I3j8PAiEAlZXRiqN+yhKLGg0H81LFQpa1OP+mJfzdEUJpZv4ZyMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 389817}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "737dee5eaba8dbf43f91ccef6ffd7c2bc1258155", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.5.0_1700861543580_0.34287904925670754", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "tsx", "version": "4.5.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.5.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "4c387c6b785dbe5332d79b89cad9771de39e0bff", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.5.1.tgz", "fileCount": 29, "integrity": "sha512-/5KxoXKkiKnlQm+adAMOgfGP0/QEmdE6fL9/8LxtWYJlzaFx89ou3eqnvN3KDgm+jZJOvsg0klOFymJ4/k00qQ==", "signatures": [{"sig": "MEUCIQCBmxmvsE/T5nFjd3RWa5B6Y0bhgHk3qpN7Q2QK0ZZvWAIgJ5oZw+wluJTjmH/eWdVD/oAkFcjyPwtdEvPxSLAvp5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 390198}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "3b17ef6126cf821dd1faa77f77140a61c74b8bbb", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.5.1_1701128167469_0.07620767268121154", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "tsx", "version": "4.6.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.6.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "d899e0d8725ec2a6db6d9315105bf09a004b8c64", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.6.0.tgz", "fileCount": 35, "integrity": "sha512-HLHaDQ78mly4Pd5co6tWQOiNVYoYYAPUcwSSZK4bcs3zSEsg+/67LS/ReHook0E7DKPfe1J5jc0ocIhUrnaR4w==", "signatures": [{"sig": "MEYCIQCp09yJaDZOpKgReRF+lrU1KTfWiTsqLGVrwFK4Wc000QIhAMWgMchDd2a32rMPVTiRZRXopuRD9hwUeK9VjYPdGKlh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393442}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "4f4a99ae17768bbba49b9be42cc1700a4142dc73", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.6.0_1701129178273_0.08877464217086128", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "tsx", "version": "4.6.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.6.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "fc5456aabace99733eca23b42f223771a0ec1909", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.6.1.tgz", "fileCount": 35, "integrity": "sha512-OQ4TAPHXAPUo/NZAmmIybl0o8LFOTlycQxFepLBAp6EV87U88fOKYGCQI2viGAEOVU9UW/cgQcxcOMnfEKVY3Q==", "signatures": [{"sig": "MEUCIFFbLSSX6Bo7ggkz1tK4hMjIHs/SRBJNAshCBJCDI85QAiEAw8ENWUrGlzwx5596WIfGac7Mtzqeq/bmpjF+km/UAKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393554}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "f4f19e0b87ba00a8bc3a701f2416ed307f9dfad4", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.6.1_1701310197406_0.3489201573989955", "host": "s3://npm-registry-packages"}}, "4.6.2": {"name": "tsx", "version": "4.6.2", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.6.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "8e9c1456ad4f1102c5c42c5be7fd428259b7d39b", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.6.2.tgz", "fileCount": 35, "integrity": "sha512-QPpBdJo+ZDtqZgAnq86iY/PD2KYCUPSUGIunHdGwyII99GKH+f3z3FZ8XNFLSGQIA4I365ui8wnQpl8OKLqcsg==", "signatures": [{"sig": "MEUCIEh5jsOlyRHkYMxMzS6fclu1anaa3QX+ikOv2XPm6gAxAiEAtwbwQzehDeBXhKMmUayZj3L2lsS3KamksTc064WyTcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393811}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "d0ffc29e022c1c876fe5b5a4d9ecb6d1a4fabb23", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"esbuild": "~0.18.20", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.6.2_1701506926490_0.962271777846045", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "tsx", "version": "4.7.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.7.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "1689cfe7dda495ca1f9a66d4cad79cb57b9f6f4a", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.7.0.tgz", "fileCount": 35, "integrity": "sha512-I+t79RYPlEYlHn9a+KzwrvEwhJg35h/1zHsLC2JXvhC2mdynMv6Zxzvhv5EMV6VF5qJlLlkSnMVvdZV3PSIGcg==", "signatures": [{"sig": "MEUCIQDiSEyyKd2b5gBv/lrjYHwKQ/AvsROtqf1KBKoGgbjrXAIgdDB/PmMRxV4g8nJkw1/WHHgBkLF+T08lXVtPn/o1b7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393583}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "8c778919496a8164ec63d0b488c805bce1aba839", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"esbuild": "~0.19.10", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.7.0_1703000996407_0.7628795709806366", "host": "s3://npm-registry-packages"}}, "4.7.1": {"name": "tsx", "version": "4.7.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.7.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "27af6cbf4e1cdfcb9b5425b1c61bb7e668eb5e84", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.7.1.tgz", "fileCount": 35, "integrity": "sha512-8d6VuibXHtlN5E3zFkgY8u4DX7Y3Z27zvvPKVmLon/D4AjuKzarkUBTLDBgj9iTQ0hg5xM7c/mYiRVM+HETf0g==", "signatures": [{"sig": "MEUCIQCsH++UpIEok5orA10G5wwyNouwkmt3fru81rd5QTvrngIgfoaZKLl6R1iRjyKwme6xANPd5PDkaSgRKlYlj395zyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393521}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "2c829258a6ca238b7a22c85cc8a09a531267d472", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"esbuild": "~0.19.10", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.7.1_1707526892006_0.8299856028584136", "host": "s3://npm-registry-packages"}}, "4.7.2": {"name": "tsx", "version": "4.7.2", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.7.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "a108b1a6e16876cd4c9a4b4ba263f2a07f9cf562", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.7.2.tgz", "fileCount": 35, "integrity": "sha512-BCNd4kz6fz12fyrgCTEdZHGJ9fWTGeUzXmQysh0RVocDY3h4frk05ZNCXSy4kIenF7y/QnrdiVpTsyNRn6vlAw==", "signatures": [{"sig": "MEYCIQD7FZ/ptgd61DtyU5GvYGDxjQLJcPlnYQXjiiu6pIAsLQIhAPJHq7hspbvqkIYx2tD8Q9fJ3BNggrOzq+IFqSn5bvRT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 391513}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "1a10da7bf9d0ca66ced6c897ade8f5f9aae0268d", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.0", "dependencies": {"esbuild": "~0.19.10", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.7.2_1712218641205_0.5989722594595712", "host": "s3://npm-registry-packages"}}, "4.7.3": {"name": "tsx", "version": "4.7.3", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.7.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "c32f3f5cb928708a5c6becf617e432b395999242", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.7.3.tgz", "fileCount": 35, "integrity": "sha512-+fQnMqIp/jxZEXLcj6WzYy9FhcS5/Dfk8y4AtzJ6ejKcKqmfTF8Gso/jtrzDggCF2zTU20gJa6n8XqPYwDAUYQ==", "signatures": [{"sig": "MEYCIQDtGn+eczYjkMnD2MEJ6F7xv4tHJTFi9cX5KJcw9AvisQIhAKdmHgKzFwS+e+8+8gjd0rD4FstNyQSLgUSX6D2r+Wd1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 380173}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "bcc5f639871f45da832018c8c9a679707d9ab1ef", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.19.10", "get-tsconfig": "^4.7.2"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.7.3_1714041279133_0.1382184624963576", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "tsx", "version": "4.8.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.8.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "b548e20b872c020060a62ca3d3a211ae163aa31d", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.8.0.tgz", "fileCount": 40, "integrity": "sha512-lp2y1/L03n7HUMujPgJQ/BwLDT4QEPPY5gBsDohsx54o0d0FqC8uL7QeQwv29ZVrv6S8rH8nzQ5cPn5ry4KBZA==", "signatures": [{"sig": "MEUCIFT5Sm+xF/HOlVRPvxWQI+ANjly+ILaJDeeMUkpjfAbTAiEAn1Zly7CUftzEy7TnaOCbYZX1W+WVa1j6dRWRIYkEsOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 388966}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.ts", "default": "./dist/cjs/api/index.cjs"}, "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "99ba136afc89de486f99dc41562fa5f3a033d6b5", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.8.0_1714529437083_0.8582224379899472", "host": "s3://npm-registry-packages"}}, "4.8.1": {"name": "tsx", "version": "4.8.1", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.8.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "edca9c16f1f2327c8a1fcec95616889999152096", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.8.1.tgz", "fileCount": 40, "integrity": "sha512-xkdE3/l+gZPHKfcZDCeCrCj2aJS5V8WFF2lPmMfj5ZAPa9RNN74XQsXA6lnPDe3uToGMCZ0ybytLvoP2rv8f5Q==", "signatures": [{"sig": "MEQCIFn+iEEieUlwLZqVMu9iwKyxz1p9bTuIofeGqvvuzcs3AiA4lW4cUZQdtQWwoJOvWfVEv2o3DEpB/8ZqbDDE0pGWLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 388818}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.ts", "default": "./dist/cjs/api/index.cjs"}, "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "084dec008149b29c0f56124d4a1400f2bb98f101", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.8.1_1714538822410_0.4040598517572864", "host": "s3://npm-registry-packages"}}, "4.8.2": {"name": "tsx", "version": "4.8.2", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.8.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "0897b1ef4b5aca4b2bc6803e5058a2b2633fce4f", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.8.2.tgz", "fileCount": 40, "integrity": "sha512-hmmzS4U4mdy1Cnzpl/NQiPUC2k34EcNSTZYVJThYKhdqTwuBeF+4cG9KUK/PFQ7KHaAaYwqlb7QfmsE2nuj+WA==", "signatures": [{"sig": "MEUCIQCcGQKthp8B5GtD0H2/Ug9g+lw5aoL2WKCVHueVaa7HfwIgElCPiX1GUChr9A6qng9zT0iIv2T7CLs9wLvJ3mggRuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 388819}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./preflight": "./dist/preflight.cjs", "./source-map": "./dist/source-map.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "4b1f03c29000a28bfdbc1247eeec131f39078246", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.8.2_1714548112094_0.2478744238589219", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "tsx", "version": "4.9.0", "keywords": ["esbuild", "runtime", "node", "cjs", "commonjs", "esm", "typescript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.9.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "08d7ea58df19d87416d26cce8caf93d0c3619852", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.9.0.tgz", "fileCount": 45, "integrity": "sha512-UY0UUhDPL6MkqkZU4xTEjEBOLfV+RIt4xeeJ1qwK73xai4/zveG+X6+tieILa7rjtegUW2LE4p7fw7gAoLuytA==", "signatures": [{"sig": "MEYCIQDI6UornvcvkEQ44Ifpy6nIXPoUYroxq0RqjqPxiSl+sQIhAM4J1bmR29Yi7RC986g+GTg1UfB85DJGyCZcFWuUm2a2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 392928}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "4f515ab0bb8f3c8c610dc57556b978cbf90a5560", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.9.0_1714724234915_0.10884867027584688", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "tsx", "version": "4.9.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.9.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "e8eb1ce2766ce9067d577fba237a3cf884b229fa", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.9.1.tgz", "fileCount": 45, "integrity": "sha512-CqSJaYyZ6GEqnGtPuMPQHvUwRGU6VHSVF+RDxoOmRg/XD4aF0pD973tKhoUYGQtdcoCHcSOGk34ioFaP+vYcMQ==", "signatures": [{"sig": "MEYCIQDp5PkeQnaVFpE30CtslUfebJm1WZkc34osFHc0ip+A6wIhAN/vPD1EosdEuBEQePogIGIoKNOmYPk4QGr25qELVzgr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 392967}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "96bc59680c7300ab6f4f740da26fa16e1970c7c0", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.9.1_1714831158146_0.5506762396596692", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "tsx", "version": "4.9.2", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.9.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "9a7caedf02da5382ac3290ff1b760c915c96809e", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.9.2.tgz", "fileCount": 45, "integrity": "sha512-D2<PERSON>oeJ8nBsrqQyMrikW5X9CaelelZTx/5Nm8l94NhDAOsv6dkcEoFOq7232upStYPuEE5Z4I8IpYi4RomDzKKw==", "signatures": [{"sig": "MEQCIEpSHWr+WQlX10arq118FcC1TfU3mLxJVUvG9tLi3tUwAiBbkVk3dpt1wQFpi/SLmR6zohcGgqlv1/yYBokyIFlLgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 392996}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "3a0ea182d7be3eae2a34e1a028ae6a2e4e9c94fb", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.9.2_1714955904277_0.38254717204873034", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "tsx", "version": "4.9.3", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.9.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "801ca18ca22b3d2f7acd89d4b888aa2425ea1302", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.9.3.tgz", "fileCount": 45, "integrity": "sha512-czVbetlILiyJZI5zGlj2kw9vFiSeyra9liPD4nG+Thh4pKTi0AmMEQ8zdV/L2xbIVKrIqif4sUNrsMAOksx9Zg==", "signatures": [{"sig": "MEUCIQDEuzlbYQ9f45Rs2itGA4K7B6enZNh2o/DM1opqjXpjGQIgLRDdzmb5sDAAq2rnL2zfN9D8WczBQUrvAI47AsO8UaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 392972}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "8022fcfd480f21c53d4d351ba4bfbb611145d396", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.9.3_1714959380934_0.8180047347213235", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "tsx", "version": "4.9.4", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.9.4", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "a7c37d944f0d88743c8b6bb030adb3cfc488435a", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.9.4.tgz", "fileCount": 45, "integrity": "sha512-TlSJTVn2taGGDgdV3jAqCj7WQ/CafCB5p4SbG7W2Bl/0AJWH1ShJlBbc0y2lOFTjQEVAAULSTlmehw/Mwv3S/Q==", "signatures": [{"sig": "MEYCIQCsZSfhXNiOstKPlPvMcxNdg6R8MeXW94IdAirTA/oCcQIhAP3k0L3LJlU9Gu9rwvDTpAIS99eX/ay48HXkMaINxtZQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393028}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "48f0a7552fb6743fa06b63f6f600a9af95cb19f9", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.9.4_1715356977432_0.3415510405594069", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "tsx", "version": "4.9.5", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.9.5", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "570b91748ec5feeac08c338623cef432a7c45724", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.9.5.tgz", "fileCount": 45, "integrity": "sha512-bV<PERSON>owxL5sqd5Pxkkgy6M9IGpxyY9+SPxWPNviZyqNBT/i6/X9EHbmuLDUw/6/Tugw1ca8VXNDtcM/amQjXcEOA==", "signatures": [{"sig": "MEYCIQDtAVIZP595hQhQXfbGeMhTgNSRuo8Q2ag6sElU/NF+IgIhAJRXNzrB2sOwx1E8jFP8cLMitIkKt0fu4T+NvbJqwh9D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393356}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "dae9f0d62dd06591ad2b7abb970e412a15f417a6", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.9.5_1715433730064_0.8308821616394177", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "tsx", "version": "4.10.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.10.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "c9efb353f59219fe9056d4c271ef2f77563345c0", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.10.0.tgz", "fileCount": 45, "integrity": "sha512-Ct/j4Yv49EFlr1z5CT++ld+BUhjLRLtimE4hIDaW9zEVIp3xJOQdTDAan+KEXeme7GcYIGzFD421Zcqf9dHomw==", "signatures": [{"sig": "MEYCIQCABBplrXEjifI4FkriJFzPgyZh4QC5wTdD64LijTop7wIhAKbbC3p+AX7ybGUQhQOtpqLq2z801Rh7wo2LSv8qH99M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 394619}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "53bb4aa51e0f5c2b85b6c49515a9f2f1b3757c09", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.10.0_1715442697095_0.17653282885059718", "host": "s3://npm-registry-packages"}}, "4.10.1": {"name": "tsx", "version": "4.10.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.10.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "80085dbb27e7bdf7292d9f712ef11ff99aba55c5", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.10.1.tgz", "fileCount": 45, "integrity": "sha512-G+CcyTOopwhuI81FU+KpzGN5UBhHgGEDlGt8mHAXKxv8pDGr6WI7hI7aRjTRol5WzFVsSNuzl3ekCZ0eLIJlEQ==", "signatures": [{"sig": "MEYCIQD6i/X+pzV4s6SbsHJEt5KGeEM1pyqXUPwqXu5sC1Mm/gIhAIHBrWHU9gq1zvxztIMIbGDvMIqAQoLiDCbR8ZLs3ZF9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 394830}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "efb35094f23ea48c51e8f0ec4f1aade47e91dd01", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.10.1_1715512491743_0.6551274539752081", "host": "s3://npm-registry-packages"}}, "4.10.2": {"name": "tsx", "version": "4.10.2", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.10.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "ed1a39f556e208e899d2d8b204aa4108fe7b4668", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.10.2.tgz", "fileCount": 45, "integrity": "sha512-gOfACgv1ElsIjvt7Fp0rMJKGnMGjox0JfGOfX3kmZCV/yZumaNqtHGKBXt1KgaYS9KjDOmqGeI8gHk/W7kWVZg==", "signatures": [{"sig": "MEYCIQDDDOsyMreSBEdolpMu4F+h7VMVPcqPWzrPwl4PDRIjEQIhAPzH7I84q73e3V4ZGj8atmUGZabBt9pZqjyeRwvfMend", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395584}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "2c87fc156b6343b1ac789ecd5de0a4b64a2f7361", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.3"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.10.2_1715594277399_0.7667604032239632", "host": "s3://npm-registry-packages"}}, "4.10.3": {"name": "tsx", "version": "4.10.3", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.10.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "c0b1dac1e3ebb3db4465363dc8eef7b3ae0e7538", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.10.3.tgz", "fileCount": 45, "integrity": "sha512-f0g60aFSVRVkzcQkEflh8fPLRfmt+HJHgWi/plG5UgvVaV+9TcpOwJ0sZJSACXmwmjMPg9yQR0BhTLbhkfV2uA==", "signatures": [{"sig": "MEUCIQCRTmUgucgqn5/Fe9SFcoUU09CjIx9d1r8xNqqaCZONDwIgT4Hy3eRnpP+yC2lDxNr/s0S71FWNFuvFWqH5+1croEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395756}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "5cdd50bc3fb406c02b9508ea15e33c4f9d597bfb", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.10.3_1715842484129_0.45558417863426093", "host": "s3://npm-registry-packages"}}, "4.10.4": {"name": "tsx", "version": "4.10.4", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.10.4", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "4a24716968702ab0d917ef03ed58eee6e8b2646d", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.10.4.tgz", "fileCount": 45, "integrity": "sha512-Gtg9qnZWNqC/OtcgiXfoAUdAKx3/cgKOYvEocAsv+m21MV/eKpV/WUjRXe6/sDCaGBl2/v8S6v29BpUnGMCX5A==", "signatures": [{"sig": "MEUCIH7rDBsOptGbupjKGkGJSGOmyT0uDNozIWizDqyc8a+EAiEAupZ5Q7b3CuJLj6NWMDBYw7DWuNKJT1CP0fZrebyqzB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395989}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "4a8a2dcce25e1223dec65219659065901fd7620f", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.10.4_1715914786431_0.6553443305682805", "host": "s3://npm-registry-packages"}}, "4.10.5": {"name": "tsx", "version": "4.10.5", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.10.5", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "157139bd2472dd64ff4118385f3eccd27868bb36", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.10.5.tgz", "fileCount": 45, "integrity": "sha512-twDSbf7Gtea4I2copqovUiNTEDrT8XNFXsuHpfGbdpW/z9ZW4fTghzzhAG0WfrCuJmJiOEY1nLIjq4u3oujRWQ==", "signatures": [{"sig": "MEQCIH21h/P7HZykXcrDvV23JCCUHoauY4//CW3uSV0jnXe0AiBbaB8OBobUS+RLoI1lfOsuwY9w9GJaeChm8DRK2YAgZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396307}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "86cf87c861991eddb39cf8ea88b2870b9f536ff5", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.10.5_1716016409809_0.6599195224148096", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "tsx", "version": "4.11.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.11.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "fdd8ce7ccaa0a84aed64c11e2fc5da314e1d8d14", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.11.0.tgz", "fileCount": 45, "integrity": "sha512-vzGGELOgAupsNVssAmZjbUDfdm/pWP4R+Kg8TVdsonxbXk0bEpE1qh0yV6/QxUVXaVlNemgcPajGdJJ82n3stg==", "signatures": [{"sig": "MEQCICENcIMFnPvBwyWr41o9LU0It3ER6Q9o0CytnmzZFD6AAiAG1+tg2YraZFh7ad96vrZLwry6nWAuIJnRUWMrWDzIAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 397048}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "b6bf39b1cc4ca037a16f5b02c619ed5c36e6d598", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.11.0_1716436296587_0.01523040040835455", "host": "s3://npm-registry-packages"}}, "4.11.1": {"name": "tsx", "version": "4.11.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.11.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "133adc0e08324553e820a813347e697761339b31", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.11.1.tgz", "fileCount": 45, "integrity": "sha512-8ZmVC4OlQipEh3XBclB9YIih1XlNerBJ5NSwo5p76lIWIZdoSF+jFOVm9Gc/FQQ9554mZfxEEq9yTZfdghGYrw==", "signatures": [{"sig": "MEYCIQCqgBKXy9X+efzVXWN+Y6HWxmEqw4KtSL40dpeD3EkWtQIhAMXpStA6/yOhAOhDo16m1HSzrL5hd0SFIy3qSry8umf/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 397192}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "5e701051c4eb639bfd204c805310777789907a55", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.11.1_1717383689813_0.5469625621626211", "host": "s3://npm-registry-packages"}}, "4.11.2": {"name": "tsx", "version": "4.11.2", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.11.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "91791db82cbcd3f7515e623cc9ff288f2aa663dc", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.11.2.tgz", "fileCount": 45, "integrity": "sha512-V5DL5v1BuItjsQ2FN9+4OjR7n5cr8hSgN+VGmm/fd2/0cgQdBIWHcQ3bFYm/5ZTmyxkTDBUIaRuW2divgfPe0A==", "signatures": [{"sig": "MEYCIQCS8nMjAXHPAUrd0T7QnDFHp84v84kAR5dHXbKGImPnEAIhALnh4dSKhM0gXsYnRyXptKLyslrbIWISZPP7g3Dy9teD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 397278}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "aa2b639a9bac8ae28a46b847013fda7f5cd4466b", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.11.2_1717386947861_0.24165786291090408", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "tsx", "version": "4.12.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.12.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "2a2483d27f2073fc36e8f89bcb1b753d031d699b", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.12.0.tgz", "fileCount": 47, "integrity": "sha512-642NAWAbDqPZINjmL32Lh/B+pd8vbVj6LHPsWm09IIHqQuWhCrNfcPTjRlHFWvv3FfM4vt9NLReBIjUNj5ZhDg==", "signatures": [{"sig": "MEUCIBfgjOgK1C5WZ6NBbUoSkCX2rDGQDluomFIDmio95AVlAiEA1iz9nQIs8xZ2qkvjMwevUif5SkiO0ube13AWds8RbZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 416562}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "7c85303b6f049eaa6bad0e75c26c03348049bfcd", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.12.0_1717649907914_0.10463003974591412", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "tsx", "version": "4.12.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.12.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "90ae8e51c2bdadd444cd10bbf23909a4fec0a5db", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.12.1.tgz", "fileCount": 47, "integrity": "sha512-roBBguHaP2EpOheka1uHkD4XMSsp1/K5KqtMOjuA+TqRzUsmwpVjHdoNTm/XJWltjB3opmPmYQoE8c2rCS2bqQ==", "signatures": [{"sig": "MEUCIQDVqP8L5ZHMXsoMwnGmSWs+jKtG6cbo+0LoOna/h8uGjAIgecrddlHkk5DSpi/DTR/Lew/wOhH7FeMXFGq/9v7QdoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 416689}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "de900a196a5a66f5363f40fd6d38b879aee96875", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.12.1_1717721375779_0.44194801725885857", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "tsx", "version": "4.13.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.13.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "f0f98d1adcd8de397e46b3242805e9098449b8ac", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.13.0.tgz", "fileCount": 44, "integrity": "sha512-kNY70P2aLMdVBii1Err5ENxDhQ6Vz2PbQGX68DcvzY2/PWK5NLBO6vI7lPr1/2xG3IKSt2MN+KOAyWDQSRlbCA==", "signatures": [{"sig": "MEUCIGyFCw8hP2sU0i35fmuLxvmQFexYF5JMMhs9xwihR3lCAiEArQC8TCiI/e6U/TCG+ecHRLBThFwqsquoieSUTzqvlO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 419688}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "0eb4e911f31c478400c7f31027f218123450d108", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.13.0_1717729289326_0.3190815041392798", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "tsx", "version": "4.13.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.13.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "929019f83fddbdaa4dd1724983d2ae219cab7546", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.13.1.tgz", "fileCount": 44, "integrity": "sha512-MU1IshdvSdIvf0U9ofr5ZWCWjLAPvf5gIvRZygGisoA/fq/FmSxjAfLIxF8ZoZtvHffY1NRkmxIR5/RW1Qc84g==", "signatures": [{"sig": "MEUCIQCzcRpNc/mBqYlUrW0DcMX/kuiV4V86yA5Itj/UC5fdoAIgcqHUJFp/zjsmidvgg3GFieyeGdaczRz1HH4EZMnn81M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 419604}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "0a78bfd11b20fcb6460fdcdea7d3b1daebfe3ad0", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.13.1_1717742177900_0.7028509451018472", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "tsx", "version": "4.13.2", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.13.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "e59c0a0abf955d1c352081352657a3f92c832e36", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.13.2.tgz", "fileCount": 44, "integrity": "sha512-s+WGqChkA77uU8xij1IdO9jQnwJAiWJto0bF5yJLbAZpLtNs82Qa5CwMBxWjJ7QOYU9MzBf4MCNt6lZduwkQ+g==", "signatures": [{"sig": "MEYCIQDHSQITQpgZeEw2ukWLk30mNLd1vN/YelwOozR7IvgHngIhANXEH5xdD22y0MPRF9IGh9KsIUZcGngeh7RdovwcTVzZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 419695}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "87a7683ab3d686918ec8f23b43df2961067fc1e0", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.13.2_1717754876408_0.6358766980526493", "host": "s3://npm-registry-packages"}}, "4.13.3": {"name": "tsx", "version": "4.13.3", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.13.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "7ec1d2ec3e011555c56433a24f990c9dd4f97494", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.13.3.tgz", "fileCount": 44, "integrity": "sha512-FTAJJLQCMiIbt78kD5qhLjHIR5NOQDKC63wcdelWRDBE+d1xSrXYhXq4DzejnC2tGhFZHpDy2Ika0Ugf7sK8gA==", "signatures": [{"sig": "MEYCIQCJ4WSKunF/OimmnZkaSu8dtZ5vx3sGPD52v6+/cqlO4QIhAN7Vj4LFfKCJ9B1ibuHc8f8ozQgRqefbXJydiB6KUcAu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 419758}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "c35dbaa7c4c5998ec0febef112f7c3c3b715ff5a", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.13.3_1717815979553_0.7558725256538796", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "tsx", "version": "4.14.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.14.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "71eb2abacdd6187befda3883ca46ae4e32f38bd5", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.14.0.tgz", "fileCount": 44, "integrity": "sha512-DsDLlJlusAPyCnz07S4y0gqJoUl8GciBeYcXQd75/5DqkZ4gfjKpvAUFUzmZf62nEotkcqC7JCWrdL8d+PXSng==", "signatures": [{"sig": "MEUCIAWXkjgRhnaKIF8O8MvQtKyW47vBPPVc7wcf+KR9ZXTrAiEA9T1+IF9POWTK37rcs3sjKwwHigz+Ryx35YXrt4vCgR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 421599}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "4503421e601b05078e1adb13b86a1a4619c9805d", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.14.0_1717847081672_0.8641344589611872", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "tsx", "version": "4.14.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.14.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "8329cd8215751d411d6d08a3ffeb8132640bf318", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.14.1.tgz", "fileCount": 44, "integrity": "sha512-GU8pPJq8DdxcJDSK6Bc64c2jW8zBK2hb0jzwHZDfjapbwu6AqvFnAElnzZ17Xb9TH5a/j6/sicTCVYF+eO/cmA==", "signatures": [{"sig": "MEYCIQCsRCMiDG1tzwXQLf2VaHNpH9W0mEru8M4MfVkiVIj5AQIhAORoe4nQFrRU9+chcioBzPGNAuH0UYZ2MLJ7ayRz3hem", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 421817}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "9e647a58eeb635cef85063c80635655776bfe9b6", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.20.2", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.14.1_1717888992215_0.039564085374843794", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "tsx", "version": "4.15.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.15.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "06f919e3c5a4fdd2be3583876ea3fd9b9a55c89f", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.15.0.tgz", "fileCount": 44, "integrity": "sha512-yPThHvkUdsyi3pUb+2a6JM0y0shxxLlIEfms2ZztL/rHqK+4dFYdVxh2X/0bcieo16Kvjq77VU/ML/nVICK/0g==", "signatures": [{"sig": "MEUCIBRETBPspe+Gfjiy27VA8ecYWK1oKgLhn/ij3iW30dfDAiEArTEL77ap3zH+t4cNaOjfE/NNlnjf9muVutnVpL8+5g0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 421847}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "c67d7468074d023b5c3abfb4b3ebd58484318e24", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.21.4", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.15.0_1717914455295_0.27337025428059825", "host": "s3://npm-registry-packages"}}, "4.15.1": {"name": "tsx", "version": "4.15.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.15.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "884a8d5f6bbeed82724e53f1138a6b824923c29d", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.15.1.tgz", "fileCount": 46, "integrity": "sha512-k/6h17jA1KfUR7SpcteOa880zGmF56s8gMIcSqUR5avyNFi9nlCEKpMiHLrzrqyARGr52A/JablmGey1DEWbCA==", "signatures": [{"sig": "MEYCIQDIfB8SCsrftNx94AaMlC+ahh/r7fdp/C6NsjfW05ERWgIhAOAG/hH5bIHSs4DO5iaFtpUEi7PdLQpkeJUSk5TSVwhr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422179}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "fb247eb0313a191ba13fb682857309d8c588856a", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.21.4", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.15.1_1717928451141_0.4692145889640815", "host": "s3://npm-registry-packages"}}, "4.15.2": {"name": "tsx", "version": "4.15.2", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.15.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "f2d9dfb747d89b508d950d235a4d6165400a0234", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.15.2.tgz", "fileCount": 46, "integrity": "sha512-kIZTOCmR37nEw0qxQks2dR+eZWSXydhTGmz7yx94vEiJtJGBTkUl0D/jt/5fey+CNdm6i3Cp+29WKRay9ScQUw==", "signatures": [{"sig": "MEUCIFDUsQaUUzwJy+MWU01YpDbiPWrJaRvFoLApTTajQuKmAiEAkTCIjnu64P+6Mlk2ZppYaMADd4TH2ptq6tbfyFzeIUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422207}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "7e1fe22e142643afc2226a2f1c0d1579c949f0c6", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.21.4", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.15.2_1718062146406_0.868944365484182", "host": "s3://npm-registry-packages"}}, "4.15.3": {"name": "tsx", "version": "4.15.3", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.15.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "88492aefdd1f50ba502a6071c6225987bd1203c7", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.15.3.tgz", "fileCount": 46, "integrity": "sha512-wLzUMy3xSUpGZQqDprd0U25hpLRJMH8RzONVsqJLwgmE65ZKsxfbs5xVG+sF08wHNyBILmqo5C2775DXYMbRog==", "signatures": [{"sig": "MEUCIQCTb1RFv/+Br1MjlCYei1SyDEcePdlTtixwpUau/MMUVAIgf7UNMYtN5SvpAyv0R0NQYII5dcJRxB4yWaOUjX1HoWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422295}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "6b03a38443035f377281c5122eee4c56fefea179", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.21.4", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.15.3_1718240648294_0.7602619847584973", "host": "s3://npm-registry-packages"}}, "4.15.4": {"name": "tsx", "version": "4.15.4", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.15.4", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/tsx#readme", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "d35b9d343b46af1ed8d56a3f5b0ecdb42b579f75", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.15.4.tgz", "fileCount": 46, "integrity": "sha512-d++FLCwJLrXaBFtRcqdPBzu6FiVOJ2j+UsvUZPtoTrnYtCGU5CEW7iHXtNZfA2fcRTvJFWPqA6SWBuB0GSva9w==", "signatures": [{"sig": "MEYCIQCBRNsqwKAshzsS8BvhQ2yYqB1rIuzD9vxvEm+JbB4ycwIhANDyrGgHT9YuIh7Y2mU9Pyxbu+A4oZalfgbmtRLxxMSi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422625}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "4c538538b46415dc7b98677ffa560706eb8aceaa", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.21.4", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.15.4_1718258628893_0.9109244103331173", "host": "s3://npm-registry-packages"}}, "4.15.5": {"name": "tsx", "version": "4.15.5", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.15.5", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "9c1ddb10ed0035c595c9b3e00ae40a3647a94c82", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.15.5.tgz", "fileCount": 46, "integrity": "sha512-iKi8jQ2VBmZ2kU/FkGkL2OSHBHsazsUzsdC/W/RwhKIEsIoZ1alCclZHP5jGfNHEaEWUJFM1GquzCf+4db3b0w==", "signatures": [{"sig": "MEQCICW9hzgB8+7XraQOQMSrFa/O4SubkQyHvEo7BC8Yw5odAiAPrrDxEfCzsotymdqXVSSeLaMgJDeSdWD7WBGKFLyCcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422681}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "c22fa7d1a90fa34983caddda91b5c1c10e1a4b6c", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.21.4", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.15.5_1718412839204_0.4128930424672783", "host": "s3://npm-registry-packages"}}, "4.15.6": {"name": "tsx", "version": "4.15.6", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.15.6", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "4522ed093f7fa54f031a7a999274e8b35dbf3165", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.15.6.tgz", "fileCount": 46, "integrity": "sha512-is0VQQlfNZRHEuSSTKA6m4xw74IU4AizmuB6lAYLRt9XtuyeQnyJYexhNZOPCB59SqC4JzmSzPnHGBXxf3k0hA==", "signatures": [{"sig": "MEQCIDkF+Pb3e5WzNMRYAud4/qxJsKssYxTQUmTCY+rnSpuMAiAZHMKhjtXnlvVx3nJeusqcsuPog/AohMV20FtmT7/Whg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422845}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "cb27d4dfe7670e6cf50f09b48cbd37ac73aa064a", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"esbuild": "~0.21.4", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.15.6_1718617525523_0.7524871925670535", "host": "s3://npm-registry-packages"}}, "4.15.7": {"name": "tsx", "version": "4.15.7", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.15.7", "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "69d7499196a323507c4051d2ba10753edcc057e5", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.15.7.tgz", "fileCount": 46, "integrity": "sha512-u3H0iSFDZM3za+VxkZ1kywdCeHCn+8/qHQS1MNoO2sONDgD95HlWtt8aB23OzeTmFP9IU4/8bZUdg58Uu5J4cg==", "signatures": [{"sig": "MEQCIAvpEk2yzINVgXEW3Gc632660glGDPTBz/bCeV+0lo+BAiAMRlRPZ37syUQuzgMuLGDhGusWyfo7LikhvK9uBSEXhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 423567}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "98d9f6dc9edd8d6e182b8b1a854f585f678f22a1", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"esbuild": "~0.21.4", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.15.7_1718942595897_0.6889231236648152", "host": "s3://npm-registry-packages"}}, "4.15.8": {"name": "tsx", "version": "4.15.8", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.15.8", "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "e4ef4c31864c7db9abb3d42c21e8f92858dc22be", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.15.8.tgz", "fileCount": 46, "integrity": "sha512-B8dMlbbkZPW0GQ7wafyy2TGXyoGYW0IURfWkM1h/WzgG5lxxRoeDU2VbMURmmjwGaCsoKROVTLmQQPe/s2TnLw==", "signatures": [{"sig": "MEUCIQDzWEsKR1Ys+IpylxjAhdwH+4KFagwyAhpYoHFeNOq7kAIgQ0Tl4hKwaP8JotgFWWDTAu/hvBIniHyqmk2gnxUKA0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422810}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "ae2a1bcdc1357a56d436acab5782da9a348d2cd5", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"esbuild": "~0.21.4", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.15.8_1719580221083_0.5103043277728394", "host": "s3://npm-registry-packages"}}, "4.15.9": {"name": "tsx", "version": "4.15.9", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.15.9", "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "9c8cf4e78b87a9f0e295b8b72cee5c57ad5cbfca", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.15.9.tgz", "fileCount": 46, "integrity": "sha512-OEw8VSS5Ug88pj3YhOic1Zmg+mKOuJOcBdpxl402C0D4TjRxu/LTIrr+slbc8nKQA+b7YC9SoHTZAEeQQPBwFw==", "signatures": [{"sig": "MEQCIFJQjYmvkYqHy3u/YsxR6JDSMRkI4HgtM43hNOoqX0U2AiAJ8LrFzRSWVG9ADCVujldw4ir6Vs6iNqs6bUVFS6BGOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422836}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "c67e3baefdb4b38d44779a44e21f2a989482911c", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"esbuild": "~0.21.4", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.15.9_1719622189451_0.10143028908161389", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "tsx", "version": "4.16.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.16.0", "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "913dd96f191b76f07a8744201d8c15d510aa1352", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.16.0.tgz", "fileCount": 46, "integrity": "sha512-MPgN+CuY+4iKxGoJNPv+1pyo5YWZAQ5XfsyobUG+zoKG7IkvCPLZDEyoIb8yLS2FcWci1nlxAqmvPlFWD5AFiQ==", "signatures": [{"sig": "MEQCIDQwC6xQWD1sxrzicpcpe8DiAWSzzx1KwTVukvZXRABfAiAxawpte4MK4zotMTtXqHaoFdKCZWlwvC/z6dR2YHgEqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422922}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "da3bcb06d23ffd8ae06fe0eae10b4a4c0b03b060", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"esbuild": "~0.21.5", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.16.0_1719641635297_0.07324422751316995", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "tsx", "version": "4.16.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.16.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "2692328f2bcf87eb10f94e62441034e1da765d1f", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.16.1.tgz", "fileCount": 46, "integrity": "sha512-UAKkKOFMJI4M3U4xTJuoqECBMydJbqwNuACoMvwHjqDhbQyo/SYd8ZYNCpF5F8rAjcdKrO+z9CbvBlBiFKhBLA==", "signatures": [{"sig": "MEYCIQCYZzrRyT70FA1CVgA1BzyI8ekvBCpB4PgzilcYchgiMAIhAN7JK88tGywCMunlDhz3+uY+eSt4YxvedYjhVsr05B7x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 423544}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "a74aa5857aa848a27f34198aa0ce9d028fda5801", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"esbuild": "~0.21.5", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.16.1_1719988691263_0.5773094951871578", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "tsx", "version": "4.16.2", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.16.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "8722be119ae226ef0b4c6210d5ee90f3ba823f19", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.16.2.tgz", "fileCount": 46, "integrity": "sha512-C1uWweJDgdtX2x600HjaFaucXTilT7tgUZHbOE4+ypskZ1OP8CRCSDkCxG6Vya9EwaFIVagWwpaVAn5wzypaqQ==", "signatures": [{"sig": "MEUCIQDClTiiO+xKC5zW1z8HuPNq6J+5IctHJ/TDemalWB5ecgIgTvtLxc8oDqgotbOt9lSKiBBx56UjpYhVwIsvdOcHmjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 424188}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "042be032246ef8964a4b6bf4602a9dca7c875d52", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"esbuild": "~0.21.5", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.16.2_1719995415337_0.656912346132281", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "tsx", "version": "4.16.3", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.16.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "56eb2f14f67b798e8bd8a907a9ceec57cba0e8b9", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.16.3.tgz", "fileCount": 46, "integrity": "sha512-MP8AEUxVnboD2rCC6kDLxnpDBNWN9k3BSVU/0/nNxgm70bPBnfn+yCKcnOsIVPQwdkbKYoFOlKjjWZWJ2XCXUg==", "signatures": [{"sig": "MEUCIFPNThw3ywoqyvQwGScIGzYI01dMseVqjGRBMZLKZm22AiEAw+cdgulhlP6VqNBYYAPBHBJ2hp2QfXkTtiSmk90fWuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 424556}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "ca4bf11670bdbc29bd5fe413cfa3cedba02c2e4e", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.16.0", "dependencies": {"esbuild": "~0.21.5", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.16.3_1722391627832_0.7142618710839144", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "tsx", "version": "4.16.4", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.16.4", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "27a29883f015e15d89af5c302be41439fa65cf45", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.16.4.tgz", "fileCount": 46, "integrity": "sha512-E0EDobc7FtOxdNtG0ZQWztLa9PK/TqC5QvdV0heyIMJySwcJ8vyvziOznzO1MIM2IDfacLGBgfiCUmba6mvI7Q==", "signatures": [{"sig": "MEQCIFaHUFnL8PJdx0R9J/hz+xcXbYeHUuBkpcOiFNPvPbNKAiAIDxzUzfL2Ff8CEAoHzktX5dMIDTG/T+B0v3u9iaxybg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 424604}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "3cf0b6acb9041b782d46345c23f3f48160e05f49", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.16.0", "dependencies": {"esbuild": "~0.21.5", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.16.4_1722486084540_0.8639341261836979", "host": "s3://npm-registry-packages"}}, "4.16.5": {"name": "tsx", "version": "4.16.5", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.16.5", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "49c2a8f4d4d66bd7cf538e23e7368a1919a9a1ca", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.16.5.tgz", "fileCount": 46, "integrity": "sha512-ArsiAQHEW2iGaqZ8fTA1nX0a+lN5mNTyuGRRO6OW3H/Yno1y9/t1f9YOI1Cfoqz63VAthn++ZYcbDP7jPflc+A==", "signatures": [{"sig": "MEUCIEIltgCLj+1oNtXxaD+pAxmZWnrRDcuHMAeX9pPnPIV6AiEA9zoaTnnhwooRCj80GskNM+UfArveqJejnIa5wnPGqRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 424680}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "2fada74ad363655890695716f762f89276800a46", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.16.0", "dependencies": {"esbuild": "~0.21.5", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.16.5_1722505520031_0.345918647442899", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "tsx", "version": "4.17.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.17.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "6ffd9851a0c7aa4ecacf4dc19f28d82112af25c5", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.17.0.tgz", "fileCount": 46, "integrity": "sha512-eN4mnDA5UMKDt4YZixo9tBioibaMBpoxBkD+rIPAjVmYERSG0/dWEY1CEFuV89CgASlKL499q8AhmkMnnjtOJg==", "signatures": [{"sig": "MEYCIQCuVJDWvnvoKnI8SBUVeeHcRBJoH0hWMEzTdoF2d6gj7gIhAOC2m/xQghWVARBiy1mulmedX+dzgu8LpLR8Xmdd3LaF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 425116}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "bd83d3bf59e39767ec64eec86fe5b48a8e50b991", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.16.0", "dependencies": {"esbuild": "~0.23.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.17.0_1723104643100_0.34520467973850866", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "tsx", "version": "4.17.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.17.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "16e9294507797b67b253b8ab1becfe014f6ec60c", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.17.1.tgz", "fileCount": 46, "integrity": "sha512-s+poJ84lg23SPBvxQbPmyJd/tlpeBKbOnOYmhKO54L6CR/NyCF8Sw7dqLUWLuscDnKkD31h1qqX0A4FIa9ysaA==", "signatures": [{"sig": "MEUCIDvzN6xaMMUKnb4CXKbwHwEnc6LT4C67Ze7Utw4Q19htAiEA3U0uvV5SKPQCTgovfO6KBo8WUfCUEMdS3kZcn28KJGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 425288}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "af370e7af4837cf3db069c3ee1758c90f94f7d32", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"esbuild": "~0.23.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.17.1_1724513885644_0.364447514552519", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "tsx", "version": "4.18.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.18.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "c5c6e8af9e7d162446ed22dc7b53dc4792bf920b", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.18.0.tgz", "fileCount": 46, "integrity": "sha512-a1jaKBSVQkd6yEc1/NI7G6yHFfefIcuf3QJST7ZEyn4oQnxLYrZR5uZAM8UrwUa3Ge8suiZHcNS1gNrEvmobqg==", "signatures": [{"sig": "MEYCIQDucpAOkm+G2DkJz36e/6+yB6MEm3GFACXcP8tsxsacnAIhAI+aaor+V8itR0TrlubKL/pBGpXRntQX8n9ZOW3GMoNC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 425516}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "474ea71ff9c9cbd2d3a5dbe0951f2c2d92f2e26a", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"esbuild": "~0.23.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.18.0_1724515954888_0.6461516361628683", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "tsx", "version": "4.19.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.19.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "6166cb399b17d14d125e6158d23384045cfdf4f6", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.19.0.tgz", "fileCount": 46, "integrity": "sha512-bV30kM7bsLZKZIOCHeMNVMJ32/LuJzLVajkQI/qf92J2Qr08ueLQvW00PUZGiuLPP760UINwupgUj8qrSCPUKg==", "signatures": [{"sig": "MEQCIBL+mC2JRszDXeDDncbG69rT4gx87GWExykbvs5m2VzHAiBDHX5iP5M3kddPn1xCBKfHVpbhfAdwu8oTTBkVL4ZTbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 425788}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "157c3ec6bcf0b0a5e387080576404c00f7fd662f", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"esbuild": "~0.23.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.19.0_1724740990852_0.8614196908772274", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "tsx", "version": "4.19.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.19.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "b7bffdf4b565813e4dea14b90872af279cd0090b", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.19.1.tgz", "fileCount": 46, "integrity": "sha512-0flMz1lh74BR4wOvBjuh9olbnwqCPc35OOlfyzHba0Dc+QNUeWX/Gq2YTbnwcWPO3BMd8fkzRVrHcsR+a7z7rA==", "signatures": [{"sig": "MEQCIAzoP+/+VETNH6fZ7jxsJfD/xO/D52e0+3NLnnxVw2lAAiBtopuP0brmq91h98fHL9RjO2wc/Mrf8w+9hqdSHaxQ7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 425922}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "0329bfc731346d6c8b6055c7f2882e5c3155a7ec", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"esbuild": "~0.23.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.19.1_1726120828011_0.6727105169511847", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "tsx", "version": "4.19.2", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.19.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "2d7814783440e0ae42354d0417d9c2989a2ae92c", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.19.2.tgz", "fileCount": 46, "integrity": "sha512-pOUl6Vo2LUq/bSa8S5q7b91cgNSjctn9ugq/+Mvow99qW6x/UZYwzxy/3NmqoT66eHYfCVvFvACC58UBPFf28g==", "signatures": [{"sig": "MEUCIQCJMxlxKvSA6cQQcM9SEsI3tBUP/p6ZyoQVXf00SYCO9wIge5ToI0oOPFPnN6rey7XbkIMTngSsuBSUzJeZtMbvK/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 426124}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "7c47074652790e8225bb9c0d3123fc92e75d3695", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"esbuild": "~0.23.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.19.2_1729985071746_0.023599950550363236", "host": "s3://npm-registry-packages"}}, "4.19.3": {"name": "tsx", "version": "4.19.3", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.19.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "2bdbcb87089374d933596f8645615142ed727666", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.19.3.tgz", "fileCount": 46, "integrity": "sha512-4H8vUNGNjQ4V2EOoGw005+c+dGuPSnhpPBPHBtsZdGZBk/iJb4kguGlPWaZTZ3q5nMtFOEsY0nRDlh9PJyd6SQ==", "signatures": [{"sig": "MEUCIDPuvE1y8OAgiZzV4og41w3gqd8dlmQxIs6YWcLnWSd2AiEA/0temeRn/N4406gBSgq+dcyxJgWlSPLd2WCXtwWyIrA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 426124}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "e04e6c6ccb56f74db5036c358d6c6b24bebe6319", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"esbuild": "~0.25.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.19.3_1739926552256_0.477174189552904", "host": "s3://npm-registry-packages-npm-production"}}, "4.19.4": {"name": "tsx", "version": "4.19.4", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.19.4", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "647b4141f4fdd9d773a9b564876773d2846901f4", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.19.4.tgz", "fileCount": 48, "integrity": "sha512-gK5GVzDkJK1SI1zwHf32Mqxf2tSJkNx+eYcNly5+nHvWqXUJYUkWBQtKauoESz3ymezAI++ZwT855x5p5eop+Q==", "signatures": [{"sig": "MEYCIQDLNi5ZzF/cng+g8yMGDNuCs5PrNIdkI81DbWyaC5eKSQIhAJTRlgKcO+Xw6/W/xJV0J8+4z+iRWqmY9ALd1JGSRDvd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 426585}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "3c34caa1a16773d5b4ff53dd367d6012e93feaec", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.18.3", "dependencies": {"esbuild": "~0.25.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.19.4_1745906971418_0.14554824033070668", "host": "s3://npm-registry-packages-npm-production"}}, "4.20.0": {"name": "tsx", "version": "4.20.0", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.20.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "269e97d9ade5b0d2a6ad684540793696d2bf3506", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.20.0.tgz", "fileCount": 50, "integrity": "sha512-TsmdeXxcZYiJ2MKV7fdq38na0CKyLRtCeMqTeHMmrVXQ/gf5yTeJohh+sgr7MnMGsjeKXzHzy+TwOOTR1arl+Q==", "signatures": [{"sig": "MEQCIACDx64bACOm7Q9rAj1GsYw+ZVSx6/e27/533VP+0+YDAiBBL54HdVCegzhn0dhlbMTomdJ6175lkWYJAvwnHj2teQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 436927}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "e97fe6738767aba0bca18514cc81c73ce1e3cc2c", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"esbuild": "~0.25.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.20.0_1749607597046_0.4739714980006293", "host": "s3://npm-registry-packages-npm-production"}}, "4.20.1": {"name": "tsx", "version": "4.20.1", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.20.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "f5ef9a5d4839184e4216064e3655cc9a65761986", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.20.1.tgz", "fileCount": 50, "integrity": "sha512-JsFUnMHIE+g8KllOvWTrSOwCKM10xLcsesvUQR61znsbrcwZ4U/QaqdymmvTqG5GMD7k2VFv9UG35C4dRy34Ag==", "signatures": [{"sig": "MEQCIEiY6/4Fey5CQvpzyaxKHtwxDY5Oe9xfOjkUdTuO89bLAiBFs1i2WUb85iS6FzR/dIYMzWBwUMKWlteIfSNEgD/eJA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 437063}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "9bd25469c26db2b93e1e8500fb28d04352e1ef70", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"esbuild": "~0.25.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.20.1_1749630267495_0.5705445155350197", "host": "s3://npm-registry-packages-npm-production"}}, "4.20.2": {"name": "tsx", "version": "4.20.2", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tsx@4.20.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://tsx.is", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "bin": {"tsx": "dist/cli.mjs"}, "dist": {"shasum": "a1019ea01ef1f3d16db196a530a6103e960b3142", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.20.2.tgz", "fileCount": 50, "integrity": "sha512-He0ZWr41gLa4vD30Au3yuwpe0HXaCZbclvl8RBieUiJ9aFnPMWUPIyvw3RU8+1Crjfcrauvitae2a4tUzRAGsw==", "signatures": [{"sig": "MEQCIBVt/6IZXo/QcB+gTaX7EDsjrYH/UyzEUuk71w633bz8AiAf4bCVCGyxLqpyA1IqDbCWqmHPM+x+6tJ1Db7tnrceng==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 437581}, "type": "module", "engines": {"node": ">=18.0.0"}, "exports": {".": "./dist/loader.mjs", "./cjs": "./dist/cjs/index.cjs", "./cli": "./dist/cli.mjs", "./esm": "./dist/esm/index.mjs", "./repl": "./dist/repl.mjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./preflight": "./dist/preflight.cjs", "./patch-repl": "./dist/patch-repl.cjs", "./package.json": "./package.json", "./suppress-warnings": "./dist/suppress-warnings.cjs"}, "gitHead": "c1882680bce9df3052bef23dbed1b95728e802bb", "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/tsx.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"esbuild": "~0.25.0", "get-tsconfig": "^4.7.5"}, "_hasShrinkwrap": false, "optionalDependencies": {"fsevents": "~2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/tsx_4.20.2_1749715150300_0.12761290600086972", "host": "s3://npm-registry-packages-npm-production"}}, "4.20.3": {"name": "tsx", "version": "4.20.3", "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/privatenumber/tsx.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "type": "module", "bin": {"tsx": "dist/cli.mjs"}, "exports": {"./package.json": "./package.json", ".": "./dist/loader.mjs", "./patch-repl": "./dist/patch-repl.cjs", "./cjs": "./dist/cjs/index.cjs", "./cjs/api": {"import": {"types": "./dist/cjs/api/index.d.mts", "default": "./dist/cjs/api/index.mjs"}, "require": {"types": "./dist/cjs/api/index.d.cts", "default": "./dist/cjs/api/index.cjs"}}, "./esm": "./dist/esm/index.mjs", "./esm/api": {"import": {"types": "./dist/esm/api/index.d.mts", "default": "./dist/esm/api/index.mjs"}, "require": {"types": "./dist/esm/api/index.d.cts", "default": "./dist/esm/api/index.cjs"}}, "./cli": "./dist/cli.mjs", "./suppress-warnings": "./dist/suppress-warnings.cjs", "./preflight": "./dist/preflight.cjs", "./repl": "./dist/repl.mjs"}, "homepage": "https://tsx.is", "engines": {"node": ">=18.0.0"}, "dependencies": {"esbuild": "~0.25.0", "get-tsconfig": "^4.7.5"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "_id": "tsx@4.20.3", "gitHead": "dadcf27b2e1721bd98e4e02d926180f060a5f48a", "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-qjbnuR9Tr+FJOMBqJCW5ehvIo/buZq7vH7qD7JziU98h6l3qGy0a/yPFjwO+y0/T7GFpNgNAvEcPPVfyT8rrPQ==", "shasum": "f913e4911d59ad177c1bcee19d1035ef8dd6e2fb", "tarball": "https://registry.npmjs.org/tsx/-/tsx-4.20.3.tgz", "fileCount": 50, "unpackedSize": 432520, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCTHrhZMXmxMs86eR0hjau6To2ZPsafVoGAJuXisRc5RgIhAIkBD0CMtSSjPUhRJb2AsgFLnRUZNFaHwKWR7NzwGO+n"}]}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tsx_4.20.3_1749803929615_0.8750431916118977"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-08-20T01:25:39.249Z", "modified": "2025-06-13T08:38:50.039Z", "1.0.0": "2015-08-20T01:25:39.249Z", "2.0.0": "2022-05-10T23:27:10.178Z", "2.0.1": "2022-05-11T00:38:30.907Z", "2.1.0": "2022-05-13T18:17:38.043Z", "3.0.0": "2022-05-17T17:02:58.410Z", "3.1.0": "2022-05-17T17:34:32.670Z", "3.2.0": "2022-05-17T23:57:57.388Z", "3.2.1": "2022-05-18T11:40:22.239Z", "3.3.0": "2022-05-18T16:09:46.780Z", "3.3.1": "2022-05-18T20:32:57.568Z", "3.4.0": "2022-05-19T15:27:37.879Z", "3.4.1": "2022-05-25T01:27:44.805Z", "3.4.2": "2022-05-25T20:58:49.489Z", "3.4.3": "2022-06-16T04:28:34.193Z", "3.5.0": "2022-06-21T21:44:01.434Z", "3.5.1": "2022-06-23T19:50:52.147Z", "3.6.0": "2022-06-24T00:01:06.036Z", "3.7.0": "2022-07-02T05:21:08.662Z", "3.7.1": "2022-07-02T18:45:36.673Z", "3.8.0": "2022-07-13T00:56:39.393Z", "3.8.1": "2022-08-08T01:08:54.916Z", "3.8.2": "2022-08-11T16:43:16.430Z", "3.9.0": "2022-08-29T13:31:23.900Z", "3.10.0": "2022-10-07T06:39:09.274Z", "3.10.1": "2022-10-07T10:44:53.247Z", "3.10.2": "2022-10-17T17:46:59.751Z", "3.10.3": "2022-10-18T01:29:45.636Z", "3.10.4": "2022-10-19T16:23:36.525Z", "3.11.0": "2022-10-24T00:02:56.297Z", "3.12.0": "2022-11-10T07:38:22.315Z", "3.12.1": "2022-11-12T13:24:49.371Z", "3.12.2": "2023-01-09T08:04:45.535Z", "3.12.3": "2023-02-06T03:27:03.297Z", "3.12.4": "2023-03-12T13:35:11.246Z", "3.12.5": "2023-03-13T06:53:49.271Z", "3.12.6": "2023-03-20T17:28:17.005Z", "3.12.7": "2023-04-30T00:47:35.049Z", "3.12.8": "2023-09-01T11:10:21.955Z", "3.12.9": "2023-09-12T12:51:45.507Z", "3.12.10": "2023-09-13T12:34:48.812Z", "3.13.0": "2023-09-24T13:55:08.966Z", "3.14.0": "2023-10-17T21:59:05.132Z", "4.0.0": "2023-11-09T07:51:40.794Z", "4.1.0": "2023-11-10T11:27:57.373Z", "4.1.1": "2023-11-11T02:59:45.136Z", "4.1.2": "2023-11-14T06:26:06.153Z", "4.1.3": "2023-11-17T10:29:06.788Z", "4.1.4": "2023-11-19T06:47:41.532Z", "4.2.0": "2023-11-21T06:58:14.753Z", "4.2.1": "2023-11-22T14:38:03.865Z", "4.3.0": "2023-11-22T17:15:55.330Z", "4.4.0": "2023-11-23T11:10:19.745Z", "4.5.0": "2023-11-24T21:32:23.842Z", "4.5.1": "2023-11-27T23:36:07.737Z", "4.6.0": "2023-11-27T23:52:58.467Z", "4.6.1": "2023-11-30T02:09:57.610Z", "4.6.2": "2023-12-02T08:48:46.770Z", "4.7.0": "2023-12-19T15:49:56.647Z", "4.7.1": "2024-02-10T01:01:32.227Z", "4.7.2": "2024-04-04T08:17:21.455Z", "4.7.3": "2024-04-25T10:34:39.311Z", "4.8.0": "2024-05-01T02:10:37.342Z", "4.8.1": "2024-05-01T04:47:02.661Z", "4.8.2": "2024-05-01T07:21:52.251Z", "4.9.0": "2024-05-03T08:17:15.134Z", "4.9.1": "2024-05-04T13:59:18.320Z", "4.9.2": "2024-05-06T00:38:24.546Z", "4.9.3": "2024-05-06T01:36:21.188Z", "4.9.4": "2024-05-10T16:02:57.652Z", "4.9.5": "2024-05-11T13:22:10.210Z", "4.10.0": "2024-05-11T15:51:37.302Z", "4.10.1": "2024-05-12T11:14:51.972Z", "4.10.2": "2024-05-13T09:57:57.582Z", "4.10.3": "2024-05-16T06:54:44.329Z", "4.10.4": "2024-05-17T02:59:46.737Z", "4.10.5": "2024-05-18T07:13:30.046Z", "4.11.0": "2024-05-23T03:51:36.797Z", "4.11.1": "2024-06-03T03:01:30.040Z", "4.11.2": "2024-06-03T03:55:48.050Z", "4.12.0": "2024-06-06T04:58:28.127Z", "4.12.1": "2024-06-07T00:49:36.072Z", "4.13.0": "2024-06-07T03:01:29.579Z", "4.13.1": "2024-06-07T06:36:18.142Z", "4.13.2": "2024-06-07T10:07:56.635Z", "4.13.3": "2024-06-08T03:06:19.801Z", "4.14.0": "2024-06-08T11:44:41.886Z", "4.14.1": "2024-06-08T23:23:12.440Z", "4.15.0": "2024-06-09T06:27:35.488Z", "4.15.1": "2024-06-09T10:20:51.375Z", "4.15.2": "2024-06-10T23:29:06.667Z", "4.15.3": "2024-06-13T01:04:08.600Z", "4.15.4": "2024-06-13T06:03:49.210Z", "4.15.5": "2024-06-15T00:53:59.462Z", "4.15.6": "2024-06-17T09:45:25.677Z", "4.15.7": "2024-06-21T04:03:16.209Z", "4.15.8": "2024-06-28T13:10:21.278Z", "4.15.9": "2024-06-29T00:49:49.697Z", "4.16.0": "2024-06-29T06:13:55.502Z", "4.16.1": "2024-07-03T06:38:11.538Z", "4.16.2": "2024-07-03T08:30:15.534Z", "4.16.3": "2024-07-31T02:07:08.089Z", "4.16.4": "2024-08-01T04:21:24.791Z", "4.16.5": "2024-08-01T09:45:20.180Z", "4.17.0": "2024-08-08T08:10:43.337Z", "4.17.1": "2024-08-24T15:38:05.852Z", "4.18.0": "2024-08-24T16:12:35.164Z", "4.19.0": "2024-08-27T06:43:11.069Z", "4.19.1": "2024-09-12T06:00:28.228Z", "4.19.2": "2024-10-26T23:24:32.056Z", "4.19.3": "2025-02-19T00:55:52.450Z", "4.19.4": "2025-04-29T06:09:31.666Z", "4.20.0": "2025-06-11T02:06:37.284Z", "4.20.1": "2025-06-11T08:24:27.757Z", "4.20.2": "2025-06-12T07:59:10.521Z", "4.20.3": "2025-06-13T08:38:49.847Z"}, "bugs": {"url": "https://github.com/privatenumber/tsx/issues"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://tsx.is", "keywords": ["cli", "runtime", "node", "cjs", "commonjs", "esm", "typescript", "typescript runner"], "repository": {"type": "git", "url": "git+https://github.com/privatenumber/tsx.git"}, "description": "TypeScript Execute (tsx): Node.js enhanced with esbuild to run TypeScript & ESM files", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "<h1 align=\"center\">\n<br>\n<picture>\n\t<source media=\"(prefers-color-scheme: dark)\" srcset=\".github/logo-dark.svg\">\n\t<img width=\"160\" alt=\"tsx\" src=\".github/logo-light.svg\">\n</picture>\n<br><br>\n<a href=\"https://npm.im/tsx\"><img src=\"https://badgen.net/npm/v/tsx\"></a> <a href=\"https://npm.im/tsx\"><img src=\"https://badgen.net/npm/dm/tsx\"></a>\n</h1>\n\n<p align=\"center\">\nTypeScript Execute (tsx): The easiest way to run TypeScript in Node.js\n<br><br>\n<a href=\"https://tsx.is\">Documentation</a>&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;<a href=\"https://tsx.is/getting-started\">Getting started →</a>\n</p>\n\n<br>\n\n<p align=\"center\">\n\t<a href=\"https://github.com/sponsors/privatenumber/sponsorships?tier_id=398771\"><img width=\"412\" src=\"https://raw.githubusercontent.com/privatenumber/sponsors/master/banners/assets/donate.webp\"></a>\n\t<a href=\"https://github.com/sponsors/privatenumber/sponsorships?tier_id=416984\"><img width=\"412\" src=\"https://raw.githubusercontent.com/privatenumber/sponsors/master/banners/assets/sponsor.webp\"></a>\n</p>\n<p align=\"center\"><sup><i>Already a sponsor?</i> Join the discussion in the <a href=\"https://github.com/pvtnbr/tsx\">Development repo</a>!</sup></p>\n\n## Sponsors\n\n<p align=\"center\">\n\t<a href=\"https://github.com/sponsors/privatenumber\">\n\t\t<img src=\"https://cdn.jsdelivr.net/gh/privatenumber/sponsors/sponsorkit/sponsors.svg\">\n\t</a>\n</p>\n\n", "readmeFilename": "README.md", "users": {"qbb.sh": true, "flumpus-dev": true}}