{"_id": "@rollup/rollup-freebsd-arm64", "_rev": "59-2c96d446166a5323f3493c073bb7a834", "name": "@rollup/rollup-freebsd-arm64", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.2"}, "versions": {"4.24.2": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "15fe184ecfafc635879500f6985c954e57697c44", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-1YWOpFcGuC6iGAS4EI+o3BV2/6S0H+m9kFOIlyFtp4xIX5rjSnL3AwbTBxROX0c8yWtiWM7ZI6mEPTI7VkSpZw==", "signatures": [{"sig": "MEUCIG6ohqfycc5Ip1JVODoFnP2EU9jv8FIbnUWdyuLoBiSyAiEArd+PXUlVUr3f3Aki7TQPDEmi1CY77ZRVWJkKnS/PC/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2129598}, "main": "./rollup.freebsd-arm64.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.24.2_1730043610688_0.489279229986765", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "274f152156fa123ad740089adb81073be2607ab3", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-QjIsSZoH1oFZJDySdByzWVh3VHH1NNnt4ss4zuSkTPHAytx76as2bgOYSTPEeVK4cMH9epZ38+XrSg5pVtpppw==", "signatures": [{"sig": "MEYCIQC11HTWkpW9Urmm6JDoLaqqeTCvkco+aPRO7YM61PJSIAIhAIKLhZZxtsdMcPICZ3NZzTjZmcavslZWnsx1hZ8vDq3U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2129600}, "main": "./rollup.freebsd-arm64.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.25.0-0_1730182512918_0.018030162767572744", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "27145e414986e216e0d9b9a8d488028f33c39566", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-CsC+ZdIiZCZbBI+aRlWpYJMSWvVssPuWqrDy/zi9YfnatKKSLFCe6fjna1grHuo/nVaHG+kiglpRhyBQYRTK4A==", "signatures": [{"sig": "MEQCIFAZvJ+G5yMPohPQjm7Fxg/TJb6ap6XVs6Asnx5rnUaSAiA9qeBIPAbI2EkfZ5s6ydCpFcgBjYwMqON4d0fIfiP9Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2129598}, "main": "./rollup.freebsd-arm64.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.24.3_1730211250285_0.3817818438676088", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "c54b2373ec5bcf71f08c4519c7ae80a0b6c8e03b", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-py5oNShCCjCyjWXCZNrRGRpjWsF0ic8f4ieBNra5buQz0O/U6mMXCpC1LvrHuhJsNPgRt36tSYMidGzZiJF6mw==", "signatures": [{"sig": "MEQCIC0JwQ2BsVzAuP1UiurcFHMKVPz2WXS4xzfkzZba0wL7AiAim6bl7PiTkgdYvTfOcqEwjp8/gnBm6jRHnTfPc8iaeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2137118}, "main": "./rollup.freebsd-arm64.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.24.4_1730710031572_0.480813924363644", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "99e9173b8aef3d1ef086983da70413988206e530", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-xpEIXhiP27EAylEpreCozozsxWQ2TJbOLSivGfXhU4G1TBVEYtUPi2pOZBnvGXHyOdLAUUhPnJzH3ah5cqF01g==", "signatures": [{"sig": "MEUCIETbB6Y+JgABR+Ij+l/gkAWkLPQch1HNO9fe+32fNZvsAiEA/8ICRRY7A611wS487sAPJJmpFbArLab8JJ4UNTFcRxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2120606}, "main": "./rollup.freebsd-arm64.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.25.0_1731141444656_0.15598835615346784", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "550a0ebf5bea6ceee79dc2f75a0bcef7d660de2c", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-Y9vpjfp9CDkAG4q/uwuhZk96LP11fBz/bYdyg9oaHYhtGZp7NrbkQrj/66DYMMP2Yo/QPAsVHkV891KyO52fhg==", "signatures": [{"sig": "MEQCICpSZLo8keMvgzUWIwMF48CZ/M/EeEFgZYLGVE/aHPeSAiB2TQi1PlFHiBaCLOsIhfWEqc9XvA8OZopsb2+kPKogWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2120606}, "main": "./rollup.freebsd-arm64.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.26.0_1731480301941_0.35150139821575666", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "a65da1df18d428fc04854cc11a4546421f738eb1", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-IoeRR3RbwZojnP0e9Ssgs2s8yQ5QZ91d7dKrlUThjb+w0kfmdTwgNXP6olSI8U0KBkiD+FGbIeBOrYcg4fLhQg==", "signatures": [{"sig": "MEUCIEM+CTf/m3eVdJMbfwLqxIq0wXj3oI4uZwkT29Tw9+oDAiEApJ1zQX73QLOyT4euvWJa3Sa6Ih18sZQPnYtKNDJqiew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2120608}, "main": "./rollup.freebsd-arm64.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.27.0-0_1731481396213_0.5838473623101974", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "a3658078914c9f11f704617f80aa6b7424c7b9bc", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-sH4BXTmsSTp8XJ+WQAW2FhkbJ65Rbwrjiwh7Ov94LyF33hFEphbDFsI5Hv9cS0R1I/OLr9Ai3MLHw9hPt4UHlw==", "signatures": [{"sig": "MEUCIARz/rTIi+Z7YY3xTBfBqcw3iFGzxHyWiNfuV+R0MlEcAiEArWutNydc5Lci2hhYHiBw/XL/1f1/oONlRxZ1+HBcP0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2120608}, "main": "./rollup.freebsd-arm64.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.27.0-1_1731565993784_0.4541492842764223", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "4fa07aa44c9404c5ee4c4cfa7a41c61465a307c1", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-I2eRlZG87gl6WxP6PvSB5bfFA1btE7tWnG6QAoEU/0Gr47f6KaxRwiRfBujHlzkuMPqtpTlSOW4aOEOyMtQqfg==", "signatures": [{"sig": "MEUCIFpWRcYPlhZ6O2H8BLytkQlnSvSmIsMu17Z3COiMxaHBAiEA22jnfyn/Z14bmxN2HFDocvETTAmlivBvpDC7HWJup1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2132966}, "main": "./rollup.freebsd-arm64.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.27.0_1731667238247_0.017704298030575938", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "3941e0208a390c3c155b7c7b18b2175f6b3a1b66", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-lVq+p55p5V3SlkWHH3HzV6JUZroPey61cLAJu9qZPzbEHGh3oaPp5X1tHRJuZ8qrmCvx4svmM/uKCFz2d+S8KQ==", "signatures": [{"sig": "MEUCIAL1JRy46t2t3B3lxSxJfxN7TxrMqflQTjLw+mbizeqkAiEA2LRR5nJpj0npALu/bqFpmxmVEbB1g/XHGi3YoHAr0P8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2132968}, "main": "./rollup.freebsd-arm64.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.27.1-0_1731677290489_0.49137998275386763", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "98c7500c7eb8d19dbaa13efe50a19b1a2e9c5a51", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-sTP07pfYZb2X7nRyvaSeW4yaQsaO6vaH0OCYowL7z3Z70cVRK7SYTeMhV5RU979+tg3IbBg0qpRM9aWxyOGj6A==", "signatures": [{"sig": "MEQCIBSet5HoL2UQmFpGtY9UkJt7frxSh4zZoLu+mbUftDnZAiB3WIWY/SQuPsu6tWjEdB0qDgYIwXGQw+CBvT18UrSSrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2132968}, "main": "./rollup.freebsd-arm64.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.27.1-1_1731685086008_0.03602295036312486", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "b103ea5daf4cfdb25cad49b60968baea93571f33", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-WOAkR+4FwxTZ5QvsSt2j1XB5Artr5eGyAInfH8G1uvL2ic1sdXuOtNDX3oj5jFswnb8Tv5r5uOFSI3e+io435A==", "signatures": [{"sig": "MEYCIQDEnkYMjcviQH4/o1GGd/uXyUY/HMSmR+IYNcy5GWMF/AIhALfHPD59vrmIs92pBBayWawnvnR6IjCoA1SiH4eVxG7p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2132966}, "main": "./rollup.freebsd-arm64.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.27.1_1731686864125_0.10185112787620376", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "ee5421bd8b3b9e1e9402c0328c3b02cde7869404", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-RjgKf5C3xbn8gxvCm5VgKZ4nn0pRAIe90J0/fdHUsgztd3+Zesb2lm2+r6uX4prV2eUByuxJNdt647/1KPRq5g==", "signatures": [{"sig": "MEQCIBcdqp2tPzSjd06FjTKIsbS0T+F+AnnzucmKuNYm/PpMAiA/nLJTm6qzsyVzqf7oll+3mxoobFc6DrLqUIPWJtKUYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2132966}, "main": "./rollup.freebsd-arm64.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.27.2_1731691206158_0.8701076523807665", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "bcb4112cb7e68a12d148b03cbc21dde43772f4bc", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-58E0tIcwZ+12nK1WiLzHOD8I0d0kdrY/+o7yFVPRHuVGY3twBwzwDdTIBGRxLmyjciMYl1B/U515GJy+yn46qw==", "signatures": [{"sig": "MEUCIFzGltVEGuONR/jq/c9S9aitBVMy3ThJYvlNkx/R7Ti4AiEAtZdlfqOVxuL1aFUxG4lyj02UICzedQFZ3N72PBHNGwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2132966}, "main": "./rollup.freebsd-arm64.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.27.3_1731947978353_0.997406143070313", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "662f808d2780e4e91021ac9ee7ed800862bb9a57", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-NBI2/i2hT9Q+HySSHTBh52da7isru4aAAo6qC3I7QFVsuhxi2gM8t/EI9EVcILiHLj1vfi+VGGPaLOUENn7pmw==", "signatures": [{"sig": "MEUCIQCiuyaQ8sptshWKlK4+9BVBXG7OTzD3LN8Nhi6Egm9+bQIgUeQbg4F/3KL5HrC+P1FGQQQrCEmMjywb4dXro8SyJ9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2128150}, "main": "./rollup.freebsd-arm64.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.27.4_1732345223954_0.20826637417945726", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "3c919dff72b2fe344811a609c674a8347b033f62", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-lA1zZB3bFx5oxu9fYud4+g1mt+lYXCoch0M0V/xhqLoGatbzVse0wlSQ1UYOWKpuSu3gyN4qEc0Dxf/DII1bhQ==", "signatures": [{"sig": "MEUCIQCnetcCQEtfbSP1PFdiny8uyG4HeWJ51W5E6uYVrGjtDQIgSVPkTle7wC7BQU88T0Trqw4nSKkqzOrLUWf4g2zGl5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2132222}, "main": "./rollup.freebsd-arm64.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.28.0_1732972550535_0.8582790675607659", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "30ed247e0df6e8858cdc6ae4090e12dbeb8ce946", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-HTDPdY1caUcU4qK23FeeGxCdJF64cKkqajU0iBnTVxS8F7H/7BewvYoG+va1KPSL63kQ1PGNyiwKOfReavzvNA==", "signatures": [{"sig": "MEQCICeg+w8XFAb+JD5gWdKJ3IeRtWENHtkglKA/VfBiHzk3AiAePOKDqI3zIXKF0uvEFAYf5lk5QVKfrak5oaYHEVZEfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2120414}, "main": "./rollup.freebsd-arm64.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.28.1_1733485498910_0.539572699736236", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "526c5937276e3a1f21da3e5d54bf23ddda694a24", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-KqlCPVZqHPe73iJ2WpaNJgKlqkOD1VN7BmlmxVARAkzASZHKC9JuWLvzSQ8/lhzfOzzb1iQCO4CW23WKiKpNqw==", "signatures": [{"sig": "MEUCIC3R4RhM9DgkVg2zgyETKTOmwUa2avJr2lGjhiIpp+FuAiEA1yzHYIET5qLRpPzXi4GynuCRMMCAK/JYFpURW+5qkdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2120432}, "main": "./rollup.freebsd-arm64.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.29.0-0_1734331196059_0.5398526291513026", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "dcfa108267a9c3a8c2b1761bbdc9a708ffdbe46f", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-VwU6lmvWjEB3JJf+RzuWIPWQ18qrOAfgDBhTEP2hQtRc2ZGgESgJjZe6IRih8CsxwC+TLHaBBPU6qtmQB2hFLg==", "signatures": [{"sig": "MEYCIQDf5a/RFJiL/OnRuiiFi+t85JwXeDPgeb392unn4RFDLwIhAP6pQKiIPkm1a9XnZaFf5IKu/lpNeovCYY6KPv90BNA4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2120432}, "main": "./rollup.freebsd-arm64.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.29.0-1_1734590253976_0.4865412973258627", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "307d037efbafcae50e30c6f17e6de3bf1e8ef235", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-TDR/95oJfkzOhXqDXSzRPHVvehNsGc103r0y+kvR4U+oXrWyrQX81IAvM2uDTLpBVKKJP/faGx0gyDWsKnAnpw==", "signatures": [{"sig": "MEUCIQCgJh2Cuy6yISMMThCS3588vgS+KDPFIRLxBXwTbH/gwwIgGNmN0Cchkcks+23icgsPMist+dQkBTA8nIESWZdLjvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2120432}, "main": "./rollup.freebsd-arm64.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.29.0-2_1734677765934_0.6732528973372245", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "55386ff9e62a98e43c62bb6d79e582516dfcd9dd", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-BB8+4OMzk2JiKL5+aK8A0pi9DPB5pkIBZWXr19+grdez9b0VKihvV432uSwuZLO0sI6zCyxak8NO3mZ1yjM1jA==", "signatures": [{"sig": "MEYCIQDK+vy6E2lNG98OT987Xxt94eQhAmC8LZiEZ+gBi6jATwIhAPITtXhjMztlAhGPUJ7YWV2oFdH+KIRFg/q/Gokuy79k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2129750}, "main": "./rollup.freebsd-arm64.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.29.0_1734719847339_0.6555587775045908", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "83178315c0be4b4c8c1fd835e1952d2dc1eb4e6e", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-91C//G6Dm/cv724tpt7nTyP+JdN12iqeXGFM1SqnljCmi5yTXriH7B1r8AD9dAZByHpKAumqP1Qy2vVNIdLZqw==", "signatures": [{"sig": "MEQCIEMIxLDEla4befTJWV2lYk0qZX8nQXVleMbh/i0jnpryAiAgGElpn7rAtbzSNh1knZVXdnqbPGumeCUJdnfZ9o48VA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2129750}, "main": "./rollup.freebsd-arm64.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.29.1_1734765365395_0.1641554108968195", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "1cdf1ce770e4d0168653c7b515a15e2f9c90f209", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-P3G/gZ+GHkY7iNCZnJirLZOex6ETauoRz3YUtwTiAo8tglFjqRFnEsrG5e1twiSXHwW8gMRG1cdwjh36ZNfH7A==", "signatures": [{"sig": "MEUCICGMbX7Qkz0k7Wg969CyBJ+uIsQumBqrL973osy79v8vAiEAkKNc7eGux4Sw6dPglvmK6r5yl9ede0HjatWr5MsmOTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2129752}, "main": "./rollup.freebsd-arm64.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.30.0-0_1734765437360_0.6263093419773342", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "18388f0221d3820c1323147cda192597472462c9", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-6tCCkgaXJwtx8vsDqf2ztsNbPq54tFfpJ4NzpA5grVV2EhIZHyX8KVgKxHqu6y6ixL6PJ85rCQROccIVxNOcuA==", "signatures": [{"sig": "MEQCICjCZh/dQydM22VTowsQtq2smCDWmK4raLeINXPIuR4ZAiBpU5Xj/+TEJEBMnW5ecQg/6kcAOcn5zSo4Bn8pDC6ZHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2121072}, "main": "./rollup.freebsd-arm64.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.30.0-1_1735541539166_0.7177044691816639", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "1c11650970c4b52d7fb077f5a4a6e16ba5e6db4f", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-/xdNwZe+KesG6XJCK043EjEDZTacCtL4yurMZRLESIgHQdvtNyul3iz2Ab03ZJG0pQKbFTu681i+4ETMF9uE/Q==", "signatures": [{"sig": "MEUCIQDVD+fbUmZBtKbWXlhXHzSuijy8JDDDtsQTJCs+XFfWVQIgWRDOqLl1qLBZa4pAzE4X52yDB0JvhsH/EYmPYZeGBt8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2121070}, "main": "./rollup.freebsd-arm64.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.29.2_1736078865665_0.24438194540407387", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "3e13b5d4d44ea87598d5d4db97181db1174fb3c8", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-3REQJ4f90sFIBfa0BUokiCdrV/E4uIjhkWe1bMgCkhFXbf4D8YN6C4zwJL881GM818qVYE9BO3dGwjKhpo2ABA==", "signatures": [{"sig": "MEUCIEtKsX8fJONKuUEY3T1VHjPDC1uJFaDvmyuYx+hz4OR7AiEA0uToBUTuZ/1n5FfkhCem+zFStTa5Bu3gdApc7QYJq+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2121070}, "main": "./rollup.freebsd-arm64.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.30.0_1736145403776_0.8127564782723862", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "233f8e4c2f54ad9b719cd9645887dcbd12b38003", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-HYTlUAjbO1z8ywxsDFWADfTRfTIIy/oUlfIDmlHYmjUP2QRDTzBuWXc9O4CXM+bo9qfiCclmHk1x4ogBjOUpUQ==", "signatures": [{"sig": "MEUCIQDJGB0Y4kJ21H1U3vCEXd1T4pvihhv6fGPFNJYWiMV/VQIgE+c936FYEWijaESvmZlv56jHT26ANGPPYRJ7viLS9+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2121070}, "main": "./rollup.freebsd-arm64.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.30.1_1736246155570_0.527929441540772", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "88461a6f93f14a3d11c92b3d68ae352a6b67ccc4", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-t0cs4TbMoFukr+ERMicPkv0YAtajrUAotx7SQGNJaDWcHRaclPLDrZUHU11zExrqnvxf7BuU9DCcOzWxejW05Q==", "signatures": [{"sig": "MEYCIQDq5/3A+4vEWwm3QSXb0V1DI6LRpwfvj33uaLCU/+VhKQIhAO9sPQIOppETCEfZuPmf2YRRHJIGO7Is5xPG9FCfDTat", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2121072}, "main": "./rollup.freebsd-arm64.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.31.0-0_1736834264949_0.6374128818114608", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "96ce1a241c591ec3e068f4af765d94eddb24e60c", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-S2oCsZ4hJviG1QjPY1h6sVJLBI6ekBeAEssYKad1soRFv3SocsQCzX6cwnk6fID6UQQACTjeIMB+hyYrFacRew==", "signatures": [{"sig": "MEUCIDKgovIWuqofgFRXT8G231waGygEB3lcKQq8mJY6smrmAiEAxIt/SAGN//o4n3iVsZTx6FjF16eOkBXGnA0Rbjy0wO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2149278}, "main": "./rollup.freebsd-arm64.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.31.0_1737291410049_0.29906194493933214", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "8ad634f462a6b7e338257cf64c7baff99618a08e", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-JpsGxLBB2EFXBsTLHfkZDsXSpSmKD3VxXCgBQtlPcuAqB8TlqtLcbeMhxXQkCDv1avgwNjF8uEIbq5p+Cee0PA==", "signatures": [{"sig": "MEYCIQDoV0kg2OPpczxFxO0tTkWTNYC+1IMfGTlYOKGndzECewIhALgmpV8NeTIp8WQrO2nI05W6vt/bF33bl6kYpcEE0pMN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2147630}, "main": "./rollup.freebsd-arm64.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.32.0_1737707257978_0.8711805467054181", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "8dee11e844acf615236572c276d446cf0f19bf7d", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-d3AVmfAnL1TA57zGj+i9RediiSC1oOIZaupj8tj42yKEpzzRTAtqqJGqHRX0RKtQvgmZmzwpN/QSCHedAESXVw==", "signatures": [{"sig": "MEYCIQCQEY2KhBzhkQ5uD3tFp1Ea9/NJImUk+8BG84PfxfyFegIhAJAycm8lJkaaszKlVwBJ8/xX9H76iIa/6dzHX0R3l+/J", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2147632}, "main": "./rollup.freebsd-arm64.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.33.0-0_1738053011840_0.6599526295418567", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "3bdf18d4ef32dcfe9b20bba18d7a53a101ed79d9", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-NbOa+7InvMWRcY9RG+B6kKIMD/FsnQPH0MWUvDlQB1iXnF/UcKSudCXZtv4lW+C276g3w5AxPbfry5rSYvyeYA==", "signatures": [{"sig": "MEUCIFzqd4zgWQDE58h7f1Tc+nOE8Ck3vlo8dTW5ah+0kUeXAiEA88m7aP78gjDDWZudApUjU+Bqm6wpR5l3qHpJWh4Euu0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2147630}, "main": "./rollup.freebsd-arm64.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.32.1_1738053200407_0.25055201552460327", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "5f09ad4a475137b5bb427cde27f9c9f9c4f79e55", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-VZhX9ymo7t0PSHN3tJXaZ5tne8lmNxFnUBgpjWRT+x6HYWMvfMC0GBrhTEJhLanI6l6ctF0UHrqEWQ8AzgXvOw==", "signatures": [{"sig": "MEQCIDDlpvvbiAIBVmeLHhO/NKZQ5ybSCnfow1cBPRXSzLFxAiAkCN86mEqZolNMl73GoWBNO1dYBadT5waH7QIFQzsWyA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2133150}, "main": "./rollup.freebsd-arm64.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.33.0_1738393922975_0.3935420721327434", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "04f4220ee8662749acfa44b34eb05f3bea3ecb11", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-kpdsUdMlVJMRMaOf/tIvxk8TQdzHhY47imwmASOuMajg/GXpw8GKNd8LNwIHE5Yd1onehNpcUB9jHY6wgw9nHQ==", "signatures": [{"sig": "MEYCIQDY3v08VDyDKUO++yq8QtSW/fYGgFG82HGdUw8Ji4t3rwIhANoOzDBZTZjbcdX7wIqMEc+LX2S6TWIbFlRrJB3b/o+C", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2133150}, "main": "./rollup.freebsd-arm64.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.34.0_1738399226281_0.7181465800111415", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "43f75cb686a23fc0aef3ce6bfe44501bf934e412", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-SUeB0pYjIXwT2vfAMQ7E4ERPq9VGRrPR7Z+S4AMssah5EHIilYqjWQoTn5dkDtuIJUSTs8H+C9dwoEcg3b0sCA==", "signatures": [{"sig": "MEYCIQDy8mzY1o/hRKAG0D+aKPji7ldx/ZsL/8VYlnxwg6urbgIhAKqxcBLnuOfp/o3F+ubaNOn/Qdz5cwkvLXHHsPa+oUBg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2133150}, "main": "./rollup.freebsd-arm64.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.34.1_1738565896539_0.001080259507130421", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "d30e642542d82423628e06841a3a0704b5a1be37", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-eiaHgQwGPpxLC3+zTAcdKl4VsBl3r0AiJOd1Um/ArEzAjN/dbPK1nROHrVkdnoE6p7Svvn04w3f/jEZSTVHunA==", "signatures": [{"sig": "MEUCIBH0z5Z1DBcNRdWfTiSp9BnlGnjzamrWbxObIF998N5QAiEAnBKNH4UENDunNhlIwzwQpPHJjQD9M9jLI8VXjpiAcpg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2133150}, "main": "./rollup.freebsd-arm64.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.34.2_1738656605002_0.7866572677737118", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "a54caebb98ab71aaf67e826cc9e6a145fb30ffb5", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-lpBmV2qSiELh+ATQPTjQczt5hvbTLsE0c43Rx4bGxN2VpnAZWy77we7OO62LyOSZNY7CzjMoceRPc+Lt4e9J6A==", "signatures": [{"sig": "MEUCICBKjCNUTdAZSo02mAiZTc7cjdXvQZR4nA5dhL6hCBiPAiEA82m/GEqTH2JmNSi7fY3IsodOM0vPxh2xFXF68QXqnUI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2133150}, "main": "./rollup.freebsd-arm64.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.34.3_1738747328067_0.9514944113362798", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "38d0db29f2fafa64fc6d082fd952617684742bf7", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-/L0LixBmbefkec1JTeAQJP0ETzGjFtNml2gpQXA8rpLo7Md+iXQzo9kwEgzyat5Q+OG/C//2B9Fx52UxsOXbzw==", "signatures": [{"sig": "MEQCICj/J2kAWqwq6ZBcoIZuTP9DneE5eMngBrKWFpy4yJCfAiBHE3l2ySOfUxobNZPsKw0ZhGbbLI/AoBwX8Rzmda8yrw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2133150}, "main": "./rollup.freebsd-arm64.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.34.4_1738791074185_0.6412113204669074", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "791ae6793c4f18e2afaa630bf45366120b9151e3", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-JPkafjkOFaupd8VQYsXfGFKC2pfMr7hwSYGkVGNwhbW0k0lHHyIdhCSNBendJ4O7YlT4yRyKXoms1TL7saO7SQ==", "signatures": [{"sig": "MEYCIQC3JynUu9sTqdaPwzqcVct31h3G6XlnE0axkfSUOTeTwQIhALCxUgySz6V+T8yBub2mFvhCo4CCYk7C3JoXXerFxlw8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2129198}, "main": "./rollup.freebsd-arm64.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.34.5_1738918384996_0.2635847380759244", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "29c54617e0929264dcb6416597d6d7481696e49f", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-YSwyOqlDAdKqs0iKuqvRHLN4SrD2TiswfoLfvYXseKbL47ht1grQpq46MSiQAx6rQEN8o8URtpXARCpqabqxGQ==", "signatures": [{"sig": "MEUCIEPIHxbK0mAd/n9dqJVVq3SeKSowreq385AXfRod8nU+AiEAqORCWZee+vXWdZvkENNg8uUyvX4g4Lx1+WAnmTTiWXE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2120294}, "main": "./rollup.freebsd-arm64.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.34.6_1738945930227_0.44993734400018925", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "7a028357cbd12c5869c446ad18177c89f3405102", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-oIoJRy3ZrdsXpFuWDtzsOOa/E/RbRWXVokpVrNnkS7npz8GEG++E1gYbzhYxhxHbO2om1T26BZjVmdIoyN2WtA==", "signatures": [{"sig": "MEQCIHtu1brq3OzgJPUlN/iEVI1mub/G1atMIFp9RmkrmxJiAiBJD8GbTaC+uq1ptOBSdCG6kt8ut6vxK1vBqh6JrJdfww==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2116534}, "main": "./rollup.freebsd-arm64.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.34.7_1739526842399_0.2500024778238201", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "cf74f8113b5a83098a5c026c165742277cbfb88b", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-IQNVXL9iY6NniYbTaOKdrlVP3XIqazBgJOVkddzJlqnCpRi/yAeSOa8PLcECFSQochzqApIOE1GHNu3pCz+BDA==", "signatures": [{"sig": "MEUCID9liJL83xygDpF+PGuMfdLPLClCO7PUVLaXig3FOTEnAiEA0D1hQm/x93hyJSp2dw3oone0aSWhiuAlsZXBnggYqiY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2116534}, "main": "./rollup.freebsd-arm64.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.34.8_1739773588315_0.7208894129307681", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "d95bd8f6eaaf829781144fc8bd2d5d71d9f6a9f5", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-2lzjQPJbN5UnHm7bHIUKFMulGTQwdvOkouJDpPysJS+QFBGDJqcfh+CxxtG23Ik/9tEvnebQiylYoazFMAgrYw==", "signatures": [{"sig": "MEQCIB4Ac2x/7kYxZKUIpwi69ZkYmzhuF9kNRlPEMhq3XuYzAiBWlmz5B4HPzKtVqDjgjiZG57v7voHRr9kE2uhtKfk55g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2181398}, "main": "./rollup.freebsd-arm64.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.34.9_1740814359509_0.47756051904530006", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "be3d39e3441df5d6e187c83d158c60656c82e203", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-sxjoD/6F9cDLSELuLNnY0fOrM9WA0KrM0vWm57XhrIMf5FGiN8D0l7fn+bpUeBSU7dCgPV2oX4zHAsAXyHFGcQ==", "signatures": [{"sig": "MEUCIQCY0JiI3DHkrHovy/9G/xY3q5dw+VxeAdrBjRj0lZRRKQIgDATbhFC8SbgNW76d8lKTI44dIB/ZkUzfpfvg9zQMRi8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2184030}, "main": "./rollup.freebsd-arm64.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.35.0_1741415087757_0.9913694763773271", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "6aee296cd6b8c39158d377c89b7e0cd0851dd7c7", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-KXVsijKeJXOl8QzXTsA+sHVDsFOmMCdBRgFmBb+mfEb/7geR7+C8ypAml4fquUt14ZyVXaw2o1FWhqAfOvA4sg==", "signatures": [{"sig": "MEYCIQC6hPvqalNEThQe1kdU5ipuXgPWmAcJMzDY8n7+pxKsiQIhAObuB7SYaBhD8MWxkQniDxEArzU6uVz5nCPEJNrcYxGS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2189054}, "main": "./rollup.freebsd-arm64.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.36.0_1742200546407_0.6008674161126191", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "18113e8e133ccb6de4b9dc9d3e09f7acff344cb7", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-FOMXGmH15OmtQWEt174v9P1JqqhlgYge/bUjIbiVD1nI1NeJ30HYT9SJlZMqdo1uQFyt9cz748F1BHghWaDnVA==", "signatures": [{"sig": "MEUCIFDYdVys/hYcY8jLy1//e7B74lTaRZHXmyKn58H6/MfJAiEA8zSZH+WMOFbKPZvGaV7mgnf8G1r8N1+C2sgPYYbQtWM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2192598}, "main": "./rollup.freebsd-arm64.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.37.0_1742741829807_0.9468752537438239", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "ac8028c99221d1cef22788adda465077d5926911", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-zzJACgjLbQTsscxWqvrEQAEh28hqhebpRz5q/uUd1T7VTwUNZ4VIXQt5hE7ncs0GrF+s7d3S4on4TiXUY8KoQA==", "signatures": [{"sig": "MEQCIBSVi5QRpiC5boDKsyHdmMK8F7UbFDJVF8OAhSLGoLBAAiAR05ktdoBf1hbxGM+Vgha/w1LQpqskVOTyaO78yQLbTw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2191702}, "main": "./rollup.freebsd-arm64.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.38.0_1743229749486_0.432717029086809", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "2b60c81ac01ff7d1bc8df66aee7808b6690c6d19", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-jivRRlh2Lod/KvDZx2zUR+I4iBfHcu2V/BA2vasUtdtTN2Uk3jfcZczLa81ESHZHPHy4ih3T/W5rPFZ/hX7RtQ==", "signatures": [{"sig": "MEYCIQD98kM3qCyoVlv26KZyAztY+krE9Hn8kXc8BkHnsk7ACwIhAPf2OnL3ARZ8+9Ok3mVG44L/UONInejHfmAJb0CBU/vd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2191702}, "main": "./rollup.freebsd-arm64.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.39.0_1743569375010_0.3143982821043336", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "cbcbd7248823c6b430ce543c59906dd3c6df0936", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-r7yGiS4HN/kibvESzmrOB/PxKMhPTlz+FcGvoUIKYoTyGd5toHp48g1uZy1o1xQvybwwpqpe010JrcGG2s5nkg==", "signatures": [{"sig": "MEUCIQD7L3bKlP+rHL2VSLSrJpTYp/Za9sdubGzv5pCPpy1z4wIgNaVpFHIqpin9UZR8AbIlN2guB0T3tfcIbOAnKDZp5YM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2210222}, "main": "./rollup.freebsd-arm64.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.40.0_1744447178466_0.7386738362301122", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "04c892d9ff864d66e31419634726ab0bebb33707", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-B<PERSON>LJ2mHTrIYdaS2I99mriyJfGGenSaP+UwGi1kB9BLOCu9SR8ZpbkmmalKIALnRw24kM7qCN0IOm6L0S44iWw==", "signatures": [{"sig": "MEYCIQD8KVWihP+Sy2KGOqx9sLV6wKzdcrdKTfATo/LeF9z9bAIhAMI7XxUJDNOB8hQXT9N+18jhKXR7ul7M8rPf3QIuhXs7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2314918}, "main": "./rollup.freebsd-arm64.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.40.1_1745814926955_0.7016286897474149", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "91a28dc527d5bed7f9ecf0e054297b3012e19618", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-8t6aL4MD+rXSHHZUR1z19+9OFJ2rl1wGKvckN47XFRVO+QL/dUSpKA2SLRo4vMg7ELA8pzGpC+W9OEd1Z/ZqoQ==", "signatures": [{"sig": "MEUCIQDcQUFpWcURXy1POg3plpxnE4NFhwSnwwwlQ7O67j8pPAIgd4X4eoT5kips25kl6eOM46IFuBInD0g5LuIRrKD3TAc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2315974}, "main": "./rollup.freebsd-arm64.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.40.2_1746516416026_0.46450550526841883", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "a52b58852c9cec9255e382a2f335b08bc8c6111d", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-GSxU6r5HnWij7FoSo7cZg3l5GPg4HFLkzsFFh0N/b16q5buW1NAWuCJ+HMtIdUEi6XF0qH+hN0TEd78laRp7Dg==", "signatures": [{"sig": "MEUCIQCCcfTgyUG/PVMg+Q43LqYg7urhuaZpalkPiNZyclG8vQIgDhj2ZLuXRhTR0dOuELFy3z0gN4+adZR2xUTzOQ3tfmg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2310390}, "main": "./rollup.freebsd-arm64.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.41.0_1747546415250_0.5325983129391756", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "df10a7b6316a0ef1028c6ca71a081124c537e30d", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-DBVMZH5vbjgRk3r0OzgjS38z+atlupJ7xfKIDJdZZL6sM6wjfDNo64aowcLPKIx7LMQi8vybB56uh1Ftck/Atg==", "signatures": [{"sig": "MEYCIQDXZo59g+kOFaddReaqSWtcX6UQY5/VI7ROEYacqvb8HQIhAN3HYo+SmPHdNOImoBD2dkdpkrWR9Kc52CgmAzykjjzK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2407790}, "main": "./rollup.freebsd-arm64.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.41.1_1748067274644_0.18962104980937822", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "8a2162e1b360135194d7306e0426debcdaa01efb", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-yKXgW9UF2f78/PaSAFgDJbrLB/aETxH98jJFIpqESLyhyRmEqZl1dqAoy0IigJJZdwP+ZJdLw2isKiwb1wAqcA==", "signatures": [{"sig": "MEUCIQDrbizbHdbkQHMNfiDv9LrH3XVIpV3buOF3L7Tb5J1m2QIgZPcSHukLzQ3VujPZVLLWKLZBN/rEkzy3AGD+jIQD974=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2399070}, "main": "./rollup.freebsd-arm64.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.41.2_1749210034916_0.18170995292201297", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "0ebdb3b470ccf6acf0eacae8177f34e27477559f", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-fJcN4uSGPWdpVmvLuMtALUFwCHgb2XiQjuECkHT3lWLZhSQ3MBQ9pq+WoWeJq2PrNxr9rPM1Qx+IjyGj8/c6zQ==", "signatures": [{"sig": "MEUCIQDnTBVECK17euRHIJTb0n/xyBgBrATm3k4RqwXQuPdYRgIgbFVz/2XeF0IEQkgX3XDw/LHZW6iEANWDCQmXGY6gAZQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2399070}, "main": "./rollup.freebsd-arm64.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.42.0_1749221295543_0.9500489560533014", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "b128dbe7b353922ddd729a4fc4e408ddcbf338b5", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-SV+U5sSo0yujrjzBF7/YidieK2iF6E7MdF6EbYxNz94lA+R0wKl3SiixGyG/9Klab6uNBIqsN7j4Y/Fya7wAjQ==", "signatures": [{"sig": "MEYCIQDl8xCy6C1AsICDTCzQbGtvP5+mKKieE8JIc+6Geji+7AIhALIXk/Q3ek8169oG63izVdMXS12040HUPW8ZGTPLyl+L", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2399070}, "main": "./rollup.freebsd-arm64.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.43.0_1749619361128_0.5067709517719536", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "d5d4c6cd3b8acb7493b76227d8b2b4a2d732a37b", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-u5AZzdQJYJXByB8giQ+r4VyfZP+walV+xHWdaFx/1VxsOn6eWJhK2Vl2eElvDJFKQBo/hcYIBg/jaKS8ZmKeNQ==", "signatures": [{"sig": "MEUCIBMwjRydrL5JJ3oEYxx78R7N2L12Rp5wyZIGRxQok1OJAiEAuy6dzxva/bkCXyACIk+aHU79581bYYYxxqnHuGkW06s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2412734}, "main": "./rollup.freebsd-arm64.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.44.0_1750314179910_0.06108032789723694", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-freebsd-arm64@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "d199d8eaef830179c0c95b7a6e5455e893d1102c", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-wnFQmJ/zPThM5zEGcnDcCJeYJgtSLjh1d//WuHzhf6zT3Md1BvvhJnWoy+HECKu2bMxaIcfWiu3bJgx6z4g2XA==", "signatures": [{"sig": "MEYCIQD9FI0ivOp0PuCEuRs6i+BOwwzVCrYFR6uN65oS0452FwIhAONmICMR9CUWtASBhLYkRsI+UeGK2o5tK4qLuJTNRwTi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2412734}, "main": "./rollup.freebsd-arm64.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-freebsd-arm64_4.44.1_1750912459045_0.6150142609008151", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-freebsd-arm64", "version": "4.44.2", "os": ["freebsd"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.freebsd-arm64.node", "_id": "@rollup/rollup-freebsd-arm64@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-W4tt4BLorKND4qeHElxDoim0+BsprFTwb+vriVQnFFtT/P6v/xO5I99xvYnVzKWrK6j7Hb0yp3x7V5LUbaeOMg==", "shasum": "4880f9769f1a7eec436b9c146e1d714338c26567", "tarball": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2405086, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCebujeONvvH6goc0D/yuIrUttLVO+TWQeEU+q870iRmAIgVFvyZzFwZhY6hSeBEqYtZUmJggnJ+8u5CN789Y1stK8="}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-freebsd-arm64_4.44.2_1751633771198_0.4755465050827963"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-10-27T15:40:10.545Z", "modified": "2025-07-04T12:56:11.719Z", "4.24.2": "2024-10-27T15:40:10.982Z", "4.25.0-0": "2024-10-29T06:15:13.200Z", "4.24.3": "2024-10-29T14:14:10.508Z", "4.24.4": "2024-11-04T08:47:11.853Z", "4.25.0": "2024-11-09T08:37:24.817Z", "4.26.0": "2024-11-13T06:45:02.223Z", "4.27.0-0": "2024-11-13T07:03:16.472Z", "4.27.0-1": "2024-11-14T06:33:14.060Z", "4.27.0": "2024-11-15T10:40:38.526Z", "4.27.1-0": "2024-11-15T13:28:10.835Z", "4.27.1-1": "2024-11-15T15:38:06.277Z", "4.27.1": "2024-11-15T16:07:44.396Z", "4.27.2": "2024-11-15T17:20:06.476Z", "4.27.3": "2024-11-18T16:39:38.661Z", "4.27.4": "2024-11-23T07:00:24.182Z", "4.28.0": "2024-11-30T13:15:50.788Z", "4.28.1": "2024-12-06T11:44:59.135Z", "4.29.0-0": "2024-12-16T06:39:56.266Z", "4.29.0-1": "2024-12-19T06:37:34.224Z", "4.29.0-2": "2024-12-20T06:56:06.153Z", "4.29.0": "2024-12-20T18:37:27.693Z", "4.29.1": "2024-12-21T07:16:05.691Z", "4.30.0-0": "2024-12-21T07:17:17.593Z", "4.30.0-1": "2024-12-30T06:52:19.378Z", "4.29.2": "2025-01-05T12:07:45.948Z", "4.30.0": "2025-01-06T06:36:43.988Z", "4.30.1": "2025-01-07T10:35:55.814Z", "4.31.0-0": "2025-01-14T05:57:45.157Z", "4.31.0": "2025-01-19T12:56:50.339Z", "4.32.0": "2025-01-24T08:27:38.195Z", "4.33.0-0": "2025-01-28T08:30:12.076Z", "4.32.1": "2025-01-28T08:33:20.600Z", "4.33.0": "2025-02-01T07:12:03.223Z", "4.34.0": "2025-02-01T08:40:26.514Z", "4.34.1": "2025-02-03T06:58:16.767Z", "4.34.2": "2025-02-04T08:10:05.302Z", "4.34.3": "2025-02-05T09:22:08.297Z", "4.34.4": "2025-02-05T21:31:14.420Z", "4.34.5": "2025-02-07T08:53:05.182Z", "4.34.6": "2025-02-07T16:32:10.517Z", "4.34.7": "2025-02-14T09:54:02.629Z", "4.34.8": "2025-02-17T06:26:28.558Z", "4.34.9": "2025-03-01T07:32:39.803Z", "4.35.0": "2025-03-08T06:24:48.020Z", "4.36.0": "2025-03-17T08:35:46.648Z", "4.37.0": "2025-03-23T14:57:10.041Z", "4.38.0": "2025-03-29T06:29:09.735Z", "4.39.0": "2025-04-02T04:49:35.347Z", "4.40.0": "2025-04-12T08:39:38.708Z", "4.40.1": "2025-04-28T04:35:27.196Z", "4.40.2": "2025-05-06T07:26:56.258Z", "4.41.0": "2025-05-18T05:33:35.461Z", "4.41.1": "2025-05-24T06:14:34.887Z", "4.41.2": "2025-06-06T11:40:35.147Z", "4.42.0": "2025-06-06T14:48:15.740Z", "4.43.0": "2025-06-11T05:22:41.397Z", "4.44.0": "2025-06-19T06:23:00.132Z", "4.44.1": "2025-06-26T04:34:19.316Z", "4.44.2": "2025-07-04T12:56:11.489Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-freebsd-arm64`\n\nThis is the **aarch64-unknown-freebsd** binary for `rollup`\n", "readmeFilename": "README.md"}