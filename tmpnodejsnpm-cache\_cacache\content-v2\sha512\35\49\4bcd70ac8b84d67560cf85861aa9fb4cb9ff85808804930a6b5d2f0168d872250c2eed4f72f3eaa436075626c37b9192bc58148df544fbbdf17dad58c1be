{"_id": "detect-libc", "_rev": "22-1ea7820737fe246d4775e86daf7bc47a", "name": "detect-libc", "dist-tags": {"latest": "2.0.4"}, "versions": {"0.0.1": {"name": "detect-libc", "version": "0.0.1", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@0.0.1", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc-family": "./cli-family.js", "detect-libc-version": "./cli-version.js"}, "dist": {"shasum": "7b58b465e4e412099517361720754c830b5e1800", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-0.0.1.tgz", "integrity": "sha512-VCXRHamGxLxRQiCqb6G6kBnmKr6h2GzKIxP0yTyjGSwgX493EazxKgaLKQfnIO2kLIazlHphSfuvfSHROuC0iQ==", "signatures": [{"sig": "MEUCIHy54CWNN0aVCuVvPXwXPPG6NcCBbbgoUHYT3AsOshnjAiEAvnSDT40w+vs/DjkxVUwYEmfLka/lSgQo3AVEshMgVp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7b58b465e4e412099517361720754c830b5e1800", "engines": {"node": ">=4"}, "gitHead": "2af2acd52b2f34061d19d40d9e44e2b43eac5aa8", "scripts": {"test": "semistandard && ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "6.10.3", "devDependencies": {"ava": "^0.20.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-0.0.1.tgz_1499115499208_0.6038856126833707", "host": "s3://npm-registry-packages"}}, "0.0.2": {"name": "detect-libc", "version": "0.0.2", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@0.0.2", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc-family": "./cli-family.js", "detect-libc-version": "./cli-version.js"}, "dist": {"shasum": "b5c90b3565cf7483f6ca21432340a1151811d031", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-0.0.2.tgz", "integrity": "sha512-UjVVU/34H8+yyp7PiWTRJo/3uycKSnJc0WkytaNfnxOwASLyr6BTlyMb9qYLxAuSOd06AsBWhTeqx0jw3O3fGw==", "signatures": [{"sig": "MEYCIQCjM2Vo08VLHmZGW6l/y2Bs0XXzCM77KK2I17QZflVwQQIhAI7GHY040MOfo37Ngk4rNsywYKeWocDsCx7TghKx1rBF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b5c90b3565cf7483f6ca21432340a1151811d031", "engines": {"node": ">=4"}, "gitHead": "6acd413c65dc9b9186f72c24df185b44e7e3cc3e", "scripts": {"test": "semistandard && ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "6.10.3", "devDependencies": {"ava": "^0.20.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-0.0.2.tgz_1499118188324_0.5160433223936707", "host": "s3://npm-registry-packages"}}, "0.0.3": {"name": "detect-libc", "version": "0.0.3", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@0.0.3", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc-family": "./cli-family.js", "detect-libc-version": "./cli-version.js"}, "dist": {"shasum": "450cc62d3cf7117d6acb321cdafe953c6d8114f6", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-0.0.3.tgz", "integrity": "sha512-89rQX5RXmAiFtzq/QqhFDxJbZqKdL0CZgS8PjBAuCScE2tPUzyUdQ8UCEwHabTjXr2YfgZZUgyyGUyBRgRM9dQ==", "signatures": [{"sig": "MEUCIQDoXVWWAG/57ukRo/VGZJLQHvRiRNU7EVOqx7KTJPEGigIgQgpiJxUCc9xieFYzBUFmyGKDvQkQaTen5+KOC6eePKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "450cc62d3cf7117d6acb321cdafe953c6d8114f6", "engines": {"node": ">=4"}, "gitHead": "68efd6bb18fa4ec54b0575e50b39ca5efbb1da86", "scripts": {"test": "semistandard && ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "6.10.3", "devDependencies": {"ava": "^0.20.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-0.0.3.tgz_1499271276149_0.3303824078757316", "host": "s3://npm-registry-packages"}}, "0.0.4": {"name": "detect-libc", "version": "0.0.4", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@0.0.4", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "eaf81433b59832a1107cf6ff397d258b209687ed", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-0.0.4.tgz", "integrity": "sha512-HpCwja8Lzf+zrPMRBqIuD0V4CpVxoFnNIj2NGLAKFc9VcSM+IujvWOMo4JjJlb2Up3V8y0/6PI4mcnh1p22SAQ==", "signatures": [{"sig": "MEUCIQDcdEfapwmRAVE9zZc1UsKNnr85uvcMZFB33JcT0m74HgIgcF3k7/uJPYA/+jYBl8jaB5ctcx0phfjBI6j39GDvo80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "_from": ".", "_shasum": "eaf81433b59832a1107cf6ff397d258b209687ed", "engines": {"node": ">=4"}, "gitHead": "5fb5adccb6865bac46bb3961b49a8e57c3ad499c", "scripts": {"test": "semistandard && ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "6.10.3", "devDependencies": {"ava": "^0.20.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-0.0.4.tgz_1499280348877_0.5235641626641154", "host": "s3://npm-registry-packages"}}, "0.0.5": {"name": "detect-libc", "version": "0.0.5", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@0.0.5", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "b733dffd51f744898c1148e24d4d4a8ba9c1ede1", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-0.0.5.tgz", "integrity": "sha512-nv69x74HGy6oXOvvfB5pLYCX6+pCQM5r/y5xBj1DtNijDJWVAF7wtB6VG92+gQDYBFhyI3taHWLBiK57JjHPbw==", "signatures": [{"sig": "MEYCIQCFmbShNjz1ahQvC9E7Jjq7ao5ciak9yZIi2nQKSeY5IwIhAIZQ77iGvGh4HH11vcnJ6rIe1Po2KXHpRiBjyaS0e3hw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "_from": ".", "_shasum": "b733dffd51f744898c1148e24d4d4a8ba9c1ede1", "engines": {"node": ">=4"}, "gitHead": "0dffcb1988d859b69ce9a22096765840e8cf6548", "scripts": {"test": "semistandard && ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "6.10.3", "devDependencies": {"ava": "^0.20.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-0.0.5.tgz_1499280793529_0.38893276965245605", "host": "s3://npm-registry-packages"}}, "0.0.6": {"name": "detect-libc", "version": "0.0.6", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@0.0.6", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "c982314a2f8afac07647c3188333b1f92f32fa01", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-0.0.6.tgz", "integrity": "sha512-90SG3ibtANkAlhNENneta/d4Lg4fAyEjpHcu4hMkJk1CUr1lu9jRrl/jSKiaMLH1AP5inrKePMZuGyXft6E1Wg==", "signatures": [{"sig": "MEQCIEVVq5NkiPXT+s/vBmHDOSFo2TxJrIEwi7iYmz83Hq1nAiADpz6x0tVOJ4uRrcZzRy3WxNNBQW26DMKYqwnzB3tmrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "_from": ".", "_shasum": "c982314a2f8afac07647c3188333b1f92f32fa01", "engines": {"node": ">=4"}, "gitHead": "89d34bfe360ed2dfe9d108f8c8db6fdce378657d", "scripts": {"test": "semistandard && ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "8.1.3", "devDependencies": {"ava": "^0.20.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-0.0.6.tgz_1499804050501_0.5635840552859008", "host": "s3://npm-registry-packages"}}, "0.0.7": {"name": "detect-libc", "version": "0.0.7", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@0.0.7", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "83e00c6d5005566ad85d88068d5d891d7653f6dc", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-0.0.7.tgz", "integrity": "sha512-QaSj55Y6RsuiHhbh5jofhsFPju7e8ELcXgFdHemTcaPg3FzVD5iiPQNo2ZP6E2YnI3qsDzm31iciP2FSu1HTHA==", "signatures": [{"sig": "MEUCIG4qzZuYDNzB40alAQN+XdNIQCwTJCfjkn7uYmv1xV/4AiEAqCDa++eCf5HXBcomC8x5gBxntnkbLeVamxDT6b1W+Ao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "_from": ".", "_shasum": "83e00c6d5005566ad85d88068d5d891d7653f6dc", "engines": {"node": ">=4"}, "gitHead": "c8609d7d7b47af459c313aaf41cc3563c81ad08b", "scripts": {"test": "semistandard && ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "8.1.3", "devDependencies": {"ava": "^0.20.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-0.0.7.tgz_1499850805115_0.6814915009308606", "host": "s3://npm-registry-packages"}}, "0.0.8": {"name": "detect-libc", "version": "0.0.8", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@0.0.8", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "6d85872129453f3485c830aea26eb43901670295", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-0.0.8.tgz", "integrity": "sha512-KnQhkXispqq6hhmoxiUccGJLctTL+sNHz7ZEkhV/VlI1jdXpocqMhpC/cfXHTkYQ+LnexJkWrGvNiKNa3+dv0g==", "signatures": [{"sig": "MEUCIE95XbbDaTAxKuxgRukM20ATmZMj8pUKb7Izh6KOsBPQAiEA2o4z6NOJxht1ZVF35y7peeIe2ZuhigPkK7qVTSvhpYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "_from": ".", "_shasum": "6d85872129453f3485c830aea26eb43901670295", "engines": {"node": ">=4"}, "gitHead": "f38a4e65f8ffe7c37e4d724ca0152705a06d1ba4", "scripts": {"test": "semistandard && ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "8.1.3", "devDependencies": {"ava": "^0.20.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-0.0.8.tgz_1499862437396_0.8144354657270014", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "detect-libc", "version": "0.1.0", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@0.1.0", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "5760f8e78a3787d8b15fafdae4abed656cba8281", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-0.1.0.tgz", "integrity": "sha512-HcjgsFmv+rgSUh5cwCCRNjlAo365Ko/2aA2ifnhrHn1ogWz4/17Q6Ify4Jiinz5XK1aX4KujwIsmPExOqbmgHQ==", "signatures": [{"sig": "MEUCIQCxGIUesxqaEhO/XTTRHv2GYsCZdeCE/TM3/MepV7ZaLAIgfxH3/PnLtXAyj8vsqEdsxyE5ieunm4qFMNsTNdKj62g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "_from": ".", "_shasum": "5760f8e78a3787d8b15fafdae4abed656cba8281", "engines": {"node": ">=4"}, "gitHead": "7b36e3454df446471f4cb8f89f80c50115d70e59", "scripts": {"test": "semistandard && ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "8.1.3", "devDependencies": {"ava": "^0.20.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-0.1.0.tgz_1499863697721_0.15945673314854503", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "detect-libc", "version": "0.2.0", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@0.2.0", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "47fdf567348a17ec25fcbf0b9e446348a76f9fb5", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-0.2.0.tgz", "integrity": "sha512-M2DlBIG+ImANd4hvyne5ijJ4AWf3HAKi7E8poBClIg9m++kiFscZ+lY6fyU75CkV+RwU9qASjILmKWFm1dD/EQ==", "signatures": [{"sig": "MEUCIQD9XsNVc/bgdZNAe2tIU2RCQHl1IBeJskzA5MP3BtswMQIgXRTSZfXy0kd20Hqok5GasGYUGTuEyg2HgWHk0n95hCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "_from": ".", "_shasum": "47fdf567348a17ec25fcbf0b9e446348a76f9fb5", "engines": {"node": ">=4"}, "gitHead": "4642b204b34414b6089bca854939b05b3d2c1b7c", "scripts": {"test": "semistandard && nyc --reporter=lcov ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "8.1.3", "devDependencies": {"ava": "^0.20.0", "nyc": "^11.0.3", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-0.2.0.tgz_1499942359678_0.36980422819033265", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "detect-libc", "version": "1.0.0", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@1.0.0", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "79d86aa37fe719161e9465f82f132c00a598724a", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.0.tgz", "integrity": "sha512-/NTxDieUNHNKsGwJNoxbtb51aJvuxPWaUexOX3ENn5RVrxBoPBVSKs7Pf0urApkSurpmUmPVNosl3LjK5cf2uA==", "signatures": [{"sig": "MEUCIQCtoyzeVxpeNYozw0k41RJA0TThEgsttfruQzANvrgPCgIgVahUaNM6YbL+mHCJlMLuX9+MUNLwMzqA4OZ382Rjgik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "engines": {"node": ">=0.10"}, "gitHead": "7d7536306be42a2e25c2c775f0061498b11d1da9", "scripts": {"test": "semistandard && nyc --reporter=lcov ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "8.7.0", "devDependencies": {"ava": "^0.23.0", "nyc": "^11.2.1", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-1.0.0.tgz_1509131676507_0.05455085542052984", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "detect-libc", "version": "1.0.1", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@1.0.1", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "fe1894bc64d651f45bb6e079f3bbcbd9a26fc494", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.1.tgz", "integrity": "sha512-mlbO3F3mwb328z41S0hE6gSBgWn2cspdf0KYDi6cyMgvY7R9l8VKx3bbzKnHUxLjFM7Ag1vKWLLDa1rIeDzKOQ==", "signatures": [{"sig": "MEYCIQC7DwBD9mwihSdyYKVqpFeZwCNp2RdtFtsZpzhZJRdRUQIhAIa2Jyl8fkPYMcduYFX2Jk42ucRepouJ+gOe5R9Vl8cb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "_from": ".", "_shasum": "fe1894bc64d651f45bb6e079f3bbcbd9a26fc494", "engines": {"node": ">=0.10"}, "gitHead": "250755465bf514cd909fe838881bc4fde68e11e4", "scripts": {"test": "semistandard && nyc --reporter=lcov ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "8.1.3", "devDependencies": {"ava": "^0.23.0", "nyc": "^11.3.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-1.0.1.tgz_1509179492031_0.6935913264751434", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "detect-libc", "version": "1.0.2", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@1.0.2", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "71ad5d204bf17a6a6ca8f450c61454066ef461e1", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.2.tgz", "integrity": "sha512-YexetqP2dQZlFZoGoE/Ab7ZWxIhExaRwEhluPEqegGJzKIVvVtVinvILxAh/WrzDoNIZ19XU3E+J0tEXPE5MAw==", "signatures": [{"sig": "MEQCIBcxnKP2z6U9Zw+G6J9tpl1SEB0lg5/MPehcol1dj3T+AiAO0w0u8yhHJ6txfN5Z6zNZUZ4Yh7laWkAaZClW0kG2gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "_from": ".", "_shasum": "71ad5d204bf17a6a6ca8f450c61454066ef461e1", "engines": {"node": ">=0.10"}, "gitHead": "92e6276e5d8bfb8fdf4e22a52d9f9fc785f56e83", "scripts": {"test": "semistandard && nyc --reporter=lcov ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "8.1.3", "devDependencies": {"ava": "^0.23.0", "nyc": "^11.3.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-1.0.2.tgz_1509269296302_0.9933948260731995", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "detect-libc", "version": "1.0.3", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@1.0.3", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "bin": {"detect-libc": "./bin/detect-libc.js"}, "dist": {"shasum": "fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz", "integrity": "sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==", "signatures": [{"sig": "MEUCIBAFJGvhPFSi8nL/fQ3IJpq6JR8S4e+iPthb+g5mKukBAiEAws/DhfUwYbc1eXyO/Oz3IZc/guXlsENTM08gMOEpBEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/detect-libc.js", "_from": ".", "_shasum": "fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b", "engines": {"node": ">=0.10"}, "gitHead": "88df1a5950bf3cd9bffa1e0137ab6471c4546118", "scripts": {"test": "semistandard && nyc --reporter=lcov ava"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "8.1.3", "devDependencies": {"ava": "^0.23.0", "nyc": "^11.3.0", "proxyquire": "^1.8.0", "semistandard": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc-1.0.3.tgz_1511377655799_0.23113705799914896", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "detect-libc", "version": "2.0.0", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@2.0.0", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "dist": {"shasum": "c528bc09bc6d1aa30149228240917c225448f204", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-S55LzUl8HUav8l9E2PBTlC5PAJrHK7tkM+XXFGD+fbsbkTzhCpG6K05LxJcUOEWzMa4v6ptcMZ9s3fOdJDu0Zw==", "signatures": [{"sig": "MEYCIQDCgxVVhTGDg14IAp6DBEy14gBvsyzowEHP9h+XnWHCUgIhAJtAQlVcyZmz/ndfO3Qb3HQtQ5Dh6yyVrQpnzRweL4Zz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5+ZzCRA9TVsSAnZWagAAyMIP/0QJW0WS/Wq41gO033QA\ngohg2JJmIWvUYN4YcMwHmxWKfXSBmetup8inoAt5Yze5nSiDKMwcpfWV6rOo\ng9FPuHNCXbQBcHniueSxsCYt9bdQnn8u1Tt4fIZZtmcBasfctg2XB9nNRraD\nuMTTQ09WZSZS/3iLbj6I6RapLM2+GcBiEQ43UJA5jnmuXVY8z/QARULKLT4X\nPXIrjquUecGOEj8LBp6r2D5Yj9bkeVmw44W9n7B0zb3/BqvXk1fSOPyX4Wuq\n7NisXT1GtLTf4x62NbNyk+n7Azay6dQkmmwdFukqtaTm8Oo3aUVlsOgg9ded\nE6izpoocBiys5XH/1SHI394Lt5/QftVf6XHqJswcBWegMUl3lro0Jug0hTCs\nGIVXWbg/x5CYLs0yg356sxGGBH/mZireuDoMTOMf/hkm/2g7lrNI+Wx3/PXr\nBOjpOgaN4UhBe8wT8OK1ie/KViv6+4DZIC5Ro2BcOlSy6fNsshMrivFzRJTm\nsBS+cd0LeEu0Fc4tLb2gHpnBBeirj1ornHlBuO9eQRlXOHK7+UhFXQK0ttV/\n8xNdXdDp9i0cjIZFZqPp+1AnoIhz9Pu9YWHJv6/N6N0fEFU4CX+5AnyBFrJr\nk4V9mAQE84GgCjsybm2GZe1BlUlo6QvDdo1M6b5sK50Me3T0Xp0opo+TPN+e\navNu\r\n=dQ0F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/detect-libc.js", "engines": {"node": ">=8"}, "gitHead": "19e2c00f8f6fc50cefb83c3fd3d774cf8f0e4918", "scripts": {"test": "semistandard && nyc --reporter=lcov --check-coverage --branches=100 ava test/unit.js"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"ava": "^2.4.0", "nyc": "^15.1.0", "proxyquire": "^2.1.3", "semistandard": "^14.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc_2.0.0_1642587763020_0.6191330126779362", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "detect-libc", "version": "2.0.1", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@2.0.1", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "dist": {"shasum": "e1897aa88fa6ad197862937fbc0441ef352ee0cd", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.1.tgz", "fileCount": 6, "integrity": "sha512-463v3ZeIrcWtdgIg6vI6XUncguvr2TnGl4SzDXinkt9mSLpBJKXT3mW6xT3VQdDN11+WVs29pgvivTc4Lp8v+w==", "signatures": [{"sig": "MEUCIQCVt+pj++dTLbd32QrUFiARR9iSnPYSFc4TP1K423+dKwIgHXDepo8iEQnnmN0pShAXfyBRlQb91Uk+BYGMdMox+2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCjJ6CRA9TVsSAnZWagAA1cwP/2Q4wUvWMDg8+3h6tuJp\nWE1lsKmbe+im1T0oEGR9jAt048ws5FbrNAllVXJRisLIO66uen5JXmJ3CJBO\nqqz2FxDWH6kRHIa0y/UP+YKglz5xW3y3qBc8VXTN8jmdmwWcwmE8M/KOR2Xw\ndUKVyGHK5tcDEhFM3ERl0+kWiMmoiq1UhJnV38C5ma4jra4m9mvU+YG1gcW7\nwlEZq4BUw/6LZNcdWqLv8XkNl3SpvEd+7QZQtcj9g/42ifc0RzLT1emCiDZ4\nLL3l5Eh/rLfWoMApTElAPeD9lf/IqeklaKfGeBbV67H4sMzFnb9XKMP6jjw/\nppI9HldyJ2QvIOYGSsUp5cyDn+1dpSRcKbYe2XXmRrg1MhgYo1wduqGM/zCK\nh5MeQMPY017cLqBGZKRlgFzdQRCd6Gcerz4veQOa3DDbQSVPZ9p5O8ABPRh+\n3wyD31TJSddt++9F0GW2SK+0vlkCS2/5ZRkEnbZkD/fD/raIdEenp++BQrIL\nN/zcytaucvagAscEnSDu9nD7qVhAJJfQJWtJTFiyWmOOijZXpqBL/IV5QRDU\nV8Xrmn2VhaSnC6gl/11+pnh9wddNw42orw2rSmRfc6/tSlpybsjCLW1waTSK\nMroLGj96fEaDn2Ti40pzBb9lQjcOTH9cI+R9BGTp+mytHQx0peR42nCOSfgQ\nkkYO\r\n=MIrN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/detect-libc.js", "engines": {"node": ">=8"}, "gitHead": "0e10bbb465414b99078fcdd9c69b0bc2df3ed745", "scripts": {"test": "semistandard && nyc --reporter=lcov --check-coverage --branches=100 ava test/unit.js"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "14.19.0", "_hasShrinkwrap": false, "devDependencies": {"ava": "^2.4.0", "nyc": "^15.1.0", "proxyquire": "^2.1.3", "semistandard": "^14.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc_2.0.1_1644835450082_0.27267816695886715", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "detect-libc", "version": "2.0.2", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@2.0.2", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "dist": {"shasum": "8ccf2ba9315350e1241b88d0ac3b0e1fbd99605d", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.2.tgz", "fileCount": 7, "integrity": "sha512-UX6sGumvvqSaXgdKGUsgZWqcUyIXZ/vZTrlRT/iobiKhGL0zL4d3osHj3uqllWJK+i+sixDS/3COVEOFbupFyw==", "signatures": [{"sig": "MEYCIQDrxCrjVuj74NBighmDY0rch2KpYOp4haJFxAEoLeNCMQIhAJ1eVDXEnOJu9tv6lWMLUrHUMDYNhXEYXgv/DUhG7tTz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23709}, "main": "lib/detect-libc.js", "engines": {"node": ">=8"}, "gitHead": "f0edbc8f49b2f531aac64bd1d695d7386f4d569f", "scripts": {"test": "semistandard && nyc --reporter=lcov --check-coverage --branches=100 ava test/unit.js", "bench": "node benchmark/detect-libc", "bench:calls": "node benchmark/call-familySync.js && sleep 1 && node benchmark/call-isNonGlibcLinuxSync.js && sleep 1 && node benchmark/call-versionSync.js"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "devDependencies": {"ava": "^2.4.0", "nyc": "^15.1.0", "benchmark": "^2.1.4", "proxyquire": "^2.1.3", "semistandard": "^14.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc_2.0.2_1689669481632_0.5067082093348458", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "detect-libc", "version": "2.0.3", "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "detect-libc@2.0.3", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lovell/detect-libc#readme", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "dist": {"shasum": "f0cd503b40f9939b894697d19ad50895e30cf700", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.3.tgz", "fileCount": 7, "integrity": "sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==", "signatures": [{"sig": "MEUCIQC5j6+uFZWEBg+ptGm+rSH2FTJ4yTqrYVN996IBCpAZNwIgfgW2WPetACotvN4wLoP/InReqI5nDqFPCioVMIqLXc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23627}, "main": "lib/detect-libc.js", "engines": {"node": ">=8"}, "gitHead": "db8ea62694a6bd274366c2b4d982b13c074537a3", "scripts": {"test": "semistandard && nyc --reporter=text --check-coverage --branches=100 ava test/unit.js", "bench": "node benchmark/detect-libc", "bench:calls": "node benchmark/call-familySync.js && sleep 1 && node benchmark/call-isNonGlibcLinuxSync.js && sleep 1 && node benchmark/call-versionSync.js"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lovell/detect-libc.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "devDependencies": {"ava": "^2.4.0", "nyc": "^15.1.0", "benchmark": "^2.1.4", "proxyquire": "^2.1.3", "semistandard": "^14.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/detect-libc_2.0.3_1710842272536_0.236372110431194", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "detect-libc", "version": "2.0.4", "description": "Node.js module to detect the C standard library (libc) implementation family and version", "main": "lib/detect-libc.js", "scripts": {"test": "semistandard && nyc --reporter=text --check-coverage --branches=100 ava test/unit.js", "bench": "node benchmark/detect-libc", "bench:calls": "node benchmark/call-familySync.js && sleep 1 && node benchmark/call-isNonGlibcLinuxSync.js && sleep 1 && node benchmark/call-versionSync.js"}, "repository": {"type": "git", "url": "git://github.com/lovell/detect-libc.git"}, "keywords": ["libc", "glibc", "musl"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "Apache-2.0", "devDependencies": {"ava": "^2.4.0", "benchmark": "^2.1.4", "nyc": "^15.1.0", "proxyquire": "^2.1.3", "semistandard": "^14.2.3"}, "engines": {"node": ">=8"}, "types": "index.d.ts", "_id": "detect-libc@2.0.4", "gitHead": "0b468c7302909c1376756aba59bd681126702be9", "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "homepage": "https://github.com/lovell/detect-libc#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==", "shasum": "f04715b8ba815e53b4d8109655b6508a6865a7e8", "tarball": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz", "fileCount": 7, "unpackedSize": 23652, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDrPscaj3SfovbJZbEipgyVwgHCf0uuy8Y+DEy4MX3/PAIhAI68T+LB6S6NykjPBknG4P9DBLXCW5pea/1Nrpp0qgc1"}]}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/detect-libc_2.0.4_1745317603644_0.7962491150981554"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-07-03T20:58:20.378Z", "modified": "2025-04-22T10:26:43.977Z", "0.0.1": "2017-07-03T20:58:20.378Z", "0.0.2": "2017-07-03T21:43:09.271Z", "0.0.3": "2017-07-05T16:14:37.064Z", "0.0.4": "2017-07-05T18:45:49.805Z", "0.0.5": "2017-07-05T18:53:14.471Z", "0.0.6": "2017-07-11T20:14:11.487Z", "0.0.7": "2017-07-12T09:13:25.991Z", "0.0.8": "2017-07-12T12:27:18.327Z", "0.1.0": "2017-07-12T12:48:18.673Z", "0.2.0": "2017-07-13T10:39:20.732Z", "1.0.0": "2017-10-27T19:14:37.414Z", "1.0.1": "2017-10-28T08:31:32.893Z", "1.0.2": "2017-10-29T09:28:17.271Z", "1.0.3": "2017-11-22T19:07:36.743Z", "2.0.0": "2022-01-19T10:22:43.396Z", "2.0.1": "2022-02-14T10:44:10.231Z", "2.0.2": "2023-07-18T08:38:01.781Z", "2.0.3": "2024-03-19T09:57:52.683Z", "2.0.4": "2025-04-22T10:26:43.824Z"}, "bugs": {"url": "https://github.com/lovell/detect-libc/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://github.com/lovell/detect-libc#readme", "keywords": ["libc", "glibc", "musl"], "repository": {"type": "git", "url": "git://github.com/lovell/detect-libc.git"}, "description": "Node.js module to detect the C standard library (libc) implementation family and version", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}], "readme": "# detect-libc\n\nNode.js module to detect details of the C standard library (libc)\nimplementation provided by a given Linux system.\n\nCurrently supports detection of GNU glibc and MUSL libc.\n\nProvides asychronous and synchronous functions for the\nfamily (e.g. `glibc`, `musl`) and version (e.g. `1.23`, `1.2.3`).\n\nThe version numbers of libc implementations\nare not guaranteed to be semver-compliant.\n\nFor previous v1.x releases, please see the\n[v1](https://github.com/lovell/detect-libc/tree/v1) branch.\n\n## Install\n\n```sh\nnpm install detect-libc\n```\n\n## API\n\n### GLIBC\n\n```ts\nconst GLIBC: string = 'glibc';\n```\n\nA String constant containing the value `glibc`.\n\n### MUSL\n\n```ts\nconst MUSL: string = 'musl';\n```\n\nA String constant containing the value `musl`.\n\n### family\n\n```ts\nfunction family(): Promise<string | null>;\n```\n\nResolves asychronously with:\n\n* `glibc` or `musl` when the libc family can be determined\n* `null` when the libc family cannot be determined\n* `null` when run on a non-Linux platform\n\n```js\nconst { family, GLIBC, MUSL } = require('detect-libc');\n\nswitch (await family()) {\n  case GLIBC: ...\n  case MUSL: ...\n  case null: ...\n}\n```\n\n### familySync\n\n```ts\nfunction familySync(): string | null;\n```\n\nSynchronous version of `family()`.\n\n```js\nconst { familySync, GLIBC, MUSL } = require('detect-libc');\n\nswitch (familySync()) {\n  case GLIBC: ...\n  case MUSL: ...\n  case null: ...\n}\n```\n\n### version\n\n```ts\nfunction version(): Promise<string | null>;\n```\n\nResolves asychronously with:\n\n* The version when it can be determined\n* `null` when the libc family cannot be determined\n* `null` when run on a non-Linux platform\n\n```js\nconst { version } = require('detect-libc');\n\nconst v = await version();\nif (v) {\n  const [major, minor, patch] = v.split('.');\n}\n```\n\n### versionSync\n\n```ts\nfunction versionSync(): string | null;\n```\n\nSynchronous version of `version()`.\n\n```js\nconst { versionSync } = require('detect-libc');\n\nconst v = versionSync();\nif (v) {\n  const [major, minor, patch] = v.split('.');\n}\n```\n\n### isNonGlibcLinux\n\n```ts\nfunction isNonGlibcLinux(): Promise<boolean>;\n```\n\nResolves asychronously with:\n\n* `false` when the libc family is `glibc`\n* `true` when the libc family is not `glibc`\n* `false` when run on a non-Linux platform\n\n```js\nconst { isNonGlibcLinux } = require('detect-libc');\n\nif (await isNonGlibcLinux()) { ... }\n```\n\n### isNonGlibcLinuxSync\n\n```ts\nfunction isNonGlibcLinuxSync(): boolean;\n```\n\nSynchronous version of `isNonGlibcLinux()`.\n\n```js\nconst { isNonGlibcLinuxSync } = require('detect-libc');\n\nif (isNonGlibcLinuxSync()) { ... }\n```\n\n## Licensing\n\nCopyright 2017 Lovell Fuller and others.\n\nLicensed under the Apache License, Version 2.0 (the \"License\");\nyou may not use this file except in compliance with the License.\nYou may obtain a copy of the License at [http://www.apache.org/licenses/LICENSE-2.0](http://www.apache.org/licenses/LICENSE-2.0.html)\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an \"AS IS\" BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\nSee the License for the specific language governing permissions and\nlimitations under the License.\n", "readmeFilename": "README.md"}