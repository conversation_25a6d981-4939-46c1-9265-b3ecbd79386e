@extends('layouts.admin')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<!-- Thống kê tổng quan -->
<div class="row g-3 mb-4">
    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-primary">
                    <i class="fas fa-users"></i>
                </div>
                <h2 class="stats-number">{{ number_format($totalCustomers) }}</h2>
                <p class="stats-label mb-0">Khách hàng</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    Tăng trưởng tốt
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-success">
                    <i class="fas fa-box"></i>
                </div>
                <h2 class="stats-number">{{ number_format($totalServicePackages) }}</h2>
                <p class="stats-label mb-0">Gói dịch vụ</p>
                <small class="text-info">
                    <i class="fas fa-chart-line me-1"></i>
                    Đa dạng sản phẩm
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-info">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2 class="stats-number">{{ number_format($totalActiveServices) }}</h2>
                <p class="stats-label mb-0">Dịch vụ hoạt động</p>
                <small class="text-primary">
                    <i class="fas fa-sync-alt me-1"></i>
                    Đang vận hành
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2 class="stats-number">{{ number_format($expiringSoonServices) }}</h2>
                <p class="stats-label mb-0">Sắp hết hạn</p>
                <small class="text-warning">
                    <i class="fas fa-clock me-1"></i>
                    Cần chú ý
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Quick Action - Gán dịch vụ -->
<div class="row g-3 mb-4">
    <div class="col-12">
        <div class="card quick-assign-card shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body py-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <div class="me-4">
                                <div class="bg-white bg-opacity-20 rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                    <i class="fas fa-link fa-xl text-white"></i>
                                </div>
                            </div>
                            <div class="text-white">
                                <h4 class="mb-2 fw-bold">Gán dịch vụ cho khách hàng</h4>
                                <p class="mb-2 opacity-90 fs-6">Kích hoạt dịch vụ mới cho khách hàng một cách nhanh chóng và hiệu quả</p>
                                <div class="d-flex align-items-center gap-3 opacity-85">
                                    <small>
                                        <i class="fas fa-calendar-day me-1"></i>
                                        Hôm nay: <strong>{{ $assignmentsToday }}</strong> dịch vụ
                                    </small>
                                    <small>
                                        <i class="fas fa-calendar-week me-1"></i>
                                        Tuần này: <strong>{{ $assignmentsThisWeek }}</strong> dịch vụ
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end text-center mt-3 mt-md-0">
                        <a href="{{ route('admin.customer-services.create') }}"
                           class="btn btn-light btn-lg px-5 py-3 fw-bold quick-assign-btn">
                            <i class="fas fa-plus me-2"></i>
                            Gán dịch vụ ngay
                            <i class="fas fa-arrow-right ms-2"></i>
                        </a>
                        <div class="mt-2">
                            <small class="text-white opacity-75">
                                <i class="fas fa-clock me-1"></i>
                                Chỉ mất vài phút
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Service Assignments -->
@if($recentAssignments->count() > 0)
<div class="row g-3 mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 text-muted">
                        <i class="fas fa-history me-2"></i>
                        Dịch vụ được gán gần đây
                    </h6>
                    <a href="{{ route('admin.customer-services.index') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="row g-0 recent-assignments">
                    @foreach($recentAssignments as $index => $assignment)
                    <div class="col-lg-4 col-md-6 col-12">
                        <div class="p-3 {{ $index < 2 ? 'border-end' : '' }} {{ $index == 0 ? 'border-bottom border-md-0' : ($index == 1 ? 'border-bottom border-lg-0' : '') }} h-100">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <div class="bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center" style="width: 45px; height: 45px;">
                                        <i class="fas fa-check text-success fa-lg"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold">{{ $assignment->customer->name }}</div>
                                    <div class="text-primary small fw-semibold">{{ $assignment->servicePackage->name }}</div>
                                    <div class="text-success small">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ $assignment->created_at->diffForHumans() }}
                                    </div>
                                </div>
                                <div class="ms-2">
                                    <a href="{{ route('admin.customer-services.show', $assignment) }}"
                                       class="btn btn-sm btn-outline-success rounded-pill"
                                       data-bs-toggle="tooltip"
                                       title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Lead Statistics -->
<div class="row g-3 mb-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-user-plus text-primary me-2"></i>
            Thống kê Lead (Khách hàng tiềm năng)
        </h4>
    </div>

    <div class="col-xl-2-4 col-lg-4 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-info">
                    <i class="fas fa-users"></i>
                </div>
                <h2 class="stats-number">{{ number_format($totalLeads) }}</h2>
                <p class="stats-label mb-0">Tổng Lead</p>
                <small class="text-info">
                    <i class="fas fa-chart-line me-1"></i>
                    Tất cả lead
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-2-4 col-lg-4 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-success">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h2 class="stats-number">{{ number_format($newLeads) }}</h2>
                <p class="stats-label mb-0">Lead mới</p>
                <small class="text-success">
                    <i class="fas fa-plus me-1"></i>
                    Chưa xử lý
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-2-4 col-lg-4 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-warning">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <h2 class="stats-number">{{ number_format($followUpTodayLeads) }}</h2>
                <p class="stats-label mb-0">Cần theo dõi hôm nay</p>
                <small class="text-warning">
                    <i class="fas fa-clock me-1"></i>
                    Hôm nay
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-2-4 col-lg-4 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2 class="stats-number">{{ number_format($overdueLeads) }}</h2>
                <p class="stats-label mb-0">Quá hạn</p>
                <small class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Cần xử lý gấp
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-2-4 col-lg-4 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-primary">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2 class="stats-number">{{ number_format($convertedThisMonth) }}</h2>
                <p class="stats-label mb-0">Chuyển đổi tháng này</p>
                <small class="text-primary">
                    <i class="fas fa-trophy me-1"></i>
                    Thành công
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Lead cần chú ý -->
<div class="row g-4 mb-4">
    <div class="col-xl-6 col-lg-6">
        <div class="card ">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                        Lead khẩn cấp
                    </h5>
                    <a href="{{ route('admin.leads.index', ['priority' => 'urgent']) }}"
                        class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                @if($urgentLeads->count() > 0)
                @foreach($urgentLeads as $lead)
                <div class="d-flex align-items-center p-3 border-bottom">
                    <div class="avatar-sm me-3">
                        <div class="avatar-initial bg-danger rounded-circle">
                            {{ substr($lead->name, 0, 1) }}
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ $lead->name }}</h6>
                        <p class="text-muted mb-1">
                            <i class="fas fa-phone me-1"></i>{{ $lead->phone }}
                            @if($lead->servicePackage)
                            | {{ $lead->servicePackage->name }}
                            @endif
                        </p>
                        <small class="text-muted">
                            Tạo: {{ $lead->created_at->diffForHumans() }}
                            @if($lead->assignedUser)
                            | PV: {{ $lead->assignedUser->name }}
                            @endif
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-danger">{{ $lead->getPriorityName() }}</span>
                        <br><small class="text-muted">{{ $lead->getStatusName() }}</small>
                    </div>
                </div>
                @endforeach
                @else
                <div class="text-center py-4">
                    <div class="empty-icon mb-3">
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    <h5 class="text-muted">Không có lead khẩn cấp</h5>
                    <p class="text-muted mb-0">Tất cả lead đều được xử lý tốt</p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-lg-6">
        <div class="card ">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-warning"></i>
                        Lead quá hạn theo dõi
                    </h5>
                    <a href="{{ route('admin.leads.index', ['overdue' => 1]) }}"
                        class="btn btn-sm btn-outline-warning">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                @if($overdueLeadsList->count() > 0)
                @foreach($overdueLeadsList as $lead)
                <div class="d-flex align-items-center p-3 border-bottom">
                    <div class="avatar-sm me-3">
                        <div class="avatar-initial bg-warning rounded-circle">
                            {{ substr($lead->name, 0, 1) }}
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ $lead->name }}</h6>
                        <p class="text-muted mb-1">
                            <i class="fas fa-phone me-1"></i>{{ $lead->phone }}
                            @if($lead->servicePackage)
                            | {{ $lead->servicePackage->name }}
                            @endif
                        </p>
                        <small class="text-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Quá hạn {{ $lead->getDaysOverdue() }} ngày
                        </small>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">
                            {{ $lead->next_follow_up_at->format('d/m/Y') }}
                        </small>
                        <br><span class="badge {{ $lead->getStatusBadgeClass() }}">{{ $lead->getStatusName() }}</span>
                    </div>
                </div>
                @endforeach
                @else
                <div class="text-center py-4">
                    <div class="empty-icon mb-3">
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    <h5 class="text-muted">Không có lead quá hạn</h5>
                    <p class="text-muted mb-0">Tất cả lead đều được theo dõi đúng hạn</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- Dịch vụ sắp hết hạn -->
    <div class="col-xl-8 col-lg-7">
        <div class="card ">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-warning"></i>
                        Dịch vụ sắp hết hạn (5 ngày tới)
                    </h5>
                    <a href="{{ route('admin.customer-services.index', ['filter' => 'expiring']) }}"
                        class="btn btn-sm btn-outline-warning">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                @if($expiringSoon->count() > 0)
                <div class="table-container">
                    <table class="table mb-0">
                        <thead>
                            <tr>
                                <th><i class="fas fa-user me-2"></i>Khách hàng</th>
                                <th><i class="fas fa-box-open me-2"></i>Dịch vụ</th>
                                <th><i class="fas fa-calendar-times me-2"></i>Hết hạn</th>
                                <th><i class="fas fa-hourglass-half me-2"></i>Còn lại</th>
                                <th><i class="fas fa-bell me-2"></i>Nhắc nhở</th>
                                <th><i class="fas fa-cogs me-2"></i>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($expiringSoon as $service)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-initial rounded-circle bg-primary text-white me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                            {{ substr($service->customer->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ $service->customer->name }}</div>
                                            <small class="text-muted">{{ $service->customer->customer_code }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="fw-semibold">{{ $service->servicePackage->name }}</div>
                                    <small class="text-muted">{{ $service->servicePackage->category->name ?? 'N/A' }}</small>
                                </td>
                                <td>
                                    <div class="fw-semibold">{{ $service->expires_at->format('d/m/Y') }}</div>
                                    <small class="text-muted">{{ $service->expires_at->format('H:i') }}</small>
                                </td>
                                <td>
                                    @php
                                    $daysRemaining = $service->getDaysRemaining();
                                    @endphp
                                    @if($daysRemaining <= 1)
                                        <span class="badge bg-danger">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        {{ $daysRemaining }} ngày
                                        </span>
                                        @elseif($daysRemaining <= 3)
                                            <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ $daysRemaining }} ngày
                                            </span>
                                            @else
                                            <span class="badge bg-info">
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ $daysRemaining }} ngày
                                            </span>
                                            @endif
                                </td>
                                <td class="text-center">
                                    @if($service->reminder_count > 0)
                                        <div class="mb-1">
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>
                                                Đã nhắc {{ $service->reminder_count }} lần
                                            </span>
                                        </div>
                                        @if($service->reminder_sent_at)
                                            <small class="text-muted">
                                                Lần cuối: {{ $service->reminder_sent_at->format('d/m H:i') }}
                                            </small>
                                        @endif
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            Chưa nhắc (ID: {{ $service->id }})
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ route('admin.customers.show', $service->customer) }}"
                                            class="btn btn-sm btn-outline-primary"
                                            data-bs-toggle="tooltip"
                                            title="Xem chi tiết khách hàng">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.customer-services.show', $service) }}"
                                            class="btn btn-sm btn-outline-info"
                                            data-bs-toggle="tooltip"
                                            title="Xem chi tiết dịch vụ">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        {{-- Debug: Service ID {{ $service->id }}, Reminder Count: {{ $service->reminder_count }} --}}
                                        @if($service->reminder_count > 0)
                                            <button type="button"
                                                class="btn btn-sm btn-success"
                                                data-bs-toggle="tooltip"
                                                title="Đã nhắc nhở {{ $service->reminder_count }} lần"
                                                disabled>
                                                <i class="fas fa-check"></i>
                                            </button>
                                        @else
                                            <button type="button"
                                                class="btn btn-sm btn-outline-warning mark-reminded-btn"
                                                data-bs-toggle="tooltip"
                                                title="Đánh dấu đã nhắc nhở (ID: {{ $service->id }})"
                                                data-service-id="{{ $service->id }}"
                                                onclick="markAsReminded({{ $service->id }})"
                                                style="background-color: #ffc107; color: #000; border: 2px solid #ff6b35;">
                                                <i class="fas fa-bell"></i> Nhắc
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-check-circle fa-3x text-success opacity-50"></i>
                    </div>
                    <h5 class="text-muted">Không có dịch vụ nào sắp hết hạn</h5>
                    <p class="text-muted mb-0">Tất cả dịch vụ đều hoạt động bình thường</p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Khách hàng mới nhất -->
    <div class="col-xl-4 col-lg-5">
        <div class="card ">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2 text-primary"></i>
                    Khách hàng mới nhất
                </h5>
            </div>
            <div class="card-body">
                @if($recentCustomers->count() > 0)
                <div class="customer-list">
                    @foreach($recentCustomers as $customer)
                    <div class="d-flex align-items-center p-3 mb-3 bg-light rounded">
                        <div class="avatar-initial bg-primary text-white rounded-circle me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            {{ substr($customer->name, 0, 1) }}
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-semibold">{{ $customer->name }}</div>
                            <div class="text-muted small">
                                <span class="me-3">{{ $customer->customer_code }}</span>
                                <span>{{ $customer->customerServices->count() }} dịch vụ</span>
                            </div>
                            <small class="text-muted">
                                {{ $customer->created_at->diffForHumans() }}
                            </small>
                        </div>
                        <a href="{{ route('admin.customers.show', $customer) }}"
                            class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                    @endforeach
                </div>

                <div class="text-center mt-3">
                    <a href="{{ route('admin.customers.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>
                        Xem tất cả khách hàng
                    </a>
                </div>
                @else
                <div class="text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-user-plus fa-2x text-muted opacity-50"></i>
                    </div>
                    <p class="text-muted mb-0">Chưa có khách hàng mới</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
<!-- Dịch vụ phổ biến và Bài đăng -->
<div class="row g-4 mt-2">
    <!-- Dịch vụ phổ biến -->
    <div class="col-12">
        <div class="card ">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2 text-success"></i>
                    Dịch vụ phổ biến nhất
                </h5>
            </div>
            <div class="card-body">
                @if($popularServices->count() > 0)
                <div class="row g-3">
                    @foreach($popularServices as $index => $service)
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <div class="service-card text-center p-4 rounded-3 h-100" style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%); border: 2px solid rgba(16, 185, 129, 0.2); transition: all 0.3s ease;">
                            <div class="service-rank position-absolute top-0 start-0 m-2">
                                <span class="badge bg-success rounded-pill">#{{ $index + 1 }}</span>
                            </div>
                            <div class="service-icon mb-3">
                                <div class="icon-wrapper mx-auto" style="width: 60px; height: 60px; background: var(--success-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-box fa-2x text-white"></i>
                                </div>
                            </div>
                            <h6 class="fw-bold mb-2">{{ $service->name }}</h6>
                            <div class="mb-2">
                                <span class="badge bg-primary">
                                    <i class="fas fa-users me-1"></i>
                                    {{ $service->customer_services_count }} khách hàng
                                </span>
                            </div>
                            @if($service->category)
                            <small class="text-muted">{{ $service->category->name }}</small>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-chart-bar fa-3x text-muted opacity-50"></i>
                    </div>
                    <h5 class="text-muted">Chưa có dữ liệu thống kê</h5>
                    <p class="text-muted mb-0">Dữ liệu sẽ hiển thị khi có khách hàng sử dụng dịch vụ</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Content Posts Status -->
<div class="row g-4 mt-2">
    <div class="col-lg-6">
        <div class="card ">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2 text-info"></i>
                        Bài đăng sắp tới (24h)
                    </h5>
                    <a href="{{ route('admin.content-scheduler.index') }}" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if($upcomingPosts->count() > 0)
                <div class="post-list">
                    @foreach($upcomingPosts as $post)
                    <div class="post-item d-flex align-items-center p-3 rounded-3 mb-3" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(29, 78, 216, 0.05) 100%); border-left: 4px solid var(--info-gradient);">
                        <div class="post-icon me-3">
                            <div class="icon-wrapper" style="width: 45px; height: 45px; background: var(--info-gradient); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-calendar text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold mb-1">{{ $post->title }}</div>
                            <div class="d-flex align-items-center text-muted small">
                                <i class="fas fa-clock me-1"></i>
                                <span class="me-3">{{ $post->scheduled_at->format('d/m/Y H:i') }}</span>
                                <i class="fas fa-users me-1"></i>
                                <span>{{ $post->target_groups_string }}</span>
                            </div>
                            <div class="mt-1">
                                <span class="badge bg-info">
                                    {{ $post->scheduled_at->diffForHumans() }}
                                </span>
                            </div>
                        </div>
                        <div class="ms-2">
                            <a href="{{ route('admin.content-scheduler.show', $post) }}"
                                class="btn btn-sm btn-outline-info rounded-pill"
                                data-bs-toggle="tooltip"
                                title="Xem chi tiết">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-calendar-check fa-2x text-success opacity-50"></i>
                    </div>
                    <h6 class="text-muted">Không có bài đăng nào trong 24h tới</h6>
                    <p class="text-muted mb-0 small">Lịch đăng bài trống</p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card ">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                        Bài đăng quá hạn
                    </h5>
                    <a href="{{ route('admin.content-scheduler.index', ['status' => 'scheduled']) }}" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-eye me-1"></i>
                        Xem tất cả
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if($overduePosts->count() > 0)
                <div class="post-list">
                    @foreach($overduePosts as $post)
                    <div class="post-item d-flex align-items-center p-3 rounded-3 mb-3" style="background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(220, 38, 38, 0.05) 100%); border-left: 4px solid var(--danger-gradient);">
                        <div class="post-icon me-3">
                            <div class="icon-wrapper" style="width: 45px; height: 45px; background: var(--danger-gradient); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-exclamation text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold mb-1">{{ $post->title }}</div>
                            <div class="d-flex align-items-center text-muted small">
                                <i class="fas fa-clock me-1"></i>
                                <span class="me-3">Đã quá {{ $post->scheduled_at->diffForHumans() }}</span>
                                <i class="fas fa-users me-1"></i>
                                <span>{{ $post->target_groups_string }}</span>
                            </div>
                            <div class="mt-1">
                                <span class="badge bg-danger">
                                    Quá hạn
                                </span>
                            </div>
                        </div>
                        <div class="ms-2">
                            <a href="{{ route('admin.content-scheduler.edit', $post) }}"
                                class="btn btn-sm btn-outline-danger rounded-pill"
                                data-bs-toggle="tooltip"
                                title="Chỉnh sửa">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-check-circle fa-2x text-success opacity-50"></i>
                    </div>
                    <h6 class="text-muted">Không có bài đăng quá hạn</h6>
                    <p class="text-muted mb-0 small">Lịch đăng bài được quản lý tốt</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mt-2">
    <div class="col-12">
        <div class="card ">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>
                    Thao tác nhanh
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <a href="{{ route('admin.customers.create') }}" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" style="min-height: 120px;">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span class="fw-bold">Thêm khách hàng</span>
                            <small class="text-white-50 mt-1">Khách hàng mới</small>
                        </a>
                    </div>
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <a href="{{ route('admin.service-packages.create') }}" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" style="min-height: 120px;">
                            <i class="fas fa-box fa-2x mb-2"></i>
                            <span class="fw-bold">Thêm gói dịch vụ</span>
                            <small class="text-white-50 mt-1">Sản phẩm mới</small>
                        </a>
                    </div>

                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <a href="{{ route('admin.content-scheduler.create') }}" class="btn btn-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" style="min-height: 120px;">
                            <i class="fas fa-calendar-plus fa-2x mb-2"></i>
                            <span class="fw-bold">Tạo bài đăng</span>
                            <small class="text-white-50 mt-1">Lên lịch đăng</small>
                        </a>
                    </div>

                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                        <a href="{{ route('admin.reports.profit') }}" class="btn btn-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" style="min-height: 120px;">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <span class="fw-bold">Báo cáo</span>
                            <small class="text-white-50 mt-1">Lợi nhuận</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
    /* 5-column layout for lead stats */
    .col-xl-2-4 {
        flex: 0 0 auto;
        width: 20%;
    }

    @media (max-width: 1199.98px) {
        .col-xl-2-4 {
            width: 25%;
        }
    }

    @media (max-width: 991.98px) {
        .col-xl-2-4 {
            width: 50%;
        }
    }

    @media (max-width: 575.98px) {
        .col-xl-2-4 {
            width: 100%;
        }
    }

    /* Quick Action Card Styles */
    .quick-assign-card {
        transition: all 0.3s ease;
        border: none !important;
        overflow: hidden;
        position: relative;
    }

    .quick-assign-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .quick-assign-card:hover::before {
        left: 100%;
    }

    .quick-assign-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .quick-assign-btn {
        transition: all 0.3s ease;
        border: 2px solid rgba(255,255,255,0.3);
        backdrop-filter: blur(10px);
    }

    .quick-assign-btn:hover {
        transform: scale(1.05);
        border-color: rgba(255,255,255,0.8);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    @media (max-width: 768px) {
        .quick-assign-card .card-body {
            text-align: center;
        }

        .quick-assign-card .row {
            flex-direction: column;
        }

        .quick-assign-card .col-md-8,
        .quick-assign-card .col-md-4 {
            width: 100%;
            margin-bottom: 1rem;
        }
    }

    /* Recent Assignments Responsive */
    @media (max-width: 991.98px) {
        .recent-assignments .border-end {
            border-right: none !important;
        }

        .recent-assignments .border-bottom {
            border-bottom: 1px solid #dee2e6 !important;
        }
    }

    @media (max-width: 767.98px) {
        .recent-assignments .col-md-6:last-child .border-bottom {
            border-bottom: none !important;
        }
    }

    /* Enhanced Dashboard Styles */
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }

    .quick-action-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        cursor: pointer;
    }

    .quick-action-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .metric-card {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-left: 4px solid var(--primary);
    }

    .activity-item {
        border-left: 3px solid var(--primary);
        background: rgba(102, 126, 234, 0.05);
    }

    .progress-ring {
        width: 60px;
        height: 60px;
    }

    .progress-ring circle {
        fill: transparent;
        stroke-width: 4;
        stroke-linecap: round;
    }

    /* Mark Reminded Button Styles */
    .mark-reminded-btn {
        transition: all 0.3s ease;
        min-width: 40px;
    }

    .mark-reminded-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    }

    .mark-reminded-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    /* Table responsive improvements */
    .table-container {
        overflow-x: auto;
        min-height: 200px;
    }

    .table th, .table td {
        white-space: nowrap;
        vertical-align: middle;
    }

    .btn-group .btn {
        margin: 0 1px;
    }
</style>
@endpush

@section('scripts')
<script>
    // Mark service as reminded function
    function markAsReminded(serviceId) {
        // Tìm button để cập nhật UI
        const button = document.querySelector(`[data-service-id="${serviceId}"]`);

        if (confirm('Bạn có chắc chắn muốn đánh dấu đã nhắc nhở khách hàng về dịch vụ này?')) {
            // Disable button ngay để tránh click nhiều lần
            if (button) {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            }

            fetch(`/admin/customer-services/${serviceId}/mark-reminded`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    notes: 'Đánh dấu từ Dashboard'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showAlert('Đã đánh dấu nhắc nhở thành công!', 'success');

                    // Update button appearance
                    if (button) {
                        button.classList.remove('btn-outline-warning', 'mark-reminded-btn');
                        button.classList.add('btn-success');
                        button.innerHTML = '<i class="fas fa-check"></i>';
                        button.title = `Đã nhắc nhở ${data.reminder_count} lần`;
                        button.disabled = true;
                        button.removeAttribute('onclick');
                    }

                    // Update reminder status in the table
                    const row = button.closest('tr');
                    if (row) {
                        const reminderCell = row.querySelector('td:nth-child(5)'); // Cột nhắc nhở
                        if (reminderCell) {
                            reminderCell.innerHTML = `
                                <div class="mb-1">
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>
                                        Đã nhắc ${data.reminder_count} lần
                                    </span>
                                </div>
                                <small class="text-muted">
                                    Lần cuối: ${data.reminder_sent_at}
                                </small>
                            `;
                        }
                    }
                } else {
                    showAlert('Có lỗi xảy ra: ' + (data.message || 'Lỗi không xác định'), 'error');
                    // Restore button if error
                    if (button) {
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-bell"></i>';
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Có lỗi xảy ra khi đánh dấu nhắc nhở!', 'error');
                // Restore button if error
                if (button) {
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-bell"></i>';
                }
            });
        } else {
            // User cancelled, restore button if it was disabled
            if (button && button.disabled) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-bell"></i>';
            }
        }
    }

    // Show alert function
    function showAlert(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Find a container to show the alert (you might need to adjust this)
        const container = document.querySelector('.row.g-4') || document.body;
        const alertDiv = document.createElement('div');
        alertDiv.innerHTML = alertHtml;
        container.insertBefore(alertDiv.firstElementChild, container.firstChild);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 3000);
    }

    // Simple dashboard interactions
    document.addEventListener('DOMContentLoaded', function() {
        // Track user activity for potential auto-refresh
        ['mousemove', 'keypress', 'scroll', 'click'].forEach(event => {
            document.addEventListener(event, () => {
                localStorage.setItem('lastActivity', Date.now());
            });
        });
    });
</script>
@endsection