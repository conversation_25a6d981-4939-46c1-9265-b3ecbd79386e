@extends('layouts.admin')

@section('title', 'Test UI Components')
@section('page-title', 'Test UI Components')

@section('content')
<div class="row g-3">
    <!-- Stats Cards Test -->
    <div class="col-12">
        <h4 class="mb-3">Stats Cards</h4>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-primary">
                    <i class="fas fa-users"></i>
                </div>
                <h2 class="stats-number">1,234</h2>
                <p class="stats-label mb-0">Khách hàng</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    Tăng trưởng tốt
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-success">
                    <i class="fas fa-box"></i>
                </div>
                <h2 class="stats-number">56</h2>
                <p class="stats-label mb-0">Gói dịch vụ</p>
                <small class="text-info">
                    <i class="fas fa-chart-line me-1"></i>
                    Đa dạng sản phẩm
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h2 class="stats-number">12</h2>
                <p class="stats-label mb-0">Sắp hết hạn</p>
                <small class="text-warning">
                    <i class="fas fa-clock me-1"></i>
                    Cần chú ý
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card card-stats">
            <div class="card-body text-center">
                <div class="stats-icon mx-auto mb-3 bg-danger">
                    <i class="fas fa-times-circle"></i>
                </div>
                <h2 class="stats-number">3</h2>
                <p class="stats-label mb-0">Hết hạn</p>
                <small class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Cần xử lý
                </small>
            </div>
        </div>
    </div>

    <!-- Buttons Test -->
    <div class="col-12 mt-4">
        <h4 class="mb-3">Buttons</h4>
        <div class="card">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <h6>Primary Buttons</h6>
                        <div class="d-flex gap-2 flex-wrap">
                            <button class="btn btn-primary">Primary</button>
                            <button class="btn btn-primary btn-sm">Small</button>
                            <button class="btn btn-primary btn-lg">Large</button>
                            <button class="btn btn-outline-primary">Outline</button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Other Colors</h6>
                        <div class="d-flex gap-2 flex-wrap">
                            <button class="btn btn-success">Success</button>
                            <button class="btn btn-warning">Warning</button>
                            <button class="btn btn-danger">Danger</button>
                            <button class="btn btn-info">Info</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Test -->
    <div class="col-12 mt-4">
        <h4 class="mb-3">Table</h4>
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Tên</th>
                                <th>Email</th>
                                <th class="d-none d-md-table-cell">Ngày tạo</th>
                                <th>Trạng thái</th>
                                <th class="text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-initial bg-primary text-white me-2">N</div>
                                        <div>Nguyễn Văn A</div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td class="d-none d-md-table-cell">01/01/2024</td>
                                <td><span class="badge bg-success">Hoạt động</span></td>
                                <td class="text-center">
                                    <div class="btn-group">
                                        <button class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-initial bg-success text-white me-2">T</div>
                                        <div>Trần Thị B</div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td class="d-none d-md-table-cell">02/01/2024</td>
                                <td><span class="badge bg-warning">Tạm dừng</span></td>
                                <td class="text-center">
                                    <div class="btn-group">
                                        <button class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Forms Test -->
    <div class="col-12 mt-4">
        <h4 class="mb-3">Enhanced Forms</h4>
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Form với Real-time Validation & Auto-save</h6>
            </div>
            <div class="card-body">
                <form class="enhanced-form" data-auto-save="true" id="testForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Tên *</label>
                            <input type="text" id="name" name="name" class="form-control"
                                placeholder="Nhập tên" required minlength="2" data-capitalize="true">
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" id="email" name="email" class="form-control"
                                placeholder="Nhập email" required>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">Số điện thoại</label>
                            <input type="tel" id="phone" name="phone" class="form-control"
                                placeholder="Nhập số điện thoại">
                        </div>
                        <div class="col-md-6">
                            <label for="password" class="form-label">Mật khẩu *</label>
                            <input type="password" id="password" name="password" class="form-control"
                                placeholder="Nhập mật khẩu" required minlength="6">
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">Trạng thái</label>
                            <select id="status" name="status" class="form-select">
                                <option value="">Chọn trạng thái</option>
                                <option value="active">Hoạt động</option>
                                <option value="inactive">Tạm dừng</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="website" class="form-label">Website</label>
                            <input type="url" id="website" name="website" class="form-control"
                                placeholder="https://example.com"
                                pattern="https?://.+"
                                data-pattern-message="URL phải bắt đầu bằng http:// hoặc https://">
                        </div>
                        <div class="col-12">
                            <label for="description" class="form-label">Mô tả</label>
                            <textarea id="description" name="description" class="form-control"
                                rows="3" placeholder="Nhập mô tả" maxlength="500"></textarea>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    Tôi đồng ý với điều khoản sử dụng *
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Lưu thông tin
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2"
                                onclick="FormUtils.clearForm('#testForm')">
                                <i class="fas fa-times me-2"></i>
                                Xóa form
                            </button>
                            <button type="button" class="btn btn-outline-info ms-2"
                                onclick="populateTestData()">
                                <i class="fas fa-fill me-2"></i>
                                Điền dữ liệu mẫu
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Alerts Test -->
    <div class="col-12 mt-4">
        <h4 class="mb-3">Alerts</h4>
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            Thành công! Dữ liệu đã được lưu.
        </div>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Cảnh báo! Vui lòng kiểm tra lại thông tin.
        </div>
        <div class="alert alert-danger">
            <i class="fas fa-times-circle me-2"></i>
            Lỗi! Không thể thực hiện thao tác.
        </div>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Thông tin: Hệ thống sẽ bảo trì vào 2h sáng.
        </div>
    </div>

    <!-- Enhanced Table Test -->
    <div class="col-12 mt-4">
        <h4 class="mb-3">Enhanced Table Test</h4>

        <!-- Search và Actions -->
        <div class="row mb-3">
            <div class="col-md-4">
                <input type="text" data-table-search="testTable" class="form-control" placeholder="Tìm kiếm nhanh...">
            </div>
            <div class="col-md-4">
                <div data-bulk-actions="testTable" style="display: none;">
                    <button class="btn btn-outline-danger btn-sm" onclick="alert('Bulk delete selected items')">
                        <i class="fas fa-trash me-1"></i>
                        Xóa (<span data-selected-count>0</span>)
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-outline-secondary btn-sm" data-export-csv="testTable">
                    <i class="fas fa-file-csv me-1"></i>
                    Xuất CSV
                </button>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table id="testTable" class="table table-hover enhanced-table">
                        <thead>
                            <tr>
                                <th data-sort="id">ID</th>
                                <th data-sort="name">Tên</th>
                                <th data-sort="email">Email</th>
                                <th data-sort="status">Trạng thái</th>
                                <th data-sort="created">Ngày tạo</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>001</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-initial bg-primary text-white me-2">N</div>
                                        <div>Nguyễn Văn A</div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-success">Hoạt động</span></td>
                                <td>01/01/2024</td>
                                <td>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-info btn-sm"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-outline-warning btn-sm"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-outline-danger btn-sm"><i class="fas fa-trash"></i></button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>002</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-initial bg-success text-white me-2">T</div>
                                        <div>Trần Thị B</div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-warning">Tạm dừng</span></td>
                                <td>02/01/2024</td>
                                <td>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-info btn-sm"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-outline-warning btn-sm"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-outline-danger btn-sm"><i class="fas fa-trash"></i></button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>003</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-initial bg-info text-white me-2">L</div>
                                        <div>Lê Văn C</div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-danger">Hết hạn</span></td>
                                <td>03/01/2024</td>
                                <td>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-info btn-sm"><i class="fas fa-eye"></i></button>
                                        <button class="btn btn-outline-warning btn-sm"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-outline-danger btn-sm"><i class="fas fa-trash"></i></button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <small class="text-muted" data-table-count="testTable">Hiển thị 3 / 3 bản ghi</small>
            </div>
        </div>
    </div>

    <!-- Responsive Test Info -->
    <div class="col-12 mt-4">
        <h4 class="mb-3">Responsive Test</h4>
        <div class="card">
            <div class="card-body">
                <p>Để test responsive design:</p>
                <ul>
                    <li>Thay đổi kích thước cửa sổ trình duyệt</li>
                    <li>Sử dụng Developer Tools (F12) để test các breakpoints</li>
                    <li>Test trên mobile: 375px, 768px</li>
                    <li>Test trên tablet: 768px, 1024px</li>
                    <li>Test trên desktop: 1200px, 1920px</li>
                </ul>
                <div class="d-block d-sm-none">
                    <span class="badge bg-danger">XS - Extra Small (&lt;576px)</span>
                </div>
                <div class="d-none d-sm-block d-md-none">
                    <span class="badge bg-warning">SM - Small (≥576px)</span>
                </div>
                <div class="d-none d-md-block d-lg-none">
                    <span class="badge bg-info">MD - Medium (≥768px)</span>
                </div>
                <div class="d-none d-lg-block d-xl-none">
                    <span class="badge bg-success">LG - Large (≥992px)</span>
                </div>
                <div class="d-none d-xl-block">
                    <span class="badge bg-primary">XL - Extra Large (≥1200px)</span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// Test form functionality
function populateTestData() {
    FormUtils.populateForm('#testForm', {
        name: 'Nguyễn Văn Test',
        email: '<EMAIL>',
        phone: '0123456789',
        password: 'password123',
        status: 'active',
        website: 'https://example.com',
        description: 'Đây là dữ liệu mẫu để test form validation và auto-save.',
        terms: true
    });
}

// Demo form submission
document.addEventListener('DOMContentLoaded', function() {
    const testForm = document.getElementById('testForm');
    if (testForm) {
        testForm.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent actual submission for demo

            setTimeout(() => {
                alert('Form đã được submit thành công! (Demo mode)');
                // Re-enable submit button
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Lưu thông tin';
                }
            }, 2000);
        });
    }
});
</script>
@endsection
