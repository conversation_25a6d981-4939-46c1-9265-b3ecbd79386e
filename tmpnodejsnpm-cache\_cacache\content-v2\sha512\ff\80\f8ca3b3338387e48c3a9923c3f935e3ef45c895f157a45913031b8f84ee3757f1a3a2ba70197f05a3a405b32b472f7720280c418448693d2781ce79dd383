{"_id": "@rollup/rollup-linux-arm64-gnu", "_rev": "154-1fc2824f6c54a20b305de7373f8c299a", "name": "@rollup/rollup-linux-arm64-gnu", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.2"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-0", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "540a9f1325d219e0a8f5d351ca63b9dac0fec575", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-0.tgz", "fileCount": 2, "integrity": "sha512-b/lPeDQ/JN+cX7/HiPY9mLXhVOF0pDTStBqviUnwJgQLJrjHVLwnRoJNuo4RM+L2mNKEDdUtgvreb73agG5aZw==", "signatures": [{"sig": "MEYCIQCFF1qVg8y3jHvxxowZbEzZXSqF42ueFw+JB35epQNwpQIhAKaKP61fUotzC1eLJwPc0CJuAZUB+rO6HTmSaRwqEYu1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 762}, "libc": ["glibc"], "main": "native/rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Next-generation ES module bundler", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-0_1690831075792_0.9683605120499901", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f8a6cc4ae8419f303cf505770b5997bcda892f9b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-gk7Ela9OKERKJe15iBpOBCDDnqytZQPzQyhdBJuXu08kfFKXrQ7GEq0oZ2iczXZiP2wZqTN6eTJ0nVruZ2JJZg==", "signatures": [{"sig": "MEUCIQDgVHTGlUm1XhX1lC+sqZZYTeACk7WAkXacRzQJdEQKlAIgKP7L3xCCLPwFt4hHJSvyuOZPrka6pV98yA2mhA9cd1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2284627}, "libc": ["glibc"], "main": "rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-1_1690865340106_0.15326111834862255", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8a75bee63ae8f86ddaffdd9a756532a3dc017d72", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-MVwl/ng02aaPZOMLfIqjzmGd0ObxUNdUoKUTZeFv2xOY34JIL8uRSfd8PDGXycBm6UQTsj16CsAkEnvJMuRfRQ==", "signatures": [{"sig": "MEQCIFZgUzq7r3BVlbnaJ2P0Q+cesL1qNF/A8FxJByJgHsPqAiATC9G8PQ4pgiMGIvIKI7hdlxar/z6MX6AnDJhn+R8w6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2284627}, "libc": ["glibc"], "main": "rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-2_1690888593704_0.15671281793801417", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4a6ff2e4fff51cd2f403f579d9868bae718e2fb1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-K62mwY7WzuznFcctS2QY1oRUFzV6hiKs5Nah4h+m7yzulek0tPx4B9ggaFLafMaVbb12R+Yb+faFsxbnJYEyPA==", "signatures": [{"sig": "MEYCIQDqGM4CJEsapJJrUz2qJQmpby58+2xJXx4urrnZVt1OGQIhAK/QXo6kPyNhxaT2/23ZL7WVslXCzAaO3F5CXPMUaSCL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2276435}, "libc": ["glibc"], "main": "rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-3_1691137019335_0.7849652997053211", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "603e2844417b74348e5c687285e500a1c84dd134", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-/dlz3+6aq8gwg5iTr9tZs0d6p0WuqLZXIh3uT6P378Ai7NdiQWHNiEQvxwqfezSSm+6Cw79SIWu38V3GeH+zaQ==", "signatures": [{"sig": "MEQCIGu8e5tWrgltt9VoOjjfAF36mv9CZ4aLXPaJQyed5+pmAiA5t19q7Mh2+ctOXeHEziKt7Oo23DAi7m2rxORgR78W8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2276435}, "libc": ["glibc"], "main": "rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-4_1691148998030_0.8117456735481499", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "64a61762b08849d7eefc80824ae81baec881e7e8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-nZJd6WIXCYxby5lV+oseTUdgxqTdqI5XfH4EqnQV2Pl68Gr7NMDeFirPCqdJqCWwAcTnX6tuh+zUY1ArLsVxSA==", "signatures": [{"sig": "MEQCIFV8dGSAwrF93y2F0xeP1z3W8zgMn+UKiWPhbhqeTuRTAiBQIEKM/xDGwkYZr5/E/HLIcv5LkuKMp7LDOKhzpHESgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2653390}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm64-gnu.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-5_1692514610265_0.43092851286807954", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d4b5262db9a4e2ff5f3b3a3ddedbf6c99af97ac9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-93ChAYd/IpzQ2nZ0tIA6ClF70esTNTm6bS92K3JKrsqtvlImtnc79xbJ241Xn38YiKNi9Dnjm8bnbKIaAa3lPA==", "signatures": [{"sig": "MEUCIHaPfEf4OPCqTAtVqxUDjqrXmKg7BJbwb+xgiDD6QhKpAiEAlUCbhnlhAqpvJXbdTDy03lfPIxHwprDQ4ojvfT2SGic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2653390}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm64-gnu.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-6_1692517899173_0.8512721906666885", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3a9cc10be781c4a6eebb162a5e6ddbde5f0953a3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-f1YEBCLEh4KkX8ePFj5wBTdBlt7uLq2Ve2Zn4WQ27Pe5axIfP4Mfs/LkjlIAqbQzvS7mDrk3dSZ8jVpvmLf9KA==", "signatures": [{"sig": "MEQCIBklm/X+431ANqShIj+KGDF0/HXeSii71tR0f3eaz4fiAiB5WmZBfBuQyLhr9IFMkfe4Ki9IW+I9Muc6N+cyasl4jA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8108312}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm64-gnu.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-7_1692527608617_0.3655047387334016", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "0dd0e0eff8c6549063db6c01dab0d492062cc580", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-NKI8tBaGGE5BqJjZTUmn9iywFGLH6IxsZQhmFSH46cG/YKSXHQGBoY1nRdWxGJoTX/UJhlHxFsXCZXGMnU+iAg==", "signatures": [{"sig": "MEUCIQCAtlauLCmavtojl2n1gqB8EXAdTR5cCpsi/Yq5GeqTOgIgd7dTxSGQRAwZ6b+48HcWo4KSYrHQhilQ/1eN7bE2WI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8108312}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm64-gnu.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-8_1692530540071_0.3238727830367578", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "17b593333cde7bfe65a1833461cc2ad3e8ab2b88", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-P8COSGVMPyUjSJvde6RKJ4oe359baXSimfXAJJEaKtLeR+yngJRbGyuO3QiIkJlUPMXGYGk1afiSzO3pFcXu5w==", "signatures": [{"sig": "MEUCIFQYAAQR0bjyvFBIVUJEihr0NPBbtwM6pFv80PHG1IgoAiEA12ddfxvK3KWLrUVMJqdFruHmLE+ZoT3LbbfUgyHoXV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8108312}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm64-gnu.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-9_1692541746485_0.14163750854516266", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3c1e2262d75d1b363d87d6c16f63fc70214c2bf6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-/RTvsv9w+svUtvBdIVmxPU1awMms+b/v3eDAnCYV13jQxc2oDDDNLXugmVXucWscJoA5Q8oi/8KWFMSco4ZpkQ==", "signatures": [{"sig": "MEUCIF+zNjwvC4N7w8wrQC+afC6yIk76pedcf3tcZksqnDOGAiEAt4bYAp0qj2BKLrgvebA9btw9s43RyNIuDVYJSVxhwlo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8096756}, "libc": ["glibc"], "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.linux-arm64-gnu.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-10_1692631804473_0.18974770183251466", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2ed2978ed0d93319f36a3bc0754610eb948e7d56", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-adaAYcmDYpwDH1CCz1pqkWhUNL2iGD7N0HROHFLwfhoRfi0aqEDjkhp/Yj8thNwxXnKVKIcT1bfK+7nReQNEWQ==", "signatures": [{"sig": "MEUCIC673DUJmtW+pQtsglfBMzkePqSzorLm+Gn3sXga12q7AiEA4bxiaSC5GqFeibQExzM9R0LOVo2/b3f2+IO+XwJXtzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2653279}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-11_1692785755487_0.7056030493831782", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c3c7c1804a188513fb77589a17409ad6d2aa5e8c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-J+mjSVJab/gvucoeu4DPxxcLXQlOtiWX2T0I2B9fit5zdRKbvg+ul7JIPywcsfT/Wqbnx6LnL0CV9UGYDrRU2g==", "signatures": [{"sig": "MEUCIH0LHh+I75MUGwbFV5J5UNrDKvL2YD261gzRbEDPCg06AiEA/rxnS9PmOJbQHFlYtJeJhTw0/jhQ5FtuLCeV3vevzGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2653279}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-12_1692801630370_0.22330551164426327", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8e8bcc91a03d65852ef5aa383dc45b65782727df", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-UU+et3hhXrFXNoIUOftfrplkFStRL5wBZTwcGf3Ymas5AM3N0DxiADoz3RRsdEd79xIAeYneauHYeyzjm0kc0g==", "signatures": [{"sig": "MEUCID9Ps03ZfY5GOOS6Dai2gNk/CU3/ylOkXEudcFHhnO91AiEArlnpChSqqvVyX+LhcjZAYafaxM6gA8355VPyCZ9IfSs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2636895}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-13_1692892115104_0.9426249613225619", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "025ee57fe0c5d82fd2f4668a4176853dd317aea6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-RJRot4ShvhmghwubGG8l+A93EYSMNQpYSokn/mAc1ltVqsShu9qDWxQs/esqD7IcOQGF8LAhdVoLxAhEPnRIvA==", "signatures": [{"sig": "MEUCIQC22x2V45nyujNnak9sGzk4x6P5/Z4P3YkZTUhxXz6t2wIgc+CvNKghGNG+Mojm+Nq+DJy4RDtn7z8J1/pGwP/azp0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2628703}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-14_1694781267129_0.058118096348881876", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "04c29979d2f666e2450c436cdc9ad3e76cbb83a2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-hFdsjloeP4Rl2oznuRqu+xvKbKkyXzUKjMe/RpOQHk2koidQ63pLsdL/VyACxolahNhNOS6EGINrGgkt2MtKNw==", "signatures": [{"sig": "MEQCH3ZGfu81FKFFt5QxDj18rfZm4DQ7hdKHC0G2NJaD9B4CIQDty0sesyvSPRJnJZ9+RbpXcsE+jfBGakHnKT2EBDMieA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2628703}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-15_1694783214225_0.03140460292846359", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "08454d93784281a49af6ae135a62198151f6865d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-3BmdLZpxD2EECNjQLuNdIh2f3j7QQFJi1u0j195P83e8ORvPJrP6Jlm/pYE7xGqHVMeA881l3vjXvFZ8U7GxEA==", "signatures": [{"sig": "MEUCIEItTeHiBO4HCu7xoIIxLxPi3LG+/4dkiVoxpeVhbZQ/AiEAjxwJwjVOY7RVu4t6w4I5CqxDA+oXBniXmLx9WQqPmBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2628703}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-16_1694787438126_0.8829245406911355", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "0019cf0fcb60bfdb5582605149c16926064e91ff", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-t2iJflZEj3FrhuGhmyFxLX/OUnXuvUaNymMkqUYdXMjDHrf9UG3CgxnkO7R2I40KECHRxqBTD9XwArbRmKbsdw==", "signatures": [{"sig": "MEQCIB1IHW7jlmEFiOCaZcTG1/pV52n5hOaz59tY7zJiqFVcAiA3DnXISln7J7QQ242G4XH9HMpYB6Mw39dNv/xoYWP6Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2628703}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-17_1694789947836_0.4598192454125076", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "25d73847ba813f5a6cf808327bd91f3a476a1e7d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-KgyTZVLFzAoiJlpsq5mQV3Aq8CRpPtjBSeLNNYhNBvohPpkowxq2eXyCmBIS7y3z3YXFhQOR3coE3zsoY1RjTQ==", "signatures": [{"sig": "MEUCIEGv9bLm3/y0ZMqIxmd71IDE+bRNN6ASOZxOutULzNbmAiEA16K+q/Z2vy43qpY/tJOZ4JSTGqhdSBcQp7uQ6k2rPQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2628703}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-18_1694794210320_0.665520645787925", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b1b975891fdb20a1c0fdcb346af40625fbce4e00", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-+mXaSKwK/1xIn2a9RPhjpX/JsVstKpRcZMO1exJ2QS8Q95y1A/aLGmSFSGsvMKaC9f0KnzFX2XUJtY7WB9+49g==", "signatures": [{"sig": "MEYCIQCBHfdgHJsm/s1nNKgivGHO3gaJRoKnX/o+SBPh9trI+wIhAO8XeD6Ktv9euXNXpRjD+EL/mFVzTZQmM8xJeMJ4x1EZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2628703}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-19_1694803858690_0.9224299173531938", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "cf1d58927f3f1d6af1b0c0656497609cdf015d8a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-xMdNqLr7VuYL/ogSBYJ2dwGcINkMgiCRRr+dQRGBaEDiXvinlzNqrvEDHAezCWm+2rlxqcNxDjjZxBXcLG9a8Q==", "signatures": [{"sig": "MEUCID4cZRYk0XXTwRaQlaijgpIQj2PMDHECvP1L6/1DKVi/AiEA9pBenjZZx1HgtHgH5YlIl1dCc524WsMtK+Mixk0WiEg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2632661}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-20_1695535839267_0.8161053701533867", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "5481f7ced0cefb154980d8b17dc0079a92a712b2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-JhZBtbhbDICtMyDxNjo0mO1vsz3THsWXTDQEOd3/53uGiJguU5DmXccr4RipH7FBhCc6aRQ2Iun37CLNfNhLkA==", "signatures": [{"sig": "MEQCIEfAwwduuNyWvw4bGA9fQB/QAGgAgWC2p7bVueFcq+CoAiAvcG9fL5UEsBoeFLixF20scowhK23jvK7OFbeZiZxZgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2632661}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-21_1695576145742_0.6371824091503824", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "97ce5c557fdf6519b2b532d143a4a981a59ea18a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-kf465ldLv4mA8UvPB8VYfGBkraN2S/O+F/x2lZ0qilAwSNPAVVKzwatlFcWN5B0Z0N7jM1sC2xHA/KB9fF0cIw==", "signatures": [{"sig": "MEUCIGG4hkujhwO/KXt/PUOLa70+vxtyHWklvre0+sQk+J/iAiEA915meIlWME3PMnV69rRiltMxUkm+qDVkbkrCyh//qP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2636757}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-22_1695745043010_0.01134627040602898", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "665eecba2acf61fc55012a22eb4780479266da49", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-R4ffdK1hkjqqh8RSYOi06tztZPnbExh8ppNL1m5HgwaQhrmcUd3wgBJScaZXmetHUrunWfShxtbQCdZoTp8A2A==", "signatures": [{"sig": "MEYCIQDDOxzSbjUxBbmNp/fQw/4QnwuoJBD9QbXEJ/W0sOXMIgIhAPYkVYWinmyH5Ur+SwfWT5n8thcOxrRl38WacGGd1CfU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2636757}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-23_1695759264550_0.10244307335434821", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "eaf08a98ce24cdbbb9418a8d656b603dff6f8f97", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-BGnRktAZq4RI6FSicI+F6ws9paiYmjyaXUNKSukLthzgzPC91V4SXVylbFOCKvrhdWAr0lvZgcTrkgYNAmAcuQ==", "signatures": [{"sig": "MEUCIEXwSn38DHxASe3ShIw4FesyK+l7TCgcf0XCGSfkDTdmAiEA+tGSmrW7aVdl0aIIry0QmzhZj7gDuhtcjoIPgCE/wKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2653141}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-24_1696309967721_0.7819457618916261", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9738338d0289fa01dff7c6478021ea9e2a3b5a3c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-4xbCbi7ynDTILJjA0GWHOK4lsr6qKEj39BThnxGzcLZv1CYC1DYXn/TZeXiCd1OwkVaLkoLHcm4Tw68OX45Jkw==", "signatures": [{"sig": "MEQCIHDgi1As+TuXlPxJiqVWg/TNB0eczWi03IlTIbnvkFo/AiBGcJslUoWcuqGSv4+RU+vEa0uI7ztcffHtofY13wsbnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649045}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0-25_1696515166075_0.4131002182023831", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f7d56849e33d749751a5a6608dcc72d12654c2be", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-/BTevM/UKprMJgFse0nm+YXQ83iDqArru+k3kZtQlvaNMWdkLcyscOP8SwWPpR0CJuLlXr8Gtpps+EgH3TUqLA==", "signatures": [{"sig": "MEQCIHqEksKI6BtrCKO/hhmdf1LRtXntUAM/LiP7tArK88gFAiB55EBn1b2LGcElD5hLWV2bF4kIXTvYpJp5rtd3tOkyHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649042}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.0_1696518872559_0.4688793225777397", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f00825396011476d5d7b3eca22b16aa24798946b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-1AKiocQGspfLBrn71FDMpDOIjLJjQhvepwHV3/o9lO5XVSoN/hXH1OeRj6dSkcwozltUTRipQZJ6ku7mX0ZIkQ==", "signatures": [{"sig": "MEYCIQDlKfKo9Bx+G1u491kOoENLL+rtjaEkPSYQAA06vpTPzAIhAMnvHmxmfDHYfk3YHAgvNVQNbLay8xjL8HRNh4AmtDjT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2632658}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.1_1696595802135_0.36370145259783526", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "dfc5b1b3f1e991278ff40a63e24e84e28164b34f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-GCK/a9ItUxPI0V5hQEJjH4JtOJO90GF2Hja7TO+EZ8rmkGvEi8/ZDMhXmcuDpQT7/PWrTT9RvnG8snMd5SrhBQ==", "signatures": [{"sig": "MEYCIQCAiCTtgMjhKVNkrh5U6vdrQ1sr2Y6zd0qmz/82UvZUUgIhAKhaXeryenfUceVapTPsqQ7qo5ict5de4GX830667B5l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2632658}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.0.2_1696601922467_0.0918258293323182", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "5811294fc0bd2567fd14604c57215d481f462a1c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-snLIi8gp2VUntI/vXoiY0DnsmdP0dc/g4D3bHiiytPv9t8oWj5lCFtnHoZLvvreT2IsaZ1H8P+9D9lFBKr82xA==", "signatures": [{"sig": "MEUCIDSIMIyLvC+cTj4JygbnmDa9Ex9Jb4IVwJnINUKd0CQWAiEAjvtCtZKRNlT0i7DxCEOTcTSGc1z278/N7deKn2qMq6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2679706}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.1.0_1697262735424_0.2577189591223499", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e6dc7bdf09946b395cf4f95e800db8b2e3ae6e8c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-FLjAU1iM4/y8+W2Hktm9WH750CTbrohF7NWdQkUMrNwh5zC2kC/JKhsrNdUvpj1710P924UHXnIH2cEIJ4V3lA==", "signatures": [{"sig": "MEQCIH5+WVPIA38WmmxiqQbTIAaupJSHIk6ph5bDQdVyndyBAiBpDIz06ASMBlL8Hn1RuZJFT2X0QIzpQn/4mtD0BLLCbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2802986}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.1.1_1697351509119_0.489539029606169", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2b177d74ccf583b84a18e1dc81857144f9c93a3a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-XI7zAp5ADEph8iHXjk4silYARe/QO7zDGM71R7A6jnny4vlXCYBPB6V6rZm2kZ4VfckeZQukdGOlnvvW5fWLzA==", "signatures": [{"sig": "MEQCIELrvaefEzR6vBMmHD7hcQjkU7TmNJ+V5ueLk/FShi1yAiAaNhabVkY4dZrax5Q7KSdn8mp6XXL8F+cJhO3Mhq/S5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2802986}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.1.3_1697392108040_0.7838853988959313", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "5afa269b26467a7929c23816e3e2cf417b973d3b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-TzJ+N2EoTLWkaClV2CUhBlj6ljXofaYzF/R9HXqQ3JCMnCHQZmQnbnZllw7yTDp0OG5whP4gIPozR4QiX+00MQ==", "signatures": [{"sig": "MEUCIQDkq3LiuKJHQVpOriRNZQv8BSSd+Tz/vmJXFxLRm2JFnAIgKtjNnGOHHZcEebuDFWwD93qj2+CMhYk3W1h+nfdZXIw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2794794}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.1.4_1697430850809_0.7112725870132921", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a79023f1fa1ca7f0fc8bec343e87ee48ce67d159", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-Q2A/PEP/UTPTOBwgar3mmCaApahoezai/8e/7f4GCLV6XWCpnU4YwkQQtla7d7nUnc792Ps7g1G0WMovzIknrA==", "signatures": [{"sig": "MEUCIF96fMIcT2oRhLDlWvWPCayfTYi8Xon5aOsqu+W7dFANAiEAiPuFGYAW3h/9LPZUfKLwyZrb2A0EXYKy5/eytYDHrsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2790698}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.1.5_1698485013140_0.34622703007901157", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "188c61d3e665c62e4b58bd5a54c300781676bac5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-oiKV3k5RB3GpP4brVT9nuBat42MhyPFmhFAat4VI95GALV7hFHAS1DOhmittp0fpoFO3pFW6VqhSt9hougS/FQ==", "signatures": [{"sig": "MEQCIBV0vM4MJIWzY6DCCIfWn9zB1UsmfLylt6pT7Z+/9PZ1AiBYdfL0iknjlVpFLctMqyGvIJNCKU4B9H7PJu3MjZWkxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2790698}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.1.6_1698731115853_0.9722538840283492", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "811ec171fcf75dfcab59b1a04502c62c8410235e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-bNsTYQBgp4H7w6cT7FZhesxpcUPahsSIy4NgdZjH1ZwEoZHxi4XKglj+CsSEkhsKi+x6toVvMylhjRKhEMYfnA==", "signatures": [{"sig": "MEYCIQCi5lPHx0VTGp1ltDPWSvNC13b9XUmRJyPSH9fxmYCUKgIhAIkvktC704dtjVo35M0o8gDJv2YMSW+9xG9hxOnQU1KI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2807082}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.2.0_1698739841450_0.5315281497802169", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4f7ba42f779f06e93876755b7393c61676e2958a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-v/14JCYVkqRSJeQbxFx4oUkwVQQw6lFMN7bd4vuARBc3X2lmomkxBsc+BFiIDL/BK+CTx5AOh/k9XmqDnKWRVg==", "signatures": [{"sig": "MEUCIGp/SbQfHSSZpWx40Fmt6uHARGg4+516RlDQZNbLOdiTAiEA5jqoexExsWJcuKl78JEW/WReN/OQf2uLbOqd64tvnU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2811178}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.3.0_1699042383976_0.10616099607740992", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "5410e601c808df87cf3eeb24c2fcbd92100b03f3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-zjnPmrnXz59M6SaVwJSD0bWQ3ljFxpDMDVDi94Xn60/XX/qokZco9/psvu4hSvV+3A4OKwt4XwAULygXwN8y5w==", "signatures": [{"sig": "MEQCIAXfcdEZb50hibzdAFlmJUDysZh9hRyzsGVlaqU0NjYwAiB7XwRd2yIOHxo3vm84eGT1lrzMEGsuJ1zoVSmb+mIn8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2756170}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.3.1_1699689475600_0.8604049947352757", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "185b6a28d8e2307708b18eee72f1361595091e45", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-2hBHEtCjnBTeuLvDAlHRCqsuFQSyAhTQs9vbZEVBTV8ap35pDI1ukPbIVFFCWNvL/KE7xRor5YZFvfyGCfvLnA==", "signatures": [{"sig": "MEYCIQDboseCTM4XK4JbMp/TUt0c/SLO5ZgChzzwsP57J5Qy+wIhAMQMJ8xICjdqUw6trxYCX8V++DVY/+99TaDK5yejQUus", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2395714}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.4.0_1699775394956_0.6700898384144556", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "5d07588b40a04f5b6fbd9e0169c8dc32c1c2ed21", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-phLb1fN3rq2o1j1v+nKxXUTSJnAhzhU0hLrl7Qzb0fLpwkGMHDem+o6d+ZI8+/BlTXfMU4kVWGvy6g9k/B8L6Q==", "signatures": [{"sig": "MEUCIH0R6nMUB3KUvKgk7zgMR1baD8Jnm2ZXOAwNFwKYecpSAiEAiGy0ejbMY/MO5Cro4ews60pT+rBLUlqQrnSNSvfP9Lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2395714}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.4.1_1699939513521_0.9441696537367206", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "5434b844a47ba4e35602ee312de9f39b38b1777b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-OrEyIfpxSsMal44JpEVx9AEcGpdBQG1ZuWISAanaQTSMeStBW+oHWwOkoqR54bw3x8heP8gBOyoJiGg+fLY8qQ==", "signatures": [{"sig": "MEUCIEhTSwAafrYXLDhgVKtn4siDjJu30qs0Gevg9WuCEGaVAiEA0o2z+jSfwl8ky/wRed49ZI0WuphbnPptLxcD4R5Jy6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2403906}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.5.0_1700286733168_0.7629452636413208", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "bdb0c8552d167477d2624a4a6df0f71f128dc546", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-FEuAjzVIld5WVhu+M2OewLmjmbXWd3q7Zcx+Rwy4QObQCqfblriDMMS7p7+pwgjZoo9BLkP3wa9uglQXzsB9ww==", "signatures": [{"sig": "MEUCIQCaQzaAbtPsChrC8KUcdwpr6bXmCbLaOTKACseI6KwMRwIgOZJLpB1v2d6tBNfHTtnWg9bYSLSogDZmV6E6Zqbedmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2403906}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.5.1_1700597590466_0.9204609595077966", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a0dc66fa46553b8bb1c96df41ae8308770aebecf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-7Pk/5mO11JW/cH+a8lL/i0ZxmRGrbpYqN0VwO2DHhU+SJWWOH2zE1RAcPaj8KqiwC8DCDIJOSxjV9+9lLb6aeA==", "signatures": [{"sig": "MEUCIGroht4j4a7lQAiS8ih7UUxJRAVfbwJzCZAJY3AF529uAiEA3I04qlEJMBTBlXTd9FvOKxFSOr5kj5vbDFmwswXPrOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412098}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.5.2_1700807388422_0.1574547510862121", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6de1caa2c9952d16dafa21dd26da9562d4ea2112", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-Ed8uJI3kM11de9S0j67wAV07JUNhbAqIrDYhQBrQW42jGopgheyk/cdcshgGO4fW5Wjq97COCY/BHogdGvKVNQ==", "signatures": [{"sig": "MEUCIGovtOIZ4ZT9sJb4XyxzclgcvUud55pHO1VsSpkDHTa5AiEA1xYKDGNo98QcvqDB3oI4NInPuqUNj2pIvaQmW6JWGs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412098}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.6.0_1701005954720_0.5285163738536396", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f4cfbc71e3b6fdb395b28b1472414e181515c72d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-9lhc4UZstsegbNLhH0Zu6TqvDfmhGzuCWtcTFXY10VjLLUe4Mr0Ye2L3rrtHaDd/J5+tFMEuo5LTCSCMXWfUKw==", "signatures": [{"sig": "MEYCIQDepda5PvgM9xxIVA/8MKac1S690iySyxhKMxH2+Pzq0wIhAMC61Pz7sQEdYRdFA0FqptO15oiI8Cl4Q9VmseWTGBP1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412098}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.6.1_1701321788439_0.2683085248599115", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "0ef4ef25e83f610b56d94a5a1fffa27220d5224d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-RAdr3OJnUum6Vs83cQmKjxdTg31zJnLLTkjhcFt0auxM6jw00GD6IPFF42uasYPr/wGC6TRm7FsQiJyk0qIEfg==", "signatures": [{"sig": "MEQCIAU8hRuVa/inPOsQ8fp/3Dxx0byOLOgQc+6gy2g6mKgRAiA8Ii68/3O/0sxv1JuX5+13CfTsTdxmKZS9r4C1T/Behg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2395714}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.7.0_1702022281750_0.07815451707274956", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "95207444b78f235c9de62797ec2a3dcd18daf473", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-hBNCnqw3EVCkaPB0Oqd24bv8SklETptQWcJz06kb9OtiShn9jK1VuTgi7o4zPSt6rNGWQOTDEAccbk0OqJmS+g==", "signatures": [{"sig": "MEYCIQCseQcKYmysuea1W4rb+LpKiicwBRLP+y+x7yWYPuaJOQIhAJNnb5rhUmWfBoqpQ+OwEz0nx3xfmzKNQUQpSkJUtD+3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2395770}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.8.0_1702275894474_0.8729055588953993", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "345b276b814a5377344adc5780c4dfb7cd0e8ba9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-<PERSON>bEPfhndYeWHfOSeh4DOFvNXrj7ls9S/2omijVsao+LBPTPayT1uKcK3dHW3MwDJ7KO11t9m2cVTqXnTKpeaiw==", "signatures": [{"sig": "MEYCIQC7C89kY72gTpzV/+sF/xWzaYNuaI5CsYNG0v3mekZhVgIhANCf7fV/ePRl74+GvzztzNsQ4KM2RBj58PKCDTr7HkKM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2395770}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.9.0_1702459457742_0.6139598890635238", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "967ba8e6f68a5f21bd00cd97773dcdd6107e94ed", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-u3XkZVvxcvlAOlQJ3UsD1rFvLWqu4Ef/Ggl40WAVCuogf4S1nJPHh5RTgqYFpCOvuGJ7H5yGHabjFKEZGExk5Q==", "signatures": [{"sig": "MEUCID5DFJ2Ufe5wCw3AyjNfopnPtoKIOLrie87qNW28nb7rAiEApMKt1tHMqwLIpn97/q15HelKK6KppWIg97W696669b8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2416250}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.9.1_1702794371954_0.012514824548696568", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3a50f0e7ae6e444d11c61fce12783196454a4efb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-pL6QtV26W52aCWTG1IuFV3FMPL1m4wbsRG+qijIvgFO/VBsiXJjDPE/uiMdHBAO6YcpV4KvpKtd0v3WFbaxBtg==", "signatures": [{"sig": "MEYCIQD9KUPnPTiwB9e6kdq/FokE4IH3i42tZ8GZTBjJH0ViGQIhAPtsr9a/R2t+MyzHB55YO6L0K2RNmKkf4aWQyoEb/GQc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2416250}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.9.2_1703917408666_0.38757865827693494", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "434e4a4e9d859e3830e918a7c16e0218b9826a4b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-xANyq6lVg6KMO8UUs0LjA4q7di3tPpDbzLPgVEU2/F1ngIZ54eli8Zdt3uUUTMXVbgTCafIO+JPeGMhu097i3w==", "signatures": [{"sig": "MEUCIQCgMOoqi81EJkLP0mpf/1LsP3S/Lr0RQZ2nvNSPMpa94AIgYipMuRAljh3INsNmT/XXvmZ8v0hJ5+SkH2mUeDbDQVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2408058}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.9.3_1704435647762_0.14499323858544333", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "33b09bf462f1837afc1e02a1b352af6b510c78a6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-TVYVWD/SYwWzGGnbfTkrNpdE4HON46orgMNHCivlXmlsSGQOx/OHHYiQcMIOx38/GWgwr/po2LBn7wypkWw/Mg==", "signatures": [{"sig": "MEQCIAwm0VP6uGr2utjdHXaqxsEWbvVl+KQgjih5yxwp4maPAiA27vchd5thfwSjHnr2GxEz01M7Ok0wzLSQMyAC1obMug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2408058}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.9.4_1704523140989_0.32354295023815594", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c99d857e2372ece544b6f60b85058ad259f64114", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-dkRscpM+RrR2Ee3eOQmRWFjmV/payHEOrjyq1VZegRUa5OrZJ2MAxBNs05bZuY0YCtpqETDy1Ix4i/hRqX98cA==", "signatures": [{"sig": "MEUCIE8W5oG9ZR8iwCJqG9rlTpLREK4fmUBOdPHTRReODxEBAiEAssBYWN8GP33Xi7ZhpVnSGyJXcqAwH93CiFvGFdJsUKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412154}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.9.5_1705040174614_0.8033063220204666", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "19a3c0b6315c747ca9acf86e9b710cc2440f83c9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-Z3<PERSON>60yxPtuCYobrtzjo0wlmvDdx2qZfeAWTyfOjEDqd08kthDKexLpV97KfAeUXPosENKd8uyJMRDfFMxcYkDQ==", "signatures": [{"sig": "MEUCIQCtxLR/H+x0IT9Y0Pd3NChC7IJp3E4/U3FkWtnGtkr9RgIgBY/WEi4E58d96YoholYb8T6Z/LVyGKBaBl2uyWeMmZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2399866}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.9.6_1705816339932_0.5862160447541773", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e6dae70c53ace836973526c41803b877cffc6f7b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-yPtF9jIix88orwfTi0lJiqINnlWo6p93MtZEoaehZnmCzEmLL0eqjA3eGVeyQhMtxdV+Mlsgfwhh0+M/k1/V7Q==", "signatures": [{"sig": "MEQCIFpg2o1VLz7cJDL+e8hQXQCBOcJn0TiAnEGUWK6nal26AiBQELpURk1RZTT7fIzPbyun0ofZuav7cDMp1aeIfvJ03Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485883}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.10.0_1707544722367_0.4616425189192441", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ed398c7434734101e77a1fd0b05682b2c2027ecd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-673Lu9EJwxVB9NfYeA4AdNu0FOHz7g9t6N1DmT7bZPn1u6bTF+oZjj+fuxUcrfxWXE0r2jxl5QYMa9cUOj9NFg==", "signatures": [{"sig": "MEUCIQD5SncFted6yxr4xNfE/53vM5/Xbe5gb4bfqVpVuKnrtAIgXfCCgPQWyV5mliBJDUgVWCBMAyXSGBHuQB74SaSuO9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485883}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.11.0_1707977379428_0.0004875271634934286", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "788c2698a119dc229062d40da6ada8a090a73a68", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-0fZBq27b+D7Ar5CQMofVN8sggOVhEtzFUwOwPppQt0k+VR+7UHMZZY4y+64WJ06XOhBTKXtQB/Sv0NwQMXyNAA==", "signatures": [{"sig": "MEUCICZVhp+e+KMZNpzex5/hd+Wk4jINqOdLwY5ekXnOL1kdAiEA+ipduHdI+PJBEj0ZFRcBbS4eAlp/KbNc7Da9Xx0oG4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2473595}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.12.0_1708090337412_0.8136962724728842", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e41a271ae51f79ffee6fb2b5597cc81b4ef66ad9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-0bK9aG1kIg0Su7OcFTlexkVeNZ5IzEsnz1ept87a0TUgZ6HplSgkJAnFpEVRW7GRcikT4GlPV0pbtVedOaXHQQ==", "signatures": [{"sig": "MEUCIQD5JYyL+kw+QpQXkdgIlTl1CbCNp2/X070bYzEi7/P7VwIgEL/5GJMznsZ9YOpRxHeyJXTc8pRVjEt28g/Qm9cNlek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485883}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.12.1_1709705015276_0.6260556597199802", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "5fc8cc978ff396eaa136d7bfe05b5b9138064143", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-Iu0Kno1vrD7zHQDxOmvweqLkAzjxEVqNhUIXBsZ8hu8Oak7/5VTPrxOEZXYC1nmrBVJp0ZcL2E7lSuuOVaE3+w==", "signatures": [{"sig": "MEQCIDi1pyoR2LChYmTUQAbSEciQYftVYtJZMUBqc4z3bk28AiBf3XecVavtffYsrlzd7tYrjAvcm9xsWpbKo8YsblNyiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485883}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.13.0_1710221315693_0.7318484834859371", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6f17adb594d1a3b5b25522f2f81bbecc772c5217", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-/QwFIPOSUtr8KQEy1roE0Yx4+vxePNqYNRJ09uGBv84FS2zucu+v0+QfsfgQNK7vc0cYx5fEpR+kdUfjTvh7xw==", "signatures": [{"sig": "MEYCIQCwJmWqCwF56MaYA0BfAMd+gsIYATGLe0yG9qnJjeUavwIhAJ298gE3ro7IxckRh5tB/sebqZBF7RATgm8ZrMYiPdi0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469501}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.13.1-1_1711265962537_0.33439316835689614", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "13353f0ab65f4add0241f97f7ccc640b3a2b5cf2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-bur4JOxvYxfrAmocRJIW0SADs3QdEYK6TQ7dTNz6Z4/lySeu3Z1H/+tl0a4qDYv0bCdBpUYM0sYa/X+9ZqgfSQ==", "signatures": [{"sig": "MEUCIBsayK2U0osUguKnJDhv++7ye2RoYXP81xCg/ynyxID3AiEA4+Mclzr+1kRugeY7PJw2nSjNpWCx0VQQBHZxRzWHZ2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469499}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.13.1_1711535261381_0.4790422373710761", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "5a24aac882bff9abfda3f45f6f1db2166c342a4a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-L1+D8/wqGnKQIlh4Zre9i4R4b4noxzH5DDciyahX4oOz62CphY7WDWqJoQ66zNR4oScLNOqQJfNSIAe/6TPUmQ==", "signatures": [{"sig": "MEUCIB+bpWSXuNIqxVR5R4YI/5n9TnfIcZ/DgmJUm7MJu0A8AiEAxGt0WT/xIYWtsEwZjDlCPF6+oSnwkLPB11P5jl3ZrYc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2469499}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.13.2_1711635215963_0.35344411992683034", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e20fbe1bd4414c7119f9e0bba8ad17a6666c8365", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-x+uJ6MAYRlHGe9wi4HQjxpaKHPM3d3JjqqCkeC5gpnnI6OWovLdXTpfa8trjxPLnWKyBsSi5kne+146GAxFt4A==", "signatures": [{"sig": "MEUCIBCFqFxm5qPVHBYX2DA3XN/d7SkkXamkr6Ep5vHOeTMdAiEA6TgP6jnOrNXzW6XUy1atjFwyJjuC2B81AMJJu+ngVck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2494075}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.14.0_1712121773935_0.1706343967438495", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4da6830eca27e5f4ca15f9197e5660952ca185c6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-p9rGKYkHdFMzhckOTFubfxgyIO1vw//7IIjBBRVzyZebWlzRLeNhqxuSaZ7kCEKVkm/kuC9fVRW9HkC/zNRG2w==", "signatures": [{"sig": "MEYCIQCf5O7NWBJbMsHBOwZnxPE6T8m4TcxHaonJvDoIqDuHCAIhAPFwaDi3kqdMkuw8/NJQQKpaOiMiU3B2/P2bdW4rlQ4R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477691}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.14.1_1712475340164_0.35642978104648826", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "9901e2288fb192b74a2f8428c507d43cc2739ceb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-Pg5TxxO2IVlMj79+c/9G0LREC9SY3HM+pfAwX7zj5/cAuwrbfj2Wv9JbMHIdPCfQpYsI4g9mE+2Bw/3aeSs2rQ==", "signatures": [{"sig": "MEUCIHOEAgfwvrCVeXBwoaQxkqlztjaQRbzcylNRrW15Byi0AiEAt0haJk5lzp3nDTzV1An/a9YPk9qZX29gFoQL0qha7Tk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2473595}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.14.2_1712903020501_0.46325441075913676", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "426fce7b8b242ac5abd48a10a5020f5a468c6cb4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-Eci2us9VTHm1eSyn5/eEpaC7eP/mp5n46gTRB3Aar3BgSvDQGJZuicyq6TsH4HngNBgVqC5sDYxOzTExSU+NjA==", "signatures": [{"sig": "MEUCIHMSrWv62Ocqj7Pg4Fnb/lew259mJeEdwkzsJUr2jqWyAiEAkWfHPBlYWK8c3/SMZHK3GY3Y6OGa/klw4hkvVAt6x60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477691}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.14.3_1713165512470_0.13144763823582917", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "531d3792e1526583ecd794ceee0ab980d79813dd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-XzOsnD6lGDP+k+vGgTYAryVGu8N89qpjMN5BVFUj75dGVFP3FzIVAufJAraxirpDwEQZA7Gjs0Vo5p4UmnnjsA==", "signatures": [{"sig": "MEYCIQCgDFcOmktLgGmoj1y+B66RPht/15OJeiirWw7FB4ipZAIhAMtr/z7DPg4Vn/Abvhr9r93tyXIfo76fzzbes53zuaGv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2559611}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.15.0_1713591434424_0.8396196020707358", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "49f4369d436a73fe0b4d4bc4d59cd89c79ee0b4a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-iypHsz7YEfoyNL0iHbQ7B7pY6hpymvvMgFXXaMd5+WCtvJ9zqWPZKFmo78UeWzWNmTP9JtPiNIQt6efRxx/MNA==", "signatures": [{"sig": "MEUCIGJNV9WwYMo685pOCnjBcw6rtxLunZZUeXjaK4g0rBzfAiEA/WuuOo/7CKuxOZ1UBNXNFVyE/ZWv6OLZBkE1SZchYDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2559611}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.16.0_1713674514181_0.06928738581280935", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c8f2d523ac4bcff382601306989b27137d536dd6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-snma5NvV8y7IECQ5rq0sr0f3UUu+92NVmG/913JXJMcXo84h9ak9TA5UI9Cl2XRM9j3m37QwDBtEYnJzRkSmxA==", "signatures": [{"sig": "MEYCIQC/O+hrE4VGIMq4HH8Qu1hf5kee7gzRcj5G8Wgzb11blwIhAMDwR15jIuWiHvuqJ1xRzftbqUTNVAqdgs6CIgqyopVR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2559611}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.16.1_1713724199528_0.24720700721786182", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "128adb9dbf0057b989127d2e7fd73931a6729410", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-UN7VAXLyeyGbCQWiOtQN7BqmjTDw1ON2Oos4lfk0YR7yNhFEJWZiwGtvj9Ay4lsT/ueT04sh80Sg2MlWVVZ+Ug==", "signatures": [{"sig": "MEYCIQCTfmlDRC161lXU3G0uUfepYhNAifgnLW00q9C1NaUhNAIhAPLw7uPMpRixdd3NfMgqgfyGAB2laWdvf94Z3HM1X+Sv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2559611}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.16.2_1713799156885_0.9906717145040378", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "314bc75af7cf926456e778e98319b6b3e887c606", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-vY3fAg6JLDoNh781HHHMPvt8K6RWG3OmEj3xI9BOFSQTD5PNaGKvCB815MyGlDnFYUw7lH+WvvQqoBwLtRDR1A==", "signatures": [{"sig": "MEYCIQCfz/TK/cRzyK1JKPIHDUd5uiDZjduBvXZoptEHMDfbQgIhAMEbQqtq5zY9HdZrCK5hWTWw0UI3ch9sVenkYNIkYWV/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2559611}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.16.3_1713849155408_0.19838373625791061", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "0a23b02d2933e4c4872ad18d879890b6a4a295df", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-7dy1BzQkgYlUTapDTvK997cgi0Orh5Iu7JlZVBy1MBURk7/HSbHkzRnXZa19ozy+wwD8/SlpJnOOckuNZtJR9w==", "signatures": [{"sig": "MEUCIGz1mu5fUuHkpWFrILy+pELqLeb/mwp3qZhmhzD2OB4eAiEA/dvPVsQpNwACJ3tsh/EH6ErJikP/kuDi+RzlBLfVPos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2559611}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.16.4_1713878108177_0.4225543884853602", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a6c51266ec41df8431ccb3a5d9c09339264d86b8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-zL5rBFtJ+2EGnMRm2TqKjdjgFqlotSU+ZJEN37nV+fiD3I6Gy0dUh3jBWN0wSlcXVDEJYW7YBe+/2j0N9unb2w==", "signatures": [{"sig": "MEQCIFMKFnIYM+LT/zKe+im+BPujcq/x1u47QEnarbMaoj6EAiBcLA4R7F/IFHlbT8/u+QbMze/sdp4SxmGmhJN+ILKodw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2551419}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.17.0_1714217392755_0.5608390351695491", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "30d3e02585c7b1d2ea96b2834a79aeb1fb10237b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-e2uWaoxo/rtzA52OifrTSXTvJhAXb0XeRkz4CdHBK2KtxrFmuU/uNd544Ogkpu938BzEfvmWs8NZ8Axhw33FDw==", "signatures": [{"sig": "MEQCIFJ4Xa6pSTaF1C/Qt2EqoLMNBDg9hvAg0oZLROcXFRD+AiArWq123kUU8c23gqFLj0/rb4SfsVcelGypAYRkUJBIOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2551419}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.17.1_1714366675723_0.5188303356020849", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "fa531425dd21d058a630947527b4612d9d0b4a4a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-EMMPHkiCRtE8Wdk3Qhtciq6BndLtstqZIroHiiGzB3C5LDJmIZcSzVtLRbwuXuUft1Cnv+9fxuDtDxz3k3EW2A==", "signatures": [{"sig": "MEUCIQCbSiN/LAt0v1ayvqXDKU35pda8KTS8AdS5doXQ9htXagIgMmgsUhPMVm4VPXM8WnTBDskbcnY2AkRz1rTZ6BLicro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2551419}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.17.2_1714453246353_0.7990538868649899", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "99a7ba5e719d4f053761a698f7b52291cefba577", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-rJ5D47d8WD7J+7STKdCUAgmQk49xuFrRi9pZkWoRD1UeSMakbcepWXPF8ycChBoAqs1pb2wzvbY6Q33WmN2ftw==", "signatures": [{"sig": "MEYCIQDy8ux9ZV+K295VrhS/Vl+ujbgPJe24ykpcQ7NL5+2hcwIhAP0ZDMAlNulRWBaYCr0P5lb1DEnwRyPmD9KGFxsZXKxA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2551419}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.18.0_1716354225351_0.23353584973582864", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "19abd33695ec9d588b4a858d122631433084e4a3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-8mwmGD668m8WaGbthrEYZ9CBmPug2QPGWxhJxh/vCgBjro5o96gL04WLlg5BA233OCWLqERy4YUzX3bJGXaJgQ==", "signatures": [{"sig": "MEUCICE6cGI1TTJJfHy5Ik2MVoII8qQdZriq7lakPQKUYKl5AiEA6iE6Z3E1+jb6VFdp3IserY3gu0h2ZnvVsX2USR+iG48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2334363}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.18.1_1720452313282_0.4421847298111701", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d871e3f41de759a6db27fc99235b782ba47c15cc", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-0EkX2HYPkSADo9cfeGFoQ7R0/wTKb7q6DdwI4Yn/ULFE1wuRRCHybxpl2goQrx4c/yzK3I8OlgtBu4xvted0ug==", "signatures": [{"sig": "MEQCIBlrvnnDWxGrauFOFCRKsw5rZJu282ZpJ8JLC8f/iSq9AiAKb9iNXGASQBIRtw7dOB4PEAU+7O5PgGefz3g9HjHjyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2363035}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.19.0_1721454375974_0.7072578410623716", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1703d3a418d33f8f025acaf93f39ca1efcd5b645", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-C7evongnjyxdngSDRRSQv5GvyfISizgtk9RM+z2biV5kY6S/NF/wta7K+DanmktC5DkuaJQgoKGf7KUDmA7RUw==", "signatures": [{"sig": "MEUCIQDSyxpbR5dtHhU/PP5C697vFVa9QP/YeD6Ldu3ix+HXygIgGiMt3uTTb5IjNk0IqCF5dD3bHo8/ggeXkNr+oZc83ug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2358939}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.19.1_1722056042631_0.7769363798530464", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b7f104388b2f5624d9f8adfff10ba59af8ab8ed1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-OR5DcvZiYN75mXDNQQxlQPTv4D+uNCUsmSCSY2FolLf9W5I4DSoJyg7z9Ea3TjKfhPSGgMJiey1aWvlWuBzMtg==", "signatures": [{"sig": "MEUCIHBQL6IbMJPEINpy7b66PL4cyApjnJolBqH5550L56XMAiEA2Ypa0m8P12oUOIs8W8LZcMxor6W4rEl7r8eYTDxEfak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2285211}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.19.2_1722501175168_0.8251340202436468", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "65d1d5b6778848f55b7823958044bf3e8737e5b7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-COBb8Bkx56KldOYJfMf6wKeYJrtJ9vEgBRAOkfw6Ens0tnmzPqvlpjZiLgkhg6cA3DGzCmLmmd319pmHvKWWlQ==", "signatures": [{"sig": "MEYCIQCrsgbv2ClrgGOyXEz+unUvRZtlBd7I1i1rHH/stemYUgIhAITRjoIX/1xVBai+e5RLBv/0PZK3xlMYAb8Rdl8IExiS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2285211}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.20.0_1722660532363_0.518702600848767", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "22309c8bcba9a73114f69165c72bc94b2fbec085", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-ZrPhydkTVhyeGTW94WJ8pnl1uroqVHM3j3hjdquwAcWnmivjAwOYjTEAuEDeJvGX7xv3Z9GAvrBkEzCgHq9U1w==", "signatures": [{"sig": "MEUCIQDyFy2uNnC/0c/VTkoAbq5wwhhnyQRhr+VYitSOVMXuIAIgf3mjjQ8ozHs5ZYy9Zlls7wSsNm0T/LscO5LrDJ93k/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2227867}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.21.0_1723960536124_0.8793952444078537", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6dc60f0fe7bd49ed07a2d4d9eab15e671b3bd59d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-0kuAkRK4MeIUbzQYu63NrJmfoUVicajoRAL1bpwdYIYRcs57iyIV9NLcuyDyDXE2GiZCL4uhKSYAnyWpjZkWow==", "signatures": [{"sig": "MEUCIQC4Hv9FhJw/Jluq5JpOsDiBzNz3nBPLeejK0ngEMfG23gIgBNy/84eaYTdfVASegRL9U/h6ySO1rZ5ImKGhQ87/2B8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2223771}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.21.1_1724687655287_0.25241714099373525", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c2fe376e8b04eafb52a286668a8df7c761470ac7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-69CF19Kp3TdMopyteO/LJbWufOzqqXzkrv4L2sP8kfMaAQ6iwky7NoXTp7bD6/irKgknDKM0P9E/1l5XxVQAhw==", "signatures": [{"sig": "MEUCIHDKmmvGt70agy37yZs4HLmnX1uJ1MTi2sMHXUCCnndoAiEA/gBhkmd73RcbuJXBaVfzn+nnY/3SEf+G8sZ8S+004KU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2223771}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.21.2_1725001467737_0.4287925457520487", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ed96a05e99743dee4d23cc4913fc6e01a0089c88", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-nSZfcZtAnQPRZmUkUQwZq2OjQciR6tEoJaZVFvLHsj0MF6QhNMg0fQ6mUOsiCUpTqxTx0/O6gX0V/nYc7LrgPw==", "signatures": [{"sig": "MEQCIGmBrHnzwQQVJN+rUZ1O4Dezn6f7YJh7Eva8UL46nApjAiBV7LQfiSulR+QecrC9Fq7RuM28bRnuv6g6/cIUp4bNEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2227867}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.21.3_1726124751479_0.03819338774121994", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "cd626963b9962baf8e09d792e67b87269a5bcfff", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-U4G4u7f+QCqHlVg1Nlx+qapZy+QoG+NV6ux+upo/T7arNGwKvKP2kmGM4W5QTbdewWFgudQxi3kDNST9GT1/mg==", "signatures": [{"sig": "MEUCIBJAHuU90ceTrhGezkMzRHB8zlGfqGIFweSa982l2ugLAiEA951SkJAYIdI76me7sfLolzUn0nbFvXs3YitwYMkjCyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2227867}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.22.0_1726721733226_0.7941572837635245", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "8320489fc9f93524691ef19f2103f303e3cf895c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-GsvZqPloVOrh3G2nmZmwNSNGqWLf3L3a0nFDO1zecwucAYxEFgZkrvqQrVMT+zUjChaHPBp0eoTOQMWSKFcV8w==", "signatures": [{"sig": "MEQCIG3Jcacw+1py9THZGkd41VElf/akiqFJiJvXP3t+L6J9AiAeSrKUFmuokkEcHdtrS+Iie9PCblmmELuL0KvMjhecFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231963}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.22.1_1726820515275_0.32459963424599425", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "766064021d2bfc42f13f4653f8870a9b8bbdc31d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-Z1YpgBvFYhZIyBW5BoopwSg+t7yqEhs5HCei4JbsaXnhz/eZehT18DaXl957aaE9QK7TRGFryCAtStZywcQe1A==", "signatures": [{"sig": "MEUCIA2mhMtkeBXKn9RG5d2IoD89TL5+b05vFscO99mZohrzAiEA4Hx5X/pYgxfl7fkDGwOuyrzIUazhkklfeLFKQb7ZAmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231963}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.22.2_1726824826920_0.31253676792617946", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "570a451291d60813a27b70086209cbabe75b77ed", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-hCNviVTPPDvmJWUH6v+8xjxSiVs+IYcs5P4JTfkGBy5vCoXNoflyLzM3dbBdBXURSsqevA9D32xmQyt5BIgVqQ==", "signatures": [{"sig": "MEYCIQDEE7Y0KbrRGYS9KLG+S1P1uRiw9YUfXM7FbOv5e7e/8gIhANR2pod0P3q1i1/N57YmIpu25aehbMhCJXR0W8D3g25u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231965}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.22.3-0_1726843679615_0.9685773654302043", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "0573d2c771293448a7b105609ff1ca9c00315da5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-kVrJmt7kSQBptiuKccz+QVHTkRz7UhQuKhmXtCFbG8RukSfvbrFDfanYe5jaCUc0hDUE4T40sVPsyrwbnhlf+Q==", "signatures": [{"sig": "MEUCIGhpO2TiV7wv32/o1eSVXeGytjFTRctuBsL1bRFlo21TAiEAjli5s6dOVwv/VwMZWrvT6uYlSWA4p6C+leuR4ErNcVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231963}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.22.3_1726894990984_0.38783826072252836", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "7abe900120113e08a1f90afb84c7c28774054d15", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-AdPRoNi3NKVLolCN/Sp4F4N1d98c4SBnHMKoLuiG6RXgoZ4sllseuGioszumnPGmPM2O7qaAX/IJdeDU8f26Aw==", "signatures": [{"sig": "MEUCIH7z3CqUa5QY7rpmzY+PT+cGSpLpmdC7/mzqb+s7D3MsAiEA0CPxj1xvobusQuN0AMiQZXXqag70z+jEMOGMx0ldLcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231963}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.22.4_1726899083834_0.8763289529254323", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a7f146787d6041fecc4ecdf1aa72234661ca94a4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-oTXQeJHRbOnwRnRffb6bmqmUugz0glXaPyspp4gbQOPVApdpRrY/j7KP3lr7M8kTfQTyrBUzFjj5EuHAhqH4/w==", "signatures": [{"sig": "MEQCIDAGvkZr1GgvETtlJ1URyUFantwlAgALPTG7pDaoYbTnAiA97+4pDwp0KJWp/l4F3w/nexEWyKoKuC+R39kRwtP81g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2227867}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.22.5_1727437697920_0.4262983079382121", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1b685e1c219494e39f7441cd6b15fe4779ceda77", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-nAbWsDZ9UkU6xQiXEyXBNHAKbzSAi95H3gTStJq9UGiS1v+YVXwRHcQOQEF/3CHuhX5BVhShKoeOf6Q/1M+Zhg==", "signatures": [{"sig": "MEQCIGhihQvtNG1d6eqO23uOTtQ1ujAWd+FotFdSsiEZw3XaAiBA8uj/kr41lY0xhGK6tWckaXcGZA+/NyR6d0SUNiPpdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2191003}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.23.0_1727766608693_0.7803595357257698", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1632990f62a75c74f43e4b14ab3597d7ed416496", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-i0xTLXjqap2eRfulFVlSnM5dEbTVque/3Pi4g2y7cxrs7+a9De42z4XxKLYJ7+OhE3IgxvfQM7vQc43bwTgPwA==", "signatures": [{"sig": "MEYCIQC/WjAEIHXV28pZ73/TCKoOAbohs03Amur5fWwN/67FYAIhANYhkQ/5emn5++wa+kUKHxQtFnlFGNIrqCKUhAGbHiMj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2199195}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.24.0_1727861842478_0.06243759596879328", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "16ca754ca76b2766642c99c5f8bce2146fa55732", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-Rh12WITgvLydYFR9XAjmCRArU71nMfi5lDVLhpRV8dR2sCGtZESVkfD66mi3owp4q1scwysT35nNMPleRTQOow==", "signatures": [{"sig": "MEUCIFCjAJ8id2oiKjnR4Npam5enYxtConj7UR74PpNPRYWDAiEA5RA2XsIgII/5lLR79Mx990yAIZbe/i0qPD0+XciY73s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211483}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.24.1_1730011385376_0.3950098673265712", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6eb6851f594336bfa00f074f58a00a61e9751493", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-kr3gqzczJjSAncwOS6i7fpb4dlqcvLidqrX5hpGBIM1wtt0QEVtf4wFaAwVv8QygFU8iWUMYEoJZWuWxyua4GQ==", "signatures": [{"sig": "MEYCIQDOFry78etfCRaDCMA3qntDgkhjlUvOznKjoQ0/geCxnQIhAOkEjYEJWfdsQDc3Ug9fmpGlIbW6heL9D6MZJriRUqM8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211483}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.24.2_1730043613141_0.7563614610317877", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "fce63c301fce39869dd9a5a50815a0f237d4b746", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-2sqewcMoakoOWpowZjU8OE0PWE5J+RT+CxvoNdhTNPSWE+60Z/mZIN5q5NEToHgwk+1cuR2ZQbP3JLiNcSU8zg==", "signatures": [{"sig": "MEQCIC+8y4tlluDTaHDiHN8KcBgRHTrbbysQ90WCdEJXDwTwAiBykWLeEf0F+1ifjyUuMyGIY1YleNSsncMheNRJ93r6Bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211485}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.25.0-0_1730182515663_0.9618880180603704", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "89e5a4570ddd9eca908324a6de60bd64f904e3f0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-fKElSyXhXIJ9pqiYRqisfirIo2Z5pTTve5K438URf08fsypXrEkVmShkSfM8GJ1aUyvjakT+fn2W7Czlpd/0FQ==", "signatures": [{"sig": "MEYCIQCtZ7MJ02htwHvc7YZDP32CHnxNS1RZOXxYT6PBknZISAIhAL/WOAoCPPwjoVHZlzpiqfS5Hav/18GuIcksGLxn2TQH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211483}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.24.3_1730211252707_0.559004032582683", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "633f632397b3662108cfaa1abca2a80b85f51102", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-uHYJ0HNOI6pGEeZ/5mgm5arNVTI0nLlmrbdph+pGXpC9tFHFDQmDMOEqkmUObRfosJqpU8RliYoGz06qSdtcjg==", "signatures": [{"sig": "MEUCIQCKyJ0B2S1cfGlyVaz4v4lVzOFRUe+a0I8VAVUPfGAYJQIgaaT6Whz7F+7+HdQaiVibNGkMtpWFgdk83kPOUWJd0p0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219675}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.24.4_1730710034060_0.3529506485615408", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f98ec111a231d35e0c6d3404e3d80f67f9d5b9f8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-qboZ+T0gHAW2kkSDPHxu7quaFaaBlynODXpBVnPxUgvWYaE84xgCKAPEYE+fSMd3Zv5PyFZR+L0tCdYCMAtG0A==", "signatures": [{"sig": "MEUCIB7ofWxntE+Cegq06/Xv15urpRSfG1Ml2Yiibk6Hz+clAiEAoEoH7Ivf1JOSNgtkfzU6zenAC5+/dHn+dj0RCApGvMU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211483}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.25.0_1731141447022_0.23874169863736383", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2b06e147ca68c7729ca38e5c7a514d1b00f4d151", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-4daeEUQutGRCW/9zEo8JtdAgtJ1q2g5oHaoQaZbMSKaIWKDQwQ3Yx0/3jJNmpzrsScIPtx/V+1AfibLisb3AMQ==", "signatures": [{"sig": "MEYCIQDDvce6cqIHHparEIME+lXUXxednK5JeZViX1jQvFs3ZQIhAL5eXRrP4qnJI299Pg/eoybimyW8Qo5bGww1N3hjhu6J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211483}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.26.0_1731480304565_0.6504839045057076", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e673ecbafeb53b11c3268027fd550ac64a507f1d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-6YwBUTaHiTujcFTkWXx4t+Mi1GKcTdAUUom2YlJVhbEDI8/bdWU+6NpRC347J86GowvUKdEF05SerRACKJinzQ==", "signatures": [{"sig": "MEYCIQDdrtb8D53Hl5l7wVRFZmzlVqUmmbZ9tBTy52pDnU0JBQIhAIzie/T1a1/l3VW7O9bOV+cXiNGz2OZ7HAvMugmgZmeU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211485}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.27.0-0_1731481398589_0.6740562344819436", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "5bf997085d8321d1ccb5c124e6dd23659b3bd2b7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-6I92sVn8DEwvYOf81dJr2+Cb++JoWvnJHZ2mqwhQ64ij4ns6IlU60tRaghwtz/ch3xmkO5ER8wvPcd1840MLaQ==", "signatures": [{"sig": "MEUCIQCZTrQS0MtBzC/+qTnruG9wG8omPCygqmTkuIHytinJewIgUtkYOYlRP1+19R196/43vHXrOyzFCMVV+zpA6GsVv0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211485}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.27.0-1_1731565996286_0.4687208965307843", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "770f7ea89e04139f05d28d8477c646da6d5f3536", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-4zCKY5E9djPyHzvoCWIouFsuAvg+dk+rNia8lz1bjKpzKz02QvK4JPHyjcDT8CFR2J/aA98WccCirdDOy+VDWQ==", "signatures": [{"sig": "MEUCIQCOdh5soDgZEWO2VOYlqtugZdosbw7wLcvEVVryUMPqlAIgAbrwqfmw6m0Zqgob0AC/qynIePAGme8cZwcq+0HLvZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219675}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.27.0_1731667240536_0.8460769804538186", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "45693916ee8ea54fb85b43263e9cb3336775f4c6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-A7HRs6bKQpEhoqON4/kQBjXOyurP4k1vpN190ckfxZum9np9cU2tpAEa8EfyUaOwI+3VjCi5jGbCkCuHj3gHCA==", "signatures": [{"sig": "MEUCIA80k+NfBoygdxkckXte4Tacj107l5zL/9RRBXz1rOB1AiEAq4GNWpD1LRueNavD8uIsFu/3sR2sgV4QmJ6MhSkTLvU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219677}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.27.1-0_1731677293227_0.34018205507875554", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b05bd49159e3dc9e6814d67c95e01bfb13983555", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-ALCPEbe+JYqKjMIkjxOheVd9bcMeisr6akCeK+Crd400hf63GVsIx08u9TWIltc62+LAy9+xgrGHGqBq01iCtg==", "signatures": [{"sig": "MEUCICgGbeTRCcPCyivGfVK/iQUYaxHQmNJPo916bMh0/ITbAiEA9WztLWsLRMjINHg6NMXLpe1kj/sQsz5xk5BNI8A+t54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219677}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.27.1-1_1731685088584_0.40954752352993307", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "10cda199543503a5d790ca3415a3f8e369f3c595", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-p9huy9uW0FCtjqAHRrHcBVU63xtbfBwcvjV4N4a0cy69sdvTsHLlg/pb3WqSz4eTFxnpfZ6SMTpilew53+DQ1Q==", "signatures": [{"sig": "MEUCIQDaRSRdEzS0nBVYjjK8DOQaggCuPkqlgGD/5co9Us4bJAIgRHrsl8/FVSzF3hQwSCw+wvK6oJIurZxQ7QCCYk1fIQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219675}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.27.1_1731686866628_0.9452915979468723", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "560ecf7f12dbb33a93fe19d9791211283fe4b0e0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-uCFX9gtZJoQl2xDTpRdseYuNqyKkuMDtH6zSrBTA28yTfKyjN9hQ2B04N5ynR8ILCoSDOrG/Eg+J2TtJ1e/CSA==", "signatures": [{"sig": "MEUCIQDtMUO+Rt7VSXZHCvsdCXRjL0CyAFYEVJ8GJPx3DMNQQAIgXpEAGFCm79f7RWnEz7+STzjOIp3EsWVnMBELn9N39bs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219675}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.27.2_1731691208899_0.11735444352190894", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2046553e91d8ca73359a2a3bb471826fbbdcc9a3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-FPoJBLsPW2bDNWjSrwNuTPUt30VnfM8GPGRoLCYKZpPx0xiIEdFip3dH6CqgoT0RnoGXptaNziM0WlKgBc+OWQ==", "signatures": [{"sig": "MEUCIQDQz/lGk6ayEMyl1x8EpHsq+u8OaasXPeSZ0zmYYKSkCAIgVVRvaed+eYGxkhI4wnGWmgRAXwFTE+DDJOPbJJpTzcI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219675}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.27.3_1731947981027_0.8955487827913122", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c65d559dcb0d3dabea500cf7b8215959ae6cccf8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-pleyNgyd1kkBkw2kOqlBx+0atfIIkkExOTiifoODo6qKDSpnc6WzUY5RhHdmTdIJXBdSnh6JknnYTtmQyobrVg==", "signatures": [{"sig": "MEYCIQD3j2vVFYdOJsG01fBuym1vOLn4EVnMyJGLoVbgK+OS+AIhAILMrpx/Hqrko5KHE2/SKik8ZE7UwkS6PBl2ZoaryDxP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219675}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.27.4_1732345225914_0.881050165318289", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "93ce2addc337b5cfa52b84f8e730d2e36eb4339b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-+P9G9hjEpHucHRXqesY+3X9hD2wh0iNnJXX/QhS/J5vTdG6VhNYMxJ2rJkQOxRUd17u5mbMLHM7yWGZdAASfcg==", "signatures": [{"sig": "MEUCIB95nF90wLkwUT4KX4VY5q7HfQYsz2HmEwbwQsv1lt80AiEAhG0IDeEMk3maGQYmdEILVrAA5m7Yc/2oC5k+w+8m2xw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2223771}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.28.0_1732972553154_0.11543429879120559", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "6b1719b76088da5ac1ae1feccf48c5926b9e3db9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-uGr8khxO+CKT4XU8ZUH1TTEUtlktK6Kgtv0+6bIFSeiSlnGJHG1tSFSjm41uQ9sAO/5ULx9mWOz70jYLyv1QkA==", "signatures": [{"sig": "MEYCIQCELMrapBpoUgIJj1BKyjASgc5siltxskhhMNCGNPCcSgIhAJ2SLCudgySMuCgSpPsl6brWJRXhkkS3N/ke5Q241/aT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2207395}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.28.1_1733485501271_0.002574602870025755", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "cf050112f6ac46234ac49431e20774d9ec6ad1ef", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-fjK2aaqaJJJLyV1B1fMI/30h+111RVAJM2ypf9aRuVMDXnC6v/ZKxpeXGTEnkJ3xyNDQh2G/zjQsOtgt/OJs9A==", "signatures": [{"sig": "MEQCIFQ4LkPazSJRfRWTvrW0Ze1vylu0AgJV/k9V4+j8cJf8AiB9sBcYm3DdnwulytGvCvoDxkeHNDs2BLprPBLv1j5r1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211493}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.29.0-0_1734331199030_0.8509451285142442", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "eb3123f99b38c5309b078765bb67187e7cf12ea2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-dkiVfmFLRezgdugzMQhzxpcGEd+G4sKqN6TyIwBv29kZOCtWd6qA57ur8HbBgdatqKxMyNedhv0YPsg3G0BWUQ==", "signatures": [{"sig": "MEUCIQDeubnFAnBHgpwiDrCTGAbAZnikWtWLq5vDEiAXwrURUAIgH46/rp7qJasZRocU9qkD4fX/ISvez6uhpbc+Ms23dAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211493}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.29.0-1_1734590256242_0.850815156399843", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "be616bc066ce4253a8ba9f31178a7759069592ae", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-FFHFFVPrUkLyw+aW/XtMBHKRsijgARgnvKnx0f34MBuyHH2e7F23XqJoDvx+IZDn23BnKkz1O4Zf1i7v4hjgNw==", "signatures": [{"sig": "MEQCIG5FH0yAl6rhjigSx1zs/e0XZ7MNSwhrbku7PypQ35uPAiBeDUxaSpAKHCK4mbanLsV5jgYhU6uIvl27dOuepNU1gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2211493}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.29.0-2_1734677768368_0.16083539302830485", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "bd9c6750fc62c9a27bfd1b0ed443f46330a086a6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-Pnnn/2CAZWcH9GQoj1nnr85Ejh7aNDe5MsEV0xhuFNUPF0SdnutJ7b2muOI5Kx12T0/i2ol5B/tlhMviZQDL3g==", "signatures": [{"sig": "MEUCIQDXLyLsQ43Uf0L6o3hZnCJ4VSXH89suefOgLllurjqcOQIgWwNk7mH3ILP+dat3WAIZNILqTi5qNeNIQQOpWzuH5VY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2215579}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.29.0_1734719849967_0.08497460635669296", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b5e9d5e30ff36a19bedd29c715ba18a1889ff269", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-Z80<PERSON>+taYxTQITWMjm/YqNoe9d10OX6kDh8X5/rFCMuPqsKsSyDilvfg+vd3iXIqtfmp+cnfL1UrYirkaF8SBZA==", "signatures": [{"sig": "MEQCIGQgBbGW4ageZHhdquWq7NfLRVrbXmDg2MzHeq2uTRNvAiAA7cuMK0v/O1BE+tmP8IQ1SLvBEI+VOmqKFzMhTHP9kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2215579}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.29.1_1734765368148_0.8999225504238464", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2741f797222a168b1b403391720e4bdb8233498d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-caTZaZK31+EhzTaiYAHMBibvdjSj9GMDvEcEm5rZH/9nyEieqtvqQKwGiyBpTyMLYoqpw0IgApwpXHT39oRQDw==", "signatures": [{"sig": "MEUCIQCwZ9SmYlrSXVILitIVKzryybLsjFV8DjYZIdF7o+atXgIgZ7u1sYhwsTjFItj47pmCSaxwqKrETlgjM5T+4klgIuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2215581}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.30.0-0_1734765439915_0.6897069905420894", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b755290187547ddf1bdcd0d6e41c37367d54d0ad", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-P1befXMCAW8J2yXsw6PZCkeyP7vBsdSU6FAzVjJogtytEhS3369x5+7FMHe4Lq/QzLcuigZU0QPR+HjqHl87oA==", "signatures": [{"sig": "MEUCIQCcR5nnqcJG0D9w51W8+1jDF2CmuTSGOXIHsmZVRAs2GAIgWy4EonBpK+JHjOAiZ/uVF+fnTsmzT4GpHW7SWeifEx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219677}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.30.0-1_1735541542154_0.8042958611734059", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "60d457954c288c168049aadb304c204d8a680236", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-Z7zXVHEXg1elbbYiP/29pPwlJtLeXzjrj4241/kCcECds8Zg9fDfURWbZHRIKrEriAPS8wnVtdl4ZJBvZr325w==", "signatures": [{"sig": "MEYCIQCWatMvTRmgi54y5Z9qQ2sdox6GfK632CELoffznRnPpQIhAKjuMFpcB66hVphStQhTCrhcK80GcGLiB8d8Rb5385u3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219675}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.29.2_1736078867946_0.22106827858422284", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2c4bd90f77fcf769502743ec38f184c00a087e08", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-CFE7zDNrokaotXu+shwIrmWrFxllg79vciH4E/zeK7NitVuWEaXRzS0mFfFvyhZfn8WfVOG/1E9u8/DFEgK7WQ==", "signatures": [{"sig": "MEYCIQDs6b5rwkbBKB5QW4EdzFGLJ+um+ioV9BOq/tvX6cyvVAIhANcVReTZPxgPPakmN5j2D5dMCxV3xmd66UyTOw/4I6aV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219675}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.30.0_1736145406025_0.9284305291664006", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2550cf9bb4d47d917fd1ab4af756d7bbc3ee1528", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-hqVyueGxAj3cBKrAI4aFHLV+h0Lv5VgWZs9CUGqr1z0fZtlADVV1YPOij6AhcK5An33EXaxnDLmJdQikcn5NEw==", "signatures": [{"sig": "MEYCIQDFHPaDOW8o0GhiarK3ayoLySj1x93Wca507bUHlNyMnQIhANR3BrNaYsoyVXDBvsLL5sW6UKT6DtycYWNqbb7GOqvx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219675}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.30.1_1736246158011_0.19899099206008564", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "72d3bdc72e0d2847dd2ac01cb0a956cca13760fb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-+U5+IBKCVgjcwF3Xr23rAL2w0w6ktrvFqOh07FWXEtouhtRu99d2herQeXCNCXrfQ8sGqmWaOJUF5gv+1GsFgQ==", "signatures": [{"sig": "MEUCIF5PGIYCtECmNCD7QNpJujtGmEAmqlECNAv8hMpjucxOAiEA3ytBKqxKuGJloURdpGEG1Kr4XTZBshoW2h+MerTnOlM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2219677}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.31.0-0_1736834267671_0.2131797613719848", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f38bf843f1dc3d5de680caf31084008846e3efae", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-JyFFshbN5xwy6fulZ8B/8qOqENRmDdEkcIMF0Zz+RsfamEW+Zabl5jAb0IozP/8UKnJ7g2FtZZPEUIAlUSX8cA==", "signatures": [{"sig": "MEQCIFeXTL5mLcxB4jPcHSkFk5DFsspguhS+XiBmTgk9RNILAiB/ZRb4RkNfMtUSZUkNOgrm+c0QoA0vip+qTkqQL2cmQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2227867}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.31.0_1737291412686_0.6774305709800144", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ec1b1901b82d57a20184adb61c725dd8991a0bf0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-r7/OTF5MqeBrZo5omPXcTnjvv1GsrdH8a8RerARvDFiDwFpDVDnJyByYM/nX+mvks8XXsgPUxkwe/ltaX2VH7w==", "signatures": [{"sig": "MEYCIQCtzaY1AIuE0neYzVEomIWl3VC6m/GUUb3msWCOV/lPLgIhAOUzpBr+uyyMszXt8670DiqohrWrq6e7jQWCasG836Xu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2227867}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.32.0_1737707260172_0.10309902808096005", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "80806cc08abb30f3743006e6714750df0ce9a251", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-fGZaLe87k2cPuVs3p95MY624JqD/htfNnqj3NCZE8rY5dbwN+Kj3Xkk6N0st94kYTZ1Y+X1XZu2DYz4ZOuAyEw==", "signatures": [{"sig": "MEUCIQCMvjFqtbczg0Cv+tD8GWk+JEZXv1C+KnY00f/xkKrViwIgbYejNKqf2uWKMxXYA1hoIGK6nG5tP72GELPiD3ysrBw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2227869}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.33.0-0_1738053014201_0.6358451920864423", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ce6a5eacbd5fd4bdf7bf27bd818980230bdb9fab", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-tZWc9iEt5fGJ1CL2LRPw8OttkCBDs+D8D3oEM8mH8S1ICZCtFJhD7DZ3XMGM8kpqHvhGUTvNUYVDnmkj4BDXnw==", "signatures": [{"sig": "MEYCIQC/hBezkvxmvdUPtWq2D1gL60OtqPUjOT4EaIU0DwISAQIhAMMXkehDki/OoXggCaUOII/w9HO6kBT2XF5LAnctGWOV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2227867}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.32.1_1738053202442_0.2100560694901099", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "27fdb2d2f8fc905f0e128164d1fa4069e05df49d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-WSyz2HpBxS/EQpZ21/fruXcQSVRYU7kcfOVbZsLuIbberf1Lw8f1y0IoWu4GVv8PK+V6CdjSIsZiLP4/tDKAJA==", "signatures": [{"sig": "MEUCIHj6ycGehzScduc13LsfaDBKEDirIRcP6xpx3F/qs9gcAiEA4HWHyt8MdvGocWb9A51U7fhF22sztHe53A+StJVASBI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2213515}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.33.0_1738393925630_0.18472305713059556", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ab5720b3d77810a65cdc5d940fa5e1ed4948d62d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-e7kDUGVP+xw05pV65ZKb0zulRploU3gTu6qH1qL58PrULDGxULIS0OSDQJLH7WiFnpd3ZKUU4VM3u/Z7Zw+e7Q==", "signatures": [{"sig": "MEUCIDhY/9xclWzbU9X7QZpTP0OOd90oWeeGV+4Bl3L3u4YMAiEAiLhZjYf9kcwrOQKMgIA7Km0O22eEjFUdXdR9yteF6Ek=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2213515}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.34.0_1738399228934_0.7196102064075673", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "4de1e8ade39d0da1e981ab05f94a69f3de72725a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-Wy+cUmFuvziNL9qWRRzboNprqSQ/n38orbjRvd6byYWridp5TJ3CD+0+HUsbcWVSNz9bxkDUkyASGP0zS7GAvg==", "signatures": [{"sig": "MEUCIFnIcJP5Jp8fVRJ9ViakLfBa1yylPTUCQYzEhEffDiluAiEAjnWdMQNnScjI3WY732OKQaiOB7gco2ejIkYXJSeG+lA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2213515}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.34.1_1738565898730_0.08573932731531775", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1b96e68b1569484158b91ef48738aa87cbd3a1d7", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-ZvkPiheyXtXlFqHpsdgscx+tZ7hoR59vOettvArinEspq5fxSDSgfF+L5wqqJ9R4t+n53nyn0sKxeXlik7AY9Q==", "signatures": [{"sig": "MEQCIGuKOiMdZ7tcJavs3SqIdy9RoVUhsCMbYNJjGxkMOrvPAiB1YVDyivseQb+rSiHBBMOjTyJNOIfdPz2CF3iGBb4UQw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2213515}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.34.2_1738656607609_0.2992421043633777", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "df198a61a48db932426eee593f3699aa289e90f5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-R3JLYt8YoRwKI5shJsovLpcR6pwIMui/MGG/MmxZ1DYI3iRSKI4qcYrvYgDf4Ss2oCR3RL3F3dYK7uAGQgMIuQ==", "signatures": [{"sig": "MEUCICyKbJ8ghQjpBaOr2SeWBNir38uhezhlDWQbvkCbTyppAiEApUAImT1f55Zq3uxAWqtdm3DdANa+1CQ2hCUSnhilPPk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2213515}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.34.3_1738747330497_0.8506305362513515", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "0ad274a83f8366b3c434b4ee4c109e3e89942f72", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-7CwSJW+sEhM9sESEk+pEREF2JL0BmyCro8UyTq0Kyh0nu1v0QPNY3yfLPFKChzVoUmaKj8zbdgBxUhBRR+xGxg==", "signatures": [{"sig": "MEQCIDRvq1WLXxI809oLrPVhvGQcSFwgQ0Rb8AJs8VlcjQBAAiADIbVh7n5zKYyAllevVpo8paAegCr9MCFjDmNN+d07HA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2213515}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.34.4_1738791076571_0.8991974212513949", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "2c6ca31de0ff8d764162ab4ffec37ffa1e8bf0c5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-KHlrd+YqmS7rriW+LBb1kQNYmd5w1sAIG3z7HEpnQOrg/skeYYv9DAcclGL9gpFdpnzmiAEkzsTT74kZWUtChQ==", "signatures": [{"sig": "MEUCIHBSviUo7q6ApdCJLBv+M8dFAZtDSSQV3BZ19YCG9mW+AiEA93moBO/ALUVzO54CexBj09tXmgRiyEaYcy6snx9FVw8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2217611}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.34.5_1738918387479_0.4845997197770118", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ebb499cf1720115256d0c9ae7598c90cc2251bc5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-9RyprECbRa9zEjXLtvvshhw4CMrRa3K+0wcp3KME0zmBe1ILmvcVHnypZ/aIDXpRyfhSYSuN4EPdCCj5Du8FIA==", "signatures": [{"sig": "MEUCIQChY7zcMSmBVk5tMb5LD1Zo0Xo8md0ZPMYZUVq2NPEejAIgU9nAK+bqNBPm2vMkRKEW/iCGFwU9ZbleICKz8LNtX/I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2197115}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.34.6_1738945932765_0.8861829142873565", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "32d3d19dedde54e91574a098f22ea43a09cf63dd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-KCjlUkcKs6PjOcxolqrXglBDcfCuUCTVlX5BgzgoJHw+1rWH1MCkETLkLe5iLLS9dP5gKC7mp3y6x8c1oGBUtA==", "signatures": [{"sig": "MEQCID9OhUHGXPQq4WWQPUkRHrQYwwi4kAI1Bzi3vHM3O1VkAiBNZPIicQ+5ek0lye/YYPtY6JZ7GKfvRZb1t8v6GE8cXQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2205307}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.34.7_1739526844862_0.20170325212725948", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "fac700fa5c38bc13a0d5d34463133093da4c92a0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-jpz9YOuPiSkL4G4pqKrus0pn9aYwpImGkosRKwNi+sJSkz+WU3anZe6hi73StLOQdfXYXC7hUfsQlTnjMd3s1A==", "signatures": [{"sig": "MEQCIGEVpPh3DzIzAZYzv7jVg5QyQE3i+pNmhEkgPArt5tIAAiBHTaMa0ucSieydJCKhYTTk33Rd8kyMQ8EQQtwrDZiqaw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2205307}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.34.8_1739773590696_0.8604856367408009", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1015c9d07a99005025d13b8622b7600029d0b52f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-6TZjPHjKZUQKmVKMUowF3ewHxctrRR09eYyvT5eFv8w/fXarEra83A2mHTVJLA5xU91aCNOUnM+DWFMSbQ0Nxw==", "signatures": [{"sig": "MEUCIGpEjhfH+b8yOk9fzp0q3Exz0MyC5uu5DTxlllpep8nCAiEA2vYmaZn/kof44EkLvYgO0s0uBPV6qEJhqN7c1C7rHEs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2270843}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.34.9_1740814361898_0.17663128007408724", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "1ec841650b038cc15c194c26326483fd7ebff3e3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-uagpnH2M2g2b5iLsCTZ35CL1FgyuzzJQ8L9VtlJ+FckBXroTwNOaD0z0/UF+k5K3aNQjbm8LIVpxykUOQt1m/A==", "signatures": [{"sig": "MEUCIQC626145eTj5GJuNXNBz/8qr7AUVJlXI/FUnhaNdiy7dwIgJa0z34QhImtkOyRV79R7mW2JOveLeHeQrCahcaMIfXA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2270843}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.35.0_1741415090430_0.8228701415798194", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "d32e42b25216472dfdc5cb7df6a37667766d3855", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-KqjYVh3oM1bj//5X7k79PSCZ6CvaVzb7Qs7VMWS+SlWB5M8p3FqufLP9VNp4CazJ0CsPDLwVD9r3vX7Ci4J56A==", "signatures": [{"sig": "MEUCIQD9K3mYVt2SKnOTW0Rf6LCAL9Iwq2WXICDxaDyxhgq7dwIgFNqup92KSxHaixy/zpkQ5F2ofnKoUG2qHahOvhrjaBI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2283131}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.36.0_1742200549064_0.06941816388888089", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c3b4324496236b6fd9f31fda5701c6d6060b1512", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-oNrJxcQT9IcbcmKlkF+Yz2tmOxZgG9D9GRq+1OE6XCQwCVwxixYAa38Z8qqPzQvzt1FCfmrHX03E0pWoXm1DqA==", "signatures": [{"sig": "MEUCIQDlVvViLLPBOaq18J1nyrV7ixXcBpbGIm0UWVgj4m2mjwIgYUE0S6iIy1ycGBgyeNU6sBhXvOSLb4S5Ok0oU/uj1p0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2283131}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.37.0_1742741832334_0.5499915936263569", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "a97f73a43a374e44bef4a9ed84899c26454831ea", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-wZco59rIVuB0tjQS0CSHTTUcEde+pXQWugZVxWaQFdQQ1VYub/sTrNdY76D1MKdN2NB48JDuGABP6o6fqos8mA==", "signatures": [{"sig": "MEQCICaH1C0W5yPPg5ICzLDeEmF9tNA+Mb38uPGPSi2Yq841AiAXq9quzwZoO5G7qDoV66cyNTvd/P8R7Y66ayEE+7JvYg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2287227}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.38.0_1743229752365_0.6452520455438071", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "cb43303274ec9a716f4440b01ab4e20c23aebe20", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-EKf7iF7aK36eEChvlgxGnk7pdJfzfQbNvGV/+l98iiMwU23MwvmV0Ty3pJ0p5WQfm3JRHOytSIqD9LB7Bq7xdQ==", "signatures": [{"sig": "MEQCIE0DIZaBgI05PSOA5fZV11TIXNzsjr5iW+efbvJSmYQZAiAdkOM61UIR010KNuVd/aYJ1chg5X/WFWpiPRwttFHAJA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2287227}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.39.0_1743569377465_0.6303417785076351", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "ac527485ecbb619247fb08253ec8c551a0712e7c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-L5ZLphTjjAD9leJzSLI7rr8fNqJMlGDKlazW2tX4IUF9P7R5TMQPElpH82Q7eNIDQnQlAyiNVfRPfP2vM5Avvg==", "signatures": [{"sig": "MEUCIG6s0rzae0v80ZNroSAn5CX4EXMMGwAl7IMmHFLpXqZOAiEArzIczrVwGdyon6rJPSTxm6/j6vEPjpfL9a9DD/AE8+0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2292643}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.40.0_1744447181036_0.9509961263812245", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "141c848e53cee011e82a11777b8a51f1b3e8d77c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-Y+GHnGaku4aVLSgrT0uWe2o2Rq8te9hi+MwqGF9r9ORgXhmHK5Q71N757u0F8yU1OIwUIFy6YiJtKjtyktk5hg==", "signatures": [{"sig": "MEQCIAwe6si/9zvYj1Iauf19RESLuO87wk6J3hcoSRpZpBpCAiBfEB4+rujJ3YXOSn9tixU1GfH8OWwKJKJcFD526Y4eXg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2325435}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.40.1_1745814929538_0.03724711951007875", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "fa72ebddb729c3c6d88973242f1a2153c83e86ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-KlE8IC0HFOC33taNt1zR8qNlBYHj31qGT1UqWqtvR/+NuCVhfufAq9fxO8BMFC22Wu0rxOwGVWxtCMvZVLmhQg==", "signatures": [{"sig": "MEQCIAtZNrQEp6PoffGXelhIWXhwaT07JHDtRgBdGic8Nd45AiArgfk2H/pnzBDt4VWLaPVV4sbKvxkLSkZKf+Q7/G60gw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2325435}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.40.2_1746516418888_0.7343800643082063", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "c28620bcd385496bdbbc24920b21f9fcca9ecbfa", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-nn8mEyzMbdEJzT7cwxgObuwviMx6kPRxzYiOl6o/o+ChQq23gfdlZcUNnt89lPhhz3BYsZ72rp0rxNqBSfqlqw==", "signatures": [{"sig": "MEUCIQD22rEm/ZpHIQK5uuOV6ZdPf2iickiCr1mEBWKYcrbNaQIgBV/4LTLS7vGkFco7zDw1pXcXV+OYMhGNG64MC4lCGuU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2325435}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.41.0_1747546417113_0.7524201416400198", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "f4fc317268441e9589edad3be8f62b6c03009bc1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-g0UBcNknsmmNQ8V2d/zD2P7WWfJKU0F1nu0k5pW4rvdb+BIqMm8ToluW/eeRmxCared5dD76lS04uL4UaNgpNA==", "signatures": [{"sig": "MEYCIQDyma4kj9qJsQAApUi1x1juYkJc5vV3P8ZZQ67/YDOz3AIhAOsuBO380Zd+C/3sUEHSKAsGN6g9qEP7BuG1HqsA4vOw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2411667}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.41.1_1748067277714_0.4258104137464609", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3d434fde51770b87ff0f6a9085d0ce9098c102ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-Cg1ywrBHMMCj4Il0QdcVamt5UK9HQ1ntNUXzhAw4yZ2rTJGFkjnkYyEPEpunb1L9orm+g0kvtZUydShTtLUNkA==", "signatures": [{"sig": "MEQCIAduoUNe/m+LPOlZx4PIwp5peybwDbR7VE+L11iW3pvhAiBlVIp7pgTyyqjBOKyJsoBCPKoCabbPqOPQQjLTSuChvA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2411667}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.41.2_1749210037611_0.44420772853154", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "3deeacff589e7f370aca5cef29d68d4c8fa0033c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-eoujJFOvoIBjZEi9hJnXAbWg+Vo1Ov8n/0IKZZcPZ7JhBzxh2A+2NFyeMZIRkY9iwBvSjloKgcvnjTbGKHE44Q==", "signatures": [{"sig": "MEUCIQC5gsVA4sOUt7zsEiK40N+pasyHy4qjprlnruvWltV3NgIgPwYteLBnYY2tuc5+b/ww6DxX2daEv6+tiSnHaPywD10=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2411667}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.42.0_1749221298197_0.5604661059032938", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "e43bb77df3a6de85312e991d1e3ad352d1abb00d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-8FnkipasmOOSSlfucGYEu58U8cxEdhziKjPD2FIa0ONVMxvl/hmONtX/7y4vGjdUhjcTHlKlDhw3H9t98fPvyA==", "signatures": [{"sig": "MEYCIQChB7W5KwCydF7HP3v6OCPaGwxPkFvrlSscbfab4jUV3wIhAIFmW7Jyw1FUdokIZ4u118geTw5kuuYRetLa80Fqxnms", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2411667}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.43.0_1749619364213_0.13392099355861875", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "dbc5036a85e3ca3349887c8bdbebcfd011e460b0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-ZTR2mxBHb4tK4wGf9b8SYg0Y6KQPjGpR4UWwTFdnmjB4qRtoATZ5dWn3KsDwGa5Z2ZBOE7K52L36J9LueKBdOQ==", "signatures": [{"sig": "MEYCIQCpJ8A1s/wQ75cUhY9rvZkYGPRal7Uzag6PSA+ERiyIUwIhAJbdTxDdsw5VshYag5TUx8qMS43p+8eYvP6uKdgHCZtE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2423955}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.44.0_1750314183003_0.48311263349149125", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-arm64-gnu@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["arm64"], "dist": {"shasum": "b39db73f8a4c22e7db31a4f3fd45170105f33265", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-yuktAOaeOgorWDeFJggjuCkMGeITfqvPgkIXhDqsfKX8J3jGyxdDZgBV/2kj/2DyPaLiX6bPdjJDTu9RB8lUPQ==", "signatures": [{"sig": "MEQCIDL/rRXrBMnTdueBEfap9gJ0pdXx5nuyS/zMnqCNy+ZGAiAd1YwCYSuM4wZdBCXxb39p5Jp5c6TlnzkpNgeq3MDOHw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2423955}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-arm64-gnu_4.44.1_1750912461787_0.8504445112406605", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-linux-arm64-gnu", "version": "4.44.2", "os": ["linux"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-arm64-gnu.node", "_id": "@rollup/rollup-linux-arm64-gnu@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-NMsDEsDiYghTbeZWEGnNi4F0hSbGnsuOG+VnNvxkKg0IGDvFh7UVpM/14mnMwxRxUf9AdAVJgHPvKXf6FpMB7A==", "shasum": "c0f1fc20c50666c61f574536a00cdd486b6aaae1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.44.2.tgz", "fileCount": 3, "unpackedSize": 2419859, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDMAPixUpA3+KIfM0c/d3zjaJmN4bza4wqqlsJPjtFKIwIgU/a5ifSKVwe+Aiff7fO6hyRn/95xpVlc+RdX+DGBRLY="}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-arm64-gnu_4.44.2_1751633773728_0.8193850398819575"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-07-31T19:17:55.722Z", "modified": "2025-07-04T12:56:14.219Z", "4.0.0-0": "2023-07-31T19:17:55.951Z", "4.0.0-1": "2023-08-01T04:49:00.260Z", "4.0.0-2": "2023-08-01T11:16:33.937Z", "4.0.0-3": "2023-08-04T08:16:59.622Z", "4.0.0-4": "2023-08-04T11:36:38.403Z", "4.0.0-5": "2023-08-20T06:56:50.484Z", "4.0.0-6": "2023-08-20T07:51:39.449Z", "4.0.0-7": "2023-08-20T10:33:28.843Z", "4.0.0-8": "2023-08-20T11:22:20.341Z", "4.0.0-9": "2023-08-20T14:29:06.738Z", "4.0.0-10": "2023-08-21T15:30:04.757Z", "4.0.0-11": "2023-08-23T10:15:55.805Z", "4.0.0-12": "2023-08-23T14:40:30.617Z", "4.0.0-13": "2023-08-24T15:48:35.317Z", "4.0.0-14": "2023-09-15T12:34:27.474Z", "4.0.0-15": "2023-09-15T13:06:54.580Z", "4.0.0-16": "2023-09-15T14:17:18.411Z", "4.0.0-17": "2023-09-15T14:59:08.085Z", "4.0.0-18": "2023-09-15T16:10:10.560Z", "4.0.0-19": "2023-09-15T18:50:58.987Z", "4.0.0-20": "2023-09-24T06:10:39.534Z", "4.0.0-21": "2023-09-24T17:22:26.006Z", "4.0.0-22": "2023-09-26T16:17:23.275Z", "4.0.0-23": "2023-09-26T20:14:24.834Z", "4.0.0-24": "2023-10-03T05:12:47.974Z", "4.0.0-25": "2023-10-05T14:12:46.379Z", "4.0.0": "2023-10-05T15:14:32.797Z", "4.0.1": "2023-10-06T12:36:42.376Z", "4.0.2": "2023-10-06T14:18:42.713Z", "4.1.0": "2023-10-14T05:52:15.590Z", "4.1.1": "2023-10-15T06:31:49.422Z", "4.1.3": "2023-10-15T17:48:28.319Z", "4.1.4": "2023-10-16T04:34:11.058Z", "4.1.5": "2023-10-28T09:23:33.423Z", "4.1.6": "2023-10-31T05:45:16.152Z", "4.2.0": "2023-10-31T08:10:41.733Z", "4.3.0": "2023-11-03T20:13:04.166Z", "4.3.1": "2023-11-11T07:57:55.867Z", "4.4.0": "2023-11-12T07:49:55.231Z", "4.4.1": "2023-11-14T05:25:13.850Z", "4.5.0": "2023-11-18T05:52:13.441Z", "4.5.1": "2023-11-21T20:13:10.707Z", "4.5.2": "2023-11-24T06:29:48.701Z", "4.6.0": "2023-11-26T13:39:14.988Z", "4.6.1": "2023-11-30T05:23:08.732Z", "4.7.0": "2023-12-08T07:58:02.091Z", "4.8.0": "2023-12-11T06:24:54.718Z", "4.9.0": "2023-12-13T09:24:17.969Z", "4.9.1": "2023-12-17T06:26:12.228Z", "4.9.2": "2023-12-30T06:23:28.953Z", "4.9.3": "2024-01-05T06:20:48.020Z", "4.9.4": "2024-01-06T06:39:01.184Z", "4.9.5": "2024-01-12T06:16:14.843Z", "4.9.6": "2024-01-21T05:52:20.149Z", "4.10.0": "2024-02-10T05:58:42.552Z", "4.11.0": "2024-02-15T06:09:39.701Z", "4.12.0": "2024-02-16T13:32:17.716Z", "4.12.1": "2024-03-06T06:03:35.440Z", "4.13.0": "2024-03-12T05:28:35.853Z", "4.13.1-1": "2024-03-24T07:39:22.740Z", "4.13.1": "2024-03-27T10:27:41.596Z", "4.13.2": "2024-03-28T14:13:36.154Z", "4.14.0": "2024-04-03T05:22:54.173Z", "4.14.1": "2024-04-07T07:35:40.441Z", "4.14.2": "2024-04-12T06:23:40.718Z", "4.14.3": "2024-04-15T07:18:32.670Z", "4.15.0": "2024-04-20T05:37:14.642Z", "4.16.0": "2024-04-21T04:41:54.424Z", "4.16.1": "2024-04-21T18:29:59.754Z", "4.16.2": "2024-04-22T15:19:17.099Z", "4.16.3": "2024-04-23T05:12:35.651Z", "4.16.4": "2024-04-23T13:15:08.445Z", "4.17.0": "2024-04-27T11:29:52.918Z", "4.17.1": "2024-04-29T04:57:55.943Z", "4.17.2": "2024-04-30T05:00:46.527Z", "4.18.0": "2024-05-22T05:03:45.581Z", "4.18.1": "2024-07-08T15:25:13.514Z", "4.19.0": "2024-07-20T05:46:16.217Z", "4.19.1": "2024-07-27T04:54:02.992Z", "4.19.2": "2024-08-01T08:32:55.392Z", "4.20.0": "2024-08-03T04:48:52.505Z", "4.21.0": "2024-08-18T05:55:36.434Z", "4.21.1": "2024-08-26T15:54:15.563Z", "4.21.2": "2024-08-30T07:04:27.945Z", "4.21.3": "2024-09-12T07:05:51.697Z", "4.22.0": "2024-09-19T04:55:33.463Z", "4.22.1": "2024-09-20T08:21:55.549Z", "4.22.2": "2024-09-20T09:33:47.148Z", "4.22.3-0": "2024-09-20T14:47:59.893Z", "4.22.3": "2024-09-21T05:03:11.300Z", "4.22.4": "2024-09-21T06:11:24.120Z", "4.22.5": "2024-09-27T11:48:18.190Z", "4.23.0": "2024-10-01T07:10:08.973Z", "4.24.0": "2024-10-02T09:37:22.687Z", "4.24.1": "2024-10-27T06:43:05.681Z", "4.24.2": "2024-10-27T15:40:13.363Z", "4.25.0-0": "2024-10-29T06:15:15.868Z", "4.24.3": "2024-10-29T14:14:13.006Z", "4.24.4": "2024-11-04T08:47:14.267Z", "4.25.0": "2024-11-09T08:37:27.256Z", "4.26.0": "2024-11-13T06:45:04.737Z", "4.27.0-0": "2024-11-13T07:03:18.818Z", "4.27.0-1": "2024-11-14T06:33:16.511Z", "4.27.0": "2024-11-15T10:40:40.811Z", "4.27.1-0": "2024-11-15T13:28:13.514Z", "4.27.1-1": "2024-11-15T15:38:08.832Z", "4.27.1": "2024-11-15T16:07:46.936Z", "4.27.2": "2024-11-15T17:20:09.176Z", "4.27.3": "2024-11-18T16:39:41.322Z", "4.27.4": "2024-11-23T07:00:26.117Z", "4.28.0": "2024-11-30T13:15:53.391Z", "4.28.1": "2024-12-06T11:45:01.494Z", "4.29.0-0": "2024-12-16T06:39:59.246Z", "4.29.0-1": "2024-12-19T06:37:36.449Z", "4.29.0-2": "2024-12-20T06:56:08.591Z", "4.29.0": "2024-12-20T18:37:30.277Z", "4.29.1": "2024-12-21T07:16:08.376Z", "4.30.0-0": "2024-12-21T07:17:20.156Z", "4.30.0-1": "2024-12-30T06:52:22.368Z", "4.29.2": "2025-01-05T12:07:48.214Z", "4.30.0": "2025-01-06T06:36:46.217Z", "4.30.1": "2025-01-07T10:35:58.381Z", "4.31.0-0": "2025-01-14T05:57:47.854Z", "4.31.0": "2025-01-19T12:56:52.987Z", "4.32.0": "2025-01-24T08:27:40.374Z", "4.33.0-0": "2025-01-28T08:30:14.467Z", "4.32.1": "2025-01-28T08:33:22.686Z", "4.33.0": "2025-02-01T07:12:05.890Z", "4.34.0": "2025-02-01T08:40:29.245Z", "4.34.1": "2025-02-03T06:58:18.935Z", "4.34.2": "2025-02-04T08:10:07.972Z", "4.34.3": "2025-02-05T09:22:10.798Z", "4.34.4": "2025-02-05T21:31:16.815Z", "4.34.5": "2025-02-07T08:53:07.675Z", "4.34.6": "2025-02-07T16:32:13.038Z", "4.34.7": "2025-02-14T09:54:05.201Z", "4.34.8": "2025-02-17T06:26:31.095Z", "4.34.9": "2025-03-01T07:32:42.146Z", "4.35.0": "2025-03-08T06:24:50.742Z", "4.36.0": "2025-03-17T08:35:49.245Z", "4.37.0": "2025-03-23T14:57:12.595Z", "4.38.0": "2025-03-29T06:29:12.598Z", "4.39.0": "2025-04-02T04:49:37.743Z", "4.40.0": "2025-04-12T08:39:41.283Z", "4.40.1": "2025-04-28T04:35:29.804Z", "4.40.2": "2025-05-06T07:26:59.143Z", "4.41.0": "2025-05-18T05:33:37.336Z", "4.41.1": "2025-05-24T06:14:37.953Z", "4.41.2": "2025-06-06T11:40:37.842Z", "4.42.0": "2025-06-06T14:48:18.422Z", "4.43.0": "2025-06-11T05:22:44.465Z", "4.44.0": "2025-06-19T06:23:03.218Z", "4.44.1": "2025-06-26T04:34:22.029Z", "4.44.2": "2025-07-04T12:56:13.989Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-linux-arm64-gnu`\n\nThis is the **aarch64-unknown-linux-gnu** binary for `rollup`\n", "readmeFilename": "README.md"}