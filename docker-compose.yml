version: "3.8"

services:
    app:
        build:
            context: .
            dockerfile: Dockerfile
        container_name: truycuuthongtin_app
        ports:
            - "8000:80"
        volumes:
            - .:/var/www/html
        working_dir: /var/www/html
        depends_on:
            - mysql
        environment:
            - APACHE_DOCUMENT_ROOT=/var/www/html/public

    mysql:
        image: mysql:8.0
        container_name: truycuuthongtin_db
        restart: always
        environment:
            MYSQL_DATABASE: truycuuthongtin
            MYSQL_ROOT_PASSWORD: root
            MYSQL_USER: laravel
            MYSQL_PASSWORD: laravel
        ports:
            - "3306:3306"
        volumes:
            - dbdata:/var/lib/mysql
        command: --default-authentication-plugin=mysql_native_password

    phpmyadmin:
        image: phpmyadmin/phpmyadmin
        container_name: truycuuthongtin_phpmyadmin
        restart: always
        ports:
            - "8081:80"
        environment:
            PMA_HOST: mysql
            MYSQL_ROOT_PASSWORD: root
        depends_on:
            - mysql

volumes:
    dbdata:
