{"_id": "lightningcss-darwin-arm64", "_rev": "37-0c5211e50c3c759cc0499165988060f9", "name": "lightningcss-darwin-arm64", "dist-tags": {"latest": "1.30.1"}, "versions": {"1.14.0": {"name": "lightningcss-darwin-arm64", "version": "1.14.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "a10bd749b375c2749d1dd6bacd5c075bb10a4939", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.14.0.tgz", "fileCount": 3, "integrity": "sha512-AFt8Qs9Qjbg5AlB/3cYljanVEeyJI4C9bPqO8hI7bNa2HdDIINI4NgfGpri8BNwA3zcAlFPH38caIqcG3LWC+g==", "signatures": [{"sig": "MEUCIQC7vMHUeZCd4s8evOKtUdTc2pEdPtlk8MQ2kPAEDjyzWgIgBwA27fZRj330kQ61hLJmZEP/xUDPu+99yUNjAkmA1oA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3122639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGXtGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0OA//QzGqk+hPsqEbbxMhLSYhZ/gwjeW6T0Of4o4CnKrTMquyAafp\r\nL1PP+1rFPiFaoa8SolI8PhKjZKhQVq78GJYK63eBm63Mzc0kf7uoDF2uHDD2\r\nFxBUwbR3wMEnB/EiJV6zCep8kR5ekPxv3x3L6P2Ut2sF29aJ31ZoCvjO+67Y\r\nx6rF84eD/lW9zaBcQ7+FdEsJEDkumUxUYvwrYGFLU+g8+LTWzSWaGMni8xS8\r\nvddlZzRDUKptCoSD/LJJcY2clkMrJZq4I61fO91IpV7XGLVBWG55nUJ0IidA\r\nwlGOgBcTMadgGPUSeS4s0/2rcQgEfCoYrY1FfiW1S9i+dfZ7Tsw1Q4oP5yis\r\nW3UVHu6hpb5xBmEpvVEqDz9aShLGKetsj+LpEUht1dqWjgxsNtOIdkEHDED+\r\nbsrxfhdXPAXKMCtooJt6xLYa3dceRA5BBxSs819iiD/dhQ97L8THaGN315pJ\r\niSqaLooDefXD5QOfDQpkshSauM4h+fMf0QU1y/wamvJwoNsD2DZFVsz2MZSj\r\nCZ8bCh9HZ/HCoWZ2E9zh4lxNWtYHGDScP1Jbt35EZCSO4a2GOigPkT0gb8dx\r\nZ8fekzmjYZ/UKSn58wuayTgWGlsYsWBCWUiq1aC8QXgbJHZ4vFyroXOnEbZb\r\nhkwVH0JJN5v8BJpgwZm0NHURmBN4nzv3Rtc=\r\n=QkKy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "2d17e0e1ed1482525985ef4d30809b4c06d76944", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-arm64_1.14.0_1662614342103_0.8104009407857602", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "lightningcss-darwin-arm64", "version": "1.15.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "c4fad19f33d96545e92e98f4a664d36edf52f03c", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.15.0.tgz", "fileCount": 3, "integrity": "sha512-5MZH4uOq/CgcFJ+8LrhTyAu1gwqEONC+kWg8z/0Lr+VeWl/hTBdPs9f0DMw4EHZVCtAajaicgV7pnwUoDBcn0A==", "signatures": [{"sig": "MEUCIDDi6Pq3oxHWZ/146RNf9fSNpNmrbfWqr+TWBhlsXZi6AiEA+9G929lWbaIb1iXQ0HmQJ8X+v/QoDyOeePgP9Kw1ZO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3139230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIqcWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowHxAAmMlD169Zi90to4ZNN3aki9t1EZDnFsUZ6+lkVoVDIyWFS+0T\r\nyDVlGHGOUscTQ6jdggPyJZcjasf75FmdwUomfVLpzH0wD5PSyO8FugTfnOZR\r\nVMLbcO6AOi0i0R+aw7n7r9uL/NQ/X96iv0mCKoGhcGIDZ4JSv7zzPr57WdVO\r\nuG/bmzevYfwc4e2BRtVP+lhz8qSgV5qlmvZTUXn3sKzrzA3PMKGyzPODQECe\r\n0ZNNAfMVpxOuaAny3J7ZAEP8NeDD78lAzfwhlNimFaCmVrCHHAkS2zJj1SCC\r\nv3/30Ef4c5XLM3dPJJgR8vDe6sJtWB6AAMQIMf8lBA7xg3reP2n/l+GUlgVG\r\nfJYebsznkM0xjYF+uYfuAYh7T9NjWcDfebCqYTIzlSKE43ioyoDZjGfIMiof\r\nc73VcYLzHbFaOR/G+WIyLTuicvOZzKxv/Xc9R9FNxwXsu1naOuR+O//YhnlS\r\n9CEPXyH6FCjKx/VWYLvv7unVqQmrJ5lECrxSlT0CHU33Kv/OHqrghvng/sGD\r\nnX+8AvaCU7QwAMlgm2hjNX0sc+Q+OQfB7cSG2wNLkr9VYvoJxIDcZHYdqs+t\r\n0LRSl0T7q7ANG3fvqd1ThWvYJw1waI2IU8ef+kG6o/o5AIrPLLAMVOxSYFow\r\nfjbm9FHEqK/2EmY+wVUflXb8eqGW8L9K5KI=\r\n=+jYW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "1a23835a0d72afc06ab5f98cbbee5fb03ae09074", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.15.0_1663215382184_0.5345795938616065", "host": "s3://npm-registry-packages"}}, "1.15.1": {"name": "lightningcss-darwin-arm64", "version": "1.15.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.15.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "9b15f7d65898cae9ceae11d5c7ef6ea64edcce16", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.15.1.tgz", "fileCount": 3, "integrity": "sha512-4bqe9OCWzj8gPgwzpDK7TPopIoKx9CQMPVN83/+T5LVLkh9sSS0ltZLjAFI399GIkC6idl6rguUQ5qPeq4yxsQ==", "signatures": [{"sig": "MEYCIQC+wYWV8TKV38sLgUN2ihVfP2W6IYCRemM/cH9e8GUuJAIhAOQMct7C0PNWvxXy6xbmpzmiMhj32fkhcWyW7qlF0gb1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3139230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjI/I4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoblQ//Z1elW6iCycW0MO37y9chdBXo7Vpm9K8FFBFA2yc8Y+n7ISEe\r\nKZIfHco02J8flSTsIX1qvUASSShoZqSXONvmZ/mcNTQ5mJWg9KuyA6zWQPtX\r\nzkk42Q0p1ma15w9V17yosvR5BWQzTLHr5XWyGCkOPFKJfp/MnpYn5B4F93Ao\r\nsmG0pn4IPLLKMDlZKGRNpa07l9wqBGUjDFi/w40NJTROSLkXOcebJ4rs6qsN\r\nYPqAnwth44dglFyeOpS8bJzHX0TZRhrjoWRUpx61uHxukLzjBeiAyBtSuaX2\r\nCzHUT6k4KlFdAMO9i95kHHW8H3peBYWnGfG+r8CvdGpP5gdPAIH1xMyBcQ/e\r\n1oq11JyVglAszqj+4YLkBnm+P0UvZggNZijRUzvsRDo7C14qvQHCkiMls8fm\r\n7G/Y+VgaFSXQbteU5J2W1688LHE1y5Zxe6VXSSxWr3JANspbMo7ROTleVZog\r\nzQZgua+0fGhVGw7quGCkHBAF0mOBaaueTf+bURxOEXSgUDMMb0xpACKd62tt\r\nEhTRrbrgjPTiIXdteWzmGuLafWmsyFqWX8Goz95dYrbuJafmUX+d+acIAwBl\r\nou+86cm6rFTiyVvFnmwaGyvhCvjQXW6Ba13IpBcMW/jNGbFK4IF6LREhTe59\r\n0EzpeIQ0LO4br7obY1D4n3kgp3HaF3+gemg=\r\n=lZ1O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb66ece2f9b47bf4ef60848736dde697ec80a319", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.15.1_1663300152166_0.47843881965703083", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "lightningcss-darwin-arm64", "version": "1.16.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "f3318a2e64ca160610977675ee1a7e611f4a3617", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.16.0.tgz", "fileCount": 3, "integrity": "sha512-gIhz6eZFwsC4oVMjBGQ3QWDdLQY7vcXFyM/x91PilgHqu63B9uBa10EZA75YoTEkbKhoz0uDCqyHh/EoF1GrkQ==", "signatures": [{"sig": "MEUCIEU8t6U9Rfuy+Hxh/hOk5SOUoeYUVZOUI57ho9RaU3U6AiEA5g3OASccN5nOYz7PrePcEERmo1E5foEfsJzqQ6XARPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3833662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKTIWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreNg/8DV1/x/isPWSJgQWIJG5Y8lhliPd2sgfd7hy5XZItewogjn+1\r\nFuXw2lVU9sTnQI8z+ODGU9PSkv/Mx16/iIEF9XhlNvaeMrdvDgJvzHVEuh64\r\nwu0QOkVRcoatRJO3sWcg7FYf4RhadAqkpmGK42mETvVl3pVPZpg33R6XgnTB\r\nypqsT2nS1rhSdC3LR03c2kmGnyACzXqqX/7tuMMfeiitVFK4RQQxQUPHbuH2\r\npWxO4QEw5AKDZpe/EP8zbz55tbGubu4MN1mQhm2IVwQBp8LToA7ZdLD28UmR\r\nnu8nzwJ1eVV3U9nSkjDxCPeiVzmwoGg/UEmYRQuWyBD2wMFxPydBqd+CQEqu\r\nm5MZt422x6hWk5WAfQT3WZ1kgTlOaQ56unjJZRXsH5LvoZ2Nfnqeu7Y7gStC\r\nrFqzxpSaC/Zpmo41VD7nJUTICFomug4JCgaaMcYYpDLzbIk98HR6dakafsXI\r\nUow7W4vcKHN0n08vN/1YY99W6ZL61DAvW783pxBkvAv3z3JBS6YC6cLs+6ce\r\nj7CJOWGyZttzGqkOtXueHhLvE604wy/olOp6wcHWHHp2QcyEoGyj9XQx8fpu\r\npk+2fWYDHpBpLxLjOzqVol/u+Tu/8wzXqKE6oZLfrgACHeB+ccX4a+Gc6f8M\r\nhsk83ynVc5idEbqkEknKMk3h6PCosVX+Cl4=\r\n=LNXf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "200120e24aa31449703707db14ffef1bf40a7fea", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.16.0_1663644182487_0.6819581558033965", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "lightningcss-darwin-arm64", "version": "1.16.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.16.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "f67287c500b96bc5d21e6d04de0e2607ff2784ff", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.16.1.tgz", "fileCount": 3, "integrity": "sha512-/J898YSAiGVqdybHdIF3Ao0Hbh2vyVVj5YNm3NznVzTSvkOi3qQCAtO97sfmNz+bSRHXga7ZPLm+89PpOM5gAg==", "signatures": [{"sig": "MEQCIDPCBXy54GOLdtAn+UL4oFaZdmDqloVmq/JjYC9X5E1ZAiBEYtp/jGdHHroIs6aHNouPUTNCGFkQGyUNvS9lNLH4Ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3834270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZ/aUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxSQ//ZwtyRkA2W4+tWrcsck8ljKnNEXAH+ORvVPI9JvBlCwu71NXB\r\nlHXZ5tpwif0FxGzFDZWEV5qBSQuaKsllsV030E7OWy/bbg0dm2yepyPdJSCt\r\nrnkDfJX35++MKG0AaLKg3zDguUsTkVU2aZJYH+/dXeKkVlsYQkmAayj35w2D\r\nX4d/bmM3sqvA0yDAVcVYIShDjIDGFCy6QClajVU2JcI6rJM+NP8X4G6pi1T6\r\naTDShrpNSWBf66mbaoljojVhHQ3Ior9aorQeVzwjQkqEvPDDEjp4li4WjNwi\r\nqDRFwPnb/rwwQXN95aN6LynPJysEAAHZId34GgCSFZY/8ytFYZkxMqCDSgJw\r\nuCt7qNpTKEXm9BnCKPbAg7zHLksmIv7pdlBoPN0JOiYNzYm1HKE+KbgbxBxn\r\nd8UnH2buiIT9donsMJ7KOfG7O5Xfs1lfAj9Vm4LpGBZ3oA6WYmZGkL7HDcnu\r\n1UkpvUtZJzCE+ltyQxt4y9NCQfARRF0VZ1m0x1fTeV2qSdgDSmxdcOrFlY7T\r\n4tVeOERvvVk+3axeMlZOB6OFfhpkkyIHIrfGWaucK4+ggwb6iCNqh/FV6pGU\r\nmqLq2iyCQcjnyzLEQRYWmeI3zrzvIaNpSB5Hr8Kbm4nNS2HDT+tgHrSdyJ2F\r\n1cjbdg5ratbTH2NLOKshZKlkMIwplDEJ97o=\r\n=tRp5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "87e808fd771374ae705886351ee62c4fac3d3630", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.16.1_1667757716580_0.22902846159395862", "host": "s3://npm-registry-packages"}}, "1.17.0": {"name": "lightningcss-darwin-arm64", "version": "1.17.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "c30e5f0c7419a3c88bb645fd1aa69573166c160b", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.17.0.tgz", "fileCount": 3, "integrity": "sha512-k8/9PaFUZ7ISS5XGDo3RLRUoj1Yf0yNvjYsU4n8TqjE7kCZY0eJaYb6PFvFba1RQ2i3EX5SFiW9nxrEctU+o1A==", "signatures": [{"sig": "MEYCIQDmi0u4tnf7w927YNxmOnPqez3S5vwVsDFkKKuMyl5ZUAIhAKXKrWXqAnjvFTjRktDnfMzI80/kxUV77YJFDqrLqRzy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4793934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhjeoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSIA/7BtwPpNFsU37i2jJy6VeWsEapkvH1lJnnn1KKZHCTJ4z+Mi5Z\r\nlim0uqivwnFJcfEjonQSeDZq3Wlp1LlXYVHjzdh0CB+mGzjEyVROfINwgZyP\r\nz1XgD+FMMCLdB/trAqDBQfXV1HV+CXcOflrwHr9P62u9CY4fSkRFSemPHT0o\r\nrwGU71j2Dc5SagCqqPYF+QNzBtcBIOAsUlrFoqNayA+4E3/jLj0pOvU4FNZk\r\noBuQz6gpWQlTlW111wpJLBsa78LP7ZCRTF47BU4Xmgg7aceQnk6IJW9fvdOS\r\nHS3xhdl+oUHK3+i3aGZF2w7ETFyij3/MSbUqmcrdFoCN/ZCw1cwoWCqbJ/sY\r\nYktuoJQ8u9xW8GWD9ri0UxfpAQv0K+FcM4bfCPZ6WajTmucZg5U8fDjqy+XG\r\ncv169MZm7fdmS6KnzV9bRfKThsp9+O2h1oVVJta6LXKswo80nG+YJiSD0qNm\r\nxx89lYUM4ST4jHTeJ3yjW7Q1Hv1HQD+VkI2Buod3k08mnztmWEa0GElfHRkz\r\n5ie9p1REQoOONVbFlOKNPdoJuGXDyt8cTRuF2/lVfrzFupZIIXksmQhHO8/U\r\nk4CixIj6gWgI1WTa/xdI8nukmp0zG2wuLJIKJCWYLNLgXkluzwFPwko5gvz0\r\naYY9XB9T1+BJCSqmNUrIU52ICRjw1SQLneI=\r\n=RMcb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d4853b204cc80b051d29be324e2d16f621e76336", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.17.0_1669740455980_0.4703796641360236", "host": "s3://npm-registry-packages"}}, "1.17.1": {"name": "lightningcss-darwin-arm64", "version": "1.17.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.17.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "d0384a47f19f1a02c29074930a23e5888e76b11a", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.17.1.tgz", "fileCount": 3, "integrity": "sha512-YTAHEy4XlzI3sMbUVjbPi9P7+N7lGcgl2JhCZhiQdRAEKnZLQch8kb5601sgESxdGXjgei7JZFqi/vVEk81wYg==", "signatures": [{"sig": "MEQCIBSoNqoQiNAWBf3lAeEt689UcK+cXMqJbLqSrNWErWrpAiBiU94+0QZMX1+YA1PlOYB8DOESm26QDhc0ZhyMYFHgUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4793934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh5MtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqe2BAAh78QU4J/JAeXy0l9n4WG3uS90yFkxbwIy8U7LKi+5i40ze6d\r\nB+gQbfrW7Sc6xu2hre0IWTfXjOi56GuPxlZUeOfPejT4kQlkCDrQjHF53E8I\r\nWU4b+GYPkYdh281AgLv9sA1lY6pHO4FNMATO6vre9QY7MLbmew8ZZ+cgZJ43\r\ngjg5HGOLEErl/W88mMOYHp4vyJ9KoUdx/fiMlMm66CadGOO6iSe2UJq6RM9Z\r\npI37JwZVo2eOrtAAKDSpNmigSwdYJCbUEs7hB4bZ3iJlgQWAxpwdb8KOb+jX\r\n/Z0d1TsmYzzrfQj/bVBOl+Wy4lSU3R2q+y1PFJ3B8o/Hv8rVjZEey3HLIcNk\r\nnxdtoEpufeR1aa9DVmhzB8ZJgzJ7qhvxs/YxjhXFfsuiQGdPTi4UXCigNYKc\r\nsVynm2TqZXvpXzL3om8aF/ajf7caLUPQ5c7n7WYY008cq+HFiMrUuhQmRngn\r\nMGWk9ifyxJoPn4K7x6j8ZIL7eZh83FHJ22EpvPS3KpBQ/+DKtX+fPw7VK2nv\r\nen9Jg199JxSs6paSLDeU4xV+YSV7FPHnlBWJXjwa3FPkwCp8n7wzmfJcyFH0\r\nUbdpJoVpUXHUavQqHKuK6HjqW3tUcABBBrvp1Cj3LNVhaxd6Lt7BSXm95pWk\r\nQAmnBK/oPXoKzoLoXRBBPUe8tQEOf3EqdLQ=\r\n=Nfnh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "27cb9c2a382fd8d9702de8def9dd18db54280c43", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.17.1_1669829421614_0.2393501816327679", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "lightningcss-darwin-arm64", "version": "1.18.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.18.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "bcd7d494d99c69947abd71136a42e80dfa80c682", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.18.0.tgz", "fileCount": 3, "integrity": "sha512-OqjydwtiNPgdH1ByIjA1YzqvDG/OMR6L3LPN6wRl1729LB0y4Mik7L06kmZaTb+pvUHr+NmDd2KCwnlrQ4zO3w==", "signatures": [{"sig": "MEQCIHq7uuFE+T6TNwTM9QBa5LUP1Zyos/QnrzhuY0dudwqMAiA+N+XuUybJL41w+Rd2O6hSi5Mj/rRftVmHUuSuKH9/Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7772115, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtbCTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqg0A/+ONNoM5R4Gq6FwJ6ztAwh2ZmoYhVcyW4KwTKeIeAq15cr5YhC\r\nWRkTfwuja/f35+a4/d/BaY+K+FnnrMkUUEmsYp767xfo7wXE2oc+RmCd0TQ7\r\np1xJrEqD7WH+hbQZq8qD9c7VUT/eQk3m47U3e47f4QYk8TChNUyO+p3HygyD\r\nXZuevSyNYf6Ixd+jM0exDRDl7sod+WS6GbZ6THLM6H1UnatrEAXcWKA7B/op\r\nlMeoqUb4lXbAsptjclIf1ajMaSF+vuN4jabZsSLV2a38QD85fuwoSe1YlX10\r\nyM5xIfXpk2/OcLGPXWkEecNWc8MKeFyq8tshnigcnsdSOuTohNe2SOlVMIOy\r\n1gOdcQxJPl9Huiil45cUr2cJda4NlUC6O0lu3f89ddTZVXyslhySUAA/on8J\r\n0phJ/+xrxxWJKBk3YoXYMuenufElQwgiBSSk7XU8FlWwIXWCegnM4Xnu5O5g\r\nPf+Yeywj1MGhpsbHQCRdOqwc5PxR+xAf7Du+TRf2Mg70zlQWKzqf49rSi5s1\r\nu/JZqA46DQP1F9O13Cc6wvpn9T4/ageosUmfvC99PMNNKO30Bzno+n+RQkvB\r\nBlYxWljkWbWjZDneBwi3jRoEVpc+pv6+DRUEzgtPGok4NZP0TGkDjVJNuTjH\r\nkjL8+bdj3CiBn5H1ijWueeFBsxQdo7fW280=\r\n=TON/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "6fb77292c9ef4320fa85a1e26fec3fbdbae3cf8a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.18.0_1672851601807_0.38803363880244746", "host": "s3://npm-registry-packages"}}, "1.19.0": {"name": "lightningcss-darwin-arm64", "version": "1.19.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.19.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "56ab071e932f845dbb7667f44f5b78441175a343", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.19.0.tgz", "fileCount": 4, "integrity": "sha512-wIJmFtYX0rXHsXHSr4+sC5clwblEMji7HHQ4Ub1/CznVRxtCFha6JIt5JZaNf8vQrfdZnBxLLC6R8pC818jXqg==", "signatures": [{"sig": "MEQCIEUQW1ffqkqJiW+WtOuK6ldAkd574pRh28v0wnu69rXAAiBufKOCkg5UXZYwy6CFBykgLQpUNBJ/OQW1Ttnwg3QMeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7258643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6l5BACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZZQ/8Dnv3pjYL0c7faUzaR1MGvhpeOnFtqZN6fhp0MxIf9GHj9VCj\r\nJwRYYjS2U3d1vyDvZcWbrVrsxFQCHXqDK0RoSLyey4j8YnG3LlBxy8gOG6YH\r\nHgY3jTUiGqZtXKJO/GraEKWfjL7re1k5j/NjJiQcT9G2oFhXnX1Ty9WXvTke\r\n66UA+PEuPJQZTkWRnzKHzfqXJ0pEpDVPiZ5gtutWKZrYm2MEn39eX1Z8KyFu\r\nCyM7MZNSFZP+gVXBzJmc3mclLK9vbduwOCvs13FxKA+S1kCPxPdJRot6hk3e\r\nIV3C4FKNeKUPh67TxKTi/shoeuHg3TijW8+zJLD7AsMNc25ZfkDQECuSyB/A\r\nYwzkYrk2pU2QgnA3dcCdWHdbi2VW0miXlWlbOpPJoZ9qefViRUQx/zYJzQzL\r\nIhoYq08j96YBKz+8lhPo668dajuqOeo3/GI7E8RCn721eJN9kS1QshgSHreZ\r\nFXBeg+ShxshkuHSgFdbWBPIZmZURpiZnXYbboaSWOELPZmNKEvprvMXkbMZO\r\n/tpLzO1rdmonsADPpXA2TTYqJplSSxR62qDHUSOfI0hihCGJhvojiQ1oDrkQ\r\nHsOZ/QDy4HIGPQ82yZOmH61rUFCw1nQkY2UH42OEl7D0s6hBHIoR09vR7lg3\r\nGdol2KOV5+uRLpPmbKioKjH9Gn8aRTQ/A3g=\r\n=pO/6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "05c23f1269321d4b072f6264a5c9cb6edd8a02bb", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-arm64_1.19.0_1676303936877_0.9467463043479669", "host": "s3://npm-registry-packages"}}, "1.20.0": {"name": "lightningcss-darwin-arm64", "version": "1.20.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.20.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "0416a0cb840944ea4aee972df02491c0168a784c", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.20.0.tgz", "fileCount": 4, "integrity": "sha512-aYEohJTlzwB8URJaNiS57tMbjyLub0mYvxlxKQk8SZv+irXx6MoBWpDNQKKTS9gg1pGf/eAwjpa3BLAoCBsh1A==", "signatures": [{"sig": "MEQCIA/z3ZMwWMyKojHTu35LmFAVpV1fKl+9gDWW8So48vd4AiAkvPvRTIF+Um/OnZBUV23b8c0XWxQZaGArMRa3b4J0fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7457043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQGQ2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8QhAAlJ+xEptLaTGqeYnEVmbXnDwFc2Yeezvi/zDtJ+Pfp6vchMQg\r\nd8MdzG9SFKS01B2R13qtMLbQfJVeK6TEK+5ThHbV2pBqivnPsIFXbqvmugMU\r\nJGatBJbQKsWMQOyNUsxEOkS81T6aHCagRQ8H0T55hK5vxEBUYRcPVC/R4yLg\r\nJsFAIjo6WoPxD6zXmlvl80eq+qi/4G8+7vjCDiRSWMmcLnZhTtK8W/u3shRG\r\nvbuZQTwkiCtC+oN1bLgyB0NDLLsiWJMwEtozPnt9CZAONdBdy9NgA8vpWZau\r\nNrDPEVp4EvxwsE/P42Chra3x9A8vjW03WI0bk2Y1ctHculxBn/zCs1hu4B4N\r\nAWwIa2pJZOyt9V+2zD9FNPtHrbjrrub22APXjRK2gWxzl/c9cgjfVhthZNCN\r\nScb1W9ps5bE13BIsxYQNsAG6tCR9QX+CRiwssJZF8eKJw3SUejmgAo/EX0Ff\r\nk/rjVwkaUcLKTVu1OcMQd6y5bATijR2L9V9l80HHGcBw3SEizJPWOu0QuNV0\r\n4q4Ja38n28Rg68DZEggO84EEui/TFci5d5qZ6a2XrvZTDbM6P4w98Vd28dL0\r\nSoNdovpx5DH9aLUxaLKLIY9q0ak8ewkOLLCNTgQiHdejns48TKSNanPr2TPE\r\n2gXCFH+2s945GR7XL8U3sG3NhjIlT7t6oSo=\r\n=QV+U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4028d4d9026750b31b813ecb4ca5387dba8dd396", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.15.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.20.0_1681941558343_0.3115296458776744", "host": "s3://npm-registry-packages"}}, "1.21.0": {"name": "lightningcss-darwin-arm64", "version": "1.21.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.21.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "8d74d3fd5e6fdff4697e1d72a31ee6e30c244c35", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.21.0.tgz", "fileCount": 4, "integrity": "sha512-WcJmVmbNUnCbUqqXV46ZsriFtWJujcPkn+w2cu4R+EgpXuibyTP/gzahmX0gc4RYQxTz2zXIeGx4cF2gr8fLwA==", "signatures": [{"sig": "MEUCIQCmEdX0nO7Jd0rdSAoIVp/IAMG/H2sR2qYTB5Sf3jF/UQIgQdQJSqueuWhkBUdiP5wbYhxMXHn+tttTWz7JUeWub1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7754643}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "11f4179f81041e14a04536d3bb0618f06de4e454", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-arm64_1.21.0_1686117486582_0.6802641915520096", "host": "s3://npm-registry-packages"}}, "1.21.1": {"name": "lightningcss-darwin-arm64", "version": "1.21.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.21.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "595b45510a3637c4b85be513dd029bade6abf0e7", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.21.1.tgz", "fileCount": 4, "integrity": "sha512-dljpsZ15RN4AxI958n9qO7sAv29FRuUMCB10CSDBGmDOW+oDDbNLs1k5/7MlYg5FXnZqznUSTtHBFHFyo1Rs2Q==", "signatures": [{"sig": "MEUCIQD/Zrd0emyVpBws4HJTfxxCEQaf4ESjZmNaKdQ+HVv55AIgF/RJHCYW5XfoW23KpmatweBo3WeiF/DP5BxcHDR6EZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7754659}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a36c105af3bb83370f64e28a8cf98b7dea52a6e0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.21.1_1687660722877_0.34047886949015593", "host": "s3://npm-registry-packages"}}, "1.21.2": {"name": "lightningcss-darwin-arm64", "version": "1.21.2", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.21.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "b7f846f196ce9e93ba379d83c4c82b633478477b", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.21.2.tgz", "fileCount": 4, "integrity": "sha512-uh2+MUWWc6rQpMfD3YoIRRxxRaUdhJSI3zeq7EXWQI4pDFcgV8LxGq6yfyMB0cJT1Hh55elQHM3Y139YJrhHFQ==", "signatures": [{"sig": "MEQCIDBTiAD0T0tSTUQFwKlV73GMkNuwhbobp87J7s7Qs50TAiBH0WxGLNKViHBC3ytAhAM1zwONzSeDzZt3zzA0KjJ2UA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7754675}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "e4963a58a813e02b47a7dd0320defcf0c4182512", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-arm64_1.21.2_1688266086325_0.001323972566795728", "host": "s3://npm-registry-packages"}}, "1.21.3": {"name": "lightningcss-darwin-arm64", "version": "1.21.3", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.21.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "db9ce035e4633098d60ced9702082df03a65370e", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.21.3.tgz", "fileCount": 4, "integrity": "sha512-s6ZUqUD1ZIIE0bYk/DrBhw1LAk4UGXoKDK/y2jU26jeIDYbsB3yDRi2y4zJ+4xccShzWucaQG4g0+NAAHL0soQ==", "signatures": [{"sig": "MEUCIDjrC+FhHGDbFz3sgnfm9FJLKj6execPOs0SNI+QHi5/AiEA3IASy04ToqPF+gdwN7XZ0DEreJJchJJn27pcQMlkrko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7754675}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "83bab71e80781b4d2b128f5938099b2f0c8239bd", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.21.3_1688397339380_0.18848957457840765", "host": "s3://npm-registry-packages"}}, "1.21.4": {"name": "lightningcss-darwin-arm64", "version": "1.21.4", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.21.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "90e5dd4fa371dd309aa5a2510db91a3c9cf55d50", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.21.4.tgz", "fileCount": 4, "integrity": "sha512-xqiS2QamPoecdTARl29JwjgrWYTlpQifKuIv40mEgpRaufC1oIPygVQ9P16tIYtZ1vyLBoWclAsbfsNFQnMv2A==", "signatures": [{"sig": "MEYCIQD3Gv8JmSG7GlEqwD7CU5qKgiiGZBWEr6paspqgz9IrTAIhAKg1791XTfnEfI35XXAKQ5GJnmVy7zjNXZ5dKmf1OJl1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7754675}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "bd517fde245dcce75a4b2eb21a25b147d54eb64a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.21.4_1688441858476_0.9372128327543681", "host": "s3://npm-registry-packages"}}, "1.21.5": {"name": "lightningcss-darwin-arm64", "version": "1.21.5", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.21.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "d4d2a235da382311779afd44936f05f0d470bf12", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.21.5.tgz", "fileCount": 4, "integrity": "sha512-z05hyLX85WY0UfhkFUOrWEFqD69lpVAmgl3aDzMKlIZJGygbhbegqb4PV8qfUrKKNBauut/qVNPKZglhTaDDxA==", "signatures": [{"sig": "MEUCIQDyfHvJMLlbuoJyGlLRFw/q/SOe5+KNqUQoL9sc6GZ20QIgaTPieZ31ffIbfK8CkKI2qAvK9Y/7KQ39JoxG0mez1Ew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7754675}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d44a73c3c000ccebbe8355b7cf9272236e573b4d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.21.5_1688531406055_0.5143331225092562", "host": "s3://npm-registry-packages"}}, "1.21.6": {"name": "lightningcss-darwin-arm64", "version": "1.21.6", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.21.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "4d22abd9e2c6000f3a14f16d2a220a0d6a0a9313", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.21.6.tgz", "fileCount": 4, "integrity": "sha512-NVPFGkn9o3Z06IpT9aO2OnlQObmVtjaHMwerpHbGUJpQiDOUC0EckK/+EUtVuUoz6r2nk3tUFBzE/q6q5nRzbw==", "signatures": [{"sig": "MEUCIQCP0ZWojH1urmw1Gm5FpaH8x8+TLJQkK2RrzawPjjzEogIgCC9xJ9jVJ+08nT/zAplvH/CPTKTKgFL8bK8uU3WgSKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7771187}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f485eb51d13e677a674cb10e170c71138446b71d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.21.6_1692511434485_0.05950509499720025", "host": "s3://npm-registry-packages"}}, "1.21.7": {"name": "lightningcss-darwin-arm64", "version": "1.21.7", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.21.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "0490d5fb8e0a38ec5e58e7c5fad12f7263f54311", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.21.7.tgz", "fileCount": 4, "integrity": "sha512-tt7hIsFio9jZofTVHtCACz6rB6c9RyABMXfA9A/VcKOjS3sq+koX/QkRJWY06utwOImbJIXBC5hbg9t3RkPUAQ==", "signatures": [{"sig": "MEUCIQCXwTlrl4CFuAB9MljG/fa83MFmL4Pab3MclL7dm28dLwIgEhOYJNAy+vW2nnEBzo+tXcEyibO3eTjfaeA4jqGXIxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7771187}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "393013928888d47ec7684d52ed79f758d371bd7b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.21.7_1692555034813_0.5266033738134459", "host": "s3://npm-registry-packages"}}, "1.21.8": {"name": "lightningcss-darwin-arm64", "version": "1.21.8", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.21.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "b4ea8d5133236bff361623ce8c30639a1b024240", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.21.8.tgz", "fileCount": 4, "integrity": "sha512-BOMoGfcgkk2f4ltzsJqmkjiqRtlZUK+UdwhR+P6VgIsnpQBV3G01mlL6GzYxYqxq+6/3/n/D+4oy2NeknmADZw==", "signatures": [{"sig": "MEUCIDDx/kXHjGQDvpJVbWiKupSZoGiEtVWzV87JLtjBQ3Y5AiEApKY4NnufTYJAblcRS8+UiV0C2ERxGtA8FlLLKUn6dRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7771187}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81cb0daee30dad924b14af7fbc739ddfe31d7f9e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.21.8_1694407615481_0.5125016533378406", "host": "s3://npm-registry-packages"}}, "1.22.0": {"name": "lightningcss-darwin-arm64", "version": "1.22.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.22.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "28e189ce15290b3d0ab43704fc33e8e6366e6df4", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.22.0.tgz", "fileCount": 4, "integrity": "sha512-aH2be3nNny+It5YEVm8tBSSdRlBVWQV8m2oJ7dESiYRzyY/E/bQUe2xlw5caaMuhlM9aoTMtOH25yzMhir0qPg==", "signatures": [{"sig": "MEUCIHG91Gbb5cXLWJCKDiFBZLyA8fRC21C8PxzE/Nlp3v1DAiEAjd8MMNpgd06Ofygn4/PzuPNxR0kkAH9IQqdlQCZv6B0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7771219}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "7ff93ca5c69ba9df415e1e2319d275e2cec249d7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-arm64_1.22.0_1694990926240_0.4275043360524122", "host": "s3://npm-registry-packages"}}, "1.22.1": {"name": "lightningcss-darwin-arm64", "version": "1.22.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.22.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "c03c042335fd7e9e1f45c977b39ff6886b8b064f", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.22.1.tgz", "fileCount": 4, "integrity": "sha512-ldvElu+R0QimNTjsKpaZkUv3zf+uefzLy/R1R19jtgOfSRM+zjUCUgDhfEDRmVqJtMwYsdhMI2aJtJChPC6Osg==", "signatures": [{"sig": "MEQCICPNy9Gmm2Sz3MV6YGhIkS596qbW+v6Vlbad8CxSdJuqAiBBt7SeJIxm4AUY/Pk+r/lLLIVh8nGqUMkgl5d+8ng0GA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7788115}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4994306f8f8d98a2b37433fced50efdacbbab901", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.18.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.22.1_1699395797486_0.532225811537504", "host": "s3://npm-registry-packages"}}, "1.23.0": {"name": "lightningcss-darwin-arm64", "version": "1.23.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.23.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "11780f37158a458cead5e89202f74cd99b926e36", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.23.0.tgz", "fileCount": 4, "integrity": "sha512-kl4Pk3Q2lnE6AJ7Qaij47KNEfY2/UXRZBT/zqGA24B8qwkgllr/j7rclKOf1axcslNXvvUdztjo4Xqh39Yq1aA==", "signatures": [{"sig": "MEUCIFI9EQae28+jZTmT3hAfZ0UdqMMpqIusGnybc6a2dl+gAiEAyhMe5aeNKP1qpV/N4skntu0uY+kq0V8qRBMxUghlzdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7821171}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b47f496a86075adcb6719aee3a8867e749a880b9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.23.0_1705276064587_0.6041010003151697", "host": "s3://npm-registry-packages"}}, "1.24.0": {"name": "lightningcss-darwin-arm64", "version": "1.24.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.24.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "3d2450fe6881ed84c825f91510415dfa35dc3237", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.24.0.tgz", "fileCount": 4, "integrity": "sha512-rTNPkEiynOu4CfGdd5ZfVOQe2gd2idfQd4EfX1l2ZUUwd+2SwSdbb7cG4rlwfnZckbzCAygm85xkpekRE5/wFw==", "signatures": [{"sig": "MEUCIHeFVPsCqTY04whMbJlxfKTu6ilhkdr+FFusGgb3SGcBAiEAr6aOE9qkDKe4SF/DcTy27Nq5MeEbs/Frb9QXhNRPsUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7936515}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "30b9d79b3d277dfb90d90d78cd9182ac755ad6cf", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-arm64_1.24.0_1708648876880_0.8571385131781146", "host": "s3://npm-registry-packages"}}, "1.24.1": {"name": "lightningcss-darwin-arm64", "version": "1.24.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.24.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "551735defa1e092ecf91244ca081f65f10ebd5f0", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.24.1.tgz", "fileCount": 4, "integrity": "sha512-1jQ12jBy+AE/73uGQWGSafK5GoWgmSiIQOGhSEXiFJSZxzV+OXIx+a9h2EYHxdJfX864M+2TAxWPWb0Vv+8y4w==", "signatures": [{"sig": "MEQCIGPK0rH/jkIdCgzdNOc2gAC0aEtsbzleB2eStbxtwNbAAiAyCeEHu3JToWxoiPVs9KRK1bvYkudjRScLG7O1jZWjng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7738883}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "baa1a2b7fa52eeb3827f8edcc2e14de33cd69ad0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.24.1_1710476565167_0.48104791926124535", "host": "s3://npm-registry-packages"}}, "1.25.0": {"name": "lightningcss-darwin-arm64", "version": "1.25.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.25.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "94c5290c5fa6974853c17d1f6cd448a7abe8bb31", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.25.0.tgz", "fileCount": 4, "integrity": "sha512-neCU5PrQUAec/b2mpXv13rrBWObQVaG/y0yhGKzAqN9cj7lOv13Wegnpiro0M66XAxx/cIkZfmJstRfriOR2SQ==", "signatures": [{"sig": "MEUCIE6k5DXl7Al4v7zu8vDUI5iHhppW0PQgaeU74MrHbM6nAiEAtm04x0Xtf0u15bIHfrf16zP3Yn9QZ7QS60t3Kjv5FeI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7854739}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81d21b9f5201c6eb8365fbb4fa81cbd947c3474f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.25.0_1715973745962_0.5925162201480021", "host": "s3://npm-registry-packages"}}, "1.25.1": {"name": "lightningcss-darwin-arm64", "version": "1.25.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.25.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "f2943d6dc1a4d331de0ff9ad54cd03cf10e0ead3", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.25.1.tgz", "fileCount": 4, "integrity": "sha512-G4Dcvv85bs5NLENcu/s1f7ehzE3D5ThnlWSDwE190tWXRQCQaqwcuHe+MGSVI/slm0XrxnaayXY+cNl3cSricw==", "signatures": [{"sig": "MEQCIBB3O18jpBe7Z+Y4r/2STgQ/6rgcVcT2HPoDnavQ5r03AiAuqhW3863KHJhccqXN0wU3GejyhTg/aCOz5r2k50hcOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7854755}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "fa6c015317e5cca29fa8a09b12d09ac53dcfbc77", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-arm64_1.25.1_1716617167058_0.5455110661432689", "host": "s3://npm-registry-packages"}}, "1.26.0": {"name": "lightningcss-darwin-arm64", "version": "1.26.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.26.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "1fda92d523dd1ef2fc8042d17153a5ded2de4726", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.26.0.tgz", "fileCount": 4, "integrity": "sha512-n4TIvHO1NY1ondKFYpL2ZX0bcC2y6yjXMD6JfyizgR8BCFNEeArINDzEaeqlfX9bXz73Bpz/Ow0nu+1qiDrBKg==", "signatures": [{"sig": "MEQCIFtSJ1Qp/KhlyTUatIDmUNj++D551iNarn4wwuu3Qvx/AiBeglifcfRWjHJJpL52EWxhbYc+Kh/pGA6Bqk+J7saCHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7772291}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "0bcd896e81a8a2c5d847f626a37e2cffea79e2a0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.26.0_1722958356121_0.7437339476076623", "host": "s3://npm-registry-packages"}}, "1.27.0": {"name": "lightningcss-darwin-arm64", "version": "1.27.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.27.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "565bd610533941cba648a70e105987578d82f996", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.27.0.tgz", "fileCount": 4, "integrity": "sha512-Gl/lqIXY+d+ySmMbgDf0pgaWSqrWYxVHoc88q+Vhf2YNzZ8DwoRzGt5NZDVqqIW5ScpSnmmjcgXP87Dn2ylSSQ==", "signatures": [{"sig": "MEUCIQDu6UJSKnzruO96XlTaJ671FUKhymNSpUgafoJIl/QcHgIgDU2GbCZY0vgpf6o4WnrkqQWVELJ+twRGLD8vUHTi3ps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7788803}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb49015cf887ae720b80a2856ccbdf61bf940ef1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-arm64_1.27.0_1726023630750_0.10817289263054941", "host": "s3://npm-registry-packages"}}, "1.28.0": {"name": "lightningcss-darwin-arm64", "version": "1.28.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.28.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "67251c6af8ac9ceec0265b809d9b9772971d562d", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.28.0.tgz", "fileCount": 4, "integrity": "sha512-ED44EhfHU1VVWzkHEX3j5xR5LHEumiKPTqT3qtKldT97BHZGZBTY5NYQ6FXgY/nkFAu2AFrJ9/82ncwY23DLMQ==", "signatures": [{"sig": "MEUCIDMH0OpTmxrUpzMwNUEwT3ulnsqjumRuOF5CNzwy9k9hAiEA98fFG74o++L8pjLk8snIZ1LLCDZtgAiQyiqDGGOonjY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7788856}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "8a67583105757e4a25378d65d243b87a345b2c2d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.28.0_1730668648648_0.740234321098346", "host": "s3://npm-registry-packages"}}, "1.28.1": {"name": "lightningcss-darwin-arm64", "version": "1.28.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.28.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "043c30e2d22ee68beb7f8782e96390821ba8ab34", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.28.1.tgz", "fileCount": 4, "integrity": "sha512-VG3vvzM0m/rguCdm76DdobNeNJnHK+jWcdkNLFWHLh9YCotRvbRIt45JxwcHlIF8TDqWStVLTdghq5NaigVCBQ==", "signatures": [{"sig": "MEUCIEjsk0eNeL9MkwgMKejBJBqHCV7CMPbWrBmIdyzR7/XjAiEAoat2uAaDZN7dP52f1ZtUKj/x7ZOfZ/b5dJyjTG8NHsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7788856}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a3390fd4140ca87f5035595d22bc9357cf72177e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.28.1_1730674688031_0.7718640727566115", "host": "s3://npm-registry-packages"}}, "1.28.2": {"name": "lightningcss-darwin-arm64", "version": "1.28.2", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.28.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "a906fd84cb43d753cb5db9c367f8f38482e8fb03", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.28.2.tgz", "fileCount": 4, "integrity": "sha512-/8cPSqZiusHSS+WQz0W4NuaqFjquys1x+NsdN/XOHb+idGHJSoJ7SoQTVl3DZuAgtPZwFZgRfb/vd1oi8uX6+g==", "signatures": [{"sig": "MEUCIQD3YCsGY2yiOK+EtpDvRTFLZJRXrDG1t72+nNhXZDRs4gIgX5/FvaNCoecfIqjROPjH4pZqro7jwqxljgkzQ/LqaSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7788856}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "9b2e8bbe732d7c101272ddab03ac21b88bf55c4a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.5", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.28.2_1732512276998_0.8811456842401015", "host": "s3://npm-registry-packages"}}, "1.29.0": {"name": "lightningcss-darwin-arm64", "version": "1.29.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.29.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "3ce257896ddbec324dc264b211b8bdcdc23e70d8", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.29.0.tgz", "fileCount": 4, "integrity": "sha512-oxLEuI5qICT1FanwPm1U3CTKl4WljMOPuSJiibE6i4GYQgzH3BNFYF/xBhuLDvbJER+wwtuhWD2ir695HUssRg==", "signatures": [{"sig": "MEUCIQDDsSWudH/EcxQvC33nRbZf1Junl1C2mfXcIeT2VvUdgAIgd53hb3pEgjuuYwJf48LrM2ibCAJsHx/D102pZwqUEX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7704136}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "222fa9f7ad8b4004afd7a30c2f99cab3c8621191", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.29.0_1736401648906_0.4518870194534874", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.1": {"name": "lightningcss-darwin-arm64", "version": "1.29.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.29.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "dce17349c7b9f968f396ec240503de14e7b4870b", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.29.1.tgz", "fileCount": 4, "integrity": "sha512-HtR5XJ5A0lvCqYAoSv2QdZZyoHNttBpa5EP9aNuzBQeKGfbyH5+UipLWvVzpP4Uml5ej4BYs5I9Lco9u1fECqw==", "signatures": [{"sig": "MEQCIFWuIyPGyWM+GyneB5JWTGAl9xlr/ZaMDOoUlXdIdL8EAiAgGJvikxs5XBYc64v5Gw1yMpasxYa954FRJ95rKadplg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7704136}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b10f9baf8878411bf2b09dfe8d64ba09ef7a4eac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.29.1_1736445744056_0.48597051965384774", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.2": {"name": "lightningcss-darwin-arm64", "version": "1.29.2", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.29.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "6ceff38b01134af48e859394e1ca21e5d49faae6", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.29.2.tgz", "fileCount": 4, "integrity": "sha512-cK/eMabSViKn/PG8U/a7aCorpeKLMlK0bQeNHmdb7qUnBkNPnL+oV5DjJUo0kqWsJUapZsM4jCfYItbqBDvlcA==", "signatures": [{"sig": "MEUCIF75s/OZPLv0YKc1xcNvMTG+wb76hpHM0uLgZIOsGqPfAiEAkv3zsIweWyMoCrOysD3JlYIPpr1Xa2ya9oYGP8tEYW8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7720627}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f2303df29448a55c5f4f284f747eb53760769fe4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.29.2_1741242104346_0.329983963769106", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.3": {"name": "lightningcss-darwin-arm64", "version": "1.29.3", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.29.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "5a70c0518bbc5b48f229a85ec07d2aca0a19b5a6", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.29.3.tgz", "fileCount": 4, "integrity": "sha512-fb7raKO3pXtlNbQbiMeEu8RbBVHnpyqAoxTyTRMEWFQWmscGC2wZxoHzZ+YKAepUuKT9uIW5vL2QbFivTgprZg==", "signatures": [{"sig": "MEUCIQDG0QDtXeaGaTUWgg+ZKonosL+jWTnD+CD+9GMzT/T4lQIgTSrLkfDVQnxxLEfTHcqqbvvnDnqJZm1iZ7rhimaCd8E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7720627}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "80eb8617c5f8f5519ed85bd3eb6d8953f4d32493", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.29.3_1741974580601_0.6790990641178221", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.0": {"name": "lightningcss-darwin-arm64", "version": "1.30.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-arm64@1.30.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["arm64"], "dist": {"shasum": "2e8925fc86a6f8568351680524cade9406b0c6c8", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.30.0.tgz", "fileCount": 4, "integrity": "sha512-L9lhvW4rTHL6vaG1WU3Itj0ivtdBuwu7ufrKEbijRCPhS1pt1haXEXI8h9g73qCQsOaYs1GCc9chvSgxPmhpRA==", "signatures": [{"sig": "MEYCIQDqggrze/d3Zdl+EjTzFJMBBhATMmgMR23/U/7jt7lNPAIhAOhlssdxOXdFI6aITy6Wv8tmHR/hY5Kg7MNZt9XhRP3s", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7753699}, "main": "lightningcss.darwin-arm64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "474c67c046b4a54217166057296fa4ca28764858", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.30.0_1746945539578_0.31757266019047803", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.1": {"name": "lightningcss-darwin-arm64", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.darwin-arm64.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "resolutions": {"lightningcss": "link:."}, "os": ["darwin"], "cpu": ["arm64"], "_id": "lightningcss-darwin-arm64@1.30.1", "gitHead": "f2dc67c4d3fe92f26693c02366db1e60cae0db27", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-c8JK7hyE65X1MHMN+Viq9n11RRC7hgin3HhYKhrMyaXflk5GVplZ60IxyoVtzILeKr+xAJwg6zK6sjTBJ0FKYQ==", "shasum": "3d47ce5e221b9567c703950edf2529ca4a3700ae", "tarball": "https://registry.npmjs.org/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.30.1.tgz", "fileCount": 4, "unpackedSize": 7753699, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCBeMoBEF3Dbht5ViZ8B8PgFwYtPZ1WPSDxIeKYZoXF6QIgJGTPRBwOvlp2N+7W9Jyj2EPFVuez+4aPTxJlZMhwT84="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss-dar<PERSON>-arm64_1.30.1_1747193908110_0.08144046048735087"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-09-08T05:19:02.047Z", "modified": "2025-05-14T03:38:28.514Z", "1.14.0": "2022-09-08T05:19:02.307Z", "1.15.0": "2022-09-15T04:16:22.434Z", "1.15.1": "2022-09-16T03:49:12.490Z", "1.16.0": "2022-09-20T03:23:02.776Z", "1.16.1": "2022-11-06T18:01:56.798Z", "1.17.0": "2022-11-29T16:47:36.240Z", "1.17.1": "2022-11-30T17:30:21.890Z", "1.18.0": "2023-01-04T17:00:03.106Z", "1.19.0": "2023-02-13T15:58:57.148Z", "1.20.0": "2023-04-19T21:59:18.614Z", "1.21.0": "2023-06-07T05:58:06.877Z", "1.21.1": "2023-06-25T02:38:43.169Z", "1.21.2": "2023-07-02T02:48:06.605Z", "1.21.3": "2023-07-03T15:15:39.663Z", "1.21.4": "2023-07-04T03:37:38.755Z", "1.21.5": "2023-07-05T04:30:06.357Z", "1.21.6": "2023-08-20T06:03:54.830Z", "1.21.7": "2023-08-20T18:10:35.131Z", "1.21.8": "2023-09-11T04:46:55.771Z", "1.22.0": "2023-09-17T22:48:46.561Z", "1.22.1": "2023-11-07T22:23:17.840Z", "1.23.0": "2024-01-14T23:47:44.860Z", "1.24.0": "2024-02-23T00:41:17.211Z", "1.24.1": "2024-03-15T04:22:45.401Z", "1.25.0": "2024-05-17T19:22:26.257Z", "1.25.1": "2024-05-25T06:06:07.391Z", "1.26.0": "2024-08-06T15:32:36.309Z", "1.27.0": "2024-09-11T03:00:31.113Z", "1.28.0": "2024-11-03T21:17:28.924Z", "1.28.1": "2024-11-03T22:58:08.337Z", "1.28.2": "2024-11-25T05:24:37.241Z", "1.29.0": "2025-01-09T05:47:29.149Z", "1.29.1": "2025-01-09T18:02:24.503Z", "1.29.2": "2025-03-06T06:21:44.555Z", "1.29.3": "2025-03-14T17:49:40.850Z", "1.30.0": "2025-05-11T06:38:59.836Z", "1.30.1": "2025-05-14T03:38:28.329Z"}, "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "license": "MPL-2.0", "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "description": "A CSS parser, transformer, and minifier written in Rust", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "This is the aarch64-apple-darwin build of lightningcss. See https://github.com/parcel-bundler/lightningcss for details.", "readmeFilename": "README.md"}