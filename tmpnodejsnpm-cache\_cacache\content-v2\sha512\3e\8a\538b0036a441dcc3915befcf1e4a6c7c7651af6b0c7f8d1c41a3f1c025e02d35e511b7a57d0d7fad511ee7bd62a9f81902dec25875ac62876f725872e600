{"_id": "@rollup/rollup-linux-powerpc64le-gnu", "_rev": "93-80e11e83557cdac69066a06d09a9e2e4", "name": "@rollup/rollup-linux-powerpc64le-gnu", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.2"}, "versions": {"4.13.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64le"], "dist": {"shasum": "46b2463d94ac3af3e0f7a2947b695397bc13b755", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-zvXvAUGGEYi6tYhcDmb9wlOckVbuD+7z3mzInCSTACJ4DQrdSLPNUeDIcAQW39M3q6PDquqLWu7pnO39uSMRzQ==", "signatures": [{"sig": "MEUCIQDpn0nYJrjp/HYPiQhPu5tQIey/qjrqqjwvXiL2zkKXzQIgNAQWvdTnGt/9n6ysVJV3nVDoo+7wnf39iFhobH4wFhk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3022473}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.13.2_1711635234400_0.6526493069817156", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64le"], "dist": {"shasum": "6a431c441420d1c510a205e08c6673355a0a2ea9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-xV0d5jDb4aFu84XKr+lcUJ9y3qpIWhttO3Qev97z8DKLXR62LC3cXT/bMZXrjLF9X+P5oSmJTzAhqwUbY96PnA==", "signatures": [{"sig": "MEUCIQDaaBHDYYg9uomkP2OInt+snmyoPA2g68bzfMaRQzY7fAIgHZcNVTikN8paCsO7WyHs+wo7N+4YfOMjQPvnV26hQRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3022473}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.14.0_1712121790077_0.008399535785597934", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64le"], "dist": {"shasum": "024ad04d162726f25e62915851f7df69a9677c17", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-im7HE4VBL+aDswvcmfx88Mp1soqL9OBsdDBU8NqDEYtkri0qV0THhQsvZtZeNNlLeCUQ16PZyv7cqutjDF35qw==", "signatures": [{"sig": "MEUCIBakBOrPnwqvTFwML/McN+J+K2nnP0DFQyRl49AQHU7MAiEA1x0wMKhHDQbFNHcDZu/cZzF3ikbo8VzNPKcvVgnO9sE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3022473}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.14.1_1712475354212_0.7019491312446016", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "71bf99c8017476ac85b09d21b3fa2eacbad96100", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-4RyT6v1kXb7C0fn6zV33rvaX05P0zHoNzaXI/5oFHklfKm602j+N4mn2YvoezQViRLPnxP8M1NaY4s/5kXO5cw==", "signatures": [{"sig": "MEUCIQC9hg4/EF5VAtCKwLyE9iMxBofnJ+3mtKS84or4Tuk0ggIgIShcuiqcnHIw7Om9MN1ZqeOUvhGS+XPefsBdXNGwZWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3022471}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.14.2_1712903035229_0.9090983779190087", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "494ba3b31095e9a45df9c3f646d21400fb631a95", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-5aRjvsS8q1nWN8AoRfrq5+9IflC3P1leMoy4r2WjXyFqf3qcqsxRCfxtZIV58tCxd+Yv7WELPcO9mY9aeQyAmw==", "signatures": [{"sig": "MEYCIQCyVJqmaGcDjK7Rkt4wDcz8GMtB7JdDyBWiXPbQ458PZQIhAP+27gh3dQYliDwIdhJiEpgEQsnDPzjA0ItSJS5Kwgt4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3022471}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.14.3_1713165528454_0.048370906836556316", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "6adf69ce27d1266dbb86eeac237ad5dd4d4a1f28", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-1cUSvYgnyTakM4FDyf/GxUCDcqmj/hUh1NOizEOJU7+D5xEfFGCxgcNOs3hYBeRMUCcGmGkt01EhD3ILgKpGHQ==", "signatures": [{"sig": "MEQCIG2vQbOY5FpCcvX5lut/DKOByiBF0kAYS7psyY3nqtD4AiAbwBY2uZ5Qepmxilsd+y6W5u3qV/lzSvm8/mfNr6BjrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3088007}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.15.0_1713591453006_0.8447432394748524", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "d7a0b85cfca2aaf3adbf752569183b21233ac1de", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-FSuFy4/hOQy0lH135ifnElP/6dKoHcZGHovsaRY0jrfNRR2yjMnVYaqNHKGKy0b/1I8DkD/JtclgJfq7SPti1w==", "signatures": [{"sig": "MEQCIGpRc56Pe71VXYBTZwsfCC0JYmA6oeB+x6zat8rAEpTvAiBAt5ZVWLJ5QtXYPZE1rL5EePbws/1q9nsr+PmP96ew1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3088007}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.16.0_1713674558643_0.8797504451226923", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "5de8b20105aaaeb36eb86fab0a1020d81c7bd4d5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-/gsNwtiGLqYwN4vP+EIdUC6Q6LTlpupWqokqIndvZcjn9ig/5P01WyaYCU2wvfL/2Z82jp5kX8c1mDBOvCP3zg==", "signatures": [{"sig": "MEYCIQD/qbSEF1LDjdnJNx2dGZJPXmHy4zsAhY9PZby0zSNqKQIhANkdadY19lepxWNpKc6YLgVkM5Zd9P4oOm0/fZ3xDem4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3088007}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.16.1_1713724221080_0.04455657479746589", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "7154fe9ffc6405b2a6555ca931c42c0aa5198c2a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-LjMMFiVBRL3wOe095vHAekL4b7nQqf4KZEpdMWd3/W+nIy5o9q/8tlVKiqMbfieDypNXLsxM9fexOxd9Qcklyg==", "signatures": [{"sig": "MEYCIQCX05P1Nvnhe2blFXnLvf8cVTkRDGb7uAQhDE0OyiE7QAIhAMGCLglzzdKomhV4qE/xBoh7mewd7+ivT6cvnWU7z169", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3088007}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.16.2_1713799179103_0.09659307980526255", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "3e2da7d5aace0f949d709f7bf09524927bd484d2", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-4XGexJthsNhEEgv/zK4/NnAOjYKoeCsIoT+GkqTY2u3rse0lbJ8ft1bpDCdlkvifsLDL2uwe4fn8PLR4IMTKQQ==", "signatures": [{"sig": "MEYCIQDx7FR2Qge3NSmKURVdw7R2uMbM//fyWcd8OvYHsjAYxAIhAJjfcRrEl+EgbVsUxNYHwcUE14VJViQ5CCsTg+Z3QL2O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3088007}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.16.3_1713849170932_0.11693313560184104", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "8c69218b6de05ee2ba211664a2d2ac1e54e43f94", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-p8C3NnxXooRdNrdv6dBmRTddEapfESEUflpICDNKXpHvTjRRq1J82CbU5G3XfebIZyI3B0s074JHMWD36qOW6w==", "signatures": [{"sig": "MEYCIQCnkTglX/5kRgAxYGmZDFeTlJFIJSavTYL0I+nIj64pbQIhAI9F3+L30p1swY7UjYbxq/DYQD30WhrSW+1fLBYM4jQD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3088007}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.16.4_1713878123746_0.5658476260046412", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "ce19c505ef6ce2c83df74b42824d95d38069cc2b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-7F99yzVT67B7IUNMjLD9QCFDCyHkyCJMS1dywZrGgVFJao4VJ9szrIEgH67cR+bXQgEaY01ur/WSL6B0jtcLyA==", "signatures": [{"sig": "MEUCIEhUGTVRdMeXFx6mdWIrkXatYFDpE+m0xxrGoa4CD+zJAiEA65ZoBzOYobCWSwv2B0TXbcSk29FqD8ebXPyiRDJAbtg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3153543}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.17.0_1714217409196_0.25685487172456", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "35b5af3ded0b20dd7cc00813d96f9823b321d2ea", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-UGV0dUo/xCv4pkr/C8KY7XLFwBNnvladt8q+VmdKrw/3RUd3rD0TptwjisvE2TTnnlENtuY4/PZuoOYRiGp8Gw==", "signatures": [{"sig": "MEYCIQDvqKI8ct9A522PuVYMdHXLGLWrcm6P4ojjisttFJZPvQIhAJzZeB6Aq/nybFVOiOJXblLKvzpXmiuNveZaxthn8pYB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3153543}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.17.1_1714366690963_0.3627312403117655", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "94e69a8499b5cf368911b83a44bb230782aeb571", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-T19My13y8uYXPw/L/k0JYaX1fJKFT/PWdXiHr8mTbXWxjVF1t+8Xl31DgBBvEKclw+1b00Chg0hxE2O7bTG7GQ==", "signatures": [{"sig": "MEUCIBw5NVywpGStXMOW2L56TOuA50xnm3YgoJFYjhooYvvIAiEAo+zrWvCzG+FNeM5bDUzb5s5SAZfnVMJsFS9mFZlKWC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3153543}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.17.2_1714453267176_0.3591428118814455", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "cbb0837408fe081ce3435cf3730e090febafc9bf", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-hNVMQK+qrA9Todu9+wqrXOHxFiD5YmdEi3paj6vP02Kx1hjd2LLYR2eaN7DsEshg09+9uzWi2W18MJDlG0cxJA==", "signatures": [{"sig": "MEYCIQD7l4teARK40OwuzsC2i7DY0hHiHp34sWlYz4bOzoEViQIhAPkKUOzm3y4sp2u17AvlPMs5VWFXAkYG6t7//WUzRvCw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3153543}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.18.0_1716354242935_0.4514881187603774", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "b1194e5ed6d138fdde0842d126fccde74a90f457", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-V72cXdTl4EI0x6FNmho4D502sy7ed+LuVW6Ym8aI6DRQ9hQZdp5sj0a2usYOlqvFBNKQnLQGwmYnujo2HvjCxQ==", "signatures": [{"sig": "MEYCIQCi2bvuBD0ou75ZikG/BJWGUMZLSgKPg0as9ZTwWaOb0gIhALpbdmCvQF1Dd6Q/3QPD7Ah2pMZhEbooPI+nL/9+ejJb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2956967}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.18.1_1720452330975_0.6733891630454603", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "1540b284d91c440bc9fa7a1714cfb71a5597e94d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-N6cFJzssruDLUOKfEKeovCKiHcdwVYOT1Hs6dovDQ61+Y9n3Ek4zXvtghPPelt6U0AH4aDGnDLb83uiJMkWYzQ==", "signatures": [{"sig": "MEYCIQD1uXD7vwI928+HZ6tiYQM9taNJPs5XhaGrdUO4oRDBQwIhAKzBKMq2I5jTKe1doiS+NdtFu5h3g1gw1qntUsESGlGS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2891431}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.19.0_1721454392953_0.5989813591128315", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "3f99a0921596a6f539121a312df29af52a205f15", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-PromGeV50sq+YfaisG8W3fd+Cl6mnOOiNv2qKKqKCpiiEke2KiKVyDqG/Mb9GWKbYMHj5a01fq/qlUR28PFhCQ==", "signatures": [{"sig": "MEUCIQCnvQh/1eYPknE3e2vP3b3RahyDFVwlHYDJ5oajTC753gIgOQ3DKG2YYHettynC+bFRW3/oqYyJHa574VhAS7P/mLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2825895}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.19.1_1722056058413_0.05767256027538048", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "4df9be1396ea9eb0ca99fd0f2e858008d7f063e3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-rhjvoPBhBwVnJRq/+hi2Q3EMiVF538/o9dBuj9TVLclo9DuONqt5xfWSaE6MYiFKpo/lFPJ/iSI72rYWw5Hc7w==", "signatures": [{"sig": "MEUCIFG0GotmCMbMIOkm9CiMDf3BM/W65jogGN3ZIwAOVGpFAiEA7v+BUTE2F10ysMGjqKECnU0RjwoAwB5IIGAG1Y7nFhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2825895}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.19.2_1722501191850_0.9528736650827978", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "8837e858f53c84607f05ad0602943e96d104c6b4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-yAMvqhPfGKsAxHN8I4+jE0CpLWD8cv4z7CK7BMmhjDuz606Q2tFKkWRY8bHR9JQXYcoLfopo5TTqzxgPUjUMfw==", "signatures": [{"sig": "MEYCIQCkpum4/W3vJz3m4K8QtO8bTeHWsUegsuZ2rA31BkxQYAIhAJRFIyoy869WCumxM+brNdsRoTN1nPCjh7HyBgTPCFrz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2825895}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.20.0_1722660549395_0.10260978424270761", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "493c5e19e395cf3c6bd860c7139c8a903dea72b4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-ZKPan1/RvAhrUylwBXC9t7B2hXdpb/ufeu22pG2psV7RN8roOfGurEghw1ySmX/CmDDHNTDDjY3lo9hRlgtaHg==", "signatures": [{"sig": "MEQCIG/aHkmdbu/OlglLk4e4ScaCmi1VR+1UOCf8YOZyMoflAiAdMae1ETATMtA5Xdpbt3BtA77aYEfQmlCiW6on/dI+6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.21.0_1723960551037_0.8118999367958846", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "ee3810647faf2c105a5a4e71260bb90b96bf87bc", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-ltUWy+sHeAh3YZ91NUsV4Xg3uBXAlscQe8ZOXRCVAKLsivGuJsrkawYPUEyCV3DYa9urgJugMLn8Z3Z/6CeyRQ==", "signatures": [{"sig": "MEUCIEuzV5qwOj/Ufu2+JjPN7vASlFp7wTPlxHhU+VGMUWKxAiEA6FmkOgDkT78XHSXGC81TsoJeri/HOr1lVmpAL3PQd9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.21.1_1724687672492_0.6273595480469774", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "24b3457e75ee9ae5b1c198bd39eea53222a74e54", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-cZdyuInj0ofc7mAQpKcPR2a2iu4YM4FQfuUzCVA2u4HI95lCwzjoPtdWjdpDKyHxI0UO82bLDoOaLfpZ/wviyQ==", "signatures": [{"sig": "MEUCIQDis+3gSAydX2oVOl7zwj7hiriKc2/WPwQy8Op3/tbG4QIgNxNbIwvJ4S5gs6IDPuncnVkGfbvA14YTQHjovK9zpMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.21.2_1725001483910_0.27153519007531624", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "6e6e1f9404c9bf3fbd7d51cd11cd288a9a2843aa", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-+W+p/9QNDr2vE2AXU0qIy0qQE75E8RTwTwgqS2G5CRQ11vzq0tbnfBd6brWhS9bCRjAjepJe2fvvkvS3dno+iw==", "signatures": [{"sig": "MEYCIQCKUxNvJItzkT0HbWYVr6WjaCAEV8VDzknjjYX8HWSU7wIhALUeaOTFk5Vc/xaGOHLxrZzaLcck9L2tJsq/CIooDRYh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.21.3_1726124768836_0.8585037073428754", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "fdd173929a5bba8b7e8b37314380213d9604088f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-9fx6Zj/7vve/Fp4iexUFRKb5+RjLCff6YTRQl4CoDhdMfDoobWmhAxQWV3NfShMzQk1Q/iCnageFyGfqnsmeqQ==", "signatures": [{"sig": "MEUCICkOOtipEGBRq6xaiQKJ95Xr9awKEQVDxoC2AT/cVFl+AiEAmDYfPD51vBjDJq+QJ9DzVwmRt3Un5QHqaqAT9xILcjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.22.0_1726721749882_0.6512956513741985", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "2f8f4eca06d54ad4f96a0bbbd1dc67913a31b0ae", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-6psD9nKw+wLj9bMhArTkzKt5etA6kb+cBJQws4MovI9gQSRkdX4nyYZofBfgTtaZtymQl7uRfe1I75guePal5A==", "signatures": [{"sig": "MEUCIFYJnNxtgoePCXz5V+TJOgjj06bZ9AFqqRilx+3DTUXgAiEAlc7tn05VYBWMJFx0uR0cCJ77OY96eIPFNUnLDciCczw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.22.1_1726820531315_0.38955102171630873", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "03a67f1476dd80f115ce35bc9b0d03c50c16679d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-HpJCMnlMTfEhwo19bajvdraQMcAq3FX08QDx3OfQgb+414xZhKNf3jNvLFYKbbDSGBBrQh5yNwWZrdK0g0pokg==", "signatures": [{"sig": "MEUCIQDHH+9o0aDrsoiC4p+8/4ioUFXqELYVfioXHgQvlMigKwIgbXGF5GS9ScFfD/Ti4PaX6d0f46zJ8ubaPJnYYoePHOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.22.2_1726824843538_0.2851652926280006", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "32acf1bf88683a1376458206dcdf81ced17ec2dd", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-Y/2opYifm0Thjdh6+7nCuNu/CcBldVJogvC7mdvH+GbwCTG9D10cyIaWT3CKTAeJB0EjchEBpI/aRsFcDrxTKw==", "signatures": [{"sig": "MEUCIQCQ/wa5ZPos2fyOALpGRguSBqSAADRKqZ66FR2A1yo90wIgZVHArgCQUzTcga+eGSE+yONDmswX144Y6DEFPdpnh/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.22.3-0_1726843697815_0.5077612429718554", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "d2a174a0576c30cdd4f0c17d3bf76081cddcd859", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-QJTk7pEUN1T1OGIkHbfeQd/+wthw+kmXrmNZUrDvDznKxgpgS+Uqbi+egux6UdEXuuaxhavm5L+aotbTuWqqaQ==", "signatures": [{"sig": "MEYCIQCvTcxEmmBWB3CUTZtlk43s09nKwHUKrcrG6VF/DiW0wQIhANeADJT6bIMmesmTC/SN5KoMSFHjtYA+0dVhDuUEO9oG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.22.3_1726895008668_0.5481265024137985", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "9a79ae6c9e9d8fe83d49e2712ecf4302db5bef5e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-3aVCK9xfWW1oGQpTsYJJPF6bfpWfhbRnhdlyhak2ZiyFLDaayz0EP5j9V1RVLAAxlmWKTDfS9wyRyY3hvhPoOg==", "signatures": [{"sig": "MEQCICIHYSMSxG650A+0KG1bH8cst793dVtjVQ/D3LVp6AOUAiAINQfOFER2AXt1WDRvN1Jt9VqsE+I6kUI4hF4GFGzL5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.22.4_1726899099217_0.29587398946127386", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "5661420dc463bec31ecb2d17d113de858cfcfe2d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-TMYu+DUdNlgBXING13rHSfUc3Ky5nLPbWs4bFnT+R6Vu3OvXkTkixvvBKk8uO4MT5Ab6lC3U7x8S8El2q5o56w==", "signatures": [{"sig": "MEYCIQCIMrT4n/EeiH4Auwmns9h1xYiHfOe/NTfPmMUBOAIikgIhAPei+QS1a7h6XJ237Ef4BG7raH0mjMMJn0oz24wC1upm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.22.5_1727437715522_0.9084539225959705", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "2ce0518e709a8a4c0ae563ae0dd4526bc8b14df8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-Sefl6vPyn5axzCsO13r1sHLcmPuiSOrKIImnq34CBurntcJ+lkQgAaTt/9JkgGmaZJ+OkaHmAJl4Bfd0DmdtOQ==", "signatures": [{"sig": "MEUCIDz7L+RUyk8qD5SHyS2pxGtOUtB56LuJrBt9BjJGF11PAiEA/zKSLBnp7Lpp1qhngOxTPgLPZbJcGOlkdprNyYfVUCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.23.0_1727766636367_0.08015968950208219", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "5b98729628d5bcc8f7f37b58b04d6845f85c7b5d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-2XFFPJ2XMEiF5Zi2EBf4h73oR1V/lycirxZxHZNc93SqDN/IWhYYSYj8I9381ikUFXZrz2v7r2tOVk2NBwxrWw==", "signatures": [{"sig": "MEQCICS23XqqlA5fVEMm0dq0g+EzrZAbRzhrWePr5iiP79NdAiBsIrHGNeFns9xotA2ddhUQcLeGxg7uq/KJuvecYk7Rzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.24.0_1727861860201_0.5200226038497446", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "51505cbbb1827b0eefadedb0a4b1b64b6c7000df", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-h9ipTGhMzTBAJL/bg1HsElhGPWLGeCKE8JkxgvrJ5O/S1MXH9RxMUTl++tzlpzxdOBCAGqygZIMBj3wIDf/kJw==", "signatures": [{"sig": "MEQCIBH1iw7kra678MJHIiz6EP6/RIfuwkbVvy4vYwWoqXWeAiBKh1jvc7jLAfcNTglLwb5mGysi6+ZddDvf5/GQlC9dGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.24.1_1730011404660_0.9817343042512352", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "358e3e7dda2d60c46ff7c74f7075045736df5b50", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-xv9vS648T3X4AxFFZGWeB5Dou8ilsv4VVqJ0+loOIgDO20zIhYfDLkk5xoQiej2RiSQkld9ijF/fhLeonrz2mw==", "signatures": [{"sig": "MEUCIQDqlFouM0MMQbiImxBsb8g6CjmNNnWC9rIeWcLEIzjYGgIgPWpkrypPu4ReVokusQMS9jhS84HTPe1QoM/VvOY+dl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.24.2_1730043631759_0.34040726904927254", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "72f882b55c29e1540da0b4756e82ba55f737b5b3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-jV4NXjYy9KNfQ9M/jydura+AH8uzHybidvATWw5Tf0tYojXsjwexLQYllcsMLoXG0HVzKvraUyZ45OHW5i1cNA==", "signatures": [{"sig": "MEYCIQC+thykKZTdDMzEEDT+dsFFz/WTBAQXiSvEc7a/j1cSLgIhAKP5/VUdd/L2ggudxNkQQDDSugEYGz2VVq2Xie2blCcH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.25.0-0_1730182534313_0.6126947809590757", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "4d32ce982e2d25e3b8116336ad5ce6e270b5a024", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-yNaWw+GAO8JjVx3s3cMeG5Esz1cKVzz8PkTJSfYzE5u7A+NvGmbVFEHP+BikTIyYWuz0+DX9kaA3pH9Sqxp69g==", "signatures": [{"sig": "MEQCIAi6+5Iv+/p0rqCq4AwW1tn1JDh3xFugw2aV2uqaQb99AiAi7POc8gccnGkg/WoxJK+gijJ+4EZnbz6sxbOzfYbnow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.24.3_1730211272256_0.501068057167275", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "a9418a4173df80848c0d47df0426a0bf183c4e75", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-q73XUPnkwt9ZNF2xRS4fvneSuaHw2BXuV5rI4cw0fWYVIWIBeDZX7c7FWhFQPNTnE24172K30I+dViWRVD9TwA==", "signatures": [{"sig": "MEUCIQD72IkloILSuctmw6Z8JlVyQU5AYOWFExNEUZTnIICLuQIgG9rG3S5Kq9rAeouEhMoINvVr8hrn6UpVPKx8xhwL51E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.24.4_1730710051534_0.05835614926416066", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "52f4b39e6783505d168a745b79d86474fde71680", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-BVSQvVa2v5hKwJSy6X7W1fjDex6yZnNKy3Kx1JGimccHft6HV0THTwNtC2zawtNXKUu+S5CjXslilYdKBAadzA==", "signatures": [{"sig": "MEQCIHcB+FILRlb87Fb1GoISTkSgaGk1H6pxgN+RN/E3J+KtAiACQ+uoBR85mft40KKhs1yuKJaFEkLffsFYdWdTZ8f+2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.25.0_1731141465534_0.4312133799903999", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "21aed3ef42518b7fe33f4037a14b0939a071cf75", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-Odp/lgHbW/mAqw/pU21goo5ruWsytP7/HCC/liOt0zcGG0llYWKrd10k9Fj0pdj3prQ63N5yQLCLiE7HTX+MYw==", "signatures": [{"sig": "MEUCIQDkfIX0N5uxqaAjZn/V9xXCTrXH5HrAdRuqEsoMw8f36QIgNC3nVoOYGHjxZT6nWXg+YxhvuM4M1f8H/8XZYkkYnR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.26.0_1731480322068_0.08900164068509953", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "56fea6a18382ac9488c65b15d8a80e247a0a9974", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-ygnI+UIk08nlSrJbjrwIgMeolc5jDs4HiwD8y0Fv64JZaYBzkC2EzFSaCiR+/6qjVIKbFLBsBdDz6ocgDcLtDg==", "signatures": [{"sig": "MEUCIQC7LdO6++gUK1b3SbHBTAwKIJsU6NQovR6f0YOqgFTzWQIgEI34/7LjvtbNB+8hJPNqHo/l9rp2fYFphpbTxsUoh4g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.27.0-0_1731481415976_0.026036593565402333", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "f93e901b7b17919f6324bee8e2af04b5c134c65c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-xLWKZhJBGHBGUI+FH4ptD2dCdJjIf1Y8zbDAs4oB2GYV9hnTz8Gpp3VzAc6/1bBWX3xoFrQxPDyrfvAsdPx9zQ==", "signatures": [{"sig": "MEUCIQDhd7etjCxq6AWAmRnhQuH6AEifHMV89LwsC/lRPwKJhQIgCmxYGjXTFOfOMMJbjRpFqcBrmnPaKG7a1tv6ke5putI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.27.0-1_1731566013812_0.9265205642749885", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "11531e1c4fcc55eaa3bae8f5b891f5ab48e0f08f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-dIBfp8NDrgvwUJxyqFv7501coIba+7xxBJy1gQEF0RGkIKa3Tq0Mh3sF9hmstDLtaMt7gL2aXsCNG9SCKyVVZg==", "signatures": [{"sig": "MEQCIGlhC5eT5XJkMt4k0jUT25wXN//WJd5PlT5XhSzwXT2jAiBeVp01iN+X2AVaoxTCVyuGusUWKmeYloJ6W4pvIS8VJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.27.0_1731667258433_0.3240989357351549", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "c6223f70b90a21b94fb2a3279fc3a7095f41dc5a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-hU9E8HRCoU6IXOpYTwTyOmUdCF059SrAYeRUAde99P7mt1nkHHtmgmNJmg1LdOAPxpBNSp7dx9U9Lqc5cE6ZUg==", "signatures": [{"sig": "MEQCIFrdsWXMF3aULXrf3OOZmkrmpT+iLIFf4qKaXP22uqG9AiBJnus0b7IbKx15jAZL/YkUw4kh7LNMPz5izBmPSwUCUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.27.1-0_1731677312119_0.4798357335654937", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "8289ef4167026664bdb073f0e9e8a01f1a7d6a87", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-v7kwCUWChEuk7bFQ3p4/Oi0w2zdycTS50hIDRA4rr74+XeP783c++YgjTsiGB5dhjZaxo9DDQS5nDatjgDMsrA==", "signatures": [{"sig": "MEUCIQDW8nacQ9gmQ02HpX1de2T1CHAq9ZFP+fXhs4o1HYClMQIgZgDH6TMqqwrw9N4Gwn05fFp/6oF+vxjDrqjABftme6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.27.1-1_1731685106738_0.4661283621000121", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "17346c4a8612671c7e253809035d16c47d85b52d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-/ht989pqM1mw+6xBAExE5WlgCCT0HV4uJQmRYUUZvXAPtbnPjkZ4oevR0upyF2fPhrtgsrptMtBiD2Zax0PXzw==", "signatures": [{"sig": "MEUCIQC0+WUFOYtd/0q83W0t9Ub0sTIrcfWWhAvV5Eo7KXyoZgIgKnXcJgXka0Y95j9NPOPfAVerEluLJhz1Rv2d57KgNLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.27.1_1731686885171_0.25366775359314264", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "c588dbaacc1809a95a49dc2e954b76044eb368f5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-eCHmol/dT5odMYi/N0R0HC8V8QE40rEpkyje/ZAXJYNNoSfrObOvG/Mn+s1F/FJyB7co7UQZZf6FuWnN6a7f4g==", "signatures": [{"sig": "MEYCIQD1Sg9AS/E8vNKj/4be4yj7mlMksxOAdb71hVrUYBCrkAIhANoLQvmsemirXUR1LJmD5Ul1/A9n5WqfRwAGnEmvYkiD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.27.2_1731691228049_0.19564447604420465", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "d281d9c762f9e4f1aa7909a313f7acbe78aced32", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-v2M/mPvVUKVOKITa0oCFksnQQ/TqGrT+yD0184/cWHIu0LoIuYHwox0Pm3ccXEz8cEQDLk6FPKd1CCm+PlsISw==", "signatures": [{"sig": "MEQCIBInMvi1gL2FfYPDE1DPySd7HYjSQK2oS9NsHFj7tyx+AiAGjXMC7spWuh5ussGtPwk6WEeTjtSjG7IBVY28hX0sVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.27.3_1731947999684_0.3780773912450017", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "8d9fe9471c256e55278cb1f7b1c977cd8fe6df20", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-FScrpHrO60hARyHh7s1zHE97u0KlT/RECzCKAdmI+LEoC1eDh/RDji9JgFqyO+wPDb86Oa/sXkily1+oi4FzJQ==", "signatures": [{"sig": "MEUCIAJ9V+DhD+YRH4mZvQjUJETsMboKvBC46ApiM7yOZdPVAiEA6BZMFHEcxjH3dFUD7s48EgJxdT3Lktj8Y0WMsxkAbss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.27.4_1732345242735_0.3629567596062775", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "b5565aac20b4de60ca1e557f525e76478b5436af", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-zgWxMq8neVQeXL+ouSf6S7DoNeo6EPgi1eeqHXVKQxqPy1B2NvTbaOUWPn/7CfMKL7xvhV0/+fq/Z/J69g1WAQ==", "signatures": [{"sig": "MEUCIDKlsQ2s2ajVB08p7shkzS4xka+VW+fAx88CpIhiMn/TAiEAiKkrEKwqaLAIV0UKSdDfgfEm0InvA4nFf+748ojN2Zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.28.0_1732972570763_0.6433844711592225", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "652ef0d9334a9f25b9daf85731242801cb0fc41c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-pTnTdBuC2+pt1Rmm2SV7JWRqzhYpEILML4PKODqLz+C7Ou2apEV52h19CR7es+u04KlqplggmN9sqZlekg3R1A==", "signatures": [{"sig": "MEYCIQD+ZVNNZrS6xuNTW3cB4wWVgP9LXwKc7ZjWWEin22+Y+QIhAJX4WwOkDudfOm94yrFT0nbCnBSkoJU1aq15DErkSVox", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.28.1_1733485522127_0.5363306873446774", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "06f6c3632cd6957a74e22454a0258d70c735de27", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-dkXk8RptzHYFCYXMxcO2xc+3n1w1BNhUqlb5bS5l1gowLmIusgQZlvCVQerLLwoImqihE29Z403KOyP0t+a2jQ==", "signatures": [{"sig": "MEQCIDLdL8Pg8OXkgQ20QdqYGfeASxweOhxirJrPp+pfhDxsAiAUcZ5ufCNxjTmTlC4h72sE8iYEyRBRdWvum4nnKiEd4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.29.0-0_1734331220110_0.023934188147068935", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "30622cf483a1466da9749c156e0d9bd79a54ea3e", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-Z7xE2X+N/Xt9C65az/ZS9PK+HGiaNNxYjhKfrEGW6WX0MxF52L+VQ+UUif7f4sX9j41eAAeVLqoZXKNlcLEXLA==", "signatures": [{"sig": "MEQCIEgMWRK88EvoOQLHtTq2ddyR2LK2HAYYJLJ2zVNJh65kAiAQBBjPgRb9i4Xcnj5fivwv1uXYDwnkxYqZNmoupAKP4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.29.0-1_1734590274959_0.5125639172355567", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "aa7967ba9117fda1ff2a2a609b9f3467f00b796f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-fgWOo3prU5rTrrTkyRnBXBH7sxPF/N0nyP0MzWqcKvQKi7aPZkkb/31An7XIAN8Xy1WTUlTagf7gGTbFIYmVog==", "signatures": [{"sig": "MEYCIQDzlwvkZ9vqSxTRsaEfMCTD9sYVU15fGVVzt9pjDueR6AIhAKplyYROyYC6DTQVxp6M+egKgTCoK7j6J6jRgKm8HH/X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.29.0-2_1734677788985_0.8319056109305729", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "b405bcc1d19247fa20c896f89f7e0255f959adae", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-Nmt5Us5w2dL8eh7QVyAIDVVwBv4wk8ljrBQe7lWkLaOcwABDaFQ3K4sAAC6IsOdJwaXXW+d85zVaMN+Xl8Co2w==", "signatures": [{"sig": "MEUCIBuoG5weMepkx8Gjntr7VRgo0L/QP3fLPJiHrhp5ZS86AiEA0ALiN2lx8CN4ECNQdhscex6h3YAiKYks8Lh6zSrS+zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.29.0_1734719870739_0.6432963367139788", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "544ce1b0847a9c1240425e86f33daceac7ec4e12", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-9b4Mg5Yfz6mRnlSPIdROcfw1BU22FQxmfjlp/CShWwO3LilKQuMISMTtAu/bxmmrE6A902W2cZJuzx8+gJ8e9w==", "signatures": [{"sig": "MEUCIQCvE6fmG5eeNzz3H9gWbE7m56UwFh7TK7X/Ti4R7OIP5gIgagQq+KVpS7Dx2kf3y+MBYlH4NQZY0TsQGlV8D67NsRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.29.1_1734765388889_0.08584479485909591", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "a7774f2532d0df64ada60fadb4e18ffd8a12fbe9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-LpABE0mkoL0KkgMWOwO2faSsssfBmzKZgToG+V+rUSqRqNP3lcER59q0KcNwpR+KeI57RIJgnlD6IDIglV6x7Q==", "signatures": [{"sig": "MEUCIQCfFAsCZqRnBQNCNqT/RsJEnIvthmTFUjeNMyrd+vglNwIgXog5ePyGRXpJowrSZso7rFPcQ+qKjKvjMrA2X9LUtY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.30.0-0_1734765460528_0.8732679562969665", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "a12ca1997157e123edbd8ed04ec6c9a4d388707b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-6R6c2GFSsjeP1XflASwCY2z1mr9AwxSLP54vovHagxTKoeiIeOVqNvs66qTxPC3uhXceIzTKsa17+D+PAAHjUA==", "signatures": [{"sig": "MEUCIQCVZze4ERzLPC3MHALD79u48qEwGszxTHnvokP04zLO2gIgYneb6ObLKRvn5ja/0ZrE2XSk0bndcFenjSf6NzzThWQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760361}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.30.0-1_1735541563752_0.5774507925418757", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "33e45cac222fa6d09891f73bfb2d5d027ec34989", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-gIh776X7UCBaetVJGdjXPFurGsdWwHHinwRnC5JlLADU8Yk0EdS/Y+dMO264OjJFo7MXQ5PX4xVFbxrwK8zLqA==", "signatures": [{"sig": "MEQCIFpccCz8icqk98CxuEKx1zwu5ER7EF4Ek4LUKiMFeYN7AiAuveEGwXz1+ckeI9JKUVAkPVfbBC6yKC0/P/AJu2Tgzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.29.2_1736078890249_0.63172311572147", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "cea71e0359f086a01c57cf312bef9ec9cc3ba010", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-1hiHPV6dUaqIMXrIjN+vgJqtfkLpqHS1Xsg0oUfUVD98xGp1wX89PIXgDF2DWra1nxAd8dfE0Dk59MyeKaBVAw==", "signatures": [{"sig": "MEQCIBUWRb4arV3LYTHVxFfbzxSrwzeTsAhf6n/H0VX5oVt1AiB99nmQJ5F8EOvaWr6C5otBVzsoTQmpicICse8kDmr8qA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760359}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.30.0_1736145424271_0.14999242297635407", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "e8585075ddfb389222c5aada39ea62d6d2511ccc", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-GLrZraoO3wVT4uFXh67ElpwQY0DIygxdv0BNW9Hkm3X34wu+BkqrDrkcsIapAY+N2ATEbvak0XQ9gxZtCIA5Rw==", "signatures": [{"sig": "MEUCIQDPrEsKncvb9x6dE9NHNzQRLHzO4KidpiSOx6CD0enVxwIgYHZwJ4gsNYNq00n4y67jeCk0sBeN5fu90tUO/H10H/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760335}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.30.1_1736246177940_0.3524561770998058", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "e0b3f551cb612265b4cdde51d382dbfbeb98b106", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-n+MFv9mCKXR8imcBqzf+2jULEajQO0JSXCOl771JlMSKCPC4YFVvpdJQskYeMoXRbxguSDiqs6NW7S5R+mXOLw==", "signatures": [{"sig": "MEQCIGGoM/aI2QZLxycmHIRQHM3dev8lanr5cxFcb54/ylm5AiAVilVsELOTIfkZq1TYUH8dOxPfus9NY4MrXBpOmXgirQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760289}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.31.0-0_1736834287948_0.9212278823722646", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "809479f27f1fd5b4eecd2aa732132ad952d454ba", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-D7TXT7I/uKEuWiRkEFbed1UUYZwcJDU4vZQdPTcepK7ecPhzKOYk4Er2YR4uHKme4qDeIh6N3XrLfpuM7vzRWQ==", "signatures": [{"sig": "MEYCIQDiKRkrQWikUUZG8zWp2J4WZ8bXFAauIFROwAwyq+vx1AIhAM7jzDuaelF2vcqraUlDXdLg8BZPrG1oZaJ+RNF1K1ae", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2825823}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.31.0_1737291433009_0.36425225613765133", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "e687dfcaf08124aafaaebecef0cc3986675cb9b6", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-Sts5DST1jXAc9YH/iik1C9QRsLcCoOScf3dfbY5i4kH9RJpKxiTBXqm7qU5O6zTXBTEZry69bGszr3SMgYmMcQ==", "signatures": [{"sig": "MEQCIEzKy8MDOwkZq0fMkRW85+PagoJuCWly8uZWf/JjLfYYAiAzU87BX0OC8Jkyr9FZhnx7vibsR59dGxXaqAfEvnG6bQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2825823}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.32.0_1737707279858_0.10413252817745855", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "6350327e0bae86d7ac58ce824c74fad5142e60e9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-rCFp2xWutJfsbeXvNMdc9hI2JpBhaOj4ksJAAaBSoi2I+IHEJd6O8SLGQ2hmEW0QoJ7xeiUzLCuQYgpBrFJIfw==", "signatures": [{"sig": "MEUCIFIpy9yPrgxgnoXUHGFw6ER4ueE+yJ69k84k3EwkoDqTAiEA8ZXjy0BCc5D+Xoa2JvQVQpiahoQMhQiU/tJZpTeyAPE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2825825}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.33.0-0_1738053033763_0.03957408759152359", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "b713a55d7eac4d2c8a0109c3daca6ea85fc178b3", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-wO0WkfSppfX4YFm5KhdCCpnpGbtgQNj/tgvYzrVYFKDpven8w2N6Gg5nB6w+wAMO3AIfSTWeTjfVe+uZ23zAlg==", "signatures": [{"sig": "MEQCIEt1vFnTt+zaR00+YiyyMuQJzsrxAUbawmCzkX6zPOlaAiBEG9mQubBFRR59yFABFXQ+fegbanaHbOdQ8yWcUpXZ2A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2825823}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.32.1_1738053221492_0.2374388617817096", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "2d760d9de29a3f154c303233464b676cb8dd3540", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-dtqsVHoQ1xyQpCMTuWJOYbE6j3byEUCjUAD5J9XEHl2zRO/1+F/N8jfu97GZxOx+ugrZHEFn6hTFvI+PR1Fxbg==", "signatures": [{"sig": "MEQCIFUFj/KEt7Qw8VjmMDHvQTp17W2PgPkeEkQkyUGbvFivAiA8xVck2Un9hrP6CBt5tVi8NqcayjylvfInKlfe+V1oXw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2762319}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.33.0_1738393946332_0.865785152114511", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "393030fdb664326a007300a2b7eabc7a8a6c7609", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-3SWN3e0bAsm9ToprLFBSro8nJe6YN+5xmB11N4FfNf92wvLye/+Rh5JGQtKOpwLKt6e61R1RBc9g+luLJsc23A==", "signatures": [{"sig": "MEQCIAkJKllx5TGEALZgrx8wOjTn3G4RpKTEqoZhvFgBWUJ/AiB8N8ihoM1xz44JXd/oJkhEredxmLbWzl1BDAH13DePNA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2762319}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.34.0_1738399247701_0.9226056150098836", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "ad5902358a0d91bda75b3cbd45d61d1f6698b6f5", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-fwr0n6NS0pG3QxxlqVYpfiY64Fd1Dqd8Cecje4ILAV01ROMp4aEdCj5ssHjRY3UwU7RJmeWd5fi89DBqMaTawg==", "signatures": [{"sig": "MEUCIA+bNym9q9qrSCZtnGNC3wvg37yBt0gvOUD/csim9y/XAiEAwuWCJICM1qduxXGreSlPYmtQzrGyU0TlIU7v2lAZphQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2762319}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.34.1_1738565918464_0.67810136272342", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "0a39be62918231b09ffa8417f1128fd01180f2ab", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-g/O5IpgtrQqPegvqopvmdCF9vneLE7eqYfdPWW8yjPS8f63DNam3U4ARL1PNNB64XHZDHKpvO2Giftf43puB8Q==", "signatures": [{"sig": "MEUCIGMVLZrm/r4K5CpWAD+Fb+GIBvbH46ny+MXS37mTGZKpAiEAlX887+06PaC/E68bhDLhpVG6IJITEqFZIgfrapCKmx0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2762319}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.34.2_1738656629162_0.12900232275036871", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "df3c2c25f800bc0bdf5e8cfc00372b5ac761bc5b", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-kMbLToizVeCcN69+nnm20Dh0hrRIAjgaaL+Wh0gWZcNt8e542d2FUGtsyuNsHVNNF3gqTJrpzUGIdwMGLEUM7g==", "signatures": [{"sig": "MEUCIQC5XDYJAgKD420jX81Uv8ibMJsCXi+2rDBenIQQiMSsLgIgKVhssZGlFZaWHJ3UH63G+A3auhm8T7eYa118Md4i24A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2762319}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.34.3_1738747351398_0.38377124198501433", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "f52928672c40c8a8a30f46edf8eeb72d2b5560ab", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-KvLEw1os2gSmD6k6QPCQMm2T9P2GYvsMZMRpMz78QpSoEevHbV/KOUbI/46/JRalhtSAYZBYLAnT9YE4i/l4vg==", "signatures": [{"sig": "MEUCIEAMtfVOIK7d9WMBBGJI2z8ZBQTPjuqXN9NGESAF53D3AiEAjf4ylX4nCiNlDV1wBjSVIQBQJRqB1C7uWyEquifVuL0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2762319}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.34.4_1738791097571_0.5542582310216058", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "66b0f7ca788ec1c6e9154556a77984307f855299", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-crUWn12NRmCdao2YwS1GvlPCVypMBMJlexTaantaP2+dAMd2eZBErFcKG8hZYEHjSbbk2UoH1aTlyeA4iKLqSA==", "signatures": [{"sig": "MEYCIQDbvbMXCmaLMK3OOIxFAd+RSC0zAshSyXHsUB8+1p/L3wIhALDjc8o3Hm+aCzWsF2daPmcGGpjpd6uTMfF4tIp3ekOA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2762319}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.34.5_1738918409311_0.8520197050413019", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "fe0bce7778cb6ce86898c781f3f11369d1a4952c", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-Zsrtux3PuaxuBTX/zHdLaFmcofWGzaWW1scwLU3ZbW/X+hSsFbz9wDIp6XvnT7pzYRl9MezWqEqKy7ssmDEnuQ==", "signatures": [{"sig": "MEQCIEu1mLniUIGTtIUFLsdyL0VOx/q9m6fGcj8+DATUoq0CAiAc3zHQFS9GKzqCnpiLuSPFr+cU6YAsD3Dt5JGdq6iBQQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2762303}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.34.6_1738945953850_0.4255131104934402", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "e9b9c0d6bd248a92b2d6ec01ebf99c62ae1f2e9a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-vrDk9JDa/BFkxcS2PbWpr0C/LiiSLxFbNOBgfbW6P8TBe9PPHx9Wqbvx2xgNi1TOAyQHQJ7RZFqBiEohm79r0w==", "signatures": [{"sig": "MEUCIQCyHWTfEd/yGIp9QBzlF84wCdJo+smccpoVa7mUGMOpIwIgddji3QlMpUZsgyBzLPybzllzkKb0wcNPNcBxWVxQz8k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2762303}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.34.7_1739526864277_0.19103571735592628", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "5cdd9f851ce1bea33d6844a69f9574de335f20b1", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-LMJc999GkhGvktHU85zNTDImZVUCJ1z/MbAJTnviiWmmjyckP5aQsHtcujMjpNdMZPT2rQEDBlJfubhs3jsMfw==", "signatures": [{"sig": "MEYCIQCcx+8rBfw0OMEqeWSyb6yQOuxtTdEPAwrL19TIMNhH3AIhAN/kpTZ6BnTvuHZmgb6elbAwuLvF9B0RP6hwUstEwz6f", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2762303}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.34.8_1739773610279_0.5786532547523597", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "7ebb5b4441faa17843a210f7d0583a20c93b40e4", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-PHcNOAEhkoMSQtMf+rJofwisZqaU8iQ8EaSps58f5HYll9EAY5BSErCZ8qBDMVbq88h4UxaNPlbrKqfWP8RfJA==", "signatures": [{"sig": "MEQCIBHt7Oe7yCdWc9iJbdX/D3Oou7hi+f2eInFqlVCLj1aDAiA0ffOh4EJWFlKDmA5n21Le7+JqkvhNugVN/CYz9nzodA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2893375}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.34.9_1740814383487_0.48072570370825995", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "45d849a0b33813f33fe5eba9f99e0ff15ab5caad", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-c+zkcvbhbXF98f4CtEIP1EBA/lCic5xB0lToneZYvMeKu5Kamq3O8gqrxiYYLzlZH6E3Aq+TSW86E4ay8iD8EA==", "signatures": [{"sig": "MEQCIFx9hM0CZDCYx87jH+2zMada04Ydm8E/ACicQFBEszmgAiA20Bt/x51EYMSHwg/FcJlYDyDFwIYb+O9fS9Nfukh5JQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2958911}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.35.0_1741415113833_0.6547137901210993", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "c3ca6f5ce4a8b785dd450113660d9529a75fdf2a", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-VMPMEIUpPFKpPI9GZMhJrtu8rxnp6mJR3ZzQPykq4xc2GmdHj3Q4cA+7avMyegXy4n1v+Qynr9fR88BmyO74tg==", "signatures": [{"sig": "MEUCIQCoo1FfqVS8SuRmza5KkFD52+3rwV1H3ej2P/iUFaBkVwIge6NXjuS/1goiXO+WV6npLrw5Nw9hfSEQjqRCVZA/nIQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2958911}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.36.0_1742200571645_0.9909793640947937", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "8273166495d2f5d3fbc556cf42a5a6e24b78bdab", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-NxcICptHk06E2Lh3a4Pu+2PEdZ6ahNHuK7o6Np9zcWkrBMuv21j10SQDJW3C9Yf/A/P7cutWoC/DptNLVsZ0VQ==", "signatures": [{"sig": "MEYCIQD4Z2nhshzpeZi9SAum1yyufNfBq2wDICy6MAJ7OS1WtgIhAL61YsgJNjSmon7bhN7dW78rTFZQUEk0yl2XnJVpaW2Y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2958911}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.37.0_1742741852905_0.05326367033068169", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "54c2d7a4d86767001475f0157c408fd042f7fd8f", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-NXqygK/dTSibQ+0pzxsL3r4Xl8oPqVoWbZV9niqOnIHV/J92fe65pOir0xjkUZDRSPyFRvu+4YOpJF9BZHQImw==", "signatures": [{"sig": "MEUCIGsyrtmJbQIT3hCFmma2NWBm/SIWDK1sppqLowrGsS6nAiEAg2g98MnChHUfbBmtOu9KRrePnsjvfp8lN1DmbFhRzBc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2958911}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.38.0_1743229777492_0.3369360076877672", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "f669f162e29094c819c509e99dbeced58fc708f9", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-0pCNnmxgduJ3YRt+D+kJ6Ai/r+TaePu9ZLENl+ZDV/CdVczXl95CbIiwwswu4L+K7uOIGf6tMo2vm8uadRaICQ==", "signatures": [{"sig": "MEUCIQD/Hi9Jcto8/+OgjRnkxUdnS/Qtr3OKGbTDZryR6mkhgQIgHy1aJKRzatZtyVtuGgKaSl6PPUAqXVzeXJHPCzfmFOE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2958911}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.39.0_1743569402338_0.23567249407513025", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "4077e2862b0ac9f61916d6b474d988171bd43b83", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-vgXfWmj0f3jAUvC7TZSU/m/cOE558ILWDzS7jBhiCAFpY2WEBn5jqgbqvmzlMjtp8KlLcBlXVD2mkTSEQE6Ixw==", "signatures": [{"sig": "MEUCIQDGA5SivF7F8tYGq0q+5tkhbrP2ttKZZ1MlK4OxVm1JnQIgYUlYn31KBJON9siIAw+LN3x7sqnx6egLfUseYpYwbSQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2962551}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.40.0_1744447204622_0.32899071894695986", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "2c90f99c987ef1198d4f8d15d754c286e1f07b13", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-BvvA64QxZlh7WZWqDPPdt0GH4bznuL6uOO1pmgPnnv86rpUpc8ZxgZwcEgXvo02GRIZX1hQ0j0pAnhwkhwPqWg==", "signatures": [{"sig": "MEYCIQDD+pa7Dvz5/C+6pOayrbtKIMbTEYeMCh5Z0daYL47M/wIhAKqdht5TYW7J8si2LzvpprH1MkxsX1ZEUPDR/Br7xmOr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3028111}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.40.1_1745814952870_0.49805305991570803", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "0bb4cb8fc4a2c635f68c1208c924b2145eb647cb", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-3FCIrnrt03CCsZqSYAOW/k9n625pjpuMzVfeI+ZBUSDT3MVIFDSPfSUgIl9FqUftxcUXInvFah79hE1c9abD+Q==", "signatures": [{"sig": "MEQCIC4cbQM0y4Pl/nU3Wvd5G/cTcuLttuimhaETsJFeKe48AiBXlV5tKS8h+huSY5zVrQmfWZMlbnoe+hk0lyBG+aLCeg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3028111}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.40.2_1746516443813_0.2823558285864636", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "8a2a1f6058c920889c2aff3753a20fead7a8cc26", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-eRDWR5t67/b2g8Q/S8XPi0YdbKcCs4WQ8vklNnUYLaSWF+Cbv2axZsp4jni6/j7eKvMLYCYdcsv8dcU+a6QNFg==", "signatures": [{"sig": "MEUCIQCo7A9Ifnh2pvl+GQifp48Aor63qinLZ/SVktIxGHQD7wIgdgO/1OEvkyWa5PGkQ7tJmnhg9+RNtcx6U4Eko+Gf/tQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3159183}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.41.0_1747546437765_0.13431317163654866", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "612e746f9ad7e58480f964d65e0d6c3f4aae69a8", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-3mr3Xm+gvMX+/8EKogIZSIEF0WUu0HL9di+YWlJpO8CQBnoLAEL/roTCxuLncEdgcfJcvA4UMOf+2dnjl4Ut1A==", "signatures": [{"sig": "MEYCIQDUcb3pd9/1fEK12bOJH8LT2BZ+4VTwBKQlCJD32UdIPgIhAOf9lJct1d0KKctTcCf5HVLgbE9csWHHkNRlK1H3nA5V", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3224935}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.41.1_1748067307308_0.9583664956857301", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "3823f27e6753aa694e49087ba08e7b41251da7d0", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-1DmJwH0PPRPjVwUGHINP1YiH7fO25mhpf2B5N8ubBQdOjEVTRI0x69m8eJGoUSLISoW0kgPX2zTAiHc4zllQ/g==", "signatures": [{"sig": "MEQCIEqVMK2k5l59RKQvqlwLuX4sTA0itMnfXualr/JShcQbAiBOcyEk3U1klYRobyBemEC6aw4iyhJOOAyhZ3WldSAD3Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3290471}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.41.2_1749210062872_0.8640387695196916", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "6d21a0f18262648ec181fc9326b8f0ac02aa744d", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-6Qb66tbKVN7VyQrekhEzbHRxXXFFD8QKiFAwX5v9Xt6FiJ3BnCVBuyBxa2fkFGqxOCSGGYNejxd8ht+q5SnmtA==", "signatures": [{"sig": "MEYCIQDf4ll6KVMhUWDK8/9qcYjiTVZaYWo3t3I60K0EpdnezwIhAMoCE831VgF+eDcpr+L4IyJRXt7OAtlNj514EdKds51Y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3290471}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.42.0_1749221321969_0.3006454893222148", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "43c3c053b26ace18a1d3dab204596a466c1b0e34", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-gEmwbOws4U4GLAJDhhtSPWPXUzDfMRedT3hFMyRAvM9Mrnj+dJIFIeL7otsv2WF3D7GrV0GIewW0y28dOYWkmw==", "signatures": [{"sig": "MEUCIQC4ZmM5E8nalYOG304y4uKWLZFDCF6oUBcYbshf6kvm2wIgZ1RDtxEGq7wuOIxZF6r/M6BEu/aWPbNMO8JEl45rNfA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3290471}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.43.0_1749619391640_0.6942958514708282", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.44.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.44.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "93cb96073efab0cdbf419c8dfc44b5e2bd815139", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.44.0.tgz", "fileCount": 3, "integrity": "sha512-bKGibTr9IdF0zr21kMvkZT4K6NV+jjRnBoVMt2uNMG0BYWm3qOVmYnXKzx7UhwrviKnmK46IKMByMgvpdQlyJQ==", "signatures": [{"sig": "MEUCIQC4Y6rP7600/nKr+mfvdgEA0qA5I9ne47XRWChBtP0NfAIgSk9jsvU0HVQHoomfsq3j96NbQSdAjYikNhrA/C2Xo3o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3224935}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.44.0_1750314209286_0.3026380399472999", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.1": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.44.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.44.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["linux"], "cpu": ["ppc64"], "dist": {"shasum": "8cf843cb7ab1d42e1dda680937cf0a2db6d59047", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.44.1.tgz", "fileCount": 3, "integrity": "sha512-Rl3JKaRu0LHIx7ExBAAnf0JcOQetQffaw34T8vLlg9b1IhzcBgaIdnvEbbsZq9uZp3uAH+JkHd20Nwn0h9zPjA==", "signatures": [{"sig": "MEYCIQC5x4TFgM9FE6TobXVc5o+k1CP7HZcKuhW8ZQZGEKrTHQIhAMqfOQYFU/Ztl9F6QnhpqOPsJ0HIgmJGv0YbDHt8pPX7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 3224935}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "gitHead": "5a7f9e215a11de165b85dafd64350474847ec6db", "_npmUser": {"name": "lukastaegert", "actor": {"name": "lukastaegert", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-linux-powerpc64le-gnu_4.44.1_1750912487252_0.5243974112645291", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.2": {"name": "@rollup/rollup-linux-powerpc64le-gnu", "version": "4.44.2", "os": ["linux"], "cpu": ["ppc64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "libc": ["glibc"], "main": "./rollup.linux-powerpc64le-gnu.node", "_id": "@rollup/rollup-linux-powerpc64le-gnu@4.44.2", "gitHead": "d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-03vUDH+w55s680YYryyr78jsO1RWU9ocRMaeV2vMniJJW/6HhoTBwyyiiTPVHNWLnhsnwcQ0oH3S9JSBEKuyqw==", "shasum": "4197ffbc61809629094c0fccf825e43a40fbc0ca", "tarball": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.44.2.tgz", "fileCount": 3, "unpackedSize": 3224935, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICHtgtjBvdoXJtdKAJct+GlomtZ3DpNxOkNqEask8j6CAiBIvuv1ORmF5iHfj16KD8uKtf8BI9NMnsZfQBDpC6ov5A=="}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-linux-powerpc64le-gnu_4.44.2_1751633798149_0.33130963132726454"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-03-28T14:13:54.268Z", "modified": "2025-07-04T12:56:38.646Z", "4.13.2": "2024-03-28T14:13:54.662Z", "4.14.0": "2024-04-03T05:23:10.338Z", "4.14.1": "2024-04-07T07:35:54.490Z", "4.14.2": "2024-04-12T06:23:55.469Z", "4.14.3": "2024-04-15T07:18:48.650Z", "4.15.0": "2024-04-20T05:37:33.166Z", "4.16.0": "2024-04-21T04:42:38.890Z", "4.16.1": "2024-04-21T18:30:21.280Z", "4.16.2": "2024-04-22T15:19:39.337Z", "4.16.3": "2024-04-23T05:12:51.203Z", "4.16.4": "2024-04-23T13:15:23.926Z", "4.17.0": "2024-04-27T11:30:09.418Z", "4.17.1": "2024-04-29T04:58:11.222Z", "4.17.2": "2024-04-30T05:01:07.387Z", "4.18.0": "2024-05-22T05:04:03.129Z", "4.18.1": "2024-07-08T15:25:31.197Z", "4.19.0": "2024-07-20T05:46:33.157Z", "4.19.1": "2024-07-27T04:54:18.645Z", "4.19.2": "2024-08-01T08:33:12.148Z", "4.20.0": "2024-08-03T04:49:09.549Z", "4.21.0": "2024-08-18T05:55:51.227Z", "4.21.1": "2024-08-26T15:54:32.800Z", "4.21.2": "2024-08-30T07:04:44.129Z", "4.21.3": "2024-09-12T07:06:09.054Z", "4.22.0": "2024-09-19T04:55:50.093Z", "4.22.1": "2024-09-20T08:22:11.575Z", "4.22.2": "2024-09-20T09:34:03.799Z", "4.22.3-0": "2024-09-20T14:48:18.100Z", "4.22.3": "2024-09-21T05:03:28.959Z", "4.22.4": "2024-09-21T06:11:39.495Z", "4.22.5": "2024-09-27T11:48:35.769Z", "4.23.0": "2024-10-01T07:10:36.633Z", "4.24.0": "2024-10-02T09:37:40.457Z", "4.24.1": "2024-10-27T06:43:24.917Z", "4.24.2": "2024-10-27T15:40:32.009Z", "4.25.0-0": "2024-10-29T06:15:34.526Z", "4.24.3": "2024-10-29T14:14:32.546Z", "4.24.4": "2024-11-04T08:47:31.750Z", "4.25.0": "2024-11-09T08:37:45.721Z", "4.26.0": "2024-11-13T06:45:22.409Z", "4.27.0-0": "2024-11-13T07:03:36.194Z", "4.27.0-1": "2024-11-14T06:33:33.980Z", "4.27.0": "2024-11-15T10:40:58.640Z", "4.27.1-0": "2024-11-15T13:28:32.329Z", "4.27.1-1": "2024-11-15T15:38:26.987Z", "4.27.1": "2024-11-15T16:08:05.410Z", "4.27.2": "2024-11-15T17:20:28.334Z", "4.27.3": "2024-11-18T16:40:00.036Z", "4.27.4": "2024-11-23T07:00:42.978Z", "4.28.0": "2024-11-30T13:16:11.042Z", "4.28.1": "2024-12-06T11:45:22.363Z", "4.29.0-0": "2024-12-16T06:40:20.268Z", "4.29.0-1": "2024-12-19T06:37:55.236Z", "4.29.0-2": "2024-12-20T06:56:29.215Z", "4.29.0": "2024-12-20T18:37:51.127Z", "4.29.1": "2024-12-21T07:16:29.193Z", "4.30.0-0": "2024-12-21T07:17:40.747Z", "4.30.0-1": "2024-12-30T06:52:43.978Z", "4.29.2": "2025-01-05T12:08:10.562Z", "4.30.0": "2025-01-06T06:37:04.498Z", "4.30.1": "2025-01-07T10:36:18.132Z", "4.31.0-0": "2025-01-14T05:58:08.170Z", "4.31.0": "2025-01-19T12:57:13.239Z", "4.32.0": "2025-01-24T08:28:00.171Z", "4.33.0-0": "2025-01-28T08:30:34.006Z", "4.32.1": "2025-01-28T08:33:41.662Z", "4.33.0": "2025-02-01T07:12:26.649Z", "4.34.0": "2025-02-01T08:40:47.967Z", "4.34.1": "2025-02-03T06:58:38.668Z", "4.34.2": "2025-02-04T08:10:29.461Z", "4.34.3": "2025-02-05T09:22:31.643Z", "4.34.4": "2025-02-05T21:31:37.753Z", "4.34.5": "2025-02-07T08:53:29.890Z", "4.34.6": "2025-02-07T16:32:34.124Z", "4.34.7": "2025-02-14T09:54:24.571Z", "4.34.8": "2025-02-17T06:26:50.519Z", "4.34.9": "2025-03-01T07:33:03.751Z", "4.35.0": "2025-03-08T06:25:14.093Z", "4.36.0": "2025-03-17T08:36:12.016Z", "4.37.0": "2025-03-23T14:57:33.131Z", "4.38.0": "2025-03-29T06:29:37.754Z", "4.39.0": "2025-04-02T04:50:02.618Z", "4.40.0": "2025-04-12T08:40:04.871Z", "4.40.1": "2025-04-28T04:35:53.114Z", "4.40.2": "2025-05-06T07:27:24.016Z", "4.41.0": "2025-05-18T05:33:58.058Z", "4.41.1": "2025-05-24T06:15:07.542Z", "4.41.2": "2025-06-06T11:41:03.107Z", "4.42.0": "2025-06-06T14:48:42.230Z", "4.43.0": "2025-06-11T05:23:11.891Z", "4.44.0": "2025-06-19T06:23:29.536Z", "4.44.1": "2025-06-26T04:34:47.534Z", "4.44.2": "2025-07-04T12:56:38.434Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-linux-powerpc64le-gnu`\n\nThis is the **powerpc64le-unknown-linux-gnu** binary for `rollup`\n", "readmeFilename": "README.md"}