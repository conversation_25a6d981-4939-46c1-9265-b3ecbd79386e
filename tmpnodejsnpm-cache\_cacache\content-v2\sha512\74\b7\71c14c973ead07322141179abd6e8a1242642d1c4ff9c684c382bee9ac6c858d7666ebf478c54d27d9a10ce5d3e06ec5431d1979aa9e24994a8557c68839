{"_id": "proxy-from-env", "_rev": "8-221f1e388c5297ad5bde7ca8a7fef2f7", "name": "proxy-from-env", "description": "Offers getProxyForUrl to get the proxy URL for a URL, respecting the *_PROXY (e.g. HTTP_PROXY) and NO_PROXY environment variables.", "dist-tags": {"latest": "1.1.0"}, "versions": {"0.0.1": {"name": "proxy-from-env", "version": "0.0.1", "description": "Offers getProxyForUrl to get the proxy URL for a URL, respecting the *_PROXY (e.g. HTTP_PROXY) and NO_PROXY environment variables.", "main": "index.js", "scripts": {"lint": "jscs index.js && jshint index.js ;:", "test": "mocha ./test.js --reporter spec"}, "repository": {"type": "git", "url": "git+https://github.com/Rob--W/proxy-from-env.git"}, "keywords": ["proxy", "http_proxy", "https_proxy", "no_proxy", "environment"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://robwu.nl/"}, "license": "MIT", "bugs": {"url": "https://github.com/Rob--W/proxy-from-env/issues"}, "homepage": "https://github.com/Rob--W/proxy-from-env#readme", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "46bf283b345b5526ed38d5cd29eadb66879ca690", "_id": "proxy-from-env@0.0.1", "_shasum": "b27c4946e9e6d5dbadb7598a6435d3014c4cfd49", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "5.6.0", "_npmUser": {"name": "rob-w", "email": "<EMAIL>"}, "maintainers": [{"name": "rob-w", "email": "<EMAIL>"}], "dist": {"shasum": "b27c4946e9e6d5dbadb7598a6435d3014c4cfd49", "tarball": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-0.0.1.tgz", "integrity": "sha512-B9Hnta3CATuMS0q6kt5hEezOPM+V3dgaRewkFtFoaRQYTVNsHqUvFXmndH06z3QO1ZdDnRELv5vfY6zAj/gG7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDkKug3LBrDWTHLFdqkC1n6borV8SM3VilbzkWxUNF+YAiADzmv1E4Y9h3PlOiAfViyBOFhgGFJ8o+yphI7LTh8sjg=="}]}, "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/proxy-from-env-0.0.1.tgz_1455829607680_0.3209165702573955"}, "directories": {}}, "0.1.0": {"name": "proxy-from-env", "version": "0.1.0", "description": "Offers getProxyForUrl to get the proxy URL for a URL, respecting the *_PROXY (e.g. HTTP_PROXY) and NO_PROXY environment variables.", "main": "index.js", "scripts": {"lint": "jscs index.js && jshint index.js ;:", "test": "mocha ./test.js --reporter spec"}, "repository": {"type": "git", "url": "git+https://github.com/Rob--W/proxy-from-env.git"}, "keywords": ["proxy", "http_proxy", "https_proxy", "no_proxy", "environment"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://robwu.nl/"}, "license": "MIT", "bugs": {"url": "https://github.com/Rob--W/proxy-from-env/issues"}, "homepage": "https://github.com/Rob--W/proxy-from-env#readme", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "4096700204437c8926756fcda4d86610eed18643", "_id": "proxy-from-env@0.1.0", "_shasum": "3ade4afe2debe279de8421afef60fdb179ec410f", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "5.6.0", "_npmUser": {"name": "rob-w", "email": "<EMAIL>"}, "maintainers": [{"name": "rob-w", "email": "<EMAIL>"}], "dist": {"shasum": "3ade4afe2debe279de8421afef60fdb179ec410f", "tarball": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-0.1.0.tgz", "integrity": "sha512-UL8tCBZHp5AUAyYJiXEZaVzwpK0zN/vXkPjK+VQ0KzA3jU1UdUpAPTuBdHyQMSyaThUui5m/WLLIoWtZjMMDrg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDhrJZbkEonoHgH/qIFVEkPNhGixzGjive+pG7KMIMQAAiEA4qkifxgMWXsQON5ef4KoNFdY5wLoakjlHwQjbD7PnF8="}]}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/proxy-from-env-0.1.0.tgz_1455884285734_0.5764795215800405"}, "directories": {}}, "0.1.1": {"name": "proxy-from-env", "version": "0.1.1", "description": "Offers getProxyForUrl to get the proxy URL for a URL, respecting the *_PROXY (e.g. HTTP_PROXY) and NO_PROXY environment variables.", "main": "index.js", "scripts": {"lint": "jscs *.js && jshint *.js", "test": "mocha ./test.js --reporter spec", "test-coverage": "istanbul cover ./node_modules/.bin/_mocha -- --reporter spec"}, "repository": {"type": "git", "url": "git+https://github.com/Rob--W/proxy-from-env.git"}, "keywords": ["proxy", "http_proxy", "https_proxy", "no_proxy", "environment"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://robwu.nl/"}, "license": "MIT", "bugs": {"url": "https://github.com/Rob--W/proxy-from-env/issues"}, "homepage": "https://github.com/Rob--W/proxy-from-env#readme", "devDependencies": {"coveralls": "^2.11.6", "istanbul": "^0.4.2", "jscs": "^2.10.1", "jshint": "^2.9.1", "mocha": "^2.4.5"}, "gitHead": "6266d30c373153b55ed46a97aff81250f0433e68", "_id": "proxy-from-env@0.1.1", "_shasum": "c5f529b41d2a23f1f81f7ee258974fb80885a6e7", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.8.1", "_npmUser": {"name": "rob-w", "email": "<EMAIL>"}, "maintainers": [{"name": "rob-w", "email": "<EMAIL>"}], "dist": {"shasum": "c5f529b41d2a23f1f81f7ee258974fb80885a6e7", "tarball": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-0.1.1.tgz", "integrity": "sha512-COoSB26cBQseMl9DaIt0L0cAlvuk7QfcIZpf/9v8GtAS2chFCulYbeMZbm4uLwDUe/HlUet+oAxSern9i/OgmQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZpGBGSSyUCYziM3pyeyf5cC0aN8Bi3rUTrjpI8dvh4gIgfltFtWs0jL0fHYUmmTLm3+2VH0XMZ9CQ1tj249slA7k="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/proxy-from-env-0.1.1.tgz_1476736991549_0.034898495534434915"}, "directories": {}}, "1.0.0": {"name": "proxy-from-env", "version": "1.0.0", "description": "Offers getProxyForUrl to get the proxy URL for a URL, respecting the *_PROXY (e.g. HTTP_PROXY) and NO_PROXY environment variables.", "main": "index.js", "scripts": {"lint": "jscs *.js && jshint *.js", "test": "mocha ./test.js --reporter spec", "test-coverage": "istanbul cover ./node_modules/.bin/_mocha -- --reporter spec"}, "repository": {"type": "git", "url": "git+https://github.com/Rob--W/proxy-from-env.git"}, "keywords": ["proxy", "http_proxy", "https_proxy", "no_proxy", "environment"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://robwu.nl/"}, "license": "MIT", "bugs": {"url": "https://github.com/Rob--W/proxy-from-env/issues"}, "homepage": "https://github.com/Rob--W/proxy-from-env#readme", "devDependencies": {"coveralls": "^2.11.6", "istanbul": "^0.4.2", "jscs": "^2.10.1", "jshint": "^2.9.1", "mocha": "^2.4.5"}, "gitHead": "bdf563343ff76e88402e1ab0b5040515b71502b5", "_id": "proxy-from-env@1.0.0", "_shasum": "33c50398f70ea7eb96d21f7b817630a55791c7ee", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.5.0", "_npmUser": {"name": "rob-w", "email": "<EMAIL>"}, "maintainers": [{"name": "rob-w", "email": "<EMAIL>"}], "dist": {"shasum": "33c50398f70ea7eb96d21f7b817630a55791c7ee", "tarball": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.0.0.tgz", "integrity": "sha512-F2JHgJQ1iqwnHDcQjVBsq3n/uoaFL+iPW/eAeL7kVxy/2RrWaN4WroKjjvbsoRtv0ftelNyC01bjRhn/bhcf4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGxKyZKlAqPX61lgjPBQ4To1Ho2ZuupukYNRvXwNhstGAiEAoZIG36Xu1RkUOmg7KFqozKF3YkgO+GHslB1+sgh+EPM="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/proxy-from-env-1.0.0.tgz_1487931977410_0.9466661661863327"}, "directories": {}}, "1.1.0": {"name": "proxy-from-env", "version": "1.1.0", "description": "Offers getProxyForUrl to get the proxy URL for a URL, respecting the *_PROXY (e.g. HTTP_PROXY) and NO_PROXY environment variables.", "main": "index.js", "scripts": {"lint": "eslint *.js", "test": "mocha ./test.js --reporter spec", "test-coverage": "istanbul cover ./node_modules/.bin/_mocha -- --reporter spec"}, "repository": {"type": "git", "url": "git+https://github.com/Rob--W/proxy-from-env.git"}, "keywords": ["proxy", "http_proxy", "https_proxy", "no_proxy", "environment"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://robwu.nl/"}, "license": "MIT", "bugs": {"url": "https://github.com/Rob--W/proxy-from-env/issues"}, "homepage": "https://github.com/Rob--W/proxy-from-env#readme", "devDependencies": {"coveralls": "^3.0.9", "eslint": "^6.8.0", "istanbul": "^0.4.5", "mocha": "^7.1.0"}, "gitHead": "96d01f8fcfdccfb776735751132930bbf79c4a3a", "_id": "proxy-from-env@1.1.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "_npmUser": {"name": "rob-w", "email": "<EMAIL>"}, "maintainers": [{"name": "rob-w", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==", "shasum": "e102f16ca355424865755d2c9e8ea4f24d58c3e2", "tarball": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "fileCount": 7, "unpackedSize": 29452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXyH4CRA9TVsSAnZWagAAksUP/1xI1JdvDAAo+FHuPEF1\nRLwWvI/lKVM/2ksapJNiJX0Xpt/rCBKOdSPfR0DpXVAmuaP+9JGLSd9Ac5fT\ndCikeI/SsIiRI3HpHthF5z5Aa2UD9MIX9y9Y1XNRw8TsqsX4Jvy5178vhV8D\nJD249+lDXrjwNMibMkRLPWvSFmpRUmY8E81mXm4x9stRq82abs2dEWURmmS2\n9ATjxT/jG+GiL4YXPLGsPEUbBavoL7q93E7M8VlJaOOIfEXV6HqpMvQdW/cU\nSo2BOsJhzBW4utWuxaSjJsdxQk5Uj6PAB84UeEeOpvI1NKimR4WyZ1i47c4s\nx7Zg9Rk8RsH02w9K9N3Ix76wsH4DQ6RWRjRWkLGMu16IyQSJ4wdYAKEOBr68\n5ADv/BCwnOMLgXI+UnH8VskTV/eJ2tdIXvr9anvTj3F+3CDEkeqZ+L2rPy8B\n4P9nEGiWsMGfWtkLKmFDk/wCOo4uvm2Q/lymRN846lh7xI5xGqAzq2ylMOap\nGTM+WTmaRdAnWN+aj0TeLjura3SRbZv0RDh1yJ7KFbfto5tLpYAJcfRT9hTb\nQ4cZSpeFDh3mSkuoTbEk9MWviP2HKu8kyYPLwpb/u2w0Dj0EYn8NjMDcsp+8\n4T8IAWfGOJiSDzirvUbsbj1ZgezYHeMZ7R8mqngj/L/u2FIvny/wEEBoUaNb\nGsyR\r\n=2zPU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAhI5LHXgkZS39dNhkqODm7knrj64zEVqh+OBgtRaTIiAiEAmu1f+Ax+02HZu/qyday6T4hLr9+FNThJIWv4Bz4JWK4="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/proxy-from-env_1.1.0_1583292919676_0.18466952575605178"}, "_hasShrinkwrap": false}}, "readme": "# proxy-from-env\n\n[![Build Status](https://travis-ci.org/Rob--W/proxy-from-env.svg?branch=master)](https://travis-ci.org/Rob--W/proxy-from-env)\n[![Coverage Status](https://coveralls.io/repos/github/Rob--W/proxy-from-env/badge.svg?branch=master)](https://coveralls.io/github/Rob--W/proxy-from-env?branch=master)\n\n`proxy-from-env` is a Node.js package that exports a function (`getProxyForUrl`)\nthat takes an input URL (a string or\n[`url.parse`](https://nodejs.org/docs/latest/api/url.html#url_url_parsing)'s\nreturn value) and returns the desired proxy URL (also a string) based on\nstandard proxy environment variables. If no proxy is set, an empty string is\nreturned.\n\nIt is your responsibility to actually proxy the request using the given URL.\n\nInstallation:\n\n```sh\nnpm install proxy-from-env\n```\n\n## Example\nThis example shows how the data for a URL can be fetched via the\n[`http` module](https://nodejs.org/api/http.html), in a proxy-aware way.\n\n```javascript\nvar http = require('http');\nvar parseUrl = require('url').parse;\nvar getProxyForUrl = require('proxy-from-env').getProxyForUrl;\n\nvar some_url = 'http://example.com/something';\n\n// // Example, if there is a proxy server at ********:1234, then setting the\n// // http_proxy environment variable causes the request to go through a proxy.\n// process.env.http_proxy = 'http://********:1234';\n// \n// // But if the host to be proxied is listed in NO_PROXY, then the request is\n// // not proxied (but a direct request is made).\n// process.env.no_proxy = 'example.com';\n\nvar proxy_url = getProxyForUrl(some_url);  // <-- Our magic.\nif (proxy_url) {\n  // Should be proxied through proxy_url.\n  var parsed_some_url = parseUrl(some_url);\n  var parsed_proxy_url = parseUrl(proxy_url);\n  // A HTTP proxy is quite simple. It is similar to a normal request, except the\n  // path is an absolute URL, and the proxied URL's host is put in the header\n  // instead of the server's actual host.\n  httpOptions = {\n    protocol: parsed_proxy_url.protocol,\n    hostname: parsed_proxy_url.hostname,\n    port: parsed_proxy_url.port,\n    path: parsed_some_url.href,\n    headers: {\n      Host: parsed_some_url.host,  // = host name + optional port.\n    },\n  };\n} else {\n  // Direct request.\n  httpOptions = some_url;\n}\nhttp.get(httpOptions, function(res) {\n  var responses = [];\n  res.on('data', function(chunk) { responses.push(chunk); });\n  res.on('end', function() { console.log(responses.join(''));  });\n});\n\n```\n\n## Environment variables\nThe environment variables can be specified in lowercase or uppercase, with the\nlowercase name having precedence over the uppercase variant. A variable that is\nnot set has the same meaning as a variable that is set but has no value.\n\n### NO\\_PROXY\n\n`NO_PROXY` is a list of host names (optionally with a port). If the input URL\nmatches any of the entries in `NO_PROXY`, then the input URL should be fetched\nby a direct request (i.e. without a proxy).\n\nMatching follows the following rules:\n\n- `NO_PROXY=*` disables all proxies.\n- Space and commas may be used to separate the entries in the `NO_PROXY` list.\n- If `NO_PROXY` does not contain any entries, then proxies are never disabled.\n- If a port is added after the host name, then the ports must match. If the URL\n  does not have an explicit port name, the protocol's default port is used.\n- Generally, the proxy is only disabled if the host name is an exact match for\n  an entry in the `NO_PROXY` list. The only exceptions are entries that start\n  with a dot or with a wildcard; then the proxy is disabled if the host name\n  ends with the entry.\n\nSee `test.js` for examples of what should match and what does not.\n\n### \\*\\_PROXY\n\nThe environment variable used for the proxy depends on the protocol of the URL.\nFor example, `https://example.com` uses the \"https\" protocol, and therefore the\nproxy to be used is `HTTPS_PROXY` (_NOT_ `HTTP_PROXY`, which is _only_ used for\nhttp:-URLs).\n\nThe library is not limited to http(s), other schemes such as\n`FTP_PROXY` (ftp:),\n`WSS_PROXY` (wss:),\n`WS_PROXY` (ws:)\nare also supported.\n\nIf present, `ALL_PROXY` is used as fallback if there is no other match.\n\n\n## External resources\nThe exact way of parsing the environment variables is not codified in any\nstandard. This library is designed to be compatible with formats as expected by\nexisting software.\nThe following resources were used to determine the desired behavior:\n\n- cURL:\n  https://curl.haxx.se/docs/manpage.html#ENVIRONMENT  \n  https://github.com/curl/curl/blob/4af40b3646d3b09f68e419f7ca866ff395d1f897/lib/url.c#L4446-L4514  \n  https://github.com/curl/curl/blob/4af40b3646d3b09f68e419f7ca866ff395d1f897/lib/url.c#L4608-L4638  \n\n- wget: \n  https://www.gnu.org/software/wget/manual/wget.html#Proxies  \n  http://git.savannah.gnu.org/cgit/wget.git/tree/src/init.c?id=636a5f9a1c508aa39e35a3a8e9e54520a284d93d#n383  \n  http://git.savannah.gnu.org/cgit/wget.git/tree/src/retr.c?id=93c1517c4071c4288ba5a4b038e7634e4c6b5482#n1278  \n\n- W3:\n  https://www.w3.org/Daemon/User/Proxies/ProxyClients.html  \n\n- Python's urllib:\n  https://github.com/python/cpython/blob/936135bb97fe04223aa30ca6e98eac8f3ed6b349/Lib/urllib/request.py#L755-L782  \n  https://github.com/python/cpython/blob/936135bb97fe04223aa30ca6e98eac8f3ed6b349/Lib/urllib/request.py#L2444-L2479\n", "maintainers": [{"name": "rob-w", "email": "<EMAIL>"}], "time": {"modified": "2022-06-24T21:56:56.683Z", "created": "2016-02-18T21:06:51.722Z", "0.0.1": "2016-02-18T21:06:51.722Z", "0.1.0": "2016-02-19T12:18:09.827Z", "0.1.1": "2016-10-17T20:43:13.490Z", "1.0.0": "2017-02-24T10:26:18.024Z", "1.1.0": "2020-03-04T03:35:19.815Z"}, "homepage": "https://github.com/Rob--W/proxy-from-env#readme", "keywords": ["proxy", "http_proxy", "https_proxy", "no_proxy", "environment"], "repository": {"type": "git", "url": "git+https://github.com/Rob--W/proxy-from-env.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://robwu.nl/"}, "bugs": {"url": "https://github.com/Rob--W/proxy-from-env/issues"}, "license": "MIT", "readmeFilename": "README.md"}