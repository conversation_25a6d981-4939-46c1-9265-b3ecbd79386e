{"_id": "@esbuild/freebsd-arm64", "_rev": "95-351206438d9f599fc02b3c8b88ca62c1", "name": "@esbuild/freebsd-arm64", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.15.18": {"name": "@esbuild/freebsd-arm64", "version": "0.15.18", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.15.18", "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "cfbd8ca0fa3626dbaea99ca939532bdee3f09c51", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.15.18.tgz", "fileCount": 3, "integrity": "sha512-wnGvPZKaD2rbxTtw7CDGA+8OOvcOnM7rVd1B1RPW4ijEw39HXRkuu8FsmuevnV5uLs24WHqqD+jPlfEntTlHIg==", "signatures": [{"sig": "MEUCIQCS9Bs7N1b/1QzwKwD3bac5bSxgqlWvLeU5TVqZpqqsIgIgYSpUplAwFXDBx7yyhNmUbjNLjWIspHkaX4mQzTrnEtw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8061418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoIPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqwA//Wr1kuhGAMlg+aIrxxRvjogQFjjB+u5DfD312vrBt8atd6PbS\r\nPf1UOJM7+Vg/gOA2YM8ijvTLCbszEhciAQRwNdY1ZXisPjXByjZdpDBqy6rV\r\n+dsY1+RX2Oxf22yzRYMGMq/KF+TNSUtYO4smoc0S8c5h/2WTp5wQoeqBxBz6\r\n8byNxlFcVUhlG64A42VJTtkdCEvy5Ncru2zAPO/NSyP+4UayqizSQMAguUik\r\nc9jQt9+BDLh9xEoRSS8p44gBCaWfEWGNYAAIpY+GUnu0BIj7qJJzPidWVBmL\r\n+OxT61yiFj3np9s7Dbp7ZItQzzkkwfmc647ppOJ0gMJ7fE0pY+WnRP8bYKU7\r\n/E5dIcmuJCV3eGHPLhdf0IsEQlC2YCfrKDsrP7wKxcX2s+ouoQFTldemRXNi\r\nCVL4eJLndH9n2795Ppwbq2kaaK4figqXTyxrkZJ/czc3B1cheiOqIoA00nMJ\r\nd7uVJpbw4d6ekwORNLzvXtBMzObNftyLrwDpVuz0MTU2aMuMX8pkdamMWQV5\r\nR692FXKfR9OE+GK1zao5lsT216LAyqB8Nm4NRg+yfD1pBiUdKUPTCcpEiL1k\r\nBJxLU+Pwmp64RQWgulsFbF5oFCBdBp7oGISlOwqmJ9EnLue7XNDY+JkzniMU\r\ngzwvcio1Qjka5JcQEO629H3bD2ijtMdj1Gg=\r\n=ySYo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.15.18_1670283791645_0.42399150406421326", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@esbuild/freebsd-arm64", "version": "0.16.0", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "f2c5ffb69287f7a77fa326b4dafa97061d60f8c9", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.0.tgz", "fileCount": 3, "integrity": "sha512-EG0G/XghR1qm3WgtKJY77BLiN5UjttqEXhhg47Pp9rZbqRsa5iw2KufbZiYUNye81jSSQLM0WJZv6sOPAvRjow==", "signatures": [{"sig": "MEUCIHqpm8oEgaX+XbnuQ9Q6PYgi72tJ8UGtOeIl6vMFw/zoAiEA2XezgHarVyocaIVRUyjT8cKEq30uh5pwySJM2UMb6a0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8061417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA6SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKBg/9Hrg4jG2IMIRffqV9mYUK4Wbp5Y4JKEAh9o1LsX2MBIqWjWZ0\r\nJi3pHaRSqycN8zzdW26SZDCxp23wIzTEAeb2rnSqloHNicneGK2xYQE6EcUr\r\nkO/yG/UPzv/vuKDcO2blf0UGI4n834fhrUJXEr1bG/68DehRstLXUpgq/Aca\r\nGyqoOaNJFbZxvys62rvFXhoD5R0ETEvmok+TIRTOlR4FpD6qsp0du94dx+S1\r\nODLn9E93QB6f3fn4Sis6xp7xenGzK4FPl6DTlZrYlHZYUya2fiF36GCWerzu\r\nsN8+NOu/Xh8pc/JpXPDsQxyRpLRCWOxOp3g43nw7P+2dlHfhyDByffoIR8us\r\nLrQSkxz/WeotHFSLmOZQOLansLyXZU7o+eIzSlpfpKQi9gGYvO+Y3oz8kYDR\r\nNURsui/MU4lFhC8x9sXZEG2LDqC/kaj+cZ7cv4q4I889mo85i2xo+8vl72LS\r\nDjgAa0b+dCmc8tdASUm0kCeY1aVVn8PL/7AvgVWgEyYrmF4STuh4uaSDvXR/\r\nIsCehzMa2eag5oO4wwzRonVbxaL3+MViZ82lBOTo1yQIJQA0AaC+HQ3by11J\r\nm9elrtEzHJMbEpPW9pR+gtniC9ahk34Jo3Zeddu3l7eIZVdAS8CQZuo3mlV5\r\nK8uQpulDzHp9YEHyeeqjHq8U1d2gdOIYjzw=\r\n=nLzg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.0_1670385298495_0.6086659526333553", "host": "s3://npm-registry-packages"}}, "0.16.1": {"name": "@esbuild/freebsd-arm64", "version": "0.16.1", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "bc3e33c46af0eea93ee0c4bbb37dd41bf9548711", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.1.tgz", "fileCount": 3, "integrity": "sha512-/6kJ0VROu7JYiWMV9EscVHH66HCCDd0Uo3mGjrP6vtscF19f9Prkf3xZJH3AO9OxUOZpfjtZatf9b0OyKVMl6A==", "signatures": [{"sig": "MEQCIDdoDvK9RZLHHLhD3mcXFzK7iI7SsFhfxZnArjdLfkGrAiBzAe2pFotsJUdV8dHICft1SkhldS65iUzSjsZC9Ten0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8061417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBsIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqk9Q//de8WUPpxp0C3eiYetBP7E3mPhhsfoJG7l0s8LtGKuWF+lVNe\r\nAPz93gWEUPxyy4Pb4CytWZq2cGi6Bl59BWQxw8OR96iLzj1DkTiI70EEizWx\r\nYNabj+ciL+vaheWDgZTk/oC80FxujJhQF8bIR6vYqffvZjZ7I8u5vgzvxH4L\r\nZKMh6FzISf/fEquMHK+Dq0GKbBUDxagR9+E8JcFSZwSgKfZXNgZTGt1WHB2b\r\nPon0c703AUZDR+ok4sBFh6tqmjM7nmtcm8sfmdN8qUOfSfovh+WPWxH0idff\r\n/kCoFTFsLTYlMOd6mLVsSPsr9ftBQgNf1k4Dt7NunFbtcfBn0d5J0IHZtB42\r\nSPdOhi4HLAalh2KVdbjtleSbo6WoGs1mG8hs+UHWWYooiDV72M4EfTGva0uc\r\nBNMSFkQptLCv804qkpdsVL4lHRkWTFZMYZW3XxVD8M0alCnviL2qfwtkQmsz\r\nDBsJvpwDoUGaP2fMyNNBwcZ/ptwRmWdhVivVyPXOl8Ac3SvPNK+YJC8mfzvV\r\nY2MSyQzYJbTpYwQL5FzOdDM2T0N9QXQhcGaq1JP6ihbqpAPaJHPPCVi1mJG3\r\n2g0qIYf/91ZJciMJkd4e3adKIHWNwSPICU20Pi3pjNTaRROUrFKZtnq2i1bK\r\nmyw7oBkwCMd23KsxAACoMscz/T5sn5kQDOU=\r\n=Ce90\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.1_1670388488142_0.333480221017177", "host": "s3://npm-registry-packages"}}, "0.16.2": {"name": "@esbuild/freebsd-arm64", "version": "0.16.2", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "3abbf2a113dbcb77f253f031c412a27874a31b71", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.2.tgz", "fileCount": 3, "integrity": "sha512-1QuZr7GnoipDYMFJDucqXmVvJZidZuHbvw5QLzBehYq67GR1Jub9pSo6O0Rt4LtKnu3TF2K/bjgzPJAGFY6W4Q==", "signatures": [{"sig": "MEUCIQDmmDAHuxUswYoEDjwdsMT0PXRgtES05uAHvM3LNgDT0wIgGpNIRa2KZSMLRtt1ekc/uYHASqZzSi0AbONx/DFNMX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYtfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXsxAAom3A1Ul3pVjyQDC6OyJBVs+Q8rWh7w9UgjDlWYsZweTelWTh\r\ndnYM66nlawrHf07Fw8Sc5uEgSTA2KpQ1LLVQXuID7VKvjE7960XBAke1uJ3R\r\nwLH0b77o4vrDhbFUhmr93fbIzFgDRYyLnNdv75V/nYaSTjMGgpLptnTLV7xw\r\nhJmWJyVP45Gwd4W/BJaQZQwNutUw9lPbaxq8BRQX6wDlPW5WUsMJnAswFXO9\r\neV6hRS9XNkQ2qcAfybm/4Mlik3w9gqIdZVYyXw8FlUAhwryiDo0bKBxuv5k4\r\nuMAphZnRMuEviP1AhV9OMw39HO7Wkuejj46kzv/CU28iyURRcrpkmL+yLQTw\r\nBlKz4rqkOMX9VANC2SYpVcFPETjz78VnVT0+Qh4MGUclG4KbASjEYPvb87DS\r\ntgVrzHnhfYM+371NKjibAjV3QJAgVhjiVEn4JG/G4/8unaDpg6CONhCo01lT\r\n3iI8gXpLRJj68w5tiq3DYc/Zd3GQS0GHJQCa5mlNXd9MlnuXBCaVYU8/B7Kj\r\nbEajqtzL3aLTA9GH80gFMlXTEJymbwSqlcD7441SPPs9J0MKv1yz5M+8VaPc\r\nuvYl0oR+/7R0OxV7eVz2N+gEye2S5sHYb4hTz9KwnCLNup4Ju1UG/T7tIigV\r\nS5Zx7U2wu7kVo1x/exue4PH57iIm5TH5I1U=\r\n=K2NG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.2_1670482783158_0.27641229678739765", "host": "s3://npm-registry-packages"}}, "0.16.3": {"name": "@esbuild/freebsd-arm64", "version": "0.16.3", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "16735ce16f8c9a4e7289e9e259aa01a8d9874307", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.3.tgz", "fileCount": 3, "integrity": "sha512-nJansp3sSXakNkOD5i5mIz2Is/HjzIhFs49b1tjrPrpCmwgBmH9SSzhC/Z1UqlkivqMYkhfPwMw1dGFUuwmXhw==", "signatures": [{"sig": "MEUCIAkCLGvpDw6TKTGcPbVHdEarwx+bh52MbLlzZIK59h4bAiEA7RE4pKSdDyItG1YMK41zlEa4HXvjBNXVQazd57KulDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkVEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpecw/8CDn/zlm+7SAUrsiL2IAkgezkUhsDECv1VCpx7LsonBUmSh2e\r\nVcLLiHdlpa+YRQnBiV5ySfFExHbqEyIyMiHKFXTDvTCVmMB7MR65UmmVwlIL\r\nRdjrT/0mvfw5kVOua3nqh4kDpGrx5zkC8TzxKaupcAKcJcQJwnNe3XYqSrek\r\nJebuHBkh9/2FDm3BicU6gdzJtedTyB0PxD2ZQfYqWB2u/k9yp3FDdzvYB4Cc\r\nkrLUm1pU2rKHFZd/PokhSH4GmTNMt51ydgPQShQBML19+TGMrPqrrx3X059j\r\neubvvOBaCumUrZyK+83KQU/wx2rSWWBRch4/wAdFPQPwGE3Cafd6510edTNR\r\nNhu0wv/BMfp/7jWtPRGZsK+GqEoQsYlVeXrP3Hnl1Y2XZBQ7gnLD9Mz/TsJI\r\ngRh8ZflwX654nh9OTXP6xgrpnemHF8bvFdwvc+waLxWFkp8PP8Zz/U5lekIE\r\nM29DQfObyT2sZ5UoVCMDVZQ2p6fY65VM4SvljW9CwCP2gwTYaN+QLInigK6f\r\nrC2Nuh1y5vNlK9+F1waYkUNqm1+wJWtG5x0hlv1pJGFV84mKf0yUL2WCWKTw\r\n/43gJQobLz6Od+pWmPakGa1mDNkV5zZRVYmviT4RJBkKcEErZHdt7cSaJ+Cp\r\nizvYSeykGIQhUVwDaJuH6iL4oLTyNvlx80w=\r\n=N3og\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.3_1670530371950_0.11236841381860074", "host": "s3://npm-registry-packages"}}, "0.16.4": {"name": "@esbuild/freebsd-arm64", "version": "0.16.4", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "364568e6ca2901297f247de0681c9b14bbe658c8", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.4.tgz", "fileCount": 3, "integrity": "sha512-Oup3G/QxBgvvqnXWrBed7xxkFNwAwJVHZcklWyQt7YCAL5bfUkaa6FVWnR78rNQiM8MqqLiT6ZTZSdUFuVIg1w==", "signatures": [{"sig": "MEUCIB4it9NBR2FfIkv6yVxpRYFi6bTYvZyMhZGOZzo+VxXHAiEAyORKuIitVyWPdQ+jcTy6NLYjYxfYmrS914UcWSKRrSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAIBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpN9w//eNkUAA06V/pvWWP3+jKt7RDfmE2Z8u+4zzs6/c1fEnOjIBaf\r\n9Za9olfqlU07Ko68DbbOHfEilZmvNfS268uW1R0FKrJ1lobqDu2C2qcPX3Dh\r\ngMkGmv8pJKS6htBKmidivovfJZPFn2zPLPcJpzxmwFA3QfOfqNiBFdBazn52\r\nlpA40/pne4SN1MZ46fy7CQTc2UMeMtqtla6YdIxxCZS4RtKudeHJal7lLMKL\r\nRtcYwefzU+W4nON3jMmNbFxwS+Wfx/bjPidmkS74RhGrrjznQGOIhW2SbDp1\r\ngKtNznOI01ExcFFo8qjmqhblxAJPctEIGs76cEOWJfdb/bybmcpUavajw/p6\r\nbqUw+DLK5YFBqd/R+wJh9D2uLPBJa06tndV7ajv/UMTZzJCZva/6z2auoa2P\r\nF21BE0e5H0b/ZnKeAkuZNXq3GpCFuuc2NSBTjeFHUSNznhmuEa/7Ok10FbeI\r\n8aVxIt6V5QqdO/w2U2Og+1EkRMLJhGD1/zq0aso2CXgelkbzfcgIPybBt4bg\r\ncuepbV0C8WrLT4NlSl6z2DPKgbH2LZYlE52bkxetNxlbci1tuDV9nxdcR53e\r\nihUchDifW3tR3ExBlBkwtGA289+mgj+jpYRyzBNzAoRZyWZhXcESq3BFbB9d\r\nKpvVIO8nqNBKe9G1llEioFsHunje0ZCMVGY=\r\n=f1m7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.4_1670644225109_0.03020998087303295", "host": "s3://npm-registry-packages"}}, "0.16.5": {"name": "@esbuild/freebsd-arm64", "version": "0.16.5", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "e101c671b150a6e0fe06b5978f2005505afd97b8", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.5.tgz", "fileCount": 3, "integrity": "sha512-w0dJ8om4KiagLCHURgwxXVWzi5xa0W7F5woMxzWO+LDCebrlyZUhCIbSXUKa4qD3XbdG7K4Y8N4mLDRMkZzMuw==", "signatures": [{"sig": "MEYCIQDQxtuO9UB1RyJavtTQOCRQZVDftd0T1PCZL+zByaNQQAIhAOP0+szKp0m2nBk06n6WQpi+3e0S3nanAFSETZjquWkD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLq5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqW4A/+JJgWUzMwkIaDe4Z9C/C6fT1SZvr1G/0gGPmEp4UDNm4GLEK2\r\nZi/fZUAPRtKJGW3gRiG7mNtJR9nTnoVLC0spDg9krywLrYqYIStocRRvoLEr\r\niw+8RN0G2vUED58hdNo74vjDMFG/05XiGcKX0I7mGKLi79K4QxjDVy1sNzhJ\r\nVx0KwW8SHnD7attWzurgp/Yr6aWJTavqSvcTToBMSlBzcwlcqK4ihzZVCI6F\r\nrBAnVA4ASK0JNrTWZ6IXQ4Cif2MF9g+SJ3ULrpdhgzUkgroHhUQ98QJqj6fD\r\nVHb5jQHu03pLacBwq1P8ytt72mFedAbcthGBsvPu6kwm5RRwV66MmpOu5euj\r\noEjNUIBzsTmVPZE4a+7MyrRyKttM9xEamkha+kFEOaDhJN+6J+Grva5YB7ul\r\naEgjmCBfSaOhju6mZisD5Z6cK2uwRxx/jQgmm6dQbaUBbytez03ti+t7dhB8\r\nbPCzzvxte4syOhjIbXNu3mqwfxT7AaTAOKapQICP+TlDj9FZridOuzPdebkJ\r\nB8f2SIsJYzIooaFujMfhct0+YpuxPIF7yessU/WB6um2i+GuZnV6zAxmIAV1\r\nU1c0aXQQvhjbe4GbmkWPE5yTOz9tnqDB5wBTKMV8Qz7+WVOtkDs3yLPvNeOZ\r\nhSSbQheovilLofZL0ruSdH752ZMppO/JKoA=\r\n=0Vkp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.5_1670953656963_0.9682457899529715", "host": "s3://npm-registry-packages"}}, "0.16.6": {"name": "@esbuild/freebsd-arm64", "version": "0.16.6", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "3d432d5e9fa17955f1e8b23255a00207f22891c0", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.6.tgz", "fileCount": 3, "integrity": "sha512-mMHz7ePkfVXW5wEhRR0XtoTlXDa5F1hIoxnfoeY+G0wWs4Q3HZgHZrXw3PSO26JnZOxIgyV/OuWIP87nQoWegQ==", "signatures": [{"sig": "MEYCIQCiU3XJPswU+eZAtyoXDr09iGHon3h1P7ePIYIQ9U2LpgIhANPUWnQIYfuK6dALI30j2tekBP1Cwm6YkYKmCMrA/CgW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV29ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrV1Q/8DNACjB8pykXNCKCLDXdB2Mx8wsAa4whI/oMaapaG81x54a53\r\nILjCqSztn4EKS4zJ6EVzDi/VdvnFM3N9N78DA74TGCzaTnwIbBd9f/HLJJ10\r\nvoBUoX7w7xoEB7YNhYTW8gfjKZsBGi25VqonHYVzlYMkvYGGY6kteCfoxiIu\r\n5sa04euXk0GRuIQNEQLcvZ0uiNVOrM2DNe94Eg5tain6Andu9clfb5U0farH\r\nBL48ow6iY+AoYBytsbKil3Q/09kvbF7YjdEtnhqPvVcWwEf5bpiBkH2uAZ0Q\r\n39A2uBOOmo0SGyxjIYiAdvC5v90cqUX8eCCvPL1d10US7ctEJJntUpL1QcPj\r\nQjsFjCiu6FPLztjUaMvFr503gphboIcyWDOKWPLR9nHX7AFtNcU5otWyJbxi\r\nMOAjhXaJslU3rrfAQrqLXEkEpSslXgo7QBvBo6CSOK6tfHwe4sPXVz8XCalA\r\nRoMtsShInPr5Uwy2tZLeqJqThl7U/WlAqugY8X0xV94uLwWp82owfA/R59b9\r\n3r70qP4FzZ+QRgTTPrO2nLsNTJ0GujxvfgO/pcdDN3DyOjfiazMUDShVKETT\r\n/p+YcbPGpI0D8328pzYsx+nEnT37RdUu5OH0mVX9z0DGlKGHGAa9V/85dLxL\r\n14jfmO0KCOkQHX98eY6EUuxfP4+Gc9w7Dvk=\r\n=Y3QV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.6_1670995389272_0.6076981733694524", "host": "s3://npm-registry-packages"}}, "0.16.7": {"name": "@esbuild/freebsd-arm64", "version": "0.16.7", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "ca52bd64b0bba69ae4063245366f25838357c332", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.7.tgz", "fileCount": 3, "integrity": "sha512-9kkycpBFes/vhi7B7o0cf+q2WdJi+EpVzpVTqtWFNiutARWDFFLcB93J8PR1cG228sucsl3B+7Ts27izE6qiaQ==", "signatures": [{"sig": "MEUCIQCaVqXzTuE8TYZb0LVR39x8G8p6clRKwbsHUkTGKYwtjwIgQU2BRFd1FHZgKsMtmX+gwGtLj3lOWSUmFLTRcpx7L04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlJbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/xw/8CrHbeFx/ewIkXX05scGgiZJD660wRhGSIto6nXQtwmgy6PMc\r\nuCSuiC3pw/2iKE/RrjUo82pecn4aR4Hw+wM0p3ERAkZ7qLTBpHyd0jUlZaBk\r\nAvJTmvS9tfXxAVzhdD7SxuPaZY88QL5Xk6GfjWbBBpeumGvFN1uTjdU0bAc4\r\ncj5NTkpF9JIqth7lFafjISJ5yA8e804bPtG4tQtHR9EwAKp/J82vQQbCu+ub\r\n5WGncYkvZHjhVWUCrw8fcamR5BekTEIhljuiI8FuHOwdtejKXlEj2tEpvROj\r\nQ7bD3kuSXo8urSz0Xr2II8V2Iq7GG7+rZGQCe53pfD5oRk5TrVMZ2p3UIa41\r\nb8cziRJC4+KcBRVhsjPaLl6/JcyuG0zBXBu9CsLQ2wfa9RwHXnATv1yahjC3\r\nXcGHkRUswuBCfue0jLvQF7HIN0hEVQ6/dp7cuqJmq/PV9gZzEdvLVNFS0BQ3\r\n72TTUBOTPZVGC1WgARMo00RJfrAHAUpHCoeXNUit2Z9H01OQp4XPxtTIFDII\r\nIaF7cXuR2GK9hOHBbRhRh26vKnahlRWpX9XXxucDtdkJmvP3t/8kOAzkHq6s\r\n2bN0qxksmw1x9TGlFD2JaLBf05edQGGrw4F8XXZmK0Y5s5gRGbFOl3TB+Huo\r\niwenZkuW4/HRfMqdRrnyq1qezJVNEeumtfM=\r\n=IlY7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.7_1671058011742_0.2306304990559509", "host": "s3://npm-registry-packages"}}, "0.16.8": {"name": "@esbuild/freebsd-arm64", "version": "0.16.8", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "c52f25e4f74c1b50b7242a7aefd208652716a209", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.8.tgz", "fileCount": 3, "integrity": "sha512-6DJuU3+tG9LcHCG/4K3e0AnqmmKWhUc9WDNIhLHOOdleafXwZeFvsqwfyaowNg9yUw5KipRLvV3JJMQ8kT1aPg==", "signatures": [{"sig": "MEYCIQD2z2DBBDQJ9rqeqCG6RFT0J5aihM6KCQTab7MDSXXPeQIhAKVKiQbYezuRdqTFAy3mqkg+Vui2KupJmGKKorIF16g4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQF7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNdRAAihZ8hUZwnaAAMKvz/c7XlLvK1tKxJ+E3ttgR7/nIyeNoXlbq\r\nyCQjPEIDwFEda7fUKc2OlVbHo7VZ8U6DjfpDiW623m35Yev6wCnug1MoIbF0\r\njjWiGDAZYb/buFcOH0ptFazlVsj3JKCTk4660rfv/1RsqI9sIrSH8wFjpcye\r\nD+aF0xOtTIj5fY1RhvgZSkU8x7MHAnwLdazJzY+TN9AnwO/W18tFk4FScMBk\r\n5o4cmNXWWU56mhiIs7gS1kPzne1N4sYqZ0p4dkYL/5Xs4b0jcLLolKR9rXtw\r\n9dZm54twHra2OmLol1PWyey5BC8ltXD/dOS3iBO3GaGk4YN4LlMeDRz3gVeD\r\nmz3BWH5gieh+iU+JTtMmAFnTQTzhkcYH6YGwpBhrSrhj0OaP+k2cPyLr6Z7L\r\nZJM2/cbo3otIBRkP3MBBU9cpw3UoPZUnNYE9ot/RpMaqc0e5bI/vw8CWaBr3\r\ntvW7at+oPoJ1VdPNwWSRbXV25+qjMsvNa66eJeYq+6LtSAbfL3FucZWilRDG\r\nOqPG646CmEZ3iFbKOba18MQ6UDMUm7PHEJSRe5L2qytSLPjTUz0Ko9kNy2yr\r\ndpa8H2rcdrEUp39jhBNzVdPE6KFqtrDOLQO8nXJAnNtqoQpG3Vl5wcz2MYac\r\nojKAEU6pE8mzNheO4MpjIL/XhlzUJnffDtQ=\r\n=7jWO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.8_1671233915305_0.5411008280426493", "host": "s3://npm-registry-packages"}}, "0.16.9": {"name": "@esbuild/freebsd-arm64", "version": "0.16.9", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "2b7c16f5d15c259ed279b293b97c28c4a4bb107f", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.9.tgz", "fileCount": 3, "integrity": "sha512-gIj0UQZlQo93CHYouHKkpzP7AuruSaMIm1etcWIxccFEVqCN1xDr6BWlN9bM+ol/f0W9w3hx3HDuEwcJVtGneQ==", "signatures": [{"sig": "MEUCIQCvOWTg71whA5zsMnfarcDIa+6AGlHUBFY13q61wPg+LQIgXD9thRxc2iPuAGmTxqVJP/qsHozy14m1IQYZbpXDLaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpeVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbSRAAo6MZRl/wC8gEoy7SPW/22r92sacmvW6mIHwQGunuXjQpsPBL\r\nwBwh6S232cHSB+CKsmWwy97V58A7ZS6Ywhc38cnznjJfi885CNplQq44OCZH\r\nDyohPv2jJK2CUylAo38h2ni0csjTeia7U2zCUShu24bh50XoLxePywgHhoWs\r\naD+y95wwefvF5ocoDl7aMRfGDuI6OHZdjQ1g1VdpdTmJYcOumJuj2SgBY1Ae\r\n4U7NjzPce/kF1oZUvRukx4Y5nAA/IZkr2TTqsLXkJwZ4PHXCCti0TfYXwd4l\r\nbe8GjuIHEWOGZDiQDlWxcUFq/5soekmho54cIpSTWO+BEZnK97DfkG3limfK\r\ntRKH3Q59uC+Sd+Mm8exBwR2w6gmP0MvSnnl9eXJ+IqITcv9QH6345uZ65BXh\r\n6YK7MgfRcfc2Mc8RRZfze/1W5LdVAtbuwH+TJGb8z7V9NY8+VZvytpuKPWgQ\r\n4KYuIoOkLyKbvuzhKubKhty8quyeAvpOlKpjtQDtwePGD35hNmGzSkmm6Yqo\r\n1I4+SUt9K6WlvG0yZOftpqU0GKqk+ANfYKDaLnysZwd0+ijzJmuu7fjaeYQK\r\nDa0cZjKw7o4QfvuoBaxa/ZiZTOJPtyqBIZqb1BcPx3mTvyepyT3NrzSEhe/8\r\nEFAh0IQq5CNoVyF5G6JOi38DDA717fEGmKM=\r\n=ui2J\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.9_1671337876707_0.5590280621964681", "host": "s3://npm-registry-packages"}}, "0.16.10": {"name": "@esbuild/freebsd-arm64", "version": "0.16.10", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "c69c78ee1d17d35ad2cf76a1bb67788000a84b43", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.10.tgz", "fileCount": 3, "integrity": "sha512-shSQX/3GHuspE3Uxtq5kcFG/zqC+VuMnJkqV7LczO41cIe6CQaXHD3QdMLA4ziRq/m0vZo7JdterlgbmgNIAlQ==", "signatures": [{"sig": "MEQCIDYDD+eu1+Nc6CH6QMIPRfy8cLK49Y67XfuH7oN1bnpSAiAOgaFqIu/KqOJ1/N2yK4A6Q2ldJ10NYVuIOEYPVWiUKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPMmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpy0A/+O2YVDUK30FKxmq4GDKVmQQ89QSFVs8NxowPWAB01cPRDYoK/\r\n14lfbIpmiWMDRzEhZmdoaUVnkq0JAPViFE9AO5087PBA5Cppc9PLJ1rbmKY5\r\nnB1BmWBjI+6zKXXWlt0zNq451IgJqV7lRw8QnGTAWAcCOYlu4AXFxPT0B4rY\r\nVKpvIqFP6fe3kJubkzjcn3eGC7zas3xvjMIDQJmBCT4wbmSpYrPMR+iILOE5\r\nSfovPj/iasaIkDvj2nRxBVyfAt3juA9H9+CioR+1cKRWb8KRJqfck92l1h4M\r\njHgKdkXVcIpNtHMcOHTO5tcRL1xES8S2fKu61ADl49Ma4XO2vUOkk6OaawGC\r\nNmSu/1Au/s6bVLp9ovWwfBuZmuZEScqS4YcyN27FXHtd+RD4jPeP1Ydnb8ye\r\n9etvjyMgFUU2D2bMfR8Ke8rlErFNlBPAkZgl6d6DL6+BcxWYcTERAx4l07QY\r\nFn0nb1piJGraz9F9yVey1ZZHAmeJkiqy4APdh3YJwC+cqJEWs0EMXyJetzyv\r\nmR2XqfQq7cQBSTy3WfA4gL1hfiWvdRgotVrVYXr0J+T568Dcb9l/ZnF6GsvL\r\ny/4EQ1U1YcKEjkipc3th068YqW3xuG2te0hSfobWHyFRe4/uDGveWjak/lA6\r\nGPeeLTLEB8kGYhGNYqNmM2ZrZinZXJeoW1I=\r\n=YrXl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.10_1671492390344_0.10223595959581555", "host": "s3://npm-registry-packages"}}, "0.16.11": {"name": "@esbuild/freebsd-arm64", "version": "0.16.11", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "3bfb050e3c70bdb1e8901b1b0ed5ff2bfc10b88c", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.11.tgz", "fileCount": 3, "integrity": "sha512-58FTdlgIQ3ZxFtGphjbIBmo7kfDhQih/PlfAnKraAcCDZOYXWcRFmHJtW+EVg32IIxuEAqhLAzCgrqpm5o8Wlw==", "signatures": [{"sig": "MEUCIFoBeAgiyAy1v+rK2qm02VWpuOS3VTMIqj18EcREMe7PAiEAkTtjcfdZ0jWKZ3ZgIuU/pRX9xSykuMmqCBPe5EYnZgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqky3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrC5Q/+IqUbssW2cUBAtF6f5t9eAS5DUPoaGpFIKz7ya69OmptyhpTp\r\nr1bK2Me3AT26DTi5w0nVM4LOz6U5z6qfLvDHU/lyxQ9YceH0ZA34VCTSuUfw\r\nJ3OIvr4ziB+t/aO4N47ZaPqMxM73IuNQvLFTJs+ADv5p+HyrRZGhSb+trGGo\r\nXOigDEuBpCg0dWVyI62+QpOctkr+x77T31eaciZmP944pRT0007q7jlD0Jm/\r\ncFFxPUrqrobEHGcaSrEFy5y9Rapo4hjxawzdde//pk3cNp6JkWjFhJWcl3eG\r\nuaoeQyyc4JkhDzQjvo4bbmmDcZcWWjT1KUZalwro1MYiwAcrgBpcRPbPzY3k\r\nqHsHe3CdaRMBODwyn6OB8Tcx9xzo2Qe42Uz6g3ZkjXfHoXg24XX1O3gdwM8H\r\n8nq/6g8QTL0toT1ELEIJnf8EteRiYJKYNwrN/3V3604tJjzcwb5On3fwzomj\r\nq/3sj8LDy7b5crp3PXUGCzfxQVo8fAdGaUMXydWOe6cmRLyKXfRip+cNW4jf\r\n1AMueKS6tgimb1usL5fFqsAIQCpByT8KqJIVvSVOa+XM0nXrjnQRrw+XqyCF\r\nFDBh5VNX5sjFg+2bfgWt+bf5cnOhH/cjeSmvvDGu5lhDwkee2ipOREYViA3E\r\nFqX+VNMHY1mausiC8GyVgpL6dYPuRdgLxZ8=\r\n=j0Bh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.11_1672105143499_0.826765788517303", "host": "s3://npm-registry-packages"}}, "0.16.12": {"name": "@esbuild/freebsd-arm64", "version": "0.16.12", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "b150587dc54dc2369cb826e6ee9f94fc5ec14635", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.12.tgz", "fileCount": 3, "integrity": "sha512-AMdK2gA9EU83ccXCWS1B/KcWYZCj4P3vDofZZkl/F/sBv/fphi2oUqUTox/g5GMcIxk8CF1CVYTC82+iBSyiUg==", "signatures": [{"sig": "MEUCIGz/cB1JLccjOpatAH3a6CyU2Mp2EB23ZArLUU1CS1DWAiEA3taXTPB8bTzy/Wh60QMQuxdwgwGV6XaDV76PnIEdGVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6Q3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohgQ/5Af1eK+Jf4G+1/4yD2NkRcQY+yJdXJ0LpTHmmT06y4uk6yJp1\r\ni/taR0fKWDPdYa9yFia6704fYc9wyjbmFoyZCaFyUdBCqz/cwqmkbrh5C7Z7\r\nhHx+9YOrcVnBplblNRlG2MOF1qwTChnhB1jpQgPvKQXeCLCcaX3UUSTG9EQM\r\noYaOwf5pq1weNdmFvIpjZB3yEehM1DnIGZVru7lZFacduT1RLG2imibiy0wO\r\nTr8DeoIosuEvtYJch65Bl2VnmYMl4Ul0cGkIiGm03Z0WjcutlMZGTRZVjzRB\r\nkRMJX/W0sstM3rxogwD5nr3N9jAJB/dlE3PoX3O80TktOoeaHzLPMqeugo68\r\nqqohog+amOpnfZaiU67G2jfoOsN82+fnygpD4+czFWSV0Bu5uIkUhJ8wQcaV\r\n9zmM3jUcc5Md3Vavr/Dncme+yXaF04vdC88IDRpJ29q2gnfa+EosXBNXSL9P\r\nlzuaf/U5xMkmanQ7q0lGRZpt+W/kel5Edf94krjD3/v5QBUFeVajXCV3fkW9\r\nIBnSy3lEeLQoHLg/hqKq3E9XrF22mQypRNoXFJ2mepvnVVGw6HOhWp0gIOtY\r\nZbcg1NL0SBDL8dmAkhgst/7jYBUp9aE7pzyoaOUu3PX8/gTfkRD50HTdE0uj\r\nVWV2HPvxKpRHFMsniaMRy6BpQGwEZ6aWooo=\r\n=CAmv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.12_1672193079524_0.1834642085355238", "host": "s3://npm-registry-packages"}}, "0.16.13": {"name": "@esbuild/freebsd-arm64", "version": "0.16.13", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "d1a45ac5c4a1be566c4eefbadbe5a967288ad338", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.13.tgz", "fileCount": 3, "integrity": "sha512-KxMO3/XihBcHM+xQUM6nQZO1SgQuOsd1DCnKF1a4SIf/i5VD45vrqN3k8ePgFrEbMi7m5JeGmvNqwJXinF0a4Q==", "signatures": [{"sig": "MEUCIFFYLShvzYTuHZXYNSg4f4F6S5roLZJZnVxO+lEyyCYxAiEA2VNZbpHitlLGgVytE0EtW7UCA5thoUeKU12dgLQxX3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2FMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1dA/9GTbGWk2zOP4piPnD9qhgkY1GNIbDzMGT6U3C3aONIO1F8mb5\r\na3aQmRQ0WeHolX1ruEzg9YQ6hWUtquvlcNR8AKo+5vOMaGgn5VeccsXveXfN\r\n1BhSVlL/mjD1c72kTdHwOZg0lvxXBEvpWfD9jPGUnkpsznVh61ek8yh9FEnM\r\nrgR3u/b6hvPFKGMbf0gaO1udiVMpX4hyJ7/CooiZXy9ONSWFwNxUiRGbleKQ\r\ngMB+nRtqMTvuea4v5fVgmdH6AoTm74Y9j7C/AA2pVO3P8k6WGKCprWsgpx1s\r\nl0R4rmwtvrRKuUGx5oTHKHmL4fR0Pu9o+GCD+ia8llO9h6UDsJiLc0mRsg5z\r\nbvCmZSS/HDQIdWng9DIudgeZAl1k7AD68aQxb8QY7rXkvJZLWA6/ZfKN+Nq/\r\nI2wE7OpI17qTjYfr0bDSVKfhArTXHPWz61/uEYRXjYKJTfFvkuTNIzmK1V6l\r\nQhqEX6vArg/StTubmIejRGS+VFEKcLfEEU4K2wFc7wFxN8ScoEKpHMKgDhQq\r\ndesZhEBYXy1zWS8qMOXyY4L5YVBrXZBQlDfcfagmeJINj25cfJehN3Qo1m1f\r\nGBUkbjzf+j1HCiWKHj9Wgog2IdeNr0xcnLH89ttKIzMe32dz+Al2usX0LoFL\r\ngxM+5Ny4z8K+6z8h27lv0T0j2OM+IAsO9wM=\r\n=iM7r\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.13_1672700236699_0.9458313761613317", "host": "s3://npm-registry-packages"}}, "0.16.14": {"name": "@esbuild/freebsd-arm64", "version": "0.16.14", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "2e3f5de2951a8ec732a3e4ec4f5d47a7c9626001", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.14.tgz", "fileCount": 3, "integrity": "sha512-xRM1RQsazSvL42BNa5XC7ytD4ZDp0ZyJcH7aB0SlYUcHexJUKiDNKR7dlRVlpt6W0DvoRPU2nWK/9/QWS4u2fw==", "signatures": [{"sig": "MEYCIQDdGmbxLapWwATMjBtWDEubWo0/JdyUKD1CHRhLyIq8kgIhANvT797uTwik9iIiQJJoSMQDoo6Yt6P93olro5Xe7B49", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd3FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUsA/+MtM2gslYtg1F/dPpl/F0n0R+IYjL9IgZvFpoHPn0xwaxbwks\r\n3cotnvYfWLWA/14Vsmk1Fnga7ULxUdY9DGD2L0g2CB4oNx66XlQ6z7ZFcUk7\r\n1zYxGoO3mIipzK7jmq62P2GoGHKryu3KE33vzRghbuyiq8PehFB9OI4OgSrW\r\naSl8sZwQLQgsmtCdE7qwB2T4QIE1asjhHwVBM1J7YR9bUdJ1kj0OyZQi8XvL\r\nvdNVtLV8Qf74g7kLnImlmQfnznQkUms+nQqk+My2SNlk0FUPkLtFCxUWFmmh\r\nxrZoMDZ4UHLDLwuRXEWa3u3js64lvKcwBJaCJssOC9kyHoQX5eHB2nkS8rK4\r\n1iDg8nFLZ60g8eV9Jv9InyNXeWXhch8H/1VR7UypnwbeNrI2+MVibcXQhnPK\r\n7twcB5YYq7Py6lcDx5vA+uqfcEOpTZvnMBrhOZ4XEvVjFVPM1NkJOARPsbPm\r\nWdweopP6DEHuZ6etBIxdws1uR2dI1bBhp+9geblJubxTFGQepeHWeYffetiO\r\nC9joUjbtdzJm5wEhUa0WRqlSpT0voX/xG638ZgqodbT1SLm9rrC9Ne2vhx8q\r\nDyHuQ1naYJomHcSZQgek/GX9YpU0TAVV2g1U5rdhV83VuDcTk6+/Z0teeO8e\r\nFHi02fKPP3GYn6ykV3KREZtfBV+YX+E3h98=\r\n=dtjz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.14_1672863173041_0.3903878935798848", "host": "s3://npm-registry-packages"}}, "0.16.15": {"name": "@esbuild/freebsd-arm64", "version": "0.16.15", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "05c19bf6e4e56387f6a56bd6933839e889146726", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.15.tgz", "fileCount": 3, "integrity": "sha512-LBWaep6RvJm5KnsKkocdVEzuwnGMjz54fcRVZ9d3R7FSEWOtPBxMhuxeA1n98JVbCLMkTPFmKN6xSnfhnM9WXQ==", "signatures": [{"sig": "MEUCIHkVVBS1uJU6rXQAC2o0vFcSN6LKwtiGqc3YjGRZ7PojAiEA+/FBNX7lNFCUyMSgKBanR3NRrlK7S7dhEgnrWuxV4kI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126954, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPKrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpQRAAmQl2YasDkNMwh+9DHagw3th1s3ZESDEThd/GZe3UwtoCwG0q\r\n56zjkpTJmawzZ2ziORVWhXxjynNjQtkie4MTUmf+iUvmMv7umg4mlRsn66si\r\nGDnPXqpOtRpbEW/uuhkJwtmBc1zNJIE42A+A8tc7Fd0Xs1UfoqhjBOcNdMrJ\r\nLJvpt9SP4dbeqoQTj+31/GQ+to6QI7HwZE//29eJJe4xlurx/2FZ8Rk/Q7b2\r\nZuGFJuev7G+IYvwnGr+rA+oPX9M5NxTAoiB/gTlTZjNBlh8PzIQerQXhLHIw\r\nddej7XuRapKwJjGtQi+JEauEU/MI0PfTf3JjBEf7mGwJX178JPOny42TNjs/\r\nQR3V75bztTumXTTuHQ7R8dloyL9Wo/WiiPuSJILdCqiSApLaxpKwVY656+s2\r\nrqchysstr3VJIN5M0n4qheHkTKshaQRCwLihTXe51ZkE9fG5ZdSGNtnKoq2Y\r\nEDiNhlp+7xT1oDHBoXwdlMMzYXjrBgpWMIcr0JUN1/xNjppj8GP7L89+b9dE\r\nbsSENMDMDTYV4ZQjOP0Z5f4Qh0Tmow2r/N0+BOQLQHtpG0hD7W4Gk19tmN0m\r\nuO0EM8+b+LoG3AR3nBza2Qhr9IMZ7yQQeiEOtc7zFEm+SYpoPrs4ZZkfc9a/\r\n0vc4MeHVmbTTqvSGc1O2ScKb+5wYF/l8dVQ=\r\n=xgTr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.15_1673065131557_0.96832769704375", "host": "s3://npm-registry-packages"}}, "0.16.16": {"name": "@esbuild/freebsd-arm64", "version": "0.16.16", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "db7bce444d372e5a213a3f359c42aee3acc0dd45", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.16.tgz", "fileCount": 3, "integrity": "sha512-fxrw4BYqQ39z/3Ja9xj/a1gMsVq0xEjhSyI4a9MjfvDDD8fUV8IYliac96i7tzZc3+VytyXX+XNsnpEk5sw5Wg==", "signatures": [{"sig": "MEYCIQCdvwIoGmBl6z9krQ9Vz8BS65ipXog9++VgnrEeBSriWwIhAO6olVu7JNuRnO7C5ZgisfrS6ruxivue54+hhN5EPbXi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8126954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0clACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDAA/8D547HKO++tyO+/LKXUnmj9hJ1uNYyKxsM9wEZmDFz3s/pyye\r\nALV5C0HX0sRPdZC0fKeuNH+fDdkIjgqXiSs7LMUK+4b6yGHUqNhv80U2rTWg\r\nP8MIX/cffR/TKKsBwj5pRFv+FyMoaUiTXkr7rD9++cKKMHUD0QHoY7+exq4W\r\nZvVvKi3Rzem53aeGwJNHG4wQL06rSzBDbY8zrWj6n0tXxYCseutCBl31IwlD\r\nOX4aWKJnWn+Ngy7nIAu91XSHFkxhhqPBbK3tTpNIuUmDZd9dy+XN4xSlok1/\r\nk3zXwYdHmHcZut9slEs7eMfZa0JvfrpJAv5ZdEz67ISt9sKHL+NtsqHlbGBk\r\nab6qNqO9Jw5BaWz62sJomxF9TB1Dslwx6WU0diMHUDufq5Dw3RN31M4a/ZW8\r\nYXnutUi/I9ftfpsGCqxMKTsUbN1Zwr08Qy5laHkRSSCZLaydUeJ4uobMFKki\r\n+umDTWg5ZDunjfoClteWYuDjyXlEMaxy13VnoBKqZzXS8NMN1MEgohZh2vsi\r\nl8XC+qbQgodsPIOTrYKL42gytP6AI2RuaAzcoDq6x+diCx1hI4GdrtvNQ4YU\r\npxil4Cnmr9Vzt7ciLiCUF0Yc6RfYSYGb8C3AHH84j8AkoNG7W9GJ9XNJPGig\r\n0qOi0ODUQdFBVCdcccLX+akt0l7QJQ10di4=\r\n=hY8y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.16_1673217828786_0.6931921853917506", "host": "s3://npm-registry-packages"}}, "0.16.17": {"name": "@esbuild/freebsd-arm64", "version": "0.16.17", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.16.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "1f4af488bfc7e9ced04207034d398e793b570a27", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.16.17.tgz", "fileCount": 3, "integrity": "sha512-mt+cxZe1tVx489VTb4mBAOo2aKSnJ33L9fr25JXpqQqzbUIw/yzIzi+NHwAXK2qYV1lEFp4OoVeThGjUbmWmdw==", "signatures": [{"sig": "MEQCIAUXwVwg4cqCAmaq66bTcwrQER0zN78ruBj4VmDAfNC6AiBHACNeATQazD4k8rEdbWKIDgrtpKJdA+Mh4kPETpZ+8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzDiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqdnw//efuC/MXeGaLXe73NZx3CsWy5oEZaCB2WtybeG6osh++TX9D6\r\nLC31EzBSS9dtobu14Bmw3XYwn+0iUKuU72SOekZnkxPeebLh+TULp69jhLJJ\r\nRw9VjWjrCRW5dVOPJFneF0dSnDhxONHJh4aqfpf4HSelr1g8T14WorclOor8\r\nL7+n9SoloAVnoNIahbycCbwhWzA2E1WzGzDOiiDnl0pgN9IIFLZjW2LtKzfI\r\na85J9SRDU5jbGjQzBy7EzTrg2qdVAvfHCcnCD5bnZYJsHOR7pG0xfCdTaKTw\r\nig5zRluCeR0a9WD9xKWp0jMNgk9L14/KJ0OlzIACJAM/jP1OA1U/wY+DJ8oU\r\nwhiV+C96E79MHG/ZOM14b3bwGhVZ4ofBAqJXWBSsbaUhGWbh1vrehLJwjAWn\r\nn5t94KW4dGzciEVBtIgSR5h0soXubdC7phJpQBNToirkMdGycImRanvtrwMW\r\nq7wlXNMFOJyUe2Zh42yFt4tkGZF84w9i9aM3ZpRAzoxTma22b27SyNe3Ak2/\r\np8ePwnsBItZSl31Y+iTDYywDSjgj34go4wzBmH0/pV0kAudjd7mOH7Y10m2G\r\niTsM2zN/wkEuW8roiJ/VjpVPghVAvjIzYCh8PqFPLOimgQ9a+E54rgCeSZlx\r\nJd+sAcgQ8aGTX+aCmuYofdgE0FMHof7fFnA=\r\n=njCF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.16.17_1673474273992_0.18965685254425946", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@esbuild/freebsd-arm64", "version": "0.17.0", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "3412ffa1703c991b4d562176881fb43a9ee6f7e3", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.0.tgz", "fileCount": 3, "integrity": "sha512-FO7+UEZv79gen2df8StFYFHZPI9ADozpFepLZCxY+O8sYLDa1rirvenmLwJiOHmeQRJ5orYedFeLk1PFlZ6t8Q==", "signatures": [{"sig": "MEQCIGeP5ZlmdM+Ym7NJOSMdtBSij1cnVvd29qS5oQfsejzjAiA/S48W1G+7LSBHrurkEg+/ukW2hs034O3kAO9TDJgoDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8454633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjCjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZDA/+P/ECMX6cd3NbbNc5KmOLCsWaNxsRWoAAwEbYijFJaAv/fvci\r\n92SjdpiyJ49U8S+b4tCpyyJDGHexYYt/jsxYob8Uh1LCL3CA0TvX0JdoP4M3\r\nFoNeJHz4lZodUY3wrGHfO9nLgYiN3nx6EWNZIxDSfXJQLpd5V22y3LrPJPkh\r\nyYr6qF79Da7+lCQIdYv9WJnSWm69gP0R6qQsm9S4mfbyihmyUeHHxJecxdrt\r\ncogCEVaV5IRlHRYQUe8DmMJa/QJafj2s+4IfWhdXWEA4UtuaKIK/P0tu29lP\r\nQ1zyojYYlrN846hJu8NuasIT35ttdmNE+8UilF+ddtJ78rIRLYdXjoC7H0KN\r\nrnerPKngPqyYQKLfgJsMzT55Gi/cKOTdsNdVCxbEB0oG9VcHWwVTqwz8gXGE\r\n09x5Y6FQ/ZBoKQzUZwu36LPMswu7gDuzJOehSK0HelhSacATSMBK5LXS7bkh\r\n/Z4jKygSrFWCdGtEwxZa9IpKUQ8B8Fab9SGVmRbenmIhkc7/lFKACypPys2q\r\nCv0AkR08fyQspc31q+BQUkcvR1MNyGrsb2CZaL+CBTTIJCKYhGlAXlayXUPo\r\nZIns41iw87qdkbY80DgN+6ynw/5cqzHnhFCZ5mSukrhcOAC3mFYzfm7NGmox\r\nE/aun+PzEChkTkGdPA9ee2RIw47unzcxcgk=\r\n=vTus\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.0_1673670819576_0.1564648055605733", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@esbuild/freebsd-arm64", "version": "0.17.1", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "22de2b3d5be3ade9066cf27e96f919d72e4cccb4", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.1.tgz", "fileCount": 3, "integrity": "sha512-yEXsg9KggF6p1qqwb2ZIKPeMv3znU9zQ4xu4fi3GmCDmqMGXpzR6vBFVYlrMgGOGN1shHcKnMp/HhEp4mdkLGQ==", "signatures": [{"sig": "MEYCIQDtS0dIYwDYRBmDQOtJcImPm13NbmTuiArwCa/Tm6dnDAIhANMUeH5sFUvUF6uVwaR65rF522snb1RGLO51eOJ09URJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8454633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZHxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHqg/+LEMqCyj4MpB4rMF0+ReG/JOrmcabgZSq9wLOcY6l9JhJu/Kt\r\n5cCruuoQc64bVkXPaL2PtxWW2ory/t5a3OdXneqUVIw5V+PbZV8XYl36rS9S\r\n3L76ck1IcRFgTJ6S0AZtZJxvxUgqgTipHNSGPmSMxwpeHUzV5jKCXrtYO2ht\r\nlTYxKSgDfgqLgXZP0iY3tGfSRmG/xwcVftYKxr4C2CRuIGIj4U43enfnfZ5q\r\nzWLYN2mN1Z2YRRaRTSDLsNHoJLBORTgmrKR2ps1p5yATcUIUxtousXPrn7Cc\r\nkeH2qAJoVgmxNFrLuUORgANgYUotH+epaV1swhF36qw5459UH8ZmV3shs3m2\r\nnBvNclW0jO9YoDuQspozS/3hZNP1DQO1VNWrD0a38pCzfggcNcTHJ4ZYMCqf\r\np/p5kYv1wayBGdSnLb1qd/F5LU/eZQ0vYrFxnme3wLbxFEoj4gZcGJqyOZyL\r\nn5nFSNIuCxQrXQ1pK2XMPkSWL3Ml9eDCMaqMwG/Na7tb6/mX5TxOBhMNeOjz\r\nmds4UC/VDO/jdl0JZN/mJ+uxMPdUGAEvIOV1LdLQ4vVaqNVAvcKZ1Ls++P7x\r\n+cAuL91ofghAPeAkmwxD3hFsg4FVAyd0fgud5GpZbkqoW6VkzFBxq1sFGrHh\r\nVaUcvdgbxHpL7GolEzYdqOVOvLKQqpRoWts=\r\n=Fo8U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.1_1673892337450_0.24838989743385453", "host": "s3://npm-registry-packages"}}, "0.17.2": {"name": "@esbuild/freebsd-arm64", "version": "0.17.2", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "0265bd51eb1951b27eb693fd4989a4154e32bd58", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.2.tgz", "fileCount": 3, "integrity": "sha512-ti7GU+/KUQQXEPmSUep7efZpA3KR2SkKsVuSL2FE7Yxka9apuqKfymAgQmVPMxstzAgCRBIu8uEu0KFmTfs3/Q==", "signatures": [{"sig": "MEUCIQDbxHXzMFkNq7V0w2HW5gFBBZQcjsuT5BDUkG7hHF5PHgIgNTtQmTwOnKKSLt1v4U6lQIbyGp0E+rcuJnHRoe5EpjY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8454633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkKqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNqw/9Ha7NP1BxcGdKm2mawpxvJOnpD3dxGGbKExozwSZM2b/mCrtN\r\nPTiWA4GKAe80p+yuRpCcHfJaUTYcomRdpCYBv7+PuDwjJIeci1sIFle7wifK\r\nUCyHquKHyh0E0kF5BvxR8rVM+hNJOnKOz09j2Szkc2x3dmG5LrGPtuF5hHo1\r\nK455MKR/Vbm4bnzyzNpT/owbDulI+bXZVh+UCJM52XbUAxbjVa6tlV7dfDIq\r\nqRszFR1KcRx0ZxTA5brEspY1SYvgnED0eyLQZY43ME3nSkb6rGtlublvzx7o\r\nAP9VCFxI1adG0bGM4fcD304RrEm0rTQ4AlWiN5NyE2SbFcWk0jj25vYHtgmJ\r\njshCOcObuEROAb8SFaW6Uuh8tmuodEMbjtgboB/aNNGbJri1h60Aq8MaI1/D\r\nnBa3B19iRO/GRNec+tBDm9NVO0SWyqEb5RzEL4ZdBMCqvT+5vO1RCNh7p5En\r\nailVNeMcBb7yAXxVugr7FVzdYFLPjSNjEGTPxl1AFQmcpbFLxi8KimP+MP/+\r\nEeb7JPYn9jtqUItJTBRSmneMVd+ej0ByMMbGswK6XslHEqpYR0C5vBpR6BSu\r\nRGxL4Zn+DoaZcQozuUVv9WtFtkdktiQojQuhr7/vfco7qA3sNgWvPMjrOCid\r\nom1XUuwd22Jnzo+COgd68+BWWQ3Bu9iAqs0=\r\n=p5nR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.2_1673937578168_0.903624654073024", "host": "s3://npm-registry-packages"}}, "0.17.3": {"name": "@esbuild/freebsd-arm64", "version": "0.17.3", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "90ec1755abca4c3ffe1ad10819cd9d31deddcb89", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.3.tgz", "fileCount": 3, "integrity": "sha512-CN62ESxaquP61n1ZjQP/jZte8CE09M6kNn3baos2SeUfdVBkWN5n6vGp2iKyb/bm/x4JQzEvJgRHLGd5F5b81w==", "signatures": [{"sig": "MEQCIEqvbZ7ASetiF3+p5r0aB+FogYJnwL0RhZ9z9xn4ip/1AiB7z0NH9q/ra01rdUnsp4S2uujvLMIq0WjT+JRGfTl3Vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8454633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEUOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyEQ/+LXTUYw08UuVYYIHcb1CO1m1tciDNlkif+Cf7pRx19H9q4yfP\r\nbZ4fezZ6Ts304ENkQ9fgSrYedmNMvlJr6k705j1J1nDSoGNwXMdWa9mETGhb\r\nHyY987Yzc7Zje20ofGzeSp42OTl2ScxA2OwadCkmGZ/4ZzCzLdwWZTA1VB5r\r\ndqx89o/b4Ad17PhSMoC8Iq2ZOxKk4nwEMHp6O9ebnfcTbwh6WDi1IvGJDmYT\r\n2pH0FziVMmdeldnE4TyftOGAKtm1CZjGcMDm/TzfyUzYz9XHNYvVf8LbhxJ5\r\ntd01yIxOFjMM4Jbye959khaqQQId9xFA/16WSu56vcCJVdSBz+hHzDGOe/Xl\r\nShKSr8OLi82v9Z4YgGE8sjLyUYZvN1puzpO5Jz+7Guj6BOtPyrobl5Gj/VUA\r\n0z+Z2lPH2Swrk3lWFUfxfye6d2eO/+ud5UKdvcgwNgsd8Stqqeq+jh6wXwbL\r\nk6aOzps4qzfG4MIZjPLoUuPcRjTFWx//hiYLsh4jUhHqw1gyHt48cFCSbVE/\r\nxVW7aHK7QRzAUaZA89cMVlFy/aCgp9dX2ziIb09W2UAWuYI/M6CibQM98VJ0\r\nA7+6WaoZYJ3/Lm6DoQaPGBixxwkINFzGQZJkf8+DnXBlUDZmE2gBIUM0NrfU\r\nhJj+p+p02n0E9ZnGDahnon+0tcZfrg/q0Lk=\r\n=uQ1I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.3_1674069261754_0.007124303599316484", "host": "s3://npm-registry-packages"}}, "0.17.4": {"name": "@esbuild/freebsd-arm64", "version": "0.17.4", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "8eaaa126d9ff24822c730f06a71ac2d1091dc1c2", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.4.tgz", "fileCount": 3, "integrity": "sha512-oH6JUZkocgmjzzYaP5juERLpJQSwazdjZrTPgLRmAU2bzJ688x0vfMB/WTv4r58RiecdHvXOPC46VtsMy/mepg==", "signatures": [{"sig": "MEYCIQCM63bPl2lNOuuixHM/ZU/+huLZc8E6SSf1Yw2fU9AzrAIhAKrzH0OMOpW8KF1dRNkguQRp/IGv92Rid0kZGY29mmTS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8454633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVkA/6AwxEv6/P/b0UZG2KrIdOf3D+P8CIT6Dryhjhl3a7t3zaZxqu\r\nESSjXmE3syl6OgVOLpWETm/6PRmWUS3oITs06r+6Apk+xIojWVKHxFSuS3HC\r\nh73V/XkeDEQ7AxAXK7g1j5OsGQ6KO0aXWl9SjGLlK6pcL2RUdTEW0Vga/4WB\r\nhPG7EAoc5Vv9JqGtUC3+MrMNPhiWwMXGKgn0/XhAnMMa2yntfSEprXI6rWED\r\nlFuq3H+ow8uGwwc9Qa7YFRmbwZRSmg7oGeDjKVwR7x9UcA2sXavkQCZxQavC\r\nB22+HzwXkcavqF9WCfRBHfPW4kJT+/ZHu5soL6zhPQeaEbLfK638KNNH3RZp\r\n5NxGmlk2SsN5WtTSd/RHVPuHXgygaKJEzLpOBVznShprmbON6n9qL+O9Rsf+\r\nN/X0+KX9jqxRij1TFILzXht0xb36c5VTa6oxrbA7SNenftVT6oc3vgzAWZU4\r\n1fky1PPYDhStuy8573nKepg3gowrAEoWE2gtSftUG98TpJvv4i5J9eeB2QPN\r\nTY0nPdwLB+r2nEhXg6NZVTD/VUoU21/RDrGmrSgWjzDl78MLok7yhVPC9zIY\r\nF5eWX92lzLz08Q9D06yqiWsm1EtYbKKxANxc4kN8vMmrVNgut2a8xolX3SUl\r\n8sypm6LUc+NtxDxOwUGtFMFFtRLXQh+QVno=\r\n=pKnd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.4_1674368017171_0.1759429720457939", "host": "s3://npm-registry-packages"}}, "0.17.5": {"name": "@esbuild/freebsd-arm64", "version": "0.17.5", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "be1dd18b7b9411f10bdc362ba8bff16386175367", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.5.tgz", "fileCount": 3, "integrity": "sha512-VbdXJkn2aI2pQ/wxNEjEcnEDwPpxt3CWWMFYmO7CcdFBoOsABRy2W8F3kjbF9F/pecEUDcI3b5i2w+By4VQFPg==", "signatures": [{"sig": "MEUCIQCNXUtYB0hzpHud5sF9ieiVT88xW/A0RfilYSDNdvpj4gIgdJXEoL1tcn8QgcCrbdR5lxKjaokb3mGWnS2FZZurp68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8454633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/3ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+Tg/+IORpIqPqzl54Pji1c+Ri+HATRFrtnhjISBJ2ecIfFQcA9Jhy\r\nmjBTpn8vfWyX6MNg043NMzuSzI3kPZ/ipoBVyWberpllncWttQd0GKWmuVfa\r\nhnSnmzluQ//63J7Swob8Q5epJETyCuFKaIaEHRvbo1eHFajOYFM02dQaZT6v\r\noXEhRQmIgHN3GYn2rMwCaA+8Mx+Ol1Gp+GwH9hQpD5cTXgy5yQ5178kdI0uM\r\nc2fxn/tlNNRwZmjmvON/AUh98wOvc8HnWqOR3trGWYI+iCzfCNvI7R/rkWb8\r\nUSTDCI4kQkPGV67on/tbjF4zobcDOBoRZDshp1U/QCd1YSSleAkmvlj9TTO3\r\nAMnh+T2/n30OeixEKnYO9lR/tssk1JeKLoZ7TCiQ/tZnDUZfPRG3OMNNJ0E8\r\nRuSgsldQF5v7QGWte/iFrp9mINFIEBnOPdJ/MuVYpgmYoD/gahfk2G/cFruF\r\nPAikSQ8aVwNmV7BU9WDXsqah5qwKFFeiZVBsBKqXdFccyJ6WB2OkXi6lbgAM\r\nb5HXAhgg2V251gq3MCqNRyftfZqB2xxZ28TA0Wm3diqYeZtdleAyX/pVg9an\r\nTPo/CmJsBuFCP49AGAvpugq84qBPEJL4Hu1JK0He7XUxny3AJ/ATIsrtulXH\r\nfwtVPPRN+C8bwQXPUYkFuJmsZ3YOdcMl/xE=\r\n=Bydq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.5_1674837464809_0.0005598243924627155", "host": "s3://npm-registry-packages"}}, "0.17.6": {"name": "@esbuild/freebsd-arm64", "version": "0.17.6", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "ea4531aeda70b17cbe0e77b0c5c36298053855b4", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.6.tgz", "fileCount": 3, "integrity": "sha512-EnUwjRc1inT4ccZh4pB3v1cIhohE2S4YXlt1OvI7sw/+pD+dIE4smwekZlEPIwY6PhU6oDWwITrQQm5S2/iZgg==", "signatures": [{"sig": "MEUCID8KhObnEr+1unJqX8O4BUKpHbEA5xdNjHOpI1rn1eVBAiEAxoUR2CCrygd1h+lEdr1SmToXSd8LDUD04U1B/IrVCP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TI4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFURAAkvF9Xo767UfzjJsrvVFkRmnNkCtsLvodtYZ5AhhTcTLxiAqW\r\nyLz7cuPAexBXKFThVwBNtX4SUFaDSRjP+jvNpFXvNKdn8yKGlcpDTy3GVbNE\r\nz86VGH046yDkP3xvKK07bs+duVugR6cIwryo53Viltkx5fqCHZLKtcqVensJ\r\n2WlyjOlWy1giyS+1Nt8Cg4BistyUItUEzW/KtK2G7aVO97c3M+eLYsRRqRH/\r\nqB5DbB2/SxBQ/utmgFOANsWDKHbu8ZRZJNP5nXNH3j6IZacg9E0QajlT1mw/\r\npKWjZQ7+IFWcU1jofjD62S/sL/ZTwVRXBTX+h/4+wUHVlN1P2ODs8HEQSqau\r\nLvsVmzso3V3n5WZoB0N71UvY71VIsa6sFYn1XhRxeKwsJe0yNsNV6q0KaXio\r\nzPUzofKIuq1EFJWXpPTLJA9sc6ARvwxr9bYHU4RTV3vXBgm/icQEQFvz2OGN\r\nmgxlHVGd+Ojw1zRYNilxCRNUY6iJlgNvUeSyNvIhdR9J5w/dpcDke9Qar60h\r\ntR7RvjE1c3ChtQOgFHEW9uJHHpdAHzRfGBcRbH1+XVmnd70bP18bii9o5tIT\r\nL2jPeB/YYJT9EusqLKZmwwyyI+t9MpxMmFwgQhurp60+rSW03yldoyhVPGpb\r\nzEYgay3KXWDUVfZOksEkUvL/Qp+a5WxVLMo=\r\n=6JG/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.6_1675702840601_0.****************", "host": "s3://npm-registry-packages"}}, "0.17.7": {"name": "@esbuild/freebsd-arm64", "version": "0.17.7", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "1dd3de24a9683c8321a4e3c42b11b32a48e791d4", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.7.tgz", "fileCount": 3, "integrity": "sha512-2<PERSON>JjeQ9kiabJkVXLM3sHkySqkL1KY8BeyLams3ITyiLW10IwDL0msU5Lq1cULCn9zNxt1Seh1I6QrqyHUvOtQw==", "signatures": [{"sig": "MEYCIQCGCDpMIY40Dsc4arfTXQ3rWTFtuOKb6Sa3VCSRvysXkQIhANWS8dl5wscHSNB+0n/8t7KXKm2ly0uZe7aiAI4NVEyD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XMgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8FQ/+LoXkf7+XSVOa9WuqGU/YEd1cW17ohDcT7to9HEIdBV6nVqMA\r\n1e0rss8k+IjmhsE77/NBA8J4lWN2b2vFl3wFeWLHC6+5+GV+VI4bl7s2IdBA\r\n0l2ANA44ygvFVA2mFAVQGmCWRF1Ygwc8C89Y4i4u+/Lxm1X8kHQh7zHDmfw4\r\nvYxj7AbLfr5Zr7MQiVu2Dgq4g8bgQl2Ny2sryQR4rW7NyGpWB/8I7w/wpxgJ\r\nnoP2+ClQJvdDvSCS5umfs7CK7YfBHD89DPXd4wB/CsH22Vk05/e+nBvR78uA\r\nxfry+66+d7fwRerJpjTLpTvHLqIvZZhwPjj4jD7t80xD3So8Bo+ZANk1NF7u\r\n+GDy4mh2MzNf/Kv0me695gsCUfrcH87i60NaNeeoBvAq1h4fPLt3lKW/Pd+b\r\nNnyFTeKMKDCZUooV1bNpvMLQmaBTJmRRLQJ1mNrzlC31VVB0S7LyvMaAXK9G\r\nnxsH22OOVm1qEfaB3bY94NdY133ZWLXxwfHPw/uschrTgmcdmJagQB/Oz6Oz\r\n3uX6/EnqEtUZOZpi+fdXQM+stcm8rCy+D4l/y5DvLtopx8Zs2Dq8QRODn7g6\r\nFn2M0vzTCojsOwMve67lUgsfD8jeVIA41rENLiz8FMFk69K0dYGWiVPR52w5\r\n3qEkBIby9H/6eSEvXI+OQ8spdDSXQarmM+U=\r\n=tXx5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.7_1675981599925_0.9166007892501364", "host": "s3://npm-registry-packages"}}, "0.17.8": {"name": "@esbuild/freebsd-arm64", "version": "0.17.8", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "e6738d0081ba0721a5c6c674e84c6e7fcea61989", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.8.tgz", "fileCount": 3, "integrity": "sha512-a/SATTaOhPIPFWvHZDoZYgxaZRVHn0/LX1fHLGfZ6C13JqFUZ3K6SMD6/HCtwOQ8HnsNaEeokdiDSFLuizqv5A==", "signatures": [{"sig": "MEUCIDJcBrXkrv1dGXZGqgmocduZX9RDzYFNlGJ/ouBjdqa7AiEA7Gd2fw90+gQorKrBvhLXItZa+Efiv6DTRrR65D7Y7hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6do5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrxog/+PyPlKx9a+4ItERKwZqIu7xtz71+eWS3bkUgwG0fC107UtpKh\r\nsRCAiYU0HhrUzCebMEgwFw88eXYLWEsyt7gv/Xu0nPxifowVS/qDkmueIMVV\r\nXaS8bZIdk7j+0dMX9mcuEXtQ1HYeLFwGQSgGZDSECXF+v2jWhe47J2dBLXE1\r\nDT0OyYVVR05w3/6k9lBFrYwbTuagRVyvW1VKPuFAh3f8lTQ9Rwu/JgdAyOIE\r\nvtBrCaXHLZiJCFPEYASpnkIihip8Sr+pUcWjSDGzFAIprT+QFarnvHhKV1E8\r\nzLvP3l+R3X1yCMv2foMFYItD4phbjGbEMgy18d3rp1Jpj0qtahw5sKEIxYO+\r\nDN7v6eWPRF889IJr7mo40IElK/28TITVCLfI2AAnAHy1/ZaAStC+OodYD6wF\r\nrmPzsNngiyrgj6ipnZStWqgeQZusZVgEO6Fs2Serrku3nQo5c2IZ/QCsdLNg\r\nvXKXzlQnICBRH0hCkCZKDmXNK2CY+gkcL+bWfZnyZLAz6xsYSUOIXpThs38F\r\neHAj3w3gRF22WSNhTGYPNgbpTUbn4NJXkXEP+2yA0LJB3vla91ML7V8Moyvu\r\ndNf4Zn6d+/huVhWSjwNPAma/ZdjxJctmoUpD/2BWeqLWCbwVbmUPBYwL0eTf\r\nGbkM1a/3fcz5tBpuYVS7D/7VQmb+/0hmpa0=\r\n=5cJ/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.8_1676270137450_0.7483391069697334", "host": "s3://npm-registry-packages"}}, "0.17.9": {"name": "@esbuild/freebsd-arm64", "version": "0.17.9", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "b357eda2c38b17bb9f3d217a063caae51caeb637", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.9.tgz", "fileCount": 3, "integrity": "sha512-l3v6bZdpZIG4RpNKObqNqJhDvqQO5JqQlU2S+KyMCbf0xQhYCbTuhu5kKY8hndM1oKhmqq6VfPWhOSf6P3XT/g==", "signatures": [{"sig": "MEQCIFp8syAzANB9BqnSOPIITiEy4gLWwrGIaMKCyc2QPZKEAiAdN3cKvQ8vGPRKxuz8u5gc8kCupxeM/lXwAfECLYO6YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mAuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2yQ/6AtTg8gxqW6eoAPjF3gW4CEhXNRE2/g8fKaTzCwpnYNxgcG2x\r\nn0QWPomAyufUqtv1ix1CIsCAaC5Wmtc0//Rb7MoDq2K2y4dh0joAbVGj0/MF\r\n22x2SXN9J1SryqNAM7MHbIqhX/LGRBUlHKwLLLxJVivlx6IV0PfsYxpqbSra\r\nRwqY5R0q8A8MS2oE85NDs8P6G6Lxw/zFgCmKUTdAPdFuiEF2YEnuRR0FQdh2\r\nsyQaAdMi73jm1IltP2gqClPxUuTXjlyQQqBCD/ox8KHBi3gCoKag94tsIZL6\r\nDpNDCV3t8LbXuGyQZDpiO7Y7RJRIcMjXh5q51nljetHHkdoL1LdWB8TCfj3W\r\noXlns3wm2+yGZwC4Hh5t0d/Kk33m29Nos4T12ALj0aw4stHQzMJ2lCAHxF6P\r\nYsVlZDa+/GFHq8KsQQKXIp+d1M0qUAAhxRL2jX1D3uASDAowIMgkt6d+K3xo\r\nxyxjTxnecyyT+o02JBEwR7I8vuankqMwKh/7UzKLMldMRssfrYlLUeBFPLEd\r\n979oiM2bVROnx91TEgLF8V4AMUWVmA6qHSXO0QkZWZZsTskqUh0E+k7mgMjq\r\nslx0EpOwMg0QR/MfcCwFxwYoB+a3+UboQTlDOEn1AiFfVr2HBmS6rGs0TXf+\r\nIKUtKmY22hVFRTV3zD3S10n7gl/EviaJxp0=\r\n=rA7x\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.9_1676828718013_0.14044273960897913", "host": "s3://npm-registry-packages"}}, "0.17.10": {"name": "@esbuild/freebsd-arm64", "version": "0.17.10", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "e8a85a46ede7c3a048a12f16b9d551d25adc8bb1", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.10.tgz", "fileCount": 3, "integrity": "sha512-ZkX40Z7qCbugeK4U5/gbzna/UQkM9d9LNV+Fro8r7HA7sRof5Rwxc46SsqeMvB5ZaR0b1/ITQ/8Y1NmV2F0fXQ==", "signatures": [{"sig": "MEUCIQCMDfMLOCK9VdEhPsg8jqrRFWbWNb1gGyFmFu5X9c7ohAIgP1me/pQ3TSkyVG/7oxhnB5pzMX/44tuKV8j9CFmXnIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87PtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzrQ//bwa5AkXa4mQGzpU6+dDA6WFpg2RSl2ribn2bHURB4jvGDoB9\r\nYStaKm7eP8g7nSPxTQISPODHZwoP0TeHE8UFxRB0q/0YH2NJWmV99LIZQnu/\r\nV/q1seRna62yk9csl02GSwdUByKbOZy/IU8Igz6u+oceVDfqU7cTIjpxCQ8A\r\nNhZvW4Vhm11hNlwI8ppEHk3j+KOtTwOAYx89VEPUT8hsV9V8i7gyKPtPb0+4\r\nIt0sfhm1+l8pRSXpQyWYNEaEoo3EmwoHMqaS9rf2YxnCFi77XOc8l3x6m1jK\r\n1OrWf6nhP4qk/XOBtLBd7LxN9FU8MJtQbXKsrAspaivLnFSx8zdlI3ZzHIV1\r\nKsVm/eK+LFYXuTO6Aq4MyhK48rdszgvAIGoa9l9CSPjqofBgvjpf5XzhBWnj\r\n+jBvtILCDeQIJgD+qGfh1AksipBYux/a/SjM59HoSvyswFhrzfouZH/qywIk\r\nVtqve+txk15pQSAowhCm4Fmu0NF3l41frNQK7sbCHqFBbd8wC0TWVeyTXEBK\r\nA5ZwIq+0vdMO17X8N1nyIxrW1UuOxN2NDqn3ClYzw1NF+gI4FQvPGhBw4IZN\r\nstAOZBBiaLdqCiCkg1Huj+a9oyjvaLyM7GsoX8sLoZT9FjE7yUCzyMPj4jOw\r\n0NZYRYxk3to1sQ9gKRzzjlYy4D6ZEIcjAKY=\r\n=W0VK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.10_1676915692706_0.7514569445646551", "host": "s3://npm-registry-packages"}}, "0.17.11": {"name": "@esbuild/freebsd-arm64", "version": "0.17.11", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "d5138e873e15f87bd4564c024dfa00ef37e623fd", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.11.tgz", "fileCount": 3, "integrity": "sha512-7EFzUADmI1jCHeDRGKgbnF5sDIceZsQGapoO6dmw7r/ZBEKX7CCDnIz8m9yEclzr7mFsd+DyasHzpjfJnmBB1Q==", "signatures": [{"sig": "MEQCIANcOCYwLAXLPZOI+8l6rhQkCNmFB7mP0yDHKZuJ+pv0AiB1EL9kh8FdjVD/US/2lUSiQAb5xh3N/5QjVzML6fCZfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8323562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAndJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJvxAAlRsOjMS3fZC5fdl/KGFg4ATSG1qYDovWfH3Hk3/V3a+VexAQ\r\nsHltAAGcKwlcaoaAT2bm51rqGmWr1WV7R7BQf6gR9Z2lyjucKVTcVan1UW3+\r\n7XYmaN8Mj0nOg0qMjNR7sGE4YOyLhJ1SMkDSFrk/y41CD/I+/huAJIhPMieN\r\nFfTSzX2w8zcA6dudbAMItt0q581WjH1Ymp3UEmy1djl1nHl7+urJhtsEppcP\r\n4UXQnLQrdII1ss9ltxwh5YVfgrsj1DkfBmWnTgzXObFDMXSXQydU+zb+1bOo\r\n7YEEqVGblGx2TkS9A8M1QdBZ/PwesDfQL+PaoL5kAyj36M15Y4s1xx1x2Iqi\r\n+/ys2o0tQDLBOWKpHbPjdNoyeKgrGNn+zCMzO5Ob7ogAVbRzd5NfiIEbmS3t\r\ndLuNYmLGWOr9iZujI0EB8M4xGlltnnoGWMGKjBXv0tU3MNHnOYdQAtPfIO2O\r\n/p056PoCwn2y2SH1Kcg1TVUj4NjGyqcRSOEL6fvDE7G4wmj/T5PenTYkOHYm\r\nU5o28nl0iKOtVqa5cF248Q0VEX3AJZC0iijNvPDT9S0/9u13/WbDeZjwnyNn\r\neBNPJLisTV+LlLN/xjhtdaVJC8Nseq+cnxHINZ1NabtuVnv5oaj3RFi5z7i+\r\nwI/gvlUPQ8h083TsUjz5KrVsVNDmw0DxSI0=\r\n=flQ1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.11_1677883209431_0.9264185842979458", "host": "s3://npm-registry-packages"}}, "0.17.12": {"name": "@esbuild/freebsd-arm64", "version": "0.17.12", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "99a18a8579d6299c449566fe91d9b6a54cf2a591", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.12.tgz", "fileCount": 3, "integrity": "sha512-OWvHzmLNTdF1erSvrfoEBGlN94IE6vCEaGEkEH29uo/VoONqPnoDFfShi41Ew+yKimx4vrmmAJEGNoyyP+OgOQ==", "signatures": [{"sig": "MEQCIB9znpcDzcRTRxQ++Dn09cxm7bQqXiziUu2/YEjMyLLSAiAUkJtwcLfl4BCLTJr4XKY6Ut0chjjCBkpObUfPM8KQNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAWyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruTA/6AmijBoBZO8HzUYd1TVhRYFovKP03kG4iBSqJaZgHQCNIdhlS\r\nrTUkU+HWtWmKgQK/ijTo0Dja8bEKeq9SviN/fuCbZQQWD7jwAygNfRAgK4lt\r\nAGPd8czdpV37hNzxNx1EZXig6N8WzzJZHtDrRA7uVXZSWoZfwIWnB9BaADuM\r\nX3MXW/4JxbnlIOo+6DhMwo2qx4XUozXeObDYNWlxRiL9Qojr7jmqxoU+Pqxv\r\ntOjr1oG5TmxpMCgGFj09WEfZ4bohC1rXGnTv8BRUc2W0RihYmSokv/CRj7v5\r\ntnJxSThKWRgtBAgg1f/8XLq4HuymBZQ/tSu3b+7EHsXHxUhdPgMpOROgdwRt\r\nx4W4GdOAwT4JGFzg00PsENxS0H18PbY9WCrALS4Prd/bu0ysJAtzToPQNcfK\r\nc/oegVrm+jQD7Cmm0BJifvIxbR1qUmNVvbrBmuBBNvkfNIjCMkFfvxm2LIMf\r\nEfmKaK+RKQTYm/AqGkmoCVVDuxrcm221lNbenqwXj4PxJOJR2I93XZt619FI\r\nVffFQQhEcFoXoBgem7t+szZcD/b3XN0wnOX5PtpKO59bsNeTA8+SOeRwVQSG\r\nTWYspwfk/1s8ayyzfBKNUGd/Zvzog+dYv8wXWcQ8Nk4fbNj5gFERVNUlFw7V\r\nXmyXI6yQWo4B5OHDohZAejaEKmilNQkaxhA=\r\n=y5Ql\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.12_1679033778110_0.08706460836244356", "host": "s3://npm-registry-packages"}}, "0.17.13": {"name": "@esbuild/freebsd-arm64", "version": "0.17.13", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "59ca41062922c759ad32421d93a6fa0e85290a6b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.13.tgz", "fileCount": 3, "integrity": "sha512-t1T5/nIf2j+FdSf1Fa3dcU0cXycr0nK4xJe52qjWa+1I249mM5NBY1ODjiabZxZ0x3CG05y4fd9bxfDLy9kQtA==", "signatures": [{"sig": "MEQCICU6+/SLTiUsdFNCgu5VnMntgZ2QhTerq5tNAB5mtP3oAiBmnCplVyKaXpTKV3YCOq0a4CQjJdHX+oq1SQSFYWVdRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfKAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHxg/9FeWAdWIEGYWTixU9+wHds/MUlmzJ0nOR2ShN9vgLi5ua9JJA\r\nELjM45l1OByf2d/iSsL4SfRt/o9oBmTVUTk7yuvYEtnA7RqhX9v1ojvUQh+6\r\nqyNac6Z9gMuR2MnLSfaHtBCd0UGSZYdZ/52PF1mW8iMR6Epc0jDc2Z0oja23\r\nNhuJrFGyXShP3BEeFBV4dFX/4qtp8ofMc9PLBlWBAO4psTHqd70hTX8tk6zk\r\nTp3+h/krEABRzJ44Jfu9fwlYvwnNnmvT94QrsjtNJKBZjysgUIO0tY6zVUfb\r\nSPnksSCdmW3btAUqSf05Tzx4KIzjfSzedPkFKbeBXt3zazzFi5tFTR1dWWbc\r\n5ElDsWdVL2SytdpaeTq4qqUdws/+0+n4GiSWYZMObzYxu21vu+OWVW5wq+nT\r\nKuj4OUrQwmnNuQJNjdeRSw3QDhPoemF/J05ZQStFQ9x73iPLQmZdQaGQpkLG\r\nBynLhiIOm5J0yKQyO7JfwLQ8MsXvY9tzNSS9kHtsBZe/l9w7gsdxDnLLwzrH\r\n2fe2QHy1i/yyjU1A5muBcm4ZINlCVWm4pMudrQ4ixRvoDY4qvK+tLzYxj/Li\r\nGk51ePz//9N40BMhYz3iWgd7RXZCAt7S5WsoxDTfSOiV5sp+JUx6aiXFhoWb\r\naOqc1WGckwDW4JAn/5iMlVpnO3swQUfHFkE=\r\n=4ePv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.13_1679684223741_0.9384267578697567", "host": "s3://npm-registry-packages"}}, "0.17.14": {"name": "@esbuild/freebsd-arm64", "version": "0.17.14", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "480923fd38f644c6342c55e916cc7c231a85eeb7", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.14.tgz", "fileCount": 3, "integrity": "sha512-z0VcD4ibeZWVQCW1O7szaLxGsx54gcCnajEJMdYoYjLiq4g1jrP2lMq6pk71dbS5+7op/L2Aod+erw+EUr28/A==", "signatures": [{"sig": "MEQCIBJNGkk51LuY4yJsTGwlYnIlaRd3jHgedFLb60rF9sVYAiBai9W/l/sI0CclIt0VdH6H68iIL6SUCCBN82AIgr7LWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7JLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3bg/9FgXTuM4LGSwratsdAOtlWb6AVa1bNlPQKJFc+3B8viBDt0HZ\r\nDU03KZJ3U0wPYMjXH7OoXmmr73zwbbZM0pxCdYro6rjXX169EdIN1uBJIc+k\r\nS/P603+40dBDpuyUbL5skUsR0RJ5wsgQSnrNtGE3s8g5XbmaLjT3lcT++Xhn\r\nNxjwYCrY7ca+dbDPK9xO5/W3sO7smZuDIbll56XSSQ9hnk9qwRJ8Xe614xmU\r\ngUBF4+dkzCOYf7wV32fHgZJVVLEAfr0YY8+/siYeOYQe4NDCtdbQ9lOOAYlV\r\n8MLDJPEiHVjAWm1M9pzwPbVeHLygpsbG0eAyDhYMTSSgzotRSmWMoLr2IeSj\r\naCclb1axNGb+UfoY04lug68zSSmueqJKSPWNyQghDIoGrXsDImJi2l4laoMM\r\nT08XCIBkdn9p1S0hCyBDCBrsaC5dgr0z41Xp7uQMRzTIrpnmur9tZgcybNJr\r\nfiLd3O60Aj9rxTduspdv/qtKz2tEFysledN/8hlvAR5xWY9VO+FrNyseH9yT\r\nZgPVH9q+Vb767S1unUCO5u3Hm6vhKGjom2Fg34HJ/XJIvKzvrbUZdtkOqoIF\r\n405R8YfB+yqdR20JrCdIbfz93C26TXJ2XJY273eByhc5FrQpnmaJpYfcxqxh\r\nZ9TGIu3A1dgNYWsP2RkDUGbAjC++SCVYStQ=\r\n=2Hx4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.14_1679798859322_0.695719427107643", "host": "s3://npm-registry-packages"}}, "0.17.15": {"name": "@esbuild/freebsd-arm64", "version": "0.17.15", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "2ad5a35bc52ebd9ca6b845dbc59ba39647a93c1a", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.15.tgz", "fileCount": 3, "integrity": "sha512-Xk9xMDjBVG6CfgoqlVczHAdJnCs0/oeFOspFap5NkYAmRCT2qTn1vJWA2f419iMtsHSLm+O8B6SLV/HlY5cYKg==", "signatures": [{"sig": "MEUCIQC9IAUGsq/V3ySxVIK2Wfz6RjJrsIO91YqKm6VtqOEfWQIgPzrjXlVWtfJE5Pzz7R3o7nzSXtN3HgXz5E71WzWLGds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK+lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrELw/+I4ivDANZGLG7BP2fd+4p/FxRaZYR08UFSdNTLu1QbFcyW+mD\r\noaotS+00g4iO8ZhSD61h6JlL1EYKsZhOe4xYoMIL8r/9AAxj3HJvp9RLkJFM\r\n3r+ukyjV71vPaaJA0gGCTFgr/Uq8JNK/DKN1v4iovBCNH6FZRtTiXpyqdRNk\r\n7H+1rkQavJSlXMzPDB2n6NzdCZceGkYQbcSycfFv+V74sxKAJvYZkbJ+k+tU\r\n7hRibFngN4cjGP6EqBQa311EjIDOvjswgN/HdY67qsLXLtqPY1JQNJzStM2J\r\nt6It7CBTGq4OI7ybuOeeFgZqiRsKt/igqIzkERPAMebU/XHLBJd5s93SaMEj\r\n9k6WMOF+a+vt2oZNIue+nAewif9tPmGNov87fCSNQgcqUbDWmrF/pGogDJmK\r\ngycIxSBhJZFy2RSU7IzP2Eohkw0yV7zHRS3PzD1EoG4j6mhORjzDhpwDT3K7\r\n1/buUC1KzQO47J0pEFTJb1V8FUyLjNY8GFCrRLNbtxj9Jaw3lFv3nriMgxUD\r\nrCq5uAuR0JQ27d4QjDT2TGvgoLmUguvZB2c2zdZ0cRKLhTeCaw+r1hkKwOYT\r\nS0ZRJW/ZcgmcgRhaeYoqVyY19voDFLx9bqm6HxweWgldNOiurc0iwbxsqTWD\r\nu3cmnpSc8BIwCuXtrsgcfI1caY/Q85j6sRk=\r\n=xMxo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.15_1680388005466_0.9827051820056762", "host": "s3://npm-registry-packages"}}, "0.17.16": {"name": "@esbuild/freebsd-arm64", "version": "0.17.16", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "b00b4cc8c2e424907cfe3a607384ab24794edd52", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.16.tgz", "fileCount": 3, "integrity": "sha512-ZqftdfS1UlLiH1DnS2u3It7l4Bc3AskKeu+paJSfk7RNOMrOxmeFDhLTMQqMxycP1C3oj8vgkAT6xfAuq7ZPRA==", "signatures": [{"sig": "MEQCIF4ufU76Cc86ZemBI+Hj2Vt64oPm2pxUI8sl8kUD9gnxAiAhTY/PbFNC/HwE7Uf1+Yo3lKX/2GoIrNoEh2KoBtZSUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5HxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroHA//QuyKnBM079e1kBRPJX6QP4VQLue+2AT7OtJzYmH4mlkpfdVl\r\ns0G5QSme41o7ysHTkd5VfY1SGW9a7Hc1nnEoH6mHb5MRsZfAmg2rXGsUt0Yx\r\nDBfh42Pw+l0+hv4cNrUIKD4bEmDv5ZlscEJjcp2v6qOKVItAn4PVMfmknb/+\r\nyqz9lY2nfDzhYV/YrNkC9JmKG7Z/Npeqnfjy0ORqkhA/kPjKSECc4FLw8hPH\r\nri/XCNXiCiITWB8Y775W1fzbTRxMnzgQzAbPGPcuO/LLDw1hHXBwfssRBvMI\r\nL45wtTSg5Hq7qxl0eM1/L/OOajl9n2WkqXfgBeTy+C+4OORq5I22hDoHxnLV\r\nBSPL+um6iLF50aK6Zn9xLM3V+VvGnrLjwEsuU7tnjexBqZwwy5fu6PZ7WNAd\r\n4i+8fiNcZoEWhC9eF63rpH0il/xR8YQI87wOZ06Um3ena7KjEh/A2jOA9pEH\r\n3gDj1dsXr7FtwwGO2lSmXKUSD6d0dXkv9dYHlK/sism+XstLcWXrDLkftOll\r\njvytNuuN2htOjHcQxM/TQnUYHZkkn/tOQ5y7U5UBf3+/7zd5jXE/z1wXxb1A\r\n5YLlcpfYYR2+QdyJobVFzVStQeSJ7oq9o9ahoJiPMtMV5r6idplT0VuQ6b7t\r\nMhSHDdcQInDR3zZ+/RnCb2fI7Pt7k1V4mH0=\r\n=Aiaq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.16_1681101297441_0.15743184128523646", "host": "s3://npm-registry-packages"}}, "0.17.17": {"name": "@esbuild/freebsd-arm64", "version": "0.17.17", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "db846ad16cf916fd3acdda79b85ea867cb100e87", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.17.tgz", "fileCount": 3, "integrity": "sha512-4PxjQII/9ppOrpEwzQ1b0pXCsFLqy77i0GaHodrmzH9zq2/NEhHMAMJkJ635Ns4fyJPFOlHMz4AsklIyRqFZWA==", "signatures": [{"sig": "MEUCIQCP4jvBIS8b2uaklMPWX23bj8m4CxV0Lv1OiXMLOoVTyQIgMlWIHIIaTv8W8PD35vmLTXeTzRVDG5GFmgQJBrbHGpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGdZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMKxAAiGKImepjlYY4EpHVDRDdo/cpTadCopidvwGsBoMFOVU41y3s\r\naWaIeROfSR2HMcRrYCsFVlbDukBNrQ4R9ozAfl9EWKAEZXNWadWeooU5lhBW\r\nR54C6f/t+4xXS5FB7Z9RcKuWcaBTnIyjVc8hp0MBqg2rqFlc4WbeG87qoM+z\r\nI+9YoPSKMBsKs4boXD9wVOMBt6DBrDSFP4TcKvGcQWtV7rrg885u3gEGe3tj\r\nsvWGYLWjr/7N03n7HbsUY+ldwRYd5RKZEsMUNgXn6ef/Sxs5sMVT8fDn9C6U\r\nViLMaG17b7dJk8CshN+q9zRKaKucb0zEi6/z8VJ0jnoLjyBeSvFF2HZYo2ut\r\nUYX9lm47P47g1rqONwxtdrScAPP22KsTwcc0/jQkqrhZuyOTrK7JPEPE1XIB\r\neDi8ct3CJcOti8Vt2Eh6XflQId1ecz+oU7qD+VfYrgSSHkEivns/V2QHBpfq\r\nsz/cRv2glZoje6fFbq+PaMtXAezs5Zep0CmvrXa8OkKr1fP57P7fnAK7P7xW\r\nv7LDBPZo1ik2VyqbOn6f9RrDpulBf+r03oG1AywaxzJAi6kKmRHDVEUSdGJs\r\nRccb7qRPd8nUpy50kYi+REi2gl4Dn7l3eCFuZrpUNcruI+GDCBOVh0uZstpr\r\ngDqDs+M2c0IffOv3WCBi+1IsnOf/ELgh/Bs=\r\n=G4Zd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.17_1681680216917_0.34275076453442077", "host": "s3://npm-registry-packages"}}, "0.17.18": {"name": "@esbuild/freebsd-arm64", "version": "0.17.18", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "b83122bb468889399d0d63475d5aea8d6829c2c2", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.18.tgz", "fileCount": 3, "integrity": "sha512-fw/ZfxfAzuHfaQeMDhbzxp9mc+mHn1Y94VDHFHjGvt2Uxl10mT4CDavHm+/L9KG441t1QdABqkVYwakMUeyLRA==", "signatures": [{"sig": "MEQCIDZBfbAOWURr7h16FuaQO0+LECb/iTCagvr6P+flNrGqAiBDUei2U6bsv4qBLv9If81QsWPF8M0yqzzekYfss98A4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREZzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEDA//QMytZcyJnzBjDixq6/x75XPx/FR2QGkfD23Q5DvJ4qkjrQcF\r\nhhvsvAVts/lKVQdtivLboIoJUqv4lTyeTXWOYs5nWB1b0Aj/ENXv0d6tgola\r\nNT3mATBkmnkEKED5pnHFIK8YH94l01mwssa22cJpSmtm3cy3BsORLokvw0s4\r\nBRf94j+TqFUr1L/z9u9hQiXNLJ1+XiHxhUvRt/EHrcRAnz2AGSr3EfiMTOEK\r\n/sjeXRwTRcCzYBCl5q/g/gCaz4ERtrpRBHlAwe78IIZM12vwTDHN0TYOIkex\r\nMM1cDF4liGK76ag6W3CIqJ/OE179PX06RflUWJ7DxQhf5Ar3u7uaBSgG5u5d\r\nDoVDUL4QQae5lWI8Ju+v0frwssZaHXYDrOQALwKoVy9o2tF46v2ooIybDhwK\r\nnd1nH2eCysvNG7hmVlIoWplMwSuqhLu0HzYSYNcH2UHYctetZOpLE9vRjLeY\r\nX3qTwa14pOqeM1W51tY3l4CcQpYKYNCvVxsViA9kzRj47qrBS0B97f5fwoAy\r\nEGaJsKktvWrKjqHBEpAyUit+jitfAH1r0+VtYQ5vypDS+0lJAujpBLP7Q7ns\r\nbguDETrBGWk00JcpO66AVdmJapzA5QRERNbpBh1hns8D6pCBun5CGWUl6aOh\r\nfuP/qicBiqoFY8f69dBccPFDDzgl5rVbUPU=\r\n=Jqpu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.18_1682196083039_0.9015919902049163", "host": "s3://npm-registry-packages"}}, "0.17.19": {"name": "@esbuild/freebsd-arm64", "version": "0.17.19", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.17.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "cacd171665dd1d500f45c167d50c6b7e539d5fd2", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.17.19.tgz", "fileCount": 3, "integrity": "sha512-pBwbc7DufluUeGdjSU5Si+P3SoMF5DQ/F/UmTSb8HXO80ZEAJmrykPyzo1IfNbAoaqw48YRpv8shwd1NoI0jcQ==", "signatures": [{"sig": "MEYCIQDvTFL6G2ifS1g8MzlUWKJvSMzKLLX0SDI9wUzOpJKjHQIhANZqWJgw4/OmUeHRAW6NALkk3vMLAUtKDGIrieS3Yuyr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389098}, "engines": {"node": ">=12"}, "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.17.19_1683936386070_0.6217556911770612", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@esbuild/freebsd-arm64", "version": "0.18.0", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "61396db6c6beb74c71088513b733374dac45b766", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.0.tgz", "fileCount": 3, "integrity": "sha512-DMazN0UGzipD0Fi1O9pRX0xfp+JC3gSnFWxTWq88Dr/odWhZzm8Jqy44LN2veYeipb1fBMxhoEp7eCr902SWqg==", "signatures": [{"sig": "MEYCIQDIEojpstqJ6uEl0UKPFu6Z3iJzEbpZVTC3XDU8ZnaLugIhAMvlwQ28TZy2IHUZgufbHwiChmZF6CNPY9UsqkjHxx7y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389097}, "engines": {"node": ">=12"}, "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.0_1686345856328_0.020634690203765294", "host": "s3://npm-registry-packages"}}, "0.18.1": {"name": "@esbuild/freebsd-arm64", "version": "0.18.1", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "72cac2daba6369a1252aa77b6f04b275f31ea7d5", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.1.tgz", "fileCount": 3, "integrity": "sha512-eY<PERSON>DR3thO96ULRf4rJcG9TJ/sQc6Z/YNe16mC/KvVeAOtzmeTXiPMETEv/iMqTCxZhYkHyQG/mYbAxPBWC2mcg==", "signatures": [{"sig": "MEYCIQCeKPWxtX5lPW7U1bjpjvjR4M1iEQf3aElDx/rFff7ZPAIhAMH3AVzHa2nEhO/+WEfZcvFxmc4pq5zsdpwh0GJ3AJL8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389097}, "engines": {"node": ">=12"}, "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.1_1686545501254_0.3930979604007001", "host": "s3://npm-registry-packages"}}, "0.18.2": {"name": "@esbuild/freebsd-arm64", "version": "0.18.2", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "64775742b7669623de3a87980f325f39aff235c0", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.2.tgz", "fileCount": 3, "integrity": "sha512-68rGMGUdgmq+c5IvseCMqY4yaa2CAY/DIILMBA6bEU1caISF7fXnV69B1uU4s3ERuVDcasVVwiAFyNxCtkS6Zg==", "signatures": [{"sig": "MEQCIBt8l75A72IUvyJId2c+Rq9BGRnlnoKMLGkqOupmBhPaAiA/SMuwH54zyQsp4JQv/l/CENVqgCcqSaa5Z4mkPiXKCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389097}, "engines": {"node": ">=12"}, "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.2_1686624025500_0.6863041226290956", "host": "s3://npm-registry-packages"}}, "0.18.3": {"name": "@esbuild/freebsd-arm64", "version": "0.18.3", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "155fbdfdfa7b3689a0ce6bec563ede1ec629c5ef", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.3.tgz", "fileCount": 3, "integrity": "sha512-XiLK1AsCk2wKxN7j8h9GXXCs8FPZhp07U0rnpwRkAVSVGgLaIWYSqpTRzKjAfqJiZlp+XKo1HwsmDdICEKB3Dg==", "signatures": [{"sig": "MEUCIAD6DOZ2AyxtnwDWpSzK/0LO6UqOe5y+Nb5j38nPO/8vAiEAy5J0rQiTqy456AWtsPOySjAyOJQL3S8UrDCCE1jPkSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389097}, "engines": {"node": ">=12"}, "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.3_1686831648841_0.2739728344669128", "host": "s3://npm-registry-packages"}}, "0.18.4": {"name": "@esbuild/freebsd-arm64", "version": "0.18.4", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "7966b77bfad31af17e82e21ecaf06e1392dcd3f3", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.4.tgz", "fileCount": 3, "integrity": "sha512-I8EOigqWnOHRin6Zp5Y1cfH3oT54bd7Sdz/VnpUNksbOtfp8IWRTH4pgkgO5jWaRQPjCpJcOpdRjYAMjPt8wXg==", "signatures": [{"sig": "MEUCIQDmaYfTHFEHGX19GPGn0vQmgSYEQ1fxjZg+za24CBkftwIgRIb+629I9949IK2v91qQJQ6TYamIUSwf0mF05iJEAKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8389097}, "engines": {"node": ">=12"}, "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.4_1686929894938_0.6507717526961037", "host": "s3://npm-registry-packages"}}, "0.18.5": {"name": "@esbuild/freebsd-arm64", "version": "0.18.5", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "7fe4d93e5352e43826fd89213809cb4c62d1236f", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.5.tgz", "fileCount": 3, "integrity": "sha512-Eg1UnkTZHfsphgcy1Wj/McNModSO/F+kqtWqvtvEZc9BAgvdwxAt11BESgBczU+Gti0G2dLvHs0Sfb3gavwhGg==", "signatures": [{"sig": "MEUCIQCV8OiExnBISlsBsHynEStNg5lJACghscaoRNyFbROEAAIgH5B+eOBZ47GJ/SmRg21n60YwTyZdSjxdTGI6tyZ8pNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520169}, "engines": {"node": ">=12"}, "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.5_1687222356036_0.09359313901335486", "host": "s3://npm-registry-packages"}}, "0.18.6": {"name": "@esbuild/freebsd-arm64", "version": "0.18.6", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "71008abc926e47d0f91eba46bf9af643ce1118d4", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.6.tgz", "fileCount": 3, "integrity": "sha512-bq10jFv42V20Kk77NvmO+WEZaLHBKuXcvEowixnBOMkaBgS7kQaqTc77ZJDbsUpXU3KKNLQFZctfaeINmeTsZA==", "signatures": [{"sig": "MEUCIAOq7Ljj9PwQsrzI9nRIe+oAdQJCwqgiHx2EXcK726W/AiEA3NENKPazSYXyULr+lpKZJSe2adXNEfJVrc5i4IEBQlg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520169}, "engines": {"node": ">=12"}, "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.6_1687303489622_0.7229146990913005", "host": "s3://npm-registry-packages"}}, "0.18.7": {"name": "@esbuild/freebsd-arm64", "version": "0.18.7", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "7a34ab611dd27f25fbc14587e2c64b6447c2d25c", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.7.tgz", "fileCount": 3, "integrity": "sha512-wKyySDdoKVOPn9eDci/b3eP3EJVAVXC3b2CiaHphhCKfh4n5pWLwj7Ue96anK1HnpcZ0Uti8Sw9xq3Im0earHA==", "signatures": [{"sig": "MEUCIFJkGfhlkcKcTMaDF7ssT4NhJtuQo6IfePDWHiUpd8uwAiEA+smnm6zrtrCA7tY8UXzpurPf2TPfDZJpRMHH9soBgjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520169}, "engines": {"node": ">=12"}, "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.7_1687574781195_0.37228270936345575", "host": "s3://npm-registry-packages"}}, "0.18.8": {"name": "@esbuild/freebsd-arm64", "version": "0.18.8", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "f94b88400f6bc947683f85adf8887118d34d70a3", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.8.tgz", "fileCount": 3, "integrity": "sha512-ur5cFSmlE5YPqD+5X9E32wJ2nBnz/Lk30QuAiotam0kx2e2f9+dgTarqaDhUKt+xJo+6OLhCpHAlogQ1TAvJrw==", "signatures": [{"sig": "MEUCIQCe75HObixY2dbcfvbrZs3jDr54D64D62Vwo/PriE0u3QIgJumk/AXx+5t1xSeJD3AvvFyqOPtGi46p3nWoH0tbiD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520169}, "engines": {"node": ">=12"}, "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.8_1687663147840_0.9763842001112895", "host": "s3://npm-registry-packages"}}, "0.18.9": {"name": "@esbuild/freebsd-arm64", "version": "0.18.9", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "3ec3bb5a70edf0b890f11b8077c69ed297e0467e", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.9.tgz", "fileCount": 3, "integrity": "sha512-Anyk3qeTKJUcxiLE8VQ6y6frVuqFc71M5TEc2EzvXchoy6oWn5eZK+MpZBVnENVMSDA4wOjDKiFsPtVhnrhHHA==", "signatures": [{"sig": "MEUCIQCF8c32ZmpIc8Lr8sqlAACYqcRx7/l5juR+e1iTdRx0yAIgLEB7b6leL/ECIIhV2TfAUIo/cyAsnqhbJLcu2ZhHC/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520169}, "engines": {"node": ">=12"}, "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.9_1687757269472_0.48704149224635995", "host": "s3://npm-registry-packages"}}, "0.18.10": {"name": "@esbuild/freebsd-arm64", "version": "0.18.10", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "eda518b8d9b38b6c17c2c58600f8b97ff7acd73b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.10.tgz", "fileCount": 3, "integrity": "sha512-QJluV0LwBrbHnYYwSKC+K8RGz0g/EyhpQH1IxdoFT0nM7PfgjE+aS8wxq/KFEsU0JkL7U/EEKd3O8xVBxXb2aA==", "signatures": [{"sig": "MEUCICU19nlx162sduQhNHkqv7Q93VMzzM9rQ6IQOb4TWEXiAiEA8qTbG4ZEinJsdO2ESRbKyqSG38cttWWYs8i1UO5sug0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520170}, "engines": {"node": ">=12"}, "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.10_1687814424095_0.6897407367011563", "host": "s3://npm-registry-packages"}}, "0.18.11": {"name": "@esbuild/freebsd-arm64", "version": "0.18.11", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "7012fb06ee3e6e0d5560664a65f3fefbcc46db2e", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.11.tgz", "fileCount": 3, "integrity": "sha512-atEyuq6a3omEY5qAh5jIORWk8MzFnCpSTUruBgeyN9jZq1K/QI9uke0ATi3MHu4L8c59CnIi4+1jDKMuqmR71A==", "signatures": [{"sig": "MEQCIFpCYzz+us9obGJC5t3cwjuPYJdY6tr0FAUjIxpX0EveAiAFIBfosH3tJo5pvARNq+qpOaRfNXQyiMadGO4rd+z4aA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520170}, "engines": {"node": ">=12"}, "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.11_1688191428061_0.3830598815674233", "host": "s3://npm-registry-packages"}}, "0.18.12": {"name": "@esbuild/freebsd-arm64", "version": "0.18.12", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "613e261c2af436c5c88df77ebcf8ba9f5da49fa8", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.12.tgz", "fileCount": 3, "integrity": "sha512-GIIHtQXqgeOOqdG16a/A9N28GpkvjJnjYMhOnXVbn3EDJcoItdR58v/pGN31CHjyXDc8uCcRnFWmqaJt24AYJg==", "signatures": [{"sig": "MEQCIEvW3qtA45lWp/T38ttOiED14QZXCvI/rBdexJ0DT4bBAiAgVCOKZKeSiZe9+7sni/BIZh2+xK5GJMPuB2b+6JlZvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520170}, "engines": {"node": ">=12"}, "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.12_1689212041213_0.3286367531163612", "host": "s3://npm-registry-packages"}}, "0.18.13": {"name": "@esbuild/freebsd-arm64", "version": "0.18.13", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "895bb37fdea886db09549119158e044f146861f0", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.13.tgz", "fileCount": 3, "integrity": "sha512-AfRPhHWmj9jGyLgW/2FkYERKmYR+IjYxf2rtSLmhOrPGFh0KCETFzSjx/JX/HJnvIqHt/DRQD/KAaVsUKoI3Xg==", "signatures": [{"sig": "MEUCIQDhlK5jPKSuprFQ25Bm9R1q23x110Ou2yTgRGvnXr/8dwIgZSfLi/Pa2X1xbDwaDqn+/IuAmHQ6An6dENI7LECYM1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520170}, "engines": {"node": ">=12"}, "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.13_1689388633765_0.1426623589778755", "host": "s3://npm-registry-packages"}}, "0.18.14": {"name": "@esbuild/freebsd-arm64", "version": "0.18.14", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "1fa876f627536b5037f4aed90545ccc330fd509b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.14.tgz", "fileCount": 3, "integrity": "sha512-h3OqR80Da4oQCIa37zl8tU5MwHQ7qgPV0oVScPfKJK21fSRZEhLE4IIVpmcOxfAVmqjU6NDxcxhYaM8aDIGRLw==", "signatures": [{"sig": "MEQCIHnenCHUmXtiwHVeUyS2w/CH5CsyPHZYJr0fqFjetuKHAiAimB0AUgdVhF0QnDMI8RnrDt67xFBTeT0Kskq5b5hWvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520170}, "engines": {"node": ">=12"}, "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.14_1689656417181_0.9570575497672345", "host": "s3://npm-registry-packages"}}, "0.18.15": {"name": "@esbuild/freebsd-arm64", "version": "0.18.15", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "575940b0fc2f52833de4f6360445586742a8ff8b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.15.tgz", "fileCount": 3, "integrity": "sha512-LoTK5N3bOmNI9zVLCeTgnk5Rk0WdUTrr9dyDAQGVMrNTh9EAPuNwSTCgaKOKiDpverOa0htPcO9NwslSE5xuLA==", "signatures": [{"sig": "MEUCIH3w54/szWwZx3dMnctu0MwDVpvi6p9iKkyk3lBdJALbAiEAtzq+fNAMIi6JdctgLOt88aclNahaziCMABfPw63s1SQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520170}, "engines": {"node": ">=12"}, "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.15_1689857590142_0.7816460132719176", "host": "s3://npm-registry-packages"}}, "0.18.16": {"name": "@esbuild/freebsd-arm64", "version": "0.18.16", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "9bdbb3f0e5f0842b21c9b8602e70c106174ac24c", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.16.tgz", "fileCount": 3, "integrity": "sha512-x35fCebhe9s979DGKbVAwXUOcTmCIE32AIqB9CB1GralMIvxdnMLAw5CnID17ipEw9/3MvDsusj/cspYt2ZLNQ==", "signatures": [{"sig": "MEUCIQDTK5rKWZ8RMfX18ukKpH4/EWbWr+eOQl0DnAYKz4kp2QIgYXXWsScILvbAvm8LjQ481hDG2HFXGJa3WPj1OrrJRNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8520170}, "engines": {"node": ">=12"}, "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.16_1690087675927_0.3440292744681155", "host": "s3://npm-registry-packages"}}, "0.18.17": {"name": "@esbuild/freebsd-arm64", "version": "0.18.17", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "934f74bdf4022e143ba2f21d421b50fd0fead8f8", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.17.tgz", "fileCount": 3, "integrity": "sha512-cjTzGa3QlNfERa0+ptykyxs5A6FEUQQF0MuilYXYBGdBxD3vxJcKnzDlhDCa1VAJCmAxed6mYhA2KaJIbtiNuQ==", "signatures": [{"sig": "MEYCIQC/K0hzFaTXbIHO69Mqd76/b6X7zJVscozfhnMGteonNAIhAJ3hXQo8kUUV7Ngh4UpYPFi/BMGRJ0+5PGAwQtwiObs3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8585706}, "engines": {"node": ">=12"}, "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.17_1690335651155_0.67976387238484", "host": "s3://npm-registry-packages"}}, "0.18.18": {"name": "@esbuild/freebsd-arm64", "version": "0.18.18", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "0aafde382df508d7863360950d5f491c07024806", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.18.tgz", "fileCount": 3, "integrity": "sha512-BHnXmexzEWRU2ZySJosU0Ts0NRnJnNrMB6t4EiIaOSel73I8iLsNiTPLH0rJulAh19cYZutsB5XHK6N8fi5eMg==", "signatures": [{"sig": "MEQCIBP7uIMSKNFM7/IRlM/PCo7McRuiVBtu6kw/ol8TBMVIAiBhQPQqkzqt1LWtYxRXMjBI7/Ee3t1mDlFg9dqC2C8Lcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651242}, "engines": {"node": ">=12"}, "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.18_1691255184506_0.40215365520959123", "host": "s3://npm-registry-packages"}}, "0.18.19": {"name": "@esbuild/freebsd-arm64", "version": "0.18.19", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "9fa91e3b08d10c0adfa71b37372a7627b26e9686", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.19.tgz", "fileCount": 3, "integrity": "sha512-G0p4EFMPZhGn/xVNspUyMQbORH3nlKTV0bFNHPIwLraBuAkTeMyxNviTe0ZXUbIXQrR1lrwniFjNFU4s+x7veQ==", "signatures": [{"sig": "MEQCIB4RqaLhqXoqYEWfl2hKP6KcqxBaArNuk4cujXfHgBY4AiBr5lBObVf0PPK64Mis+/63I3mO+dLkZO87Z4MtpJcokQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651242}, "engines": {"node": ">=12"}, "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.19_1691376673581_0.10856370010763228", "host": "s3://npm-registry-packages"}}, "0.18.20": {"name": "@esbuild/freebsd-arm64", "version": "0.18.20", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.18.20", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "98755cd12707f93f210e2494d6a4b51b96977f54", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.20.tgz", "fileCount": 3, "integrity": "sha512-yqDQHy4QHevpMAaxhhIwYPMv1NECwOvIpGCZkECn8w2WFHXjEwrBn3CeNIYsibZ/iZEUemj++M26W3cNR5h+Tw==", "signatures": [{"sig": "MEUCIQD8uk7IQNLb/qFufpNGtxhaNLEs6aqeKZpKN7CNzkFAgQIgW+wqO8ZpZTqVQgaMof+lO6p+DIp4r1c0Bd4wkbY96G8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651242}, "engines": {"node": ">=12"}, "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.18.20_1691468090176_0.15482515446282585", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@esbuild/freebsd-arm64", "version": "0.19.0", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "3f64c76dc590f79cc40acef6b22dd5eb89fc2125", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.0.tgz", "fileCount": 3, "integrity": "sha512-s7i2WcXcK0V1PJHVBe7NsGddsL62a9Vhpz2U7zapPrwKoFuxPP9jybwX8SXnropR/AOj3ppt2ern4ItblU6UQQ==", "signatures": [{"sig": "MEUCIEFX/pspogA35G81V/1bRN1zrQM8sZlW49piI34sojL6AiEApjNBXLuFB7dCgql7SbFA/uVfT0PlgYxcJb+6l2Km4+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651241}, "engines": {"node": ">=12"}, "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.0_1691509914747_0.5084936297843843", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@esbuild/freebsd-arm64", "version": "0.19.1", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "61f70529c0aa2432e0a652b63f99ef1e1f83f7d6", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.1.tgz", "fileCount": 3, "integrity": "sha512-TadKO0AaTDAPV2RyGZQ0AaiDTVYg7RsgNaA6OJjXXmoLbTs++NwHtzAmVFBq8Q/P9A11wgkv36HeyAYhWHbW1w==", "signatures": [{"sig": "MEQCIBZ7YBLCdRjIXRw545/koKYNh7KzTcWb/FULdNif6UCsAiBw27lEcCnWI5Re7exNErbD5wymAETgXpat4PsP7ZKlSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716777}, "engines": {"node": ">=12"}, "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.1_1691769445605_0.47134582550299187", "host": "s3://npm-registry-packages"}}, "0.19.2": {"name": "@esbuild/freebsd-arm64", "version": "0.19.2", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "8e478a0856645265fe79eac4b31b52193011ee06", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.2.tgz", "fileCount": 3, "integrity": "sha512-YbPY2kc0acfzL1VPVK6EnAlig4f+l8xmq36OZkU0jzBVHcOTyQDhnKQaLzZudNJQyymd9OqQezeaBgkTGdTGeQ==", "signatures": [{"sig": "MEUCIFeZSF8sgKGBTieDBy8IE9LJM3crXgNPsjJygkvm8WGmAiEAgF6L3g3G7NotS07QY/2B7UMRkjGVTSR5EK5Fav/4dxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651241}, "engines": {"node": ">=12"}, "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.2_1691978295863_0.1733462250421003", "host": "s3://npm-registry-packages"}}, "0.19.3": {"name": "@esbuild/freebsd-arm64", "version": "0.19.3", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "cf8b58ba5173440ea6124a3d0278bfe4ce181c20", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.3.tgz", "fileCount": 3, "integrity": "sha512-ERDyjOgYeKe0Vrlr1iLrqTByB026YLPzTytDTz1DRCYM+JI92Dw2dbpRHYmdqn6VBnQ9Bor6J8ZlNwdZdxjlSg==", "signatures": [{"sig": "MEUCIQCdEFzzWb3f269qJyM0jjeky2vieSnEoOHMZOhophqH1wIgBRlXCr5uCLV0bMQKsQA++pnr0i46GmOIF+8QOIuuZaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716777}, "engines": {"node": ">=12"}, "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.3_1694653939954_0.9943878047910522", "host": "s3://npm-registry-packages"}}, "0.19.4": {"name": "@esbuild/freebsd-arm64", "version": "0.19.4", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "29751a41b242e0a456d89713b228f1da4f45582f", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.4.tgz", "fileCount": 3, "integrity": "sha512-vz59ijyrTG22Hshaj620e5yhs2dU1WJy723ofc+KUgxVCM6zxQESmWdMuVmUzxtGqtj5heHyB44PjV/HKsEmuQ==", "signatures": [{"sig": "MEQCIFZ0dSxm6pUJ/XnrMzojV04Gu1us7D7ZhepytZugmETAAiB8CA7CjIPcgC8tPTdXaLuASbkIxiwC5LwwlzIk74SnQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716777}, "engines": {"node": ">=12"}, "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.4_1695865606205_0.22437270093587136", "host": "s3://npm-registry-packages"}}, "0.19.5": {"name": "@esbuild/freebsd-arm64", "version": "0.19.5", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "d554f556718adb31917a0da24277bf84b6ee87f3", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.5.tgz", "fileCount": 3, "integrity": "sha512-GGDNnPWTmWE+DMchq1W8Sd0mUkL+APvJg3b11klSGUDvRXh70JqLAO56tubmq1s2cgpVCSKYywEiKBfju8JztQ==", "signatures": [{"sig": "MEYCIQDZbLnbAMUziuOvEUHRr6WZoFaj+8VLkFF9vHB5Ia9mjgIhAITCjjVRDL5+TjJT9zJD8VMimhAsTEqFHgm3YqEAcrSs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716777}, "engines": {"node": ">=12"}, "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.5_1697519429060_0.3384698925572498", "host": "s3://npm-registry-packages"}}, "0.19.6": {"name": "@esbuild/freebsd-arm64", "version": "0.19.6", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "8af07bd848afa2470b8a2339b203ce29a721152b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.6.tgz", "fileCount": 3, "integrity": "sha512-J53d0jGsDcLzWk9d9SPmlyF+wzVxjXpOH7jVW5ae7PvrDst4kiAz6sX+E8btz0GB6oH12zC+aHRD945jdjF2Vg==", "signatures": [{"sig": "MEQCIBbFwLpGoE577you2abmpeWM1WpNODcxnAO0ETNPcbiGAiBXhZdkzaI4fLjxuhmpnYq6EPX9NfscX4xrTPZOZQ3FTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8716777}, "engines": {"node": ">=12"}, "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.6_1700377887625_0.9129495219093318", "host": "s3://npm-registry-packages"}}, "0.19.7": {"name": "@esbuild/freebsd-arm64", "version": "0.19.7", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "bc6a69b9a7915da278f0a5ebaec069c813982c22", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.7.tgz", "fileCount": 3, "integrity": "sha512-+y2YsUr0CxDFF7GWiegWjGtTUF6gac2zFasfFkRJPkMAuMy9O7+2EH550VlqVdpEEchWMynkdhC9ZjtnMiHImQ==", "signatures": [{"sig": "MEUCIG7bo2kJAegTLxtCp2a2l5/+D3FVas3aEopt48hMuXFQAiEA6oo9cKdHPGTg3v9PQ7l0aA2b7qgOO7VVxTn3PpPxW6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8782313}, "engines": {"node": ">=12"}, "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.7_1700528451603_0.6091550768152172", "host": "s3://npm-registry-packages"}}, "0.19.8": {"name": "@esbuild/freebsd-arm64", "version": "0.19.8", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "9d7259fea4fd2b5f7437b52b542816e89d7c8575", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.8.tgz", "fileCount": 3, "integrity": "sha512-WAnPJSDattvS/XtPCTj1tPoTxERjcTpH6HsMr6ujTT+X6rylVe8ggxk8pVxzf5U1wh5sPODpawNicF5ta/9Tmw==", "signatures": [{"sig": "MEUCIQCUkFif4MFqttyzNtHlvtiZ4G2GCTu3HKSBHDW6Fqyi0wIgZCa+fLI2SQiT9uIkw7eX6WlNVDMwur+E4VE11Bgqn4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8782313}, "engines": {"node": ">=12"}, "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.8_1701040072182_0.3886443254085463", "host": "s3://npm-registry-packages"}}, "0.19.9": {"name": "@esbuild/freebsd-arm64", "version": "0.19.9", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "63d4f603e421252c3cd836b18d01545be7c6c440", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.9.tgz", "fileCount": 3, "integrity": "sha512-uFQyd/o1IjiEk3rUHSwUKkqZwqdvuD8GevWF065eqgYfexcVkxh+IJgwTaGZVu59XczZGcN/YMh9uF1fWD8j1g==", "signatures": [{"sig": "MEQCIGfxhKTH5G5+Yl0BxugFYTzw2vZuBQDmoQowCmn87QijAiAzV3tfKGXLcCJipCP/vAbHvce3ci8mFJHVnziPS1rvkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847849}, "engines": {"node": ">=12"}, "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.9_1702184961727_0.6469054912121941", "host": "s3://npm-registry-packages"}}, "0.19.10": {"name": "@esbuild/freebsd-arm64", "version": "0.19.10", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "16904879e34c53a2e039d1284695d2db3e664d57", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.10.tgz", "fileCount": 3, "integrity": "sha512-dMtk1wc7FSH8CCkE854GyGuNKCewlh+7heYP/sclpOG6Cectzk14qdUIY5CrKDbkA/OczXq9WesqnPl09mj5dg==", "signatures": [{"sig": "MEQCIFx19siWiUZFaA6m2wWlBajB9Adi8D3tTQL6WYvj69h8AiBLls7GrW3VNQJR+P9yBxtvSLlAZ1INun2PvaTg41/9qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847850}, "engines": {"node": ">=12"}, "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.10_1702945285128_0.6920573728743593", "host": "s3://npm-registry-packages"}}, "0.19.11": {"name": "@esbuild/freebsd-arm64", "version": "0.19.11", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "d478b4195aa3ca44160272dab85ef8baf4175b4a", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.11.tgz", "fileCount": 3, "integrity": "sha512-lhoSp5K6bxKRNdXUtHoNc5HhbXVCS8V0iZmDvyWvYq9S5WSfTIHU2UGjcGt7UeS6iEYp9eeymIl5mJBn0yiuxA==", "signatures": [{"sig": "MEYCIQDycEcc8zieh+eJlQruxK1fkPQdKnekTwiBK8H2MW73+wIhAIzPqd3UIsp48z8tHHhiD8fDyxhr9R6OZwR+2y4gIkCQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847850}, "engines": {"node": ">=12"}, "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.11_1703881905333_0.8575608932117844", "host": "s3://npm-registry-packages"}}, "0.19.12": {"name": "@esbuild/freebsd-arm64", "version": "0.19.12", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.19.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "1ee4d8b682ed363b08af74d1ea2b2b4dbba76487", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.19.12.tgz", "fileCount": 3, "integrity": "sha512-4aRvFIXmwAcDBw9AueDQ2YnGmz5L6obe5kmPT8Vd+/+x/JMVKCgdcRwH6APrbpNXsPz+K653Qg8HB/oXvXVukA==", "signatures": [{"sig": "MEUCIQCxc2UyrgILdgeDmo3bSNfRjQ7nSMnp0YrcRW1yJvPTaQIgCa0KQJl1xNRlYccdWU8VIxgoifZdvUYJty+MFM9LOzY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847850}, "engines": {"node": ">=12"}, "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.19.12_1706031618771_0.6978199423960396", "host": "s3://npm-registry-packages"}}, "0.20.0": {"name": "@esbuild/freebsd-arm64", "version": "0.20.0", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.20.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "84178986a3138e8500d17cc380044868176dd821", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.20.0.tgz", "fileCount": 3, "integrity": "sha512-kQ7jYdlKS335mpGbMW5tEe3IrQFIok9r84EM3PXB8qBFJPSc6dpWfrtsC/y1pyrz82xfUIn5ZrnSHQQsd6jebQ==", "signatures": [{"sig": "MEYCIQCOMBrWzUABmQojAO4bs95adxnH4eKFr53zNi/L/qY70QIhAL3qwov2/3YEEnZageBW/uONdXeAkKy0bL4oukIo4VNJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847893}, "engines": {"node": ">=12"}, "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.20.0_1706374166892_0.9556145276450985", "host": "s3://npm-registry-packages"}}, "0.20.1": {"name": "@esbuild/freebsd-arm64", "version": "0.20.1", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.20.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "a1646fa6ba87029c67ac8a102bb34384b9290774", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.20.1.tgz", "fileCount": 3, "integrity": "sha512-UyW1WZvHDuM4xDz0jWun4qtQFauNdXjXOtIy7SYdf7pbxSWWVlqhnR/T2TpX6LX5NI62spt0a3ldIIEkPM6RHw==", "signatures": [{"sig": "MEQCIBjAAsU9CK4EI/GOXxgePOvuBgTJnD5NgiqKSSbYqFV5AiA9UwM6716mqLxYs4yTMzcTy8ZTmrjmoGQxoKCqlilYVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847893}, "engines": {"node": ">=12"}, "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.20.1_1708324676257_0.5039387948053808", "host": "s3://npm-registry-packages"}}, "0.20.2": {"name": "@esbuild/freebsd-arm64", "version": "0.20.2", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.20.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "d71502d1ee89a1130327e890364666c760a2a911", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.20.2.tgz", "fileCount": 3, "integrity": "sha512-d3qI41G4SuLiCGCFGUrKsSeTXyWG6yem1KcGZVS+3FYlYhtNoNgYrWcvkOoaqMhwXSMrZRl69ArHsGJ9mYdbbw==", "signatures": [{"sig": "MEYCIQCPN/EpSHaB+Y03P+5IwcbDaR8+MWza777U6D9MccMO2AIhAPCXCy7JXuMQqAOvHRV/tdyqk4IDIRURYAqmEVLdZVb8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847893}, "engines": {"node": ">=12"}, "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.20.2_1710445776651_0.5464712962016711", "host": "s3://npm-registry-packages"}}, "0.21.0": {"name": "@esbuild/freebsd-arm64", "version": "0.21.0", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.21.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "8ccf4efef88889d1807b63345d4f80e55ef4f767", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.0.tgz", "fileCount": 3, "integrity": "sha512-g8/wBRLbsjryMBo4PGg050I1fn4qrJobkxpT1OekO6I4H2HVQfVfBAvGPhwzc9tr8CUVu0pSGSz9oDPGIjhLNw==", "signatures": [{"sig": "MEQCIDm6NqeXDW5a7ViT3LaAnD4WsGZYBIg/jr3gDXlV95xVAiBCSrn1YcIQgC7GLEjjk3FU66pBTatWkWryoGLJk6SWPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978965}, "engines": {"node": ">=12"}, "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.21.0_1715050340863_0.4805686256363195", "host": "s3://npm-registry-packages"}}, "0.21.1": {"name": "@esbuild/freebsd-arm64", "version": "0.21.1", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.21.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "f0f3bc20c23af999bd696099a324dceb66d77761", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.1.tgz", "fileCount": 3, "integrity": "sha512-/uVdqqpNKXIxT6TyS/oSK4XE4xWOqp6fh4B5tgAwozkyWdylcX+W4YF2v6SKsL4wCQ5h1bnaSNjWPXG/2hp8AQ==", "signatures": [{"sig": "MEYCIQCTJiXSUIjriW0riJ1O7tImMCoBYPAhly2Sf0/xWFTRgQIhAPQM50ylk37G/VXggAGT+PelwlOb8kLqwqZDYMzQ2n1A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978965}, "engines": {"node": ">=12"}, "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.21.1_1715100890735_0.5181911977086946", "host": "s3://npm-registry-packages"}}, "0.21.2": {"name": "@esbuild/freebsd-arm64", "version": "0.21.2", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.21.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "f665703471824e67ff5f62e6c9ed298f3c363b1b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.2.tgz", "fileCount": 3, "integrity": "sha512-4kbOGdpA61CXqadD+Gb/Pw3YXamQGiz9mal/h93rFVSjr5cgMnmJd/gbfPRm+3BMifvnaOfS1gNWaIDxkE2A3A==", "signatures": [{"sig": "MEQCIEpA8G8uUXxTGaUEZX35m9SRjkIvR4fvPDmOYVk0W7KxAiBEZ40DFf0BfrI+vkmkvhOKttuRuKvs3YLa5CPQ5nPBcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978965}, "engines": {"node": ">=12"}, "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.21.2_1715545971215_0.7664415074764732", "host": "s3://npm-registry-packages"}}, "0.21.3": {"name": "@esbuild/freebsd-arm64", "version": "0.21.3", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.21.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "f6b29e07bce25c545f6f7bb031d3be6a6ea1dc50", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.3.tgz", "fileCount": 3, "integrity": "sha512-fsNAAl5pU6wmKHq91cHWQT0Fz0vtyE1JauMzKotrwqIKAswwP5cpHUCxZNSTuA/JlqtScq20/5KZ+TxQdovU/g==", "signatures": [{"sig": "MEQCIHgJ+A22vsMiNfUiwofntebKYEGARVf9xGyMj86pEaHNAiBypi/f69bA1vv8Rle8xPyupei/wGaQw7JVl6nOI7xxqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978965}, "engines": {"node": ">=12"}, "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.21.3_1715806345214_0.3235451416714765", "host": "s3://npm-registry-packages"}}, "0.21.4": {"name": "@esbuild/freebsd-arm64", "version": "0.21.4", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.21.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "91cbad647c079bf932086fbd4749d7f563df67b8", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.4.tgz", "fileCount": 3, "integrity": "sha512-8JfuSC6YMSAEIZIWNL3GtdUT5NhUA/CMUCpZdDRolUXNAXEE/Vbpe6qlGLpfThtY5NwXq8Hi4nJy4YfPh+TwAg==", "signatures": [{"sig": "MEUCIFgEZWuB5mis0lcFESr5+IbWP00awxjRlj5cxPBgJPmcAiEA8Cfjb4uQN7Cenpk0+kIGw5QhAontRuU0fYw8qEdIpcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978965}, "engines": {"node": ">=12"}, "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.21.4_1716603046575_0.8213420588376896", "host": "s3://npm-registry-packages"}}, "0.21.5": {"name": "@esbuild/freebsd-arm64", "version": "0.21.5", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.21.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "646b989aa20bf89fd071dd5dbfad69a3542e550e", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz", "fileCount": 3, "integrity": "sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==", "signatures": [{"sig": "MEYCIQC1ReaQnbdDGcGniw/gDVg9Bpcv07i/ZpFTDw58U8wPxAIhAKm7zfjembn9ZX5nLZ6LnQcEpyKK9+udlJxOWm3/f+Go", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978965}, "engines": {"node": ">=12"}, "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.21.5_1717967811884_0.7065954691136056", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "@esbuild/freebsd-arm64", "version": "0.22.0", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.22.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "4251e0a14716116f4fa7e22d908f47408b6c2fb5", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.22.0.tgz", "fileCount": 3, "integrity": "sha512-0e1ZgoobJzaGnR4reD7I9rYZ7ttqdh1KPvJWnquUoDJhL0rYwdneeLailBzd2/4g/U5p4e5TIHEWa68NF2hFpQ==", "signatures": [{"sig": "MEQCIHFsOQccHbvA9892o7TDDyGNkibyyUpcLxm2KZmIKEo2AiBTOpiKkHGmP+mw9PoymNJkuD5FkLCEmYbfVb6WkN0gnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306807}, "engines": {"node": ">=18"}, "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.22.0_1719779861696_0.6626771154692206", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "@esbuild/freebsd-arm64", "version": "0.23.0", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.23.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "30f4fcec8167c08a6e8af9fc14b66152232e7fb4", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.23.0.tgz", "fileCount": 3, "integrity": "sha512-0muYWCng5vqaxobq6LB3YNtevDFSAZGlgtLoAc81PjUfiFz36n4KMpwhtAd4he8ToSI3TGyuhyx5xmiWNYZFyw==", "signatures": [{"sig": "MEYCIQDCznzDDFKzFmCbfwHjt5qekIg3kdY9VJyHqz4D3HBL8AIhAKhy3dGhV+3+NbnCeDR8YMrlOIDeUMSjXmjboAfsOQXW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306807}, "engines": {"node": ">=18"}, "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.23.0_1719891216836_0.6844485885243916", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "@esbuild/freebsd-arm64", "version": "0.23.1", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.23.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "f9220dc65f80f03635e1ef96cfad5da1f446f3bc", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.23.1.tgz", "fileCount": 3, "integrity": "sha512-h1k6yS8/pN/NHlMl5+v4XPfikhJulk4G+tKGFIOwURBSFzE8bixw1ebjluLOjfwtLqY0kewfjLSrO6tN2MgIhA==", "signatures": [{"sig": "MEQCIG73DOJ7E/ERmK72jgCp4odrKZwzQUR1eAEpTtKejUotAiBW1Z8CLPKccgZcFVZHHTXY1IUSLfmKp2o305ZJL6OBEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306807}, "engines": {"node": ">=18"}, "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.23.1_1723846388107_0.270689011292367", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "@esbuild/freebsd-arm64", "version": "0.24.0", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.24.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "bb76e5ea9e97fa3c753472f19421075d3a33e8a7", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.24.0.tgz", "fileCount": 3, "integrity": "sha512-6Mtdq5nHggwfDNLAHkPlyLBpE5L6hwsuXZX8XNmHno9JuL2+bg2BX5tRkwjyfn6sKbxZTq68suOjgWqCicvPXA==", "signatures": [{"sig": "MEQCIBG2GU/Ll2j2JhXpb8FkM5otwi32YV7/igDjkTm34n9eAiB5YSo0H1UtgPLr79oEuu1AxCm5FSKgApOBaDvbkfvYjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503415}, "engines": {"node": ">=18"}, "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.24.0_1726970776447_0.5559711971689629", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "@esbuild/freebsd-arm64", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "e7af88b49d4fab4ff195a994746c196eb43a65f4", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.24.1.tgz", "fileCount": 3, "integrity": "sha512-Kkl8APGvkp1S1g9tttiicChe49p+A3198sISIVcUGECqDPFXk9hSmVrUmaoCZWKo/zGK9TgLczLRLXduuhLplw==", "signatures": [{"sig": "MEUCIQDCQkAVXVtHUC5aGWfuz+mFkXb8SBvqVb5Vf3ih63Y35gIgYQMZ0KFxnf2h/DLgkg8onJnBwZ3o27guGZNr6ZpGqo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503415}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.24.1_1734673232328_0.610964803916531", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/freebsd-arm64", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "5e82f44cb4906d6aebf24497d6a068cfc152fa00", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.24.2.tgz", "fileCount": 3, "integrity": "sha512-UN8HXjtJ0k/Mj6a9+5u6+2eZ2ERD7Edt1Q9IZiB5UZAIdPnVKDoG7mdTVGhHJIeEml60JteamR3qhsr1r8gXvg==", "signatures": [{"sig": "MEUCIQCwkzPAjEh1CoKiEKbKGOx9MZlnnNo3y1SPgJqzwM6CEwIgPU8ViCVgFb6tf1H925ztK0a+qdBn9dAvQlYYlwe2PPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9503415}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.24.2_1734717352808_0.36189000564844487", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/freebsd-arm64", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "b97e97073310736b430a07b099d837084b85e9ce", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.0.tgz", "fileCount": 3, "integrity": "sha512-VN4ocxy6dxefN1MepBx/iD1dH5K8qNtNe227I0mnTRjry8tj5MRk4zprLEdG8WPyAPb93/e4pSgi1SoHdgOa4w==", "signatures": [{"sig": "MEQCIHDqU9K2Bz+ilkil0CSjaa4nKD/dRtTSSYgqIQKGKK8ZAiADQRdk4xV2mjmpi6ZsvuwP9Qulp+WJiuzclMpsDBwzRg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9503415}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.25.0_1738983720661_0.8177776104260217", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/freebsd-arm64", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "cff18da5469c09986b93e87979de5d6872fe8f8e", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.1.tgz", "fileCount": 3, "integrity": "sha512-1MrCZs0fZa2g8E+FUo2ipw6jw5qqQiH+tERoS5fAfKnRx6NXH31tXBKI3VpmLijLH6yriMZsxJtaXUyFt/8Y4A==", "signatures": [{"sig": "MEYCIQDNBeJi90zFV9Jewh5OU8XC4Q1d5Qxb/kQ1iWjQb/xe1wIhALmhqAlRKBqxtCqSCkRWDk5Af9qS0SFBZZOSzVQguo6h", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9503415}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.25.1_1741578308978_0.2101531074505658", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/freebsd-arm64", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "67efceda8554b6fc6a43476feba068fb37fa2ef6", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.2.tgz", "fileCount": 3, "integrity": "sha512-mLwm4vXKiQ2UTSX4+I<PERSON>iPdiHjiZhIaE9QvC7sw0tZ6HoNMjYAqQpGyui5VRIi5sGd+uWq940gdCbY3VLvsO1w==", "signatures": [{"sig": "MEUCIQCX053UirIkBUpde2qwA8ob+H+sFKXjrPKutV3XkqxCYwIgVAPNpezKlHeeNK/hfhpX953XR8mn4SMSLu+BYYhmHqA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9503415}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.25.2_1743355961725_0.9206018798572067", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/freebsd-arm64", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "9f7d789e2eb7747d4868817417cc968ffa84f35b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.3.tgz", "fileCount": 3, "integrity": "sha512-EJiyS70BYybOBpJth3M0KLOus0n+RRMKTYzhYhFeMwp7e/RaajXvP+BWlmEXNk6uk+KAu46j/kaQzr6au+JcIw==", "signatures": [{"sig": "MEYCIQDMg8KdrFrdmC0Mo9EtbxkDg64+hmbmenfXd0T2a1to7wIhALaWMI1A0GJPO1Y7Hrccrh/Q4DYZ2k07JebQEoeTJjXG", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9503415}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.25.3_1745380532983_0.1853729115604832", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/freebsd-arm64", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/freebsd-arm64@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["arm64"], "dist": {"shasum": "dc11a73d3ccdc308567b908b43c6698e850759be", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.4.tgz", "fileCount": 3, "integrity": "sha512-yYq+39NlTRzU2XmoPW4l5Ifpl9fqSk0nAJYM/V/WUGPEFfek1epLHJIkTQM6bBs1swApjO5nWgvr843g6TjxuQ==", "signatures": [{"sig": "MEQCID0fXhiNInxiYNj/1kDRO6+ospQQcBihFJz1FGVw9cQ4AiAGTqKNVwE0SsBZS/AVzlmrM5pn9icxGE7VB9XLEC8ysA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9503415}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-arm64_0.25.4_1746491427441_0.7032379603141763", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/freebsd-arm64", "version": "0.25.5", "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["freebsd"], "cpu": ["arm64"], "_id": "@esbuild/freebsd-arm64@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw==", "shasum": "97cede59d638840ca104e605cdb9f1b118ba0b1c", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz", "fileCount": 3, "unpackedSize": 9503415, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCt0mSv/sg90ZLXX17wmS2Xav0zWRiwQMS8O+U5+lgV/wIhAI5GVLNfS9bdI6cobHlkIx6MmRFcWflw7TZtdSjP1wI5"}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/freebsd-arm64_0.25.5_1748315547816_0.809150271693176"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-12-05T23:43:11.577Z", "modified": "2025-05-27T03:12:28.314Z", "0.15.18": "2022-12-05T23:43:11.940Z", "0.16.0": "2022-12-07T03:54:58.784Z", "0.16.1": "2022-12-07T04:48:08.443Z", "0.16.2": "2022-12-08T06:59:43.480Z", "0.16.3": "2022-12-08T20:12:52.209Z", "0.16.4": "2022-12-10T03:50:25.373Z", "0.16.5": "2022-12-13T17:47:37.302Z", "0.16.6": "2022-12-14T05:23:09.599Z", "0.16.7": "2022-12-14T22:46:51.950Z", "0.16.8": "2022-12-16T23:38:35.580Z", "0.16.9": "2022-12-18T04:31:17.088Z", "0.16.10": "2022-12-19T23:26:30.605Z", "0.16.11": "2022-12-27T01:39:03.851Z", "0.16.12": "2022-12-28T02:04:39.790Z", "0.16.13": "2023-01-02T22:57:16.954Z", "0.16.14": "2023-01-04T20:12:53.357Z", "0.16.15": "2023-01-07T04:18:51.852Z", "0.16.16": "2023-01-08T22:43:49.004Z", "0.16.17": "2023-01-11T21:57:54.305Z", "0.17.0": "2023-01-14T04:33:39.928Z", "0.17.1": "2023-01-16T18:05:37.676Z", "0.17.2": "2023-01-17T06:39:38.443Z", "0.17.3": "2023-01-18T19:14:22.022Z", "0.17.4": "2023-01-22T06:13:37.423Z", "0.17.5": "2023-01-27T16:37:45.083Z", "0.17.6": "2023-02-06T17:00:40.875Z", "0.17.7": "2023-02-09T22:26:40.181Z", "0.17.8": "2023-02-13T06:35:37.713Z", "0.17.9": "2023-02-19T17:45:18.226Z", "0.17.10": "2023-02-20T17:54:52.978Z", "0.17.11": "2023-03-03T22:40:09.746Z", "0.17.12": "2023-03-17T06:16:18.387Z", "0.17.13": "2023-03-24T18:57:04.010Z", "0.17.14": "2023-03-26T02:47:39.531Z", "0.17.15": "2023-04-01T22:26:45.728Z", "0.17.16": "2023-04-10T04:34:57.704Z", "0.17.17": "2023-04-16T21:23:37.186Z", "0.17.18": "2023-04-22T20:41:23.284Z", "0.17.19": "2023-05-13T00:06:26.500Z", "0.18.0": "2023-06-09T21:24:16.557Z", "0.18.1": "2023-06-12T04:51:41.555Z", "0.18.2": "2023-06-13T02:40:25.828Z", "0.18.3": "2023-06-15T12:20:49.139Z", "0.18.4": "2023-06-16T15:38:15.239Z", "0.18.5": "2023-06-20T00:52:36.331Z", "0.18.6": "2023-06-20T23:24:49.903Z", "0.18.7": "2023-06-24T02:46:21.462Z", "0.18.8": "2023-06-25T03:19:08.072Z", "0.18.9": "2023-06-26T05:27:49.826Z", "0.18.10": "2023-06-26T21:20:24.389Z", "0.18.11": "2023-07-01T06:03:48.329Z", "0.18.12": "2023-07-13T01:34:01.545Z", "0.18.13": "2023-07-15T02:37:14.047Z", "0.18.14": "2023-07-18T05:00:17.458Z", "0.18.15": "2023-07-20T12:53:10.413Z", "0.18.16": "2023-07-23T04:47:56.159Z", "0.18.17": "2023-07-26T01:40:51.485Z", "0.18.18": "2023-08-05T17:06:24.751Z", "0.18.19": "2023-08-07T02:51:13.855Z", "0.18.20": "2023-08-08T04:14:50.421Z", "0.19.0": "2023-08-08T15:51:55.043Z", "0.19.1": "2023-08-11T15:57:25.836Z", "0.19.2": "2023-08-14T01:58:16.199Z", "0.19.3": "2023-09-14T01:12:20.228Z", "0.19.4": "2023-09-28T01:46:46.601Z", "0.19.5": "2023-10-17T05:10:29.400Z", "0.19.6": "2023-11-19T07:11:27.894Z", "0.19.7": "2023-11-21T01:00:51.864Z", "0.19.8": "2023-11-26T23:07:52.472Z", "0.19.9": "2023-12-10T05:09:21.973Z", "0.19.10": "2023-12-19T00:21:25.471Z", "0.19.11": "2023-12-29T20:31:45.618Z", "0.19.12": "2024-01-23T17:40:19.033Z", "0.20.0": "2024-01-27T16:49:27.130Z", "0.20.1": "2024-02-19T06:37:56.496Z", "0.20.2": "2024-03-14T19:49:36.911Z", "0.21.0": "2024-05-07T02:52:21.193Z", "0.21.1": "2024-05-07T16:54:50.987Z", "0.21.2": "2024-05-12T20:32:51.484Z", "0.21.3": "2024-05-15T20:52:25.494Z", "0.21.4": "2024-05-25T02:10:46.838Z", "0.21.5": "2024-06-09T21:16:52.135Z", "0.22.0": "2024-06-30T20:37:42.092Z", "0.23.0": "2024-07-02T03:33:37.066Z", "0.23.1": "2024-08-16T22:13:08.459Z", "0.24.0": "2024-09-22T02:06:16.748Z", "0.24.1": "2024-12-20T05:40:32.605Z", "0.24.2": "2024-12-20T17:55:53.052Z", "0.25.0": "2025-02-08T03:02:01.131Z", "0.25.1": "2025-03-10T03:45:09.221Z", "0.25.2": "2025-03-30T17:32:41.990Z", "0.25.3": "2025-04-23T03:55:33.251Z", "0.25.4": "2025-05-06T00:30:27.701Z", "0.25.5": "2025-05-27T03:12:28.120Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "The FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the FreeBSD ARM 64-bit binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "readmeFilename": "README.md"}