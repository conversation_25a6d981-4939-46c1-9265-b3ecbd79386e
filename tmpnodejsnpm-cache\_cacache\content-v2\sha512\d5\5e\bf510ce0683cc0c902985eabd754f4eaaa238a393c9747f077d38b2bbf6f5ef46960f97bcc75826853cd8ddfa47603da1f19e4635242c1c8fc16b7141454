{"_id": "lightningcss-win32-arm64-msvc", "_rev": "11-a4580e8f5349ca13c04ab9a787b70a68", "name": "lightningcss-win32-arm64-msvc", "dist-tags": {"latest": "1.30.1"}, "versions": {"1.26.0": {"name": "lightningcss-win32-arm64-msvc", "version": "1.26.0", "license": "MPL-2.0", "_id": "lightningcss-win32-arm64-msvc@1.26.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "77790f68891e01a520df1a97dc669b0f5546ac3c", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.26.0.tgz", "fileCount": 4, "integrity": "sha512-X/597/cFnCogy9VItj/+7Tgu5VLbAtDF7KZDPdSw0MaL6FL940th1y3HiOzFIlziVvAtbo0RB3NAae1Oofr+Tw==", "signatures": [{"sig": "MEQCIHzcpLL46qCZlVuoQkZ5TW59K0qzWYE5J/6qfSH62as/AiBOneLucVOUn0uXK+QRgHVO1VdX2UGXj/1QrqosVDLiZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7571057}, "main": "lightningcss.win32-arm64-msvc.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "0bcd896e81a8a2c5d847f626a37e2cffea79e2a0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-win32-arm64-msvc_1.26.0_1722958382447_0.9814024432048603", "host": "s3://npm-registry-packages"}}, "1.27.0": {"name": "lightningcss-win32-arm64-msvc", "version": "1.27.0", "license": "MPL-2.0", "_id": "lightningcss-win32-arm64-msvc@1.27.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "64cfe473c264ef5dc275a4d57a516d77fcac6bc9", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.27.0.tgz", "fileCount": 4, "integrity": "sha512-/wXegPS1hnhkeG4OXQKEMQeJd48RDC3qdh+OA8pCuOPCyvnm/yEayrJdJVqzBsqpy1aJklRCVxscpFur80o6iQ==", "signatures": [{"sig": "MEUCIQDwSAom9wxCln4rGjHY0HRQRxGJ54VgelmMv9YOrcoO8wIgTyyz7lOYv2qHOAch2WX4uWVaMzcRce+rzsM+sTsYYcU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7578225}, "main": "lightningcss.win32-arm64-msvc.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb49015cf887ae720b80a2856ccbdf61bf940ef1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-win32-arm64-msvc_1.27.0_1726023652233_0.9545318972294576", "host": "s3://npm-registry-packages"}}, "1.28.0": {"name": "lightningcss-win32-arm64-msvc", "version": "1.28.0", "license": "MPL-2.0", "_id": "lightningcss-win32-arm64-msvc@1.28.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "790c54104fd35f6e0d12762e9c1920195052bc33", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.28.0.tgz", "fileCount": 4, "integrity": "sha512-ZJQhMV/yQSHnn7wb42kS3ACuXUDobNWejLKjcpNi8q3kCvj++f2AlhfjU5cNg0oD547ED/UxJZFyJVsNF5vtZw==", "signatures": [{"sig": "MEYCIQCMeOebn2nTCS4WO6WShFvnTkRNZsnHPwVOIi1P3DUmwwIhAOVStfKPhpbAzJut7Qx0c9IGcE5MR63LmqqLXPFydm5a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7585942}, "main": "lightningcss.win32-arm64-msvc.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "8a67583105757e4a25378d65d243b87a345b2c2d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-win32-arm64-msvc_1.28.0_1730668674033_0.7557072619283927", "host": "s3://npm-registry-packages"}}, "1.28.1": {"name": "lightningcss-win32-arm64-msvc", "version": "1.28.1", "license": "MPL-2.0", "_id": "lightningcss-win32-arm64-msvc@1.28.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "c21f7683648a9e4d856737fc22c3eca908c773b6", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.28.1.tgz", "fileCount": 4, "integrity": "sha512-Erm72kHmMg/3h350PTseskz+eEGBM17Fuu79WW2Qqt0BfWSF1jHHc12lkJCWMYl5jcBHPs5yZdgNHtJ7IJS3Uw==", "signatures": [{"sig": "MEUCIQCFmcRbfTqfZWBYeOXa9xb9QJgXYzLeH6KD6QFYuRCnIQIgdMJBVijcFmCVxJ9zGZ9Jobmzf16cDO4hDxGCrmSGeg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7582358}, "main": "lightningcss.win32-arm64-msvc.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a3390fd4140ca87f5035595d22bc9357cf72177e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-win32-arm64-msvc_1.28.1_1730674710592_0.7483328558202527", "host": "s3://npm-registry-packages"}}, "1.28.2": {"name": "lightningcss-win32-arm64-msvc", "version": "1.28.2", "license": "MPL-2.0", "_id": "lightningcss-win32-arm64-msvc@1.28.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "eaae12c4a58a545a3adf40b22ba9625e5c0ebd29", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.28.2.tgz", "fileCount": 4, "integrity": "sha512-WnwcjcBeAt0jGdjlgbT9ANf30pF0C/QMb1XnLnH272DQU8QXh+kmpi24R55wmWBwaTtNAETZ+m35ohyeMiNt+g==", "signatures": [{"sig": "MEQCIFveq2IcwkngSjA1Ub7MtzDFVZVNqGrrXe2r4KyLOqS0AiBabvetHPFZP3ArdSSMZuDBOTCDbjrATuWgR/vvMM3xOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7589014}, "main": "lightningcss.win32-arm64-msvc.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "9b2e8bbe732d7c101272ddab03ac21b88bf55c4a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.5", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-win32-arm64-msvc_1.28.2_1732512301164_0.8975751190323302", "host": "s3://npm-registry-packages"}}, "1.29.0": {"name": "lightningcss-win32-arm64-msvc", "version": "1.29.0", "license": "MPL-2.0", "_id": "lightningcss-win32-arm64-msvc@1.29.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "e501f7747eb9ccc9222d27d3f312e9eadc054651", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.29.0.tgz", "fileCount": 4, "integrity": "sha512-wgdBdIl+CN7xvHbmYT7FyBfzGPY5evtD0T1LMqfBs6Zp7AhD6vv/QC5Q72Mc/0kAk+Nvta88wMVLszN3CuJ/Dw==", "signatures": [{"sig": "MEUCIQD7Mg8iTRXakUDRYQMARFfydUjKiameFz7zhenevg9kvgIgOf1mj095Gomp96dtdSZe1v+X5a8mjd0RbJ9PflsYy6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7385750}, "main": "lightningcss.win32-arm64-msvc.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "222fa9f7ad8b4004afd7a30c2f99cab3c8621191", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-win32-arm64-msvc_1.29.0_1736401672984_0.8378809632824276", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.1": {"name": "lightningcss-win32-arm64-msvc", "version": "1.29.1", "license": "MPL-2.0", "_id": "lightningcss-win32-arm64-msvc@1.29.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "fd4409fd1505d89d0ff66511c36df5a1379eb7cd", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.29.1.tgz", "fileCount": 4, "integrity": "sha512-QoOVnkIEFfbW4xPi+dpdft/zAKmgLgsRHfJalEPYuJDOWf7cLQzYg0DEh8/sn737FaeMJxHZRc1oBreiwZCjog==", "signatures": [{"sig": "MEUCIQCRzziT9pOMlMZIGMLNUg50NthK6vzEh8106jdILfuAEQIgSDB6iWyIGN+aji3Sv+pYP34imBIWNKtwLD9QSCjsgfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7386774}, "main": "lightningcss.win32-arm64-msvc.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b10f9baf8878411bf2b09dfe8d64ba09ef7a4eac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-win32-arm64-msvc_1.29.1_1736445768444_0.2244044889302439", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.2": {"name": "lightningcss-win32-arm64-msvc", "version": "1.29.2", "license": "MPL-2.0", "_id": "lightningcss-win32-arm64-msvc@1.29.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "da43ea49fafc5d2de38e016f1a8539d5eed98318", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.29.2.tgz", "fileCount": 4, "integrity": "sha512-nL7zRW6evGQqYVu/bKGK+zShyz8OVzsCotFgc7judbt6wnB2KbiKKJwBE4SGoDBQ1O94RjW4asrCjQL4i8Fhbw==", "signatures": [{"sig": "MEQCIEYlzLP58+HoLPp1po3calrmyajWXJ8gIvzcPZuQ2LifAiAlsKAVkxHDsD3La4G1WI61/SGlN4C+maZ07cXgHj7ebg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7389297}, "main": "lightningcss.win32-arm64-msvc.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f2303df29448a55c5f4f284f747eb53760769fe4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-win32-arm64-msvc_1.29.2_1741242128203_0.12892843889970007", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.3": {"name": "lightningcss-win32-arm64-msvc", "version": "1.29.3", "license": "MPL-2.0", "_id": "lightningcss-win32-arm64-msvc@1.29.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7a6d1c121e81d6796a7ed92ec8db971b8f67ddf5", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.29.3.tgz", "fileCount": 4, "integrity": "sha512-VRnkAvtIkeWuoBJeGOTrZxsNp4HogXtcaaLm8agmbYtLDOhQdpgxW6NjZZjDXbvGF+eOehGulXZ3C1TiwHY4QQ==", "signatures": [{"sig": "MEYCIQCaKeSlG90rFjE/YlRb+aTE/65j8yACDGWv2ywLVb9BOwIhAJ/Vwc+fJmxa2n9noHfE528h8DHM2J+TtCCyuyTln43J", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7391857}, "main": "lightningcss.win32-arm64-msvc.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "80eb8617c5f8f5519ed85bd3eb6d8953f4d32493", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-win32-arm64-msvc_1.29.3_1741974604905_0.32477618050030155", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.0": {"name": "lightningcss-win32-arm64-msvc", "version": "1.30.0", "license": "MPL-2.0", "_id": "lightningcss-win32-arm64-msvc@1.30.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "acce833215d51b0e13b1c34a8b109e79ee26dee5", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.30.0.tgz", "fileCount": 4, "integrity": "sha512-Fd9XejM6GPHx5rv7I8aqsc8mBHs+TpHEVDalP5PVP986tF6rmiVfwQzM2Ic4Cn0rXbS3z95Ru8x50hnzfR2GDA==", "signatures": [{"sig": "MEUCIG2nnzk3jT9EIjLTLIhx6m09SfA7rlxCmOm+KR9y63UvAiEA0z+lh4HqgmxUAYB7eg2/EUGUsOpr3oh+YKj2KX4WluI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7553137}, "main": "lightningcss.win32-arm64-msvc.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "474c67c046b4a54217166057296fa4ca28764858", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-win32-arm64-msvc_1.30.0_1746945560894_0.16147993012218187", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.1": {"name": "lightningcss-win32-arm64-msvc", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.win32-arm64-msvc.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "resolutions": {"lightningcss": "link:."}, "os": ["win32"], "cpu": ["arm64"], "_id": "lightningcss-win32-arm64-msvc@1.30.1", "gitHead": "f2dc67c4d3fe92f26693c02366db1e60cae0db27", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-mSL4rqPi4iXq5YVqzSsJgMVFENoa4nGTT/GjO2c0Yl9OuQfPsIfncvLrEW6RbbB24WtZ3xP/2CCmI3tNkNV4oA==", "shasum": "7d8110a19d7c2d22bfdf2f2bb8be68e7d1b69039", "tarball": "https://registry.npmjs.org/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.30.1.tgz", "fileCount": 4, "unpackedSize": 7553137, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGt+OMq5GlAjfFu/mGn4WEnf+PO0KZ8ChuxD3/qIs4B9AiAxtmaGT0sXK8ooUXPS9tHCvIi8g4I4LSkY/8kOXZtjeg=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss-win32-arm64-msvc_1.30.1_1747193934302_0.1736422554222845"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-08-06T15:33:02.298Z", "modified": "2025-05-14T03:38:54.747Z", "1.26.0": "2024-08-06T15:33:02.759Z", "1.27.0": "2024-09-11T03:00:52.544Z", "1.28.0": "2024-11-03T21:17:54.463Z", "1.28.1": "2024-11-03T22:58:30.891Z", "1.28.2": "2024-11-25T05:25:01.405Z", "1.29.0": "2025-01-09T05:47:53.217Z", "1.29.1": "2025-01-09T18:02:48.717Z", "1.29.2": "2025-03-06T06:22:08.448Z", "1.29.3": "2025-03-14T17:50:05.211Z", "1.30.0": "2025-05-11T06:39:21.153Z", "1.30.1": "2025-05-14T03:38:54.567Z"}, "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "license": "MPL-2.0", "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "description": "A CSS parser, transformer, and minifier written in Rust", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "This is the aarch64-pc-windows-msvc build of lightningcss. See https://github.com/parcel-bundler/lightningcss for details.", "readmeFilename": "README.md"}