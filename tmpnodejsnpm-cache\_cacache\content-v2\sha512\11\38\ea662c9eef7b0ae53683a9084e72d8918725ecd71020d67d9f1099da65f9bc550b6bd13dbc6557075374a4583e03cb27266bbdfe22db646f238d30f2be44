{"_id": "fdir", "_rev": "45-f62e09e6036a205cdffa764f8c1eebe2", "name": "fdir", "dist-tags": {"latest": "6.4.6"}, "versions": {"1.0.0": {"name": "fdir", "version": "1.0.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@1.0.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "4a3a197e57f0da3449aef17322a8a463939c419f", "tarball": "https://registry.npmjs.org/fdir/-/fdir-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-ZAW2znCQvj8EPK/SYLyI6WdV/+uhxvn5u2DetrZo6zStHyDyTOcOznfnhX/N0akWreISZCLaLcHELxdg+vmk+w==", "signatures": [{"sig": "MEUCIEDM7RC4fW9OcxaYs//6duTwyu/uGe2Lf46BQsSpW0HoAiEA16MDR39jeaFHLGR1sU75D25YX19WiRiOsSYl0iugMOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJea/PzCRA9TVsSAnZWagAA0p8P/12MH3iRBp0BOh69Aa7N\nTlH6dv/I3WbtpCOggHR29j5326WX7WtnJSeR4ZuihigjIJNrs8J2+o/KQEIL\nz6DA8RBNkrJv/9vuC6BvVRnYUrEZ65saAaQDR04tMYWUmJrtjNWrm5oVDPOm\nXAcrTYkUigwvKNxhCp41yaFb8/e/b5KJZ07pSg5+nRYqzL7S7mltBtl5ZCil\n/+uXYHTuCc9CzpHHM+Av8T3oUsLyO+XJEIKj0Z8XbY5RsE85yJWT65NMkIxA\nbNZyV3f7xTLOx7Yzw37mVDOcjLMhtZtmn5ia/mpy+36WiLemQ33bZyS5Msp7\naY/lkgoKawWwr/nZp1JmoUKFSNSToH55GEjwoxlrlbESTQ/PhpoTEDx0anNO\nMBk1vq4WQJKoxrmf+6mmewCLBI3Yp0X2jF9HLD65cHzSaugblkXvJuf/jXHJ\ndmAEPZs5KEI5x4OnVMye0tg9sJ9tKFjy8RbAjR55QRuWF+X+A+MrSlAChCIJ\nYQ2WizFwmYzAcel1A47VtrYRcBAn1RS/WyR9ON/xVbs5AYPZb88BUZJKbPc+\nfiT6XiJpmCxAvQHmKcq9ZkWEHVHxX7jOJGnfvOM0FgQtAsVDsgZbXVM9Bpz9\nOb4B3Th8rzD0FK0oEpfymtQswhEitkSRYpI9H+xLdNsFAiAgxMz9G5brWpxt\nd+SS\r\n=pV/Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "3abb1cd3699f189438cc4ada7d3d6a985cc2c44d", "scripts": {"test": "test", "benchmark": "node benchmark.js"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.1", "description": "The fastest directory walker written in NodeJS", "directories": {}, "_nodeVersion": "13.9.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^5.0.0", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "@folder/readdir": "^2.1.0", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_1.0.0_1584133107336_0.13950252841103183", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "fdir", "version": "1.0.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@1.0.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "bb9f67c0e983c9395470a5a8cf1eb907151bb5ee", "tarball": "https://registry.npmjs.org/fdir/-/fdir-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-aTsQM5TFdKqSTdOjBf5ubxEoTJoDorABOeN9EEV6ndUfgwhpv87Pme9ey1G03mCJmQfBkZrrDDdGf1UMU0odVg==", "signatures": [{"sig": "MEUCIQCaZsICKKDWb1eVd+4w70dAk/RbP/TBXR3GYC3VHc7fgAIgV3JlMYbGW9lbWAq75snMW/zYCmF8rLuuEuuCzni7aEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJea/VHCRA9TVsSAnZWagAAbHYP/2Z2iy40/KEQuK12CsnH\n+CUO09Hf6qFxlGw4ykSN4+zxO+tiqhyQrTq5VIfzeRDkUwan0IU2Izm6FFrw\no498dTC6H0DCcLm7MbS4aQcfLN3ZjCwZPOlJW6Od7lyN+tcRmhmhHNJJrnJM\napeWGXjhtxuK/8VSZWCRrsERro7bvDyQC9g6PXMf5PDad43M+Erkmi+Ov0kO\nM+t6pR8HxALOETf1Tmkx8vWooBIk1KA8MtD4IgA66dvZcIVfzN2Rjju71dbs\niqUCncdhp4o57Ey3Gf8le8UsgjZleI2p0IRLfsQPBSyPssZ93D/3qhZmd/x2\nXMWEyY0JAkI4Cq9qmrSlSW+mf4XgOUjEw+f/2lWdr/se0KaXNetPWQaiH9sT\nsrGUJsExmaHrJI/TDjAlinISqftyMESwtSEtg1w9Wj/uCeJGkDVi5mBWArWv\npRB2YP2dUGghE38rju58XSCcFHLg9mK0FNAB1wKWlFEGui5g7dJ/2gJaMA3i\ndYCOynwHPvQZ5HvnmU6nk2XAbov4wl93FAcr1pBo1Vgul9pUcbf46Vh5wO6+\nXsy6ChgY3v512BAn1qByOP5UsHwVs/jSk0ebhyZceXNhnewNXg7QUUrYS7Of\nQRijiDi/AP94Mw7WITFos9igoS0IaIaYiJkj0J9bxjyMzsVYtYTTXHFU5bCI\n5p0g\r\n=FyCe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "6a05fe94f92272f7ae7310c7874e68be26f89081", "scripts": {"test": "test", "benchmark": "node benchmark.js"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.1", "description": "The fastest directory crawler written for NodeJS. Zero dependencies. Grabs 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.9.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^5.0.0", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "@folder/readdir": "^2.1.0", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_1.0.1_1584133447344_0.4147828627666701", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "fdir", "version": "1.0.2", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@1.0.2", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "8ff2572fa33cd7f5ab06247ad9d59871fba1a568", "tarball": "https://registry.npmjs.org/fdir/-/fdir-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-/uvsBcz4ldDNDl4TzTQ0jzF7jayjRTGHuXunjDyudvRgsYl66v/ciufXiD8U9IYQMDYc6/WHdD/Gv92PyKaXQw==", "signatures": [{"sig": "MEYCIQC+GaDA41P7lOjhBx/1TX6vAiBCWLZcGawYZoJzUfGcpgIhAOK9KtK9DR434noNdLqDIpBuD2rwJ8omX2sXGeBNWZ3r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJea/Z0CRA9TVsSAnZWagAA5aEP/2Eqrj7YSAQId4TmRKJj\nkP+zDk77Ixp+e+Dbsq2XH0jcFfQzd3U05lXh6HkgYejwKXtzcoq4i4EJsekR\naEvcYW2FHpBM8wkgB0XAdE8U4NtQFbQ6ktl1zXreqvuF9wqh4915lY2uaJkr\n4Nqm7Uz4zy+ExOM/s8FOrFh74tzLpYildV+iWdtU1GuyKobTZvkXnKqPrg15\nuMgw3sTx/OfPeWV/0Lhnkmi4AzJ7xV1kSbru2xveXp20G5QSwNM1ITR/AgIs\nFQcG533j2b/Mf3ktpvatEjEe2bAEZFpiE69D+kN8iu5AfI+n+svO4o+O4npr\nGxMzAMkgf1hDjuriJDf2a+MI6xgcGVsxdr1Nj34wYQorf3ycCpzItRCJJuTg\nvg4N/k7Ub2+V9+yY6qpRhGfcMQWaiv9HFMnY3CfeEQb/Qg+F3ed3PTCZrXyt\nfTGfTEF7BD+DqVvDViT45SZlu0kSmGuKMmdXJgV5rc20WslJjPjwWGYxm89u\nCNIAPskase4YoGdUoN9VuicC2t+R8X5N5Jtjfrp/OQX+OEvme9KBsGfXPLYG\n4aJpA57GT92q3AEWUyrqxVXOXcWVOH2G3MCpLzNm9dc3OkqrYSlWR/6q7a+l\nxDg+18gPYHMi9R+IZZjMT1cg1E6qlV0VmXegFmYO+rodUFA3JRwwnhKzOpIh\nkfgZ\r\n=jUmU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "6a05fe94f92272f7ae7310c7874e68be26f89081", "scripts": {"test": "test", "benchmark": "node benchmark.js"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.1", "description": "The fastest directory crawler written for NodeJS. Zero dependencies. Grabs 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.9.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^5.0.0", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "@folder/readdir": "^2.1.0", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_1.0.2_1584133747912_0.16804039697218176", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "fdir", "version": "1.0.3", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@1.0.3", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "99b021a779d32ae0e97f8c4082e98f3fa9c4a94f", "tarball": "https://registry.npmjs.org/fdir/-/fdir-1.0.3.tgz", "fileCount": 5, "integrity": "sha512-CxrtkPN0orhlog3LwWfahyK7kYxjLCT6AO+f+rphBf4gIwisfBC8+bouDHyAPiMXQuu51TI+VyEp66tZhAGGtw==", "signatures": [{"sig": "MEUCIQDdCGnYdpndwRTgepZXIRzUDM26JQ1EVxM9ZldwjU2JxAIgNrou07wtMA5kOVLdhPMgHDxKoswj7eUM6eTa+aN9BQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebLJICRA9TVsSAnZWagAAL1QP/1VfOH5tAyLpOIX422aS\nSmYhToj7RMHSrsiLZzZBw/yawXa11IQoQuZEcmQLDBTqBOOVf+XqwazKxyfd\n9i2iOB0Mg928x8QlzlEDQQouNEAGr+NRFmAeHrtPXZixEYP9V1Lap85xb4ci\nSEjAYH5a3HpVIitCgcvVaWzYYW57JBGpjCCxhd9KpmkfeDgWx59OdqFkmaVK\n3Q6Yari2xF8cIXI0zR7+6ovhO0Bulia7hqrTWrVC4lShQz+Xw4AybukQ8r/F\nFEjsMxp1V5SjQTauK0a1PvERT5qEUTeQYQAP2s/aP+WhAWATWvS+WRfrrSnF\nluU1GTXaIW1nGmUl5hv22gKbkr4BABa10RzUOx52hMjuPbCrl8G0w99OjMhA\n/EczcywaUKs5yNElHP9Z9UEJ1OkRvWNLEeniFSrjwbVmd6kHRY5YxPj/2dc2\nEHinhALCqemkApHMgBEA/fu8+ppZQ9zMpaRnO0jCeEWkLYjVyl9YLH47gNmQ\nLvez6ETQUsHr9RvOpirFVF4bwzOK3xcWtGcK69jjJRMOTJVh13qu7zPwTAE2\n87S4oaTLU0bSVt9KcdFYYBsXLWZ+9wtEJjY+BdhLoJHtKcdk5fBhOEPfivyw\ncV4XiVu8EtvG4iI7OmxCPFYmHiBNfCAoQaRg0RutIiE/69VfJx/RD3vOMd06\nERDi\r\n=w+DB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c6fa353623f10533244bce8053faeb3c2865d7e5", "scripts": {"test": "test", "benchmark": "node benchmark.js"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.1", "description": "The fastest directory crawler written for NodeJS. Zero dependencies. Grabs 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.9.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^5.0.0", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "@folder/readdir": "^2.1.0", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_1.0.3_1584181832289_0.10666282104543723", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "fdir", "version": "1.1.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@1.1.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "a37df0794f6083e46d95538dbc034ac6cec86e5c", "tarball": "https://registry.npmjs.org/fdir/-/fdir-1.1.0.tgz", "fileCount": 18, "integrity": "sha512-c/0XxB0Za7/dBWoPn92mI9kuSD0E/pS6L13evZd+0CCt7Q6ab415NGOtALejZ59FXMQ28SlyfpJvhl+iWSnqMQ==", "signatures": [{"sig": "MEQCID1iyVjdPjVI53SHsV5LJK4AFRT+2myFvsjui9oSRb7qAiAmdn+1RtWSe4H8HNmE/nEEeDN+rim0OxSEp8kVhkJT4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebQ3vCRA9TVsSAnZWagAAft4P/0aoE/48nb4DoO2LIVva\nOAYadSWy+vhh3d3qtkSN1FhuQxGzurHwaepOE1fI1ziSXAzT8AHQFzdKwkc0\nMQpT1h05Yg5mCIFMDxl2HJnkJR4lpLYkdHCumaOlb/HgH3Ug1Z0asur8lylm\njmfZi09i8r6aTfRII+AdvaB+LifKNfs7smmpjuYPR0h5zU7Sw7vxrPPV/8QY\nNi6nrCPZ/mUwJRWx25XDPjb3v484JrWji1CP+qYqMZEwkRysF1DhB1at3Zpv\nQtWYCMMzU6XLi9D+RPuHDKKfUiyrVKqJiU3Ix6NKfWDaXi03rgDEaHnDMq/E\n70jKhdPEs96xSJVIpeR+WNydOsncnfVw+oY8fs4y+Uf5PdzepA1O7hOP+5Xz\nZapAzWJHkaUYzTElePo1tDGA+ADI5QUNOGWqMcLp881x+A8EksmM/pfRbHJL\nR+JPYRUkEzgO7BOP1AnxYMBjJSeFBolQVYUZ30HLU3tAVqGCm6zgrs8myD9n\nwM4vGoQDQfsSyo/aXdpqM+JAyy/d9gT3S6LVu9l7ucUV6bkK2q3NN+WhvjBu\nO1BRQPguA/8LYnoCGruF7FQbLlXBM3a5d2NK9XXKUdkyyFNLZsRPkGIJaK+C\nfCRZoa4z3X5Aj2bzIVZW1YUEHckwy3DXdZg8OFyGFw5JKT0eOa6r8YvhWDAB\nuNXW\r\n=wxqd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9d5dfd8311627eb92c754a36d1198f647115baf5", "scripts": {"test": "jest", "benchmark": "node benchmark.js", "test:coverage": "jest --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.11.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "24.0.0", "benny": "^3.6.14", "rrdir": "^2.0.0", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_1.1.0_1584205295308_0.046230999315329147", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "fdir", "version": "1.1.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@1.1.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "2c996d47088927bd08993228dcb0ce468a27533d", "tarball": "https://registry.npmjs.org/fdir/-/fdir-1.1.1.tgz", "fileCount": 6, "integrity": "sha512-wQTqkAv9EB1Tq/9iPEkvZu8M1Ow2QVuAUmixo4trVEuefoqPWZrHPm5sROMq2TfDzlApYmGT6FUe58uc7NZdTw==", "signatures": [{"sig": "MEQCIBSB7+p2IKqzOyYsTT0zMIgXWFJ7jcSVZ0EpvAylSq5oAiAxXSOVZtJC/6eysFkJw6N4LMdd2vyJYLWjdRgDQbBExQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebQ5TCRA9TVsSAnZWagAApJ4P/2q4zKiVFd97KcLHhKG8\n/V8IAousDEkKea4K470u7izW1b7YVqkh0oiqhOImUFstEyciolGlTETcnUT6\nEv+C7UBt/Lme0Fzv6lt+L44lAXsCAeVNS6kcB0os7dK0v8N/bYmAIDUgajlt\nH5UhjMISE9jafnkOwYL0Bh0XF28ZKa5xJJFE/BmsZl7tXLO2vjskMAWIY0dL\na7+iWTDS+yfwrThIAZoEq6cASdyXbvK/3GVNep9Gf5AOnY/EO2QMvo09Bvon\nH5EqeRkB4OgndjJaWv50VMNvvWF/e3ngqTjAZV/bFGDMBKDCgz2Imwb/bSTl\naRIUNphTeSlQD3CAepN6ThScw3/mSYO7IsOlktMdO4XpzPBgH7ogbZMyJ/Lv\nSt9kuqZ8iEGthxTR6uueHdgbIDUJoF6uT2RJKJtV+8GusDaMNGOHdaTem/r+\n/k+w9iQuzRbzCMZQyXpV8GB8fUw1GZNDjyokqohBfTijVKGG8cz1R02WNUyP\nZ6kDhDwDlJuTb3ayZJonLpBmRkj7optpHMG6mF9XMoxLmPRcMq5tOAiwVz77\nlbnijQ1wsNZAe1oTtIjQH5wYFAs8sHzM9NmyPpaMNTMhAxQQwlSQsrWfFHHW\nEVw/bJC+nvTuFBE1IUoLZGrhPztLHtgx5NaaKhvvSj8QSI27CyIaW1Za3JlT\njpMi\r\n=XhSw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "469e3fe138e91ef057e885c6939bc8025fbc83bf", "scripts": {"test": "jest", "benchmark": "node benchmark.js", "test:coverage": "jest --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.11.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "24.0.0", "benny": "^3.6.14", "rrdir": "^2.0.0", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_1.1.1_1584205395296_0.2617602548499933", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "fdir", "version": "1.2.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@1.2.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "05573596422464612cb10d97129b2f6a9fcecd6c", "tarball": "https://registry.npmjs.org/fdir/-/fdir-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-wZSMj7wgLfouv2dECNcMX9w4VgM0RNMgygLDkSufR99zXPqRUC6xLbY5IhJJaZZVSbWqvnVTRDdhgFR45wQ9gQ==", "signatures": [{"sig": "MEUCIAaztGsa3yZMuD6T4ED/moUhyDUzz49IcEl8JdmoeENwAiEAzIeWWhKJUgDhlHzfLodf5mY3ct5vMir2hvXNGfHRoz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeblhhCRA9TVsSAnZWagAAmFgP/RTzA7XG6YkrZsZ7yxNo\n0zBw5mnE7F6+c4fOuFUqCptghOtRf/EFgLbiGItKDds8oCEb1Y97EOQIvoB0\nOSKx0/1kzjfeZ2pMzby0W7ZwK5M0s+vT0VbndnXERCGjSd/+dTrb4nZGg/FP\ngz/nf3rKCUbk+0J3RcPlhsCxC1/eYxs/t+PoSvTJUqsPe8RVaJYCq4YjV7V0\nZPSA+AoyoG2kT6fDWZLTdw2cBHJr/AIYg3GzeVtku03mL/KVTpBvujnmGI1a\nYOYkl4ucjusIk0/7tFm8FG/b2SQfXywMkUpXY1o84qH3QaEgK9OCN+gdNYjt\nauYZ6MWfw95cuHSJiXGyKbWafdW2timjQ74rQ2BnOrgLxiyI0+2IRDbHSwBX\nw4UajNb3ur7rQK78vDFIr9hr5lNYr5Qw4xpGDruoTZd4cMek+ibOd1ezkH+1\nlx+fkT7eXVPKQjEWbi2n3kLu4V2TRjNwkIok7RFoLtSzc+LzGQeqXtSB1SOd\nedSOpZhpFKmn8Majj+fAWvp0zfDtHMY5aHCLFlKoA7oIVb+rtNdf3v/kP11f\nPye2hpuTWLNKEsq5EBi9iseie1Nqi6D3jEpqLqsLhDaWabp7mQgKXurlxWOW\naCM811yDnjBRnq5DV28eiefwC6X63N54qsJvB1Heoko1eJ1/LY2z+BxwG2FH\nrv+o\r\n=79tU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "72d02d1852199831033a696fca9f4165d17f5a8f", "scripts": {"test": "jest", "benchmark": "node benchmark.js", "test:coverage": "jest --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "8.3.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^6.1.2", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_1.2.0_1584289888705_0.7224602488473086", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "fdir", "version": "2.0.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@2.0.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "b2f38d646e4786ca256720c6dbe61ebc17a928cb", "tarball": "https://registry.npmjs.org/fdir/-/fdir-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-cDrfKNZyHeQneP/GQewfLIABN+ZxFzx/JeGowN/krdWsMtAQVEA2VPITgvG4ZRdaW35fvNBDuVxv+ndb2f6Q0g==", "signatures": [{"sig": "MEUCIQC144eqe9G31Wu33thH/YWljl5tQJlBimRzKO/hMMmNxAIgZWSqRVPoGWHK4+C21bxlhW3/+oL2BoW9rAVTQCBzwqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebzWpCRA9TVsSAnZWagAA0vsP/jsVt6l8UhlHXvxwXqj5\na9e+j5TIb61przfjOIfnG0wJZr1N9h88/uKGpDNRgmThYfau+GFm47ex/u/u\nq3ZqSGbh30ppOCbxxQrJvBWztHPWduBUZcTTw4jsXZkCfZ3cpGNbm2qvBbdj\nA/BzzUQ1LYZVBPFh6fRezbOnAazyrVuDRguNIi9rtVIF1A9sI+Y3ODSCAW/o\ngLaxuup15uUUEsGiBEomJQwjer1HIjas2gHc4Wj4iZZSb8S+QcImm2u2I8vC\nAx93Mza4B3e6Rw4LrZvmjKJfM555y61k6mSjTkoOoAExmyGmONYoFPtz+4lH\niVsdHOsemfte0u7UGld2d9/oSVcG3KhEMDI506d84g+9Qig690IckWruuOIG\nP2q/LDxTzGkEBVn9SeBdaJsaXmTqG/mFOhXRz3B7AatS5OgJvUW8NWwqOocK\ngr2o9lftbFNDom7D9JbvBDYkAEYi50kx5R0MEhAxH1yvR8nQ5+DN/xKHJ7K7\nZPegT/bzXYi+x84dQdAzqfoTbdkvLDLDI9Z0z43U3CdOwP59t0sEKWP5Xw1t\nq3HrAvCAf/MzXIr2KvInL1gMLEXsUJyBOd3KvAkHakF9vYsraoDSwXSkXc7c\nhUPEQ8Oc22wRiW33aKCVrFSnGqPN75k34Ayk+YcblwbQFB05aojeOSSz+0eR\nbPR+\r\n=enuI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2fc16f2aa18232fa1d2d12bb48146512aa6c92f3", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.11.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^6.1.2", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_2.0.0_1584346537088_0.1739858587033385", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "fdir", "version": "2.0.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@2.0.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "647461ffb418ef046b26499c890c900251bf95cf", "tarball": "https://registry.npmjs.org/fdir/-/fdir-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-FJ/Wkcawd1nPhJ+vDTtoIZKLDYnnFiPr7o9AECmqp9fEEPT/rUzdluhBUfflI16m0DHtXmZmz+E3ERYjHgMFTQ==", "signatures": [{"sig": "MEUCIGIbD/kFEPaIXBu170hbCU2dNu35crLlDa3z/HHRHHQSAiEA3N5YYRaNPAZBAz1nBBdXMeX9A5rRjGpBy4yvAweL9Ss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebzXPCRA9TVsSAnZWagAAOmcQAIcmK8pahfh2j8H3M36g\nLuVgOJ6VdJMk06mGCLsdtedZVe50PfI5JLwDnQ3N99OIgjbaOhhNxBDzY94d\ngOVj6DoVPY6VeZL7+qBNlOMaj++osMTqa7TtDHlVCQwelVucKpgMcU6y6hGf\n6y2Wt8rZIXwEYjfTwtbl/8x4VTjeLWYHVAihoaisPsWMVYuQPWiVqFlPpy3b\nHMNSUUFVuXabVMAAvKTdreOvGOmT4E5DOo3EIiKjDqMUQ++ESdL/Q45xoZ5N\na9oUjtABtOkyi6XIz0DsA3BSnt+323BKsY4lt3Fi9LwK1RsQ9GcqDQibeQMC\nlpTrBAbOOrbUz1UOcDqYWi5AbDoh1K5Xfjbky5z4VdEcWMyPn2wK7XP1Jwmh\nwxaDOc52jBVGdkmz/CanMIXbGu2ISAjT50pW7rqNPHNYMgJz2skbr7Kv69fY\nJwvgvClid5jTGYOAImihtZpyPZTs0adGGnI+ghrUdrCEjMDE1AtBtnsiSteB\nXOskfsrvYIzU7fy2OHObvup5rGB3k8jet9tbIeSGgOdI7LS6Df9TwIUe0PLd\n/FY16Z9DukxW7rtMGYz3QzpwfwxcA3YKNdB7+R39fKlqvnxBq+oiBVd7b77U\nChIzoKolJy924ohgtwV3Asopu/pxnCRtSOVCKT/nYV5d63XRLdLVTqNu50xR\nRc6J\r\n=NkT/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "d6ca7741b4a5bc57590785050606d72468350c6c", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.11.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^6.1.2", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_2.0.1_1584346575142_0.6543708596028741", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "fdir", "version": "2.0.2", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@2.0.2", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "f778a3d99d04a174ad4b8195d1245cecfaa30876", "tarball": "https://registry.npmjs.org/fdir/-/fdir-2.0.2.tgz", "fileCount": 7, "integrity": "sha512-5a8W4Z7HOB5F480mHvAt/raHkjXecQIr7WFXtMdSMI5zhzTclzyL2S4v1Fgk+xYCDAOUDMdq92/YFt4CE63BRg==", "signatures": [{"sig": "MEYCIQDXMBvKxUGglM0EKLDQsQOpHYvqkoeuv14Mb6XlZ2/8dQIhANBeGhwNI5OHc1zf3nDCYxo6ptNrv8LaFbHUfYEAwroy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecml6CRA9TVsSAnZWagAAUDwP/R+eprUHMotkGsmQXxmB\n8otY2o7pi+B7qfYpreyQ7xI3KCxoTBxByPEYechYAc98ua0EASWPj22ib5Bu\nZXX+hTHrDk6NvvO0ilookqTItj+Uhwdy9SB4n1yn5EXIV08jtQZm7xqXXg3G\n8wcWBqTm/1vibSSM+y/ShC93QHosUGUw6sW08e+PE6sWEaHUdjmUrDkI6NM6\nE63sKGWwEN1AOm+c25EmcKN+5WBOMa5o83vrwzTnfFpaP6TOd+qz19gFfzRc\nWGP7NVU3g6v2V1Tgt+piFIr4gKTMMGjmEFDWhoJ4528/M7ZvGz2j1uKr0aYy\nZ4U9PUzP22qQeFohGfAvUpk6TUE0nPo+P1mZmKqZpItDzuGGvpygf65Ny/SF\nf5UEPwZZP/Q3uTR4bqu5e11Ulmgaq7z5C4oW0AGJ4S6TRETfadMB0AZHoUKM\n/dAYngVZ6+/7+u+y99xQxAwvffUx/8L0FfA0zRPnmz9ekO/go9jyiuteCOcZ\n6J3vSaAdBM6Q9Zn5DNAzwfuMInoX7ObUW0nLwm98Disv96ou9keA9Z8+nidj\nl8PLXsdbSEXjPTypbSHMhUQ2yqEDhOaUWj0846Q4EvcGDftbio/QQo0WKY3x\n1FXkx63bNkmaOCqo5IKsUO+qxUZjTOnLFSp2Os+MOOMDvhamwAy8nZi2ZGXi\nWv0u\r\n=tGyk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "77dac2bf46e45ef06ce6dcdd6865fcfdf7648a65", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.11.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^6.1.2", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_2.0.2_1584556409604_0.43643740346984994", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "fdir", "version": "2.0.3", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@2.0.3", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "e0df292e3ff13ea3e645673b82752d0f3bddfaac", "tarball": "https://registry.npmjs.org/fdir/-/fdir-2.0.3.tgz", "fileCount": 5, "integrity": "sha512-kg8oTgW2Ef+5FHBTqL9HAOEoZy21J+nCYCX7sD9yw7fP5sWd51q3tLqocOKaN/7bZW1dtiNfzwc39QhiIw8z1Q==", "signatures": [{"sig": "MEYCIQCfoWSEDy5BxEk8tAeWVIxcJl3uO2xmOyrf+TTK9GdVRwIhAKc+1E8MD7SbWdvey0wwFU1FYhNMuWShPh3lTugUTCPy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecmmaCRA9TVsSAnZWagAAacMP/3uR93gxxyKbgYajRT72\n2q843LxKy2AkWaLPcRvyD0WSVH3q4hsfwzDKIW0mlfQFBcu/NL572k6WO10f\nYCc+TaIfLcpw3mA67HfPCC19+XYQPgLADUbGCzzZ1/uKu43tZ3kZjmwlxcbc\nnTPSlezxBXPA0oU6gNgvGZXX0lQenkGFnuMn4sDND7n+TowkY0QR/fbj0ewT\n6ReVh4rwU+Zsum/2x7Oo1FU0EPCkeM+MpSAeHRqLQ6QEvn12fi2idjHYqHIB\ntKIkS9jnmj66S9M2plecxirBPL6al+etOmOKSkqdRxIk53StepuVfRr4AEnQ\nBy+PXeuGPMtzfl/U8B0sFXqFSf6foW5XlDaM7NO0dFE4S3DSgwOeJ/Gictom\n+BWjDRvcIJoUFCamND4Uov9qkpeONeMt10iGkpxPg+FngmrIY2ouLaw3Sdjo\n+k97b8JrsgYHina8dfrL65ot++BoucHpv8Zr+zHnwauaq5asBnhQucRE0k9O\ngLQ1/sH//caEcHz88EDwAUXkA7CUJ504Pob4CKIxTs8vh1vUSdu1yqiUNwo+\nEeTZJVUiLckSgDR5XVQ6lOo8Z8B/1floRELtBrZL9uC/JOv0KSdXCbSX4EiE\nan7+nRuoT7529RxpVOeK2snJea1FhkzyeeEmXq9TzJe4a94IeA8K6aYYJIJ7\nvQM7\r\n=Ot9b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "77dac2bf46e45ef06ce6dcdd6865fcfdf7648a65", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.11.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^6.1.2", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_2.0.3_1584556441965_0.12115473584501557", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "fdir", "version": "2.1.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@2.1.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "92ea339a3a4493fcbd31475f539d0ab80c92acfa", "tarball": "https://registry.npmjs.org/fdir/-/fdir-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-3OrEdo5P0o6SCvy0G4TNXx97D4/f3E+oKMF0q9MmWGIxJDLSjQabRJnlBApLtopdoaZ108WjPjHQM7niFYHYGg==", "signatures": [{"sig": "MEUCIQDy06Q3ITLMnvcNcrf7qMphcJdt4z/dH+H87RYtl8ztEAIgFmnW981ge5NYUEQzdQpoths+U5TvwMvjOAwwgm9TWp0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedmKJCRA9TVsSAnZWagAAYQ0P/3N7nQ7CagrTpTyA5nD9\n/U26VWc9xOR8tiFc/5fg73hAL9E6ZKRkioqFoj5+DV44GpSzcTa1gxjPgjCf\nukPb2mOL8Y+sA1LA+iJvjTLZKksJeKhDcep41z2SdG1+L9BnZpS/OzBsoWH6\nETm8ZGGn3hqJbgrnbnAbDTo0y9+lRijFurySIYzrYmUjqsV4gbVEFEKil0vl\nhpRR4xfknGprvGNDtvsOi7pzirG9f2RkdefdMfahbU7izDS8V5jsDJSu5Ycw\nnQNa0V1Id24kICsG2SCqTi+yiSEa8czVN7VKOP1rDFL8E3jFPfVFcqrmTXnJ\nxN95h0COgrQ9vubsjeEA7wgAuZ7l5usAoAYuXbnAHrM6EYCuhXEM5XF/KUnv\n/FDUs7d/WqGwtKFLrWRn4Gbro/O5aQp4btgG0eH/ANtVPU9seDP7XRyr8S1x\nCuz0Fjy2p+ty9jOLgyl/oHnghbmjropbYjw7CxU7QIOMs78+10+jxId9AYcu\n593gpXfF2r0sxAhRlz3t9oAegEC1Z3D/zg/oaa+ixOXVdSnnfdgyy41t1xxv\nuo9N+BF5tQRzJRDYHwENm7xXa2seiNKqbh/UnvoHNZ10RLXKqfvtA4pxw3yR\nWmU6w8YrWA9ZscLg4COgua13TBHpsx5YBo6ufm99/dL6z+hdFByMWeZsOF6v\nFWeQ\r\n=hW06\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9f1d47c4e2af6231ea61fc8157e8247d98b6d48a", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.11.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_2.1.0_1584816776860_0.261173853932126", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "fdir", "version": "2.1.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@2.1.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "11b3d8b9b74aab626791c73c5e5ce28cf8fcc8af", "tarball": "https://registry.npmjs.org/fdir/-/fdir-2.1.1.tgz", "fileCount": 5, "integrity": "sha512-pBc2pGnR4cTkJf7y0NmhWlDFtZw4r92i5TW+tirHYxgivX7ZlfGKRk9KZ667xGbmCvrzb9f11a4j+qAeHz+oqQ==", "signatures": [{"sig": "MEYCIQCD6Lk/y+oujvCll0upEYKIwhvEaBDbdc3vKRRfhis35QIhALqrUHvOrvCVaP4NcrkYI8z4OtC+F+KO8cTixtnzk9WR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeponlCRA9TVsSAnZWagAAeb4P/AxfoMsRviWYVvuj9hZ5\nWIHP4zLSZ5l9evr2dbAQpMttf+EW+dH1TGRomAaGxapiN3bZ+NV1MM2gFsHd\nmUkTCh2csx/S99S6xI6V7xWqu8VAhnge38TcO38JRWFfrtckMtkeGwHcA2NH\n2ZbQdZZG1pXtL0GaMrQu9e9I09OSxOt1RmJamDW2S8R8d2s/jpR7QTBuIAjX\nEV1/gyJmlJ2oU2CgPnA8jebyB7UxXe1ljpXgsIb8ITDxZNnSTcahiGQ8GbKZ\nDAr8BDDUE2AekNEYJ9WJC2YdU478M8oJ4xwIUy1HJbyLXt9TgVT74AVEJQt+\nOyjC1TasySYFaC3I98Ofn4TWQBRS8bux/yKZj8EplG+HQf7KNjEkj0fIo9YX\ntuBVxLiZVFWCUtmVXogHGL5dtHG68rXksPYYAVRT8fU4xAu5ptPW/ALNlXGE\nd4vNQKvxBJAdOMUQuhxvHE9QUI9P7ZXJWl/dzwmjsKhTYjecUlf5eKCeozLU\nb80AYQCot1FAzT7Uc+NNLYlrDNe1BIUnlguvGcm3D0ldYQiV7N62GceWLfo5\nJdFVm42bvVvEDXwGvLu9IeVN3NqReJZ4gxoZRw4pe3DokOVO6icOMDU1gjPg\n/f2sWbYs5OGryWAQVj+pAJmTlzMPvl5chUeyfUuLUFahYMd+d9h72xCCuInV\ntolT\r\n=3h4s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "7f2e2f1c8d02c5ba3dfe0f0e708b215d8d51c2da", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.13.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_2.1.1_1587972581072_0.6707792697490127", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "fdir", "version": "3.0.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@3.0.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "6e0ca5ad4d1734cb01d759871c967a642c2cb9b5", "tarball": "https://registry.npmjs.org/fdir/-/fdir-3.0.0.tgz", "fileCount": 13, "integrity": "sha512-rW85fKIixACH1QeZpbL3cWDJimQer9lrzcpiTe32w+59ub1fhjXJxGlTL/RwHDjz8X4jPdue1bR+U4jC08jerA==", "signatures": [{"sig": "MEUCIB7IeuyOWZONobjc/B1Bfa8SDjV14KHf1EH901eAsrx4AiEAlGgAI31Ej1mnHXhbMvqoxW/pBm+aMh3NftM+a0WMKr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuEsmCRA9TVsSAnZWagAAvBIP/1SMuvekWwRGvcq1z/2Z\n9vyWDRpxuqNeV+kGLQ8QorM8tmJsZfzHa/1XxGWlGePz61DOiW9XnVH8HZxR\nj7S6wjI79s2s3ksOtdKowUvKqBt2T9zOfWjMgqZS2HhBixGzW0LurNDtsggh\nsSx/bVhK8rAKovL5F+xnbKdyjRCuTx8CMajY2jmmpxHsrhJQ04aXSaDe+MPt\nIaq/+iNU1+Nqb2ViubcHRjy7lnIOqCbtjUN6/LU4vquToD0xnd4UqW/M1S3M\nE1XCagpZP3c7UoPgdEqblR+DoQHg0Ob5w0SgoddRiVrncG6eTqos39bVN37J\nAkYGvie2wlSiG5GxhOForSpACnl2DFHENBHxW8jsi37UVa1/nMmlvZb2BzGu\n3gysPf/aug+WIj0ILSiQobsRnJAhDwHSzUPi1JVpEz8iJZKJZvdnNiS/DAnI\n7q8rB0zaeahL/Y4v+29AjKQYQL/4ljxyqKyHEh3VfLgKEILtxRdGqaazxWbZ\nHXrSNHMR/ZeBS6QKPpxaddyhZ3mQ1lyiGAHRxgW+AbW/2G9QsDWo251dcOYf\n+nPQX7ayCy84h7TG41k9xNL+aM40CleFrIL5nZfASRZxR+7G+yDgtMdadTip\nN7zISaNoeme0+Nj8IJ5yaiT+iaQnbNYIFKKjTMJWOPOVHYZKXuBTGMrsTn/z\nzL2e\r\n=L7MJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "8a37b802c35b5e4ea58b5238e2b67668e5231980", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "14.2.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "klaw-sync": "^6.0.0", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_3.0.0_1589136165880_0.7068900618587308", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "fdir", "version": "3.1.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@3.1.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "bc0a72bfb0a0fbfcdc3d7a1ee3bcf818b31f9284", "tarball": "https://registry.npmjs.org/fdir/-/fdir-3.1.0.tgz", "fileCount": 14, "integrity": "sha512-AYu+14CgEgeosjfi9/A6+hVr/Yui+4DwQLgwTBlfbHjydxCcg70Cbjv7SxJZP+zpGuQWyKvFon44Cr9pT7UIhw==", "signatures": [{"sig": "MEUCICKkyML5wBS6VGsikUBkdOaNdvznJyYH6hgoOK44hWhTAiEAwjTLrq2CBQFhWdJUXLkGJE2tPc8ky98ZT1pdr45qZcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuJfGCRA9TVsSAnZWagAAb3UQAKHDOfGDAmsVVRI1XLxS\nWGPskEc3sRJ/CO0Z8YA7QBJMPO9+OddwJ4xgwZ2S8p7Zg7fm9cNJ3jPZp75E\npw7ECCgjGAZlunozf1SNIBGSdcmqJ495NZOCnBMVximgclEFFjkih4Dnqk4h\nhZgrslKAopogaqxxbyQa8Ud5Bguk/hPEfPHZdxLrvzhsms/bMAQ+R6z4oaz/\npO/ueU9NV9hmEBh24Hyfmxo+teZtS0zDK7esLwSUYBdOxqctf0TMqHsAkf8K\nbl5F6rmnxsKWPT35aXH8Zh9STJbt6xkQw+S4Q0Hbgc8ONQpbogF8EpKsunYm\nB6xuoIw0/ez3+98dg+PgO3yyK2pPM3rPpWwikCaSmVv71HMu8K/+5FekyyDS\nufUyNSFslGTT6NrmXIw7S9Rn0IbeZsnNl/Z+fTokVjhaP1xoBspdD+FJHsta\nFt9Mp3AHk2hE8PGhvvH4NfmWRT1pfPH9Gjmaa3JvAQHcq/jCR0BHIgO5mozi\nFIAemmerQQN7OrCI5ZsrlLCrY3QB90Mto+vOCM5QlJaM1FgSaT6pMHbGWIA9\ns4HUnYVtI0oGb91Te+n08oH3ua8OrnqlENJ2a7jqmLMCxNnZ58PqCpBw4BGt\n4eE93E++DK1coJL7Ib1VkxNm8SeXrgNi8cDmoizDNXO6K6GO4DuPe8W2P0V/\nZ6/a\r\n=VQcK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "c35d5e1537bf326528fde88a5e9858ceab3a72b8", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "14.2.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_3.1.0_1589155781397_0.3715703895400382", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "fdir", "version": "3.1.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@3.1.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "72d2d1ca945fca5239e8fd9ba1e6d35d173f6441", "tarball": "https://registry.npmjs.org/fdir/-/fdir-3.1.1.tgz", "fileCount": 13, "integrity": "sha512-+e68Ju11VJXE9qSz3DWMsNTo+vc313hiJfk0tKffZX4vT3mkDsxNiITFVJL1NdPbA8vEkMTeBAhsiMGhuL3tLg==", "signatures": [{"sig": "MEUCIQD/aJiWhiBZo7YX4axCtpa+b5y1dNQpdaKNaqHvym31awIgb1M4kZWQangzC5SzZ7XdqX2Q0cJkfIX5cEMWYWShiWo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuJpLCRA9TVsSAnZWagAAccwQAIxcutiI70FLjeIUChDh\nk2/m4NDaf8eFeniMauFjnuHQ7Qjb2Xi2BUadpMePC/9GrxABk8qTTaNHZ3p4\nea5deYqekT/8e3JOKkR5CQPiX4h+NwkU9ntQy8z2WNmF1wG+ugDiNJ6EK9+C\nAVLR08gJtFiY80ODTwk1bu9FS6CfCpckGNIYu42mXT0hIxmaXPgVR4b/cKvg\n+ip+xU0cj2HYEwNUnEaXYZoBB0/aapZUZQN7bgZB9zwaN9TSVeEuc8d9iP4z\nAvfo/LCFyPPM3tAr/vZgWKvgNy7vg+vGnRg64vHZGIkBOUaWcKox5iKflxIg\nJ+akhJIbHEudS4gyJOQadRJpARoVBOsL08GxDYdsZ0llj6yhF0Lkj2GmTifj\n2nxFU+CIyY+OZkvI6/lfsHykjiw6BnqbjI7qdx2W4aZnRkKeybDlTLlZGE0z\nj5E/zTlI8BaH6yQNo6XrM2hELB57K2OsIifJb9LOcrmc/jrAh4eHGMM1PFBZ\n0ZYoNecK3I6CHORDQtmTE8wSMKUQmL9JiCdSFhSXZLdF6Z7LNvKEwrQYFbLm\nwPap34TMXBWFDw/64bd/7H1TF46MKmyWk/3emsbTPsXGE04AjDmC1IDNq5Kp\nQ4Fhjt47C3HxN6h+BiwGTmQUivwt2vfKVinnvWLeFCHZRmNrTmqjIzkG7tIb\nbdgz\r\n=p2Ai\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "7a553039abd2873246d48ceddbf0122ddc37040b", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "14.2.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_3.1.1_1589156426975_0.12149584649819745", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "fdir", "version": "3.2.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@3.2.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "75706505450dadf3575ae22e67667a551cee5c8b", "tarball": "https://registry.npmjs.org/fdir/-/fdir-3.2.0.tgz", "fileCount": 13, "integrity": "sha512-bRbMJtLS3CpqhBR2piQgsz2rmNWxi2L/vYM7Zg5m8yHyQ/mA0bm4l3c4hjkdhz7ttRooQ5rZzobc9k3mH9tekw==", "signatures": [{"sig": "MEYCIQDOK4hD0cuTcj5S5FC4gQPdEEX+zeQ5PlAGd5sUOi2fvgIhALAng4DxF7sIJ6KkKfxWzQTQoxYaGsNHQO2FgSIv7W+t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuscuCRA9TVsSAnZWagAAsGwP/1n8HUhk947PsC7Rd9Ei\nWyuTPOlZ70M5gFKquJM9b348ITBER5cNKIdcB1R1pByMy1fZaOWbIaZA+tuT\nD64g+ZyOFv9PPDDqmTLl8bo92wyHPjCq4QPne17+IYEeXtLWuQ1fVqZIASYd\nBZMS5XQLzjJm+6TXddigv9jXrjHUaeVZ2RXzGtFzdN+R/Xz+W3tNxhDTOQL+\n85B6p+hlgHnNu/o4NSDycPOkMQaXV0efRW/yXYAGcg3EGuPIBCphEq0NLiwf\n1x+pcIKr25UGtfAuiq85ekbY8czrv3dIbuS17ndsaQVoPPYLG/QhVJk2ZsyX\nEfnnN4G9H7PayMZMEfioNPDrqtaMleCg6DrGZvRe8hrglxCEZc8AlujWEJhL\n5uQs0rp0hc9BHX4bJvOwCjaRlvFV9rQa8TYZCLxwWcenw6vZ4BlMpTJmR6Zd\n35o0CDXWeUBj8kIoZIHBosEQ3q1WHRD9xM86/GQAMzcWS7faq5PxSqjPbJLL\nDTfk//0DIw92PD4jBpo8coFwSGGzZ29IwEdw/AmSpcyAk/ONhj/9LKKXMQE3\n76Am02ieAkhRaAMZVK8k6uPk6OLiDYwlI1+MZaiNpoWftnGhEX29eC43Y9/T\nZNlfdswgq77RvKdNf4yh5i9azgNRNTnYpaO3nfqGepvrsgJqpir6sWzfht+y\n4rnU\r\n=rmds\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "ecb1444a1502a41327f002149c47e7011bccbf1d", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.13.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_3.2.0_1589298989806_0.1788472725109358", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "fdir", "version": "3.2.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@3.2.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "cf9f08bc58deab8efe9b273ec526c2fa777e7118", "tarball": "https://registry.npmjs.org/fdir/-/fdir-3.2.1.tgz", "fileCount": 13, "integrity": "sha512-5EdvXijomCF1mFaRxAKzdXMJ0aSs97jJG0Wib4bUmkL1xPve6fT8Ja7dGy3v/ynci6pgASV58wFoWgeJoJ623w==", "signatures": [{"sig": "MEMCIA2tsnZx17MJY0EJG5ZRGVI28bGirvEjBM+NpAIHtwl4Ah9UGmBVnI1PAAPyDFHG9epANo2v7P106NWHqpc3VZVw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuugZCRA9TVsSAnZWagAAW/UP/iW0yepN2WgyxvTQPoe3\niUogrx5mKCrm0XFulSwubuG39JdnjD58DreqJkDWB8c3fJ97tkiyXp+eCDjw\niCC0GY3SQ8opv8NZBdCfUrPpx9dYBaauF/T4o97LGJt2MW10uCQFOxC/xzHP\n4ewf5+0xXb8nMHImkqiYOLoRlhdWyrH73UU5cMgd3Da4oJ9akk6TYHgdAwIP\nMgYFSCqXiU2UkSo7jm73k/zrZ3L19mDCFKQoENi3p2I3Z1FJ98RN7Lu8H040\n40X8FcLy14SXMYaZhbEVK1sCPynNDmsz+M+OEblvKLRGuPyZ7rgjnN2sOUoM\nLdr7WQu/Fg2+5PIGPesW93Foay4QMz/sYowsUHlzEvFba5z8F7cfmS28HcFP\nSeC2BLiRnla/AZjoM3DNtWcGSOc+Bitkg5FrdnyC8Cc7lokcEEyN9XRkAJ9h\nVH0X+2qyiR66RpbDi9EDD3cl5UBVRBJr/SUuWQHB8QLsRWKltot6yBKT99Yw\nnOmYQYmbevINcsiasGm+Neq948tPDFuQ5IAHf+RyyH0CSv7sX55PaBUhl2Iu\nWJhEHi8WTABpF6Ndh/rkkqqFtGc1Ub++WznpAMmBq0oaRw0ZJWIjM1OWAPb7\nha3gc1k4jzmmHgGTEP+KoyAD7tF9oJyibdcHn0FeMXhr6FK2tk4xwJKrPCZy\nKexJ\r\n=dzcg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "0b25d781047054d2881e75299314587542689eaf", "scripts": {"test": "jest __tests__/fdir.test.js", "benchmark": "node benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.13.0", "_hasShrinkwrap": false, "devDependencies": {"benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^1.0.7", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_3.2.1_1589307416960_0.47067713288742175", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "fdir", "version": "3.3.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@3.3.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "12e40d364d8a0e23fa7de7bffee45c8718ebca94", "tarball": "https://registry.npmjs.org/fdir/-/fdir-3.3.0.tgz", "fileCount": 13, "integrity": "sha512-JEQLavRfrkufmxVTMqEIN4vvkr7tPjbTA5sAZMWZfZxhdCBdQSPbWFg8MOO74TSQXban37Ub/7vKl4z99V90Mw==", "signatures": [{"sig": "MEUCICvBwYTW2ZKuQikbKAMlAaKuMNJ6JWdTHqsAHNsGnhfVAiEA47OWQ3nX46OjiET2QcSNQ1KpH9JpKKIQZ4NIPWiw1iE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeu7uxCRA9TVsSAnZWagAAlaQP/jQhEnkGgP9+Pvf5AjXx\nHo2KZeVhAKFmUpB4lPE1c0oWTl9NoazVEI7qedom77EfRyMy4O5g1GabNGJN\n7TWqEz7d8algIRT1lAtxkIMhVDaSD4H75URFtPKkr/u9qdIaEyE0Zyx2QsSW\n59EbRRGBhSNQkVmUYgLXgoehm4B0LXFxq493GPqqGXvwQULx/XhTB/OQpaF0\nDK+3Iyd6jYbT3jd0Fv/yW1kaJQ0rGanCz6R4ksGZgzdvhaXYHAf8ihw4Wi0g\nH9xoTQElGkorb45MTJFa+Y2go/bx1YTUw4ZxjpJYYbcBANK9X1BiL9c6r3uB\nEU1U/L09S9iGI86UbpGCjGdHiyQJRuDxBsL12lItSMqtNGt+sk5SFk7eJuTb\nm/xIUc5mgJCduN2uBEf7bhLbvpefCLKPavp7Km2LisOCHK79wqdd1aR/Nl1Z\nSTM+wdTiF6YDJg5F8r4dPXA3jC3lqFw2KdWbtHVHf+kk3Dfd6m9Ez8CRVa/n\n604yTbr4gPO6Ts9J5Uqk7WIQs2pmwlxTGaoglka9YIbPbbkPNcpc3uH9gFam\nj+70Zc1ZzH4lqxvWzh/9jvCqBu1rSgnqsEInC4oAxHiGZzDluiD8T13ItVLd\n4PjumGskelc5Wh//MWmEfQ4vZEUzcUKVrIz4OB0PXjgV/9H11zM394Mk6WET\nJQGO\r\n=sVxz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "e5364d9fb12935ecc788e63a0adf7da14cb5d0be", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.13.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_3.3.0_1589361584952_0.9290802931157944", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "fdir", "version": "3.4.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@3.4.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "8e8596a7f8c7634c86088580fca55f73b696bbff", "tarball": "https://registry.npmjs.org/fdir/-/fdir-3.4.0.tgz", "fileCount": 13, "integrity": "sha512-3HuDVf8DkK277RlmsiDJFZ+pWqB1HzPWNBhoaUk4vPKwaMHI8urdA/S5UPzI8veQ/qLJYrMKC0poqQCJ/sHOyg==", "signatures": [{"sig": "MEUCIFsR+/mF6SqzezTUHVsG2wuISpq2+Z1FWy5OaBGyu522AiEA6Rz45oS12Oe95Bh+oRZ9Wi+wr7Te6sIxmEBugLgmhig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevtLNCRA9TVsSAnZWagAAbQYP/RvIEGQtX+/5C9J972oF\nQ1Khyuwp/VpuAV97b0hxVvg6YBfajg/aS5wTiEPVlf/p5zqcU9ElX2l50E98\nJ6Dl9qJJYXb2VLEetuk4Eqdq0jnJ/eKmW1eHRYmbAga8jU3UYzgGND6tzkTT\n1S1uJJIsvLxB+aVi5xNZcMA/em4DHALI2hmkZZjHjKHW4LBZaf2LjRZ00ulK\n4zVbNid8iQtHgNiN0PDfaZ+Iua5YYBHx39zzPoS8Tcfy4uW4jeH7xL8HDDjb\nAl/afErDThL4PcFITplLNPJvEbcktRmDLcXHSy6OTET3drWMi/p0HsnBrcGB\nXBzNX9YFS6VNEiENoZblARTyOyBQlScP65MsBK7cBOz/VKa5ilbfQS2RRJwn\np+9CnPWoN81IacmM+NCcJwAOuql+7Q8jHv9wzaHata73qH4lOThIpv2g0iQ/\n2VFFoRRPeHjLzZOukngQ+DMadaChYugSpEaw3YNY7WdBJMM3hAB7Cjwo7VWU\nPY+w3BFApavvAbYxEkjQAa6YLYCE17N8lSWyrxImslGqC9l0/yvIy3jROOs6\nSWkvOSw7X3ZjwcxNtj2SWkCCxjD/jwgruy5/m1wdRxawM7Y9tokmaxvP78FT\nNsaTa1Rr4G2RMOCNdGyIwHr/87f67tPXex/meXpDckJtxinOKsvAb7TqS18z\nBOHZ\r\n=r50G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "6c20d4b9eab9437cc7e6d50ee243448d2746c376", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "14.2.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_3.4.0_1589564109256_0.14450182034141967", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "fdir", "version": "3.4.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@3.4.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "faf45fafe637eae9c688e50ce2cf28bd27a5eaad", "tarball": "https://registry.npmjs.org/fdir/-/fdir-3.4.1.tgz", "fileCount": 13, "integrity": "sha512-urbsiP7l58+B0JPxGv1UlYtOsMyr+DNxHc7iY29bs7TN0VWK/8p4knqbjWaeW6f/uA//2mD5BiVED9YTdEgedw==", "signatures": [{"sig": "MEYCIQCGsIfhBGdlsT8z7nR4QsFqp1qhP6SL/Ws2PNCavukS6gIhAI+DQjYelGzf6ExrvM/O24LmKUxQSLSiI8BajYxf2TkY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewQTtCRA9TVsSAnZWagAAL8IP/2iDwQSK0AgKFEBWtGbr\nyu5rKbfPn4krhD8cKnSDvxN2Ru5ElE53xC08ew3DeqJflYFiHn2RefB4YSXg\n3NcL1ekz7RiFGTRbsmZ2VYpxc6UpK8VuwGqqmcy3VslNPJOACaXy0F0BBvaz\nRUYC49KToMoG8cZFkzES4qnQg86ArTBpf17y1Uu5PBKrDOsOnIAJLMsLXKzS\n7+dwxcT7jt1EPxD3c/qgp75V8M3AqzdrLI7SPhpUEpiDNLI68f3gi9bkScdS\nvAG1rCOGoZiJNqNW2lOl/gDEb4Y3kN/qJwVszygVSqAv/qOaz3Zeq89C5ymV\nBOy+ukdG+5nqBcU5hq7ywVWt8tJFZqU6psbG1KurBRX6MSmiykfe4/hFmYWq\n+jDmYi+v/NPYQ+bJ88Tt+8QzCMte49ng5fMg+7Z0eOXNS8zONMOna8M0C3WY\nNNgKJZ08jz+ozDJEz1ddkp032xcFnt2GCW42n5tDk+DxG+sGXbOZEdp74nUS\n2Fqmq8RehZkohLb1RondnHIoFaiF3McqygrLU6mNTG98XNGGR7zC/Rzr81/F\n+4MLOiuMW9gs/mhsLRHkrapD+RuoWbcXXx9tsv9vArRWrTLyk/+E3ktLu3cK\n/myFS52ZI9gAxx/grrKtlE+o7MC6YHaSRdea/I/Up7G+ijZQbN5NW+viqwxn\ncuwT\r\n=ZWqh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "36b51ba05fd54c3eb7a661674eef29842c2342c0", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.13.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_3.4.1_1589708012501_0.8111172723985212", "host": "s3://npm-registry-packages"}}, "3.4.2": {"name": "fdir", "version": "3.4.2", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@3.4.2", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "978bc4e8cf9884b03b1429d50feb0cc9fd4824ce", "tarball": "https://registry.npmjs.org/fdir/-/fdir-3.4.2.tgz", "fileCount": 13, "integrity": "sha512-Bya64kBW9us4CmX4T7P3JV+TBY0cXB3JhmyYBQ6CXEePat5FD72f7eaKt75ljSP2R8EdwD4txEestENsH/D0Jg==", "signatures": [{"sig": "MEUCICfSAfDlJ9lVbUMR5tJg+sidsxeEOBBj8pJM2DTRRY4+AiEAy6+6LmWxCZnnICwfxn3fEIXhrGkzck1HMPOvh/4tqcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewdk4CRA9TVsSAnZWagAACRQP/2nY2JkGXdtmpCG09JU0\nKu9LTiNYt3dK2br1kOjBCdqgRYIYfJzB5X/5joNRjjGvmO1TFjfflVBXOlIk\n9rn1F/pbNG8UASc1Hi8mg3pBj0f9VUyXxIxDco+tvMaYFtUn9IeOpO9nQCUP\np4c2Aps4X1ejWIk/wcpw006EGMZpKBWzCCdhtlYfvZiK6gf7hc2JZDTX9Ggr\n0YPkzwSNgNvT+aNG7sdMKex7souGGrEXhwXd4DWTkD7k0aYo2fZ5YTR8Xjrf\nB9yoRSBrbHy8kvNtnO4/R0bbSfzO1iZcxuf1JGaLadZ1XWT6aBvvyCIGxtFn\nRwIcR1vms3FE3QrJleat7Dxl/w571xV/Dn3HEOn9yXqeE011wD06SYDZ6Yi4\n1+vBbMMUwcLcqb3L3Xl5tkjWVmPcyrvqWhjy3WnM0B+FPwmsxyh0ZCGozQ4u\nLBuOS3JCq+jdYcjK/0y/G8qbozt2pJAQIRcp5jjxL8gYE4ncn27KFs4JBIRJ\njNrDdiH7+RGwF9XKddy8MoNwdBYoVCFtv2234WvVU4Y5OGNhCwoRkGN94qsj\nNurtjye8GLFmnwBMoLza7AmBDkN1bfA/FU9vKh1aCvjQmxQTF2xIvNbfUeQZ\nAW8+ia3ddS9xvp7DA+iJoii/dV3RBGu2uxMmlG+vG6GssOj4QXZpP50jFVOD\n5mm5\r\n=b5Xu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "d6afde783bdd28ab90fc3568eef2980c1aa5f74c", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.13.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_3.4.2_1589762359895_0.6151842423977918", "host": "s3://npm-registry-packages"}}, "3.4.3": {"name": "fdir", "version": "3.4.3", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@3.4.3", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "9f9470d8b06af967ddd597a381c140872d7c8b74", "tarball": "https://registry.npmjs.org/fdir/-/fdir-3.4.3.tgz", "fileCount": 13, "integrity": "sha512-JFNfD4wNQLQ/8ht/iH4dLdYYZaxNuCsld619o+eKHORVyjzcog3YU1X8kpk8J525I8EMnqBW80SceTFdLNtklA==", "signatures": [{"sig": "MEQCIHnywhApaxpbKI2EfuOa7GGV282oC8uExb9Akmrc+YMSAiAtRYBE9L9VI+idrX8iShz/P4Tb3TqWigP2Rb2GqgtLGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1tvDCRA9TVsSAnZWagAA+4IP/AmrtT8dwSSzD1Mc5B03\nYUlAYcxanp5gJJAkausCFyT5tDd+5Dgmbu+MrxW9EHm32bNCWN5jfNq1IAJS\nrd245j2YLnup7t8T9vzxW8p+tzk8/AtHCgIJ+3dWVD1TRdnT0TIXuFIBsa4a\nZhZhdEVeyJmdJYr1vYofxlVli9LP7cEB/6aRHButgFyWwpD75a9gjwnmCgxE\nfHSq+xfHcP7dz+ksJ0NEuA9o0+d+ywIOZps4G5DKK02hMrnYCZGQaH8jRz/B\nedLifE4kKGhV4UycrUqpjxkNi/SqTrqeRUhiyiP+7tj+rKC+ky6I/fdaptHq\nuWUgaJZ795TtYL2EP+X9WGvdAHawQjTSjExYGXr6sf9te/vBVhMOA4KG6onV\n26eKKjVbsPc34KAhUNNGMT4jKtAc1fecqGzqbFWlMfeMtIcKZgi0Z392XLLa\nBnCvEw0V/l2mW9waXbtMvvExcWaynO4PgWG9UY7LVS8532Vil6gZyaJ/e7uB\nhMeyIKLstpsb1utYtSPTqaZrXvmmPaVzUGQojnn+soNa7YSYw0WefxaR07I7\nVhkh0iIGoN6zTXSG/E1YXBE+0yu9na4GXqfQ6MQQBrso3okIfkgWDY/R5wAE\nplrCdNYf79rtYJU06fe/FmdVtNgWiepkbvX3AAE8Fd7Wmq8YfLhGTfDcwg+t\nFQQT\r\n=lAmR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "5435c5026574e600fbb01084062dbdda2f2ea165", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.13.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "systeminformation": "^4.26.1", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_3.4.3_1591139266836_0.057827494166922344", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "fdir", "version": "4.0.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@4.0.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "d4a3d7b40fc10cbc3ecd3d1af5674eaee5cb46b8", "tarball": "https://registry.npmjs.org/fdir/-/fdir-4.0.0.tgz", "fileCount": 13, "integrity": "sha512-bCdcdBowDx2seQyTn9C/HnisDPrqMr503/fNF9d8MQCRiW1u5qGrCLphB6PCMpRVd64NHGEQSnKk2CWOMCOpfg==", "signatures": [{"sig": "MEUCIQCi66S44r+hV43IbbMqtyw6gL7fRCFQLePiR09oNHECKwIgfAymBbxFs2LkbwwXOPnsgiAdzBGPBcd0RUAwEol47OA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHxxDCRA9TVsSAnZWagAABOoP/RIpK6wFc4aJ8PoKDYxo\ndYf7Y+79K8q5uj2PofoVmpdSf0ApiARnIgU8qzSjVyVifsUYDv3ZCRQXoUIZ\nNJXSuFnpPtnNk93VEYm+rWQMQC9eeezrgjcJLVLUq9w9ADqfffN/9YWR9ORY\nm32OWR+xwqjzJgFkqwpJZTA33PTn8Tn4WZaQ9+vl52OdCmK06Wb1Fr+ztDib\n/kkoqn43l4BSaZt7F9nlqHFRGhPUeMhK9a+0r9uYvXqq3z92MrRKl32qosU5\nOy5ci+m2LhgMlVrFnftZbvutLEt1ZegYhxTjtijjLV971cSB0v5vVPkJxOPC\nMUyWvIw4nzHXvzxI3yRcaLm1vkZqgoIs+/uXgbYLM4GqxZ5WkisaDMFr2CGG\n6EU9bXqe1BOHmjCNl863hDQkNu2/SuCwpP5KGcp36ahd1woxeWpTb9ilkYRk\nMnIdvMfqDHxY38jpRPgefL5hDbhUgBPJmaX1aZJuTsNGE0EvrwxVHexcyxsD\nRT4m5EYJr0yYjIoXa0fw7SRgvmZTrIzNrrhY8FbITVEQP2UEaqrRXVyYhFXt\n8nb0FYvefIBISRiyqHUiMJPgXRF9HzY+ru16YN4X+c07JOvxXej5MH/JWmb2\n5jQp/gmNID7z9QOmxxCywqSDUuBavgpPcTR/jAq9ux7Vxg8+xQDoQmLRo/LM\n/DZg\r\n=3edT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "293319ed0a1832bf337543e4e4a7f45d2dfb3e34", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "14.2.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "systeminformation": "^4.26.1", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_4.0.0_1595874371394_0.68526622034767", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "fdir", "version": "4.1.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@4.1.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "f739ec79f61f69779a6430a622e5f54c57caf921", "tarball": "https://registry.npmjs.org/fdir/-/fdir-4.1.0.tgz", "fileCount": 13, "integrity": "sha512-o<PERSON><PERSON>hnPg4nUIkd6w22iGbFD7c7UvVnXB3a7/GHcPSsXDUGm6Jxp12bGI5O0gr0YuhDh5l/vDExdHOnrW/j9EqQ==", "signatures": [{"sig": "MEQCIH3Ll8u7A9D/48yyavCUTTb884iJZ3ekHeNzZc2MrBWNAiBDcNzfYEs2GTFGsB6jPBJG7x9kJqaesxvTANaLLS40TQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKX2UCRA9TVsSAnZWagAAN9sP/1VYg2HylqUMx6Po7oq0\nlHDDLkHzSd2UhMiPOM9HRdvr+L0WlivvbO3jYingv9yoGvPc0QSfgfxw0R/G\n8bJms//OBd4spbXI8FbmiXJzkcVJbXj0OWNEMfJ7WQLdbY06A4LdzYFrKdSe\n9VfTBbW0hSTbxbAwz2NzrnGq8VMR47+78d+Z/yXIRUavIM5uXM3J3cegREVv\ns7/kvXDztDr6DlcImLo8DjxAYcWWYUUkszWW3yK3YrduJ/gPb4ExTNNpyeen\nM1Qht1hAMnnXnjPDZaSE9c8FIUch+qbm/Dwho4NYrugksr2waN3W+XOPgSnw\nwN/weYGMym5besq35kHONGx9EMmj/A0H7ZBpFtcvjhzNjUXZ8GsY8/hopUH8\nIt2d0S3wGNpAzab/vyljVVbtwP3ie4EvXoVHl3kJzcepGQe4mb1/ys9XwN9C\n2j0t7Uw2G7OEQriQyzJdqfTYbugBxQiONloR0nBeh380VqZSmXg+tmzPZbYT\nWOUqCZFPpmtMNrpDyG59W2D+nsDszCRSW7K3/Snmel17zTzF6FIFqDMAZYRZ\n4UdXkthb7PHd7KS94ISScLieC3IdAPEV9e1gSBhDGeWzbjFM1vfZlq3bsMog\n1290OikjyZQgE0CH3oEH2rlLQodvQQSVzH1HcCEbOn8IwF27E4g0fBAyThNd\n70+/\r\n=wGwL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "6d0adbbde36a8e0f788b7a2ca887a6febefe393e", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "13.13.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "systeminformation": "^4.26.1", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_4.1.0_1596554643702_0.8993769255830975", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "fdir", "version": "5.0.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@5.0.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "a40b5d9adfb530daeca55558e8ad87ec14a44769", "tarball": "https://registry.npmjs.org/fdir/-/fdir-5.0.0.tgz", "fileCount": 13, "integrity": "sha512-cteqwWMA43lEmgwOg5HSdvhVFD39vHjQDhZkRMlKmeoNPtSSgUw1nUypydiY2upMdGiBFBZvNBDbnoBh0yCzaQ==", "signatures": [{"sig": "MEUCIQCuxMOaLxw2fxzoCCUBqwz2EJtuAW8a//fnyAq0XccSPgIgQQsE+RprrkaBjlRon31U275AbRorhJAXKOvb3MhKRAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/dsbCRA9TVsSAnZWagAA+k4P/RiRpgFkQFjm4WwulMtz\noeTl0a/y7OuEqAPQctr5pBipnoSbz+atwKhsEIhzIOCMEDY24okExWkmzP2Z\nLCsV4kn+iwMOTqaHr3ZzmVU2xldAOu4vhSuLwokKfmOk2rP0NTI1j1Q37Esa\n7d6CfjzkHfApKHhcqRjyh4dn519balmyghEkQkFTqa71X0FUaJT+0B61lKJ6\n2GujVVLtVSVl8oQz7Roi1VdEGiOXbuWqb066a/3ZBHK/nYfHwhUzAKcORLBr\n0VQ8CIlQQaDOYXr0lHFXxph3uIar8tn1/ZjjrJeTfzfad6dXMWNbJQ6ixqXD\n21BVisLlFXCALTxLT/v8nwVOACwsLqXgeZHVL7mVC8Nw+ObIl8gRUa43x/9X\nSB9yROgpZjchuIENbJq8+DBRcYZNl+jynmnWbNpIdjelRBGO3MbETzCUO1AP\nv+vzrRDeC3svMHjPxSn1VxGVcDyAJWOqJ0zoYZT/+r40PQ70CA3e4esiey7Z\nHGH5c9/OUbnZcxadi2eLaZ5fQWO7O6x6yVu3PP26LDtafUNzUL91wpeuT89u\nowLSq3jD7SyTE2Syb5XSzMZ12MYVcpJQTQyVaxQJmAioJM+L5JPc5dYDyq3X\npbqy7Qz5jA9N7W5faJly+64ZhF7CJVMBJErqVM/oWlAw+qzJY6zE68L5XRZ6\nbSO6\r\n=3719\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "05b69c49e56f5eecaa4dce43edc290e09413a801", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "release": "./scripts/release.sh", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "The fastest directory crawler for NodeJS. Crawls 10k files in 13ms.", "directories": {}, "_nodeVersion": "12.18.4", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "systeminformation": "^4.26.1", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_5.0.0_1610472219143_0.038973662184640157", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "fdir", "version": "5.1.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@5.1.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "973e4934e6a3666b59ebdfc56f60bb8e9b16acb8", "tarball": "https://registry.npmjs.org/fdir/-/fdir-5.1.0.tgz", "fileCount": 14, "integrity": "sha512-IgTtZwL52tx2wqWeuGDzXYTnNsEjNLahZpJw30hCQDyVnoHXwY5acNDnjGImTTL1R0z1PCyLw20VAbE5qLic3Q==", "signatures": [{"sig": "MEYCIQDn7HtTXjot94T6JpuN4ueFHNZYQJXmF8M4BV/KKiW1VAIhAOqgdlwzexze7uyPe36Usg1X3VOuGwNxHyJdC0z5ygjw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgp6++CRA9TVsSAnZWagAANf8P/jvI+HVE01XsgTFLU+D5\nwmGYY+43cJNMzZ5LpRfy7zLSS7X5ZzPQYwlddStIHSsUBLt8bh00Ev07YSLU\nN1eB7W8u0TVp6HR4c/meg+tNK8/QYOdom1juCO8Yj8ukNmmlPOno26vLrSBE\nTJ0yATe2vuuFgZu/FQytpYU25kpNUvI1ezjYEzouglQG7lRoONL0qYC+CQze\n9d/psQsYBVSYkTn+1Ir/lzByMVyI47HdnODOS5q3TJ7NJ1MjMD1pzZDkOLaF\nC7L4JTR5Twkt32prk38Np/vM6QDHBzSr3iOypLJulE4RBekijD+r2vQZLtuI\n6YFNAxeS6SJBuvDtOAUmSSjfOdf8sHt59cGe5vBJnOO3wPC0u7etfEoipsox\nws22nG8jb6FgKokZ6O1Zoc3F4VnwOgEiARUliPWyzvjSSD+nlqewD3oY9heg\nleJGEuZ+W5zdffzBLxaoT9vcxEL3gZ0b0VCfxjzFss25t6EDxUV48jZMRhHr\nQ1GXGVxmsNNIfVgTcaZGIKjxVBqBhXOrfgYqKWKxsiHogaI3utD2bB7WMpU+\n5Z5m3UXT+YukDx533qUp5mRH2fG/KyXBVe6Nglc1HyXzHr/Ht1zABM7eVRLp\nov6WRejW5rbz2Hh2FN1vTlDSWRFapakiFO9vVnbcbzacBfgYrPcDTsQsBNPQ\nRb2y\r\n=UpgX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "3b1b7076322353be169f00fe38a8bb1a8c49afec", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "release": "./scripts/release.sh", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "16.1.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^6.1.2", "mock-fs": "^4.11.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "tiny-glob": "^0.2.9", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "systeminformation": "^5.3.1", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_5.1.0_1621602237540_0.3325064570452141", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "fdir", "version": "5.2.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@5.2.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "5b3d6ae282f8bc0ef48bf913d46f9e08496304ea", "tarball": "https://registry.npmjs.org/fdir/-/fdir-5.2.0.tgz", "fileCount": 14, "integrity": "sha512-skyI2Laxtj9GYzmktPgY6DT8uswXq+VoxH26SskykvEhTSbi7tRM/787uZt/p8maxrQCJdzC90zX1btbxiJ6lw==", "signatures": [{"sig": "MEUCIQCITETW2/hQlJEcrShEHv1/Hfdd8tvVBBv80rxMfmKPhAIgRSXkGboZrh40mZZJh4XDdPx3HROlt9KG8SsJRwNNABI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5lqTCRA9TVsSAnZWagAAPzQP/R1AZV6j2PNOufLXZLG2\nFScviDROLgiZs8O6Ew3FVCJUdbBb6A8zEvwCyn18ZJMXH/v4TAG2ZPDsqKAN\npWIG3y77sHt4Ha+UxnCMSSobaPtcr0sanhHf+hJHO3UKmYETwetRvh3/y5Po\n7n1PxvvAg1Z9K4XnyQHM4fdVDslcdKI048rWmqT9U1J0UsW6h8k0DGUgJBBW\nsn1thASCXHRBA2hh/pxlSZZcQm40we18ffONMQkAiokNEmLKch9tTH7Mtmc/\nSnFtNCDcYb+Bny717qYDFdIzeQNjybYNaRMlRXwt8pUK0wXPzIEucpc4pjjg\nlDcajoG4Fqxzhvg9/p6uQ4t3KA8HfdSv9eh50iwZHuOWRl6kgOJ1a11Kr8pf\nGLJyZEKxAceWDFnFsGroKxmCkKpAs5eLrtNSi6taRbzix254QQ44ERS4DHB+\nz6eAWyZmONL/wNtOXwZFqphZPpSH86f0r+R6YpgUbz+2AbpeHM6W94lKosCk\nziKWdvf6bD0nYoaWAhXJBwk6TdQeZIGZr/dEEHma9jwBlfZ//XGkgmaM7+dp\n67ugHnukKd/xDQCQgDN/G8zrE02SXGqMEmkXWiWhCakSdoBicW18uhKkNZMS\nIeR3giYiLu5QhCVah3dyHAIr7WZOq+IqkfgB/1ODZXw/LKSeSJ1CIwQD3/AW\nxs6+\r\n=CqHE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "65f790b86223e0743896eed46e30db3f886a9e49", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "release": "./scripts/release.sh", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "17.3.1", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^6.1.2", "mock-fs": "^5.0.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "tiny-glob": "^0.2.9", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "systeminformation": "^5.3.1", "fs-readdir-recursive": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fdir_5.2.0_1642486419134_0.28027301142453975", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "fdir", "version": "5.2.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@5.2.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "d1eefb071c860f9c2ea5570b05cf6f74089c4171", "tarball": "https://registry.npmjs.org/fdir/-/fdir-5.2.1.tgz", "fileCount": 14, "integrity": "sha512-YtT6MYUmSreefWXhX8XE2tmK7cI8+nRyi5VgOI0CE6AQZE7UyFWwjTbitLCq1mn6vRdYc5zyjWnUHeb0lN//9g==", "signatures": [{"sig": "MEYCIQDVjl05uVKOTgS1e2LwmLpNznYY/PWnD+ozrrX/YxuDggIhAMGysQuoO5ib/xtjVCobpTMCmpODn5EImp4jhlY2o4W4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTaD2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOfg/+Nl0vQpxiR6/ZqQ5UXbWvb4F8seE4gBlbjmZ48IiUZ8/sjal/\r\nEHaqjgB+7fAD/+4s7TEFDrk2x02OjneUX3UQXZQrOFXftVVuHK353QDLVOAr\r\nkT81YNqYRsWHwE1br8nsTmOyNOofYnffLxFpIkWanKn3GSVDr37JogV49hTz\r\nR2mwbqsdD9JZlQHQcBmRtwoGP2usGWklIwg4tmDGllWiE6u9kQr9FyUwjm/v\r\n7wqIQWmUQ17lBhRz7PaEEl+WRLiE2Ew6Ov2GhkvPJQhOxCc50moQw0De5C6x\r\nrbf7hFWmxy2O0AGcPsC0P35mBqPoeoFVnsioitYpGNPWUzA/7ITEH32FEXaf\r\nY4kEggwWBkME8wNlLmkvelaotiy07EwRx+yi74zL+TAHZphZbUzZKMPrFt+K\r\nA/ZlLGvIwhZ1rL3ontsT+sGj47exUStl4qIxlfjBDE8vHvO9H6kUPoMDwQKt\r\nl3NTHGi+y90RIY2TV7BdX7Ete91D9nWdfV8k6mPUhz3WgrcIEVIMw/wYJcvx\r\nF6ImVWhmtYxMt7aHTwmbGF77rqkX7yyvQO4U1BFixv5nf5ufPqwF5CN7Cno2\r\ntxSibT0UURcmHSNkkCM0DTgOPTLz9+spXTEyanZQQIac0Ap0Ci6Hd1L831WB\r\nJr3aOeaUPft0R7wAnN1wk9AI6YBor4+eJQ4=\r\n=IArI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "0ed9e53686466152aa006669debdcd45349498f0", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "release": "./scripts/release.sh", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^6.1.2", "mock-fs": "^5.0.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "tiny-glob": "^0.2.9", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "systeminformation": "^5.3.1", "fs-readdir-recursive": "^1.1.0"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_5.2.1_1666031861789_0.9789395006633979", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "fdir", "version": "5.3.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@5.3.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "67c6a75edebb887906fb22fec224fa5c2b1ff1e8", "tarball": "https://registry.npmjs.org/fdir/-/fdir-5.3.0.tgz", "fileCount": 14, "integrity": "sha512-BtE53+jaa7nNHT+gPdfU6cFAXOJUWDs2b5GFox8dtl6zLXmfNf/N6im69b9nqNNwDyl27mpIWX8qR7AafWzSdQ==", "signatures": [{"sig": "MEUCICc5XkVzyqkpyNQLTq+twd6y+cQzmKj8XIV0awxsPuQ4AiEAzNSXKf7z0nSzB/lksrU4PYw6KmT7QhWsztEwXnhtvXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTuTKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPuQ/8CysDx5njtKFu624A3BPYMszyh2Mlk3bGWoCy4KXKQBooEvvK\r\n8dJs7c48ypTFlsB1YTVMpWMufm06iatc3uJvrElqxNYSvaUV+tszTctf48Og\r\nNvnhkXjOy4daaqfC21ZmsLCMv0CYiD7wMsRrxU8XDY3GHCY2LlJZDe89icXx\r\nqEHLOPxJh0sCxnThuFFhN1bvQsWbmhtmPJmkTyLZtYaxsFdzGrBL5vfTDkgT\r\nmYyoS6Dpiziy+ZLXYWohbD89tqbOZGVNuoRMULSJIHXhezWSC+P7AECFHtX9\r\nEbjA8Rn4YY+vL0Mu9e5s8iYBp4bHsLYnNgCjmCpoDxDHbDKeRlmjy5qRAEyq\r\nYfRK0c9s+tywc54brp26A5qzLr2kQRt2qut5DdpWQtMSOdp+5AOzMS7xxbbk\r\njrx+7wy40CL3BoOZNg+4ZGW6ShC5TOB67TZdFeSnf4IBv4anMMQqgWkLPFSl\r\nh4/UNtSNCXJVyi3CtKZG7vdJYy1Yuo1qKbPp2NgwQXhKIB480xArlE9nSIr6\r\ng+Megph8D/rvf054fpVofiNN9iWjX/FxDwmrBKT5MO9Ghjt15xuCEqaxZEGD\r\n0uwXO4xcYCzjA4HilYxdJYZ7DoGbUaa1gXKx8FIvn5aPWOOtViefYuZ8a0Nx\r\n9CA1RFRbHggiiN0qxOarloF0z2qbRC76eB8=\r\n=7P0e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "gitHead": "926609a108fcd53c0694826e2e2fa7f197d06af3", "scripts": {"test": "jest __tests__/fdir.test.js", "bench": "node benchmarks/benchmark.js", "release": "./scripts/release.sh", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "jest __tests__/fdir.test.js --coverage"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "benny": "^3.6.14", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^6.1.2", "mock-fs": "^5.0.0", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.2.2", "tiny-glob": "^0.2.9", "walk-sync": "^2.0.2", "recursive-fs": "^2.0.0", "get-all-files": "^2.0.0", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.2", "systeminformation": "^5.3.1", "fs-readdir-recursive": "^1.1.0"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_5.3.0_1666114762500_0.4674492286125229", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "fdir", "version": "6.0.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.0.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "277a777a39d7e54da2e4f15ed3d3ba95a4556b36", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.0.0.tgz", "fileCount": 50, "integrity": "sha512-OTY4oARmFlLhd6dMi3qWGW7MkSi5cRmorjwBp1X9SE51s61qkKspeTSPOSKzCNb/+gXkhayqQonMALkEEq/tqQ==", "signatures": [{"sig": "MEYCIQC5HXwPGjnaxU7i5RHkUaZlW42ZlfWyUZhW6/e1C9F3gwIhAIwOZkNv45YyjdESqiPvWhoxJdvEI+lN2a3lqGCDco3N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4+R2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrr2w//QJ6AK/2FWFLRkKY3ASax6sw0N925aeI4k1UWe9nv+vJYpLqD\r\nHz94NKI1aiHKQprQKKZpUZ+RMkCivGLOxjJJUCAtlMlMsKrg1knSiN05IsUE\r\nkfCcnF1N6B6X4XBL+hKhpvtIkuf+GObcd59EBXCFZk261MDXsNlL8PtNBX23\r\nZ5pSsw6F1TPRGq1g48nPARVOc0IGqZjECrYItPOd9oveZd9Xz+4QwBQkCvDh\r\n4O0YQTJ62L0+zAnG2TsvuNztT0oQW0ZaCaK20eI3ELaMucT3uR19CqwjDjEm\r\nqVrt8bW40ULXRRbaVh98tk379P1lQh+CSAIGXX+zOFobMXTRqAXy5AFVC7H3\r\n6M/ZVTnuc6WPrghQ1sIu2DvDXLMKDFCwyilgAP9yC2zRbzZ9z9C3GBBG+2/z\r\nxXn1yJRH6KjmakjwmOtFGANSbPCdfEfFCnxN+Xyzrks9ULs2d0m3MYNvwnDy\r\nJDj58BVo4obZIz6syxi0DyEYRD25nOUSIs4TYqjYV2wMrdrWpo0bTa77uuP0\r\nn2iJrLZ0xQFhYIIHclBBnSWl3qzsRatRow9+4+jymOmHWwZmpvOisxBghkrM\r\nlPRyWoohnvtEX7slCouSORyh7cVL3RhrqJGPHIKwI+YCbm/8ponyC2ktzdjU\r\nlcwn2McUbnxd7WuUE7mYw7TjNFa5Eii5zCE=\r\n=XDZM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "gitHead": "d4b6e71dd478d115b9170b06a8f1daa6dcb0752a", "scripts": {"test": "tap --ts --no-check-coverage __tests__/", "bench": "node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "tap --ts --no-check-coverage --coverage-report=html __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.4", "glob": "^8.1.0", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^10.1.1", "expect": "^29.4.2", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.3.1", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.8", "typescript": "^4.9.5", "@types/glob": "^8.0.1", "@types/node": "^18.13.0", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.1", "recursive-files": "^1.0.2", "@types/picomatch": "^2.3.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.17.8", "fs-readdir-recursive": "^1.1.0"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.0.0_1675879542248_0.7946965134011363", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "fdir", "version": "6.0.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.0.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "1edca6c0295d072802e2c67a2f4fbf941cd32af5", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.0.1.tgz", "fileCount": 50, "integrity": "sha512-bdrUUb0eYQrPRlaAtlSRoLs7sp6yKEwbMQuUgwvi/14TnaqhM/deSZUrC5ic+yjm5nEPPWE61oWpTTxQFQMmLA==", "signatures": [{"sig": "MEQCICsD4FOV5awokH50o5S/MFXcGVY6pHeFQrrs9sgNE7b2AiAoS5aGLCrhrqmHApClbGcpGgSHkUr/xTAD7SMtcTN0iQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5KSrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnFhAAmqGye5MQDXR+JpHMq2uUj7j0I0/8wl9IYxQMscJr25IKQpCN\r\nITAAlFxpGnBw8v+MAyTwle7mTdbJh36k0lW/3Nraw1IT0VxdBesgEHh6Dv0i\r\nMmqBeXrHH4I0heuplc0j4wbdqXTAsB4I+Bd9QcMeAmbY0XZFB4HT4lCKxRhQ\r\nuWSQ0xWToayPN1M0MzCrdTA53V2AhwZZcK6eQ8BkYIhU5J8hdzqrwyXkD1D9\r\nqAVtvPFjB9bQH+Im0orKQbvZfjgDujSjVTj4rWkpFBNonL9l+AqU4X2OIkbr\r\nahai2IC7ZnqvKghsC+qZp6dmWPi8Q45u8W8jHIN0TZiBf/UFF9tYdud1MArk\r\nVBZYDBX3W88QMR1ZAw4mDwB69FyLTrGUOfkRtyeOX5KPljFJlp+9A9D83rJL\r\n7Ai21yE3bM3QwRoW3kG1ySPVX9MBZ0MidPOEn25lFwSqbkuYDC2m9Um4z8/q\r\nmteC4MLH3nzKApk9vK6nL/svmSHjIN8z+nJCGrD/Kjn4WuFv3/LNs2pP46BX\r\n7UPBGC7txmW8Hj1v/05stSsvwYBdNKEAErwFLrAxmPz+w0bSk+z3GdXY/Rds\r\nA/z+t5Uj1IJIN0/Sl5fL5ZWJNzCxF4JiqBEgvDwJTxjjXqqtKaS3+HCAIp9a\r\nLggZrjUWfXna/RaH9Ue//AgYwMne5Zsraxc=\r\n=ngcu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "6a5dba68344bc7641af171d633c1377c9e901da3", "scripts": {"test": "tap --ts --no-check-coverage __tests__/", "bench": "node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "node benchmarks/fdir-benchmark.js", "bench:glob": "node benchmarks/glob-benchmark.js", "test:coverage": "tap --ts --no-check-coverage --coverage-report=html __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.4", "glob": "^8.1.0", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^10.1.1", "expect": "^29.4.2", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.2.2", "klaw-sync": "^6.0.0", "picomatch": "^2.3.1", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.8", "typescript": "^4.9.5", "@types/glob": "^8.0.1", "@types/node": "^18.13.0", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.1", "recursive-files": "^1.0.2", "@types/picomatch": "^2.3.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.17.8", "fs-readdir-recursive": "^1.1.0"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.0.1_1675928747015_0.12975760641991108", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "fdir", "version": "6.0.2", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.0.2", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "077480393c4b20583e389b1bd72b9e964a1e310c", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.0.2.tgz", "fileCount": 50, "integrity": "sha512-XJVxBciDoEpRipMYyrTCqVQA4jMTfHNiYNy8OvIGTaQzEFPuMJEvmps+Rouo6rsnivkQax9s5m5gy1lHmY2Hmg==", "signatures": [{"sig": "MEUCIA7syhQpkFX2NrWzQXzsowbAWxPwa/4PZ9hSyyzkwy44AiEA3uBOPhqzFZ16B+QF3Lk5wQZl11qw5WFI4UUedkfGVE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55247}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "b3774d9a5c0a7813e0dd27f493af62d6730f34a7", "scripts": {"test": "vitest run __tests__/", "bench": "node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "20.5.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.3", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^10.1.1", "expect": "^29.4.2", "vitest": "^0.29.8", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.3.1", "klaw-sync": "^6.0.0", "picomatch": "^2.3.1", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.8", "typescript": "^4.9.5", "@types/glob": "^8.0.1", "@types/node": "^18.13.0", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.1", "recursive-files": "^1.0.2", "@types/picomatch": "^2.3.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.17.8", "@vitest/coverage-c8": "^0.29.8", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.0"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.0.2_1690838255642_0.15017049014694694", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "fdir", "version": "6.1.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.1.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "e5813b659e14671994e21287dae0f61fec8f9a62", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.1.0.tgz", "fileCount": 50, "integrity": "sha512-274qhz5PxNnA/fybOu6apTCUnM0GnO3QazB6VH+oag/7DQskdYq8lm07ZSm90kEQuWYH5GvjAxGruuHrEr0bcg==", "signatures": [{"sig": "MEQCIEmQdt7mQvXWZhRRywe58Xt0xwIg3a5uqLpMgThT7MrIAiAfZqGiQKF0fwcPQDNJWP+Rrsk8qH20nuj5We5gVvnB0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56024}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "192e7262cc50c7027eb37bf7066479043524b0bc", "scripts": {"test": "vitest run __tests__/", "bench": "ts-node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.3", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^10.1.1", "expect": "^29.4.2", "vitest": "^0.29.8", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.3.1", "klaw-sync": "^6.0.0", "picomatch": "^2.3.1", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.8", "typescript": "^4.9.5", "@types/glob": "^8.0.1", "@types/node": "^18.13.0", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.1", "recursive-files": "^1.0.2", "@types/picomatch": "^2.3.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.17.8", "@vitest/coverage-c8": "^0.29.8", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.0"}, "peerDependencies": {"picomatch": "2.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.1.0_1691951348220_0.19719882289305146", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "fdir", "version": "6.1.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.1.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "316b58145a05223b75c8b371e80bb3bad8f1441e", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.1.1.tgz", "fileCount": 50, "integrity": "sha512-QfKBVg453Dyn3mr0Q0O+Tkr1r79lOTAKSi9f/Ot4+qVEwxWhav2Z+SudrG9vQjM2aYRMQQZ2/Q1zdA8ACM1pDg==", "signatures": [{"sig": "MEQCIE4vREQRpi/SDPMGN5fKwX2gsoA1kikpndoCfojcoSh6AiAeJwyrMi24z7ojLTEcMg8ndzWiREdb4WGvK1GL759qmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56266}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "686e20d6db007d8655e7982d0ccf5fc16bd6f510", "scripts": {"test": "vitest run __tests__/", "bench": "ts-node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "20.7.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.3", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^10.1.1", "expect": "^29.4.2", "vitest": "^0.29.8", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.3.1", "klaw-sync": "^6.0.0", "picomatch": "^3.0.1", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.8", "typescript": "^4.9.5", "@types/glob": "^8.0.1", "@types/node": "^18.13.0", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.1", "recursive-files": "^1.0.2", "@types/picomatch": "^2.3.2", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.17.8", "@vitest/coverage-c8": "^0.29.8", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.0"}, "peerDependencies": {"picomatch": "3.x"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.1.1_1699205166964_0.7398672852703851", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "fdir", "version": "6.2.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.2.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "9120f438d566ef3e808ca37864d9dd18e1a4f9b5", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.2.0.tgz", "fileCount": 52, "integrity": "sha512-9XaWcDl0riOX5j2kYfy0kKdg7skw3IY6kA4LFT8Tk2yF9UdrADUy8D6AJuBLtf7ISm/MksumwAHE3WVbMRyCLw==", "signatures": [{"sig": "MEUCIFZ5zMYtgzHQjCy5bM8dEegRZp3+gBAuqeSI/lfvjVM8AiEA70QhKHccBXPDbJ7k+pGA5VdHSsFFuPys6RtbMLrB0Mc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58290}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "db61890ca0b70f68ea0ffc2cb3fe17ca32bbecdd", "scripts": {"test": "vitest run __tests__/", "bench": "ts-node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.10", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^12.1.0", "expect": "^29.7.0", "vitest": "^0.34.6", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.3.2", "klaw-sync": "^6.0.0", "picomatch": "^4.0.2", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.11", "typescript": "^5.3.2", "@types/glob": "^8.1.0", "@types/node": "^20.9.4", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.4", "recursive-files": "^1.0.2", "@types/picomatch": "^3.0.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.21.17", "@vitest/coverage-v8": "^0.34.6", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.1"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.2.0_1721622133581_0.9584083959033181", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "fdir", "version": "6.3.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.3.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "fcca5a23ea20e767b15e081ee13b3e6488ee0bb0", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.3.0.tgz", "fileCount": 39, "integrity": "sha512-QOnuT+BOtivR77wYvCWHfGt9s4Pz1VIMbD463vegT5MLqNXy8rYFT/lPVEqf/bhYeT6qmqrNHhsX+rWwe3rOCQ==", "signatures": [{"sig": "MEUCIGqK/5TqPUmn63TIaYc/sjQ1S0dS2tKcoULPI1QxQm3dAiEAoXdnui9mDMR7yGI3shbjYR23cByIxOo3GAC1Ub59PxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38200}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "5a0aab5b3e108eaf677e0e39c22ff1ea03cd1a59", "scripts": {"test": "vitest run __tests__/", "bench": "ts-node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.10", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^12.1.0", "expect": "^29.7.0", "vitest": "^0.34.6", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.3.2", "klaw-sync": "^6.0.0", "picomatch": "^4.0.2", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.11", "typescript": "^5.3.2", "@types/glob": "^8.1.0", "@types/node": "^20.9.4", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.4", "recursive-files": "^1.0.2", "@types/picomatch": "^3.0.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.21.17", "@vitest/coverage-v8": "^0.34.6", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.1"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.3.0_1724594048505_0.8152725786187789", "host": "s3://npm-registry-packages"}}, "6.4.0": {"name": "fdir", "version": "6.4.0", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.4.0", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "8e80ab4b18a2ac24beebf9d20d71e1bc2627dbae", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.4.0.tgz", "fileCount": 39, "integrity": "sha512-3oB133prH1o4j/L5lLW7uOCF1PlD+/It2L0eL/iAqWMB91RBbqTewABqxhj0ibBd90EEmWZq7ntIWzVaWcXTGQ==", "signatures": [{"sig": "MEUCIQCRT9OnMoLLLHY8SMl4+p0wnWJaf+dsyGyP7T6EHhXaTAIgS/KgJCXHNxCNgGbPCu3P4hTj/q84SjbQxPdfJWbwr0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40190}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "5f551d5872173aeb504db5b1e883ee0cf89f8b70", "scripts": {"test": "vitest run __tests__/", "bench": "ts-node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.10", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^12.1.0", "expect": "^29.7.0", "vitest": "^0.34.6", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.3.2", "klaw-sync": "^6.0.0", "picomatch": "^4.0.2", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.11", "typescript": "^5.3.2", "@types/glob": "^8.1.0", "@types/node": "^20.9.4", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.4", "recursive-files": "^1.0.2", "@types/picomatch": "^3.0.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.21.17", "@vitest/coverage-v8": "^0.34.6", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.1"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.4.0_1727681111206_0.2858660272164242", "host": "s3://npm-registry-packages"}}, "6.4.1": {"name": "fdir", "version": "6.4.1", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.4.1", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "51a873897b1dbb6a53d59d3c3eb227ce2f69e278", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.4.1.tgz", "fileCount": 43, "integrity": "sha512-upAYvbFc8GU44qWv+r6YnWp3h13/hPFOnpCsuahbxFGmOCm7sc5UT1SytkHLS7Go/UwvhVD9k4dF6PJxpIjqAA==", "signatures": [{"sig": "MEYCIQDjxT+ce7m8YpPBRMkXwrK52xk63+tjA8l84XXSlYhpwAIhAMbxjcLfnugobbjJxjs8xvbImtjnPuLrxwBIS3bJNiF6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45142}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "e17009b5bea83ab5cfbefdfbdd595a1332dbce9f", "scripts": {"test": "vitest run __tests__/", "bench": "ts-node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.10", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^12.1.0", "expect": "^29.7.0", "vitest": "^0.34.6", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.3.2", "klaw-sync": "^6.0.0", "picomatch": "^4.0.2", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.11", "typescript": "^5.3.2", "@types/glob": "^8.1.0", "@types/node": "^20.9.4", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.4", "recursive-files": "^1.0.2", "@types/picomatch": "^3.0.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.21.17", "@vitest/coverage-v8": "^0.34.6", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.1"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.4.1_1729102463665_0.8084182469740953", "host": "s3://npm-registry-packages"}}, "6.4.2": {"name": "fdir", "version": "6.4.2", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.4.2", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "ddaa7ce1831b161bc3657bb99cb36e1622702689", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.4.2.tgz", "fileCount": 43, "integrity": "sha512-KnhMXsKSPZlAhp7+IjUkRZKPb4fUyccpDrdFXbi4QL1qkmFh9kVY09Yox+n4MaOb3lHZ1Tv829C3oaaXoMYPDQ==", "signatures": [{"sig": "MEUCIE08fZfx7ZOhtxd6O76stLsDBX6fsFb/N3pc9aUNRe5zAiEAq1B8PuNC2/tGfm5gy3ciAknoM1d9m2ZsicJFkqYcLVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45174}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "8288d392337714b0ab881a6eee2dce4629de56fd", "scripts": {"test": "vitest run __tests__/", "bench": "ts-node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.10", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^12.1.0", "expect": "^29.7.0", "vitest": "^0.34.6", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.3.2", "klaw-sync": "^6.0.0", "picomatch": "^4.0.2", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.11", "typescript": "^5.3.2", "@types/glob": "^8.1.0", "@types/node": "^20.9.4", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.4", "recursive-files": "^1.0.2", "@types/picomatch": "^3.0.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.21.17", "@vitest/coverage-v8": "^0.34.6", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.1"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.4.2_1729107288059_0.5800172926488418", "host": "s3://npm-registry-packages"}}, "6.4.3": {"name": "fdir", "version": "6.4.3", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.4.3", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "011cdacf837eca9b811c89dbb902df714273db72", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.4.3.tgz", "fileCount": 43, "integrity": "sha512-PMXmW2y1hDDfTSRc9gaXIuCCRpuoz3Kaz8cUelp3smouvfT632ozg2vrT6lJsHKKOF59YLbOGfAWGUcKEfRMQw==", "signatures": [{"sig": "MEQCIAbWvckQNoZzYDFInBTBvziztip+PA+VcfRc0toKka7gAiBte5BTvIasFVqbIUsF3Ab0zmrnr1CyyUjj1w/Ykq//vQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45205}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "1f4eece91127359619c46c359606a6c3294fc5b7", "scripts": {"test": "vitest run __tests__/", "bench": "ts-node benchmarks/benchmark.js", "build": "tsc", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.10", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^12.1.0", "expect": "^29.7.0", "vitest": "^0.34.6", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "fast-glob": "^3.3.2", "klaw-sync": "^6.0.0", "picomatch": "^4.0.2", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.11", "typescript": "^5.3.2", "@types/glob": "^8.1.0", "@types/node": "^20.9.4", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.4", "recursive-files": "^1.0.2", "@types/picomatch": "^3.0.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.21.17", "@vitest/coverage-v8": "^0.34.6", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.1"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.4.3_1737093245532_0.9672006402884059", "host": "s3://npm-registry-packages-npm-production"}}, "6.4.4": {"name": "fdir", "version": "6.4.4", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.4.4", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "1cfcf86f875a883e19a8fab53622cfe992e8d2f9", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.4.4.tgz", "fileCount": 43, "integrity": "sha512-1NZP+GK4GfuAv3PqKvxQRDMjdSRZjnkq7KfhlNrCNNlZ0ygQFpebfrnfnq/W7fpUnAv9aGWmY1zKx7FYL3gwhg==", "signatures": [{"sig": "MEQCIA0b7uEOvDjk2ByAb1jQYVnanbpB+ZwnNA+F4IF8jMIJAiAIpC1iobIgmMCmhjXeiXxBg+X3jzvBQR6UGjULz6DwwA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45593}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "22cc385f065bd229c0e4c608b7549f4c9549bb40", "scripts": {"test": "vitest run __tests__/", "bench": "ts-node benchmarks/benchmark.js", "build": "tsc", "format": "prettier --write src __tests__ benchmarks", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.10", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^12.1.0", "expect": "^29.7.0", "vitest": "^0.34.6", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "prettier": "^3.5.3", "fast-glob": "^3.3.2", "klaw-sync": "^6.0.0", "picomatch": "^4.0.2", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.11", "typescript": "^5.3.2", "@types/glob": "^8.1.0", "@types/node": "^20.9.4", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.4", "recursive-files": "^1.0.2", "@types/picomatch": "^3.0.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.21.17", "@vitest/coverage-v8": "^0.34.6", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.1"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.4.4_1745037703743_0.48355881694366376", "host": "s3://npm-registry-packages-npm-production"}}, "6.4.5": {"name": "fdir", "version": "6.4.5", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "_id": "fdir@6.4.5", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "homepage": "https://github.com/thecodrr/fdir#readme", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "dist": {"shasum": "328e280f3a23699362f95f2e82acf978a0c0cb49", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.4.5.tgz", "fileCount": 43, "integrity": "sha512-4BG7puHpVsIYxZUbiUE3RqGloLaSSwzYie5jvasC4LWuBWzZawynvYouhjbQKw2JuIGYdm0DzIxl8iVidKlUEw==", "signatures": [{"sig": "MEQCICwIvDO2PjwKvLmRGhLwq61U/tMQD4Bo5zfm+jl2FkTrAiBs5pOkB+Hd3kE5UBzmfcyPwIRHEh2Iei3vTOpGF+YN9Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45866}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "7a72642f2caf857c92b99fc462d7909800e26174", "scripts": {"test": "vitest run __tests__/", "bench": "ts-node benchmarks/benchmark.js", "build": "tsc", "format": "prettier --write src __tests__ benchmarks", "release": "./scripts/release.sh", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "test:watch": "vitest __tests__/", "test:coverage": "vitest run --coverage __tests__/", "prepublishOnly": "npm run test && npm run build"}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thecodrr/fdir.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "devDependencies": {"glob": "^10.3.10", "benny": "^3.7.1", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "rrdir": "^12.1.0", "expect": "^29.7.0", "vitest": "^0.34.6", "mock-fs": "^5.2.0", "ts-node": "^10.9.1", "prettier": "^3.5.3", "fast-glob": "^3.3.2", "klaw-sync": "^6.0.0", "picomatch": "^4.0.2", "tiny-glob": "^0.2.9", "walk-sync": "^3.0.0", "@types/tap": "^15.0.11", "typescript": "^5.3.2", "@types/glob": "^8.1.0", "@types/node": "^20.9.4", "recursive-fs": "^2.1.0", "get-all-files": "^4.1.0", "recur-readdir": "0.0.1", "@types/mock-fs": "^4.13.4", "recursive-files": "^1.0.2", "@types/picomatch": "^3.0.0", "all-files-in-tree": "^1.1.2", "recursive-readdir": "^2.2.3", "systeminformation": "^5.21.17", "@vitest/coverage-v8": "^0.34.6", "fs-readdir-recursive": "^1.1.0", "csv-to-markdown-table": "^1.3.1"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/fdir_6.4.5_1748408830004_0.1969686659385923", "host": "s3://npm-registry-packages-npm-production"}}, "6.4.6": {"name": "fdir", "version": "6.4.6", "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepublishOnly": "npm run test && npm run build", "build": "tsc", "format": "prettier --write src __tests__ benchmarks", "test": "vitest run __tests__/", "test:coverage": "vitest run --coverage __tests__/", "test:watch": "vitest __tests__/", "bench": "ts-node benchmarks/benchmark.js", "bench:glob": "ts-node benchmarks/glob-benchmark.ts", "bench:fdir": "ts-node benchmarks/fdir-benchmark.ts", "release": "./scripts/release.sh"}, "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "homepage": "https://github.com/thecodrr/fdir#readme", "devDependencies": {"@types/glob": "^8.1.0", "@types/mock-fs": "^4.13.4", "@types/node": "^20.9.4", "@types/picomatch": "^3.0.0", "@types/tap": "^15.0.11", "@vitest/coverage-v8": "^0.34.6", "all-files-in-tree": "^1.1.2", "benny": "^3.7.1", "csv-to-markdown-table": "^1.3.1", "expect": "^29.7.0", "fast-glob": "^3.3.2", "fdir1": "npm:fdir@1.2.0", "fdir2": "npm:fdir@2.1.0", "fdir3": "npm:fdir@3.4.2", "fdir4": "npm:fdir@4.1.0", "fdir5": "npm:fdir@5.0.0", "fs-readdir-recursive": "^1.1.0", "get-all-files": "^4.1.0", "glob": "^10.3.10", "klaw-sync": "^6.0.0", "mock-fs": "^5.2.0", "picomatch": "^4.0.2", "prettier": "^3.5.3", "recur-readdir": "0.0.1", "recursive-files": "^1.0.2", "recursive-fs": "^2.1.0", "recursive-readdir": "^2.2.3", "rrdir": "^12.1.0", "systeminformation": "^5.21.17", "tiny-glob": "^0.2.9", "ts-node": "^10.9.1", "typescript": "^5.3.2", "vitest": "^0.34.6", "walk-sync": "^3.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}, "_id": "fdir@6.4.6", "gitHead": "4a8b167170a049b516fe3aed18dfe1f8ef4a1bdf", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==", "shasum": "2b268c0232697063111bbf3f64810a2a741ba281", "tarball": "https://registry.npmjs.org/fdir/-/fdir-6.4.6.tgz", "fileCount": 43, "unpackedSize": 85936, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDVV2Vgc+1nMOs75Z570AQmzcX9I22RJr5+EzVLpOUr1AiBT5i4ZT9zz3MJCAUpL19NEL1Aibnh51tgI55aKrn+nSg=="}]}, "_npmUser": {"name": "thecodrr", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fdir_6.4.6_1749540054735_0.330254074070808"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-03-13T20:58:27.336Z", "modified": "2025-06-10T07:20:55.141Z", "1.0.0": "2020-03-13T20:58:27.499Z", "1.0.1": "2020-03-13T21:04:07.491Z", "1.0.2": "2020-03-13T21:09:08.046Z", "1.0.3": "2020-03-14T10:30:32.415Z", "1.1.0": "2020-03-14T17:01:35.435Z", "1.1.1": "2020-03-14T17:03:15.410Z", "1.2.0": "2020-03-15T16:31:28.798Z", "2.0.0": "2020-03-16T08:15:37.209Z", "2.0.1": "2020-03-16T08:16:15.281Z", "2.0.2": "2020-03-18T18:33:29.730Z", "2.0.3": "2020-03-18T18:34:02.161Z", "2.1.0": "2020-03-21T18:52:56.998Z", "2.1.1": "2020-04-27T07:29:41.173Z", "3.0.0": "2020-05-10T18:42:45.986Z", "3.1.0": "2020-05-11T00:09:41.544Z", "3.1.1": "2020-05-11T00:20:27.307Z", "3.2.0": "2020-05-12T15:56:29.990Z", "3.2.1": "2020-05-12T18:16:57.094Z", "3.3.0": "2020-05-13T09:19:45.050Z", "3.4.0": "2020-05-15T17:35:09.369Z", "3.4.1": "2020-05-17T09:33:32.624Z", "3.4.2": "2020-05-18T00:39:20.041Z", "3.4.3": "2020-06-02T23:07:47.019Z", "4.0.0": "2020-07-27T18:26:11.618Z", "4.1.0": "2020-08-04T15:24:03.809Z", "5.0.0": "2021-01-12T17:23:39.278Z", "5.1.0": "2021-05-21T13:03:57.727Z", "5.2.0": "2022-01-18T06:13:39.331Z", "5.2.1": "2022-10-17T18:37:41.998Z", "5.3.0": "2022-10-18T17:39:22.736Z", "6.0.0": "2023-02-08T18:05:42.434Z", "6.0.1": "2023-02-09T07:45:47.214Z", "6.0.2": "2023-07-31T21:17:35.824Z", "6.1.0": "2023-08-13T18:29:08.556Z", "6.1.1": "2023-11-05T17:26:07.154Z", "6.2.0": "2024-07-22T04:22:13.742Z", "6.3.0": "2024-08-25T13:54:08.680Z", "6.4.0": "2024-09-30T07:25:11.390Z", "6.4.1": "2024-10-16T18:14:23.917Z", "6.4.2": "2024-10-16T19:34:48.255Z", "6.4.3": "2025-01-17T05:54:05.708Z", "6.4.4": "2025-04-19T04:41:43.916Z", "6.4.5": "2025-05-28T05:07:10.182Z", "6.4.6": "2025-06-10T07:20:54.902Z"}, "bugs": {"url": "https://github.com/thecodrr/fdir/issues"}, "author": {"name": "thecodrr", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/thecodrr/fdir#readme", "keywords": ["util", "os", "sys", "fs", "walk", "crawler", "directory", "files", "io", "tiny-glob", "glob", "fast-glob", "speed", "javascript", "nodejs"], "repository": {"type": "git", "url": "git+https://github.com/thecodrr/fdir.git"}, "description": "The fastest directory crawler & globbing alternative to glob, fast-glob, & tiny-glob. Crawls 1m files in < 1s", "maintainers": [{"name": "thecodrr", "email": "<EMAIL>"}], "readme": "<p align=\"center\">\n<img src=\"https://github.com/thecodrr/fdir/raw/master/assets/fdir.gif\" width=\"75%\"/>\n\n<h1 align=\"center\">The Fastest Directory Crawler & Globber for NodeJS</h1>\n<p align=\"center\">\n  <a href=\"https://www.npmjs.com/package/fdir\"><img src=\"https://img.shields.io/npm/v/fdir?style=for-the-badge\"/></a>\n  <a href=\"https://www.npmjs.com/package/fdir\"><img src=\"https://img.shields.io/npm/dw/fdir?style=for-the-badge\"/></a>\n  <a href=\"https://codeclimate.com/github/thecodrr/fdir/maintainability\"><img src=\"https://img.shields.io/codeclimate/maintainability-percentage/thecodrr/fdir?style=for-the-badge\"/></a>\n  <a href=\"https://coveralls.io/github/thecodrr/fdir?branch=master\"><img src=\"https://img.shields.io/coveralls/github/thecodrr/fdir?style=for-the-badge\"/></a>\n  <a href=\"https://www.npmjs.com/package/fdir\"><img src=\"https://img.shields.io/bundlephobia/minzip/fdir?style=for-the-badge\"/></a>\n  <a href=\"https://www.producthunt.com/posts/fdir-every-millisecond-matters\"><img src=\"https://img.shields.io/badge/ProductHunt-Upvote-red?style=for-the-badge&logo=product-hunt\"/></a>\n  <a href=\"https://dev.to/thecodrr/how-i-wrote-the-fastest-directory-crawler-ever-3p9c\"><img src=\"https://img.shields.io/badge/dev.to-Read%20Blog-black?style=for-the-badge&logo=dev.to\"/></a>\n  <a href=\"./LICENSE\"><img src=\"https://img.shields.io/github/license/thecodrr/fdir?style=for-the-badge\"/></a>\n</p>\n</p>\n\n⚡ **The Fastest:** Nothing similar (in the NodeJS world) beats `fdir` in speed. It can easily crawl a directory containing **1 million files in < 1 second.**\n\n💡 **Stupidly Easy:** `fdir` uses expressive Builder pattern to build the crawler increasing code readability.\n\n🤖 **Zero Dependencies\\*:** `fdir` only uses NodeJS `fs` & `path` modules.\n\n🕺 **Astonishingly Small:** < 2KB in size gzipped & minified.\n\n🖮 **Hackable:** Extending `fdir` is extremely simple now that the new Builder API is here. Feel free to experiment around.\n\n_\\* `picomatch` must be installed manually by the user to support globbing._\n\n## 🚄 Quickstart\n\n### Installation\n\nYou can install using `npm`:\n\n```sh\n$ npm i fdir\n```\n\nor Yarn:\n\n```sh\n$ yarn add fdir\n```\n\n### Usage\n\n```ts\nimport { fdir } from \"fdir\";\n\n// create the builder\nconst api = new fdir().withFullPaths().crawl(\"path/to/dir\");\n\n// get all files in a directory synchronously\nconst files = api.sync();\n\n// or asynchronously\napi.withPromise().then((files) => {\n  // do something with the result here.\n});\n```\n\n## Documentation:\n\nDocumentation for all methods is available [here](/documentation.md).\n\n## 📊 Benchmarks:\n\nPlease check the benchmark against the latest version [here](/BENCHMARKS.md).\n\n## 🙏Used by:\n\n`fdir` is downloaded over 200k+ times a week by projects around the world. Here's a list of some notable projects using `fdir` in production:\n\n> Note: if you think your project should be here, feel free to open an issue. Notable is anything with a considerable amount of GitHub stars.\n\n1. [rollup/plugins](https://github.com/rollup/plugins)\n2. [SuperchupuDev/tinyglobby](https://github.com/SuperchupuDev/tinyglobby)\n3. [pulumi/pulumi](https://github.com/pulumi/pulumi)\n4. [dotenvx/dotenvx](https://github.com/dotenvx/dotenvx)\n5. [mdn/yari](https://github.com/mdn/yari)\n6. [streetwriters/notesnook](https://github.com/streetwriters/notesnook)\n7. [imba/imba](https://github.com/imba/imba)\n8. [moroshko/react-scanner](https://github.com/moroshko/react-scanner)\n9. [netlify/build](https://github.com/netlify/build)\n10. [yassinedoghri/astro-i18next](https://github.com/yassinedoghri/astro-i18next)\n11. [selfrefactor/rambda](https://github.com/selfrefactor/rambda)\n12. [whyboris/Video-Hub-App](https://github.com/whyboris/Video-Hub-App)\n\n## 🦮 LICENSE\n\nCopyright &copy; 2024 Abdullah Atta under MIT. [Read full text here.](https://github.com/thecodrr/fdir/raw/master/LICENSE)\n", "readmeFilename": "README.md"}